import { useState, useCallback } from 'react'
import { documentService } from '@/services/document-service'


/**
 * Document Upload Hook
 * Manages document upload functionality with progress tracking
 */

export interface UseDocumentUploadOptions {
  projectId?: string
  onSuccess?: (document: any) => void
  onError?: (error: Error) => void
  maxFileSize?: number // in bytes
  allowedTypes?: string[]
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

export interface UseDocumentUploadResult {
  uploading: boolean
  progress: UploadProgress | null
  error: string | null
  uploadDocument: (file: File, metadata?: any) => Promise<any>
  uploadMultiple: (files: File[], metadata?: any) => Promise<any[]>
  reset: () => void
}

export function useDocumentUpload(
  options: UseDocumentUploadOptions = {}
): UseDocumentUploadResult {
  const {
    projectId,
    onSuccess,
    onError,
    maxFileSize = 100 * 1024 * 1024, // 100MB default
    allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']
  } = options

  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState<UploadProgress | null>(null)
  const [error, setError] = useState<string | null>(null)
  
  // Note: This hook implements its own upload logic rather than using the store's uploadDocument method

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize) {
      return `File size exceeds ${Math.round(maxFileSize / 1024 / 1024)}MB limit`
    }

    // Check file type
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (extension && !allowedTypes.includes(extension)) {
      return `File type .${extension} is not allowed`
    }

    return null
  }, [maxFileSize, allowedTypes])

  const uploadDocument = useCallback(async (file: File, metadata: any = {}) => {
    const validationError = validateFile(file)
    if (validationError) {
      setError(validationError)
      onError?.(new Error(validationError))
      throw new Error(validationError)
    }

    setUploading(true)
    setError(null)
    setProgress({ loaded: 0, total: file.size, percentage: 0 })

    try {
      const uploadData = {
        file,
        projectId,
        ...metadata
      }

      const document = await documentService.uploadDocument(uploadData)

      // Note: Document is automatically added to store by the service
      
      setProgress({ loaded: file.size, total: file.size, percentage: 100 })
      onSuccess?.(document)
      
      return document
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error.message)
      onError?.(error)
      throw error
    } finally {
      setUploading(false)
    }
  }, [validateFile, projectId, onSuccess, onError])

  const uploadMultiple = useCallback(async (files: File[], metadata: any = {}) => {
    const results: any[] = []
    
    for (const file of files) {
      try {
        const document = await uploadDocument(file, metadata)
        results.push(document)
      } catch (error) {
        // Continue with other files even if one fails
        console.error(`Failed to upload ${file.name}:`, error)
      }
    }
    
    return results
  }, [uploadDocument])

  const reset = useCallback(() => {
    setUploading(false)
    setProgress(null)
    setError(null)
  }, [])

  return {
    uploading,
    progress,
    error,
    uploadDocument,
    uploadMultiple,
    reset,
  }
}
