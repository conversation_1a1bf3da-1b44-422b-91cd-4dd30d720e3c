"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { History, Download, Eye, RotateCcw, Diff } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { useEventSubscription } from "@/hooks/infrastructure";
import { EventType } from "@/services/event-grid-service";
import { documentService } from "@/services/optimized-document-service";
import { DocumentVersion } from "@/types/document";

interface DocumentVersionsProps {
  documentId: string;
  projectId: string;
  organizationId: string;
  onCompareVersions?: (sourceVersionId: string, targetVersionId: string) => void;
}

export function ProductionDocumentVersions({
  documentId,
  projectId,
  organizationId,
  onCompareVersions
}: DocumentVersionsProps) {
  const { toast } = useToast();
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedVersion, setSelectedVersion] = useState<DocumentVersion | null>(null);
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [compareSource, setCompareSource] = useState<DocumentVersion | null>(null);
  const [compareTarget, setCompareTarget] = useState<DocumentVersion | null>(null);

  // Format file size to human-readable format
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Fetch versions on mount
  useEffect(() => {
    fetchVersions();
  }, [documentId, projectId, organizationId]);

  // Subscribe to document version events
  useEventSubscription([EventType.DOCUMENT_VERSION_CREATED], (event) => {
    if (event.data.documentId === documentId) {
      fetchVersions();
    }
  });

  const fetchVersions = async () => {
    setIsLoading(true);
    try {
      const response = await documentService.getDocumentVersions(
        documentId,
        projectId,
        organizationId
      );
      setVersions(response.versions);
    } catch (error) {
      console.error("Error fetching versions:", error);
      toast({
        title: "Error",
        description: "Failed to load document versions. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewVersion = async (version: DocumentVersion) => {
    try {
      const blob = await documentService.getVersionContent(
        documentId,
        version.id
      );

      // Create a URL for the blob
      const url = URL.createObjectURL(blob);

      // Open in a new tab
      window.open(url, '_blank');
    } catch (error) {
      console.error("Error viewing version:", error);
      toast({
        title: "Error",
        description: "Failed to view document version. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDownloadVersion = async (version: DocumentVersion) => {
    try {
      const blob = await documentService.getVersionContent(
        documentId,
        version.id
      );

      // Create a download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = version.fileName || `document-v${version.versionNumber}.${blob.type.split('/')[1] || 'pdf'}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Download Started",
        description: `Version ${version.versionNumber} is being downloaded.`,
        variant: "default"
      });
    } catch (error) {
      console.error("Error downloading version:", error);
      toast({
        title: "Error",
        description: "Failed to download document version. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleRestoreVersion = (version: DocumentVersion) => {
    setSelectedVersion(version);
    setIsRestoreDialogOpen(true);
  };

  const confirmRestore = async () => {
    if (!selectedVersion) return;

    setIsRestoring(true);

    try {
      await documentService.restoreDocumentVersion(
        documentId,
        selectedVersion.id,
        projectId,
        organizationId
      );

      // Refresh versions
      await fetchVersions();

      toast({
        title: "Version Restored",
        description: `Document has been restored to version ${selectedVersion.versionNumber}.`,
        variant: "default"
      });
    } catch (error) {
      console.error("Error restoring version:", error);
      toast({
        title: "Error",
        description: "Failed to restore document version. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsRestoring(false);
      setIsRestoreDialogOpen(false);
    }
  };

  const handleCompareVersions = () => {
    if (!compareSource || !compareTarget || !onCompareVersions) return;

    onCompareVersions(compareSource.id, compareTarget.id);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Version History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4 border rounded-md">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Version History
        </CardTitle>
      </CardHeader>
      <CardContent>
        {versions.length === 0 ? (
          <div className="text-center py-8">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No version history available</p>
          </div>
        ) : (
          <div className="space-y-4">
            {versions.map((version) => (
              <div
                key={version.id}
                className={`flex flex-col md:flex-row md:items-center gap-4 p-4 border rounded-md ${
                  version.versionNumber === Math.max(...versions.map(v => v.versionNumber)) ? "bg-primary/5 border-primary/20" : ""
                }`}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium">Version {version.versionNumber}</h3>
                    {version.versionNumber === Math.max(...versions.map(v => v.versionNumber)) && (
                      <Badge variant="outline" className="text-primary border-primary">Current</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {format(new Date(version.createdAt), "PPP 'at' p")}
                  </p>
                  <p className="text-sm">{version.changes || "No change description available"}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Size: {formatFileSize(version.size || 0)}
                  </p>
                </div>
                <div className="flex gap-2 self-end md:self-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewVersion(version)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadVersion(version)}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  {version.versionNumber !== Math.max(...versions.map(v => v.versionNumber)) && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRestoreVersion(version)}
                    >
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Restore
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Version comparison navigation */}
        {versions.length > 1 && onCompareVersions && (
          <div className="mt-6 border rounded-md p-4">
            <h3 className="font-medium mb-3">Compare Versions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Source Version</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={compareSource?.id || ""}
                  onChange={(e) => {
                    const version = versions.find(v => v.id === e.target.value);
                    setCompareSource(version || null);
                  }}
                >
                  <option value="">Select a version</option>
                  {versions.map(version => (
                    <option key={version.id} value={version.id}>
                      Version {version.versionNumber} ({format(new Date(version.createdAt), "PP")})
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Target Version</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={compareTarget?.id || ""}
                  onChange={(e) => {
                    const version = versions.find(v => v.id === e.target.value);
                    setCompareTarget(version || null);
                  }}
                >
                  <option value="">Select a version</option>
                  {versions.map(version => (
                    <option key={version.id} value={version.id}>
                      Version {version.versionNumber} ({format(new Date(version.createdAt), "PP")})
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                onClick={handleCompareVersions}
                disabled={!compareSource || !compareTarget}
              >
                <Diff className="mr-2 h-4 w-4" />
                Compare Versions
              </Button>
            </div>
          </div>
        )}

        {/* Restore Confirmation Dialog */}
        <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Restore Version</DialogTitle>
              <DialogDescription>
                Are you sure you want to restore the document to version {selectedVersion?.versionNumber}?
                This will create a new version based on the selected version.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsRestoreDialogOpen(false)}
                disabled={isRestoring}
              >
                Cancel
              </Button>
              <Button
                onClick={confirmRestore}
                disabled={isRestoring}
              >
                {isRestoring ? "Restoring..." : "Restore"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
