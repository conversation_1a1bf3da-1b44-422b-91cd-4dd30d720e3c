import { useState, useEffect, useRef, useCallback } from 'react'

/**
 * Lazy Loading Hook
 * Implements lazy loading with intersection observer
 */

export interface UseLazyLoadingOptions {
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  enabled?: boolean
}

export interface UseLazyLoadingResult {
  ref: React.RefObject<HTMLElement>
  isVisible: boolean
  hasBeenVisible: boolean
  entry: IntersectionObserverEntry | null
}

export function useLazyLoading(
  options: UseLazyLoadingOptions = {}
): UseLazyLoadingResult {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    triggerOnce = true,
    enabled = true
  } = options

  const [isVisible, setIsVisible] = useState(false)
  const [hasBeenVisible, setHasBeenVisible] = useState(false)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!enabled || !ref.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setEntry(entry)
        setIsVisible(entry.isIntersecting)
        
        if (entry.isIntersecting) {
          setHasBeenVisible(true)
          
          if (triggerOnce) {
            observer.disconnect()
          }
        }
      },
      {
        threshold,
        rootMargin,
      }
    )

    observer.observe(ref.current)

    return () => {
      observer.disconnect()
    }
  }, [threshold, rootMargin, triggerOnce, enabled])

  return {
    ref,
    isVisible,
    hasBeenVisible,
    entry,
  }
}

/**
 * Image lazy loading hook
 */
export interface UseImageLazyLoadingOptions extends UseLazyLoadingOptions {
  src: string
  placeholder?: string
  onLoad?: () => void
  onError?: () => void
}

export interface UseImageLazyLoadingResult extends UseLazyLoadingResult {
  src: string
  loaded: boolean
  error: boolean
}

export function useImageLazyLoading(
  options: UseImageLazyLoadingOptions
): UseImageLazyLoadingResult {
  const { src, placeholder, onLoad, onError, ...lazyOptions } = options
  const [loaded, setLoaded] = useState(false)
  const [error, setError] = useState(false)
  const [currentSrc, setCurrentSrc] = useState(placeholder || '')

  const { ref, isVisible, hasBeenVisible, entry } = useLazyLoading(lazyOptions)

  useEffect(() => {
    if (!isVisible && !hasBeenVisible) return

    const img = new Image()
    
    img.onload = () => {
      setCurrentSrc(src)
      setLoaded(true)
      setError(false)
      onLoad?.()
    }
    
    img.onerror = () => {
      setError(true)
      setLoaded(false)
      onError?.()
    }
    
    img.src = src
  }, [src, isVisible, hasBeenVisible, onLoad, onError])

  return {
    ref,
    isVisible,
    hasBeenVisible,
    entry,
    src: currentSrc,
    loaded,
    error,
  }
}

/**
 * Component lazy loading hook
 */
export interface UseComponentLazyLoadingOptions extends UseLazyLoadingOptions {
  loader: () => Promise<any>
  fallback?: React.ComponentType
}

export function useComponentLazyLoading<T = any>(
  options: UseComponentLazyLoadingOptions
) {
  const { loader, fallback, ...lazyOptions } = options
  const [component, setComponent] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const { ref, isVisible, hasBeenVisible } = useLazyLoading(lazyOptions)

  useEffect(() => {
    if (!isVisible && !hasBeenVisible) return
    if (component || loading) return

    setLoading(true)
    setError(null)

    loader()
      .then((loadedComponent) => {
        setComponent(loadedComponent.default || loadedComponent)
        setLoading(false)
      })
      .catch((err) => {
        setError(err)
        setLoading(false)
      })
  }, [isVisible, hasBeenVisible, component, loading, loader])

  return {
    ref,
    component,
    loading,
    error,
    isVisible,
    hasBeenVisible,
  }
}
