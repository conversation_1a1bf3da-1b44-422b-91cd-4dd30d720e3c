/**
 * Organizations Hook
 * Manages organization operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { organizationService } from '@/services/organization-service'
import { useToast } from '@/hooks/use-toast'
import type { ID, CreateOrganizationData } from '@/types'
import type { Organization, OrganizationMember } from '@/types/backend'

export interface UseOrganizationsOptions {
  autoLoad?: boolean
  includeMembers?: boolean
  includeSettings?: boolean
}

export interface UseOrganizationsResult {
  // State
  organizations: Organization[]
  currentOrganization: Organization | null
  loading: boolean
  error: string | null
  
  // Organization operations
  loadOrganizations: () => Promise<void>
  loadOrganization: (organizationId: ID) => Promise<void>
  createOrganization: (data: CreateOrganizationData) => Promise<Organization>
  updateOrganization: (organizationId: ID, updates: Partial<Organization>) => Promise<void>
  deleteOrganization: (organizationId: ID) => Promise<void>
  
  // Member management
  getMembers: (organizationId: ID) => Promise<OrganizationMember[]>
  addMember: (organizationId: ID, userId: ID, role: string) => Promise<void>
  updateMember: (organizationId: ID, userId: ID, updates: Partial<OrganizationMember>) => Promise<void>
  removeMember: (organizationId: ID, userId: ID) => Promise<void>
  
  // Settings management
  getSettings: (organizationId: ID) => Promise<any>
  updateSettings: (organizationId: ID, settings: any) => Promise<void>
  
  // Utilities
  switchOrganization: (organizationId: ID) => Promise<void>
  setCurrentOrganization: (organization: Organization | null) => void
  searchOrganizations: (query: string) => Organization[]

  // Refresh
  refresh: () => Promise<void>
}

export function useOrganizations(options: UseOrganizationsOptions = {}): UseOrganizationsResult {
  const { autoLoad = true, includeMembers = false, includeSettings = false } = options
  const { toast } = useToast()
  
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [currentOrganization, setCurrentOrg] = useState<Organization | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load organizations
  const loadOrganizations = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await organizationService.getOrganizations()
      setOrganizations(response.data || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load organizations'
      setError(errorMessage)
      
      toast({
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [includeMembers, includeSettings, toast])

  // Load specific organization
  const loadOrganization = useCallback(async (organizationId: ID) => {
    setLoading(true)
    setError(null)

    try {
      const organization = await organizationService.getOrganization(organizationId)
      setCurrentOrg(organization)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load organization'
      setError(errorMessage)
      
      toast({
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Create organization
  const createOrganization = useCallback(async (data: CreateOrganizationData): Promise<Organization> => {
    try {
      const organization = await organizationService.createOrganization(data)
      await loadOrganizations()
      
      toast({
        title: 'Organization created',
        description: `Organization "${data.name}" has been created successfully.`,
      })

      return organization
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create organization'

      toast({
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadOrganizations, toast])

  // Update organization
  const updateOrganization = useCallback(async (organizationId: ID, updates: Partial<Organization>) => {
    try {
      await organizationService.updateOrganization(organizationId, updates)
      await loadOrganizations()
      
      if (currentOrganization?.id === organizationId) {
        setCurrentOrg(prev => prev ? { ...prev, ...updates } : null)
      }
      
      toast({
        title: 'Organization updated',
        description: 'Organization has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update organization'

      toast({
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [loadOrganizations, currentOrganization, toast])

  // Delete organization
  const deleteOrganization = useCallback(async (organizationId: ID) => {
    try {
      await organizationService.deleteOrganization(organizationId)
      await loadOrganizations()
      
      if (currentOrganization?.id === organizationId) {
        setCurrentOrg(null)
      }
      
      toast({
        title: 'Organization deleted',
        description: 'Organization has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete organization'

      toast({
        title: 'Deletion failed',
        description: errorMessage,
      })
    }
  }, [loadOrganizations, currentOrganization, toast])

  // Member management
  const getMembers = useCallback(async (organizationId: ID): Promise<OrganizationMember[]> => {
    try {
      const members = await organizationService.getMembers(organizationId)
      // Cast to backend type - the service returns compatible data
      return members as any
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load members'
      
      toast({
        title: 'Loading failed',
        description: errorMessage,
      })

      throw err
    }
  }, [toast])

  const addMember = useCallback(async (organizationId: ID, userId: ID, role: string) => {
    try {
      await organizationService.addMember(organizationId, { userId, role })

      toast({
        title: 'Member added',
        description: 'Member has been added to the organization.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to add member'

      toast({
        title: 'Add failed',
        description: errorMessage,
      })
    }
  }, [toast])

  const updateMember = useCallback(async (organizationId: ID, userId: ID, updates: Partial<OrganizationMember>) => {
    try {
      // Use specific service methods based on what's being updated
      if (updates.role && Object.keys(updates).length === 1) {
        await organizationService.updateMemberRole(organizationId, userId, updates.role)
      } else {
        // Handle multiple field updates
        const updatePromises = [];

        if (updates.role) {
          updatePromises.push(organizationService.updateMemberRole(organizationId, userId, updates.role));
        }

        // Handle other member properties if the service supports them
        if (updates.permissions && (organizationService as any).updateMemberPermissions) {
          updatePromises.push((organizationService as any).updateMemberPermissions(organizationId, userId, updates.permissions));
        }

        if (updates.status && (organizationService as any).updateMemberStatus) {
          updatePromises.push((organizationService as any).updateMemberStatus(organizationId, userId, updates.status));
        }

        // Execute all updates
        await Promise.all(updatePromises);
      }

      toast({
        title: 'Member updated',
        description: 'Member has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update member'

      toast({
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [toast])

  const removeMember = useCallback(async (organizationId: ID, userId: ID) => {
    try {
      await organizationService.removeMember(organizationId, userId)

      toast({
        title: 'Member removed',
        description: 'Member has been removed from the organization.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to remove member'

      toast({
        title: 'Remove failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Settings management
  const getSettings = useCallback(async (organizationId: ID) => {
    try {
      return await organizationService.getSettings(organizationId)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load settings'
      
      toast({
        title: 'Loading failed',
        description: errorMessage,
      })

      throw err
    }
  }, [toast])

  const updateSettings = useCallback(async (organizationId: ID, settings: any) => {
    try {
      await organizationService.updateSettings(organizationId, settings)

      toast({
        title: 'Settings updated',
        description: 'Organization settings have been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update settings'

      toast({
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Switch organization
  const switchOrganization = useCallback(async (organizationId: ID) => {
    try {
      // This would typically update the user's current organization context
      await loadOrganization(organizationId)

      toast({
        title: 'Organization switched',
        description: 'You have switched to a different organization.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to switch organization'

      toast({
        title: 'Switch failed',
        description: errorMessage,
      })
    }
  }, [loadOrganization, toast])

  // Set current organization
  const setCurrentOrganization = useCallback((organization: Organization | null) => {
    setCurrentOrg(organization)
  }, [])

  // Search organizations
  const searchOrganizations = useCallback((query: string): Organization[] => {
    if (!query.trim()) return organizations
    
    const lowercaseQuery = query.toLowerCase()
    return organizations.filter(org => 
      org.name.toLowerCase().includes(lowercaseQuery) ||
      org.description?.toLowerCase().includes(lowercaseQuery)
    )
  }, [organizations])

  // Load organizations on mount if autoLoad is enabled
  useEffect(() => {
    if (autoLoad) {
      loadOrganizations()
    }
  }, [autoLoad, loadOrganizations])

  return {
    // State
    organizations,
    currentOrganization,
    loading,
    error,
    
    // Organization operations
    loadOrganizations,
    loadOrganization,
    createOrganization,
    updateOrganization,
    deleteOrganization,
    
    // Member management
    getMembers,
    addMember,
    updateMember,
    removeMember,
    
    // Settings management
    getSettings,
    updateSettings,
    
    // Utilities
    switchOrganization,
    setCurrentOrganization,
    searchOrganizations,

    // Refresh
    refresh: loadOrganizations,
  }
}
