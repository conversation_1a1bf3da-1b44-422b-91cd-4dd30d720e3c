"use client";

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Trash2,
  ArrowRight,
  Play,
  Save,
  Settings,
  FileText,
  Mail,
  CheckCircle,
  Bot
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface WorkflowStep {
  id: string;
  type: 'document_processing' | 'approval' | 'notification' | 'ai_analysis' | 'custom';
  name: string;
  description?: string;
  config: Record<string, any>;
  position: { x: number; y: number };
  connections: string[];
}

interface WorkflowBuilderProps {
  workflowId?: string;
  projectId?: string;
  initialSteps?: WorkflowStep[];
  onSave?: (steps: WorkflowStep[], metadata: any) => void;
  onTest?: (steps: WorkflowStep[]) => void;
  isReadOnly?: boolean;
}

export function WorkflowBuilder({
  workflowId: _workflowId,
  projectId,
  initialSteps = [],
  onSave,
  onTest,
  isReadOnly = false
}: WorkflowBuilderProps) {
  const [steps, setSteps] = useState<WorkflowStep[]>(initialSteps);
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [isTestMode, setIsTestMode] = useState(false);
  const { toast } = useToast();

  const stepTypes = [
    {
      type: 'document_processing',
      name: 'Document Processing',
      icon: <FileText className="h-4 w-4" />,
      description: 'Process and analyze documents'
    },
    {
      type: 'ai_analysis',
      name: 'AI Analysis',
      icon: <Bot className="h-4 w-4" />,
      description: 'AI-powered document analysis'
    },
    {
      type: 'approval',
      name: 'Approval',
      icon: <CheckCircle className="h-4 w-4" />,
      description: 'Require approval from users'
    },
    {
      type: 'notification',
      name: 'Notification',
      icon: <Mail className="h-4 w-4" />,
      description: 'Send notifications to users'
    },
    {
      type: 'custom',
      name: 'Custom Action',
      icon: <Settings className="h-4 w-4" />,
      description: 'Custom workflow action'
    }
  ];

  const addStep = useCallback((type: WorkflowStep['type']) => {
    const newStep: WorkflowStep = {
      id: `step_${Date.now()}`,
      type,
      name: `${stepTypes.find(t => t.type === type)?.name || 'New Step'}`,
      config: {},
      position: { x: 100 + steps.length * 200, y: 100 },
      connections: []
    };

    setSteps(prev => [...prev, newStep]);
    setSelectedStep(newStep.id);
  }, [steps.length, stepTypes]);

  const updateStep = useCallback((stepId: string, updates: Partial<WorkflowStep>) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId ? { ...step, ...updates } : step
    ));
  }, []);

  const deleteStep = useCallback((stepId: string) => {
    setSteps(prev => {
      const filtered = prev.filter(step => step.id !== stepId);
      // Remove connections to deleted step
      return filtered.map(step => ({
        ...step,
        connections: step.connections.filter(conn => conn !== stepId)
      }));
    });
    if (selectedStep === stepId) {
      setSelectedStep(null);
    }
  }, [selectedStep]);

  // Connect steps function - kept for future use
  // const connectSteps = useCallback((fromId: string, toId: string) => {
  //   setSteps(prev => prev.map(step =>
  //     step.id === fromId
  //       ? { ...step, connections: [...step.connections, toId] }
  //       : step
  //   ));
  // }, []);

  const handleSave = useCallback(() => {
    if (!workflowName.trim()) {
      toast({
        title: "Validation Error",
        description: "Please enter a workflow name",
        variant: "destructive"
      });
      return;
    }

    const metadata = {
      name: workflowName,
      description: workflowDescription,
      projectId,
      stepCount: steps.length
    };

    onSave?.(steps, metadata);
    toast({
      title: "Workflow Saved",
      description: "Your workflow has been saved successfully",
    });
  }, [workflowName, workflowDescription, projectId, steps, onSave, toast]);

  const handleTest = useCallback(() => {
    if (steps.length === 0) {
      toast({
        title: "No Steps",
        description: "Add some steps to test the workflow",
        variant: "destructive"
      });
      return;
    }

    setIsTestMode(true);
    onTest?.(steps);

    // Simulate test completion
    setTimeout(() => {
      setIsTestMode(false);
      toast({
        title: "Test Complete",
        description: "Workflow test completed successfully",
      });
    }, 3000);
  }, [steps, onTest, toast]);

  const getStepIcon = (type: WorkflowStep['type']) => {
    return stepTypes.find(t => t.type === type)?.icon || <Settings className="h-4 w-4" />;
  };

  const selectedStepData = selectedStep ? steps.find(s => s.id === selectedStep) : null;

  return (
    <div className="flex h-full">
      {/* Workflow Canvas */}
      <div className="flex-1 p-6 bg-muted/20">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold">Workflow Builder</h2>
              <p className="text-muted-foreground">Design your document processing workflow</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleTest} disabled={isTestMode || isReadOnly}>
                <Play className="h-4 w-4 mr-2" />
                {isTestMode ? 'Testing...' : 'Test Workflow'}
              </Button>
              <Button onClick={handleSave} disabled={isReadOnly}>
                <Save className="h-4 w-4 mr-2" />
                Save Workflow
              </Button>
            </div>
          </div>

          {/* Workflow Metadata */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div>
              <Label htmlFor="workflow-name">Workflow Name</Label>
              <Input
                id="workflow-name"
                value={workflowName}
                onChange={(e) => setWorkflowName(e.target.value)}
                placeholder="Enter workflow name"
                disabled={isReadOnly}
              />
            </div>
            <div>
              <Label htmlFor="workflow-description">Description</Label>
              <Input
                id="workflow-description"
                value={workflowDescription}
                onChange={(e) => setWorkflowDescription(e.target.value)}
                placeholder="Enter workflow description"
                disabled={isReadOnly}
              />
            </div>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="relative bg-white border rounded-lg min-h-[500px] overflow-auto">
          {steps.length === 0 ? (
            <div className="flex items-center justify-center h-[500px]">
              <div className="text-center">
                <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Steps Added</h3>
                <p className="text-muted-foreground mb-4">
                  Add workflow steps from the panel on the right to get started
                </p>
              </div>
            </div>
          ) : (
            <div className="p-4">
              {steps.map((step, _index) => (
                <div key={step.id} className="relative">
                  <Card
                    className={`w-64 cursor-pointer transition-all ${
                      selectedStep === step.id ? 'ring-2 ring-primary' : ''
                    } ${isTestMode ? 'animate-pulse' : ''}`}
                    style={{
                      position: 'absolute',
                      left: step.position.x,
                      top: step.position.y
                    }}
                    onClick={() => setSelectedStep(step.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStepIcon(step.type)}
                          <CardTitle className="text-sm">{step.name}</CardTitle>
                        </div>
                        {!isReadOnly && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              deleteStep(step.id);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <Badge variant="outline" className="text-xs">
                        {step.type.replace('_', ' ')}
                      </Badge>
                      {step.description && (
                        <p className="text-xs text-muted-foreground mt-2">
                          {step.description}
                        </p>
                      )}
                    </CardContent>
                  </Card>

                  {/* Connection arrows */}
                  {step.connections.map(connectionId => {
                    const targetStep = steps.find(s => s.id === connectionId);
                    if (!targetStep) return null;

                    return (
                      <div
                        key={`${step.id}-${connectionId}`}
                        className="absolute flex items-center text-muted-foreground"
                        style={{
                          left: step.position.x + 256,
                          top: step.position.y + 40,
                          width: targetStep.position.x - step.position.x - 256,
                        }}
                      >
                        <div className="flex-1 border-t border-dashed" />
                        <ArrowRight className="h-4 w-4" />
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Right Panel */}
      <div className="w-80 border-l bg-background">
        {/* Step Types */}
        <div className="p-4 border-b">
          <h3 className="font-medium mb-3">Add Step</h3>
          <div className="space-y-2">
            {stepTypes.map((stepType) => (
              <Button
                key={stepType.type}
                variant="outline"
                className="w-full justify-start"
                onClick={() => addStep(stepType.type as any)}
                disabled={isReadOnly}
              >
                {stepType.icon}
                <span className="ml-2">{stepType.name}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Step Configuration */}
        {selectedStepData && (
          <div className="p-4">
            <h3 className="font-medium mb-3">Step Configuration</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="step-name">Step Name</Label>
                <Input
                  id="step-name"
                  value={selectedStepData.name}
                  onChange={(e) => updateStep(selectedStepData.id, { name: e.target.value })}
                  disabled={isReadOnly}
                />
              </div>

              <div>
                <Label htmlFor="step-description">Description</Label>
                <Textarea
                  id="step-description"
                  value={selectedStepData.description || ''}
                  onChange={(e) => updateStep(selectedStepData.id, { description: e.target.value })}
                  placeholder="Enter step description"
                  disabled={isReadOnly}
                />
              </div>

              <div>
                <Label>Step Type</Label>
                <Select
                  value={selectedStepData.type}
                  onValueChange={(value: WorkflowStep['type']) =>
                    updateStep(selectedStepData.id, { type: value })
                  }
                  disabled={isReadOnly}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {stepTypes.map((type) => (
                      <SelectItem key={type.type} value={type.type}>
                        <div className="flex items-center gap-2">
                          {type.icon}
                          {type.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Step-specific configuration */}
              {selectedStepData.type === 'approval' && (
                <div>
                  <Label>Approvers</Label>
                  <Select disabled={isReadOnly}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select approvers" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="project-admin">Project Admin</SelectItem>
                      <SelectItem value="organization-admin">Organization Admin</SelectItem>
                      <SelectItem value="custom">Custom Users</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {selectedStepData.type === 'notification' && (
                <div>
                  <Label>Notification Type</Label>
                  <Select disabled={isReadOnly}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select notification type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="in-app">In-App</SelectItem>
                      <SelectItem value="both">Both</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Workflow Stats */}
        <div className="p-4 border-t mt-auto">
          <h3 className="font-medium mb-3">Workflow Stats</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Steps:</span>
              <span>{steps.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Connections:</span>
              <span>{steps.reduce((acc, step) => acc + step.connections.length, 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <Badge variant={steps.length > 0 ? "default" : "secondary"}>
                {steps.length > 0 ? "Ready" : "Draft"}
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
