"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
// Removed deprecated useSignalR import - use CollaborationProvider instead
// SignalR imports removed - use CollaborationProvider for collaborative features
// Define local constants for removed SignalR enums
const HubMethod = {
  DOCUMENT_COMMENT_ADDED: 'DocumentCommentAdded',
  DOCUMENT_COMMENT_UPDATED: 'DocumentCommentUpdated',
  DOCUMENT_COMMENT_DELETED: 'DocumentCommentDeleted'
} as const
import { formatDistanceToNow } from "date-fns";
import { MessageSquare, Send, Edit, Trash2, CheckCircle, XCircle, MoreVertical, Reply } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { DocumentComment } from "@/types/document";
import { documentService } from "@/services/optimized-document-service";

interface DocumentCommentsProps {
  documentId: string;
  projectId: string;
  organizationId: string;
  currentUser: {
    id: string;
    name: string;
    avatarUrl?: string;
    email?: string;
  };
}

export function ProductionDocumentComments({
  documentId,
  projectId,
  organizationId,
  currentUser
}: DocumentCommentsProps) {
  const [comments, setComments] = useState<DocumentComment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [editedContent, setEditedContent] = useState("");
  const [replyingToId, setReplyingToId] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState("");
  const { toast } = useToast();
  // Deprecated SignalR functionality - use CollaborationProvider instead
  const subscribe = (...args: any[]) => console.log('SignalR subscribe deprecated:', args)
  const unsubscribe = (...args: any[]) => console.log('SignalR unsubscribe deprecated:', args)

  // Fetch comments on mount
  useEffect(() => {
    fetchComments();

    // Subscribe to comment events
    const handleCommentAdded = (comment: DocumentComment) => {
      if (comment.documentId === documentId) {
        setComments(prev => {
          // Check if comment already exists
          if (prev.some(c => c.id === comment.id)) {
            return prev;
          }
          return [...prev, comment];
        });
      }
    };

    const handleCommentUpdated = (comment: DocumentComment) => {
      if (comment.documentId === documentId) {
        setComments(prev =>
          prev.map(c => c.id === comment.id ? comment : c)
        );
      }
    };

    const handleCommentDeleted = (commentId: string, docId: string) => {
      if (docId === documentId) {
        setComments(prev => prev.filter(c => c.id !== commentId));
      }
    };

    subscribe(HubMethod.DOCUMENT_COMMENT_ADDED, handleCommentAdded);
    subscribe(HubMethod.DOCUMENT_COMMENT_UPDATED, handleCommentUpdated);
    subscribe(HubMethod.DOCUMENT_COMMENT_DELETED, handleCommentDeleted);

    // Cleanup subscriptions
    return () => {
      unsubscribe(HubMethod.DOCUMENT_COMMENT_ADDED, handleCommentAdded);
      unsubscribe(HubMethod.DOCUMENT_COMMENT_UPDATED, handleCommentUpdated);
      unsubscribe(HubMethod.DOCUMENT_COMMENT_DELETED, handleCommentDeleted);
    };
  }, [documentId, projectId, organizationId, subscribe, unsubscribe]);

  const fetchComments = async () => {
    setIsLoading(true);
    try {
      const response = await documentService.getDocumentComments(
        documentId,
        projectId,
        organizationId
      );
      setComments(response || []);
    } catch (error) {
      console.error("Error fetching comments:", error);
      toast({
        title: "Error",
        description: "Failed to load comments. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    setIsSubmitting(true);

    try {
      const comment = await documentService.addDocumentComment(
        documentId,
        projectId,
        organizationId,
        newComment.trim()
      );

      setComments(prev => [...prev, comment]);
      setNewComment("");

      toast({
        title: "Comment Added",
        description: "Your comment has been added successfully.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error adding comment:", error);
      toast({
        title: "Error",
        description: "Failed to add comment. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await documentService.deleteDocumentComment(
        documentId,
        commentId,
        projectId,
        organizationId
      );

      setComments(prev => prev.filter(comment => comment.id !== commentId));

      toast({
        title: "Comment Deleted",
        description: "Comment has been deleted successfully.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast({
        title: "Error",
        description: "Failed to delete comment. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleEditComment = (commentId: string) => {
    const comment = comments.find(c => c.id === commentId);
    if (comment) {
      setEditingCommentId(commentId);
      setEditedContent(comment.content);
    }
  };

  const handleSaveEdit = async (commentId: string) => {
    if (!editedContent.trim()) return;

    try {
      const updatedComment = await documentService.updateDocumentComment(
        documentId,
        commentId,
        { content: editedContent.trim() },
        projectId,
        organizationId
      );

      setComments(comments.map(comment =>
        comment.id === commentId ? updatedComment : comment
      ));

      setEditingCommentId(null);
      setEditedContent("");

      toast({
        title: "Comment Updated",
        description: "Your comment has been updated successfully.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error updating comment:", error);
      toast({
        title: "Error",
        description: "Failed to update comment. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleCancelEdit = () => {
    setEditingCommentId(null);
    setEditedContent("");
  };

  const handleReplyToComment = (commentId: string) => {
    setReplyingToId(commentId);
    setReplyContent("");
  };

  const handleSubmitReply = async (commentId: string) => {
    if (!replyContent.trim()) return;

    try {
      const updatedComment = await documentService.addCommentReply(
        documentId,
        commentId,
        { content: replyContent.trim() },
        projectId,
        organizationId
      );

      setComments(comments.map(comment =>
        comment.id === commentId ? updatedComment : comment
      ));

      setReplyingToId(null);
      setReplyContent("");

      toast({
        title: "Reply Added",
        description: "Your reply has been added successfully.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error adding reply:", error);
      toast({
        title: "Error",
        description: "Failed to add reply. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleCancelReply = () => {
    setReplyingToId(null);
    setReplyContent("");
  };

  const handleResolveComment = async (commentId: string, isResolved: boolean) => {
    try {
      const updatedComment = await documentService.updateDocumentComment(
        documentId,
        commentId,
        { isResolved },
        projectId,
        organizationId
      );

      setComments(comments.map(comment =>
        comment.id === commentId ? updatedComment : comment
      ));

      toast({
        title: isResolved ? "Comment Resolved" : "Comment Reopened",
        description: isResolved
          ? "The comment has been marked as resolved."
          : "The comment has been reopened.",
        variant: "default"
      });
    } catch (error) {
      console.error("Error updating comment resolution:", error);
      toast({
        title: "Error",
        description: "Failed to update comment. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Document Comments
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-pulse space-y-4">
              <div className="h-12 w-full bg-muted rounded-md"></div>
              <div className="h-24 w-full bg-muted rounded-md"></div>
              <div className="h-12 w-full bg-muted rounded-md"></div>
            </div>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No comments yet</p>
            <p className="text-sm text-muted-foreground mt-1">Be the first to add a comment</p>
          </div>
        ) : (
          <div className="space-y-4">
            {comments.map((comment) => (
              <div key={comment.id} className="border rounded-md p-4">
                <div className="flex justify-between items-start">
                  <div className="flex gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={comment.createdBy === currentUser.id ? currentUser.avatarUrl : undefined} />
                      <AvatarFallback>
                        {comment.createdBy === currentUser.id
                          ? currentUser.name.charAt(0).toUpperCase()
                          : "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {comment.createdBy === currentUser.id ? currentUser.name : "User"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}
                        </span>
                        {comment.resolved && (
                          <Badge variant="outline" className="text-green-500 border-green-500">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Resolved
                          </Badge>
                        )}
                      </div>

                      {editingCommentId === comment.id ? (
                        <div className="mt-2 space-y-2">
                          <Textarea
                            value={editedContent}
                            onChange={(e) => setEditedContent(e.target.value)}
                            className="min-h-[100px]"
                          />
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleCancelEdit}
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleSaveEdit(comment.id)}
                              disabled={!editedContent.trim()}
                            >
                              Save
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <p className="mt-1">{comment.content}</p>
                      )}
                    </div>
                  </div>

                  {comment.createdBy === currentUser.id && !editingCommentId && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditComment(comment.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDeleteComment(comment.id)}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {comment.resolved ? (
                          <DropdownMenuItem onClick={() => handleResolveComment(comment.id, false)}>
                            <XCircle className="h-4 w-4 mr-2" />
                            Reopen
                          </DropdownMenuItem>
                        ) : (
                          <DropdownMenuItem onClick={() => handleResolveComment(comment.id, true)}>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Resolve
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>

                {/* Replies section */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="mt-4 ml-11 space-y-3">
                    <Separator />
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="mt-2">
                        <div className="flex gap-3">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={reply.createdBy === currentUser.id ? currentUser.avatarUrl : undefined} />
                            <AvatarFallback>
                              {reply.createdBy === currentUser.id
                                ? currentUser.name.charAt(0).toUpperCase()
                                : "U"}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-sm">
                                {reply.createdBy === currentUser.id ? currentUser.name : "User"}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(reply.createdAt), { addSuffix: true })}
                              </span>
                            </div>
                            <p className="text-sm mt-1">{reply.content}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Reply form */}
                {replyingToId === comment.id && (
                  <div className="mt-4 ml-11">
                    <Separator className="mb-3" />
                    <div className="flex gap-3">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={currentUser.avatarUrl} />
                        <AvatarFallback>
                          {currentUser.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-2">
                        <Textarea
                          placeholder="Write a reply..."
                          value={replyContent}
                          onChange={(e) => setReplyContent(e.target.value)}
                          className="min-h-[80px] text-sm"
                        />
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleCancelReply}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleSubmitReply(comment.id)}
                            disabled={!replyContent.trim()}
                          >
                            Reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Reply button */}
                {!replyingToId && !editingCommentId && (
                  <div className="mt-3 ml-11">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-xs"
                      onClick={() => handleReplyToComment(comment.id)}
                    >
                      <Reply className="h-3 w-3 mr-1" />
                      Reply
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <div className="flex gap-4 w-full">
          <Avatar className="h-10 w-10">
            <AvatarImage src={currentUser.avatarUrl} alt={currentUser.name} />
            <AvatarFallback>
              {currentUser.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 space-y-2">
            <Textarea
              placeholder="Add a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="min-h-[100px]"
            />
            <div className="flex justify-end">
              <Button
                onClick={handleSubmitComment}
                disabled={!newComment.trim() || isSubmitting}
              >
                {isSubmitting ? "Posting..." : "Post Comment"}
                <Send className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
