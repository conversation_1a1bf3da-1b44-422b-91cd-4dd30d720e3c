"use client";

import { useState, useEffect, useRef } from "react";
import EditorJS from "@editorjs/editorjs";
import Header from "@editorjs/header";
import List from "@editorjs/list";
import Paragraph from "@editorjs/paragraph";
import Image from "@editorjs/image";
import Embed from "@editorjs/embed";
import Table from "@editorjs/table";
import Link from "@editorjs/link";
import Marker from "@editorjs/marker";
import InlineCode from "@editorjs/inline-code";
import Quote from "@editorjs/quote";
import Code from "@editorjs/code";
import Underline from "@editorjs/underline";

import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from "@/components/ui/spinner";
import {
  Code as CodeIcon,
  Eye,
  FileText,
  Calendar,
  User,
  Building,
  FolderK<PERSON>ban,
  AlertCircle
} from "lucide-react";
import { cn } from "@/lib/utils";
import { templateRenderingService, TemplateVariable } from "@/services/template-rendering-service";
import { useToast } from "@/components/ui/use-toast";

interface EditorJSEditorImplProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  height?: number;
}

export function EditorJSEditorImpl({
  value,
  onChange,
  className,
  height = 500
}: EditorJSEditorImplProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("visual");
  const [htmlValue, setHtmlValue] = useState(value);
  const [previewValue, setPreviewValue] = useState("");
  const [isRendering, setIsRendering] = useState(false);
  const [renderError, setRenderError] = useState<string | null>(null);
  const [variables, setVariables] = useState<TemplateVariable[]>([]);
  const [isLoadingVariables, setIsLoadingVariables] = useState(false);
  const editorRef = useRef<EditorJS | null>(null);
  const editorInstanceRef = useRef<HTMLDivElement>(null);
  const [, setEditorData] = useState<any>(null);

  // Initialize Editor.js
  useEffect(() => {
    if (!editorInstanceRef.current || activeTab !== "visual") return;

    let parsedInitialContent;
    try {
      // Try to parse the initial content as JSON
      parsedInitialContent = value && value.trim() !== "" ? JSON.parse(value) : { blocks: [] };
    } catch (error) {
      console.error("Failed to parse initial content:", error);
      // If parsing fails, create a default paragraph with the content
      parsedInitialContent = {
        blocks: [
          {
            type: "paragraph",
            data: {
              text: value || ""
            }
          }
        ]
      };
    }

    // Initialize Editor.js
    const editor = new EditorJS({
      holder: editorInstanceRef.current,
      tools: {
        header: {
          class: Header as any,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4, 5, 6],
            defaultLevel: 2
          }
        },
        list: {
          class: List as any,
          inlineToolbar: true
        },
        paragraph: {
          class: Paragraph as any,
          inlineToolbar: true
        },
        image: {
          class: Image as any,
          config: {
            uploader: {
              uploadByFile(file: File) {
                return new Promise((resolve, reject) => {
                  // Create a FileReader to convert the file to a data URL
                  const reader = new FileReader();
                  reader.onload = (event) => {
                    if (event.target && event.target.result) {
                      resolve({
                        success: 1,
                        file: {
                          url: event.target.result as string
                        }
                      });
                    } else {
                      reject(new Error("Failed to read file"));
                    }
                  };
                  reader.onerror = (error) => reject(error);
                  reader.readAsDataURL(file);
                });
              },
              uploadByUrl(url: string) {
                return Promise.resolve({
                  success: 1,
                  file: {
                    url
                  }
                });
              }
            }
          }
        },
        embed: Embed as any,
        table: Table as any,
        link: Link as any,
        marker: Marker as any,
        inlineCode: InlineCode as any,
        quote: Quote as any,
        code: Code as any,
        underline: Underline as any
      },
      data: parsedInitialContent,
      onChange: () => {
        if (editorRef.current) {
          editorRef.current.save().then((outputData) => {
            setEditorData(outputData);
            const jsonData = JSON.stringify(outputData);
            setHtmlValue(jsonData);
            onChange(jsonData);
          });
        }
      }
    });

    editorRef.current = editor;

    // Clean up on unmount
    return () => {
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    };
  }, [editorInstanceRef, activeTab, value, onChange]);

  // Update HTML value when value changes
  useEffect(() => {
    setHtmlValue(value);
  }, [value]);

  // Update preview value
  useEffect(() => {
    const renderTemplate = async () => {
      if (!htmlValue) {
        setPreviewValue("");
        return;
      }

      setIsRendering(true);
      setRenderError(null);

      try {
        // For Editor.js, we need to convert the JSON to HTML first
        let htmlContent = "";
        try {
          const editorData = JSON.parse(htmlValue);
          if (editorData && editorData.blocks) {
            // Simple conversion of Editor.js blocks to HTML
            htmlContent = editorData.blocks.map((block: any) => {
              switch (block.type) {
                case 'header':
                  return `<h${block.data.level}>${block.data.text}</h${block.data.level}>`;
                case 'paragraph':
                  return `<p>${block.data.text}</p>`;
                case 'list':
                  const listTag = block.data.style === 'ordered' ? 'ol' : 'ul';
                  const listItems = block.data.items.map((item: string) => `<li>${item}</li>`).join('');
                  return `<${listTag}>${listItems}</${listTag}>`;
                case 'image':
                  return `<figure><img src="${block.data.file.url}" alt="${block.data.caption || ''}"><figcaption>${block.data.caption || ''}</figcaption></figure>`;
                case 'quote':
                  return `<blockquote>${block.data.text}</blockquote><cite>${block.data.caption || ''}</cite>`;
                case 'code':
                  return `<pre><code>${block.data.code}</code></pre>`;
                default:
                  return `<div>${JSON.stringify(block.data)}</div>`;
              }
            }).join('');
          }
        } catch (error) {
          // If parsing fails, use the raw content
          htmlContent = htmlValue;
        }

        // Use the template rendering service to render the template
        const renderedContent = templateRenderingService.renderPreview(htmlContent);
        setPreviewValue(renderedContent);
      } catch (error) {
        console.error("Error rendering template:", error);
        setRenderError("Failed to render template. Please check your template syntax.");
        toast({
          title: "Render Error",
          description: "Failed to render template. Please check your template syntax.",
          variant: "destructive",
        });
      } finally {
        setIsRendering(false);
      }
    };

    renderTemplate();
  }, [htmlValue, toast]);

  // Load template variables
  useEffect(() => {
    const loadVariables = async () => {
      setIsLoadingVariables(true);
      try {
        const variables = await templateRenderingService.getTemplateVariables();
        setVariables(variables);
      } catch (error) {
        console.error("Error loading template variables:", error);
        toast({
          title: "Error",
          description: "Failed to load template variables.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingVariables(false);
      }
    };

    loadVariables();
  }, [toast]);

  // Handle HTML editor change
  const handleHtmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setHtmlValue(newValue);
    onChange(newValue);
  };

  // Insert variable
  const insertVariable = (variable: string) => {
    if (activeTab === "visual" && editorRef.current) {
      // For Editor.js, we need to insert the variable into the current block
      const variableText = `{{${variable}}}`;

      // Use the Editor.js API to insert content
      // This is a simplified approach - in a real implementation, you might want to
      // create a custom inline tool for variables
      if (editorRef.current) {
        // Save the current selection
        editorRef.current.save().then((outputData) => {
          // Insert the variable at the current cursor position
          // This is a simplified approach and might need to be adjusted
          // based on your specific requirements
          const blocks = outputData.blocks;
          if (blocks.length > 0) {
            const lastBlock = blocks[blocks.length - 1];
            if (lastBlock.type === "paragraph") {
              lastBlock.data.text += variableText;

              // Update the editor with the modified data
              editorRef.current?.render({
                blocks: blocks
              });

              // Save the updated content
              editorRef.current?.save().then((updatedData) => {
                setEditorData(updatedData);
                const jsonData = JSON.stringify(updatedData);
                setHtmlValue(jsonData);
                onChange(jsonData);
              });
            }
          }
        });
      }
    } else if (activeTab === "html") {
      const textarea = document.getElementById("html-editor") as HTMLTextAreaElement;
      if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newValue =
          htmlValue.substring(0, start) +
          `{{${variable}}}` +
          htmlValue.substring(end);

        setHtmlValue(newValue);
        onChange(newValue);

        // Set cursor position after the inserted variable
        setTimeout(() => {
          textarea.focus();
          textarea.selectionStart = start + variable.length + 4; // +4 for {{ and }}
          textarea.selectionEnd = start + variable.length + 4;
        }, 0);
      }
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="visual" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Visual Editor
            </TabsTrigger>
            <TabsTrigger value="html" className="flex items-center gap-2">
              <CodeIcon className="h-4 w-4" />
              JSON
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
          </TabsList>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("user.name")}
            >
              <User className="mr-1 h-4 w-4" />
              User
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("organization.name")}
            >
              <Building className="mr-1 h-4 w-4" />
              Organization
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("project.name")}
            >
              <FolderKanban className="mr-1 h-4 w-4" />
              Project
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("date")}
            >
              <Calendar className="mr-1 h-4 w-4" />
              Date
            </Button>
          </div>
        </div>

        <TabsContent value="visual" className="mt-0">
          <Card>
            <CardContent className="p-4">
              <div ref={editorInstanceRef} style={{ minHeight: `${height}px` }}></div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="html" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <textarea
                id="html-editor"
                className="w-full h-[500px] p-4 font-mono text-sm resize-none border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={htmlValue}
                onChange={handleHtmlChange}
                spellCheck={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="mt-0">
          <Card>
            <CardContent className="p-4">
              {isRendering ? (
                <div className="flex items-center justify-center py-8">
                  <Spinner className="mr-2" />
                  <span>Rendering template...</span>
                </div>
              ) : renderError ? (
                <div className="flex items-start gap-2 p-4 border border-destructive/50 bg-destructive/10 rounded-md">
                  <AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-destructive">Error rendering template</h4>
                    <p className="text-sm text-muted-foreground">{renderError}</p>
                  </div>
                </div>
              ) : (
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: previewValue }}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
