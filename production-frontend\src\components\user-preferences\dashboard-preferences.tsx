"use client";

import { useState } from "react";
import { useUpdateUserPreferences } from "@/hooks/user-preferences";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { UserPreferences } from "@/types/user";
import { FileText, FolderKanban, Clock, MessageSquare } from "lucide-react";

interface DashboardPreferencesProps {
  preferences: UserPreferences['dashboard'];
}

export function DashboardPreferences({ preferences }: DashboardPreferencesProps) {
  const [dashboardPrefs, setDashboardPrefs] = useState<UserPreferences['dashboard']>(
    preferences || {
      defaultView: 'recent',
      showWelcomeMessage: true,
      pinnedProjects: [],
      pinnedDocuments: [],
    }
  );

  const updatePreferences = useUpdateUserPreferences();

  const handleViewChange = (value: 'documents' | 'projects' | 'recent') => {
    const updatedPrefs = {
      ...dashboardPrefs,
      defaultView: value,
    };
    
    setDashboardPrefs(updatedPrefs);
    
    // Update preferences in the backend
    updatePreferences({
      dashboard: updatedPrefs,
    }).catch(console.error);
  };

  const handleWelcomeToggle = () => {
    const updatedPrefs = {
      ...dashboardPrefs,
      showWelcomeMessage: !dashboardPrefs.showWelcomeMessage,
    };
    
    setDashboardPrefs(updatedPrefs);
    
    // Update preferences in the backend
    updatePreferences({
      dashboard: updatedPrefs,
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-sm font-medium mb-4">Default Dashboard View</h3>
        <RadioGroup
          defaultValue={dashboardPrefs.defaultView}
          onValueChange={(value) => handleViewChange(value as 'documents' | 'projects' | 'recent')}
          className="grid grid-cols-3 gap-4"
        >
          <div>
            <RadioGroupItem
              value="documents"
              id="view-documents"
              className="peer sr-only"
            />
            <Label
              htmlFor="view-documents"
              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              <FileText className="mb-3 h-6 w-6" />
              Documents
            </Label>
          </div>
          <div>
            <RadioGroupItem
              value="projects"
              id="view-projects"
              className="peer sr-only"
            />
            <Label
              htmlFor="view-projects"
              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              <FolderKanban className="mb-3 h-6 w-6" />
              Projects
            </Label>
          </div>
          <div>
            <RadioGroupItem
              value="recent"
              id="view-recent"
              className="peer sr-only"
            />
            <Label
              htmlFor="view-recent"
              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              <Clock className="mb-3 h-6 w-6" />
              Recent
            </Label>
          </div>
        </RadioGroup>
      </div>

      <div className="flex items-center justify-between space-x-2 pt-4">
        <div className="flex items-center space-x-2">
          <MessageSquare className="h-4 w-4" />
          <label htmlFor="welcome-message" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
            Show Welcome Message
          </label>
        </div>
        <Switch
          id="welcome-message"
          checked={dashboardPrefs.showWelcomeMessage}
          onCheckedChange={handleWelcomeToggle}
        />
      </div>
    </div>
  );
}
