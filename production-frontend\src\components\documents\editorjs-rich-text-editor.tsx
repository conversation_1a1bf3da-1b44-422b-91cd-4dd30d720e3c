"use client";

import { useState, useEffect, useRef } from "react";
import EditorJS from "@editorjs/editorjs";
import Header from "@editorjs/header";
import List from "@editorjs/list";
import Checklist from "@editorjs/checklist";
import Paragraph from "@editorjs/paragraph";
import Image from "@editorjs/image";
import Embed from "@editorjs/embed";
import Table from "@editorjs/table";
import Link from "@editorjs/link";
import Marker from "@editorjs/marker";
import InlineCode from "@editorjs/inline-code";
import Quote from "@editorjs/quote";
import Code from "@editorjs/code";
import Underline from "@editorjs/underline";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Toolt<PERSON>, TooltipContent, Toolt<PERSON><PERSON>rovider, TooltipTrigger } from "@/components/ui/tooltip";
// Removed deprecated useSignalR import - use CollaborationProvider instead
// SignalR imports removed - use CollaborationProvider for collaborative features
// Define local constants for removed SignalR enums
const HubMethod = {
  JOIN_DOCUMENT_GROUP: 'JoinDocumentGroup',
  LEAVE_DOCUMENT_GROUP: 'LeaveDocumentGroup',
  SEND_DOCUMENT_CHANGE: 'SendDocumentChange',
  SEND_CURSOR_POSITION: 'SendCursorPosition'
} as const

const HubEvent = {
  USER_JOINED_DOCUMENT: 'UserJoinedDocument',
  USER_LEFT_DOCUMENT: 'UserLeftDocument',
  DOCUMENT_CHANGED: 'DocumentChanged'
} as const
import { useToast } from "@/components/ui/use-toast";
import { useEventSubscription } from "@/hooks/infrastructure";
import { EventType } from "@/services/event-grid-service";
import { Save, Users, Clock, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { debounce } from "lodash";
import { documentService } from "@/services/optimized-document-service";

interface User {
  id: string;
  name: string;
  avatarUrl?: string;
  isActive: boolean;
  lastActive: string;
  cursorPosition?: {
    blockId: string;
    offset: number;
  };
  selection?: {
    start: {
      blockId: string;
      offset: number;
    };
    end: {
      blockId: string;
      offset: number;
    };
  };
}

interface DocumentEdit {
  userId: string;
  userName?: string;
  timestamp: string;
  // Text-level operations
  addedText?: string;
  removedText?: string;
  position?: number;
  // Block-level operations
  blockOperation?: 'add' | 'remove' | 'update' | 'move';
  blockIndex?: number;
  blockData?: {
    type: string;
    data: any;
  };
  // Legacy operations array for backward compatibility
  operations?: Array<{
    type: "addBlock" | "removeBlock" | "updateBlock" | "moveBlock";
    blockId?: string;
    data?: any;
    position?: number;
  }>;
}

interface RichTextEditorProps {
  documentId: string;
  projectId: string;
  organizationId: string;
  documentName: string;
  initialContent: string;
  currentUser: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  readOnly?: boolean;
  autoSaveInterval?: number; // in milliseconds
  onSaved?: () => void;
}

export function EditorJSRichTextEditor({
  documentId,
  projectId,
  organizationId,
  documentName,
  initialContent,
  currentUser,
  readOnly = false,
  autoSaveInterval = 30000, // Default to 30 seconds
  onSaved
}: RichTextEditorProps) {
  // Deprecated SignalR functionality - use CollaborationProvider instead
  const invoke = async (...args: any[]) => console.log('SignalR invoke deprecated:', args)
  const isConnected = false
  const subscribe = (...args: any[]) => console.log('SignalR subscribe deprecated:', args)
  const unsubscribe = (...args: any[]) => console.log('SignalR unsubscribe deprecated:', args)
  const { toast } = useToast();
  const [, setContent] = useState<any>(null);
  const [activeUsers, setActiveUsers] = useState<User[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const editorRef = useRef<EditorJS | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const contentRef = useRef<string>(initialContent);
  const editorInstanceRef = useRef<HTMLDivElement>(null);

  // Initialize Editor.js
  useEffect(() => {
    if (!editorInstanceRef.current) return;

    let parsedInitialContent;
    try {
      // Try to parse the initial content as JSON
      parsedInitialContent = initialContent ? JSON.parse(initialContent) : { blocks: [] };
    } catch (error) {
      console.error("Failed to parse initial content:", error);
      // If parsing fails, create a default paragraph with the content
      parsedInitialContent = {
        blocks: [
          {
            type: "paragraph",
            data: {
              text: initialContent || ""
            }
          }
        ]
      };
    }

    // Initialize Editor.js
    const editor = new EditorJS({
      holder: editorInstanceRef.current,
      readOnly,
      tools: {
        header: {
          class: Header as any,
          inlineToolbar: true,
          config: {
            levels: [1, 2, 3, 4, 5, 6],
            defaultLevel: 2
          }
        },
        list: {
          class: List as any,
          inlineToolbar: true
        },
        checklist: {
          class: Checklist as any,
          inlineToolbar: true
        },
        paragraph: {
          class: Paragraph as any,
          inlineToolbar: true
        },
        image: {
          class: Image as any,
          config: {
            uploader: {
              uploadByFile(file: File) {
                return new Promise((resolve, reject) => {
                  // Create a FileReader to convert the file to a data URL
                  const reader = new FileReader();
                  reader.onload = (event) => {
                    if (event.target && event.target.result) {
                      resolve({
                        success: 1,
                        file: {
                          url: event.target.result as string
                        }
                      });
                    } else {
                      reject(new Error("Failed to read file"));
                    }
                  };
                  reader.onerror = (error) => reject(error);
                  reader.readAsDataURL(file);
                });
              },
              uploadByUrl(url: string) {
                return Promise.resolve({
                  success: 1,
                  file: {
                    url
                  }
                });
              }
            }
          }
        },
        embed: Embed as any,
        table: Table as any,
        link: Link as any,
        marker: Marker as any,
        inlineCode: InlineCode as any,
        quote: Quote as any,
        code: Code as any,
        underline: Underline as any
      },
      data: parsedInitialContent,
      onChange: handleEditorChange
    });

    editorRef.current = editor;

    // Clean up on unmount
    return () => {
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    };
  }, [editorInstanceRef, readOnly]);

  // Join document editing group on mount
  useEffect(() => {
    if (isConnected) {
      // Join document group
      invoke(HubMethod.JOIN_DOCUMENT_GROUP, documentId, {
        userId: currentUser.id,
        userName: currentUser.name,
        avatarUrl: currentUser.avatarUrl
      }).catch((error: any) => {
        console.error("Failed to join document group:", error);
      });

      // Define handlers
      const userPresenceHandler = (user: User) => {
        if (user.id !== currentUser.id) {
          setActiveUsers((prev) => {
            const existingUserIndex = prev.findIndex((u) => u.id === user.id);
            if (existingUserIndex >= 0) {
              const updatedUsers = [...prev];
              updatedUsers[existingUserIndex] = {
                ...updatedUsers[existingUserIndex],
                isActive: true,
                lastActive: new Date().toISOString()
              };
              return updatedUsers;
            } else {
              return [...prev, { ...user, isActive: true, lastActive: new Date().toISOString() }];
            }
          });

          toast({
            title: "User joined",
            description: `${user.name} is now editing this document`,
            duration: 3000
          });
        }
      };

      // Subscribe to user presence
      subscribe(HubEvent.USER_JOINED_DOCUMENT, userPresenceHandler);

      const userLeftHandler = (userId: string, userName: string) => {
        if (userId !== currentUser.id) {
          setActiveUsers((prev) => {
            const existingUserIndex = prev.findIndex((u) => u.id === userId);
            if (existingUserIndex >= 0) {
              const updatedUsers = [...prev];
              updatedUsers[existingUserIndex] = {
                ...updatedUsers[existingUserIndex],
                isActive: false,
                lastActive: new Date().toISOString()
              };
              return updatedUsers;
            }
            return prev;
          });

          toast({
            title: "User left",
            description: `${userName} is no longer editing this document`,
            duration: 3000
          });
        }
      };

      // Subscribe to user left
      subscribe(HubEvent.USER_LEFT_DOCUMENT, userLeftHandler);

      const documentEditHandler = (edit: DocumentEdit) => {
        if (edit.userId === currentUser.id) return;

        if (editorRef.current) {
          // Apply operations to the editor
          try {
            const editor = editorRef.current;
            const editData = edit;

            // Handle text-level operations
            if (editData.addedText) {
              // Handle text addition
              if (editor.blocks) {
                const blockIndex = editData.blockIndex || 0;
                const block = editor.blocks.getBlockByIndex(blockIndex);

                if (block) {
                  // Get current text
                  const currentText = block.holder.textContent || '';

                  // Calculate position
                  const position = editData.position || 0;

                  // Insert text at position
                  const newText =
                    currentText.substring(0, position) +
                    editData.addedText +
                    currentText.substring(position);

                  // Update block content
                  editor.blocks.update(block.id, { text: newText });

                  // Notify other users about the change
                  toast({
                    title: "Collaborative Edit",
                    description: `${editData.userName || "Someone"} added text to the document`,
                    variant: "default",
                  });
                }
              }
            } else if (editData.removedText) {
              // Handle text removal
              if (editor.blocks) {
                const blockIndex = editData.blockIndex || 0;
                const block = editor.blocks.getBlockByIndex(blockIndex);

                if (block) {
                  // Get current text
                  const currentText = block.holder.textContent || '';

                  // Calculate position
                  const position = editData.position || 0;
                  const endPosition = position + editData.removedText.length;

                  // Remove text at position
                  const newText =
                    currentText.substring(0, position) +
                    currentText.substring(endPosition);

                  // Update block content
                  editor.blocks.update(block.id, { text: newText });

                  // Notify other users about the change
                  toast({
                    title: "Collaborative Edit",
                    description: `${editData.userName || "Someone"} removed text from the document`,
                    variant: "default",
                  });
                }
              }
            } else if (editData.blockOperation === 'add') {
              // Handle block addition
              if (editor.blocks && editData.blockData) {
                const index = editData.blockIndex || editor.blocks.getBlocksCount();

                // Insert new block
                editor.blocks.insert(
                  editData.blockData.type || 'paragraph',
                  editData.blockData.data || { text: '' },
                  {},
                  index,
                  true
                );

                // Notify other users about the change
                toast({
                  title: "Collaborative Edit",
                  description: `${editData.userName || "Someone"} added a new block to the document`,
                  variant: "default",
                });
              }
            } else if (editData.blockOperation === 'remove') {
              // Handle block removal
              if (editor.blocks && editData.blockIndex !== undefined) {
                // Delete block
                editor.blocks.delete(editData.blockIndex);

                // Notify other users about the change
                toast({
                  title: "Collaborative Edit",
                  description: `${editData.userName || "Someone"} removed a block from the document`,
                  variant: "default",
                });
              }
            }

            // Refresh the editor view
            try {
              if (editor.render) {
                editor.render({ blocks: [] });
              }
            } catch (renderError) {
              console.warn("Editor render failed:", renderError);
            }
          } catch (error) {
            console.error("Failed to apply collaborative edit:", error);
          }
        }
      };

      // Subscribe to document edits
      subscribe(HubEvent.DOCUMENT_CHANGED, documentEditHandler);

      // Clean up on unmount
      return () => {
        // Leave document group
        invoke(HubMethod.LEAVE_DOCUMENT_GROUP, documentId, currentUser.id).catch((error: any) => {
          console.error("Failed to leave document group:", error);
        });

        // Unsubscribe from events
        unsubscribe(HubEvent.USER_JOINED_DOCUMENT, userPresenceHandler);
        unsubscribe(HubEvent.USER_LEFT_DOCUMENT, userLeftHandler);
        unsubscribe(HubEvent.DOCUMENT_CHANGED, documentEditHandler);
      };
    }
  }, [isConnected, documentId, currentUser, invoke, subscribe, unsubscribe, toast]);

  // Auto-save functionality
  useEffect(() => {
    if (readOnly) return;

    const autoSaveTimer = setInterval(() => {
      if (hasUnsavedChanges && !isSaving) {
        handleSave();
      }
    }, autoSaveInterval);

    return () => clearInterval(autoSaveTimer);
  }, [hasUnsavedChanges, isSaving, readOnly, autoSaveInterval]);

  // Subscribe to document events
  useEventSubscription([EventType.DOCUMENT_UPDATED], (event) => {
    if (event.data.documentId === documentId && event.data.userId !== currentUser.id) {
      toast({
        title: "Document Updated",
        description: `${event.data.user?.name || "Someone"} updated this document`,
      });
    }
  });

  // Handle editor change
  const handleEditorChange = debounce(() => {
    if (readOnly || !editorRef.current) return;

    setIsTyping(true);
    setHasUnsavedChanges(true);

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 2000);

    // Save current content to ref
    editorRef.current.save().then((outputData) => {
      setContent(outputData);
      contentRef.current = JSON.stringify(outputData);

      // Send real-time updates to other users
      if (isConnected) {
        try {
          // Get the current selection
          const selection = window.getSelection();
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            const blockElement = range.startContainer.parentElement?.closest('.ce-block');

            if (blockElement && editorRef.current) {
              const blockIndex = parseInt((blockElement as HTMLElement).dataset.index || '0');
              const block = editorRef.current.blocks.getBlockByIndex(blockIndex);

              if (block) {
                // Send the edit to other users
                invoke(HubMethod.SEND_DOCUMENT_CHANGE, documentId, {
                  userId: currentUser.id,
                  userName: currentUser.name,
                  blockIndex: blockIndex,
                  blockData: {
                    type: block.name,
                    data: block.save()
                  },
                  timestamp: new Date().toISOString()
                }).catch((error: any) => {
                  console.error("Failed to send document edit:", error);
                });

                // Send cursor position
                invoke(HubMethod.SEND_CURSOR_POSITION, documentId, {
                  userId: currentUser.id,
                  userName: currentUser.name,
                  blockIndex: blockIndex,
                  position: range.startOffset,
                  timestamp: new Date().toISOString()
                }).catch((error: any) => {
                  console.error("Failed to send cursor position:", error);
                });
              }
            }
          }
        } catch (error) {
          console.error("Error sending real-time updates:", error);
        }
      }
    });
  }, 300);

  // Handle save
  const handleSave = async () => {
    if (readOnly || !hasUnsavedChanges || isSaving || !editorRef.current) return;

    setIsSaving(true);

    try {
      // Get the current content from Editor.js
      const savedData = await editorRef.current.save();
      const contentToSave = JSON.stringify(savedData);

      // Save document content
      await documentService.updateDocumentContent(
        documentId,
        contentToSave,
        projectId,
        organizationId
      );

      setLastSaved(new Date());
      setHasUnsavedChanges(false);

      toast({
        title: "Document Saved",
        description: "Your changes have been saved successfully",
        variant: "default"
      });

      if (onSaved) {
        onSaved();
      }
    } catch (error) {
      console.error("Error saving document:", error);

      toast({
        title: "Save Failed",
        description: "Failed to save document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Save className="h-5 w-5" />
              {documentName}
            </CardTitle>
            <CardDescription>
              {readOnly ? "Viewing document" : "Edit document in real-time with your team"}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {!readOnly && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={isSaving || !hasUnsavedChanges}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <Badge variant="outline" className="h-8 flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {activeUsers.filter(u => u.isActive).length + 1}
                    </Badge>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-2">
                    <p className="font-semibold">Active Users</p>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={currentUser.avatarUrl} alt={currentUser.name} />
                          <AvatarFallback>
                            {currentUser.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{currentUser.name} (You)</span>
                      </div>
                      {activeUsers
                        .filter(user => user.isActive)
                        .map(user => (
                          <div key={user.id} className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={user.avatarUrl} alt={user.name} />
                              <AvatarFallback>
                                {user.name.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span>{user.name}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        {lastSaved && (
          <div className="flex items-center mt-2 text-xs text-muted-foreground">
            <Clock className="mr-1 h-3 w-3" />
            Last saved: {lastSaved.toLocaleTimeString()}
            {hasUnsavedChanges ? (
              <Badge variant="outline" className="ml-2 text-xs">
                <AlertCircle className="mr-1 h-3 w-3 text-amber-500" />
                Unsaved changes
              </Badge>
            ) : (
              <Badge variant="outline" className="ml-2 text-xs">
                <CheckCircle className="mr-1 h-3 w-3 text-green-500" />
                All changes saved
              </Badge>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="relative border rounded-md p-4">
          <div ref={editorInstanceRef} className="min-h-[500px]"></div>
          {isTyping && (
            <div className="absolute bottom-2 right-2">
              <Badge variant="secondary" className="animate-pulse">
                Someone is typing...
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
