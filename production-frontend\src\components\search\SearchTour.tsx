import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { X } from 'lucide-react';

export default function SearchTour() {
  const [showTour, setShowTour] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    // Check if the user has seen the tour before
    const hasSeenTour = localStorage.getItem('search-tour-completed');
    if (!hasSeenTour) {
      setShowTour(true);
    }
  }, []);

  const steps = [
    {
      title: 'Welcome to Advanced Search',
      description: 'Our powerful search helps you find documents and information quickly. Let\'s take a quick tour of the features.',
    },
    {
      title: 'Search Filters',
      description: 'Use filters to narrow down your search results by document type, date, author, and more.',
    },
    {
      title: 'Semantic Search',
      description: 'Our AI-powered semantic search understands the meaning behind your query, not just keywords.',
    },
    {
      title: 'Search Operators',
      description: 'Use operators like AND, OR, NOT, and quotes for precise searches. Example: "quarterly report" AND finance NOT draft',
    },
    {
      title: 'Save Searches',
      description: 'Save your frequent searches for quick access later. Click the bookmark icon next to any search.',
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      completeTour();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeTour = () => {
    localStorage.setItem('search-tour-completed', 'true');
    setShowTour(false);
  };

  if (!showTour) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <Card className="w-[500px] max-w-[90vw]">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{steps[currentStep].title}</CardTitle>
            <Button variant="ghost" size="icon" onClick={completeTour}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <CardDescription>Step {currentStep + 1} of {steps.length}</CardDescription>
        </CardHeader>
        <CardContent>
          <p>{steps[currentStep].description}</p>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={handlePrevious} disabled={currentStep === 0}>
            Previous
          </Button>
          <Button onClick={handleNext}>
            {currentStep < steps.length - 1 ? 'Next' : 'Finish'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
