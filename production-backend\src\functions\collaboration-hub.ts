/**
 * Collaboration Hub - Centralized SignalR Hub for Real-time Collaboration
 * Handles document collaboration, session management, and real-time messaging
 * Only initializes when collaborative features are actually needed
 */

import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { logger } from '../shared/utils/logger';
import { authenticateRequest } from '../shared/utils/auth';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { signalRService } from '../shared/services/signalr';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { v4 as uuidv4 } from 'uuid';

interface CollaborationSession {
  id: string;
  documentId?: string;
  title: string;
  description?: string;
  createdBy: string;
  participants: string[];
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'ended';
  maxParticipants?: number;
}

interface DocumentLock {
  documentId: string;
  userId: string;
  userName: string;
  lockedAt: string;
  sessionId?: string;
}

interface PresenceInfo {
  userId: string;
  userName: string;
  documentId?: string;
  sessionId?: string;
  cursor?: { x: number; y: number; page?: number };
  lastSeen: string;
  isTyping?: boolean;
}

class CollaborationHubManager {
  private signalRServiceInstance: any;

  constructor() {
    this.signalRServiceInstance = signalRService.getInstance();
  }

  /**
   * Create a new collaboration session
   */
  async createSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;
      const body = await request.json() as {
        documentId?: string;
        title: string;
        description?: string;
        maxParticipants?: number;
      };
      const { documentId, title, description, maxParticipants = 10 } = body;

      if (!title) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Title is required' }
        }, request);
      }

      // Create collaboration session
      const session: CollaborationSession = {
        id: uuidv4(),
        documentId,
        title,
        description,
        createdBy: user.id,
        participants: [user.id],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        status: 'active',
        maxParticipants
      };

      // Save to database
      await db.createItem('collaboration-sessions', session);

      // Cache session info
      await redis.setex(`collaboration:session:${session.id}`, 3600, JSON.stringify(session));

      logger.info('Collaboration session created', {
        correlationId,
        sessionId: session.id,
        createdBy: user.id,
        documentId
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: { session }
      }, request);

    } catch (error) {
      logger.error('Create collaboration session failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Join a collaboration session
   */
  async joinSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;
      const url = new URL(request.url);
      const sessionId = url.pathname.split('/').pop();

      if (!sessionId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Session ID is required' }
        }, request);
      }

      // Get session from cache or database
      let session: CollaborationSession | null = null;
      const cached = await redis.get(`collaboration:session:${sessionId}`);
      
      if (cached) {
        session = JSON.parse(cached);
      } else {
        session = await db.readItem('collaboration-sessions', sessionId, 'default');
        if (session) {
          await redis.setex(`collaboration:session:${sessionId}`, 3600, JSON.stringify(session));
        }
      }

      if (!session || session.status !== 'active') {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Session not found or inactive' }
        }, request);
      }

      // Check if user is already a participant
      if (!session.participants.includes(user.id)) {
        // Check max participants limit
        if (session.maxParticipants && session.participants.length >= session.maxParticipants) {
          return addCorsHeaders({
            status: 409,
            jsonBody: { error: 'Session is full' }
          }, request);
        }

        // Add user to participants
        session.participants.push(user.id);
        session.updatedAt = new Date().toISOString();

        // Update database and cache
        await db.updateItem('collaboration-sessions', session);
        await redis.setex(`collaboration:session:${sessionId}`, 3600, JSON.stringify(session));
      }

      // Notify other participants via SignalR
      await this.signalRServiceInstance.sendToGroup(`collaboration:${sessionId}`, {
        target: 'UserJoined',
        arguments: [{
          userId: user.id,
          userName: user.displayName || user.email,
          joinedAt: new Date().toISOString()
        }]
      });

      logger.info('User joined collaboration session', {
        correlationId,
        sessionId,
        userId: user.id,
        participantCount: session.participants.length
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: { session }
      }, request);

    } catch (error) {
      logger.error('Join collaboration session failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Leave a collaboration session
   */
  async leaveSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;
      const url = new URL(request.url);
      const sessionId = url.pathname.split('/')[url.pathname.split('/').length - 2]; // Get sessionId from path

      if (!sessionId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Session ID is required' }
        }, request);
      }

      // Get session
      const cached = await redis.get(`collaboration:session:${sessionId}`);
      let session: CollaborationSession | null = null;
      
      if (cached) {
        session = JSON.parse(cached);
      } else {
        session = await db.readItem('collaboration-sessions', sessionId, 'default');
      }

      if (!session) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Session not found' }
        }, request);
      }

      // Remove user from participants
      session.participants = session.participants.filter(id => id !== user.id);
      session.updatedAt = new Date().toISOString();

      // If no participants left, end the session
      if (session.participants.length === 0) {
        session.status = 'ended';
      }

      // Update database and cache
      await db.updateItem('collaboration-sessions', session);
      await redis.setex(`collaboration:session:${sessionId}`, 3600, JSON.stringify(session));

      // Notify other participants via SignalR
      await this.signalRServiceInstance.sendToGroup(`collaboration:${sessionId}`, {
        target: 'UserLeft',
        arguments: [user.id]
      });

      logger.info('User left collaboration session', {
        correlationId,
        sessionId,
        userId: user.id,
        remainingParticipants: session.participants.length
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: { success: true }
      }, request);

    } catch (error) {
      logger.error('Leave collaboration session failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Get collaboration session details
   */
  async getSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const url = new URL(request.url);
      const sessionId = url.pathname.split('/').pop();

      if (!sessionId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Session ID is required' }
        }, request);
      }

      // Get session from cache or database
      let session: CollaborationSession | null = null;
      const cached = await redis.get(`collaboration:session:${sessionId}`);
      
      if (cached) {
        session = JSON.parse(cached);
      } else {
        session = await db.readItem('collaboration-sessions', sessionId, 'default');
        if (session) {
          await redis.setex(`collaboration:session:${sessionId}`, 3600, JSON.stringify(session));
        }
      }

      if (!session) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Session not found' }
        }, request);
      }

      return addCorsHeaders({
        status: 200,
        jsonBody: { session }
      }, request);

    } catch (error) {
      logger.error('Get collaboration session failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }
}

// Create instance of the manager
const collaborationHubManager = new CollaborationHubManager();

// Register HTTP functions for collaboration management
app.http('collaboration-session-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions',
  handler: (request, context) => collaborationHubManager.createSession(request, context)
});

app.http('collaboration-session-join', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/{sessionId}/join',
  handler: (request, context) => collaborationHubManager.joinSession(request, context)
});

app.http('collaboration-session-leave', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/{sessionId}/leave',
  handler: (request, context) => collaborationHubManager.leaveSession(request, context)
});

app.http('collaboration-session-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/{sessionId}',
  handler: (request, context) => collaborationHubManager.getSession(request, context)
});
