/**
 * Enhanced Event Integration Service
 * Comprehensive integration with Event Grid and Service Bus for real-time features
 */

import { eventGridService, EventType } from './event-grid-service'
import { serviceBusService, MessageType } from './service-bus-service'
import { 
  useDocumentStore,
  useWorkflowStore,
  useCollaborationStore,
  useNotificationStore,
  useAIStore,
  useDashboardStore,
  useOrganizationStore
} from '@/stores'

// Event types for comprehensive integration
export enum EnhancedEventType {
  // Document events
  DOCUMENT_CREATED = 'document.created',
  DOCUMENT_PROCESSED = 'document.processed',
  DOCUMENT_SHARED = 'document.shared',
  DOCUMENT_SIGNED = 'document.signed',
  DOCUMENT_COMMENTED = 'document.commented',
  DOCUMENT_LOCKED = 'document.locked',
  
  // Workflow events
  WORKFLOW_STARTED = 'workflow.started',
  WORKFLOW_STEP_COMPLETED = 'workflow.step.completed',
  WORKFLOW_COMPLETED = 'workflow.completed',
  WORKFLOW_FAILED = 'workflow.failed',
  WORKFLOW_APPROVED = 'workflow.approved',
  WORKFLOW_REJECTED = 'workflow.rejected',
  
  // Collaboration events
  COLLABORATION_USER_JOINED = 'collaboration.user.joined',
  COLLABORATION_USER_LEFT = 'collaboration.user.left',
  COLLABORATION_CURSOR_MOVED = 'collaboration.cursor.moved',
  COLLABORATION_EDIT_MADE = 'collaboration.edit.made',
  COLLABORATION_COMMENT_ADDED = 'collaboration.comment.added',
  
  // AI events
  AI_OPERATION_STARTED = 'ai.operation.started',
  AI_OPERATION_COMPLETED = 'ai.operation.completed',
  AI_MODEL_TRAINED = 'ai.model.trained',
  AI_ANALYSIS_READY = 'ai.analysis.ready',
  
  // System events
  SYSTEM_HEALTH_CHANGED = 'system.health.changed',
  SYSTEM_ALERT = 'system.alert',
  SYSTEM_MAINTENANCE = 'system.maintenance',
  
  // User events
  USER_ACTION = 'user.action',
  USER_LOGIN = 'user.login',
  USER_LOGOUT = 'user.logout',
  
  // Analytics events
  ANALYTICS_METRIC_UPDATED = 'analytics.metric.updated',
  ANALYTICS_REPORT_GENERATED = 'analytics.report.generated'
}

// Service Bus queue names
export enum ServiceBusQueue {
  WORKFLOW_ORCHESTRATION = 'workflow-orchestration',
  DOCUMENT_COLLABORATION = 'document-collaboration',
  ANALYTICS_EVENTS = 'analytics-events',
  MONITORING_EVENTS = 'monitoring-events',
  AI_OPERATIONS = 'ai-operations',
  SCHEDULED_EMAILS = 'scheduled-emails',
  NOTIFICATION_DELIVERY = 'notification-delivery',
  DOCUMENT_PROCESSING = 'document-processing'
}

// Enhanced event data interfaces
export interface DocumentEventData {
  documentId: string
  documentName: string
  organizationId: string
  userId: string
  status: string
  metadata?: any
  timestamp: string
}

export interface WorkflowEventData {
  workflowId: string
  executionId: string
  stepId?: string
  status: string
  progress: number
  organizationId: string
  userId: string
  metadata?: any
  timestamp: string
}

export interface CollaborationEventData {
  sessionId: string
  documentId: string
  userId: string
  userName: string
  action: string
  data?: any
  timestamp: string
}

export interface AIEventData {
  operationId: string
  type: string
  status: string
  documentId?: string
  modelId?: string
  results?: any
  organizationId: string
  timestamp: string
}

export interface SystemEventData {
  service: string
  status: string
  message: string
  severity: 'info' | 'warning' | 'error' | 'critical'
  metadata?: any
  timestamp: string
}

class EnhancedEventIntegrationService {
  private subscriptions: Map<string, () => void> = new Map()
  private isInitialized = false

  /**
   * Initialize all event subscriptions
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // Initialize Event Grid subscriptions
      await this.initializeEventGridSubscriptions()
      
      // Initialize Service Bus subscriptions
      await this.initializeServiceBusSubscriptions()
      
      this.isInitialized = true
      console.log('✅ Enhanced Event Integration initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced Event Integration:', error)
      throw error
    }
  }

  /**
   * Initialize Event Grid subscriptions
   */
  private async initializeEventGridSubscriptions(): Promise<void> {
    // Document events
    const documentUnsubscribe = eventGridService.subscribe([
      EnhancedEventType.DOCUMENT_CREATED as any,
      EnhancedEventType.DOCUMENT_PROCESSED as any,
      EnhancedEventType.DOCUMENT_SHARED as any,
      EnhancedEventType.DOCUMENT_SIGNED as any,
      EnhancedEventType.DOCUMENT_COMMENTED as any
    ], this.handleDocumentEvent.bind(this))

    if (documentUnsubscribe) {
      this.subscriptions.set('document-events', documentUnsubscribe)
    }

    // Workflow events
    const workflowUnsubscribe = eventGridService.subscribe([
      EnhancedEventType.WORKFLOW_STARTED as any,
      EnhancedEventType.WORKFLOW_STEP_COMPLETED as any,
      EnhancedEventType.WORKFLOW_COMPLETED as any,
      EnhancedEventType.WORKFLOW_FAILED as any,
      EnhancedEventType.WORKFLOW_APPROVED as any,
      EnhancedEventType.WORKFLOW_REJECTED as any
    ], this.handleWorkflowEvent.bind(this))

    if (workflowUnsubscribe) {
      this.subscriptions.set('workflow-events', workflowUnsubscribe)
    }

    // Collaboration events
    const collaborationUnsubscribe = eventGridService.subscribe([
      EnhancedEventType.COLLABORATION_USER_JOINED as any,
      EnhancedEventType.COLLABORATION_USER_LEFT as any,
      EnhancedEventType.COLLABORATION_CURSOR_MOVED as any,
      EnhancedEventType.COLLABORATION_EDIT_MADE as any,
      EnhancedEventType.COLLABORATION_COMMENT_ADDED as any
    ], this.handleCollaborationEvent.bind(this))

    if (collaborationUnsubscribe) {
      this.subscriptions.set('collaboration-events', collaborationUnsubscribe)
    }

    // AI events
    const aiUnsubscribe = eventGridService.subscribe([
      EnhancedEventType.AI_OPERATION_STARTED as any,
      EnhancedEventType.AI_OPERATION_COMPLETED as any,
      EnhancedEventType.AI_MODEL_TRAINED as any,
      EnhancedEventType.AI_ANALYSIS_READY as any
    ], this.handleAIEvent.bind(this))

    if (aiUnsubscribe) {
      this.subscriptions.set('ai-events', aiUnsubscribe)
    }

    // System events
    const systemUnsubscribe = eventGridService.subscribe([
      EnhancedEventType.SYSTEM_HEALTH_CHANGED as any,
      EnhancedEventType.SYSTEM_ALERT as any,
      EnhancedEventType.SYSTEM_MAINTENANCE as any
    ], this.handleSystemEvent.bind(this))

    if (systemUnsubscribe) {
      this.subscriptions.set('system-events', systemUnsubscribe)
    }
  }

  /**
   * Initialize Service Bus subscriptions
   */
  private async initializeServiceBusSubscriptions(): Promise<void> {
    // Workflow orchestration
    const workflowUnsubscribe = serviceBusService.subscribeToMessages(
      ServiceBusQueue.WORKFLOW_ORCHESTRATION,
      this.handleWorkflowMessage.bind(this)
    )
    this.subscriptions.set('workflow-messages', workflowUnsubscribe)

    // Document collaboration
    const collaborationUnsubscribe = serviceBusService.subscribeToMessages(
      ServiceBusQueue.DOCUMENT_COLLABORATION,
      this.handleCollaborationMessage.bind(this)
    )
    this.subscriptions.set('collaboration-messages', collaborationUnsubscribe)

    // Notification delivery
    const notificationUnsubscribe = serviceBusService.subscribeToMessages(
      ServiceBusQueue.NOTIFICATION_DELIVERY,
      this.handleNotificationMessage.bind(this)
    )
    this.subscriptions.set('notification-messages', notificationUnsubscribe)

    // AI operations
    const aiUnsubscribe = serviceBusService.subscribeToMessages(
      ServiceBusQueue.AI_OPERATIONS,
      this.handleAIMessage.bind(this)
    )
    this.subscriptions.set('ai-messages', aiUnsubscribe)

    // Analytics events
    const analyticsUnsubscribe = serviceBusService.subscribeToMessages(
      ServiceBusQueue.ANALYTICS_EVENTS,
      this.handleAnalyticsMessage.bind(this)
    )
    this.subscriptions.set('analytics-messages', analyticsUnsubscribe)

    // System monitoring
    const monitoringUnsubscribe = serviceBusService.subscribeToMessages(
      ServiceBusQueue.MONITORING_EVENTS,
      this.handleMonitoringMessage.bind(this)
    )
    this.subscriptions.set('monitoring-messages', monitoringUnsubscribe)
  }

  /**
   * Handle document events
   */
  private handleDocumentEvent(event: any): void {
    const data = event.data as DocumentEventData
    const documentStore = useDocumentStore.getState()
    const notificationStore = useNotificationStore.getState()

    switch (event.type) {
      case EnhancedEventType.DOCUMENT_CREATED:
        // Refresh document list if in current organization
        if (this.isCurrentOrganization(data.organizationId)) {
          // documentStore.refreshDocuments()
          console.log('Document created:', data.documentId)
        }
        break

      case EnhancedEventType.DOCUMENT_PROCESSED:
        // Update document status
        documentStore.invalidateDocument(data.documentId)

        // Show success notification
        notificationStore.addNotification({
          type: 'success',
          title: 'Document Processed',
          message: `${data.documentName} has been successfully processed`
        } as any)
        break

      case EnhancedEventType.DOCUMENT_SHARED:
        // Show sharing notification
        notificationStore.addNotification({
          type: 'info',
          title: 'Document Shared',
          message: `${data.documentName} has been shared with you`
        } as any)
        break

      case EnhancedEventType.DOCUMENT_SIGNED:
        // Update document status and show notification
        documentStore.invalidateDocument(data.documentId)
        notificationStore.addNotification({
          type: 'success',
          title: 'Document Signed',
          message: `${data.documentName} has been digitally signed`
        } as any)
        break

      case EnhancedEventType.DOCUMENT_COMMENTED:
        // Refresh document to show new comment
        documentStore.invalidateDocument(data.documentId)
        break
    }
  }

  /**
   * Handle workflow events
   */
  private handleWorkflowEvent(event: any): void {
    const data = event.data as WorkflowEventData
    const workflowStore = useWorkflowStore.getState()
    const notificationStore = useNotificationStore.getState()

    switch (event.type) {
      case EnhancedEventType.WORKFLOW_STARTED:
        // Log workflow started
        console.log('Workflow started:', data.workflowId, data.executionId)
        break

      case EnhancedEventType.WORKFLOW_STEP_COMPLETED:
        // Log step completed
        console.log('Workflow step completed:', data.stepId)
        break

      case EnhancedEventType.WORKFLOW_COMPLETED:
        // Show completion notification
        notificationStore.addNotification({
          type: 'success',
          title: 'Workflow Completed',
          message: `Workflow execution has completed successfully`
        } as any)
        break

      case EnhancedEventType.WORKFLOW_FAILED:
        // Show error notification
        notificationStore.addNotification({
          type: 'error',
          title: 'Workflow Failed',
          message: `Workflow execution has failed: ${data.metadata?.error || 'Unknown error'}`
        } as any)
        break
    }
  }

  /**
   * Handle collaboration events
   */
  private handleCollaborationEvent(event: any): void {
    const data = event.data as CollaborationEventData
    const collaborationStore = useCollaborationStore.getState()

    switch (event.type) {
      case EnhancedEventType.COLLABORATION_USER_JOINED:
        console.log('User joined collaboration:', data.userId, data.userName)
        break

      case EnhancedEventType.COLLABORATION_USER_LEFT:
        console.log('User left collaboration:', data.userId)
        break

      case EnhancedEventType.COLLABORATION_CURSOR_MOVED:
        console.log('Cursor moved:', data.userId)
        break

      case EnhancedEventType.COLLABORATION_EDIT_MADE:
        console.log('Edit made:', data.userId)
        break

      case EnhancedEventType.COLLABORATION_COMMENT_ADDED:
        console.log('Comment added:', data.userId)
        break
    }
  }

  /**
   * Handle AI events
   */
  private handleAIEvent(event: any): void {
    const data = event.data as AIEventData
    const aiStore = useAIStore.getState()
    const notificationStore = useNotificationStore.getState()
    const documentStore = useDocumentStore.getState()

    switch (event.type) {
      case EnhancedEventType.AI_OPERATION_STARTED:
        console.log('AI operation started:', data.operationId)
        break

      case EnhancedEventType.AI_OPERATION_COMPLETED:
        // If document analysis, update document
        if (data.documentId && data.results) {
          documentStore.invalidateDocument(data.documentId)
        }

        // Show completion notification
        notificationStore.addNotification({
          type: 'success',
          title: 'AI Operation Completed',
          message: `${data.type} operation has completed successfully`
        } as any)
        break

      case EnhancedEventType.AI_MODEL_TRAINED:
        notificationStore.addNotification({
          type: 'success',
          title: 'Model Training Complete',
          message: `AI model has been successfully trained`
        } as any)
        break
    }
  }

  /**
   * Handle system events
   */
  private handleSystemEvent(event: any): void {
    const data = event.data as SystemEventData
    const notificationStore = useNotificationStore.getState()
    const dashboardStore = useDashboardStore.getState()

    switch (event.type) {
      case EnhancedEventType.SYSTEM_HEALTH_CHANGED:
        // Show alert for unhealthy services
        if (data.status === 'unhealthy') {
          notificationStore.addNotification({
            type: 'error',
            title: 'System Alert',
            message: `${data.service} is experiencing issues: ${data.message}`
          } as any)
        }
        break

      case EnhancedEventType.SYSTEM_ALERT:
        // Show system alert
        notificationStore.addNotification({
          type: data.severity === 'critical' ? 'error' : 'warning',
          title: 'System Alert',
          message: data.message
        } as any)
        break

      case EnhancedEventType.SYSTEM_MAINTENANCE:
        // Show maintenance notification
        notificationStore.addNotification({
          type: 'info',
          title: 'System Maintenance',
          message: data.message
        } as any)
        break
    }
  }

  /**
   * Handle workflow orchestration messages
   */
  private handleWorkflowMessage(message: any): void {
    console.log('Workflow message received:', message.action, message.workflowId)
  }

  /**
   * Handle collaboration messages
   */
  private handleCollaborationMessage(message: any): void {
    console.log('Collaboration message received:', message.action, message.documentId)
  }

  /**
   * Handle notification messages
   */
  private handleNotificationMessage(message: any): void {
    const notificationStore = useNotificationStore.getState()

    notificationStore.addNotification({
      type: message.priority === 'high' ? 'error' : 'info',
      title: message.content.title,
      message: message.content.message
    } as any)
  }

  /**
   * Handle AI operation messages
   */
  private handleAIMessage(message: any): void {
    console.log('AI message received:', message.action, message.operationId)
  }

  /**
   * Handle analytics messages
   */
  private handleAnalyticsMessage(message: any): void {
    console.log('Analytics message received:', message.type)
  }

  /**
   * Handle monitoring messages
   */
  private handleMonitoringMessage(message: any): void {
    const notificationStore = useNotificationStore.getState()

    // Show alerts for critical issues
    if (message.alerts && message.alerts.length > 0) {
      message.alerts.forEach((alert: any) => {
        notificationStore.addNotification({
          type: alert.severity === 'critical' ? 'error' : 'warning',
          title: 'System Alert',
          message: alert.message
        } as any)
      })
    }
  }

  /**
   * Check if event is for current organization
   */
  private isCurrentOrganization(organizationId: string): boolean {
    const currentOrg = useOrganizationStore.getState().currentOrganization
    return currentOrg?.id === organizationId
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup(): void {
    this.subscriptions.forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribe()
      }
    })
    this.subscriptions.clear()
    this.isInitialized = false
  }
}

// Export singleton instance
export const enhancedEventIntegration = new EnhancedEventIntegrationService()

// Export for use in components
export default enhancedEventIntegration
