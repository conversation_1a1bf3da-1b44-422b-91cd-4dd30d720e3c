/**
 * Enhanced AI Service Integration
 * Handles document analysis, chat, generation, and AI operations
 */

import { backendApiClient } from './backend-api-client'
import { memoryCache } from '../lib/cache'
import { performanceMonitor } from '../lib/performance'
import type { AIOperation, Document } from '../types/backend'

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  metadata?: {
    documentId?: string
    operationId?: string
    confidence?: number
    sources?: string[]
  }
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  context?: {
    documentIds: string[]
    projectId?: string
    organizationId?: string
  }
  createdAt: number
  updatedAt: number
}

export interface AIAnalysisResult {
  summary: string
  keyPoints: string[]
  entities: Array<{
    text: string
    type: string
    confidence: number
  }>
  sentiment?: {
    score: number
    label: 'positive' | 'negative' | 'neutral'
  }
  topics: Array<{
    name: string
    confidence: number
  }>
  metadata: {
    processingTime: number
    model: string
    version: string
  }
}

export interface DocumentGenerationRequest {
  type: 'summary' | 'report' | 'email' | 'proposal' | 'contract'
  template?: string
  context: {
    sourceDocuments?: string[]
    requirements?: string
    tone?: 'formal' | 'casual' | 'professional'
    length?: 'short' | 'medium' | 'long'
  }
  customPrompt?: string
}

class AIService {
  private chatSessions = new Map<string, ChatSession>()
  private activeOperations = new Map<string, AIOperation>()

  constructor() {
    this.loadChatSessions()
  }

  // ============================================================================
  // CHAT FUNCTIONALITY
  // ============================================================================

  async createChatSession(title?: string, context?: ChatSession['context']): Promise<ChatSession> {
    const session: ChatSession = {
      id: this.generateId(),
      title: title || `Chat ${new Date().toLocaleString()}`,
      messages: [],
      context,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    }

    this.chatSessions.set(session.id, session)
    this.saveChatSessions()

    return session
  }

  async sendChatMessage(
    sessionId: string,
    content: string,
    context?: { documentId?: string }
  ): Promise<ChatMessage> {
    const session = this.chatSessions.get(sessionId)
    if (!session) {
      throw new Error('Chat session not found')
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: this.generateId(),
      role: 'user',
      content,
      timestamp: Date.now(),
      metadata: context,
    }

    session.messages.push(userMessage)

    try {
      // Prepare context for AI
      const chatContext = this.prepareChatContext(session)
      
      // Call backend AI service
      const startTime = performance.now()
      const response = await backendApiClient.startAIOperation({
        type: 'chat',
        parameters: {
          messages: session.messages,
          context: chatContext,
          sessionId,
        }
      })

      const duration = performance.now() - startTime
      performanceMonitor.recordMetric('AI_Chat_Request', duration)

      // Poll for completion
      const result = await this.pollAIOperation(response.id)
      
      // Add assistant response
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        role: 'assistant',
        content: result.results?.response || 'I apologize, but I encountered an error processing your request.',
        timestamp: Date.now(),
        metadata: {
          operationId: response.id,
          confidence: result.results?.confidence,
          sources: result.results?.sources,
        },
      }

      session.messages.push(assistantMessage)
      session.updatedAt = Date.now()

      this.saveChatSessions()

      return assistantMessage

    } catch (error) {
      console.error('Chat error:', error)
      
      // Add error message
      const errorMessage: ChatMessage = {
        id: this.generateId(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error. Please try again.',
        timestamp: Date.now(),
      }

      session.messages.push(errorMessage)
      session.updatedAt = Date.now()
      this.saveChatSessions()

      throw error
    }
  }

  getChatSession(sessionId: string): ChatSession | undefined {
    return this.chatSessions.get(sessionId)
  }

  getAllChatSessions(): ChatSession[] {
    return Array.from(this.chatSessions.values()).sort((a, b) => b.updatedAt - a.updatedAt)
  }

  deleteChatSession(sessionId: string): void {
    this.chatSessions.delete(sessionId)
    this.saveChatSessions()
  }

  // ============================================================================
  // DOCUMENT ANALYSIS
  // ============================================================================

  async analyzeDocument(
    documentId: string,
    options?: {
      analysisType?: 'full' | 'summary' | 'entities' | 'sentiment'
      extractTables?: boolean
      extractKeyValuePairs?: boolean
    }
  ): Promise<AIAnalysisResult> {
    const cacheKey = `analysis_${documentId}_${JSON.stringify(options || {})}`
    
    // Check cache first
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const startTime = performance.now()
      
      const operation = await backendApiClient.analyzeDocument(documentId, options)
      const result = await this.pollAIOperation(operation.id)

      const duration = performance.now() - startTime
      performanceMonitor.recordMetric('AI_Document_Analysis', duration)

      const analysisResult: AIAnalysisResult = {
        summary: result.results?.summary || '',
        keyPoints: result.results?.keyPoints || [],
        entities: result.results?.entities || [],
        sentiment: result.results?.sentiment,
        topics: result.results?.topics || [],
        metadata: {
          processingTime: duration,
          model: result.results?.model || 'unknown',
          version: result.results?.version || '1.0',
        },
      }

      // Cache for 30 minutes
      memoryCache.set(cacheKey, analysisResult, 30 * 60 * 1000)

      return analysisResult

    } catch (error) {
      console.error('Document analysis error:', error)
      throw error
    }
  }

  // ============================================================================
  // DOCUMENT GENERATION
  // ============================================================================

  async generateDocument(request: DocumentGenerationRequest): Promise<{
    content: string
    metadata: {
      operationId: string
      processingTime: number
      wordCount: number
    }
  }> {
    try {
      const startTime = performance.now()

      const operation = await backendApiClient.startAIOperation({
        type: 'generation',
        parameters: {
          generationType: request.type,
          template: request.template,
          context: request.context,
          customPrompt: request.customPrompt,
        }
      })

      const result = await this.pollAIOperation(operation.id)
      const duration = performance.now() - startTime

      performanceMonitor.recordMetric('AI_Document_Generation', duration)

      return {
        content: result.results?.content || '',
        metadata: {
          operationId: operation.id,
          processingTime: duration,
          wordCount: result.results?.wordCount || 0,
        },
      }

    } catch (error) {
      console.error('Document generation error:', error)
      throw error
    }
  }

  // ============================================================================
  // BATCH OPERATIONS
  // ============================================================================

  async processBatch(
    documentIds: string[],
    operationType: string,
    organizationId: string,
    batchName?: string
  ): Promise<AIOperation> {
    try {
      const operation = await backendApiClient.processBatch({
        documentIds,
        operationType,
        organizationId,
        batchName,
      })

      this.activeOperations.set(operation.id, operation)
      return operation

    } catch (error) {
      console.error('Batch processing error:', error)
      throw error
    }
  }

  // ============================================================================
  // OPERATION MANAGEMENT
  // ============================================================================

  async getAIOperation(operationId: string): Promise<AIOperation> {
    try {
      const operation = await backendApiClient.getAIOperation(operationId)
      this.activeOperations.set(operationId, operation)
      return operation
    } catch (error) {
      console.error('Failed to get AI operation:', error)
      throw error
    }
  }

  async cancelAIOperation(operationId: string): Promise<void> {
    try {
      await backendApiClient.cancelAIOperation(operationId)
      this.activeOperations.delete(operationId)
    } catch (error) {
      console.error('Failed to cancel AI operation:', error)
      throw error
    }
  }

  getActiveOperations(): AIOperation[] {
    return Array.from(this.activeOperations.values())
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private async pollAIOperation(operationId: string, maxAttempts = 30): Promise<AIOperation> {
    let attempts = 0
    
    while (attempts < maxAttempts) {
      const operation = await this.getAIOperation(operationId)
      
      if (operation.status === 'completed' || operation.status === 'failed') {
        return operation
      }

      // Wait before next poll (exponential backoff)
      const delay = Math.min(1000 * Math.pow(1.5, attempts), 10000)
      await new Promise(resolve => setTimeout(resolve, delay))
      
      attempts++
    }

    throw new Error(`AI operation ${operationId} timed out`)
  }

  private prepareChatContext(session: ChatSession): any {
    return {
      sessionId: session.id,
      documentIds: session.context?.documentIds || [],
      projectId: session.context?.projectId,
      organizationId: session.context?.organizationId,
      recentMessages: session.messages.slice(-10), // Last 10 messages for context
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private loadChatSessions(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem('ai_chat_sessions')
      if (stored) {
        const sessions = JSON.parse(stored)
        sessions.forEach((session: ChatSession) => {
          this.chatSessions.set(session.id, session)
        })
      }
    } catch (error) {
      console.warn('Failed to load chat sessions:', error)
    }
  }

  private saveChatSessions(): void {
    if (typeof window === 'undefined') return

    try {
      const sessions = Array.from(this.chatSessions.values())
      localStorage.setItem('ai_chat_sessions', JSON.stringify(sessions))
    } catch (error) {
      console.warn('Failed to save chat sessions:', error)
    }
  }
}

// Create singleton instance
export const aiService = new AIService()
