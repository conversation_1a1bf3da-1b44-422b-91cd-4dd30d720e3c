"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { Sidebar, SidebarItem, SidebarSection } from "@/components/ui/sidebar";
import { Navbar } from "@/components/ui/navbar";
import { Button } from "@/components/ui/button";
import {
  LayoutDashboard,
  Building2,
  FolderKanban,
  FileText,
  Search,
  Settings,
  Menu,
  BarChart,
  FileStack,
  GitBranch,
  Cpu,
  Brain
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { UserNav } from "@/components/user-nav";
import { OrganizationSwitcher } from "@/components/organization-switcher";
import { EnhancedMobileNav } from "@/components/enhanced-mobile-nav";
import { GlobalSearch } from "@/components/layout/GlobalSearch";
import { TenantProvider } from "@/components/tenant-provider";
import { SmartNotifications } from "@/components/notifications/smart-notifications";
import { ContextHelp } from "@/components/help/context-help";
import { EnhancedBreadcrumbs } from "@/components/navigation/enhanced-breadcrumbs";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const { user, isLoading, isAuthenticated, logout } = useAuth();
  const pathname = usePathname();
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Set user context for logging (temporarily disabled to prevent logging errors)
  useEffect(() => {
    if (user) {
      // TODO: Re-enable user context logging once logging service is fixed
      // setUserContext({
      //   id: user.id,
      //   name: user.firstName && user.lastName
      //     ? `${user.firstName} ${user.lastName}`
      //     : user.displayName
      // });
    }
  }, [user]);

  // Close mobile nav when path changes
  useEffect(() => {
    setIsMobileNavOpen(false);
  }, [pathname]);

  if (isLoading) {
    return <LoadingLayout />;
  }

  if (!isAuthenticated) {
    return null; // Auth provider will redirect
  }

  return (
    <TenantProvider>
      <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      <Sidebar
        className="hidden md:flex"
        collapsible
        defaultCollapsed={sidebarCollapsed}
        onCollapseChange={setSidebarCollapsed}
        header={
          <div className="flex items-center">
            <Link href="/dashboard" className="flex items-center">
              <span className="text-xl font-bold">hepz</span>
            </Link>
          </div>
        }
      >
        <SidebarSection>
          <Link href="/dashboard">
            <SidebarItem
              icon={<LayoutDashboard size={20} />}
              title="Dashboard"
              active={pathname === "/dashboard"}
              collapsed={sidebarCollapsed}
            />
          </Link>
          <Link href="/organizations">
            <SidebarItem
              icon={<Building2 size={20} />}
              title="Organizations"
              active={pathname?.startsWith("/organizations")}
              collapsed={sidebarCollapsed}
            />
          </Link>
          <Link href="/projects">
            <SidebarItem
              icon={<FolderKanban size={20} />}
              title="Projects"
              active={pathname?.startsWith("/projects")}
              collapsed={sidebarCollapsed}
            />
          </Link>
          {/* Documents, workflows, and templates are now accessed through projects */}
        </SidebarSection>

        <SidebarSection title="Tools">
          <Link href="/search">
            <SidebarItem
              icon={<Search size={20} />}
              title="Search"
              active={pathname?.startsWith("/search")}
              collapsed={sidebarCollapsed}
            />
          </Link>
          <Link href="/analytics">
            <SidebarItem
              icon={<BarChart size={20} />}
              title="Analytics"
              active={pathname?.startsWith("/analytics")}
              collapsed={sidebarCollapsed}
            />
          </Link>
        </SidebarSection>

        <SidebarSection title="Settings">
          <Link href="/settings">
            <SidebarItem
              icon={<Settings size={20} />}
              title="Settings"
              active={pathname?.startsWith("/settings")}
              collapsed={sidebarCollapsed}
            />
          </Link>
        </SidebarSection>
      </Sidebar>

      {/* Main Content */}
      <div className="flex flex-col flex-1 w-full overflow-hidden">
        <Navbar
          shadow="md"
          className="border-b"
          logo={
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMobileNavOpen(true)}
            >
              <Menu size={20} />
            </Button>
          }
          actions={
            <div className="flex items-center gap-2">
              <div data-tour="notifications">
                <SmartNotifications />
              </div>
              <ContextHelp />
              <OrganizationSwitcher />
              <UserNav user={user} onLogout={logout} />
            </div>
          }
        >
          {/* Navbar content */}
          <div className="flex items-center space-x-4 w-full">
            <span className="hidden md:inline-block">Dashboard</span>
            <div className="flex-1 max-w-xl mx-auto">
              <GlobalSearch />
            </div>
          </div>
        </Navbar>

        {/* Mobile Navigation */}
        {user && (
          <EnhancedMobileNav
            user={{
              id: user.id || 'unknown',
              name: user.firstName && user.lastName
                ? `${user.firstName} ${user.lastName}`
                : user.displayName || 'User',
              email: user.email || '<EMAIL>',
              avatarUrl: user.avatarUrl
            }}
            onLogout={logout}
            notifications={{ count: 3, hasUnread: true }}
            className="md:hidden"
            open={isMobileNavOpen}
            onOpenChange={setIsMobileNavOpen}
          />
        )}

        {/* Page Content */}
        <main className="flex-1 overflow-y-auto">
          {/* Enhanced Breadcrumbs */}
          <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="px-4 md:px-6 py-3">
              <EnhancedBreadcrumbs />
            </div>
          </div>

          {/* Main Content */}
          <div className="p-4 md:p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
    </TenantProvider>
  );
}

function LoadingLayout() {
  return (
    <div className="flex h-screen bg-background">
      <div className="w-64 h-full border-r hidden md:block">
        <div className="p-4 border-b">
          <Skeleton className="h-8 w-24" />
        </div>
        <div className="p-4">
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-10 w-full" />
            ))}
          </div>
        </div>
      </div>
      <div className="flex flex-col flex-1">
        <div className="h-16 border-b flex items-center justify-between px-4">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-8 w-32" />
        </div>
        <div className="p-4 flex-1">
          <Skeleton className="h-8 w-48 mb-6" />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-40 w-full rounded-lg" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
