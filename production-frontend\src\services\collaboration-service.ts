/**
 * Collaboration Service - Lazy-loaded SignalR for Collaborative Features
 * This service only connects when explicitly requested by collaborative components
 * Replaces the auto-connecting realtime-service for better resource management
 */

import { HubConnection, HubConnectionBuilder, LogLevel } from '@microsoft/signalr'
import { useAuthStore } from '@/stores/auth-store'

export interface CollaborationEvent {
  type: 'cursor' | 'selection' | 'edit' | 'comment' | 'presence'
  userId: string
  userName: string
  documentId: string
  data: any
  timestamp: number
}

export interface PresenceInfo {
  userId: string
  userName: string
  cursor?: { x: number; y: number; page?: number }
  lastSeen: number
}

export interface DocumentLock {
  userId: string
  userName: string
  documentId: string
  lockedAt: number
}

export interface CollaborationSession {
  id: string
  title: string
  description?: string
  documentId?: string
  participants: string[]
  createdBy: string
  createdAt: string
  status: 'active' | 'ended'
}

class CollaborationService {
  private connection: HubConnection | null = null
  private isConnecting = false
  private eventHandlers = new Map<string, Set<Function>>()
  private activeDocuments = new Set<string>()
  private activeSessions = new Set<string>()

  /**
   * Connect to SignalR hub - only called when needed
   */
  async connect(): Promise<void> {
    if (this.connection || this.isConnecting) return

    this.isConnecting = true

    try {
      const token = useAuthStore.getState().token
      if (!token) {
        throw new Error('No authentication token available')
      }

      const hubUrl = `${process.env.NEXT_PUBLIC_API_URL || 'https://hepzlogic.azurewebsites.net'}/api/signalr`
      
      this.connection = new HubConnectionBuilder()
        .withUrl(hubUrl, {
          accessTokenFactory: () => token.accessToken || '',
          withCredentials: false,
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: (retryContext) => {
            if (retryContext.previousRetryCount < 3) {
              return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000)
            }
            return null
          }
        })
        .configureLogging(
          process.env.NODE_ENV === 'development' ? LogLevel.Information : LogLevel.Warning
        )
        .build()

      // Set up connection event handlers
      this.connection.onclose((error) => {
        console.warn('Collaboration SignalR connection closed:', error)
        this.emit('disconnected', error)
      })

      this.connection.onreconnecting((error) => {
        console.warn('Collaboration SignalR reconnecting:', error)
        this.emit('reconnecting', error)
      })

      this.connection.onreconnected((connectionId) => {
        console.info('Collaboration SignalR reconnected:', connectionId)
        this.emit('reconnected', connectionId)
        
        // Rejoin all active groups
        this.rejoinActiveGroups()
      })

      await this.connection.start()
      console.info('Collaboration SignalR connected successfully')
      this.emit('connected')

    } catch (error) {
      console.error('Failed to connect to collaboration SignalR:', error)
      this.connection = null
      throw error
    } finally {
      this.isConnecting = false
    }
  }

  /**
   * Disconnect from SignalR hub
   */
  async disconnect(): Promise<void> {
    if (this.connection) {
      try {
        await this.connection.stop()
        console.info('Collaboration SignalR disconnected')
      } catch (error) {
        console.warn('Error disconnecting collaboration SignalR:', error)
      }
    }

    this.connection = null
    this.activeDocuments.clear()
    this.activeSessions.clear()
    this.emit('disconnected')
  }

  /**
   * Check if connected to SignalR
   */
  get isConnected(): boolean {
    return this.connection?.state === 'Connected'
  }

  /**
   * Get connection ID
   */
  get connectionId(): string | null {
    return this.connection?.connectionId || null
  }

  /**
   * Join document collaboration
   */
  async joinDocument(documentId: string): Promise<void> {
    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.connection) {
      throw new Error('SignalR connection not available')
    }

    try {
      await this.connection.invoke('JoinDocumentGroup', documentId)
      this.activeDocuments.add(documentId)
      console.info(`Joined document collaboration: ${documentId}`)
    } catch (error) {
      console.error(`Failed to join document ${documentId}:`, error)
      throw error
    }
  }

  /**
   * Leave document collaboration
   */
  async leaveDocument(documentId: string): Promise<void> {
    if (!this.connection || !this.isConnected) return

    try {
      await this.connection.invoke('LeaveDocumentGroup', documentId)
      this.activeDocuments.delete(documentId)
      console.info(`Left document collaboration: ${documentId}`)
    } catch (error) {
      console.error(`Failed to leave document ${documentId}:`, error)
    }
  }

  /**
   * Join collaboration session
   */
  async joinSession(sessionId: string): Promise<void> {
    if (!this.isConnected) {
      await this.connect()
    }

    if (!this.connection) {
      throw new Error('SignalR connection not available')
    }

    try {
      await this.connection.invoke('JoinCollaborationSession', sessionId)
      this.activeSessions.add(sessionId)
      console.info(`Joined collaboration session: ${sessionId}`)
    } catch (error) {
      console.error(`Failed to join session ${sessionId}:`, error)
      throw error
    }
  }

  /**
   * Leave collaboration session
   */
  async leaveSession(sessionId: string): Promise<void> {
    if (!this.connection || !this.isConnected) return

    try {
      await this.connection.invoke('LeaveCollaborationSession', sessionId)
      this.activeSessions.delete(sessionId)
      console.info(`Left collaboration session: ${sessionId}`)
    } catch (error) {
      console.error(`Failed to leave session ${sessionId}:`, error)
    }
  }

  /**
   * Send message through SignalR
   */
  async sendMessage(method: string, ...args: any[]): Promise<void> {
    if (!this.connection || !this.isConnected) {
      throw new Error('SignalR not connected')
    }

    try {
      await this.connection.invoke(method, ...args)
    } catch (error) {
      console.error(`Failed to send SignalR message ${method}:`, error)
      throw error
    }
  }

  /**
   * Add event listener
   */
  on(eventName: string, handler: Function): void {
    if (!this.eventHandlers.has(eventName)) {
      this.eventHandlers.set(eventName, new Set())
    }
    this.eventHandlers.get(eventName)!.add(handler)

    // Also add to SignalR connection if available
    if (this.connection) {
      this.connection.on(eventName, handler as any)
    }
  }

  /**
   * Remove event listener
   */
  off(eventName: string, handler: Function): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventName)
      }
    }

    // Also remove from SignalR connection if available
    if (this.connection) {
      this.connection.off(eventName, handler as any)
    }
  }

  /**
   * Emit event to all listeners
   */
  private emit(eventName: string, ...args: any[]): void {
    const handlers = this.eventHandlers.get(eventName)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error(`Error in event handler for ${eventName}:`, error)
        }
      })
    }
  }

  /**
   * Rejoin all active groups after reconnection
   */
  private async rejoinActiveGroups(): Promise<void> {
    if (!this.connection || !this.isConnected) return

    try {
      // Rejoin document groups
      for (const documentId of this.activeDocuments) {
        await this.connection.invoke('JoinDocumentGroup', documentId)
      }

      // Rejoin session groups
      for (const sessionId of this.activeSessions) {
        await this.connection.invoke('JoinCollaborationSession', sessionId)
      }

      console.info('Rejoined all active collaboration groups')
    } catch (error) {
      console.error('Failed to rejoin active groups:', error)
    }
  }

  /**
   * Get active document count
   */
  get activeDocumentCount(): number {
    return this.activeDocuments.size
  }

  /**
   * Get active session count
   */
  get activeSessionCount(): number {
    return this.activeSessions.size
  }

  /**
   * Check if should disconnect (no active collaborations)
   */
  get shouldDisconnect(): boolean {
    return this.activeDocuments.size === 0 && this.activeSessions.size === 0
  }
}

// Export singleton instance
export const collaborationService = new CollaborationService()

// Auto-disconnect when no active collaborations
setInterval(() => {
  if (collaborationService.isConnected && collaborationService.shouldDisconnect) {
    console.info('Auto-disconnecting collaboration service (no active collaborations)')
    collaborationService.disconnect().catch(console.error)
  }
}, 30000) // Check every 30 seconds
