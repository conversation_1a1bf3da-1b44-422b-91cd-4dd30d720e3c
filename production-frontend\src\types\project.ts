/**
 * Project Management Types
 */

import type { ID, Timestamp, User, Document, Organization } from './index'

// Import backend Project as base
import type { Project as BackendProject } from './backend'

// Core project interface - use backend Project directly
export interface Project extends BackendProject {
  // Add frontend-specific computed properties
  priority?: ProjectPriority
  organization?: Organization
  owner?: User
  members?: ProjectMember[]
  documents?: Document[]
  tags?: string[]
  startDate?: Timestamp
  endDate?: Timestamp
  deadline?: Timestamp
  progress?: number
  budget?: ProjectBudget
  milestones?: ProjectMilestone[]
  tasks?: ProjectTask[]
  archivedAt?: Timestamp

  // Computed properties for UI
  documentIds?: string[]
  documentCount?: number
  memberCount?: number
  workflowIds?: string[]
}



// Import backend types
import type {
  ProjectStatus as BackendProjectStatus,
  ProjectVisibility as BackendProjectVisibility,
  ProjectSettings as BackendProjectSettings
} from './backend'

// Create runtime constants for enums
export const ProjectStatus = {
  ACTIVE: 'active' as const,
  INACTIVE: 'inactive' as const,
  ARCHIVED: 'archived' as const,
  DRAFT: 'draft' as const,
  COMPLETED: 'completed' as const
} as const

export const ProjectVisibility = {
  PUBLIC: 'public' as const,
  PRIVATE: 'private' as const,
  INTERNAL: 'internal' as const,
  ORGANIZATION: 'organization' as const
} as const

// Export type aliases for compatibility
export type ProjectStatus = BackendProjectStatus
export type ProjectVisibility = BackendProjectVisibility
export type ProjectSettings = BackendProjectSettings

// Project priority
export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent'

// Project member
export interface ProjectMember {
  id: ID
  userId: ID
  user: User
  name?: string
  avatar?: string
  role: ProjectRole
  permissions: ProjectPermission[]
  joinedAt: Timestamp
  invitedBy: ID
  isActive: boolean
  lastActivity?: Timestamp
}

// Project roles
export type ProjectRole = 'owner' | 'admin' | 'manager' | 'member' | 'viewer'

// Project permissions
export type ProjectPermission = 
  | 'project:read'
  | 'project:write'
  | 'project:delete'
  | 'project:manage_members'
  | 'project:manage_settings'
  | 'documents:read'
  | 'documents:write'
  | 'documents:delete'
  | 'documents:share'
  | 'tasks:read'
  | 'tasks:write'
  | 'tasks:delete'
  | 'tasks:assign'

// Extended project settings for frontend
export interface ExtendedProjectSettings {
  allowMemberInvites?: boolean
  documentRetentionDays?: number
  maxFileSize?: number
  allowedFileTypes?: string[]
  enableNotifications?: boolean
  enableComments?: boolean
  enableVersionControl?: boolean
  enableWorkflows?: boolean
  customFields?: ProjectCustomField[]
}

export interface ProjectCustomField {
  id: string
  name: string
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect'
  required: boolean
  options?: string[]
  defaultValue?: any
}

// Project metadata
export interface ProjectMetadata {
  category?: string
  client?: string
  department?: string
  location?: string
  externalId?: string
  customData: Record<string, any>
}

// Project budget
export interface ProjectBudget {
  total: number
  spent: number
  currency: string
  breakdown: ProjectBudgetItem[]
  lastUpdated: Timestamp
}

export interface ProjectBudgetItem {
  id: string
  category: string
  description: string
  budgeted: number
  actual: number
  variance: number
}

// Project milestone
export interface ProjectMilestone {
  id: ID
  name: string
  description?: string
  dueDate: Timestamp
  status: 'pending' | 'in_progress' | 'completed' | 'overdue'
  progress: number
  dependencies: ID[]
  assignedTo: ID[]
  completedAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Project task
export interface ProjectTask {
  id: ID
  title: string
  description?: string
  status: TaskStatus
  priority: ProjectPriority
  assignedTo: ID[]
  assignedBy: ID
  projectId: ID
  milestoneId?: ID
  parentTaskId?: ID
  subtasks: ProjectTask[]
  dependencies: ID[]
  estimatedHours?: number
  actualHours?: number
  progress: number
  tags: string[]
  dueDate?: Timestamp
  startDate?: Timestamp
  completedAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Task status
export type TaskStatus = 
  | 'todo'
  | 'in_progress'
  | 'review'
  | 'completed'
  | 'cancelled'
  | 'blocked'

// Project analytics
export interface ProjectAnalytics {
  overview: ProjectOverview
  progress: ProjectProgress
  team: ProjectTeamAnalytics
  documents: ProjectDocumentAnalytics
  timeline: ProjectTimelineAnalytics
  budget: ProjectBudgetAnalytics
}

export interface ProjectOverview {
  totalTasks: number
  completedTasks: number
  overdueTasks: number
  totalDocuments: number
  totalMembers: number
  daysRemaining?: number
  completionPercentage: number
}

export interface ProjectProgress {
  milestones: {
    completed: number
    total: number
    upcoming: ProjectMilestone[]
    overdue: ProjectMilestone[]
  }
  tasks: {
    byStatus: Record<TaskStatus, number>
    byPriority: Record<ProjectPriority, number>
    byAssignee: { userId: ID; userName: string; count: number }[]
  }
  timeline: {
    date: string
    completed: number
    added: number
  }[]
}

export interface ProjectTeamAnalytics {
  members: {
    userId: ID
    userName: string
    role: ProjectRole
    tasksAssigned: number
    tasksCompleted: number
    documentsUploaded: number
    lastActivity: Timestamp
    productivity: number
  }[]
  activity: {
    date: string
    logins: number
    documentsUploaded: number
    tasksCompleted: number
  }[]
}

export interface ProjectDocumentAnalytics {
  total: number
  byType: Record<string, number>
  byStatus: Record<string, number>
  uploadTrend: {
    date: string
    count: number
  }[]
  topUploaders: {
    userId: ID
    userName: string
    count: number
  }[]
}

export interface ProjectTimelineAnalytics {
  events: ProjectTimelineEvent[]
  velocity: {
    week: string
    tasksCompleted: number
    documentsProcessed: number
  }[]
}

export interface ProjectTimelineEvent {
  id: ID
  type: 'task_created' | 'task_completed' | 'document_uploaded' | 'milestone_reached' | 'member_added'
  title: string
  description: string
  userId: ID
  userName: string
  timestamp: Timestamp
  metadata?: Record<string, any>
}

export interface ProjectBudgetAnalytics {
  totalBudget: number
  totalSpent: number
  variance: number
  burnRate: number
  projectedCompletion: number
  spending: {
    date: string
    amount: number
  }[]
  categories: {
    category: string
    budgeted: number
    spent: number
    variance: number
  }[]
}

// Project operations
export interface CreateProjectRequest {
  name: string
  description?: string
  organizationId: ID
  visibility?: ProjectVisibility
  priority?: ProjectPriority
  startDate?: Timestamp
  endDate?: Timestamp
  budget?: Omit<ProjectBudget, 'spent' | 'lastUpdated'>
  tags?: string[]
  settings?: Partial<ProjectSettings>
  members?: {
    userId: ID
    role: ProjectRole
  }[]
}

export interface UpdateProjectRequest {
  name?: string
  description?: string
  status?: ProjectStatus
  priority?: ProjectPriority
  visibility?: ProjectVisibility
  startDate?: Timestamp
  endDate?: Timestamp
  deadline?: Timestamp
  tags?: string[]
  settings?: Partial<ProjectSettings>
  metadata?: Partial<ProjectMetadata>
}

export interface ProjectSearchQuery {
  query?: string
  status?: ProjectStatus[]
  priority?: ProjectPriority[]
  visibility?: ProjectVisibility[]
  organizationId?: ID
  ownerId?: ID
  memberId?: ID
  tags?: string[]
  dateRange?: {
    start: Timestamp
    end: Timestamp
  }
  hasDeadline?: boolean
  isOverdue?: boolean
}

export interface ProjectSearchResult {
  projects: Project[]
  total: number
  facets: {
    statuses: { status: ProjectStatus; count: number }[]
    priorities: { priority: ProjectPriority; count: number }[]
    organizations: { organizationId: ID; organizationName: string; count: number }[]
    tags: { tag: string; count: number }[]
  }
}

// Project templates
export interface ProjectTemplate {
  id: ID
  name: string
  description?: string
  category: string
  isPublic: boolean
  organizationId: ID
  createdBy: ID
  structure: ProjectTemplateStructure
  settings: ProjectSettings
  usageCount: number
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface ProjectTemplateStructure {
  milestones: Omit<ProjectMilestone, 'id' | 'createdAt' | 'updatedAt' | 'completedAt'>[]
  tasks: Omit<ProjectTask, 'id' | 'projectId' | 'createdAt' | 'updatedAt' | 'completedAt'>[]
  roles: {
    role: ProjectRole
    permissions: ProjectPermission[]
    isRequired: boolean
  }[]
  customFields: ProjectCustomField[]
}

// Project collaboration
export interface ProjectCollaboration {
  id: ID
  projectId: ID
  type: 'meeting' | 'review' | 'planning' | 'retrospective'
  title: string
  description?: string
  participants: ID[]
  scheduledAt: Timestamp
  duration: number
  location?: string
  meetingUrl?: string
  agenda: string[]
  notes?: string
  recordings?: string[]
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Project notifications
export interface ProjectNotification {
  id: ID
  projectId: ID
  type: 'task_assigned' | 'deadline_approaching' | 'milestone_completed' | 'member_added' | 'document_shared'
  title: string
  message: string
  recipients: ID[]
  data?: Record<string, any>
  readBy: {
    userId: ID
    readAt: Timestamp
  }[]
  createdAt: Timestamp
  expiresAt?: Timestamp
}

// Project reports
export interface ProjectReport {
  id: ID
  projectId: ID
  type: 'status' | 'progress' | 'budget' | 'team' | 'custom'
  title: string
  description?: string
  data: any
  generatedBy: ID
  generatedAt: Timestamp
  format: 'pdf' | 'excel' | 'json'
  url?: string
  isScheduled: boolean
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    dayOfWeek?: number
    dayOfMonth?: number
    time: string
  }
}

// Project integrations
export interface ProjectIntegration {
  id: ID
  projectId: ID
  type: string
  name: string
  config: Record<string, any>
  isActive: boolean
  lastSync?: Timestamp
  syncStatus: 'success' | 'error' | 'pending'
  syncError?: string
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}
