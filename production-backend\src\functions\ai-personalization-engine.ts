/**
 * AI Personalization Engine
 * Provides personalized AI experiences, content recommendations, and user behavior analysis
 * Integrates with existing AI services and user activity tracking
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { aiServices } from '../shared/services/ai-services';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Personalization interfaces
interface UserProfile {
  userId: string;
  preferences: {
    aiInteractionStyle: 'concise' | 'detailed' | 'conversational';
    contentTypes: string[];
    workflowPreferences: string[];
    collaborationStyle: 'active' | 'passive' | 'observer';
    learningGoals: string[];
  };
  behaviorPatterns: {
    documentTypes: Record<string, number>;
    activityTimes: Record<string, number>;
    collaborationFrequency: number;
    aiUsagePatterns: Record<string, number>;
  };
  interests: string[];
  expertise: string[];
  lastUpdated: string;
}

interface PersonalizationRecommendation {
  id: string;
  userId: string;
  type: 'content' | 'workflow' | 'collaboration' | 'ai_prompt' | 'document';
  title: string;
  description: string;
  confidence: number;
  reasoning: string;
  actionData: Record<string, any>;
  priority: number;
  expiresAt: string;
  createdAt: string;
}

interface UserInteractionPattern {
  userId: string;
  sessionId: string;
  interactions: Array<{
    type: string;
    timestamp: string;
    context: Record<string, any>;
    outcome: 'positive' | 'negative' | 'neutral';
  }>;
  patterns: {
    preferredFeatures: string[];
    timeOfDayPreferences: Record<string, number>;
    documentPreferences: Record<string, number>;
    collaborationPatterns: Record<string, any>;
  };
}

class AIPersonalizationEngine {
  private readonly CACHE_TTL = 3600; // 1 hour
  private readonly RECOMMENDATION_LIMIT = 10;

  /**
   * Get personalized AI recommendations for user
   */
  async getPersonalizedRecommendations(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const userId = authResult.user.id;
      const url = new URL(request.url);
      const limit = parseInt(url.searchParams.get('limit') || '10');
      const type = url.searchParams.get('type');

      // Check cache first
      const cacheKey = `personalization:recommendations:${userId}:${type || 'all'}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return addCorsHeaders({
          status: 200,
          jsonBody: JSON.parse(cached)
        }, request);
      }

      // Get user profile and behavior patterns
      const [userProfile, interactionPatterns] = await Promise.all([
        this.getUserProfile(userId),
        this.getUserInteractionPatterns(userId)
      ]);

      // Generate personalized recommendations
      const recommendations = await this.generateRecommendations(
        userId,
        userProfile,
        interactionPatterns,
        type || 'general',
        limit
      );

      // Cache results
      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(recommendations));

      return addCorsHeaders({
        status: 200,
        jsonBody: recommendations
      }, request);

    } catch (error) {
      logger.error('Error getting personalized recommendations', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Update user interaction patterns
   */
  async updateUserInteraction(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        interactionType: Joi.string().required(),
        context: Joi.object().required(),
        outcome: Joi.string().valid('positive', 'negative', 'neutral').required(),
        sessionId: Joi.string().optional()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const userId = authResult.user.id;
      const interactionId = await this.recordUserInteraction(userId, value);

      // Update user profile asynchronously
      this.updateUserProfileAsync(userId, value);

      return addCorsHeaders({
        status: 200,
        jsonBody: { interactionId, message: 'Interaction recorded successfully' }
      }, request);

    } catch (error) {
      logger.error('Error updating user interaction', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Get personalized AI prompt suggestions
   */
  async getPersonalizedPrompts(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const userId = authResult.user.id;
      const url = new URL(request.url);
      const context_type = url.searchParams.get('context');
      const documentId = url.searchParams.get('documentId');

      // Get user profile for personalization
      const userProfile = await this.getUserProfile(userId);
      
      // Generate personalized prompts based on user's style and context
      const prompts = await this.generatePersonalizedPrompts(
        userProfile,
        context_type || undefined,
        documentId || undefined
      );

      return addCorsHeaders({
        status: 200,
        jsonBody: { prompts }
      }, request);

    } catch (error) {
      logger.error('Error getting personalized prompts', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Analyze user behavior patterns
   */
  async analyzeUserBehavior(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const userId = authResult.user.id;
      const url = new URL(request.url);
      const timeRange = url.searchParams.get('timeRange') || '30d';

      // Analyze user behavior patterns using AI
      const analysis = await this.performBehaviorAnalysis(userId, timeRange);

      return addCorsHeaders({
        status: 200,
        jsonBody: analysis
      }, request);

    } catch (error) {
      logger.error('Error analyzing user behavior', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Private helper methods
  private async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const cacheKey = `user:profile:${userId}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get from database or create default profile
      let profile = await db.readItem('user-profiles', userId, userId);
      if (!profile) {
        profile = await this.createDefaultUserProfile(userId);
      }

      await redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(profile));
      return profile as UserProfile;
    } catch (error) {
      logger.error('Error getting user profile', { userId, error });
      return this.createDefaultUserProfile(userId);
    }
  }

  private async createDefaultUserProfile(userId: string): Promise<UserProfile> {
    const defaultProfile: UserProfile = {
      userId,
      preferences: {
        aiInteractionStyle: 'conversational',
        contentTypes: [],
        workflowPreferences: [],
        collaborationStyle: 'active',
        learningGoals: []
      },
      behaviorPatterns: {
        documentTypes: {},
        activityTimes: {},
        collaborationFrequency: 0,
        aiUsagePatterns: {}
      },
      interests: [],
      expertise: [],
      lastUpdated: new Date().toISOString()
    };

    await db.createItem('user-profiles', defaultProfile);
    return defaultProfile;
  }

  private async getUserInteractionPatterns(userId: string): Promise<UserInteractionPattern[]> {
    try {
      const patterns = await db.queryItems('user-interactions',
        'SELECT * FROM c WHERE c.userId = @userId ORDER BY c.timestamp DESC OFFSET 0 LIMIT 100',
        [userId]
      );
      return patterns as UserInteractionPattern[];
    } catch (error) {
      logger.error('Error getting user interaction patterns', { userId, error });
      return [];
    }
  }

  private async generateRecommendations(
    userId: string,
    userProfile: UserProfile,
    _patterns: UserInteractionPattern[],
    type?: string,
    limit: number = 10
  ): Promise<PersonalizationRecommendation[]> {
    // Use AI to generate personalized recommendations
    const prompt = `Based on the user profile and interaction patterns, generate ${limit} personalized recommendations.

User Profile:
- AI Interaction Style: ${userProfile.preferences.aiInteractionStyle}
- Content Types: ${userProfile.preferences.contentTypes.join(', ')}
- Collaboration Style: ${userProfile.preferences.collaborationStyle}
- Interests: ${userProfile.interests.join(', ')}
- Expertise: ${userProfile.expertise.join(', ')}

Recent Behavior Patterns:
- Document Types: ${JSON.stringify(userProfile.behaviorPatterns.documentTypes)}
- AI Usage: ${JSON.stringify(userProfile.behaviorPatterns.aiUsagePatterns)}

Generate recommendations for: ${type || 'all types (content, workflow, collaboration, ai_prompt, document)'}

Return as JSON array with: type, title, description, confidence, reasoning, priority`;

    try {
      const aiResponse = await aiServices.reason(prompt, [], {
        temperature: 0.7,
        maxTokens: 2000
      });

      // Parse AI response and create recommendation objects
      const recommendations = this.parseAIRecommendations(aiResponse.content, userId);
      
      // Store recommendations in database
      for (const rec of recommendations) {
        await db.createItem('personalization-recommendations', rec);
      }

      return recommendations;
    } catch (error) {
      logger.error('Error generating AI recommendations', { error });
      return this.getFallbackRecommendations(userId, userProfile, limit);
    }
  }

  private parseAIRecommendations(aiContent: string, userId: string): PersonalizationRecommendation[] {
    try {
      // Extract JSON from AI response
      const jsonMatch = aiContent.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const recommendations = JSON.parse(jsonMatch[0]);
      return recommendations.map((rec: any) => ({
        id: uuidv4(),
        userId,
        type: rec.type || 'content',
        title: rec.title || 'Personalized Recommendation',
        description: rec.description || '',
        confidence: rec.confidence || 0.5,
        reasoning: rec.reasoning || '',
        actionData: rec.actionData || {},
        priority: rec.priority || 1,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        createdAt: new Date().toISOString()
      }));
    } catch (error) {
      logger.error('Error parsing AI recommendations', { error });
      return [];
    }
  }

  private getFallbackRecommendations(userId: string, _userProfile: UserProfile, limit: number): PersonalizationRecommendation[] {
    // Fallback recommendations based on user profile
    const fallbacks: PersonalizationRecommendation[] = [
      {
        id: uuidv4(),
        userId,
        type: 'ai_prompt',
        title: 'Try Document Analysis',
        description: 'Analyze your recent documents for insights and patterns',
        confidence: 0.8,
        reasoning: 'Based on your document activity',
        actionData: { feature: 'document_analysis' },
        priority: 1,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString()
      }
    ];

    return fallbacks.slice(0, limit);
  }

  private async recordUserInteraction(userId: string, interaction: any): Promise<string> {
    const interactionRecord = {
      id: uuidv4(),
      userId,
      sessionId: interaction.sessionId || uuidv4(),
      type: interaction.interactionType,
      context: interaction.context,
      outcome: interaction.outcome,
      timestamp: new Date().toISOString(),
      tenantId: userId
    };

    await db.createItem('user-interactions', interactionRecord);
    return interactionRecord.id;
  }

  private async updateUserProfileAsync(userId: string, interaction: any): Promise<void> {
    // Update user profile based on interaction (async)
    try {
      const profile = await this.getUserProfile(userId);
      
      // Update behavior patterns based on interaction
      if (interaction.context.documentType) {
        profile.behaviorPatterns.documentTypes[interaction.context.documentType] = 
          (profile.behaviorPatterns.documentTypes[interaction.context.documentType] || 0) + 1;
      }

      if (interaction.context.feature) {
        profile.behaviorPatterns.aiUsagePatterns[interaction.context.feature] = 
          (profile.behaviorPatterns.aiUsagePatterns[interaction.context.feature] || 0) + 1;
      }

      profile.lastUpdated = new Date().toISOString();

      await db.upsertItem('user-profiles', profile);
      
      // Clear cache
      await redis.del(`user:profile:${userId}`);
      
    } catch (error) {
      logger.error('Error updating user profile', { userId, error });
    }
  }

  private async generatePersonalizedPrompts(
    userProfile: UserProfile,
    _contextType?: string,
    _documentId?: string
  ): Promise<string[]> {
    const style = userProfile.preferences.aiInteractionStyle;
    const interests = userProfile.interests.join(', ');
    
    const basePrompts = [
      `Analyze this document focusing on ${interests || 'key insights'}`,
      `Summarize the main points in a ${style} manner`,
      `Extract actionable items from this content`,
      `Identify potential improvements or optimizations`,
      `Compare this with industry best practices`
    ];

    // Customize based on user's interaction style
    if (style === 'concise') {
      return basePrompts.map(prompt => `${prompt} - provide a brief, focused response`);
    } else if (style === 'detailed') {
      return basePrompts.map(prompt => `${prompt} - provide comprehensive analysis with examples`);
    }

    return basePrompts;
  }

  private async performBehaviorAnalysis(userId: string, timeRange: string): Promise<any> {
    // Get user activities and interactions for analysis
    const activities = await db.queryItems('user-activities',
      `SELECT * FROM c WHERE c.userId = @userId AND c.timestamp >= @startDate ORDER BY c.timestamp DESC`,
      [userId, this.getStartDateForRange(timeRange)]
    );

    const interactions = await db.queryItems('user-interactions',
      `SELECT * FROM c WHERE c.userId = @userId AND c.timestamp >= @startDate ORDER BY c.timestamp DESC`,
      [userId, this.getStartDateForRange(timeRange)]
    );

    // Analyze patterns using AI
    const analysisPrompt = `Analyze user behavior patterns and provide insights:

Activities: ${JSON.stringify(activities.slice(0, 50))}
Interactions: ${JSON.stringify(interactions.slice(0, 50))}

Provide analysis on:
1. Usage patterns and trends
2. Preferred features and workflows
3. Collaboration behavior
4. Learning opportunities
5. Optimization suggestions

Return as structured JSON.`;

    try {
      const aiAnalysis = await aiServices.reason(analysisPrompt, [], {
        temperature: 0.3,
        maxTokens: 1500
      });

      return {
        summary: aiAnalysis.content,
        patterns: this.extractPatternsFromActivities(activities),
        recommendations: this.generateBehaviorRecommendations(activities, interactions),
        timeRange,
        analyzedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Error in AI behavior analysis', { error });
      return {
        summary: 'Analysis temporarily unavailable',
        patterns: this.extractPatternsFromActivities(activities),
        recommendations: [],
        timeRange,
        analyzedAt: new Date().toISOString()
      };
    }
  }

  private getStartDateForRange(timeRange: string): string {
    const now = new Date();
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    return new Date(now.getTime() - days * 24 * 60 * 60 * 1000).toISOString();
  }

  private extractPatternsFromActivities(activities: any[]): any {
    const patterns: {
      mostActiveHours: Record<number, number>;
      topActivities: Record<string, number>;
      documentTypes: Record<string, number>;
      collaborationFrequency: number;
    } = {
      mostActiveHours: {},
      topActivities: {},
      documentTypes: {},
      collaborationFrequency: 0
    };

    activities.forEach(activity => {
      const hour = new Date(activity.timestamp).getHours();
      patterns.mostActiveHours[hour] = (patterns.mostActiveHours[hour] || 0) + 1;
      patterns.topActivities[activity.type] = (patterns.topActivities[activity.type] || 0) + 1;
      
      if (activity.metadata?.documentType) {
        patterns.documentTypes[activity.metadata.documentType] = 
          (patterns.documentTypes[activity.metadata.documentType] || 0) + 1;
      }
      
      if (activity.category === 'collaboration') {
        patterns.collaborationFrequency++;
      }
    });

    return patterns;
  }

  private generateBehaviorRecommendations(activities: any[], _interactions: any[]): any[] {
    // Generate simple behavior-based recommendations
    const recommendations = [];
    
    if (activities.length > 0) {
      const lastActivity = activities[0];
      recommendations.push({
        type: 'workflow',
        title: 'Continue your workflow',
        description: `Resume working on ${lastActivity.category} activities`,
        confidence: 0.7
      });
    }

    return recommendations;
  }
}

// Create instance
const personalizationEngine = new AIPersonalizationEngine();

// Register HTTP functions
app.http('personalization-recommendations', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/personalization/recommendations',
  handler: (request, context) => personalizationEngine.getPersonalizedRecommendations(request, context)
});

app.http('personalization-interaction', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/personalization/interaction',
  handler: (request, context) => personalizationEngine.updateUserInteraction(request, context)
});

app.http('personalization-prompts', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/personalization/prompts',
  handler: (request, context) => personalizationEngine.getPersonalizedPrompts(request, context)
});

app.http('personalization-behavior', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/personalization/behavior',
  handler: (request, context) => personalizationEngine.analyzeUserBehavior(request, context)
});
