/**
 * Azure AI Foundry Test Function
 * Tests DeepSeek R1, Llama, and Azure AI Search vector database integration
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders } from '../shared/middleware/cors';
import { aiServices } from '../shared/services/ai-services';
import { ragService } from '../shared/services/rag-service';
import { cohereEmbeddingService } from '../shared/services/cohere-embedding-service';
import { config } from '../env';

interface TestResult {
  service: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
  duration?: number;
}

/**
 * Test Azure AI Foundry integration
 */
async function testAzureAIFoundryHandler(
  request: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  const startTime = Date.now();
  const results: TestResult[] = [];

  try {
    logger.info('Starting Azure AI Foundry integration tests');

    // Test 1: DeepSeek R1 Service
    results.push(await testDeepSeekR1());

    // Test 2: Llama Service
    results.push(await testLlama());

    // Test 3: Cohere Embedding Generation
    results.push(await testCohereEmbeddings());

    // Test 4: Legacy Embedding Generation (via AI Services)
    results.push(await testEmbeddings());

    // Test 5: Vector Search
    results.push(await testVectorSearch());

    // Test 6: RAG Service
    results.push(await testRAGService());

    // Test 7: End-to-End Workflow
    results.push(await testEndToEndWorkflow());

    // Calculate summary
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    const warningCount = results.filter(r => r.status === 'warning').length;

    const summary = {
      totalTests: results.length,
      successful: successCount,
      errors: errorCount,
      warnings: warningCount,
      overallStatus: errorCount === 0 ? 'success' : 'partial',
      totalDuration: Date.now() - startTime,
      configuration: {
        deepSeekR1Enabled: config.ai.deepSeekR1.enabled,
        llamaEnabled: config.ai.llama.enabled,
        cohereEnabled: config.ai.cohere.enabled,
        vectorSearchEnabled: config.ai.search.vectorEnabled,
        semanticSearchEnabled: config.ai.search.semanticEnabled
      }
    };

    logger.info('Azure AI Foundry tests completed', summary);

    const response: HttpResponseInit = {
      status: errorCount === 0 ? 200 : 207,
      jsonBody: {
        summary,
        results,
        timestamp: new Date().toISOString()
      }
    };

    return addCorsHeaders(response, request);

  } catch (error) {
    logger.error('Azure AI Foundry tests failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    const response: HttpResponseInit = {
      status: 500,
      jsonBody: {
        error: 'Azure AI Foundry tests failed',
        message: error instanceof Error ? error.message : String(error),
        results
      }
    };

    return addCorsHeaders(response, request);
  }
}

/**
 * Test DeepSeek R1 service
 */
async function testDeepSeekR1(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    if (!config.ai.deepSeekR1.enabled) {
      return {
        service: 'DeepSeek R1',
        status: 'warning',
        message: 'DeepSeek R1 service is disabled',
        duration: Date.now() - startTime
      };
    }

    const response = await aiServices.reason(
      'What is the capital of France? Provide reasoning.',
      [],
      {
        maxTokens: 100,
        temperature: 0.3
      }
    );

    if (!response.content || response.content.length === 0) {
      throw new Error('Empty response from DeepSeek R1');
    }

    return {
      service: 'DeepSeek R1',
      status: 'success',
      message: 'DeepSeek R1 reasoning test successful',
      details: {
        responseLength: response.content.length,
        tokensUsed: response.tokensUsed,
        confidence: response.confidence,
        hasReasoning: !!response.reasoning,
        model: response.model
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'DeepSeek R1',
      status: 'error',
      message: `DeepSeek R1 test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test Llama service
 */
async function testLlama(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    if (!config.ai.llama.enabled) {
      return {
        service: 'Llama',
        status: 'warning',
        message: 'Llama service is disabled',
        duration: Date.now() - startTime
      };
    }

    const response = await aiServices.generateContent(
      'Write a brief introduction about artificial intelligence.',
      {
        maxTokens: 150,
        temperature: 0.7
      }
    );

    if (!response.content || response.content.length === 0) {
      throw new Error('Empty response from Llama');
    }

    return {
      service: 'Llama',
      status: 'success',
      message: 'Llama content generation test successful',
      details: {
        responseLength: response.content.length,
        tokensUsed: response.tokensUsed,
        confidence: response.confidence,
        model: response.model
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'Llama',
      status: 'error',
      message: `Llama test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test Cohere embedding generation directly
 */
async function testCohereEmbeddings(): Promise<TestResult> {
  const startTime = Date.now();

  try {
    if (!config.ai.cohere.enabled) {
      return {
        service: 'Cohere Embeddings',
        status: 'warning',
        message: 'Cohere embedding service is disabled',
        duration: Date.now() - startTime
      };
    }

    const testTexts = [
      'This is a test document for embedding generation.',
      'Azure AI Foundry provides powerful machine learning capabilities.'
    ];

    // Test single embedding
    const singleResult = await cohereEmbeddingService.generateEmbedding(
      testTexts[0],
      'search_document'
    );

    // Test batch embeddings
    const batchResult = await cohereEmbeddingService.generateDocumentEmbeddings(testTexts);

    if (!singleResult.embedding || singleResult.embedding.length === 0) {
      throw new Error('Empty single embedding generated');
    }

    if (!batchResult.embeddings || batchResult.embeddings.length === 0) {
      throw new Error('Empty batch embeddings generated');
    }

    return {
      service: 'Cohere Embeddings',
      status: 'success',
      message: 'Cohere embedding generation test successful',
      details: {
        singleEmbedding: {
          dimensions: singleResult.dimensions,
          tokensUsed: singleResult.tokensUsed,
          model: singleResult.model,
          inputType: singleResult.inputType
        },
        batchEmbeddings: {
          count: batchResult.embeddings.length,
          dimensions: batchResult.embeddings[0]?.length || 0,
          tokensUsed: batchResult.meta?.billed_units?.input_tokens || 0
        },
        endpoint: config.ai.cohere.endpoint,
        deploymentName: config.ai.cohere.deploymentName
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'Cohere Embeddings',
      status: 'error',
      message: `Cohere embedding test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test embedding generation (via AI Services)
 */
async function testEmbeddings(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    const testText = 'This is a test document for embedding generation.';
    const response = await aiServices.generateEmbeddings(testText);

    if (!response.embeddings || response.embeddings.length === 0 || response.embeddings[0].length === 0) {
      throw new Error('Empty embedding generated');
    }

    return {
      service: 'Embeddings',
      status: 'success',
      message: 'Embedding generation test successful',
      details: {
        dimensions: response.dimensions,
        tokensUsed: response.tokensUsed,
        model: response.model,
        textLength: testText.length
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'Embeddings',
      status: 'error',
      message: `Embedding test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test vector search in Azure AI Search
 */
async function testVectorSearch(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    if (!config.ai.search.vectorEnabled) {
      return {
        service: 'Vector Search',
        status: 'warning',
        message: 'Vector search is disabled',
        duration: Date.now() - startTime
      };
    }

    // Generate a test embedding
    const testQuery = 'artificial intelligence machine learning';
    const embeddingResponse = await aiServices.generateEmbeddings(testQuery);
    
    // Search for similar embeddings
    const deepSeekService = aiServices.getDeepSeekR1();
    const searchResults = await deepSeekService.searchSimilarEmbeddings(
      embeddingResponse.embeddings[0],
      5
    );

    return {
      service: 'Vector Search',
      status: 'success',
      message: 'Vector search test successful',
      details: {
        queryEmbeddingDimensions: embeddingResponse.embeddings[0]?.length || 0,
        resultsFound: searchResults.length,
        searchEndpoint: config.ai.search.endpoint,
        indexName: config.ai.search.vectorIndexName
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'Vector Search',
      status: 'error',
      message: `Vector search test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test RAG service
 */
async function testRAGService(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    await ragService.initialize();

    return {
      service: 'RAG Service',
      status: 'success',
      message: 'RAG service initialization successful',
      details: {
        vectorEnabled: config.ai.search.vectorEnabled,
        semanticEnabled: config.ai.search.semanticEnabled,
        searchEndpoint: config.ai.search.endpoint,
        indexName: config.ai.search.indexName
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'RAG Service',
      status: 'error',
      message: `RAG service test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test end-to-end workflow
 */
async function testEndToEndWorkflow(): Promise<TestResult> {
  const startTime = Date.now();
  
  try {
    // Test document indexing workflow
    const testDocument = {
      documentId: `test-doc-${Date.now()}`,
      content: 'This is a test document about artificial intelligence and machine learning. It contains information about neural networks, deep learning, and natural language processing.',
      metadata: {
        title: 'Test AI Document',
        organizationId: 'test-org',
        section: 'test'
      }
    };

    // Index the test document
    await ragService.indexDocument(testDocument);

    // Wait a moment for indexing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Query the document
    const ragQuery = {
      query: 'What is artificial intelligence?',
      organizationId: 'test-org',
      maxResults: 3
    };

    const ragResult = await ragService.query(ragQuery);

    return {
      service: 'End-to-End Workflow',
      status: 'success',
      message: 'End-to-end workflow test successful',
      details: {
        documentIndexed: true,
        queryProcessed: true,
        answerGenerated: !!ragResult.answer,
        sourcesFound: ragResult.sources.length,
        confidence: ragResult.confidence,
        tokensUsed: ragResult.tokensUsed
      },
      duration: Date.now() - startTime
    };
  } catch (error) {
    return {
      service: 'End-to-End Workflow',
      status: 'error',
      message: `End-to-end workflow test failed: ${error instanceof Error ? error.message : String(error)}`,
      duration: Date.now() - startTime
    };
  }
}

// Register the function
app.http('test-azure-ai-foundry', {
  methods: ['GET', 'POST'],
  authLevel: 'function',
  handler: testAzureAIFoundryHandler
});
