/**
 * Smart Form Processing Hook
 * Hook for AI-powered form processing and field extraction
 */

import { useState, useCallback } from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/components/ui/use-toast'

export enum FormFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PHONE = 'phone',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  TEXTAREA = 'textarea',
  URL = 'url',
  CURRENCY = 'currency',
  PERCENTAGE = 'percentage'
}

export interface FormFieldDefinition {
  id: string
  name: string
  label: string
  type: FormFieldType
  required: boolean
  description?: string
  placeholder?: string
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
  options?: Array<{
    value: string
    label: string
  }>
  metadata?: Record<string, any>
}

export interface FormProcessingResult {
  id: string
  documentId: string
  fields: Array<{
    name: string
    value: any
    confidence: number
    boundingBox?: {
      x: number
      y: number
      width: number
      height: number
    }
    type: FormFieldType
  }>
  validationSummary: {
    isValid: boolean
    errors: string[]
    warnings: string[]
  }
  confidence: number
  processingTime: number
  status: 'completed' | 'failed' | 'processing'
  createdAt: string
}

export interface SmartFormProcessingOptions {
  templateId?: string
  formType?: string
  extractTables?: boolean
  extractKeyValuePairs?: boolean
  validateFields?: boolean
  autoCorrect?: boolean
  autoComplete?: boolean
  validateValues?: boolean
  confidenceThreshold?: number
  fieldDefinitions?: FormFieldDefinition[]
  organizationId?: string
  projectId?: string
}

export function useSmartFormProcessing() {
  const { toast } = useToast()
  const [processingResults, setProcessingResults] = useState<FormProcessingResult[]>([])

  // Process document with smart form extraction
  const processDocument = useMutation({
    mutationFn: async ({
      documentId,
      templateId,
      options = {}
    }: {
      documentId: string
      templateId?: string
      options?: SmartFormProcessingOptions
    }) => {
      return await backendApiClient.request<FormProcessingResult>('/ai/smart-form/process', {
        method: 'POST',
        body: JSON.stringify({
          documentId,
          templateId,
          options
        })
      })
    },
    onSuccess: (result) => {
      setProcessingResults(prev => [...prev, result])
      toast({
        title: 'Form Processing Complete',
        description: `Extracted ${result.fields.length} fields with ${Math.round(result.confidence * 100)}% confidence.`,
      })
    },
    onError: (error) => {
      console.error('Smart form processing failed:', error)
      toast({
        title: 'Processing Failed',
        description: 'Failed to process the form. Please try again.',
        variant: 'destructive',
      })
    },
  })

  // Get form templates
  const getFormTemplates = useQuery({
    queryKey: ['smart-form', 'templates'],
    queryFn: async () => {
      return await backendApiClient.request<Array<{
        id: string
        name: string
        description: string
        fields: FormFieldDefinition[]
        category: string
        isPublic: boolean
        createdAt: string
      }>>('/ai/smart-form/templates')
    },
    staleTime: 300000, // 5 minutes
  })

  // Create form template
  const createTemplate = useMutation({
    mutationFn: async (template: {
      name: string
      description: string
      fields: FormFieldDefinition[]
      category?: string
      isPublic?: boolean
    }) => {
      return await backendApiClient.request('/ai/smart-form/templates', {
        method: 'POST',
        body: JSON.stringify(template)
      })
    },
    onSuccess: () => {
      getFormTemplates.refetch()
      toast({
        title: 'Template Created',
        description: 'Form template has been created successfully.',
      })
    },
    onError: (error) => {
      console.error('Template creation failed:', error)
      toast({
        title: 'Creation Failed',
        description: 'Failed to create form template.',
        variant: 'destructive',
      })
    },
  })

  // Validate extracted data
  const validateFormData = useCallback(async (
    data: Record<string, any>,
    templateId?: string
  ): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    suggestions: Array<{
      field: string
      suggestion: string
      confidence: number
    }>
  }> => {
    try {
      return await backendApiClient.request('/ai/smart-form/validate', {
        method: 'POST',
        body: JSON.stringify({
          data,
          templateId
        })
      })
    } catch (error) {
      console.error('Form validation failed:', error)
      throw error
    }
  }, [])

  // Auto-correct form data
  const autoCorrectData = useMutation({
    mutationFn: async ({
      data,
      templateId
    }: {
      data: Record<string, any>
      templateId?: string
    }) => {
      return await backendApiClient.request('/ai/smart-form/auto-correct', {
        method: 'POST',
        body: JSON.stringify({
          data,
          templateId
        })
      })
    },
    onSuccess: () => {
      toast({
        title: 'Data Corrected',
        description: 'Form data has been automatically corrected.',
      })
    },
    onError: (error) => {
      console.error('Auto-correction failed:', error)
      toast({
        title: 'Correction Failed',
        description: 'Failed to auto-correct form data.',
        variant: 'destructive',
      })
    },
  })

  // Get processing history
  const getProcessingHistory = useQuery({
    queryKey: ['smart-form', 'history'],
    queryFn: async () => {
      return await backendApiClient.request<FormProcessingResult[]>('/ai/smart-form/history')
    },
    staleTime: 60000, // 1 minute
  })

  // Export form data
  const exportFormData = useCallback(async (
    resultId: string,
    format: 'json' | 'csv' | 'excel' = 'json'
  ): Promise<Blob> => {
    try {
      const response = await backendApiClient.request(`/ai/smart-form/export/${resultId}`, {
        method: 'GET',
        headers: {
          'Accept': format === 'json' ? 'application/json' : 
                   format === 'csv' ? 'text/csv' : 
                   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      })
      return new Blob([response], { 
        type: format === 'json' ? 'application/json' : 
              format === 'csv' ? 'text/csv' : 
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
    } catch (error) {
      console.error('Export failed:', error)
      throw error
    }
  }, [])

  return {
    // Processing
    processDocument,
    isProcessing: processDocument.isPending,
    processingResults,
    
    // Templates
    templates: getFormTemplates.data || [],
    isLoadingTemplates: getFormTemplates.isLoading,
    createTemplate,
    
    // Validation and correction
    validateFormData,
    autoCorrectData,
    
    // History and export
    history: getProcessingHistory.data || [],
    isLoadingHistory: getProcessingHistory.isLoading,
    exportFormData,
    
    // Utilities
    clearResults: () => setProcessingResults([]),
    refreshTemplates: () => getFormTemplates.refetch(),
    refreshHistory: () => getProcessingHistory.refetch(),
  }
}

// Types are already exported above
