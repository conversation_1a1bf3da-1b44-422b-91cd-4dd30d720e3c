"use client";

import { useWorkflow, useCompleteWorkflowStep, useRejectWorkflowStep } from '@/hooks/workflows/useWorkflows';
import { WorkflowStatus } from '@/services/workflow-service';
import { WorkflowStepStatus as TypesWorkflowStepStatus } from '@/types/workflow';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { WorkflowStepCard } from '@/components/workflows/workflow-step';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@radix-ui/react-tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, AlertCircle, CheckCircle2, XCircle, <PERSON><PERSON><PERSON>, GitBranch, User, Calendar, FileText } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';

export default function WorkflowDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("steps");

  // Ensure params exists and get workflow ID
  if (!params) {
    return <div>Loading...</div>;
  }

  const workflowId = Array.isArray(params.id) ? params.id[0] : params.id!;

  // Fetch workflow
  const {
    data: workflow,
    isLoading,
    isError
  } = useWorkflow(workflowId);

  // Complete step mutation
  const { mutate: completeStep } = useCompleteWorkflowStep();

  // Reject step mutation
  const { mutate: rejectStep } = useRejectWorkflowStep();

  // Handle go back
  const handleGoBack = () => {
    router.back();
  };

  // Handle complete step
  const handleCompleteStep = (stepId: string, comment: string) => {
    completeStep(stepId, {
      comment
    });
  };

  // Handle reject step
  const handleRejectStep = (stepId: string, reason: string) => {
    rejectStep(stepId, reason);
  };

  // Format status badge
  const getStatusBadge = (status: WorkflowStatus) => {
    switch (status) {
      case WorkflowStatus.DRAFT:
        return { variant: "outline" as const, label: "Draft" };
      case WorkflowStatus.ACTIVE:
        return { variant: "secondary" as const, label: "Active" };
      case WorkflowStatus.COMPLETED:
        return { variant: "success" as const, label: "Completed" };
      case WorkflowStatus.CANCELLED:
        return { variant: "destructive" as const, label: "Cancelled" };
      default:
        return { variant: "outline" as const, label: status };
    }
  };

  if (isLoading) {
    return (
      <div className="container py-6 space-y-6">
        <Skeleton className="h-10 w-1/3" />
        <Skeleton className="h-6 w-1/4" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (isError || !workflow) {
    return (
      <div className="container py-6 space-y-6">
        <div className="flex flex-col items-center justify-center p-8">
          <AlertCircle className="h-10 w-10 text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2">Workflow Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The workflow you are looking for does not exist or you don't have permission to view it.
          </p>
          <Button asChild>
            <Link href="/workflows">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Workflows
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  const statusBadge = getStatusBadge(workflow.status);
  // Find the current step (in a real app, this would come from the API)
  const currentStepId = workflow.steps.find(s => s.status === 'running')?.id;

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <Button variant="ghost" size="sm" onClick={handleGoBack} className="mb-2">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold tracking-tight">{workflow.name}</h1>
            <Badge variant={statusBadge.variant}>{statusBadge.label}</Badge>
          </div>
          <p className="text-muted-foreground">
            {workflow.description || "No description provided"}
          </p>
        </div>

        <div className="flex gap-2">
          {workflow.status === WorkflowStatus.DRAFT && (
            <Button>
              <CheckCircle2 className="mr-2 h-4 w-4" />
              Start Workflow
            </Button>
          )}

          {workflow.status === WorkflowStatus.ACTIVE && (
            <Button variant="destructive">
              <XCircle className="mr-2 h-4 w-4" />
              Cancel Workflow
            </Button>
          )}

          <Button variant="outline" asChild>
            <Link href={`/workflows/${workflow.id}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Workflow Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Project</p>
              <p className="font-medium">{workflow.projectId}</p>
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Created By</p>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <p className="font-medium">{workflow.createdBy}</p>
              </div>
            </div>

            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Created At</p>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <p className="font-medium">{format(new Date(workflow.createdAt), 'MMM d, yyyy')}</p>
              </div>
            </div>

            {(workflow as any).documentId && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Document</p>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <Link href={`/documents/${(workflow as any).documentId}`} className="font-medium text-primary hover:underline">
                    View Document
                  </Link>
                </div>
              </div>
            )}

            {(workflow as any).completedAt && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Completed At</p>
                <div className="flex items-center gap-2">
                  <CheckCircle2 className="h-4 w-4 text-success" />
                  <p className="font-medium">{format(new Date((workflow as any).completedAt), 'MMM d, yyyy')}</p>
                </div>
              </div>
            )}

            {(workflow as any).cancelledAt && (
              <div className="space-y-1">
                <p className="text-sm text-muted-foreground">Cancelled At</p>
                <div className="flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-destructive" />
                  <p className="font-medium">{format(new Date((workflow as any).cancelledAt), 'MMM d, yyyy')}</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-full md:w-[400px]">
          <TabsTrigger value="steps">Workflow Steps</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="steps" className="mt-6 space-y-4">
          {workflow.steps.map((step) => {
            // Convert WorkflowStepDto to WorkflowStep for the component
            const convertedStep = {
              id: step.id || '',
              workflowId: workflow.id,
              name: step.name,
              description: step.description || '',
              type: step.type as any,
              order: step.order,
              position: { x: 0, y: step.order * 100, width: 200, height: 100 },
              config: {},
              status: step.status ? (step.status as unknown as TypesWorkflowStepStatus) : TypesWorkflowStepStatus.PENDING,
              assigneeType: 'user' as const,
              assigneeId: step.assigneeId,
              dueDate: step.dueDate
            };

            return (
              <WorkflowStepCard
                key={step.id}
                step={convertedStep}
                isCurrentStep={step.id === currentStepId}
                onApprove={(stepId, comment) => handleCompleteStep(stepId, comment)}
                onReject={(stepId, comment) => handleRejectStep(stepId, comment)}
                onComplete={(stepId, comment) => handleCompleteStep(stepId, comment)}
              />
            );
          })}
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow History</CardTitle>
              <CardDescription>
                Timeline of all actions taken on this workflow
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                    <GitBranch className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">Workflow Created</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(workflow.createdAt), 'MMM d, yyyy h:mm a')}
                    </p>
                    <p className="text-sm">
                      Created by {workflow.createdBy}
                    </p>
                  </div>
                </div>

                {/* More history items would go here */}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
