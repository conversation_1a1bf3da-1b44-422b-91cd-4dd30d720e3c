/**
 * Document Service
 * Handles all document-related API calls using backend API client
 */

import { backendApiClient, type PaginatedResponse } from './backend-api-client'
import type {
  Document,
  DocumentStatus,
  DocumentType,
  AIOperation
} from '../types/backend'

class DocumentService {
  /**
   * Get all documents with optional filters
   */
  async getDocuments(params?: {
    organizationId?: string
    projectId?: string
    status?: DocumentStatus[]
    type?: DocumentType[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Document>> {
    return await backendApiClient.getDocuments(params)
  }

  /**
   * Get document by ID
   */
  async getDocument(documentId: string): Promise<Document> {
    return await backendApiClient.getDocument(documentId)
  }

  /**
   * Upload a new document
   */
  async uploadDocument(file: File, metadata?: {
    name?: string
    description?: string
    projectId?: string
    tags?: string[]
    autoProcess?: boolean
  }): Promise<Document> {
    return await backendApiClient.uploadDocument(file, metadata)
  }

  /**
   * Update document metadata
   */
  async updateDocument(documentId: string, updates: Partial<Document>): Promise<Document> {
    return await backendApiClient.updateDocument(documentId, updates)
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string): Promise<void> {
    return await backendApiClient.deleteDocument(documentId)
  }

  /**
   * Process document with AI
   */
  async processDocument(documentId: string, options?: {
    analysisType?: string
    extractTables?: boolean
    extractKeyValuePairs?: boolean
    extractEntities?: boolean
    forceReprocess?: boolean
  }): Promise<AIOperation> {
    return await backendApiClient.processDocument(documentId, options)
  }

  /**
   * Analyze document content
   */
  async analyzeDocument(documentId: string, options?: {
    analysisType?: string
    extractTables?: boolean
    extractKeyValuePairs?: boolean
  }): Promise<AIOperation> {
    return await backendApiClient.analyzeDocument(documentId, options)
  }

  /**
   * Get document content
   */
  async getContent(documentId: string): Promise<Blob> {
    try {
      const response = await backendApiClient.request(`/documents/${documentId}/content`, {
        method: 'GET',
        responseType: 'blob'
      })

      if (response instanceof Blob) {
        return response
      }

      // If response is not a blob, convert it
      return new Blob([response], { type: 'application/octet-stream' })
    } catch (error) {
      console.error('Failed to get document content:', error)
      throw new Error(`Failed to retrieve content for document ${documentId}`)
    }
  }

  /**
   * Download document
   */
  async downloadDocument(documentId: string, filename?: string): Promise<void> {
    const url = `/documents/${documentId}/download`
    return await backendApiClient.downloadFile(url, filename)
  }

  /**
   * Create document
   */
  async createDocument(data: any): Promise<Document> {
    return await backendApiClient.request('/documents', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Get processing status
   */
  async getProcessingStatus(documentId: string): Promise<any> {
    return await backendApiClient.request(`/documents/${documentId}/processing/status`)
  }

  /**
   * Share document
   */
  async shareDocument(documentId: string, shareData: any): Promise<void> {
    await backendApiClient.request(`/documents/${documentId}/share`, {
      method: 'POST',
      body: JSON.stringify(shareData)
    })
  }

  /**
   * Get shared documents
   */
  async getSharedDocuments(): Promise<Document[]> {
    return await backendApiClient.request('/documents/shared')
  }

  /**
   * Bulk delete documents
   */
  async bulkDeleteDocuments(documentIds: string[]): Promise<{ success: string[]; failed: string[] }> {
    return await backendApiClient.request('/documents/bulk/delete', {
      method: 'POST',
      body: JSON.stringify({ documentIds })
    })
  }

  /**
   * Bulk process documents
   */
  async bulkProcessDocuments(documentIds: string[], options?: any): Promise<{ success: string[]; failed: string[] }> {
    return await backendApiClient.request('/documents/bulk/process', {
      method: 'POST',
      body: JSON.stringify({ documentIds, options })
    })
  }

  /**
   * Bulk download documents
   */
  async bulkDownloadDocuments(documentIds: string[]): Promise<void> {
    const response = await backendApiClient.request('/documents/bulk/download', {
      method: 'POST',
      body: JSON.stringify({ documentIds })
    })

    // Create download link for zip file
    const url = window.URL.createObjectURL(new Blob([response]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `documents-${Date.now()}.zip`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  }
}

// Export singleton instance
export const documentService = new DocumentService()
