/**
 * Tenant Types
 * Multi-tenant system types and interfaces
 */

import type { ID, Timestamp } from './index'

export interface Tenant {
  id: ID
  name: string
  displayName: string
  description?: string
  domain?: string
  subdomain?: string
  logo?: string
  settings: TenantSettings
  subscription?: TenantSubscription
  status: TenantStatus
  metadata?: Record<string, any>
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: ID
}

export enum TenantStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  ARCHIVED = 'archived'
}

export interface TenantSettings {
  allowPublicSignup: boolean
  requireEmailVerification: boolean
  defaultUserRole: string
  maxUsers?: number
  maxProjects?: number
  maxStorage?: number // in bytes
  features: string[]
  customDomain?: string
  ssoEnabled: boolean
  mfaRequired: boolean
  sessionTimeout: number // in minutes
  passwordPolicy: PasswordPolicy
  branding: TenantBranding
}

export interface PasswordPolicy {
  minLength: number
  requireUppercase: boolean
  requireLowercase: boolean
  requireNumbers: boolean
  requireSpecialChars: boolean
  maxAge?: number // in days
  preventReuse?: number // number of previous passwords to check
}

export interface TenantBranding {
  primaryColor?: string
  secondaryColor?: string
  logo?: string
  favicon?: string
  customCss?: string
  emailTemplate?: string
}

export interface TenantSubscription {
  plan: string
  status: 'active' | 'inactive' | 'cancelled' | 'past_due' | 'trialing'
  currentPeriodStart: Timestamp
  currentPeriodEnd: Timestamp
  cancelAtPeriodEnd: boolean
  trialEnd?: Timestamp
  billingEmail?: string
  paymentMethod?: {
    type: string
    last4?: string
    brand?: string
    expiryMonth?: number
    expiryYear?: number
  }
}

export interface TenantMember {
  id: ID
  tenantId: ID
  userId: ID
  role: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  joinedAt: Timestamp
  invitedBy?: ID
  user: {
    id: ID
    email: string
    firstName: string
    lastName: string
    displayName: string
    avatar?: string
  }
}

export interface TenantInvitation {
  id: ID
  tenantId: ID
  email: string
  role: string
  invitedBy: ID
  invitedAt: Timestamp
  expiresAt: Timestamp
  acceptedAt?: Timestamp
  status: 'pending' | 'accepted' | 'expired' | 'cancelled'
  message?: string
}

export interface CreateTenantRequest {
  name: string
  displayName: string
  description?: string
  domain?: string
  subdomain?: string
  settings?: Partial<TenantSettings>
  metadata?: Record<string, any>
}

export interface UpdateTenantRequest {
  name?: string
  displayName?: string
  description?: string
  domain?: string
  subdomain?: string
  logo?: string
  settings?: Partial<TenantSettings>
  metadata?: Record<string, any>
}

export interface TenantUsage {
  tenantId: ID
  users: {
    total: number
    active: number
    limit?: number
  }
  projects: {
    total: number
    limit?: number
  }
  storage: {
    used: number // in bytes
    limit?: number // in bytes
  }
  apiCalls: {
    current: number
    limit?: number
    resetDate: Timestamp
  }
  lastUpdated: Timestamp
}

export interface TenantAnalytics {
  tenantId: ID
  period: {
    start: Timestamp
    end: Timestamp
  }
  users: {
    total: number
    new: number
    active: number
    retention: number
  }
  projects: {
    total: number
    new: number
    active: number
  }
  documents: {
    total: number
    processed: number
    storage: number
  }
  activity: {
    logins: number
    uploads: number
    downloads: number
    apiCalls: number
  }
}

export interface TenantAuditLog {
  id: ID
  tenantId: ID
  userId?: ID
  action: string
  resource: string
  resourceId?: ID
  changes?: Record<string, {
    old: any
    new: any
  }>
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: Timestamp
}

export interface TenantBackup {
  id: ID
  tenantId: ID
  type: 'full' | 'incremental'
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  size?: number // in bytes
  createdAt: Timestamp
  completedAt?: Timestamp
  downloadUrl?: string
  expiresAt?: Timestamp
  metadata?: Record<string, any>
}

export interface TenantFilter {
  status?: TenantStatus
  plan?: string
  search?: string
  createdAfter?: Timestamp
  createdBefore?: Timestamp
  hasCustomDomain?: boolean
}

export interface TenantSearchResult {
  tenants: Tenant[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// Tenant context for components
export interface TenantContext {
  currentTenant: Tenant | null
  tenants: Tenant[]
  switchTenant: (tenantId: ID) => Promise<void>
  updateTenant: (updates: UpdateTenantRequest) => Promise<void>
  inviteMember: (email: string, role: string) => Promise<void>
  removeMember: (userId: ID) => Promise<void>
  isLoading: boolean
  error: string | null
}
