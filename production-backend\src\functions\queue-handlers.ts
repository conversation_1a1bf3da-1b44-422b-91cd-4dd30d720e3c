/**
 * Queue Storage Handlers for Azure Functions
 * Handles queue-based message processing for async operations
 */

import { InvocationContext, app } from '@azure/functions';
import { QueueServiceClient, QueueClient } from '@azure/storage-queue';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';

// Queue clients
let queueServiceClient: QueueServiceClient | null = null;
const queueClients: Map<string, QueueClient> = new Map();

/**
 * Initialize queue service client
 */
function getQueueServiceClient(): QueueServiceClient {
  if (!queueServiceClient) {
    queueServiceClient = new QueueServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );
  }
  return queueServiceClient;
}

/**
 * Get queue client for specific queue
 */
function getQueueClient(queueName: string): QueueClient {
  if (!queueClients.has(queueName)) {
    const serviceClient = getQueueServiceClient();
    queueClients.set(queueName, serviceClient.getQueueClient(queueName));
  }
  return queueClients.get(queueName)!;
}

/**
 * Document processing queue handler
 * Processes documents asynchronously
 */
async function documentProcessingQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.info('Document processing queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;
    const { documentId, processingType, priority = 'normal' } = message as any;

    if (!documentId || !processingType) {
      throw new Error('Invalid queue message: missing documentId or processingType');
    }

    // Get document from database
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.id = @documentId',
      [documentId]
    );

    if (documents.length === 0) {
      throw new Error(`Document not found: ${documentId}`);
    }

    const document = documents[0];

    // Update document status
    await db.updateItem('documents', {
      ...document,
      status: 'processing',
      processingStartedAt: new Date().toISOString(),
      processingType
    });

    // Process based on type
    let processingResult;
    switch (processingType) {
      case 'ai-analysis':
        processingResult = await processAIAnalysis(document);
        break;
      case 'text-extraction':
        processingResult = await processTextExtraction(document);
        break;
      case 'image-analysis':
        processingResult = await processImageAnalysis(document);
        break;
      case 'pdf-processing':
        processingResult = await processPDFDocument(document);
        break;
      default:
        processingResult = await processGenericDocument(document);
    }

    // Update document with results
    await db.updateItem('documents', {
      ...document,
      status: 'processed',
      processedAt: new Date().toISOString(),
      processingResult,
      extractedText: processingResult.extractedText || document.extractedText
    });

    // Publish processing completed event
    await publishEvent(
      EventType.DOCUMENT_APPROVED,
      `documents/${documentId}`,
      {
        documentId,
        processingType,
        result: processingResult,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Document processing completed', {
      documentId,
      processingType,
      resultSize: JSON.stringify(processingResult).length
    });

  } catch (error) {
    logger.error('Document processing queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });

    // Handle failed processing
    if (typeof queueItem === 'object' && queueItem !== null && 'documentId' in queueItem) {
      const documentId = (queueItem as any).documentId;
      try {
        const documents = await db.queryItems<any>('documents',
          'SELECT * FROM c WHERE c.id = @documentId',
          [documentId]
        );

        if (documents.length > 0) {
          const document = documents[0];
          await db.updateItem('documents', {
            ...document,
            status: 'processing_failed',
            error: error instanceof Error ? error.message : String(error),
            failedAt: new Date().toISOString()
          });
        }
      } catch (updateError) {
        logger.error('Failed to update document status after processing failure', { updateError });
      }
    }
  }
}

/**
 * Notification queue handler
 * Sends notifications asynchronously
 */
async function notificationQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.info('Notification queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;
    const {
      userId,
      type,
      title,
      message: notificationMessage,
      data = {},
      channels = ['in-app']
    } = message as any;

    if (!userId || !type || !title || !notificationMessage) {
      throw new Error('Invalid notification message: missing required fields');
    }

    // Create notification record
    const notificationId = `notif-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const notification = {
      id: notificationId,
      userId,
      type,
      title,
      message: notificationMessage,
      data,
      channels,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    await db.createItem('notifications', notification);

    // Send notification through specified channels
    const results = [];
    for (const channel of channels) {
      try {
        let result;
        switch (channel) {
          case 'email':
            result = await sendEmailNotification(notification);
            break;
          case 'push':
            result = await sendPushNotification(notification);
            break;
          case 'sms':
            result = await sendSMSNotification(notification);
            break;
          case 'in-app':
          default:
            result = await sendInAppNotification(notification);
            break;
        }
        results.push({ channel, success: true, result });
      } catch (channelError) {
        results.push({
          channel,
          success: false,
          error: channelError instanceof Error ? channelError.message : String(channelError)
        });
      }
    }

    // Update notification status
    const allSuccessful = results.every(r => r.success);
    await db.updateItem('notifications', {
      ...notification,
      status: allSuccessful ? 'sent' : 'partial_failure',
      sentAt: new Date().toISOString(),
      deliveryResults: results
    });

    // Publish notification sent event
    await publishEvent(
      EventType.NOTIFICATION_SENT,
      `notifications/${notificationId}`,
      {
        notificationId,
        userId,
        type,
        channels,
        results,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Notification processing completed', {
      notificationId,
      userId,
      type,
      successfulChannels: results.filter(r => r.success).length
    });

  } catch (error) {
    logger.error('Notification queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Email generation queue handler
 * Generates and sends emails asynchronously
 */
async function emailQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.info('Email queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;
    const {
      to,
      subject,
      template,
      templateData = {},
      priority = 'normal',
      scheduledFor
    } = message as any;

    if (!to || !subject || !template) {
      throw new Error('Invalid email message: missing required fields');
    }

    // Check if email is scheduled for future
    if (scheduledFor && new Date(scheduledFor) > new Date()) {
      logger.info('Email is scheduled for future, re-queuing', {
        to,
        subject,
        scheduledFor
      });

      // Re-queue with delay
      const delayMs = new Date(scheduledFor).getTime() - Date.now();
      await queueEmailWithDelay(message, delayMs);
      return;
    }

    // Generate email content from template
    const emailContent = await generateEmailFromTemplate(template, templateData);

    // Send email
    const emailResult = await sendEmail({
      to,
      subject,
      html: emailContent.html,
      text: emailContent.text,
      priority
    });

    // Log email sent
    await db.createItem('email-logs', {
      id: `email-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      to,
      subject,
      template,
      status: emailResult.success ? 'sent' : 'failed',
      sentAt: new Date().toISOString(),
      messageId: emailResult.messageId,
      error: emailResult.error
    });

    logger.info('Email sent successfully', {
      to,
      subject,
      template,
      messageId: emailResult.messageId
    });

  } catch (error) {
    logger.error('Email queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Dead letter queue handler
 * Handles messages that failed processing multiple times
 */
async function deadLetterQueueHandler(queueItem: unknown, context: InvocationContext): Promise<void> {
  logger.warn('Dead letter queue handler triggered', {
    queueItem,
    messageId: context.triggerMetadata?.id
  });

  try {
    const message = typeof queueItem === 'string' ? JSON.parse(queueItem) : queueItem;

    // Log dead letter message
    await db.createItem('dead-letter-messages', {
      id: `dead-letter-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      originalMessage: message,
      receivedAt: new Date().toISOString(),
      source: context.triggerMetadata?.queueName || 'unknown'
    });

    // Publish alert for dead letter message
    await publishEvent(
      EventType.PERFORMANCE_ALERT,
      'queues/dead-letter',
      {
        alertType: 'dead_letter_message',
        severity: 'medium',
        message,
        source: context.triggerMetadata?.queueName,
        timestamp: new Date().toISOString()
      }
    );

    logger.warn('Dead letter message logged', {
      source: context.triggerMetadata?.queueName
    });

  } catch (error) {
    logger.error('Dead letter queue handler failed', {
      queueItem,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Production document processing operations
async function processAIAnalysis(document: any): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');
  const { aiServices } = require('../shared/services/ai-services');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const containerClient = blobServiceClient.getContainerClient('documents');
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Perform comprehensive AI analysis
    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      ['layout', 'key_value', 'entities', 'tables']
    );

    // Extract entities using AI services
    const entityResult = await aiServices.extractEntities(analysisResult.extractedText || '');

    // Classify document type
    const classificationResult = await aiServices.classifyDocument(analysisResult.extractedText || '');

    return {
      type: 'ai-analysis',
      confidence: analysisResult.confidence,
      entities: entityResult.entities || [],
      documentType: classificationResult.documentType,
      extractedText: analysisResult.extractedText,
      keyValuePairs: analysisResult.keyValuePairs,
      tables: analysisResult.tables,
      processingTime: Date.now() - new Date(document.createdAt || Date.now()).getTime()
    };
  } catch (error) {
    logger.error('AI analysis failed', {
      documentId: document.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function processTextExtraction(document: any): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const containerClient = blobServiceClient.getContainerClient('documents');
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Extract text using Azure Document Intelligence
    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      'prebuilt-read'
    );

    const extractedText = analysisResult.extractedText || '';
    const wordCount = extractedText.split(/\s+/).filter((word: string) => word.length > 0).length;
    const characterCount = extractedText.length;
    const lineCount = extractedText.split('\n').length;

    return {
      type: 'text-extraction',
      extractedText,
      wordCount,
      characterCount,
      lineCount,
      confidence: analysisResult.confidence,
      language: analysisResult.language || 'en',
      processingTime: Date.now() - new Date(document.createdAt || Date.now()).getTime()
    };
  } catch (error) {
    logger.error('Text extraction failed', {
      documentId: document.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function processImageAnalysis(document: any): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');
  const { aiServices } = require('../shared/services/ai-services');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const containerClient = blobServiceClient.getContainerClient('documents');
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Analyze image using Computer Vision
    const imageAnalysis = await aiServices.analyzeImage(documentBuffer);

    // Extract text from image if present
    const ocrResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      'prebuilt-read'
    );

    return {
      type: 'image-analysis',
      objects: imageAnalysis.objects || [],
      tags: imageAnalysis.tags || [],
      text: ocrResult.extractedText || '',
      confidence: Math.min(imageAnalysis.confidence || 0.9, ocrResult.confidence || 0.9),
      dimensions: imageAnalysis.dimensions,
      colorScheme: imageAnalysis.colorScheme,
      processingTime: Date.now() - new Date(document.createdAt || Date.now()).getTime()
    };
  } catch (error) {
    logger.error('Image analysis failed', {
      documentId: document.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function processPDFDocument(document: any): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const containerClient = blobServiceClient.getContainerClient('documents');
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Analyze PDF using Document Intelligence
    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      'prebuilt-layout'
    );

    // Extract PDF metadata using pdf-parse
    const pdfParse = require('pdf-parse');
    const pdfData = await pdfParse(documentBuffer);

    return {
      type: 'pdf-processing',
      pages: pdfData.numpages,
      extractedText: analysisResult.extractedText || pdfData.text,
      metadata: {
        title: pdfData.info?.Title,
        author: pdfData.info?.Author,
        creator: pdfData.info?.Creator,
        producer: pdfData.info?.Producer,
        creationDate: pdfData.info?.CreationDate,
        modificationDate: pdfData.info?.ModDate,
        version: pdfData.version
      },
      tables: analysisResult.tables || [],
      keyValuePairs: analysisResult.keyValuePairs || [],
      confidence: analysisResult.confidence,
      processingTime: Date.now() - new Date(document.createdAt || Date.now()).getTime()
    };
  } catch (error) {
    logger.error('PDF processing failed', {
      documentId: document.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function processGenericDocument(document: any): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = BlobServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const containerClient = blobServiceClient.getContainerClient('documents');
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);

    // Determine file type and process accordingly
    const fileExtension = (document.fileName || document.blobName || '').toLowerCase();
    let analysisType = 'prebuilt-read';

    if (fileExtension.includes('.pdf')) {
      analysisType = 'prebuilt-layout';
    } else if (fileExtension.includes('.jpg') || fileExtension.includes('.png')) {
      analysisType = 'prebuilt-read';
    }

    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      analysisType
    );

    return {
      type: 'generic',
      processed: true,
      fileType: fileExtension.split('.').pop()?.toUpperCase() || 'UNKNOWN',
      extractedText: analysisResult.extractedText,
      confidence: analysisResult.confidence,
      timestamp: new Date().toISOString(),
      processingTime: Date.now() - new Date(document.createdAt || Date.now()).getTime()
    };
  } catch (error) {
    logger.error('Generic document processing failed', {
      documentId: document.id,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Helper function to convert stream to buffer
async function streamToBuffer(readableStream: any): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    readableStream.on('data', (data: Buffer) => {
      chunks.push(data);
    });
    readableStream.on('end', () => {
      resolve(Buffer.concat(chunks));
    });
    readableStream.on('error', reject);
  });
}

// Production notification functions
async function sendEmailNotification(notification: any): Promise<any> {
  const { notificationService } = require('../shared/services/notification-service');

  try {
    const emailResult = await notificationService.sendEmail({
      to: notification.recipient,
      subject: notification.subject,
      html: notification.htmlContent,
      text: notification.textContent,
      templateId: notification.templateId,
      templateData: notification.templateData,
      organizationId: notification.organizationId,
      priority: notification.priority || 'normal'
    });

    logger.info('Email notification sent successfully', {
      recipient: notification.recipient,
      messageId: emailResult.messageId,
      organizationId: notification.organizationId
    });

    return {
      success: true,
      messageId: emailResult.messageId,
      provider: emailResult.provider,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Email notification failed', {
      recipient: notification.recipient,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function sendPushNotification(notification: any): Promise<any> {
  const { notificationService } = require('../shared/services/notification-service');

  try {
    const pushResult = await notificationService.sendPushNotification({
      deviceTokens: notification.deviceTokens,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      platform: notification.platform, // 'ios', 'android', 'web'
      organizationId: notification.organizationId,
      priority: notification.priority || 'normal'
    });

    logger.info('Push notification sent successfully', {
      deviceCount: notification.deviceTokens?.length || 0,
      messageId: pushResult.messageId,
      organizationId: notification.organizationId
    });

    return {
      success: true,
      messageId: pushResult.messageId,
      deliveredCount: pushResult.deliveredCount,
      failedCount: pushResult.failedCount,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Push notification failed', {
      deviceCount: notification.deviceTokens?.length || 0,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function sendSMSNotification(notification: any): Promise<any> {
  const { notificationService } = require('../shared/services/notification-service');

  try {
    const smsResult = await notificationService.sendSMS({
      to: notification.phoneNumber,
      message: notification.message,
      organizationId: notification.organizationId,
      priority: notification.priority || 'normal'
    });

    logger.info('SMS notification sent successfully', {
      phoneNumber: notification.phoneNumber,
      messageId: smsResult.messageId,
      organizationId: notification.organizationId
    });

    return {
      success: true,
      messageId: smsResult.messageId,
      provider: smsResult.provider,
      cost: smsResult.cost,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('SMS notification failed', {
      phoneNumber: notification.phoneNumber,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function sendInAppNotification(notification: any): Promise<any> {
  const { notificationService } = require('../shared/services/notification-service');

  try {
    const inAppResult = await notificationService.sendInAppNotification({
      userId: notification.userId,
      title: notification.title,
      message: notification.message,
      type: notification.type || 'info',
      actionUrl: notification.actionUrl,
      organizationId: notification.organizationId,
      priority: notification.priority || 'normal'
    });

    logger.info('In-app notification sent successfully', {
      userId: notification.userId,
      messageId: inAppResult.messageId,
      organizationId: notification.organizationId
    });

    return {
      success: true,
      messageId: inAppResult.messageId,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('In-app notification failed', {
      userId: notification.userId,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function generateEmailFromTemplate(template: string, data: any): Promise<any> {
  const { templateService } = require('../shared/services/template-service');

  try {
    const templateResult = await templateService.renderTemplate(template, data);

    return {
      html: templateResult.html,
      text: templateResult.text,
      subject: templateResult.subject,
      templateId: template,
      renderedAt: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Email template generation failed', {
      template,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function sendEmail(emailData: any): Promise<any> {
  const { notificationService } = require('../shared/services/notification-service');

  try {
    const emailResult = await notificationService.sendEmail(emailData);

    return {
      success: true,
      messageId: emailResult.messageId,
      provider: emailResult.provider,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Direct email send failed', {
      to: emailData.to,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

async function queueEmailWithDelay(message: any, delayMs: number): Promise<void> {
  const { QueueServiceClient } = require('@azure/storage-queue');

  try {
    const queueServiceClient = QueueServiceClient.fromConnectionString(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    );
    const queueClient = queueServiceClient.getQueueClient('delayed-emails');

    // Calculate visibility timeout for delay
    const visibilityTimeoutSeconds = Math.ceil(delayMs / 1000);

    await queueClient.sendMessage(
      JSON.stringify(message),
      {
        visibilityTimeoutInSeconds: visibilityTimeoutSeconds,
        messageTimeToLiveInSeconds: 7 * 24 * 60 * 60 // 7 days
      }
    );

    logger.info('Email queued with delay', {
      delayMs,
      visibilityTimeoutSeconds,
      messageId: message.id
    });
  } catch (error) {
    logger.error('Failed to queue delayed email', {
      delayMs,
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Register queue triggers
app.storageQueue('documentProcessingQueue', {
  queueName: 'document-processing',
  connection: 'AzureWebJobsStorage',
  handler: documentProcessingQueueHandler
});

app.storageQueue('notificationQueue', {
  queueName: 'notifications',
  connection: 'AzureWebJobsStorage',
  handler: notificationQueueHandler
});

app.storageQueue('emailQueue', {
  queueName: 'emails',
  connection: 'AzureWebJobsStorage',
  handler: emailQueueHandler
});

app.storageQueue('deadLetterQueue', {
  queueName: 'dead-letter-queue',
  connection: 'AzureWebJobsStorage',
  handler: deadLetterQueueHandler
});

// Export queue utilities for use by other modules
export { getQueueClient, getQueueServiceClient };
