'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  RefreshCw, 
  AlertTriangle,
  Activity,
  Brain,
  Search
} from 'lucide-react'

interface ComponentTest {
  name: string
  component: string
  status: 'loading' | 'success' | 'error' | 'pending'
  error?: string
  loadTime?: number
}

export function DashboardLoadingTest() {
  const [tests, setTests] = useState<ComponentTest[]>([
    { name: 'Performance Widget', component: 'PerformanceWidget', status: 'pending' },
    { name: 'AI Insights Widget', component: 'AIInsightsWidget', status: 'pending' },
    { name: 'Search Analytics Widget', component: 'SearchAnalyticsWidget', status: 'pending' }
  ])
  const [isRunning, setIsRunning] = useState(false)

  const runTests = async () => {
    setIsRunning(true)
    
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i]
      
      // Update status to loading
      setTests(prev => prev.map((t, idx) => 
        idx === i ? { ...t, status: 'loading' } : t
      ))

      const startTime = Date.now()
      
      try {
        // Test component loading
        switch (test.component) {
          case 'PerformanceWidget':
            await import('@/components/dashboard/performance-widget')
            break
          case 'AIInsightsWidget':
            await import('@/components/dashboard/ai-insights-widget')
            break
          case 'SearchAnalyticsWidget':
            await import('@/components/dashboard/search-analytics-widget')
            break
        }
        
        const loadTime = Date.now() - startTime
        
        // Update status to success
        setTests(prev => prev.map((t, idx) => 
          idx === i ? { ...t, status: 'success', loadTime } : t
        ))
        
      } catch (error) {
        const loadTime = Date.now() - startTime
        
        // Update status to error
        setTests(prev => prev.map((t, idx) => 
          idx === i ? { 
            ...t, 
            status: 'error', 
            error: error instanceof Error ? error.message : String(error),
            loadTime 
          } : t
        ))
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    setIsRunning(false)
  }

  const resetTests = () => {
    setTests(prev => prev.map(t => ({ 
      ...t, 
      status: 'pending', 
      error: undefined, 
      loadTime: undefined 
    })))
  }

  const getStatusIcon = (status: ComponentTest['status']) => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: ComponentTest['status']) => {
    switch (status) {
      case 'loading':
        return <Badge variant="outline" className="text-blue-600">Loading</Badge>
      case 'success':
        return <Badge variant="outline" className="text-green-600">Success</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  const getComponentIcon = (component: string) => {
    switch (component) {
      case 'PerformanceWidget':
        return <Activity className="h-4 w-4" />
      case 'AIInsightsWidget':
        return <Brain className="h-4 w-4" />
      case 'SearchAnalyticsWidget':
        return <Search className="h-4 w-4" />
      default:
        return <AlertTriangle className="h-4 w-4" />
    }
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const errorCount = tests.filter(t => t.status === 'error').length
  const totalLoadTime = tests.reduce((sum, t) => sum + (t.loadTime || 0), 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5" />
          Dashboard Component Loading Test
        </CardTitle>
        <CardDescription>
          Test dashboard widget loading and identify any issues
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Test Controls */}
        <div className="flex items-center gap-2">
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            size="sm"
          >
            {isRunning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Activity className="h-4 w-4 mr-2" />
                Run Tests
              </>
            )}
          </Button>
          
          <Button 
            onClick={resetTests} 
            variant="outline" 
            size="sm"
            disabled={isRunning}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>

        {/* Test Results Summary */}
        {(successCount > 0 || errorCount > 0) && (
          <div className="grid grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{successCount}</div>
              <div className="text-xs text-muted-foreground">Successful</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-red-600">{errorCount}</div>
              <div className="text-xs text-muted-foreground">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{totalLoadTime}ms</div>
              <div className="text-xs text-muted-foreground">Total Time</div>
            </div>
          </div>
        )}

        {/* Individual Test Results */}
        <div className="space-y-2">
          {tests.map((test, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getComponentIcon(test.component)}
                <div>
                  <div className="font-medium text-sm">{test.name}</div>
                  <div className="text-xs text-muted-foreground">{test.component}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                {test.loadTime && (
                  <span className="text-xs text-muted-foreground">
                    {test.loadTime}ms
                  </span>
                )}
                {getStatusBadge(test.status)}
                {getStatusIcon(test.status)}
              </div>
            </div>
          ))}
        </div>

        {/* Error Details */}
        {tests.some(t => t.error) && (
          <div className="space-y-2">
            <h4 className="font-medium text-sm">Error Details:</h4>
            {tests.filter(t => t.error).map((test, index) => (
              <Alert key={index} variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>{test.name}:</strong> {test.error}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* Recommendations */}
        {errorCount > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Troubleshooting:</strong>
              <ul className="mt-2 text-sm space-y-1">
                <li>• Check browser console for detailed error messages</li>
                <li>• Verify all required services are running</li>
                <li>• Ensure network connectivity to backend services</li>
                <li>• Try refreshing the page or clearing browser cache</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {successCount === tests.length && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              All dashboard components loaded successfully! 
              Total load time: {totalLoadTime}ms
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
