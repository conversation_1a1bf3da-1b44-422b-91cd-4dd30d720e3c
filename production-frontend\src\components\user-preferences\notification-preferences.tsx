"use client";

import { useState } from "react";
import { useUpdateUserPreferences } from "@/hooks/user-preferences";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { UserPreferences } from "@/types/user";
import { Bell, Mail, FileText, MessageSquare, AtSign, Building2, FolderKanban } from "lucide-react";

interface NotificationPreferencesProps {
  preferences: UserPreferences['notifications'];
}

export function NotificationPreferences({ preferences }: NotificationPreferencesProps) {
  const [notificationPrefs, setNotificationPrefs] = useState<UserPreferences['notifications']>(
    preferences || {
      email: true,
      inApp: true,
      documentUploaded: true,
      documentProcessed: true,
      commentAdded: true,
      mentionedInComment: true,
      projectInvitation: true,
      organizationInvitation: true,
    }
  );

  const updatePreferences = useUpdateUserPreferences();

  const handleToggle = async (key: keyof UserPreferences['notifications']) => {
    const updatedPrefs = {
      ...notificationPrefs,
      [key]: !notificationPrefs[key],
    };

    setNotificationPrefs(updatedPrefs);

    // Update preferences in the backend
    try {
      await updatePreferences({
        notifications: updatedPrefs,
      });
    } catch (error) {
      console.error('Failed to update preferences:', error);
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid gap-6">
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <Mail className="h-4 w-4" />
            <label htmlFor="email-notifications" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Email Notifications
            </label>
          </div>
          <Switch
            id="email-notifications"
            checked={!!notificationPrefs.email}
            onCheckedChange={() => handleToggle('email')}
          />
        </div>
        
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <label htmlFor="in-app-notifications" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              In-App Notifications
            </label>
          </div>
          <Switch
            id="in-app-notifications"
            checked={!!notificationPrefs.inApp}
            onCheckedChange={() => handleToggle('inApp')}
          />
        </div>

        <div className="border-t pt-4">
          <h3 className="text-sm font-medium mb-4">Notification Types</h3>
          
          <div className="grid gap-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <label htmlFor="document-uploaded" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Document Uploaded
                </label>
              </div>
              <Switch
                id="document-uploaded"
                checked={notificationPrefs.documentUploaded}
                onCheckedChange={() => handleToggle('documentUploaded')}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <label htmlFor="document-processed" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Document Processed
                </label>
              </div>
              <Switch
                id="document-processed"
                checked={notificationPrefs.documentProcessed}
                onCheckedChange={() => handleToggle('documentProcessed')}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-4 w-4" />
                <label htmlFor="comment-added" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Comment Added
                </label>
              </div>
              <Switch
                id="comment-added"
                checked={notificationPrefs.commentAdded}
                onCheckedChange={() => handleToggle('commentAdded')}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <AtSign className="h-4 w-4" />
                <label htmlFor="mentioned-in-comment" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Mentioned in Comment
                </label>
              </div>
              <Switch
                id="mentioned-in-comment"
                checked={notificationPrefs.mentionedInComment}
                onCheckedChange={() => handleToggle('mentionedInComment')}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <FolderKanban className="h-4 w-4" />
                <label htmlFor="project-invitation" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Project Invitation
                </label>
              </div>
              <Switch
                id="project-invitation"
                checked={notificationPrefs.projectInvitation}
                onCheckedChange={() => handleToggle('projectInvitation')}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2">
                <Building2 className="h-4 w-4" />
                <label htmlFor="organization-invitation" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Organization Invitation
                </label>
              </div>
              <Switch
                id="organization-invitation"
                checked={notificationPrefs.organizationInvitation}
                onCheckedChange={() => handleToggle('organizationInvitation')}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
