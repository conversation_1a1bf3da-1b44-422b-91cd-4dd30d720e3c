/**
 * Project Store Hook
 * Provides access to project store functionality
 */

import { useProjectStore as useProjectStoreBase } from '../stores/project-store'

// Re-export the main store hook
export const useProjectStore = useProjectStoreBase

// Selector hooks for specific project data
export const useProjects = () => {
  const { 
    projects, 
    selectedProject, 
    members, 
    loading, 
    error,
    lastUpdated 
  } = useProjectStore()
  
  return {
    projects,
    selectedProject,
    members,
    loading,
    error,
    lastUpdated,
  }
}

export const useProjectList = () => useProjectStore((state) => state.projects)
export const useSelectedProject = () => useProjectStore((state) => state.selectedProject)
export const useProjectMembers = () => useProjectStore((state) => state.members)
export const useProjectLoading = () => useProjectStore((state) => state.loading)
export const useProjectError = () => useProjectStore((state) => state.error)

// Action hooks
export const useSelectProject = () => useProjectStore((state) => state.selectProject)
export const useCreateProject = () => useProjectStore((state) => state.createProject)
export const useUpdateProject = () => useProjectStore((state) => state.updateProject)
export const useDeleteProject = () => useProjectStore((state) => state.deleteProject)
export const useAddProjectMember = () => useProjectStore((state) => state.addMember)
export const useRemoveProjectMember = () => useProjectStore((state) => state.removeMember)
export const useRefreshProjects = () => useProjectStore((state) => state.refresh)
