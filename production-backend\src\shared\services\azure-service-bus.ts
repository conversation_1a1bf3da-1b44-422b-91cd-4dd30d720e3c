/**
 * Azure Service Bus Service
 * Provides message queuing and workflow orchestration for organizational processes
 */

import { ServiceBusClient, ServiceBusSender, ServiceBusReceiver } from '@azure/service-bus';
import { logger } from '../utils/logger';

export interface WorkflowExecutionMessage {
  workflowId: string;
  documentId: string;
  organizationId: string;
  userId: string;
  executionId: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  data: Record<string, any>;
  timestamp: string;
}

export interface ApprovalRequestMessage {
  approvalId: string;
  documentId: string;
  workflowId: string;
  organizationId: string;
  requesterId: string;
  approverId: string;
  approvalType: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  dueDate?: string;
  data: Record<string, any>;
  timestamp: string;
}

export interface DocumentProcessingMessage {
  documentId: string;
  organizationId: string;
  userId: string;
  processingType: 'classification' | 'extraction' | 'validation' | 'routing';
  documentUrl: string;
  metadata: Record<string, any>;
  timestamp: string;
}

export interface ComplianceCheckMessage {
  documentId: string;
  organizationId: string;
  complianceFramework: string[];
  checkType: 'automated' | 'manual' | 'audit';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  data: Record<string, any>;
  timestamp: string;
}

export interface MessageHandler<T> {
  (message: T): Promise<void>;
}

class AzureServiceBusService {
  private client: ServiceBusClient;
  private senders: Map<string, ServiceBusSender> = new Map();
  private receivers: Map<string, ServiceBusReceiver> = new Map();
  private initialized: boolean = false;

  constructor() {
    const connectionString = process.env.AZURE_SERVICE_BUS_CONNECTION_STRING;

    if (!connectionString) {
      throw new Error('Azure Service Bus connection string is required');
    }

    this.client = new ServiceBusClient(connectionString);
  }

  /**
   * Initialize the service bus service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Initialize senders for different queues
      this.senders.set('workflow-executions', this.client.createSender('workflow-executions'));
      this.senders.set('approval-requests', this.client.createSender('approval-requests'));
      this.senders.set('document-processing', this.client.createSender('document-processing'));
      this.senders.set('compliance-checks', this.client.createSender('compliance-checks'));

      this.initialized = true;
      logger.info('Azure Service Bus Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Azure Service Bus Service', { error });
      throw error;
    }
  }

  /**
   * Send workflow execution message
   */
  async sendWorkflowExecution(message: WorkflowExecutionMessage): Promise<void> {
    try {
      await this.ensureInitialized();
      
      const sender = this.senders.get('workflow-executions');
      if (!sender) {
        throw new Error('Workflow executions sender not initialized');
      }

      await sender.sendMessages({
        body: message,
        messageId: `workflow-${message.executionId}`,
        correlationId: message.workflowId,
        subject: 'WorkflowExecution',
        applicationProperties: {
          organizationId: message.organizationId,
          priority: message.priority,
          workflowId: message.workflowId
        },
        timeToLive: 24 * 60 * 60 * 1000 // 24 hours
      });

      logger.info('Workflow execution message sent', {
        workflowId: message.workflowId,
        executionId: message.executionId
      });

    } catch (error) {
      logger.error('Failed to send workflow execution message', {
        workflowId: message.workflowId,
        error
      });
      throw error;
    }
  }

  /**
   * Send approval request message
   */
  async sendApprovalRequest(message: ApprovalRequestMessage): Promise<void> {
    try {
      await this.ensureInitialized();
      
      const sender = this.senders.get('approval-requests');
      if (!sender) {
        throw new Error('Approval requests sender not initialized');
      }

      const messageToSend = {
        body: message,
        messageId: `approval-${message.approvalId}`,
        correlationId: message.workflowId,
        subject: 'ApprovalRequest',
        applicationProperties: {
          organizationId: message.organizationId,
          priority: message.priority,
          approverId: message.approverId,
          approvalType: message.approvalType
        },
        timeToLive: 7 * 24 * 60 * 60 * 1000 // 7 days
      };

      if (message.dueDate) {
        await sender.scheduleMessages(messageToSend, new Date(message.dueDate));
      } else {
        await sender.sendMessages(messageToSend);
      }

      logger.info('Approval request message sent', {
        approvalId: message.approvalId,
        approverId: message.approverId
      });

    } catch (error) {
      logger.error('Failed to send approval request message', {
        approvalId: message.approvalId,
        error
      });
      throw error;
    }
  }

  /**
   * Send document processing message
   */
  async sendDocumentProcessing(message: DocumentProcessingMessage): Promise<void> {
    try {
      await this.ensureInitialized();
      
      const sender = this.senders.get('document-processing');
      if (!sender) {
        throw new Error('Document processing sender not initialized');
      }

      await sender.sendMessages({
        body: message,
        messageId: `document-${message.documentId}-${Date.now()}`,
        correlationId: message.documentId,
        subject: 'DocumentProcessing',
        applicationProperties: {
          organizationId: message.organizationId,
          processingType: message.processingType,
          userId: message.userId
        },
        timeToLive: 2 * 60 * 60 * 1000 // 2 hours
      });

      logger.info('Document processing message sent', {
        documentId: message.documentId,
        processingType: message.processingType
      });

    } catch (error) {
      logger.error('Failed to send document processing message', {
        documentId: message.documentId,
        error
      });
      throw error;
    }
  }

  /**
   * Send compliance check message
   */
  async sendComplianceCheck(message: ComplianceCheckMessage): Promise<void> {
    try {
      await this.ensureInitialized();
      
      const sender = this.senders.get('compliance-checks');
      if (!sender) {
        throw new Error('Compliance checks sender not initialized');
      }

      await sender.sendMessages({
        body: message,
        messageId: `compliance-${message.documentId}-${Date.now()}`,
        correlationId: message.documentId,
        subject: 'ComplianceCheck',
        applicationProperties: {
          organizationId: message.organizationId,
          priority: message.priority,
          checkType: message.checkType,
          frameworks: message.complianceFramework.join(',')
        },
        timeToLive: 24 * 60 * 60 * 1000 // 24 hours
      });

      logger.info('Compliance check message sent', {
        documentId: message.documentId,
        frameworks: message.complianceFramework
      });

    } catch (error) {
      logger.error('Failed to send compliance check message', {
        documentId: message.documentId,
        error
      });
      throw error;
    }
  }

  /**
   * Start listening for workflow execution messages
   */
  async startWorkflowExecutionListener(handler: MessageHandler<WorkflowExecutionMessage>): Promise<void> {
    try {
      await this.ensureInitialized();

      const receiver = this.client.createReceiver('workflow-executions');
      this.receivers.set('workflow-executions', receiver);

      receiver.subscribe({
        processMessage: async (message) => {
          try {
            const workflowMessage = message.body as WorkflowExecutionMessage;
            await handler(workflowMessage);
            await receiver.completeMessage(message);
            
            logger.info('Workflow execution message processed', {
              workflowId: workflowMessage.workflowId,
              executionId: workflowMessage.executionId
            });
          } catch (error) {
            logger.error('Failed to process workflow execution message', { error });
            await receiver.deadLetterMessage(message, {
              deadLetterReason: 'ProcessingError',
              deadLetterErrorDescription: error instanceof Error ? error.message : String(error)
            });
          }
        },
        processError: async (args) => {
          logger.error('Workflow execution listener error', { error: args.error });
        }
      });

      logger.info('Workflow execution listener started');

    } catch (error) {
      logger.error('Failed to start workflow execution listener', { error });
      throw error;
    }
  }

  /**
   * Start listening for approval request messages
   */
  async startApprovalRequestListener(handler: MessageHandler<ApprovalRequestMessage>): Promise<void> {
    try {
      await this.ensureInitialized();

      const receiver = this.client.createReceiver('approval-requests');
      this.receivers.set('approval-requests', receiver);

      receiver.subscribe({
        processMessage: async (message) => {
          try {
            const approvalMessage = message.body as ApprovalRequestMessage;
            await handler(approvalMessage);
            await receiver.completeMessage(message);
            
            logger.info('Approval request message processed', {
              approvalId: approvalMessage.approvalId,
              approverId: approvalMessage.approverId
            });
          } catch (error) {
            logger.error('Failed to process approval request message', { error });
            await receiver.deadLetterMessage(message, {
              deadLetterReason: 'ProcessingError',
              deadLetterErrorDescription: error instanceof Error ? error.message : String(error)
            });
          }
        },
        processError: async (args) => {
          logger.error('Approval request listener error', { error: args.error });
        }
      });

      logger.info('Approval request listener started');

    } catch (error) {
      logger.error('Failed to start approval request listener', { error });
      throw error;
    }
  }

  /**
   * Stop all listeners and close connections
   */
  async close(): Promise<void> {
    try {
      // Close all receivers
      for (const [queueName, receiver] of this.receivers) {
        await receiver.close();
        logger.info('Receiver closed', { queueName });
      }

      // Close all senders
      for (const [queueName, sender] of this.senders) {
        await sender.close();
        logger.info('Sender closed', { queueName });
      }

      // Close the client
      await this.client.close();

      this.initialized = false;
      logger.info('Azure Service Bus Service closed');

    } catch (error) {
      logger.error('Failed to close Azure Service Bus Service', { error });
      throw error;
    }
  }

  /**
   * Ensure the service is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.initialize();
    }
  }
}

// Create and export singleton instance
export const azureServiceBusService = new AzureServiceBusService();
export default azureServiceBusService;
