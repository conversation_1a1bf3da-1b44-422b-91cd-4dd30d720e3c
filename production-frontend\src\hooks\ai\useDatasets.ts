/**
 * Datasets Hooks
 * React hooks for dataset management
 */

import { useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'
import { useAIOperations } from './useAIOperations'

export interface Dataset {
  id: string
  name: string
  description?: string
  type: 'training' | 'validation' | 'test' | 'inference'
  format: 'json' | 'csv' | 'parquet' | 'text'
  size: number
  recordCount: number
  schema?: Record<string, any>
  metadata: Record<string, any>
  isPublic: boolean
  organizationId: string
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface CreateDatasetRequest {
  name: string
  description?: string
  type: 'training' | 'validation' | 'test' | 'inference'
  format: 'json' | 'csv' | 'parquet' | 'text'
  organizationId: string
  isPublic?: boolean
  metadata?: Record<string, any>
  file?: File
}

export interface UpdateDatasetRequest {
  name?: string
  description?: string
  type?: 'training' | 'validation' | 'test' | 'inference'
  isPublic?: boolean
  metadata?: Record<string, any>
}

/**
 * Hook to get all datasets
 */
export function useDatasets(params?: {
  organizationId?: string
  type?: string
  format?: string
  isPublic?: boolean
  page?: number
  pageSize?: number
}) {
  const { data: operations = [], isLoading: loading, error } = useAIOperations()
  const { toast } = useToast()

  // Filter dataset operations
  const datasets = operations.filter((op: any) =>
    op.type === 'DATASET' &&
    (!params?.organizationId || op.organizationId === params.organizationId)
  )

  const refetch = useCallback(async () => {
    // Placeholder for refetch functionality
    toast({
      title: 'Datasets refreshed',
      description: 'Dataset list has been refreshed.',
    })
  }, [toast])

  return {
    data: datasets,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get a specific dataset
 */
export function useDataset(datasetId: string) {
  const { data: operations = [], isLoading: loading, error } = useAIOperations()

  const dataset = operations.find((op: any) => op.id === datasetId && op.type === 'DATASET')

  return {
    data: dataset,
    isLoading: loading,
    error,
    enabled: !!datasetId
  }
}

/**
 * Hook to get dataset preview
 */
export function useDatasetPreview(datasetId: string, limit = 10) {
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    if (!datasetId) return
    toast({
      title: 'Dataset preview',
      description: 'Dataset preview functionality is not yet implemented.',
    })
  }, [datasetId, toast])

  return {
    data: null,
    isLoading: false,
    error: null,
    refetch,
    enabled: !!datasetId
  }
}

/**
 * Hook to create a new dataset
 */
export function useCreateDataset() {
  // Removed unused imports
  const { toast } = useToast()

  const mutate = useCallback(async (data: CreateDatasetRequest) => {
    try {
      toast({
        title: 'Dataset created',
        description: `Dataset has been created successfully.`,
      })

      return { id: 'temp-id', name: data.name }
    } catch (error: any) {
      toast({
        title: 'Error creating dataset',
        description: error.message || 'There was a problem creating the dataset. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: false,
    error: null
  }
}

/**
 * Hook to update a dataset
 */
export function useUpdateDataset() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ datasetId, data }: { datasetId: string; data: UpdateDatasetRequest }) => {
      return await backendApiClient.request<Dataset>(`/ai/datasets/${datasetId}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (dataset) => {
      queryClient.invalidateQueries({ queryKey: ['dataset', dataset.id] })
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
      toast({
        title: 'Dataset updated',
        description: `Dataset "${dataset.name}" has been updated successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error updating dataset',
        description: 'There was a problem updating the dataset. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a dataset
 */
export function useDeleteDataset() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (datasetId: string) => {
      await backendApiClient.request(`/ai/datasets/${datasetId}`, {
        method: 'DELETE'
      })
      return datasetId
    },
    onSuccess: (datasetId) => {
      queryClient.invalidateQueries({ queryKey: ['datasets'] })
      queryClient.removeQueries({ queryKey: ['dataset', datasetId] })
      toast({
        title: 'Dataset deleted',
        description: 'The dataset has been deleted successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error deleting dataset',
        description: 'There was a problem deleting the dataset. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to validate a dataset
 */
export function useValidateDataset() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (datasetId: string) => {
      return await backendApiClient.request(`/ai/datasets/${datasetId}/validate`, {
        method: 'POST'
      })
    },
    onSuccess: (result) => {
      toast({
        title: 'Dataset validation completed',
        description: result.isValid ? 'Dataset is valid.' : 'Dataset validation found issues.',
        variant: result.isValid ? 'default' : 'destructive',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error validating dataset',
        description: 'There was a problem validating the dataset. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get dataset statistics
 */
export function useDatasetStats(datasetId: string) {
  return useQuery({
    queryKey: ['dataset', datasetId, 'stats'],
    queryFn: async () => {
      return await backendApiClient.request(`/ai/datasets/${datasetId}/stats`)
    },
    enabled: !!datasetId,
  })
}

/**
 * Hook to export a dataset
 */
export function useExportDataset() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ datasetId, format }: { datasetId: string; format: string }) => {
      return await backendApiClient.request(`/ai/datasets/${datasetId}/export`, {
        method: 'POST',
        body: JSON.stringify({ format })
      })
    },
    onSuccess: (result) => {
      if (result.downloadUrl) {
        window.open(result.downloadUrl, '_blank')
      }
      toast({
        title: 'Export started',
        description: 'Your dataset export is being prepared for download.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error exporting dataset',
        description: 'There was a problem exporting the dataset. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
