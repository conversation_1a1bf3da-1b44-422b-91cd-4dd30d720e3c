/**
 * PKI Management Service
 * Centralized management of PKI providers, certificates, and policies
 */

import {
  PKIProviderRegistry,
  PKIProviderConfig,
  Certificate,
  CertificateStatus,
  CertificateType,
  PKIProviderFactory
} from './pki-provider-interface'
import { DigiCertProvider } from './providers/digicert-provider'
import { DocumentSigningService } from './document-signing-service'
import { logger } from '../../shared/utils/logger'
import { db } from '../../shared/services/database'

export interface OrganizationPKIConfig {
  organizationId: string
  defaultProvider: string
  providers: PKIProviderConfig[]
  certificatePolicies: CertificatePolicy[]
  signingPolicies: SigningPolicy[]
  complianceSettings: ComplianceSettings
  auditSettings: AuditSettings
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

export interface CertificatePolicy {
  id: string
  name: string
  description: string
  certificateType: CertificateType
  validityPeriod: number // months
  keySize: 2048 | 3072 | 4096
  algorithm: 'RSA' | 'ECDSA'
  hashAlgorithm: 'SHA-256' | 'SHA-384' | 'SHA-512'
  autoRenewal: boolean
  renewalThreshold: number // days before expiration
  requiredApprovals: string[] // user roles that must approve
  allowedProviders: string[]
  customAttributes: Record<string, any>
}

export interface SigningPolicy {
  id: string
  name: string
  description: string
  documentTypes: string[]
  requiredSignatureType: 'visual' | 'pki' | 'both'
  requiredComplianceLevel: 'basic' | 'advanced' | 'qualified'
  timestampingRequired: boolean
  witnessRequired: boolean
  approvalWorkflow: ApprovalWorkflow
  retentionPeriod: number // years
  auditLevel: 'basic' | 'detailed' | 'comprehensive'
}

export interface ApprovalWorkflow {
  enabled: boolean
  steps: ApprovalStep[]
  parallelApproval: boolean
  timeoutDays: number
}

export interface ApprovalStep {
  stepNumber: number
  name: string
  requiredRoles: string[]
  requiredUsers?: string[]
  minimumApprovals: number
  autoApprove?: boolean
  conditions?: Record<string, any>
}

export interface ComplianceSettings {
  eidas: {
    enabled: boolean
    qualifiedSignatures: boolean
    timestampingRequired: boolean
    archivalRequired: boolean
    retentionPeriod: number // years
  }
  esignAct: {
    enabled: boolean
    intentToSign: boolean
    signerAuthentication: boolean
    recordRetention: boolean
  }
  cfr21Part11: {
    enabled: boolean
    electronicRecords: boolean
    electronicSignatures: boolean
    auditTrail: boolean
    systemValidation: boolean
  }
  customCompliance: Record<string, any>
}

export interface AuditSettings {
  enabled: boolean
  logLevel: 'basic' | 'detailed' | 'comprehensive'
  retentionPeriod: number // years
  encryptionRequired: boolean
  tamperProtection: boolean
  realTimeMonitoring: boolean
  alerting: {
    enabled: boolean
    events: string[]
    recipients: string[]
    channels: ('email' | 'sms' | 'webhook')[]
  }
}

export interface PKIHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy'
  providers: Array<{
    providerId: string
    status: 'healthy' | 'degraded' | 'unhealthy'
    responseTime: number
    lastCheck: Date
    errors?: string[]
  }>
  certificates: {
    total: number
    active: number
    expiring: number
    expired: number
    revoked: number
  }
  keyVault: {
    status: 'healthy' | 'degraded' | 'unhealthy'
    responseTime: number
    lastCheck: Date
  }
  lastHealthCheck: Date
}

export class PKIManagementService {
  private registry: PKIProviderRegistry
  private documentSigningService: DocumentSigningService
  private initialized: boolean = false

  constructor() {
    this.registry = new PKIProviderRegistry()
    this.documentSigningService = new DocumentSigningService(this.registry)
  }

  async initialize(): Promise<void> {
    try {
      // Register built-in providers
      this.registerBuiltInProviders()
      
      // Initialize document signing service
      await this.documentSigningService.initialize()
      
      this.initialized = true
      logger.info('PKI Management Service initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize PKI Management Service', { error })
      throw new Error('PKI Management Service initialization failed')
    }
  }

  async configureOrganizationPKI(config: OrganizationPKIConfig): Promise<void> {
    try {
      // Validate configuration
      this.validatePKIConfig(config)

      // Add providers to registry
      for (const providerConfig of config.providers) {
        await this.registry.addProvider(providerConfig)
      }

      // Set default provider
      this.registry.setDefaultProvider(config.defaultProvider)

      // Store configuration
      await db.createItem('organization-pki-configs', {
        id: config.organizationId,
        ...config,
        updatedAt: new Date()
      })

      logger.info('Organization PKI configured successfully', {
        organizationId: config.organizationId,
        providerCount: config.providers.length,
        defaultProvider: config.defaultProvider
      })
    } catch (error) {
      logger.error('Failed to configure organization PKI', { error, config })
      throw new Error('PKI configuration failed')
    }
  }

  async getOrganizationPKIConfig(organizationId: string): Promise<OrganizationPKIConfig | null> {
    try {
      const config = await db.readItem('organization-pki-configs', organizationId, 'default')
      return config as OrganizationPKIConfig
    } catch (error) {
      logger.error('Failed to get organization PKI config', { error, organizationId })
      return null
    }
  }

  async listAvailableProviders(): Promise<string[]> {
    return this.registry.listProviders().map(p => p.providerId)
  }

  async addPKIProvider(config: PKIProviderConfig): Promise<void> {
    try {
      await this.registry.addProvider(config)
      logger.info('PKI provider added successfully', { providerId: config.providerId })
    } catch (error) {
      logger.error('Failed to add PKI provider', { error, config })
      throw new Error('PKI provider addition failed')
    }
  }

  async removePKIProvider(providerId: string): Promise<void> {
    try {
      await this.registry.removeProvider(providerId)
      logger.info('PKI provider removed successfully', { providerId })
    } catch (error) {
      logger.error('Failed to remove PKI provider', { error, providerId })
      throw new Error('PKI provider removal failed')
    }
  }

  async getCertificates(
    organizationId: string,
    filters?: {
      status?: CertificateStatus[]
      certificateType?: CertificateType[]
      providerId?: string
      expiringBefore?: Date
    }
  ): Promise<Certificate[]> {
    try {
      const config = await this.getOrganizationPKIConfig(organizationId)
      if (!config) {
        throw new Error('Organization PKI not configured')
      }

      const allCertificates: Certificate[] = []

      // Get certificates from all providers or specific provider
      const providerIds = filters?.providerId ? 
        [filters.providerId] : 
        config.providers.map(p => p.providerId)

      for (const providerId of providerIds) {
        try {
          const provider = this.registry.getProvider(providerId)
          const certificates = await provider.listCertificates(filters)
          
          // Filter by organization
          const orgCertificates = certificates.filter(cert => 
            cert.metadata.organizationId === organizationId
          )
          
          allCertificates.push(...orgCertificates)
        } catch (error) {
          logger.warn('Failed to get certificates from provider', { 
            error, 
            providerId, 
            organizationId 
          })
        }
      }

      return allCertificates
    } catch (error) {
      logger.error('Failed to get certificates', { error, organizationId, filters })
      throw new Error('Certificate retrieval failed')
    }
  }

  async requestCertificate(
    organizationId: string,
    request: {
      commonName: string
      certificateType: CertificateType
      validityPeriod?: number
      providerId?: string
      policyId?: string
      customAttributes?: Record<string, string>
    }
  ): Promise<Certificate> {
    try {
      const config = await this.getOrganizationPKIConfig(organizationId)
      if (!config) {
        throw new Error('Organization PKI not configured')
      }

      // Get certificate policy
      const policy = request.policyId ? 
        config.certificatePolicies.find(p => p.id === request.policyId) :
        config.certificatePolicies.find(p => p.certificateType === request.certificateType)

      if (!policy) {
        throw new Error('No certificate policy found for request')
      }

      // Select provider
      const providerId = request.providerId || config.defaultProvider
      const provider = this.registry.getProvider(providerId)

      // Create certificate request
      const certificateRequest = {
        commonName: request.commonName,
        organizationName: organizationId,
        keyUsage: ['digitalSignature', 'nonRepudiation'],
        extendedKeyUsage: ['1.3.6.1.5.5.7.3.4'], // Document signing
        validityPeriod: request.validityPeriod || policy.validityPeriod,
        certificateType: request.certificateType,
        customAttributes: {
          ...request.customAttributes,
          organizationId,
          policyId: policy.id
        }
      }

      const certificate = await provider.requestCertificate(certificateRequest)

      logger.info('Certificate requested successfully', {
        certificateId: certificate.id,
        organizationId,
        providerId
      })

      return certificate
    } catch (error) {
      logger.error('Failed to request certificate', { error, organizationId, request })
      throw new Error('Certificate request failed')
    }
  }

  async revokeCertificate(
    organizationId: string,
    certificateId: string,
    reason: string
  ): Promise<void> {
    try {
      const config = await this.getOrganizationPKIConfig(organizationId)
      if (!config) {
        throw new Error('Organization PKI not configured')
      }

      // Find certificate and its provider
      let certificate: Certificate | null = null
      let providerId: string | null = null

      for (const providerConfig of config.providers) {
        try {
          const provider = this.registry.getProvider(providerConfig.providerId)
          certificate = await provider.getCertificate(certificateId)
          providerId = providerConfig.providerId
          break
        } catch (error) {
          // Certificate not found in this provider, continue
        }
      }

      if (!certificate || !providerId) {
        throw new Error('Certificate not found')
      }

      // Verify organization ownership
      if (certificate.metadata.organizationId !== organizationId) {
        throw new Error('Certificate does not belong to organization')
      }

      // Revoke certificate
      const provider = this.registry.getProvider(providerId)
      await provider.revokeCertificate({
        certificateId,
        reason: reason as any
      })

      logger.info('Certificate revoked successfully', {
        certificateId,
        organizationId,
        reason
      })
    } catch (error) {
      logger.error('Failed to revoke certificate', { error, organizationId, certificateId })
      throw new Error('Certificate revocation failed')
    }
  }

  async getHealthStatus(): Promise<PKIHealthStatus> {
    try {
      const providers = this.registry.listProviders()
      const providerStatuses = []

      // Check each provider health
      for (const providerConfig of providers) {
        try {
          const provider = this.registry.getProvider(providerConfig.providerId)
          const health = await provider.getHealthStatus()
          providerStatuses.push({
            providerId: providerConfig.providerId,
            ...health
          })
        } catch (error) {
          providerStatuses.push({
            providerId: providerConfig.providerId,
            status: 'unhealthy' as const,
            responseTime: 0,
            lastCheck: new Date(),
            errors: [error instanceof Error ? error.message : 'Unknown error']
          })
        }
      }

      // Check Key Vault health
      const keyVaultHealth = await this.documentSigningService['keyVaultService'].getKeyVaultHealth()

      // Calculate overall status
      const unhealthyProviders = providerStatuses.filter(p => p.status === 'unhealthy').length
      const degradedProviders = providerStatuses.filter(p => p.status === 'degraded').length
      
      let overall: 'healthy' | 'degraded' | 'unhealthy'
      if (unhealthyProviders > 0 || keyVaultHealth.status === 'unhealthy') {
        overall = 'unhealthy'
      } else if (degradedProviders > 0 || keyVaultHealth.status === 'degraded') {
        overall = 'degraded'
      } else {
        overall = 'healthy'
      }

      return {
        overall,
        providers: providerStatuses,
        certificates: {
          total: 0, // Would be calculated from all providers
          active: 0,
          expiring: 0,
          expired: 0,
          revoked: 0
        },
        keyVault: keyVaultHealth,
        lastHealthCheck: new Date()
      }
    } catch (error) {
      logger.error('Failed to get PKI health status', { error })
      throw new Error('Health status check failed')
    }
  }

  getDocumentSigningService(): DocumentSigningService {
    return this.documentSigningService
  }

  private registerBuiltInProviders(): void {
    // Register DigiCert provider
    PKIProviderFactory.registerProvider('digicert', DigiCertProvider as any)
    
    // Additional providers would be registered here
    logger.info('Built-in PKI providers registered')
  }

  private validatePKIConfig(config: OrganizationPKIConfig): void {
    if (!config.organizationId) {
      throw new Error('Organization ID is required')
    }

    if (!config.defaultProvider) {
      throw new Error('Default provider is required')
    }

    if (!config.providers || config.providers.length === 0) {
      throw new Error('At least one PKI provider is required')
    }

    const defaultProviderExists = config.providers.some(p => p.providerId === config.defaultProvider)
    if (!defaultProviderExists) {
      throw new Error('Default provider must be included in providers list')
    }

    // Validate each provider configuration
    for (const provider of config.providers) {
      if (!provider.providerId || !provider.name) {
        throw new Error('Provider ID and name are required')
      }

      if (!provider.credentials.apiKey && !provider.credentials.clientId) {
        throw new Error('Provider credentials are required')
      }
    }
  }
}
