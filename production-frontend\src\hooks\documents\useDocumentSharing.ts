/**
 * Document Sharing Hooks
 * React hooks for document sharing operations
 */

import { useState, useCallback } from 'react'
import { useDocumentStore } from '@/stores/document-store'
import { useToast } from '@/hooks/use-toast'
import backendApiClient from '@/services/backend-api-client'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

export interface DocumentShare {
  id: string
  documentId: string
  sharedBy: string
  sharedWith: string[]
  permissions: ('read' | 'write' | 'comment' | 'download' | 'share')[]
  expiresAt?: string
  message?: string
  isPublic: boolean
  shareLink?: string
  accessCount: number
  lastAccessedAt?: string
  createdAt: string
  updatedAt: string
}

export interface ShareDocumentRequest {
  documentId: string
  emails?: string[]
  permissions: ('read' | 'write' | 'comment' | 'download' | 'share')[]
  expiresAt?: string
  message?: string
  isPublic?: boolean
  requireAuth?: boolean
  allowDownload?: boolean
  allowComments?: boolean
}

export interface UpdateShareRequest {
  permissions?: ('read' | 'write' | 'comment' | 'download' | 'share')[]
  expiresAt?: string
  isPublic?: boolean
  requireAuth?: boolean
  allowDownload?: boolean
  allowComments?: boolean
}

export interface ShareAccessLog {
  id: string
  shareId: string
  accessedBy?: string
  ipAddress: string
  userAgent: string
  accessedAt: string
  action: 'view' | 'download' | 'comment' | 'edit'
}

/**
 * Hook to get document shares
 */
export function useDocumentShares(documentId: string) {
  return useQuery({
    queryKey: ['document-shares', documentId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentShare[]>(`/documents/${documentId}/shares`)
    },
    enabled: !!documentId,
  })
}

/**
 * Hook to get a specific document share
 */
export function useDocumentShare(shareId: string) {
  return useQuery({
    queryKey: ['document-share', shareId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentShare>(`/documents/shares/${shareId}`)
    },
    enabled: !!shareId,
  })
}

/**
 * Hook to share a document
 */
export function useShareDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ShareDocumentRequest) => {
      return await backendApiClient.request<DocumentShare>(`/documents/${data.documentId}/share`, {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (share) => {
      queryClient.invalidateQueries({ queryKey: ['document-shares', share.documentId] })
      queryClient.invalidateQueries({ queryKey: ['document', share.documentId] })
      toast({
        title: 'Document shared',
        description: 'The document has been shared successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error sharing document',
        description: 'There was a problem sharing the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update document share
 */
export function useUpdateDocumentShare() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ shareId, data }: { shareId: string; data: UpdateShareRequest }) => {
      return await backendApiClient.request<DocumentShare>(`/documents/shares/${shareId}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (share) => {
      queryClient.invalidateQueries({ queryKey: ['document-share', share.id] })
      queryClient.invalidateQueries({ queryKey: ['document-shares', share.documentId] })
      toast({
        title: 'Share updated',
        description: 'The document share has been updated successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error updating share',
        description: 'There was a problem updating the document share. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to revoke document share
 */
export function useRevokeDocumentShare() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (shareId: string) => {
      await backendApiClient.request(`/documents/shares/${shareId}`, {
        method: 'DELETE'
      })
      return shareId
    },
    onSuccess: (shareId, variables) => {
      queryClient.removeQueries({ queryKey: ['document-share', shareId] })
      queryClient.invalidateQueries({ queryKey: ['document-shares'] })
      toast({
        title: 'Share revoked',
        description: 'The document share has been revoked successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error revoking share',
        description: 'There was a problem revoking the document share. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get share access logs
 */
export function useShareAccessLogs(shareId: string) {
  return useQuery({
    queryKey: ['share-access-logs', shareId],
    queryFn: async () => {
      return await backendApiClient.request<ShareAccessLog[]>(`/documents/shares/${shareId}/access-logs`)
    },
    enabled: !!shareId,
  })
}

/**
 * Hook to get public share by token
 */
export function usePublicShare(token: string) {
  return useQuery({
    queryKey: ['public-share', token],
    queryFn: async () => {
      return await backendApiClient.request<{
        share: DocumentShare
        document: any
        canAccess: boolean
        requiresAuth: boolean
      }>(`/documents/public/${token}`)
    },
    enabled: !!token,
  })
}

/**
 * Hook to access public document
 */
export function useAccessPublicDocument() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ token, password }: { token: string; password?: string }) => {
      return await backendApiClient.request(`/documents/public/${token}/access`, {
        method: 'POST',
        body: JSON.stringify({ password })
      })
    },
    onSuccess: (result) => {
      if (result.requiresAuth) {
        toast({
          title: 'Authentication required',
          description: 'Please sign in to access this document.',
          variant: 'default',
        })
      }
    },
    onError: (error) => {
      toast({
        title: 'Access denied',
        description: 'You do not have permission to access this document.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to generate share link
 */
export function useGenerateShareLink() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, options }: { 
      documentId: string
      options?: {
        expiresAt?: string
        requireAuth?: boolean
        allowDownload?: boolean
        password?: string
      }
    }) => {
      return await backendApiClient.request(`/documents/${documentId}/share-link`, {
        method: 'POST',
        body: JSON.stringify(options || {})
      })
    },
    onSuccess: (result, { documentId }) => {
      queryClient.invalidateQueries({ queryKey: ['document-shares', documentId] })
      toast({
        title: 'Share link generated',
        description: 'A shareable link has been generated for this document.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error generating link',
        description: 'There was a problem generating the share link. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get share statistics
 */
export function useShareStats(documentId: string) {
  return useQuery({
    queryKey: ['share-stats', documentId],
    queryFn: async () => {
      return await backendApiClient.request(`/documents/${documentId}/share-stats`)
    },
    enabled: !!documentId,
  })
}

/**
 * Main document sharing hook - combines all sharing functionality
 */
export function useDocumentSharing(documentId?: string) {
  const [shareEmail, setShareEmail] = useState('')
  const [shareRole, setShareRole] = useState('viewer')
  const [shareLink, setShareLink] = useState('')
  const [linkCopied, setLinkCopied] = useState(false)

  const shares = useDocumentShares(documentId || '')
  const shareDocument = useShareDocument()
  const updateShare = useUpdateDocumentShare()
  const revokeShare = useRevokeDocumentShare()
  const generateLink = useGenerateShareLink()
  const stats = useShareStats(documentId || '')

  const handleShareWithUser = useCallback(async () => {
    if (!documentId || !shareEmail) return

    const permissions = shareRole === 'viewer' ? ['read'] :
                       shareRole === 'editor' ? ['read', 'write', 'comment'] :
                       ['read', 'write', 'comment', 'download', 'share']

    await shareDocument.mutateAsync({
      documentId,
      emails: [shareEmail],
      permissions: permissions as ('read' | 'write' | 'comment' | 'download' | 'share')[]
    })

    setShareEmail('')
  }, [documentId, shareEmail, shareRole, shareDocument])

  const handleCopyLink = useCallback(async () => {
    if (!documentId) return

    const result = await generateLink.mutateAsync({
      documentId,
      options: {
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        allowDownload: true
      }
    })

    setShareLink(result.url)
    await navigator.clipboard.writeText(result.url)
    setLinkCopied(true)
    setTimeout(() => setLinkCopied(false), 2000)
  }, [documentId, generateLink])

  const updateUserPermission = useCallback(async (shareId: string, role: string) => {
    const permissions = role === 'viewer' ? ['read'] :
                       role === 'editor' ? ['read', 'write', 'comment'] :
                       ['read', 'write', 'comment', 'download', 'share']

    await updateShare.mutateAsync({
      shareId,
      data: {
        permissions: permissions as ('read' | 'write' | 'comment' | 'download' | 'share')[]
      }
    })
  }, [updateShare])

  const removeUser = useCallback(async (shareId: string) => {
    await revokeShare.mutateAsync(shareId)
  }, [revokeShare])

  const updateLinkSettings = useCallback(async (settings: any) => {
    // Implementation for updating link settings
    console.log('Update link settings:', settings)
  }, [])

  return {
    shares: shares.data || [],
    sharedUsers: shares.data || [],
    linkSharingSettings: {
      enabled: false,
      permission: 'view' as 'view' | 'comment' | 'edit'
    },
    isLoading: shares.isLoading,
    error: shares.error,
    shareDocument: shareDocument.mutate,
    updateShare: updateShare.mutate,
    revokeShare: revokeShare.mutate,
    generateLink: generateLink.mutate,
    stats: stats.data,
    isSharing: shareDocument.isPending,
    isUpdating: updateShare.isPending,
    isRevoking: revokeShare.isPending,
    isGenerating: generateLink.isPending,
    isSubmitting: shareDocument.isPending,

    // Form state
    shareEmail,
    setShareEmail,
    shareRole,
    setShareRole,
    shareLink,
    linkCopied,

    // Actions
    handleShareWithUser,
    handleCopyLink,
    updateUserPermission,
    removeUser,
    updateLinkSettings,
  }
}
