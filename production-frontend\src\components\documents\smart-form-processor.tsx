"use client";

import { useState } from "react";
import { useSmartFormProcessing, FormFieldDefinition } from "@/hooks/ai";
import { FormFieldType } from "@/hooks/ai/useSmartFormProcessing";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { FileText, CheckCircle, AlertCircle, Plus, Trash2, Edit, Save, X } from "lucide-react";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorDisplay } from "@/components/ui/error-display";

interface SmartFormProcessorProps {
  documentId: string;
  documentName?: string;
  organizationId?: string;
  projectId?: string;
  onProcessingComplete?: (result: any) => void;
}

export function SmartFormProcessor({
  documentId,
  documentName,
  organizationId,
  projectId,
  onProcessingComplete,
}: SmartFormProcessorProps) {
  const [formType, setFormType] = useState<string>("CUSTOM");
  const [templateId, setTemplateId] = useState<string>("");
  const [autoComplete, setAutoComplete] = useState(true);
  const [validateValues, setValidateValues] = useState(true);
  const [fieldDefinitions, setFieldDefinitions] = useState<FormFieldDefinition[]>([]);
  const [activeTab, setActiveTab] = useState("config");

  // Field editor state
  const [editingField, setEditingField] = useState<FormFieldDefinition | null>(null);
  const [fieldName, setFieldName] = useState("");
  const [fieldType, setFieldType] = useState<FormFieldType>('text' as FormFieldType);
  const [fieldDescription, setFieldDescription] = useState("");
  const [fieldRequired, setFieldRequired] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<any>(null);
  const [isError, setIsError] = useState(false);

  const {
    processDocument,
    isProcessing,
    processingResults,
  } = useSmartFormProcessing();

  // Handle start processing
  const handleStartProcessing = async () => {
    try {
      const result = await processDocument.mutateAsync({
        documentId,
        templateId: templateId || undefined,
        options: {
          formType,
          fieldDefinitions: fieldDefinitions.length > 0 ? fieldDefinitions : undefined,
          autoComplete,
          validateValues,
          organizationId,
          projectId,
        }
      });

      setResult(result);

      if (result && onProcessingComplete) {
        onProcessingComplete(result);
      }

      // Switch to results tab
      setActiveTab("results");
    } catch (error) {
      console.error("Failed to process form", error);
    }
  };

  // Add a new field definition
  const handleAddField = () => {
    if (fieldName.trim()) {
      const newField: FormFieldDefinition = {
        id: `field_${Date.now()}`,
        name: fieldName,
        label: fieldName,
        type: fieldType,
        description: fieldDescription || undefined,
        required: fieldRequired,
      };

      setFieldDefinitions([...fieldDefinitions, newField]);
      resetFieldEditor();
    }
  };

  // Update an existing field
  const handleUpdateField = () => {
    if (editingField && fieldName.trim()) {
      const updatedFields = fieldDefinitions.map(field =>
        field.name === editingField.name
          ? {
              ...field,
              name: fieldName,
              label: fieldName,
              type: fieldType,
              description: fieldDescription || undefined,
              required: fieldRequired,
            }
          : field
      );

      setFieldDefinitions(updatedFields);
      resetFieldEditor();
    }
  };

  // Delete a field
  const handleDeleteField = (fieldName: string) => {
    setFieldDefinitions(fieldDefinitions.filter(field => field.name !== fieldName));
  };

  // Edit a field
  const handleEditField = (field: FormFieldDefinition) => {
    setEditingField(field);
    setFieldName(field.name);
    setFieldType(field.type);
    setFieldDescription(field.description || "");
    setFieldRequired(field.required || false);
  };

  // Reset field editor
  const resetFieldEditor = () => {
    setEditingField(null);
    setFieldName("");
    setFieldType('text' as FormFieldType);
    setFieldDescription("");
    setFieldRequired(false);
  };

  // Render loading state
  if (isProcessing) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Smart Form Processing
          </CardTitle>
          <CardDescription>
            Processing form {documentName || documentId}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <LoadingState
            title="Processing form..."
            description="This may take a few moments depending on the complexity of the form."
            variant="spinner"
          />
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (isError) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Smart Form Processing
          </CardTitle>
          <CardDescription>
            Failed to process form {documentName || documentId}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorDisplay
            title="Processing Failed"
            description="There was an error processing the form."
            error={error}
            variant="card"
          />
        </CardContent>
        <CardFooter>
          <Button onClick={handleStartProcessing} variant="outline">
            Try Again
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="mr-2 h-5 w-5" />
          Smart Form Processing
        </CardTitle>
        <CardDescription>
          {result ? "Form processing results" : `Process form ${documentName || documentId}`}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="config" disabled={!!result}>Configuration</TabsTrigger>
            <TabsTrigger value="fields" disabled={!!result}>Field Definitions</TabsTrigger>
            <TabsTrigger value="results" disabled={!result}>Results</TabsTrigger>
          </TabsList>

          <TabsContent value="config" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="form-type">Form Type</Label>
              <Select
                value={formType}
                onValueChange={setFormType}
              >
                <SelectTrigger id="form-type">
                  <SelectValue placeholder="Select form type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="INVOICE">Invoice</SelectItem>
                  <SelectItem value="RECEIPT">Receipt</SelectItem>
                  <SelectItem value="ID_DOCUMENT">ID Document</SelectItem>
                  <SelectItem value="TAX_FORM">Tax Form</SelectItem>
                  <SelectItem value="MORTGAGE_FORM">Mortgage Form</SelectItem>
                  <SelectItem value="CUSTOM">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="template-id">Template ID (Optional)</Label>
              <Input
                id="template-id"
                value={templateId}
                onChange={(e) => setTemplateId(e.target.value)}
                placeholder="Enter template ID if available"
              />
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Checkbox
                id="auto-complete"
                checked={autoComplete}
                onCheckedChange={(checked) => setAutoComplete(checked as boolean)}
              />
              <Label htmlFor="auto-complete">Auto-complete missing fields</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="validate-values"
                checked={validateValues}
                onCheckedChange={(checked) => setValidateValues(checked as boolean)}
              />
              <Label htmlFor="validate-values">Validate field values</Label>
            </div>
          </TabsContent>

          <TabsContent value="fields" className="space-y-4">
            <div className="space-y-4 border p-4 rounded-md">
              <h3 className="text-sm font-medium">
                {editingField ? "Edit Field" : "Add New Field"}
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="field-name">Field Name</Label>
                  <Input
                    id="field-name"
                    value={fieldName}
                    onChange={(e) => setFieldName(e.target.value)}
                    placeholder="e.g., invoiceNumber"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="field-type">Field Type</Label>
                  <Select
                    value={fieldType}
                    onValueChange={(value) => setFieldType(value as FormFieldType)}
                  >
                    <SelectTrigger id="field-type">
                      <SelectValue placeholder="Select field type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TEXT">Text</SelectItem>
                      <SelectItem value="NUMBER">Number</SelectItem>
                      <SelectItem value="DATE">Date</SelectItem>
                      <SelectItem value="CHECKBOX">Checkbox</SelectItem>
                      <SelectItem value="SELECTION">Selection</SelectItem>
                      <SelectItem value="TABLE">Table</SelectItem>
                      <SelectItem value="SIGNATURE">Signature</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="field-description">Description (Optional)</Label>
                <Input
                  id="field-description"
                  value={fieldDescription}
                  onChange={(e) => setFieldDescription(e.target.value)}
                  placeholder="e.g., The invoice number from the top right corner"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="field-required"
                  checked={fieldRequired}
                  onCheckedChange={(checked) => setFieldRequired(checked as boolean)}
                />
                <Label htmlFor="field-required">Required field</Label>
              </div>

              <div className="flex justify-end space-x-2">
                {editingField ? (
                  <>
                    <Button variant="outline" size="sm" onClick={resetFieldEditor}>
                      <X className="h-4 w-4 mr-1" />
                      Cancel
                    </Button>
                    <Button size="sm" onClick={handleUpdateField}>
                      <Save className="h-4 w-4 mr-1" />
                      Update Field
                    </Button>
                  </>
                ) : (
                  <Button size="sm" onClick={handleAddField}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Field
                  </Button>
                )}
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="text-sm font-medium mb-2">Defined Fields ({fieldDefinitions.length})</h3>

              {fieldDefinitions.length > 0 ? (
                <ScrollArea className="h-[200px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Required</TableHead>
                        <TableHead className="w-[100px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fieldDefinitions.map((field) => (
                        <TableRow key={field.name}>
                          <TableCell className="font-medium">{field.name}</TableCell>
                          <TableCell>{field.type}</TableCell>
                          <TableCell>{field.required ? "Yes" : "No"}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button variant="ghost" size="icon" onClick={() => handleEditField(field)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => handleDeleteField(field.name)}>
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No fields defined yet. Add some fields above.
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="results" className="space-y-4">
            {result ? (
              <>
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium">Form Type: {result.formType}</h3>
                  <Badge variant={result.validationSummary?.isValid ? "success" : "destructive"}>
                    {result.validationSummary?.isValid ? "Valid" : "Invalid"}
                  </Badge>
                </div>

                <ScrollArea className="h-[300px] border rounded-md p-4">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Extracted Fields</h4>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Field</TableHead>
                          <TableHead>Value</TableHead>
                          <TableHead>Confidence</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {result.fields.map((field: any) => (
                          <TableRow key={field.name}>
                            <TableCell className="font-medium">{field.name}</TableCell>
                            <TableCell>{field.value !== null ? String(field.value) : "-"}</TableCell>
                            <TableCell>{Math.round(field.confidence * 100)}%</TableCell>
                            <TableCell>
                              {field.validationResults?.isValid ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>

                    {result.validationSummary?.errors && result.validationSummary.errors.length > 0 && (
                      <div className="mt-4">
                        <h4 className="text-sm font-medium text-destructive">Validation Errors</h4>
                        <ul className="mt-2 space-y-1">
                          {result.validationSummary.errors.map((error: any, index: number) => (
                            <li key={index} className="text-sm text-destructive flex items-start">
                              <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                              <span>
                                <strong>{error.field}:</strong> {error.message}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground">No results available. Process the form first.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        {result ? (
          <>
            <Button variant="outline" onClick={() => {
              setActiveTab("config");
            }}>
              Process Another Form
            </Button>
            <Button onClick={handleStartProcessing}>
              Reprocess Form
            </Button>
          </>
        ) : (
          <Button onClick={handleStartProcessing} disabled={activeTab === "fields" && fieldDefinitions.length === 0}>
            Process Form
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
