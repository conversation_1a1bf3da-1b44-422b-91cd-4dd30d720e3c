/**
 * Comprehensive PKI Service
 * Azure Functions for PKI management, certificate lifecycle, and document signing
 */

import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions'
import Joi from 'joi'
import { logger } from '../shared/utils/logger'
import { db } from '../shared/services/database'
import { azureIdentityService } from '../shared/services/azure-identity'
import { PKIManagementService } from '../services/pki/pki-management-service'
import { DocumentSigningService } from '../services/pki/document-signing-service'

// Global PKI Management Service instance
let pkiManagementService: PKIManagementService | null = null

/**
 * Initialize PKI Management Service
 */
async function initializePKIService(): Promise<PKIManagementService> {
  if (!pkiManagementService) {
    pkiManagementService = new PKIManagementService()
    await pkiManagementService.initialize()
  }
  return pkiManagementService
}

/**
 * Configure Organization PKI
 */
async function configurePKI(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const body = await request.json() as any
    
    // Validate request
    const schema = Joi.object({
      organizationId: Joi.string().required(),
      defaultProvider: Joi.string().required(),
      providers: Joi.array().items(Joi.object({
        providerId: Joi.string().required(),
        name: Joi.string().required(),
        type: Joi.string().valid('cloud', 'enterprise', 'self-signed').required(),
        credentials: Joi.object().required(),
        settings: Joi.object().required(),
        compliance: Joi.object().required(),
        limits: Joi.object().required()
      })).min(1).required(),
      certificatePolicies: Joi.array().items(Joi.object()).default([]),
      signingPolicies: Joi.array().items(Joi.object()).default([]),
      complianceSettings: Joi.object().required(),
      auditSettings: Joi.object().required()
    })

    const { error, value } = schema.validate(body)
    if (error) {
      return {
        status: 400,
        jsonBody: { error: error.details[0].message }
      }
    }

    // Get authenticated user
    const user = (context as any).user
    if (!user) {
      return {
        status: 401,
        jsonBody: { error: 'Authentication required' }
      }
    }

    // Initialize PKI service
    const pkiService = await initializePKIService()

    // Configure organization PKI
    const config = {
      ...value,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: user.id
    }

    await pkiService.configureOrganizationPKI(config)

    logger.info('Organization PKI configured successfully', {
      organizationId: value.organizationId,
      userId: user.id,
      providerCount: value.providers.length
    })

    return {
      status: 200,
      jsonBody: {
        message: 'PKI configuration saved successfully',
        organizationId: value.organizationId,
        providerCount: value.providers.length
      }
    }
  } catch (error) {
    logger.error('PKI configuration failed', { error })
    return {
      status: 500,
      jsonBody: { error: 'PKI configuration failed' }
    }
  }
}

/**
 * Get Organization PKI Configuration
 */
async function getPKIConfiguration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const organizationId = request.params.organizationId
    if (!organizationId) {
      return {
        status: 400,
        jsonBody: { error: 'Organization ID is required' }
      }
    }

    // Get authenticated user
    const user = (context as any).user
    if (!user) {
      return {
        status: 401,
        jsonBody: { error: 'Authentication required' }
      }
    }

    // Initialize PKI service
    const pkiService = await initializePKIService()

    // Get configuration
    const config = await pkiService.getOrganizationPKIConfig(organizationId)
    if (!config) {
      return {
        status: 404,
        jsonBody: { error: 'PKI configuration not found' }
      }
    }

    // Remove sensitive information
    const sanitizedConfig = {
      ...config,
      providers: config.providers.map(p => ({
        ...p,
        credentials: { configured: !!p.credentials.apiKey || !!p.credentials.clientId }
      }))
    }

    return {
      status: 200,
      jsonBody: sanitizedConfig
    }
  } catch (error) {
    logger.error('Failed to get PKI configuration', { error })
    return {
      status: 500,
      jsonBody: { error: 'Failed to get PKI configuration' }
    }
  }
}

/**
 * List Certificates
 */
async function listCertificates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const organizationId = request.params.organizationId
    if (!organizationId) {
      return {
        status: 400,
        jsonBody: { error: 'Organization ID is required' }
      }
    }

    // Get authenticated user
    const user = (context as any).user
    if (!user) {
      return {
        status: 401,
        jsonBody: { error: 'Authentication required' }
      }
    }

    // Parse query parameters
    const url = new URL(request.url)
    const filters = {
      status: url.searchParams.get('status')?.split(',') as any,
      certificateType: url.searchParams.get('certificateType')?.split(',') as any,
      providerId: url.searchParams.get('providerId') || undefined,
      expiringBefore: url.searchParams.get('expiringBefore') ?
        new Date(url.searchParams.get('expiringBefore')!) : undefined
    }

    // Initialize PKI service
    const pkiService = await initializePKIService()

    // Get certificates
    const certificates = await pkiService.getCertificates(organizationId, filters)

    return {
      status: 200,
      jsonBody: {
        certificates,
        total: certificates.length,
        filters
      }
    }
  } catch (error) {
    logger.error('Failed to list certificates', { error })
    return {
      status: 500,
      jsonBody: { error: 'Failed to list certificates' }
    }
  }
}

/**
 * Request Certificate
 */
async function requestCertificate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const organizationId = request.params.organizationId
    if (!organizationId) {
      return {
        status: 400,
        jsonBody: { error: 'Organization ID is required' }
      }
    }

    const body = await request.json() as any
    
    // Validate request
    const schema = Joi.object({
      commonName: Joi.string().required(),
      certificateType: Joi.string().valid('document-signing', 'code-signing', 'ssl-tls').required(),
      validityPeriod: Joi.number().min(1).max(60).optional(),
      providerId: Joi.string().optional(),
      policyId: Joi.string().optional(),
      customAttributes: Joi.object().optional()
    })

    const { error, value } = schema.validate(body)
    if (error) {
      return {
        status: 400,
        jsonBody: { error: error.details[0].message }
      }
    }

    // Get authenticated user
    const user = (context as any).user
    if (!user) {
      return {
        status: 401,
        jsonBody: { error: 'Authentication required' }
      }
    }

    // Initialize PKI service
    const pkiService = await initializePKIService()

    // Request certificate
    const certificate = await pkiService.requestCertificate(organizationId, {
      ...value,
      customAttributes: {
        ...value.customAttributes,
        requestedBy: user.id
      }
    })

    logger.info('Certificate requested successfully', {
      certificateId: certificate.id,
      organizationId,
      userId: user.id
    })

    return {
      status: 200,
      jsonBody: certificate
    }
  } catch (error) {
    logger.error('Certificate request failed', { error })
    return {
      status: 500,
      jsonBody: { error: 'Certificate request failed' }
    }
  }
}

/**
 * Sign Document
 */
async function signDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    const body = await request.json() as any
    
    // Validate request
    const schema = Joi.object({
      documentId: Joi.string().required(),
      documentBuffer: Joi.string().base64().required(), // Base64 encoded document
      documentFormat: Joi.string().valid('pdf', 'docx', 'xlsx', 'pptx').required(),
      signatureType: Joi.string().valid('visual', 'pki', 'both').required(),
      pkiOptions: Joi.object({
        providerId: Joi.string().optional(),
        certificateId: Joi.string().optional(),
        signatureFormat: Joi.string().valid('PKCS7', 'CAdES', 'PAdES', 'XAdES').default('PAdES'),
        timestampingRequired: Joi.boolean().default(true),
        includeSigningCertificate: Joi.boolean().default(true),
        includeCertificateChain: Joi.boolean().default(true),
        complianceLevel: Joi.string().valid('basic', 'advanced', 'qualified').default('advanced')
      }).when('signatureType', {
        is: Joi.string().valid('pki', 'both'),
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      visualOptions: Joi.object({
        signatureImage: Joi.string().base64().required(),
        position: Joi.object({
          page: Joi.number().min(1).required(),
          x: Joi.number().min(0).required(),
          y: Joi.number().min(0).required(),
          width: Joi.number().min(1).required(),
          height: Joi.number().min(1).required(),
          coordinateSystem: Joi.string().valid('bottom-left', 'top-left').default('bottom-left')
        }).required(),
        appearance: Joi.object({
          showSignatureImage: Joi.boolean().default(true),
          showSignerName: Joi.boolean().default(true),
          showSigningTime: Joi.boolean().default(true),
          showReason: Joi.boolean().default(false),
          showLocation: Joi.boolean().default(false),
          showCertificateInfo: Joi.boolean().default(false)
        }).default({}),
        reason: Joi.string().optional(),
        location: Joi.string().optional(),
        contactInfo: Joi.string().optional()
      }).when('signatureType', {
        is: Joi.string().valid('visual', 'both'),
        then: Joi.required(),
        otherwise: Joi.optional()
      }),
      organizationId: Joi.string().required(),
      projectId: Joi.string().optional()
    })

    const { error, value } = schema.validate(body)
    if (error) {
      return {
        status: 400,
        jsonBody: { error: error.details[0].message }
      }
    }

    // Get authenticated user
    const user = (context as any).user
    if (!user) {
      return {
        status: 401,
        jsonBody: { error: 'Authentication required' }
      }
    }

    // Initialize PKI service
    const pkiService = await initializePKIService()
    const documentSigningService = pkiService.getDocumentSigningService()

    // Prepare signing request
    const signingRequest = {
      ...value,
      documentBuffer: Buffer.from(value.documentBuffer, 'base64'),
      userId: user.id,
      visualOptions: value.visualOptions ? {
        ...value.visualOptions,
        signatureImage: Buffer.from(value.visualOptions.signatureImage, 'base64')
      } : undefined
    }

    // Sign document
    const result = await documentSigningService.signDocument(signingRequest)

    logger.info('Document signed successfully', {
      documentId: value.documentId,
      signatureId: result.signatureId,
      signatureType: value.signatureType,
      userId: user.id
    })

    return {
      status: 200,
      jsonBody: {
        signatureId: result.signatureId,
        signedDocument: result.signedDocumentBuffer.toString('base64'),
        signatures: result.signatures,
        metadata: result.metadata
      }
    }
  } catch (error) {
    logger.error('Document signing failed', { error })
    return {
      status: 500,
      jsonBody: { error: 'Document signing failed' }
    }
  }
}

/**
 * Get PKI Health Status
 */
async function getPKIHealth(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Initialize PKI service
    const pkiService = await initializePKIService()

    // Get health status
    const health = await pkiService.getHealthStatus()

    return {
      status: 200,
      jsonBody: health
    }
  } catch (error) {
    logger.error('Failed to get PKI health status', { error })
    return {
      status: 500,
      jsonBody: { 
        error: 'Failed to get PKI health status',
        overall: 'unhealthy',
        lastHealthCheck: new Date()
      }
    }
  }
}

// Register Azure Functions
app.http('configurePKI', {
  methods: ['POST'],
  authLevel: 'anonymous',
  route: 'pki/organizations/{organizationId}/configure',
  handler: configurePKI
})

app.http('getPKIConfiguration', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'pki/organizations/{organizationId}/configuration',
  handler: getPKIConfiguration
})

app.http('listCertificates', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'pki/organizations/{organizationId}/certificates',
  handler: listCertificates
})

app.http('requestCertificate', {
  methods: ['POST'],
  authLevel: 'anonymous',
  route: 'pki/organizations/{organizationId}/certificates',
  handler: requestCertificate
})

app.http('signDocument', {
  methods: ['POST'],
  authLevel: 'anonymous',
  route: 'pki/documents/sign',
  handler: signDocument
})

app.http('getPKIHealth', {
  methods: ['GET'],
  authLevel: 'anonymous',
  route: 'pki/health',
  handler: getPKIHealth
})

export { pkiManagementService }
