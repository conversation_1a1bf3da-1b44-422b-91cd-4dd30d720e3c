/**
 * Workflow Automation Center
 * Advanced workflow management and automation interface
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Workflow, 
  Play, 
  Pause, 
  Square, 
  Settings, 
  Users, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Eye,
  BarChart3
} from 'lucide-react';
import { useWorkflowAutomation } from '@/hooks/workflow/useWorkflowAutomation';
import { WorkflowTemplate, WorkflowExecution } from '@/services/workflow-automation-service';
import { formatDistanceToNow } from 'date-fns';

interface WorkflowAutomationCenterProps {
  projectId: string;
  organizationId: string;
  documentIds?: string[];
}

export function WorkflowAutomationCenter({ 
  projectId, 
  organizationId, 
  documentIds = [] 
}: WorkflowAutomationCenterProps) {
  const [activeTab, setActiveTab] = useState('templates');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>(documentIds);
  const [workflowVariables, setWorkflowVariables] = useState<Record<string, any>>({});

  const {
    templates,
    executions,
    pendingApprovals,
    analytics,
    loadingTemplates,
    loadingExecutions,
    loadingApprovals,
    loadingAnalytics,
    executeWorkflow,
    approveStep,
    cancelExecution,
    refreshTemplates,
    refreshExecutions,
    refreshApprovals,
    refreshAnalytics
  } = useWorkflowAutomation();

  useEffect(() => {
    refreshTemplates();
    refreshExecutions();
    refreshApprovals();
    refreshAnalytics();
  }, [refreshTemplates, refreshExecutions, refreshApprovals, refreshAnalytics]);

  const handleStartWorkflow = async () => {
    if (!selectedTemplate || selectedDocuments.length === 0) return;

    try {
      await executeWorkflow(selectedTemplate, {
        documents: selectedDocuments,
        variables: workflowVariables
      });
      setActiveTab('executions');
    } catch (error) {
      console.error('Failed to start workflow:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const activeExecutions = executions.filter(exec => ['pending', 'running'].includes(exec.status));
  const completedExecutions = executions.filter(exec => exec.status === 'completed');
  const failedExecutions = executions.filter(exec => exec.status === 'failed');

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Workflows</CardTitle>
            <Workflow className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeExecutions.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Users className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingApprovals.length}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting action
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedExecutions.length}</div>
            <p className="text-xs text-muted-foreground">
              Successfully finished
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Efficiency</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.performanceMetrics?.trends?.length
                ? `${Math.round((analytics.performanceMetrics.trends[0]?.successRate || 0) * 100)}%`
                : 'N/A'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Overall efficiency
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="executions">Executions</TabsTrigger>
          <TabsTrigger value="approvals">Approvals</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="new">New Workflow</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Workflow Templates</CardTitle>
                  <CardDescription>
                    Manage and configure workflow templates
                  </CardDescription>
                </div>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  New Template
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {templates.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No workflow templates found
                </div>
              ) : (
                templates.map((template) => (
                  <TemplateCard key={template.id} template={template} />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="executions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Executions</CardTitle>
              <CardDescription>
                Monitor and manage workflow executions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {executions.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No workflow executions found
                </div>
              ) : (
                executions.map((execution) => (
                  <ExecutionCard 
                    key={execution.id} 
                    execution={execution}
                    onCancel={() => cancelExecution(execution.id)}
                  />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approvals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pending Approvals</CardTitle>
              <CardDescription>
                Review and approve pending workflow steps
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {pendingApprovals.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No pending approvals
                </div>
              ) : (
                pendingApprovals.map((approval) => (
                  <ApprovalCard 
                    key={`${approval.executionId}-${approval.id || 'step'}`}
                    approval={approval}
                    onApprove={() => approveStep(approval.executionId, approval.id || 'step')}
                    onReject={() => approveStep(approval.executionId, approval.id || 'step')}
                  />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Analytics</CardTitle>
              <CardDescription>
                Performance metrics and insights
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analytics ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Execution Summary</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Total Executions:</span>
                        <span>{analytics.totalExecutions}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Completed:</span>
                        <span className="text-green-600">{Math.round(analytics.totalExecutions * analytics.successRate)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Failed:</span>
                        <span className="text-red-600">{Math.round(analytics.totalExecutions * (1 - analytics.successRate))}</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Performance</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Avg. Execution Time:</span>
                        <span>{Math.round(analytics.averageExecutionTime / 1000)}s</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Throughput:</span>
                        <span>{analytics.performanceMetrics.trends.length > 0 ? analytics.performanceMetrics.trends[0].executions : 0}/hr</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Efficiency:</span>
                        <span>{Math.round(analytics.successRate * 100)}%</span>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Bottlenecks</h4>
                    <div className="space-y-1 text-sm">
                      {analytics.performanceMetrics.bottlenecks.length === 0 ? (
                        <span className="text-muted-foreground">No bottlenecks detected</span>
                      ) : (
                        analytics.performanceMetrics.bottlenecks.map((bottleneck, index) => (
                          <div key={index} className="text-yellow-600">
                            {bottleneck.stepName} ({bottleneck.averageTime}ms)
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No analytics data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="new" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Start New Workflow</CardTitle>
              <CardDescription>
                Configure and start a new workflow execution
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="template">Workflow Template</Label>
                <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a workflow template" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Documents</Label>
                <div className="text-sm text-muted-foreground">
                  {selectedDocuments.length} documents selected
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="variables">Workflow Variables (JSON)</Label>
                <Textarea
                  id="variables"
                  placeholder='{"priority": "high", "assignee": "<EMAIL>"}'
                  value={JSON.stringify(workflowVariables, null, 2)}
                  onChange={(e) => {
                    try {
                      setWorkflowVariables(JSON.parse(e.target.value || '{}'));
                    } catch {
                      // Invalid JSON, ignore
                    }
                  }}
                />
              </div>

              <Button 
                onClick={handleStartWorkflow}
                disabled={!selectedTemplate || selectedDocuments.length === 0}
                className="w-full"
              >
                <Play className="h-4 w-4 mr-2" />
                Start Workflow
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Component implementations for cards would go here...
function TemplateCard({ template }: { template: WorkflowTemplate }) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div>
        <h4 className="font-medium">{template.name}</h4>
        <p className="text-sm text-muted-foreground">{template.description}</p>
        <div className="flex items-center space-x-2 mt-2">
          <Badge variant="outline">{template.category}</Badge>
          <Badge variant="default">
            Active
          </Badge>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm">
          <Eye className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm">
          <Edit className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

function ExecutionCard({ execution, onCancel }: { execution: WorkflowExecution; onCancel: () => void }) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center space-x-4">
        {getStatusIcon(execution.status)}
        <div>
          <h4 className="font-medium">Execution {execution.id}</h4>
          <p className="text-sm text-muted-foreground">
            {execution.input?.documents?.length || 0} documents • {execution.progress}% complete
          </p>
          <p className="text-xs text-muted-foreground">
            Started {formatDistanceToNow(new Date(execution.startedAt))} ago
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <Progress value={execution.progress} className="w-24 h-2" />
        <Badge className={getStatusColor(execution.status)}>
          {execution.status}
        </Badge>
        {['pending', 'running'].includes(execution.status) && (
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <Square className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

function ApprovalCard({ 
  approval, 
  onApprove, 
  onReject 
}: { 
  approval: any; 
  onApprove: () => void; 
  onReject: () => void; 
}) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div>
        <h4 className="font-medium">{approval.workflowName}</h4>
        <p className="text-sm text-muted-foreground">{approval.documentName}</p>
        <p className="text-xs text-muted-foreground">
          Assigned {formatDistanceToNow(new Date(approval.assignedAt))} ago
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm" onClick={onReject}>
          Reject
        </Button>
        <Button size="sm" onClick={onApprove}>
          Approve
        </Button>
      </div>
    </div>
  );
}
