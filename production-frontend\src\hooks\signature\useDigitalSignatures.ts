/**
 * useDigitalSignatures Hook
 * React hook for managing digital signatures and approvals
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../useAuth';
import { useToast } from '../use-toast';
import { digitalSignatureService } from '../../services/digital-signature-service';
import {
  type DigitalSignature,
  type ApprovalRequest,
  type ApprovalAssignment,
  type ApprovalResponse,
  type SignatureWorkflow,
  SignatureType,
  SignatureStatus,
  ApprovalStatus,
  ApprovalAction,
  ApprovalPriority,
  ComplianceLevel,
  type SignatureData,
  type SignaturePosition,
  type BiometricData,
  type CertificateInfo,
  type ValidationResult
} from '../../services/digital-signature-service';

interface UseDigitalSignaturesOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealTimeUpdates?: boolean;
}

interface SignatureFilters {
  documentId?: string;
  signerId?: string;
  status?: SignatureStatus;
  signatureType?: SignatureType;
  complianceLevel?: ComplianceLevel;
}

interface ApprovalFilters {
  priority?: ApprovalPriority;
  status?: ApprovalStatus;
  department?: string;
  assignedToMe?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
}

export function useDigitalSignatures(options: UseDigitalSignaturesOptions = {}) {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // State management
  const [signatures, setSignatures] = useState<DigitalSignature[]>([]);
  const [approvals, setApprovals] = useState<ApprovalRequest[]>([]);
  const [pendingApprovals, setPendingApprovals] = useState<ApprovalRequest[]>([]);
  const [signatureWorkflows, setSignatureWorkflows] = useState<SignatureWorkflow[]>([]);
  const [selectedSignature, setSelectedSignature] = useState<DigitalSignature | null>(null);
  const [selectedApproval, setSelectedApproval] = useState<ApprovalRequest | null>(null);
  
  // Loading states
  const [loadingSignatures, setLoadingSignatures] = useState(false);
  const [loadingApprovals, setLoadingApprovals] = useState(false);
  const [loadingWorkflows, setLoadingWorkflows] = useState(false);
  const [loadingSignatureCreation, setLoadingSignatureCreation] = useState(false);
  const [loadingSignatureApplication, setLoadingSignatureApplication] = useState(false);
  const [loadingApprovalResponse, setLoadingApprovalResponse] = useState(false);
  const [loadingValidation, setLoadingValidation] = useState(false);
  
  // Filter states
  const [signatureFilters, setSignatureFilters] = useState<SignatureFilters>({});
  const [approvalFilters, setApprovalFilters] = useState<ApprovalFilters>({});
  
  // Pagination states
  const [approvalPagination, setApprovalPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false
  });
  
  // Error state
  const [error, setError] = useState<string | null>(null);

  /**
   * Load document signatures
   */
  const loadDocumentSignatures = useCallback(async (documentId: string) => {
    if (!user) return;

    try {
      setLoadingSignatures(true);
      setError(null);

      const signatures = await digitalSignatureService.getDocumentSignatures(documentId);
      setSignatures(signatures);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load signatures';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
    } finally {
      setLoadingSignatures(false);
    }
  }, [user, toast]);

  /**
   * Load pending approvals
   */
  const loadPendingApprovals = useCallback(async (filters: ApprovalFilters = {}, reset = false) => {
    if (!user) return;

    try {
      setLoadingApprovals(true);
      setError(null);

      const offset = reset ? 0 : pendingApprovals.length;
      const result = await digitalSignatureService.getPendingApprovals({
        ...filters,
        limit: approvalPagination.limit,
        offset
      });
      
      if (reset) {
        setPendingApprovals(result.approvals);
      } else {
        setPendingApprovals(prev => [...prev, ...result.approvals]);
      }

      setApprovalPagination(prev => ({
        ...prev,
        total: result.total,
        hasMore: result.hasMore
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load pending approvals';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
    } finally {
      setLoadingApprovals(false);
    }
  }, [user, toast, pendingApprovals.length, approvalPagination.limit]);

  /**
   * Load signature workflows
   */
  const loadSignatureWorkflows = useCallback(async (filters?: {
    documentType?: string;
    isActive?: boolean;
  }) => {
    if (!user) return;

    try {
      setLoadingWorkflows(true);
      setError(null);

      const workflows = await digitalSignatureService.getSignatureWorkflows(filters);
      setSignatureWorkflows(workflows);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load signature workflows';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
    } finally {
      setLoadingWorkflows(false);
    }
  }, [user, toast]);

  /**
   * Create signature request
   */
  const createSignatureRequest = useCallback(async (requestData: {
    documentId: string;
    workflowExecutionId?: string;
    signatureType?: SignatureType;
    position: SignaturePosition;
    complianceLevel?: ComplianceLevel;
    message?: string;
    dueDate?: string;
  }) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoadingSignatureCreation(true);
      setError(null);

      const result = await digitalSignatureService.createSignatureRequest(requestData);
      
      // Refresh signatures for the document
      if (requestData.documentId) {
        await loadDocumentSignatures(requestData.documentId);
      }

      toast({
        title: 'Success',
        description: result.message
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create signature request';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    } finally {
      setLoadingSignatureCreation(false);
    }
  }, [user, toast, loadDocumentSignatures]);

  /**
   * Apply digital signature
   */
  const applySignature = useCallback(async (signatureId: string, signatureData: {
    signatureData: SignatureData;
    pin?: string;
    biometricData?: BiometricData;
    certificateInfo?: CertificateInfo;
  }) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoadingSignatureApplication(true);
      setError(null);

      const result = await digitalSignatureService.applyDigitalSignature(signatureId, signatureData);
      
      // Update the signature in the list
      setSignatures(prev => prev.map(sig => 
        sig.id === signatureId 
          ? { ...sig, status: SignatureStatus.COMPLETED, timestamp: new Date().toISOString() }
          : sig
      ));

      toast({
        title: 'Success',
        description: result.message
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to apply signature';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    } finally {
      setLoadingSignatureApplication(false);
    }
  }, [user, toast]);

  /**
   * Respond to approval request
   */
  const respondToApproval = useCallback(async (approvalId: string, response: {
    action: ApprovalAction;
    comment?: string;
    conditions?: string[];
    attachments?: string[];
    signatureRequired?: boolean;
    signatureData?: SignatureData;
  }) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoadingApprovalResponse(true);
      setError(null);

      const result = await digitalSignatureService.respondToApproval(approvalId, response);
      
      // Update the approval in the list
      setPendingApprovals(prev => prev.map(approval => 
        approval.id === approvalId 
          ? { 
              ...approval, 
              status: response.action === ApprovalAction.APPROVE ? ApprovalStatus.APPROVED : 
                      response.action === ApprovalAction.REJECT ? ApprovalStatus.REJECTED : 
                      approval.status,
              updatedAt: new Date().toISOString()
            }
          : approval
      ));

      toast({
        title: 'Success',
        description: result.message
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to respond to approval';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    } finally {
      setLoadingApprovalResponse(false);
    }
  }, [user, toast]);

  /**
   * Delegate approval
   */
  const delegateApproval = useCallback(async (approvalId: string, delegateToUserId: string, reason?: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);

      const result = await digitalSignatureService.delegateApproval(approvalId, delegateToUserId, reason);
      
      // Update the approval in the list
      setPendingApprovals(prev => prev.map(approval => 
        approval.id === approvalId 
          ? { ...approval, status: ApprovalStatus.DELEGATED, updatedAt: new Date().toISOString() }
          : approval
      ));

      toast({
        title: 'Success',
        description: result.message
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to delegate approval';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    }
  }, [user, toast]);

  /**
   * Escalate approval
   */
  const escalateApproval = useCallback(async (approvalId: string, reason: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);

      const result = await digitalSignatureService.escalateApproval(approvalId, reason);
      
      // Update the approval in the list
      setPendingApprovals(prev => prev.map(approval => 
        approval.id === approvalId 
          ? { ...approval, status: ApprovalStatus.ESCALATED, updatedAt: new Date().toISOString() }
          : approval
      ));

      toast({
        title: 'Success',
        description: result.message
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to escalate approval';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    }
  }, [user, toast]);

  /**
   * Validate signature
   */
  const validateSignature = useCallback(async (signatureId: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoadingValidation(true);
      setError(null);

      const result = await digitalSignatureService.validateSignature(signatureId);
      
      toast({
        title: result.isValid ? 'Valid Signature' : 'Invalid Signature',
        description: result.isValid 
          ? 'Signature validation passed' 
          : 'Signature validation failed',
        variant: result.isValid ? 'default' : 'destructive'
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to validate signature';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    } finally {
      setLoadingValidation(false);
    }
  }, [user, toast]);

  /**
   * Filter approvals
   */
  const filterApprovals = useCallback((filters: ApprovalFilters) => {
    setApprovalFilters(filters);
    setApprovalPagination(prev => ({ ...prev, page: 1 }));
    loadPendingApprovals(filters, true);
  }, [loadPendingApprovals]);

  /**
   * Load more approvals
   */
  const loadMoreApprovals = useCallback(() => {
    if (!loadingApprovals && approvalPagination.hasMore) {
      setApprovalPagination(prev => ({ ...prev, page: prev.page + 1 }));
      loadPendingApprovals(approvalFilters, false);
    }
  }, [loadingApprovals, approvalPagination.hasMore, loadPendingApprovals, approvalFilters]);

  /**
   * Refresh all data
   */
  const refreshAll = useCallback(async () => {
    await Promise.all([
      loadPendingApprovals(approvalFilters, true),
      loadSignatureWorkflows()
    ]);
  }, [loadPendingApprovals, loadSignatureWorkflows, approvalFilters]);

  /**
   * Clear cache
   */
  const clearCache = useCallback(() => {
    digitalSignatureService.clearCache();
  }, []);

  // Computed values
  const approvalsByPriority = useMemo(() => {
    return pendingApprovals.reduce((acc, approval) => {
      const priority = approval.priority;
      if (!acc[priority]) {
        acc[priority] = [];
      }
      acc[priority].push(approval);
      return acc;
    }, {} as Record<ApprovalPriority, ApprovalRequest[]>);
  }, [pendingApprovals]);

  const approvalsByStatus = useMemo(() => {
    return pendingApprovals.reduce((acc, approval) => {
      const status = approval.status;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(approval);
      return acc;
    }, {} as Record<ApprovalStatus, ApprovalRequest[]>);
  }, [pendingApprovals]);

  const signaturesByStatus = useMemo(() => {
    return signatures.reduce((acc, signature) => {
      const status = signature.status;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(signature);
      return acc;
    }, {} as Record<SignatureStatus, DigitalSignature[]>);
  }, [signatures]);

  const urgentApprovals = useMemo(() => {
    return pendingApprovals.filter(approval => 
      approval.priority === ApprovalPriority.URGENT || 
      approval.priority === ApprovalPriority.CRITICAL
    );
  }, [pendingApprovals]);

  const overdueApprovals = useMemo(() => {
    const now = new Date();
    return pendingApprovals.filter(approval => 
      new Date(approval.dueDate) < now && 
      approval.status === ApprovalStatus.PENDING
    );
  }, [pendingApprovals]);

  // Initial load
  useEffect(() => {
    if (user) {
      loadPendingApprovals(approvalFilters, true);
      loadSignatureWorkflows();
    }
  }, [user]); // Only depend on user to avoid infinite loops

  // Auto-refresh
  useEffect(() => {
    if (options.autoRefresh && user) {
      const interval = setInterval(() => {
        refreshAll();
      }, options.refreshInterval || 30000); // Default 30 seconds

      return () => clearInterval(interval);
    }
  }, [options.autoRefresh, options.refreshInterval, user, refreshAll]);

  return {
    // Data
    signatures,
    approvals,
    pendingApprovals,
    signatureWorkflows,
    selectedSignature,
    selectedApproval,
    
    // Computed data
    approvalsByPriority,
    approvalsByStatus,
    signaturesByStatus,
    urgentApprovals,
    overdueApprovals,
    
    // Loading states
    loadingSignatures,
    loadingApprovals,
    loadingWorkflows,
    loadingSignatureCreation,
    loadingSignatureApplication,
    loadingApprovalResponse,
    loadingValidation,
    
    // Filters
    signatureFilters,
    approvalFilters,
    
    // Pagination
    approvalPagination,
    
    // Error state
    error,
    
    // Actions
    createSignatureRequest,
    applySignature,
    respondToApproval,
    delegateApproval,
    escalateApproval,
    validateSignature,
    filterApprovals,
    loadMoreApprovals,
    refreshAll,
    clearCache,
    
    // Selection
    setSelectedSignature,
    setSelectedApproval,
    
    // Data loading
    loadDocumentSignatures,
    loadPendingApprovals,
    loadSignatureWorkflows
  };
}
