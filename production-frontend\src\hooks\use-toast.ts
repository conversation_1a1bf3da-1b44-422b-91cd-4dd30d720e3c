/**
 * Toast Hook
 * Provides toast notification functionality
 */

import { useCallback, useEffect, useState } from 'react'
import { generateId } from '../lib/utils'

export interface Toast {
  id: string
  title?: string
  description?: string
  type: 'default' | 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
  onDismiss?: () => void
}

export interface ToastOptions {
  title?: string
  description?: string
  type?: Toast['type']
  variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info'
  duration?: number
  action?: Toast['action']
  onDismiss?: () => void
}

interface ToastState {
  toasts: Toast[]
}

// Global toast state
let toastState: ToastState = {
  toasts: []
}

// Listeners for toast updates
const listeners = new Set<(state: ToastState) => void>()

// Notify all listeners of state changes
function notifyListeners() {
  listeners.forEach(listener => listener(toastState))
}

// Add a toast
function addToast(toast: Omit<Toast, 'id'> | ToastOptions): string {
  const id = generateId()
  const newToast: Toast = {
    id,
    type: 'type' in toast ? toast.type || 'default' : 'default',
    duration: 5000,
    ...toast,
  }

  toastState = {
    ...toastState,
    toasts: [...toastState.toasts, newToast]
  }

  notifyListeners()

  // Auto-dismiss after duration
  if (newToast.duration && newToast.duration > 0) {
    setTimeout(() => {
      dismissToast(id)
    }, newToast.duration)
  }

  return id
}

// Remove a toast
function dismissToast(id: string) {
  const toast = toastState.toasts.find(t => t.id === id)
  
  toastState = {
    ...toastState,
    toasts: toastState.toasts.filter(t => t.id !== id)
  }

  notifyListeners()

  // Call onDismiss callback if provided
  if (toast?.onDismiss) {
    toast.onDismiss()
  }
}

// Clear all toasts
function clearToasts() {
  toastState = {
    ...toastState,
    toasts: []
  }
  notifyListeners()
}

// Update a toast
function updateToast(id: string, updates: Partial<Toast>) {
  toastState = {
    ...toastState,
    toasts: toastState.toasts.map(toast =>
      toast.id === id ? { ...toast, ...updates } : toast
    )
  }
  notifyListeners()
}

/**
 * Hook for managing toasts
 */
export function useToast() {
  const [state, setState] = useState<ToastState>(toastState)

  useEffect(() => {
    listeners.add(setState)
    return () => {
      listeners.delete(setState)
    }
  }, [])

  // Toast creation methods
  const toast = useCallback((options: ToastOptions | string) => {
    if (typeof options === 'string') {
      return addToast({
        description: options,
        type: 'default'
      })
    }
    return addToast(options)
  }, [])

  const success = useCallback((options: ToastOptions | string) => {
    if (typeof options === 'string') {
      return addToast({
        description: options,
        type: 'success'
      })
    }
    return addToast({ ...options, type: 'success' })
  }, [])

  const error = useCallback((options: ToastOptions | string) => {
    if (typeof options === 'string') {
      return addToast({
        description: options,
        type: 'error',
        duration: 7000 // Longer duration for errors
      })
    }
    return addToast({ 
      ...options, 
      type: 'error',
      duration: options.duration || 7000
    })
  }, [])

  const warning = useCallback((options: ToastOptions | string) => {
    if (typeof options === 'string') {
      return addToast({
        description: options,
        type: 'warning'
      })
    }
    return addToast({ ...options, type: 'warning' })
  }, [])

  const info = useCallback((options: ToastOptions | string) => {
    if (typeof options === 'string') {
      return addToast({
        description: options,
        type: 'info'
      })
    }
    return addToast({ ...options, type: 'info' })
  }, [])

  // Promise-based toast for async operations
  const promise = useCallback(<T,>(
    promise: Promise<T>,
    options: {
      loading?: ToastOptions | string
      success?: ToastOptions | string | ((data: T) => ToastOptions | string)
      error?: ToastOptions | string | ((error: any) => ToastOptions | string)
    }
  ) => {
    let loadingToastId: string | undefined

    // Show loading toast
    if (options.loading) {
      if (typeof options.loading === 'string') {
        loadingToastId = addToast({
          description: options.loading,
          type: 'info',
          duration: 0 // Don't auto-dismiss
        })
      } else {
        loadingToastId = addToast({
          ...options.loading,
          type: 'info',
          duration: 0
        })
      }
    }

    return promise
      .then((data) => {
        // Dismiss loading toast
        if (loadingToastId) {
          dismissToast(loadingToastId)
        }

        // Show success toast
        if (options.success) {
          const successOptions = typeof options.success === 'function' 
            ? options.success(data) 
            : options.success

          if (typeof successOptions === 'string') {
            success(successOptions)
          } else {
            success(successOptions)
          }
        }

        return data
      })
      .catch((err) => {
        // Dismiss loading toast
        if (loadingToastId) {
          dismissToast(loadingToastId)
        }

        // Show error toast
        if (options.error) {
          const errorOptions = typeof options.error === 'function' 
            ? options.error(err) 
            : options.error

          if (typeof errorOptions === 'string') {
            error(errorOptions)
          } else {
            error(errorOptions)
          }
        }

        throw err
      })
  }, [success, error])

  const dismiss = useCallback((id: string) => {
    dismissToast(id)
  }, [])

  const clear = useCallback(() => {
    clearToasts()
  }, [])

  const update = useCallback((id: string, updates: Partial<Toast>) => {
    updateToast(id, updates)
  }, [])

  return {
    toasts: state.toasts,
    toast,
    success,
    error,
    warning,
    info,
    promise,
    dismiss,
    clear,
    update,
  }
}

// Utility functions for common toast patterns
export const toastUtils = {
  // API error handler
  apiError: (error: any) => {
    const message = error?.response?.data?.message || 
                   error?.message || 
                   'An unexpected error occurred'
    
    return addToast({
      title: 'Error',
      description: message,
      type: 'error',
      duration: 7000
    })
  },

  // Success with action
  successWithAction: (message: string, actionLabel: string, actionFn: () => void) => {
    return addToast({
      description: message,
      type: 'success',
      action: {
        label: actionLabel,
        onClick: actionFn
      }
    })
  },

  // Confirmation toast
  confirm: (message: string, onConfirm: () => void, onCancel?: () => void) => {
    return addToast({
      description: message,
      type: 'warning',
      duration: 0, // Don't auto-dismiss
      action: {
        label: 'Confirm',
        onClick: () => {
          onConfirm()
          // Toast will be dismissed automatically
        }
      },
      onDismiss: onCancel
    })
  },

  // Loading toast that can be updated
  loading: (message: string) => {
    const id = addToast({
      description: message,
      type: 'info',
      duration: 0
    })

    return {
      id,
      success: (successMessage: string) => {
        updateToast(id, {
          description: successMessage,
          type: 'success',
          duration: 5000
        })
      },
      error: (errorMessage: string) => {
        updateToast(id, {
          description: errorMessage,
          type: 'error',
          duration: 7000
        })
      },
      update: (newMessage: string) => {
        updateToast(id, {
          description: newMessage
        })
      },
      dismiss: () => {
        dismissToast(id)
      }
    }
  }
}
