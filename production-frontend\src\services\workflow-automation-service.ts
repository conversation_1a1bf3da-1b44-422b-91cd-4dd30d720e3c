/**
 * Workflow Automation Service
 * Handles workflow execution and automation
 */

import { backendApiClient } from './backend-api-client'
import type { ID } from '../types'

export interface WorkflowTrigger {
  id: ID
  workflowId: ID
  type: 'manual' | 'scheduled' | 'event' | 'webhook' | 'api'
  config: {
    schedule?: string // cron expression
    event?: string
    webhookUrl?: string
    conditions?: Array<{
      field: string
      operator: string
      value: any
    }>
  }
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ExecuteWorkflowRequest {
  workflowId: ID
  input?: Record<string, any>
  context?: {
    userId?: ID
    organizationId?: ID
    projectId?: ID
    documentId?: ID
  }
  options?: {
    async?: boolean
    timeout?: number
    retryOnFailure?: boolean
    maxRetries?: number
  }
}

export interface WorkflowExecutionResult {
  executionId: ID
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  result?: any
  error?: string
  startedAt: string
  completedAt?: string
  duration?: number
  steps: Array<{
    stepId: ID
    status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
    result?: any
    error?: string
    startedAt?: string
    completedAt?: string
    duration?: number
  }>
}

export interface ScheduleWorkflowRequest {
  workflowId: ID
  schedule: string // cron expression
  input?: Record<string, any>
  context?: Record<string, any>
  isActive?: boolean
}

export interface WorkflowSchedule {
  id: ID
  workflowId: ID
  schedule: string
  input?: Record<string, any>
  context?: Record<string, any>
  isActive: boolean
  nextRunAt?: string
  lastRunAt?: string
  lastRunStatus?: string
  createdAt: string
  updatedAt: string
}

export interface WorkflowMetrics {
  workflowId: ID
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageDuration: number
  lastExecutionAt?: string
  executionHistory: Array<{
    date: string
    executions: number
    successes: number
    failures: number
    averageDuration: number
  }>
}

class WorkflowAutomationService {
  /**
   * Execute a workflow
   */
  async executeWorkflow(request: ExecuteWorkflowRequest): Promise<WorkflowExecutionResult> {
    return await backendApiClient.request('/workflows/execute', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Get workflow execution status
   */
  async getExecutionStatus(executionId: ID): Promise<WorkflowExecutionResult> {
    return await backendApiClient.request(`/workflows/executions/${executionId}`)
  }

  /**
   * Cancel workflow execution
   */
  async cancelExecution(executionId: ID): Promise<void> {
    await backendApiClient.request(`/workflows/executions/${executionId}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Get workflow executions
   */
  async getExecutions(
    workflowId?: ID,
    options?: {
      status?: string
      limit?: number
      offset?: number
      startDate?: string
      endDate?: string
    }
  ): Promise<{
    executions: WorkflowExecutionResult[]
    total: number
    hasMore: boolean
  }> {
    const params = new URLSearchParams()
    if (workflowId) params.append('workflowId', workflowId)
    if (options?.status) params.append('status', options.status)
    if (options?.limit) params.append('limit', options.limit.toString())
    if (options?.offset) params.append('offset', options.offset.toString())
    if (options?.startDate) params.append('startDate', options.startDate)
    if (options?.endDate) params.append('endDate', options.endDate)

    return await backendApiClient.request('/workflows/executions', {
      method: 'GET',
      params: Object.fromEntries(params)
    })
  }

  /**
   * Schedule a workflow
   */
  async scheduleWorkflow(request: ScheduleWorkflowRequest): Promise<WorkflowSchedule> {
    return await backendApiClient.request('/workflows/schedule', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Update workflow schedule
   */
  async updateSchedule(scheduleId: ID, updates: Partial<ScheduleWorkflowRequest>): Promise<WorkflowSchedule> {
    return await backendApiClient.request(`/workflows/schedules/${scheduleId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    })
  }

  /**
   * Delete workflow schedule
   */
  async deleteSchedule(scheduleId: ID): Promise<void> {
    await backendApiClient.request(`/workflows/schedules/${scheduleId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Get workflow schedules
   */
  async getSchedules(workflowId?: ID): Promise<WorkflowSchedule[]> {
    const params = workflowId ? { workflowId } : undefined
    return await backendApiClient.request('/workflows/schedules', {
      method: 'GET',
      params
    })
  }

  /**
   * Create workflow trigger
   */
  async createTrigger(trigger: Omit<WorkflowTrigger, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkflowTrigger> {
    return await backendApiClient.request('/workflows/triggers', {
      method: 'POST',
      body: JSON.stringify(trigger)
    })
  }

  /**
   * Update workflow trigger
   */
  async updateTrigger(triggerId: ID, updates: Partial<WorkflowTrigger>): Promise<WorkflowTrigger> {
    return await backendApiClient.request(`/workflows/triggers/${triggerId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    })
  }

  /**
   * Delete workflow trigger
   */
  async deleteTrigger(triggerId: ID): Promise<void> {
    await backendApiClient.request(`/workflows/triggers/${triggerId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Get workflow triggers
   */
  async getTriggers(workflowId?: ID): Promise<WorkflowTrigger[]> {
    const params = workflowId ? { workflowId } : undefined
    return await backendApiClient.request('/workflows/triggers', {
      method: 'GET',
      params
    })
  }

  /**
   * Test workflow step
   */
  async testStep(stepId: ID, input?: Record<string, any>): Promise<{
    success: boolean
    result?: any
    error?: string
    duration: number
  }> {
    return await backendApiClient.request(`/workflows/steps/${stepId}/test`, {
      method: 'POST',
      body: JSON.stringify({ input })
    })
  }

  /**
   * Get workflow metrics
   */
  async getMetrics(workflowId: ID, period?: '24h' | '7d' | '30d' | '90d'): Promise<WorkflowMetrics> {
    const params = period ? { period } : undefined
    return await backendApiClient.request(`/workflows/${workflowId}/metrics`, {
      method: 'GET',
      params
    })
  }

  /**
   * Retry failed execution
   */
  async retryExecution(executionId: ID, fromStep?: ID): Promise<WorkflowExecutionResult> {
    return await backendApiClient.request(`/workflows/executions/${executionId}/retry`, {
      method: 'POST',
      body: JSON.stringify({ fromStep })
    })
  }

  /**
   * Bulk execute workflows
   */
  async bulkExecute(requests: ExecuteWorkflowRequest[]): Promise<WorkflowExecutionResult[]> {
    return await backendApiClient.request('/workflows/bulk-execute', {
      method: 'POST',
      body: JSON.stringify({ requests })
    })
  }

  /**
   * Get workflow execution logs
   */
  async getExecutionLogs(executionId: ID): Promise<Array<{
    timestamp: string
    level: 'info' | 'warn' | 'error' | 'debug'
    message: string
    stepId?: ID
    data?: any
  }>> {
    return await backendApiClient.request(`/workflows/executions/${executionId}/logs`)
  }

  /**
   * Export workflow execution data
   */
  async exportExecutions(
    workflowId: ID,
    format: 'csv' | 'json' | 'xlsx',
    options?: {
      startDate?: string
      endDate?: string
      status?: string
    }
  ): Promise<Blob> {
    try {
      const filters = options || {}
      const response = await backendApiClient.request(`/workflows/${workflowId}/executions/export`, {
        method: 'POST',
        body: JSON.stringify({ format, filters }),
        responseType: 'blob'
      })

      if (response instanceof Blob) {
        return response
      }

      return new Blob([response], {
        type: format === 'csv' ? 'text/csv' :
              format === 'xlsx' ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' :
              'application/json'
      })
    } catch (error) {
      console.error('Workflow execution export failed:', error)
      throw new Error(`Failed to export workflow executions: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
}

export const workflowAutomationService = new WorkflowAutomationService()
export default workflowAutomationService

// Export types for components
export type {
  WorkflowTemplate,
  WorkflowExecution,
  WorkflowAnalytics,
  PendingApproval
} from '../hooks/workflow/useWorkflowAutomation'
