/**
 * Dashboard Store
 * Manages dashboard state, metrics, and widgets
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { storage } from '../lib/utils'
import { backendApiClient } from '../services/backend-api-client'
import type {
  DashboardStore,
  DashboardMetrics,
  DashboardWidget,
  DashboardLayout,
  DashboardFilters,
  ActivityItem,
  PerformanceMetric
} from '../types/store'

interface DashboardStoreState extends DashboardStore {
  _hydrated: boolean
}

// Default dashboard layout
const defaultLayout: DashboardLayout = {
  columns: 12,
  rowHeight: 150,
  margin: [10, 10],
  containerPadding: [20, 20],
}

// Default widgets
const defaultWidgets: DashboardWidget[] = [
  {
    id: 'metrics-overview',
    type: 'metric',
    title: 'Overview Metrics',
    position: { x: 0, y: 0, width: 6, height: 2 },
    config: {
      metrics: ['totalDocuments', 'totalProjects', 'activeWorkflows', 'completedTasks']
    },
    visible: true,
  },
  {
    id: 'recent-activity',
    type: 'activity',
    title: 'Recent Activity',
    position: { x: 6, y: 0, width: 6, height: 4 },
    config: {
      limit: 10,
      showAvatars: true,
    },
    visible: true,
  },
  {
    id: 'performance-chart',
    type: 'chart',
    title: 'Performance Trends',
    position: { x: 0, y: 2, width: 6, height: 3 },
    config: {
      chartType: 'line',
      dataKey: 'performance',
      timeRange: '30d',
    },
    visible: true,
  },
  {
    id: 'project-status',
    type: 'chart',
    title: 'Project Status',
    position: { x: 0, y: 5, width: 4, height: 2 },
    config: {
      chartType: 'pie',
      dataKey: 'projectStatus',
    },
    visible: true,
  },
  {
    id: 'document-types',
    type: 'chart',
    title: 'Document Types',
    position: { x: 4, y: 5, width: 4, height: 2 },
    config: {
      chartType: 'bar',
      dataKey: 'documentTypes',
    },
    visible: true,
  },
  {
    id: 'workflow-status',
    type: 'list',
    title: 'Active Workflows',
    position: { x: 8, y: 4, width: 4, height: 3 },
    config: {
      limit: 5,
      showStatus: true,
    },
    visible: true,
  },
]

// Default filters
const defaultFilters: DashboardFilters = {
  dateRange: {
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
    end: new Date().toISOString(),
  },
}

export const useDashboardStore = create<DashboardStoreState>()(
  persist(
    (set, get) => ({
      // Initial state
      metrics: null,
      widgets: defaultWidgets,
      layout: defaultLayout,
      filters: defaultFilters,
      loading: false,
      error: null,
      lastUpdated: undefined,
      _hydrated: false,

      // Additional state for dashboard page
      organizations: [],
      organizationsLoading: false,
      organizationsError: null,
      projects: [],
      projectsLoading: false,
      projectsError: null,
      documents: [],
      documentsLoading: false,
      documentsError: null,

      // Actions
      fetchMetrics: async (filters?: DashboardFilters) => {
        set({ loading: true, error: null })

        try {
          const currentFilters = filters || get().filters

          // Use backendApiClient for API call
          const data = await backendApiClient.request('/dashboard/metrics', {
            method: 'GET',
          })
          const metrics: DashboardMetrics = data.data

          set({
            metrics,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })
        } catch (error: any) {
          const errorMessage = error.message || 'Failed to fetch dashboard metrics'

          // Handle network errors gracefully
          if (error.code === 'NETWORK_ERROR' || error.code === 'NOT_FOUND' ||
              errorMessage.includes('Network error') || errorMessage.includes('404')) {
            console.warn('Dashboard metrics unavailable - backend not running:', errorMessage)
            set({
              loading: false,
              error: null, // Don't show error for expected network issues
            })
            return // Don't throw for network errors
          }

          set({
            loading: false,
            error: errorMessage,
          })
          throw error
        }
      },

      updateWidget: (widgetId: string, config: Partial<DashboardWidget>) => {
        const { widgets } = get()
        const updatedWidgets = widgets.map(widget =>
          widget.id === widgetId ? { ...widget, ...config } : widget
        )

        set({
          widgets: updatedWidgets,
          lastUpdated: new Date().toISOString(),
        })
      },

      addWidget: (widget: DashboardWidget) => {
        const { widgets } = get()
        
        set({
          widgets: [...widgets, widget],
          lastUpdated: new Date().toISOString(),
        })
      },

      removeWidget: (widgetId: string) => {
        const { widgets } = get()
        const updatedWidgets = widgets.filter(widget => widget.id !== widgetId)

        set({
          widgets: updatedWidgets,
          lastUpdated: new Date().toISOString(),
        })
      },

      updateLayout: (layout: DashboardLayout) => {
        set({
          layout,
          lastUpdated: new Date().toISOString(),
        })
      },

      setFilters: (filters: Partial<DashboardFilters>) => {
        const currentFilters = get().filters
        const updatedFilters = { ...currentFilters, ...filters }

        set({
          filters: updatedFilters,
          lastUpdated: new Date().toISOString(),
        })

        // Skip auto-refresh on filter changes to prevent loops
      },

      refreshData: async () => {
        const { filters } = get()
        await get().fetchMetrics(filters)
      },

      // Additional actions for dashboard page
      fetchOrganizations: async () => {
        set({ organizationsLoading: true, organizationsError: null })
        try {
          // Use backendApiClient for API call
          const data = await backendApiClient.request('/organizations')
          set({ organizations: data.data || [], organizationsLoading: false })
        } catch (error: any) {
          const errorMessage = error.message || 'Failed to fetch organizations'

          // Handle network errors gracefully
          if (error.code === 'NETWORK_ERROR' || error.code === 'NOT_FOUND' ||
              errorMessage.includes('Network error') || errorMessage.includes('404')) {
            console.warn('Organizations unavailable - backend not running:', errorMessage)
            set({ organizationsLoading: false, organizationsError: null })
            return
          }

          set({ organizationsLoading: false, organizationsError: errorMessage })
        }
      },

      fetchProjects: async () => {
        set({ projectsLoading: true, projectsError: null })
        try {
          // Use backendApiClient for API call
          const data = await backendApiClient.request('/projects')
          set({ projects: data.data || [], projectsLoading: false })
        } catch (error: any) {
          const errorMessage = error.message || 'Failed to fetch projects'

          // Handle network errors gracefully
          if (error.code === 'NETWORK_ERROR' || error.code === 'NOT_FOUND' ||
              errorMessage.includes('Network error') || errorMessage.includes('404')) {
            console.warn('Projects unavailable - backend not running:', errorMessage)
            set({ projectsLoading: false, projectsError: null })
            return
          }

          set({ projectsLoading: false, projectsError: errorMessage })
        }
      },

      fetchDocuments: async () => {
        set({ documentsLoading: true, documentsError: null })
        try {
          // Use backendApiClient for API call
          const data = await backendApiClient.request('/documents')
          set({ documents: data.data || [], documentsLoading: false })
        } catch (error: any) {
          set({ documentsLoading: false, documentsError: error.message })
        }
      },

      clearError: () => {
        set({
          error: null,
          organizationsError: null,
          projectsError: null,
          documentsError: null
        })
      },
    }),
    {
      name: 'dashboard-store',
      storage: createJSONStorage(() => ({
        getItem: (name) => storage.get(name, null),
        setItem: (name, value) => storage.set(name, value),
        removeItem: (name) => storage.remove(name),
      })),
      partialize: (state) => ({
        widgets: state.widgets,
        layout: state.layout,
        filters: state.filters,
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true

          // Auto-refresh metrics on rehydration if data is stale
          // But only if user is authenticated to prevent auth loops
          const lastUpdated = state.lastUpdated
          const now = new Date().toISOString()
          const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString()

          // Skip auto-refresh on rehydration to prevent loops
        }
      },
    }
  )
)

// Selector hooks for specific parts of the dashboard state
export const useDashboardMetrics = () => useDashboardStore((state) => state.metrics)
export const useDashboardWidgets = () => useDashboardStore((state) => state.widgets)
export const useDashboardLayout = () => useDashboardStore((state) => state.layout)
export const useDashboardFilters = () => useDashboardStore((state) => state.filters)
export const useDashboardLoading = () => useDashboardStore((state) => state.loading)
export const useDashboardError = () => useDashboardStore((state) => state.error)

// Action hooks
export const useFetchMetrics = () => useDashboardStore((state) => state.fetchMetrics)
export const useUpdateWidget = () => useDashboardStore((state) => state.updateWidget)
export const useAddWidget = () => useDashboardStore((state) => state.addWidget)
export const useRemoveWidget = () => useDashboardStore((state) => state.removeWidget)
export const useUpdateLayout = () => useDashboardStore((state) => state.updateLayout)
export const useSetFilters = () => useDashboardStore((state) => state.setFilters)
export const useRefreshDashboard = () => useDashboardStore((state) => state.refreshData)

// Computed selectors
export const useVisibleWidgets = () => 
  useDashboardStore((state) => state.widgets.filter(widget => widget.visible))

export const useWidgetById = (widgetId: string) =>
  useDashboardStore((state) => state.widgets.find(widget => widget.id === widgetId))

export const useWidgetsByType = (type: string) =>
  useDashboardStore((state) => state.widgets.filter(widget => widget.type === type))

// Dashboard statistics
export const useDashboardStats = () => {
  const metrics = useDashboardMetrics()
  
  if (!metrics) return null

  return {
    totalItems: metrics.totalDocuments + metrics.totalProjects,
    completionRate: metrics.completedTasks > 0 
      ? (metrics.completedTasks / (metrics.completedTasks + metrics.activeWorkflows)) * 100 
      : 0,
    activityCount: metrics.recentActivity?.length || 0,
    performanceScore: metrics.performanceData?.reduce((acc, item) => acc + item.value, 0) || 0,
  }
}

// Widget management utilities
export const useWidgetManager = () => {
  const updateWidget = useUpdateWidget()
  const addWidget = useAddWidget()
  const removeWidget = useRemoveWidget()

  return {
    toggleWidget: (widgetId: string) => {
      const widget = useDashboardStore.getState().widgets.find(w => w.id === widgetId)
      if (widget) {
        updateWidget(widgetId, { visible: !widget.visible })
      }
    },
    
    moveWidget: (widgetId: string, position: DashboardWidget['position']) => {
      updateWidget(widgetId, { position })
    },
    
    resizeWidget: (widgetId: string, size: { width: number; height: number }) => {
      const widget = useDashboardStore.getState().widgets.find(w => w.id === widgetId)
      if (widget) {
        updateWidget(widgetId, {
          position: { ...widget.position, ...size }
        })
      }
    },
    
    duplicateWidget: (widgetId: string) => {
      const widget = useDashboardStore.getState().widgets.find(w => w.id === widgetId)
      if (widget) {
        const newWidget: DashboardWidget = {
          ...widget,
          id: `${widget.id}-copy-${Date.now()}`,
          title: `${widget.title} (Copy)`,
          position: {
            ...widget.position,
            x: widget.position.x + 1,
            y: widget.position.y + 1,
          }
        }
        addWidget(newWidget)
      }
    },
    
    resetToDefaults: () => {
      useDashboardStore.setState({
        widgets: defaultWidgets,
        layout: defaultLayout,
        filters: defaultFilters,
        lastUpdated: new Date().toISOString(),
      })
    },
  }
}
