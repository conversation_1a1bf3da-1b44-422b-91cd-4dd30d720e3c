"use client";

import { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  MoveVertical,
  Trash2,
  ChevronDown,
  ChevronUp,
  UserCheck,
  CheckCircle2,
  FileText,
  Calendar
} from "lucide-react";
import { WorkflowStep } from "@/types/workflow";
import { cn } from "@/lib/utils";

interface SortableWorkflowStepProps {
  step: WorkflowStep;
  onUpdate: (step: WorkflowStep) => void;
  onRemove: (id: string) => void;
}

export function SortableWorkflowStep({
  step,
  onUpdate,
  onRemove
}: SortableWorkflowStepProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: step.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.5 : 1,
  };

  // Get step icon
  const getStepIcon = (type: string) => {
    switch (type) {
      case "APPROVAL":
        return <UserCheck className="h-4 w-4" />;
      case "TASK":
        return <CheckCircle2 className="h-4 w-4" />;
      case "DOCUMENT":
        return <FileText className="h-4 w-4" />;
      default:
        return <CheckCircle2 className="h-4 w-4" />;
    }
  };

  // Update step field
  const updateField = (field: string, value: any) => {
    const updatedStep = {
      ...step,
      [field]: value
    };

    // Add updatedAt if it's supported by the WorkflowStep interface
    if ('updatedAt' in step) {
      (updatedStep as any).updatedAt = new Date().toISOString();
    }

    onUpdate(updatedStep);
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div ref={setNodeRef} style={style} className="touch-none">
      <Card className={cn(
        "w-full transition-shadow",
        isDragging ? "shadow-lg" : ""
      )}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="cursor-grab"
                {...attributes}
                {...listeners}
              >
                <MoveVertical className="h-4 w-4" />
              </Button>

              <CardTitle className="text-base flex items-center gap-2">
                <span className="flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium">
                  {step.order}
                </span>
                <span className="flex items-center gap-1">
                  {getStepIcon(step.type)}
                  {step.name}
                </span>
              </CardTitle>
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onRemove(step.id)}
              >
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={toggleExpanded}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent className="space-y-4 pt-0">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor={`step-name-${step.id}`}>Step Name</Label>
                <Input
                  id={`step-name-${step.id}`}
                  value={step.name}
                  onChange={(e) => updateField("name", e.target.value)}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor={`step-description-${step.id}`}>Description</Label>
                <Textarea
                  id={`step-description-${step.id}`}
                  value={step.description}
                  onChange={(e) => updateField("description", e.target.value)}
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor={`step-type-${step.id}`}>Step Type</Label>
                  <Select
                    value={step.type}
                    onValueChange={(value) => updateField("type", value)}
                  >
                    <SelectTrigger id={`step-type-${step.id}`}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="APPROVAL">Approval</SelectItem>
                      <SelectItem value="TASK">Task</SelectItem>
                      <SelectItem value="DOCUMENT">Document</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor={`step-assignee-${step.id}`}>Assignee</Label>
                  <Input
                    id={`step-assignee-${step.id}`}
                    value={step.assigneeId || ""}
                    onChange={(e) => updateField("assigneeId", e.target.value)}
                    placeholder="User ID or email"
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor={`step-due-date-${step.id}`}>Due Date (Optional)</Label>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <Input
                    id={`step-due-date-${step.id}`}
                    type="date"
                    value={step.dueDate ? new Date(step.dueDate).toISOString().split('T')[0] : ""}
                    onChange={(e) => updateField("dueDate", e.target.value ? new Date(e.target.value).toISOString() : null)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
