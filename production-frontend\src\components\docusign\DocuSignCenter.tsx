/**
 * DocuSign Integration Center
 * Comprehensive interface for document signing and e-signature workflows
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  PenTool, 
  Send, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Users,
  Mail,
  Calendar,
  FileText,
  Download,
  RefreshCw,
  Plus,
  Trash2,
  Eye
} from 'lucide-react';
import { useDocuSign } from '@/hooks/docusign/useDocuSign';
import { formatDistanceToNow } from 'date-fns';

interface DocuSignCenterProps {
  documentIds: string[];
  projectId: string;
  organizationId: string;
}

interface Signer {
  id: string;
  name: string;
  email: string;
  role?: string;
  order?: number;
  required?: boolean;
}

export function DocuSignCenter({ documentIds, projectId, organizationId }: DocuSignCenterProps) {
  const [activeTab, setActiveTab] = useState('send');
  const [signers, setSigners] = useState<Signer[]>([
    { id: '1', name: '', email: '', role: 'Signer', order: 1, required: true }
  ]);
  const [signingOrder, setSigningOrder] = useState<'parallel' | 'sequential'>('parallel');
  const [reminderFrequency, setReminderFrequency] = useState<'daily' | 'weekly' | 'none'>('weekly');
  const [expirationDays, setExpirationDays] = useState(30);
  const [message, setMessage] = useState('');
  const [subject, setSubject] = useState('Please sign this document');

  const {
    envelopes,
    isLoading,
    createEnvelope,
    sendForSigning,
    getEnvelopeStatus,
    downloadSignedDocument,
    sendReminder,
    voidEnvelope,
    fetchEnvelopes
  } = useDocuSign();

  useEffect(() => {
    fetchEnvelopes();
  }, []);

  const addSigner = () => {
    const newSigner: Signer = {
      id: Date.now().toString(),
      name: '',
      email: '',
      role: 'Signer',
      order: signers.length + 1,
      required: true
    };
    setSigners([...signers, newSigner]);
  };

  const removeSigner = (id: string) => {
    setSigners(signers.filter(signer => signer.id !== id));
  };

  const updateSigner = (id: string, updates: Partial<Signer>) => {
    setSigners(signers.map(signer => 
      signer.id === id ? { ...signer, ...updates } : signer
    ));
  };

  const handleSendForSigning = async () => {
    try {
      const validSigners = signers.filter(s => s.name && s.email);
      if (validSigners.length === 0) {
        return;
      }

      // For now, just use the first document ID
      // TODO: Handle multiple documents properly
      const documentId = documentIds[0];

      // Create envelope and get the real envelope ID
      const envelopeData = {
        documentId,
        subject,
        message,
        recipients: validSigners.map(s => ({
          name: s.name,
          email: s.email,
          role: (s.role?.toLowerCase() === 'signer' ? 'signer' :
                s.role?.toLowerCase() === 'cc' ? 'cc' : 'signer') as 'signer' | 'cc' | 'certifiedDelivery',
          routingOrder: s.order || 1
        })),
        organizationId: organizationId || '',
        signingOrder,
        reminderFrequency,
        expirationDays
      };

      // Use the DocuSign API to create the envelope
      const response = await fetch('/api/docusign/envelopes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(envelopeData)
      });

      if (!response.ok) {
        throw new Error(`Failed to create envelope: ${response.statusText}`);
      }

      const envelopeResult = await response.json();
      const envelopeId = envelopeResult.envelopeId;

      if (!envelopeId) {
        throw new Error('No envelope ID returned from DocuSign API');
      }

      // Send for signing using the real envelope ID
      const sendResponse = await fetch(`/api/docusign/envelopes/${envelopeId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message })
      });

      if (!sendResponse.ok) {
        throw new Error(`Failed to send envelope: ${sendResponse.statusText}`);
      }

      // Trigger the hooks to update the UI state
      createEnvelope(envelopeData);
      sendForSigning({
        envelopeId,
        message
      });

      setActiveTab('status');
    } catch (error) {
      console.error('Failed to send for signing:', error);
    }
  };



  const pendingEnvelopes = envelopes.filter(env => ['sent', 'delivered'].includes(env.status));
  const completedEnvelopes = envelopes.filter(env => env.status === 'completed');
  const expiredEnvelopes = envelopes.filter(env => env.status === 'expired');

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Signatures</CardTitle>
            <PenTool className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingEnvelopes.length}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting signatures
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedEnvelopes.length}</div>
            <p className="text-xs text-muted-foreground">
              Fully signed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{expiredEnvelopes.length}</div>
            <p className="text-xs text-muted-foreground">
              Need attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{documentIds.length}</div>
            <p className="text-xs text-muted-foreground">
              Selected for signing
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="send">Send for Signing</TabsTrigger>
          <TabsTrigger value="status">Signing Status</TabsTrigger>
          <TabsTrigger value="completed">Completed</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="send" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configure Signing Request</CardTitle>
              <CardDescription>
                Set up signers and signing options for {documentIds.length} documents
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Email Configuration */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="subject">Email Subject</Label>
                  <Input
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="Please sign this document"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Email Message</Label>
                  <Textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Please review and sign the attached document(s)."
                    rows={3}
                  />
                </div>
              </div>

              {/* Signers Configuration */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Signers</h4>
                  <Button variant="outline" size="sm" onClick={addSigner}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Signer
                  </Button>
                </div>
                
                <div className="space-y-3">
                  {signers.map((signer) => (
                    <div key={signer.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-3">
                        <Input
                          placeholder="Full Name"
                          value={signer.name}
                          onChange={(e) => updateSigner(signer.id, { name: e.target.value })}
                        />
                        <Input
                          type="email"
                          placeholder="Email Address"
                          value={signer.email}
                          onChange={(e) => updateSigner(signer.id, { email: e.target.value })}
                        />
                        <Select
                          value={signer.role}
                          onValueChange={(value) => updateSigner(signer.id, { role: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Signer">Signer</SelectItem>
                            <SelectItem value="Approver">Approver</SelectItem>
                            <SelectItem value="Witness">Witness</SelectItem>
                            <SelectItem value="CC">CC</SelectItem>
                          </SelectContent>
                        </Select>
                        <Input
                          type="number"
                          placeholder="Order"
                          value={signer.order}
                          onChange={(e) => updateSigner(signer.id, { order: parseInt(e.target.value) })}
                          min={1}
                        />
                      </div>
                      {signers.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSigner(signer.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Signing Options */}
              <div className="space-y-4">
                <h4 className="font-medium">Signing Options</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Signing Order</Label>
                    <Select value={signingOrder} onValueChange={(value: any) => setSigningOrder(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="parallel">Parallel (Any Order)</SelectItem>
                        <SelectItem value="sequential">Sequential (Specific Order)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Reminder Frequency</Label>
                    <Select value={reminderFrequency} onValueChange={(value: any) => setReminderFrequency(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="none">No Reminders</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Expiration (Days)</Label>
                    <Input
                      type="number"
                      value={expirationDays}
                      onChange={(e) => setExpirationDays(parseInt(e.target.value))}
                      min={1}
                      max={365}
                    />
                  </div>
                </div>
              </div>

              {/* Send Button */}
              <div className="flex justify-end pt-4">
                <Button 
                  onClick={handleSendForSigning}
                  disabled={isLoading || signers.filter(s => s.name && s.email).length === 0}
                  className="flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>Send for Signing</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Signing Status</CardTitle>
              <CardDescription>
                Monitor the progress of your signing requests
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {pendingEnvelopes.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No pending signatures
                </div>
              ) : (
                pendingEnvelopes.map((envelope) => (
                  <EnvelopeCard 
                    key={envelope.id} 
                    envelope={envelope}
                    onSendReminder={() => sendReminder(envelope.id)}
                    onVoid={() => voidEnvelope(envelope.id)}
                    onRefresh={() => getEnvelopeStatus(envelope.id)}
                  />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completed Signatures</CardTitle>
              <CardDescription>
                View and download completed signed documents
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {completedEnvelopes.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No completed signatures
                </div>
              ) : (
                completedEnvelopes.map((envelope) => (
                  <EnvelopeCard 
                    key={envelope.id} 
                    envelope={envelope}
                    onDownload={() => downloadSignedDocument(envelope.id)}
                    showDownload
                  />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Signing Templates</CardTitle>
              <CardDescription>
                Manage reusable signing templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Signing templates coming soon
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface EnvelopeCardProps {
  envelope: any;
  onSendReminder?: () => void;
  onVoid?: () => void;
  onRefresh?: () => void;
  onDownload?: () => void;
  showDownload?: boolean;
}

// Utility functions moved outside component
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'declined':
    case 'voided':
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    case 'sent':
    case 'delivered':
      return <Mail className="h-4 w-4 text-blue-500" />;
    case 'created':
      return <FileText className="h-4 w-4 text-gray-500" />;
    default:
      return <Clock className="h-4 w-4 text-yellow-500" />;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'declined':
    case 'voided':
      return 'bg-red-100 text-red-800';
    case 'sent':
    case 'delivered':
      return 'bg-blue-100 text-blue-800';
    case 'created':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-yellow-100 text-yellow-800';
  }
};

function EnvelopeCard({
  envelope,
  onSendReminder,
  onVoid,
  onRefresh,
  onDownload,
  showDownload = false
}: EnvelopeCardProps) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center space-x-4">
        {getStatusIcon(envelope.status)}
        <div>
          <h4 className="font-medium">{envelope.subject || 'Document Signing Request'}</h4>
          <p className="text-sm text-muted-foreground">
            {envelope.recipients?.length || 0} recipients •
            {envelope.documentsCount || 0} documents
          </p>
          <p className="text-xs text-muted-foreground">
            Sent {formatDistanceToNow(new Date(envelope.sentDateTime || envelope.createdDateTime))} ago
          </p>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <Badge className={getStatusColor(envelope.status)}>
          {envelope.status}
        </Badge>
        <div className="flex items-center space-x-2">
          {onRefresh && (
            <Button variant="ghost" size="sm" onClick={onRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
          {showDownload && onDownload && (
            <Button variant="ghost" size="sm" onClick={onDownload}>
              <Download className="h-4 w-4" />
            </Button>
          )}
          {onSendReminder && (
            <Button variant="ghost" size="sm" onClick={onSendReminder}>
              <Mail className="h-4 w-4" />
            </Button>
          )}
          {onVoid && (
            <Button variant="ghost" size="sm" onClick={onVoid}>
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
