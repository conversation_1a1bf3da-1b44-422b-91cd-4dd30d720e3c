/**
 * Advanced Collaboration Tools
 * Provides real-time collaboration, presence tracking, document locking, and AI-powered collaboration insights
 * Integrates with SignalR for real-time features and existing document management
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { signalREnhanced } from '../shared/services/signalr';
import { aiServices } from '../shared/services/ai-services';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Collaboration interfaces
interface CollaborationSession {
  id: string;
  documentId: string;
  organizationId: string;
  projectId?: string;
  title: string;
  description?: string;
  type: 'document_editing' | 'review' | 'brainstorming' | 'meeting';
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  participants: CollaborationParticipant[];
  settings: {
    allowAnonymous: boolean;
    requireApproval: boolean;
    maxParticipants: number;
    lockTimeout: number; // minutes
    autoSave: boolean;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  expiresAt?: string;
}

interface CollaborationParticipant {
  userId: string;
  userName: string;
  email: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  status: 'online' | 'offline' | 'away' | 'busy';
  joinedAt: string;
  lastActivity: string;
  permissions: {
    canEdit: boolean;
    canComment: boolean;
    canShare: boolean;
    canLock: boolean;
  };
  presence: {
    cursor?: { line: number; column: number };
    selection?: { start: { line: number; column: number }; end: { line: number; column: number } };
    viewport?: { top: number; bottom: number };
  };
}

interface DocumentLock {
  id: string;
  documentId: string;
  sessionId: string;
  userId: string;
  userName: string;
  type: 'full' | 'section' | 'element';
  scope?: {
    startLine?: number;
    endLine?: number;
    elementId?: string;
  };
  lockedAt: string;
  expiresAt: string;
  reason?: string;
}

interface CollaborationEvent {
  id: string;
  sessionId: string;
  documentId: string;
  userId: string;
  userName: string;
  type: 'join' | 'leave' | 'edit' | 'comment' | 'cursor_move' | 'selection' | 'lock' | 'unlock';
  data: Record<string, any>;
  timestamp: string;
}

interface CollaborationInsight {
  id: string;
  sessionId: string;
  type: 'productivity' | 'engagement' | 'conflict' | 'suggestion';
  title: string;
  description: string;
  confidence: number;
  actionable: boolean;
  recommendations: string[];
  generatedAt: string;
}

class AdvancedCollaborationTools {
  private readonly CACHE_TTL = 1800; // 30 minutes
  private readonly LOCK_TIMEOUT = 300; // 5 minutes default
  private readonly MAX_PARTICIPANTS = 50;

  /**
   * Create a new collaboration session
   */
  async createCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        documentId: Joi.string().uuid().required(),
        organizationId: Joi.string().uuid().required(),
        projectId: Joi.string().uuid().optional(),
        title: Joi.string().min(1).max(200).required(),
        description: Joi.string().max(1000).optional(),
        type: Joi.string().valid('document_editing', 'review', 'brainstorming', 'meeting').default('document_editing'),
        settings: Joi.object({
          allowAnonymous: Joi.boolean().default(false),
          requireApproval: Joi.boolean().default(false),
          maxParticipants: Joi.number().min(1).max(this.MAX_PARTICIPANTS).default(10),
          lockTimeout: Joi.number().min(1).max(60).default(5),
          autoSave: Joi.boolean().default(true)
        }).default({})
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const sessionId = uuidv4();
      const session: CollaborationSession = {
        id: sessionId,
        documentId: value.documentId,
        organizationId: value.organizationId,
        projectId: value.projectId,
        title: value.title,
        description: value.description,
        type: value.type,
        status: 'active',
        participants: [{
          userId: authResult.user.id,
          userName: authResult.user.displayName || authResult.user.email,
          email: authResult.user.email,
          role: 'owner',
          status: 'online',
          joinedAt: new Date().toISOString(),
          lastActivity: new Date().toISOString(),
          permissions: {
            canEdit: true,
            canComment: true,
            canShare: true,
            canLock: true
          },
          presence: {}
        }],
        settings: value.settings,
        createdBy: authResult.user.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      };

      // Store session
      await db.createItem('collaboration-sessions', session);

      // Cache session
      await redis.setex(`collaboration:session:${sessionId}`, this.CACHE_TTL, JSON.stringify(session));

      // Create SignalR group for session
      await signalREnhanced.createGroup(`collaboration:${sessionId}`);
      await signalREnhanced.addUserToGroup(authResult.user.id, `collaboration:${sessionId}`);

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Collaboration.SessionCreated',
        subject: `collaboration/sessions/${sessionId}`,
        data: {
          sessionId,
          documentId: value.documentId,
          organizationId: value.organizationId,
          createdBy: authResult.user.id
        }
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: session
      }, request);

    } catch (error) {
      logger.error('Error creating collaboration session', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Join a collaboration session
   */
  async joinCollaborationSession(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const sessionId = request.params?.sessionId;
      if (!sessionId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Session ID is required' }
        }, request);
      }

      // Get session
      const session = await this.getCollaborationSession(sessionId);
      if (!session) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Collaboration session not found' }
        }, request);
      }

      // Check if user is already a participant
      const existingParticipant = session.participants.find(p => p.userId === authResult.user?.id);
      if (existingParticipant) {
        // Update status to online
        existingParticipant.status = 'online';
        existingParticipant.lastActivity = new Date().toISOString();
      } else {
        // Check participant limit
        if (session.participants.length >= session.settings.maxParticipants) {
          return addCorsHeaders({
            status: 400,
            jsonBody: { error: 'Session has reached maximum participants' }
          }, request);
        }

        // Add new participant
        const newParticipant: CollaborationParticipant = {
          userId: authResult.user?.id || '',
          userName: authResult.user?.displayName || authResult.user?.email || '',
          email: authResult.user?.email || '',
          role: 'editor',
          status: 'online',
          joinedAt: new Date().toISOString(),
          lastActivity: new Date().toISOString(),
          permissions: {
            canEdit: true,
            canComment: true,
            canShare: false,
            canLock: false
          },
          presence: {}
        };

        session.participants.push(newParticipant);
      }

      session.updatedAt = new Date().toISOString();

      // Update session
      await db.upsertItem('collaboration-sessions', session);
      await redis.setex(`collaboration:session:${sessionId}`, this.CACHE_TTL, JSON.stringify(session));

      // Add user to SignalR group
      await signalREnhanced.addUserToGroup(authResult.user?.id || '', `collaboration:${sessionId}`);

      // Broadcast join event
      await this.broadcastCollaborationEvent(sessionId, {
        id: uuidv4(),
        sessionId,
        documentId: session.documentId,
        userId: authResult.user.id,
        userName: authResult.user.displayName || authResult.user.email,
        type: 'join',
        data: {},
        timestamp: new Date().toISOString()
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: session
      }, request);

    } catch (error) {
      logger.error('Error joining collaboration session', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Lock document or section for editing
   */
  async lockDocument(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        documentId: Joi.string().uuid().required(),
        sessionId: Joi.string().uuid().required(),
        type: Joi.string().valid('full', 'section', 'element').default('full'),
        scope: Joi.object({
          startLine: Joi.number().optional(),
          endLine: Joi.number().optional(),
          elementId: Joi.string().optional()
        }).optional(),
        reason: Joi.string().max(200).optional()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Check for existing locks
      const existingLock = await this.getDocumentLock(value.documentId, value.type, value.scope);
      if (existingLock && existingLock.userId !== authResult.user.id) {
        return addCorsHeaders({
          status: 409,
          jsonBody: { 
            error: 'Document is already locked',
            lock: existingLock
          }
        }, request);
      }

      const lockId = uuidv4();
      const lock: DocumentLock = {
        id: lockId,
        documentId: value.documentId,
        sessionId: value.sessionId,
        userId: authResult.user.id,
        userName: authResult.user.displayName || authResult.user.email,
        type: value.type,
        scope: value.scope,
        lockedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + this.LOCK_TIMEOUT * 60 * 1000).toISOString(),
        reason: value.reason
      };

      // Store lock
      await db.createItem('document-locks', lock);
      await redis.setex(`lock:${value.documentId}:${value.type}`, this.LOCK_TIMEOUT * 60, JSON.stringify(lock));

      // Broadcast lock event
      await this.broadcastCollaborationEvent(value.sessionId, {
        id: uuidv4(),
        sessionId: value.sessionId,
        documentId: value.documentId,
        userId: authResult.user.id,
        userName: authResult.user.displayName || authResult.user.email,
        type: 'lock',
        data: { lock },
        timestamp: new Date().toISOString()
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: lock
      }, request);

    } catch (error) {
      logger.error('Error locking document', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Update user presence in collaboration session
   */
  async updatePresence(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        sessionId: Joi.string().uuid().required(),
        presence: Joi.object({
          cursor: Joi.object({
            line: Joi.number().required(),
            column: Joi.number().required()
          }).optional(),
          selection: Joi.object({
            start: Joi.object({
              line: Joi.number().required(),
              column: Joi.number().required()
            }).required(),
            end: Joi.object({
              line: Joi.number().required(),
              column: Joi.number().required()
            }).required()
          }).optional(),
          viewport: Joi.object({
            top: Joi.number().required(),
            bottom: Joi.number().required()
          }).optional()
        }).required()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Update presence in Redis (fast access)
      const presenceKey = `presence:${value.sessionId}:${authResult.user.id}`;
      await redis.setex(presenceKey, 60, JSON.stringify({
        userId: authResult.user.id,
        userName: authResult.user.displayName || authResult.user.email,
        presence: value.presence,
        lastUpdate: new Date().toISOString()
      }));

      // Broadcast presence update
      await signalREnhanced.sendToGroup(`collaboration:${value.sessionId}`, {
        target: 'presence_update',
        arguments: [{
          userId: authResult.user?.id,
          userName: authResult.user?.displayName || authResult.user?.email,
          presence: value.presence
        }]
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: { message: 'Presence updated successfully' }
      }, request);

    } catch (error) {
      logger.error('Error updating presence', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Get AI-powered collaboration insights
   */
  async getCollaborationInsights(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const sessionId = request.params?.sessionId;
      if (!sessionId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Session ID is required' }
        }, request);
      }

      // Get collaboration session and events
      const [session, events] = await Promise.all([
        this.getCollaborationSession(sessionId),
        this.getCollaborationEvents(sessionId, 100)
      ]);

      if (!session) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Collaboration session not found' }
        }, request);
      }

      // Generate AI insights
      const insights = await this.generateCollaborationInsights(session, events);

      return addCorsHeaders({
        status: 200,
        jsonBody: { insights }
      }, request);

    } catch (error) {
      logger.error('Error getting collaboration insights', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Private helper methods
  private async getCollaborationSession(sessionId: string): Promise<CollaborationSession | null> {
    try {
      // Check cache first
      const cached = await redis.get(`collaboration:session:${sessionId}`);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get from database
      const session = await db.readItem('collaboration-sessions', sessionId, sessionId);
      if (session) {
        await redis.setex(`collaboration:session:${sessionId}`, this.CACHE_TTL, JSON.stringify(session));
      }

      return session as CollaborationSession;
    } catch (error) {
      logger.error('Error getting collaboration session', { sessionId, error });
      return null;
    }
  }

  private async getDocumentLock(documentId: string, type: string, scope?: any): Promise<DocumentLock | null> {
    try {
      const cached = await redis.get(`lock:${documentId}:${type}`);
      if (cached) {
        const lock = JSON.parse(cached);
        // Check if lock has expired
        if (new Date(lock.expiresAt) > new Date()) {
          return lock;
        } else {
          await redis.del(`lock:${documentId}:${type}`);
        }
      }
      return null;
    } catch (error) {
      logger.error('Error getting document lock', { documentId, type, error });
      return null;
    }
  }

  private async getCollaborationEvents(sessionId: string, limit: number = 50): Promise<CollaborationEvent[]> {
    try {
      const events = await db.queryItems('collaboration-events',
        'SELECT * FROM c WHERE c.sessionId = @sessionId ORDER BY c.timestamp DESC OFFSET 0 LIMIT @limit',
        [sessionId, limit]
      );
      return events as CollaborationEvent[];
    } catch (error) {
      logger.error('Error getting collaboration events', { sessionId, error });
      return [];
    }
  }

  private async broadcastCollaborationEvent(sessionId: string, event: CollaborationEvent): Promise<void> {
    try {
      // Store event
      await db.createItem('collaboration-events', event);

      // Broadcast via SignalR
      await signalREnhanced.sendToGroup(`collaboration:${sessionId}`, {
        target: 'collaboration_event',
        arguments: [event]
      });
    } catch (error) {
      logger.error('Error broadcasting collaboration event', { sessionId, error });
    }
  }

  private async generateCollaborationInsights(
    session: CollaborationSession,
    events: CollaborationEvent[]
  ): Promise<CollaborationInsight[]> {
    try {
      const prompt = `Analyze this collaboration session and provide insights:

Session Info:
- Type: ${session.type}
- Participants: ${session.participants.length}
- Duration: ${new Date().getTime() - new Date(session.createdAt).getTime()} ms
- Status: ${session.status}

Recent Events: ${JSON.stringify(events.slice(0, 20))}

Provide insights on:
1. Collaboration effectiveness
2. Participant engagement
3. Potential conflicts or issues
4. Productivity suggestions
5. Communication patterns

Return as JSON array with type, title, description, confidence, recommendations.`;

      const aiResponse = await aiServices.reason(prompt, [], {
        temperature: 0.3,
        maxTokens: 1500
      });

      // Parse AI response
      const insights = this.parseCollaborationInsights(aiResponse.content, session.id);
      return insights;

    } catch (error) {
      logger.error('Error generating collaboration insights', { error });
      return this.getFallbackInsights(session);
    }
  }

  private parseCollaborationInsights(aiContent: string, sessionId: string): CollaborationInsight[] {
    try {
      const jsonMatch = aiContent.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('No JSON found in AI response');
      }

      const insights = JSON.parse(jsonMatch[0]);
      return insights.map((insight: any) => ({
        id: uuidv4(),
        sessionId,
        type: insight.type || 'productivity',
        title: insight.title || 'Collaboration Insight',
        description: insight.description || '',
        confidence: insight.confidence || 0.5,
        actionable: true,
        recommendations: insight.recommendations || [],
        generatedAt: new Date().toISOString()
      }));
    } catch (error) {
      logger.error('Error parsing collaboration insights', { error });
      return [];
    }
  }

  private getFallbackInsights(session: CollaborationSession): CollaborationInsight[] {
    return [{
      id: uuidv4(),
      sessionId: session.id,
      type: 'engagement',
      title: 'Active Collaboration',
      description: `${session.participants.length} participants are actively collaborating`,
      confidence: 0.8,
      actionable: false,
      recommendations: ['Continue the current collaboration pace'],
      generatedAt: new Date().toISOString()
    }];
  }
}

// Create instance
const collaborationTools = new AdvancedCollaborationTools();

// Register HTTP functions
app.http('collaboration-create-session', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions',
  handler: (request, context) => collaborationTools.createCollaborationSession(request, context)
});

app.http('collaboration-join-session', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/{sessionId}/join',
  handler: (request, context) => collaborationTools.joinCollaborationSession(request, context)
});

app.http('collaboration-lock-document', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/lock',
  handler: (request, context) => collaborationTools.lockDocument(request, context)
});

app.http('collaboration-update-presence', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/presence',
  handler: (request, context) => collaborationTools.updatePresence(request, context)
});

app.http('collaboration-insights', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/{sessionId}/insights',
  handler: (request, context) => collaborationTools.getCollaborationInsights(request, context)
});
