"use client";

import { useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { useTemplate } from "@/hooks/templates";
import { useUpdateTemplate } from "@/hooks";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft } from "lucide-react";
import { TemplateForm } from "@/components/templates";
import { EmptyState } from "@/components/empty-state";
import { FileStack } from "lucide-react";
import { useOrganizations } from "@/hooks/organizations";
import { UpdateTemplateParams } from "@/services/template-service";

export default function EditTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const { currentOrganization } = useOrganizations();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get template ID from params
  if (!params) {
    return <div>Loading...</div>;
  }

  const templateId = params.id as string;

  // Fetch template data
  const {
    data: template,
    isLoading: isLoadingTemplate,
    error: templateError
  } = useTemplate(templateId);

  // Update template mutation
  const { mutate: updateTemplate } = useUpdateTemplate();

  // Handle form submission
  const handleSubmit = async (values: UpdateTemplateParams) => {
    setIsSubmitting(true);

    try {
      await updateTemplate({ templateId, data: values });
      router.push(`/templates/${templateId}`);
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (isLoadingTemplate) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-8 w-48" />
        </div>
        <Skeleton className="h-[600px] w-full" />
      </div>
    );
  }

  // Error state
  if (templateError || !template) {
    return (
      <EmptyState
        icon={<FileStack className="h-10 w-10 text-muted-foreground" />}
        title="Template not found"
        description="The template you are trying to edit does not exist or you don't have permission to edit it."
        action={
          <Button asChild>
            <Link href="/templates">
              Back to Templates
            </Link>
          </Button>
        }
      />
    );
  }

  // Organization check
  if (!currentOrganization) {
    return (
      <EmptyState
        icon={<FileStack className="h-10 w-10 text-muted-foreground" />}
        title="No organization selected"
        description="Select an organization from the dropdown to edit templates"
        action={
          <Button asChild>
            <Link href="/organizations">
              View Organizations
            </Link>
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" asChild>
          <Link href={`/templates/${templateId}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Edit Template</h1>
          <p className="text-muted-foreground">
            Update the template details and content
          </p>
        </div>
      </div>

      <TemplateForm
        template={template as any}
        organizationId={currentOrganization.id}
        onSubmit={handleSubmit}
        isSubmitting={isSubmitting}
      />
    </div>
  );
}
