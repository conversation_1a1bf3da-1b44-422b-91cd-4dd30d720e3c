'use client';

import React from 'react';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Scatter<PERSON>hart,
  <PERSON>atter,
  ZAxis,
} from 'recharts';
// Import with proper typing
const ForceGraph2D = require('react-force-graph').ForceGraph2D || (() => <div>Force Graph not available</div>);
const HeatMap = require('react-heatmap-grid').default || (() => <div>Heat Map not available</div>);
import ReactWordcloud from 'react-wordcloud';
import { scaleLinear } from 'd3-scale';
import { Visualization } from '@/services/document-analysis-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface DocumentVisualizationProps {
  visualization: Visualization;
}

/**
 * Component for rendering document visualizations
 */
export function DocumentVisualization({ visualization }: DocumentVisualizationProps) {
  const renderVisualization = () => {
    switch (visualization.type) {
      case 'bar':
        return renderBarChart();
      case 'pie':
        return renderPieChart();
      case 'line':
        return renderLineChart();
      case 'scatter':
        return renderScatterChart();
      case 'network':
        return renderNetworkGraph();
      case 'heatmap':
        return renderHeatmap();
      case 'wordcloud':
        return renderWordCloud();
      case 'table':
        return renderTable();
      default:
        return <div className="flex items-center justify-center h-full">Unsupported visualization type</div>;
    }
  };

  const renderBarChart = () => {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={visualization.data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="value" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  const renderPieChart = () => {
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

    return (
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={visualization.data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {visualization.data.map((_: any, index: number) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const renderLineChart = () => {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={visualization.data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Line type="monotone" dataKey="value" stroke="#8884d8" activeDot={{ r: 8 }} />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  const renderScatterChart = () => {
    return (
      <ResponsiveContainer width="100%" height="100%">
        <ScatterChart>
          <CartesianGrid />
          <XAxis dataKey="x" name="x" />
          <YAxis dataKey="y" name="y" />
          <ZAxis dataKey="z" range={[60, 400]} name="score" />
          <Tooltip cursor={{ strokeDasharray: '3 3' }} />
          <Legend />
          <Scatter name="Values" data={visualization.data} fill="#8884d8" />
        </ScatterChart>
      </ResponsiveContainer>
    );
  };

  const renderNetworkGraph = () => {
    // Check if we have valid network data
    if (!visualization.data || !visualization.data.nodes || !visualization.data.links) {
      return (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="text-muted-foreground">Invalid network data format</p>
        </div>
      );
    }

    // Process data for the force graph
    const graphData = {
      nodes: visualization.data.nodes.map((node: any) => ({
        id: node.id,
        name: node.name || node.id,
        val: node.value || 1,
        color: node.color || '#8884d8',
        ...node
      })),
      links: visualization.data.links.map((link: any) => ({
        source: link.source,
        target: link.target,
        value: link.value || 1,
        ...link
      }))
    };

    return (
      <div className="h-full w-full">
        <ForceGraph2D
          graphData={graphData}
          nodeLabel={(node: any) => node.name || node.id}
          nodeColor={(node: any) => node.color}
          nodeVal={(node: any) => node.val}
          linkWidth={(link: any) => Math.sqrt(link.value || 1)}
          linkDirectionalParticles={2}
          linkDirectionalParticleWidth={(link: any) => Math.sqrt(link.value || 1)}
          width={500}
          height={200}
          cooldownTicks={50}
          cooldownTime={2000}
        />
      </div>
    );
  };

  const renderHeatmap = () => {
    // Check if we have valid heatmap data
    if (!visualization.data || !visualization.data.matrix || !visualization.data.xLabels || !visualization.data.yLabels) {
      return (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="text-muted-foreground">Invalid heatmap data format</p>
        </div>
      );
    }

    const { matrix, xLabels, yLabels } = visualization.data;

    // Find min and max values for color scaling
    let min = Infinity;
    let max = -Infinity;

    for (let i = 0; i < matrix.length; i++) {
      for (let j = 0; j < matrix[i].length; j++) {
        if (matrix[i][j] < min) min = matrix[i][j];
        if (matrix[i][j] > max) max = matrix[i][j];
      }
    }

    // Create color scale
    const colorScale = scaleLinear<string>()
      .domain([min, (min + max) / 2, max])
      .range(['#8884d8', '#f5f5f5', '#82ca9d']);

    return (
      <div className="h-full w-full overflow-auto">
        <HeatMap
          xLabels={xLabels}
          yLabels={yLabels}
          data={matrix}
          cellStyle={(_: any, value: any, min: any, max: any) => ({
            background: colorScale(value),
            fontSize: '11px',
            color: value > (min + max) / 2 ? '#000' : '#fff'
          })}
          cellRender={(value: number) => value.toFixed(1)}
          title={(x: number, y: number) => `${yLabels[y]} × ${xLabels[x]}: ${matrix[y][x]}`}
        />
      </div>
    );
  };

  const renderWordCloud = () => {
    // Check if we have valid word cloud data
    if (!visualization.data || !Array.isArray(visualization.data)) {
      return (
        <div className="flex flex-col items-center justify-center h-full">
          <p className="text-muted-foreground">Invalid word cloud data format</p>
        </div>
      );
    }

    // Process data for the word cloud
    const words = visualization.data.map((item: any) => ({
      text: item.text || item.name || '',
      value: item.value || item.count || 1,
      ...item
    }));

    // Word cloud options
    const options = {
      colors: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b'],
      enableTooltip: true,
      deterministic: false,
      fontFamily: 'impact',
      fontSizes: [12, 40] as [number, number],
      fontStyle: 'normal',
      fontWeight: 'normal',
      padding: 1,
      rotations: 3,
      rotationAngles: [0, 90] as [number, number],
      scale: 'sqrt' as any,
      spiral: 'archimedean' as any,
      transitionDuration: 1000
    };

    return (
      <div className="h-full w-full">
        <ReactWordcloud words={words} options={options} />
      </div>
    );
  };

  const renderTable = () => {
    return (
      <div className="overflow-auto h-full">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              {visualization.data.headers.map((header: string, index: number) => (
                <th key={index} className="border p-2 text-left bg-muted">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {visualization.data.rows.map((row: any[], rowIndex: number) => (
              <tr key={rowIndex}>
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="border p-2">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="text-sm">{visualization.title}</CardTitle>
        <CardDescription>{visualization.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[200px]">
          {renderVisualization()}
        </div>
      </CardContent>
    </Card>
  );
}
