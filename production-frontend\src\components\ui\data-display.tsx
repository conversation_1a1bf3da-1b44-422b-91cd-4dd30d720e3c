"use client";

import React from "react";
import { ErrorDisplay, ErrorDisplayProps } from "@/components/ui/error-display";
import { LoadingState, LoadingStateProps } from "@/components/ui/loading-state";
import { cn } from "@/lib/utils";

export interface DataDisplayProps<T> {
  data: T | undefined | null;
  isLoading: boolean;
  isError: boolean;
  error?: any;
  children: (data: T) => React.ReactNode;
  loadingProps?: LoadingStateProps;
  errorProps?: ErrorDisplayProps;
  emptyComponent?: React.ReactNode;
  isEmpty?: (data: T | undefined | null) => boolean;
  className?: string;
  loadingFallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  emptyFallback?: React.ReactNode;
}

/**
 * A reusable component for handling data fetching states
 */
export function DataDisplay<T>({
  data,
  isLoading,
  isError,
  error,
  children,
  loadingProps,
  errorProps,
  emptyComponent,
  isEmpty = (data) => !data || (Array.isArray(data) && data.length === 0),
  className,
  loadingFallback,
  errorFallback,
  emptyFallback,
}: DataDisplayProps<T>) {
  // Loading state
  if (isLoading) {
    return loadingFallback ? (
      <>{loadingFallback}</>
    ) : (
      <div className={cn("w-full", className)}>
        <LoadingState {...loadingProps} />
      </div>
    );
  }

  // Error state
  if (isError) {
    return errorFallback ? (
      <>{errorFallback}</>
    ) : (
      <div className={cn("w-full", className)}>
        <ErrorDisplay
          error={error}
          {...errorProps}
        />
      </div>
    );
  }

  // Empty state
  if (isEmpty(data)) {
    return emptyFallback ? (
      <>{emptyFallback}</>
    ) : (
      <div className={cn("w-full", className)}>
        {emptyComponent || (
          <div className="text-center p-6">
            <p className="text-muted-foreground">No data available</p>
          </div>
        )}
      </div>
    );
  }

  // Data state
  return <>{children(data as T)}</>;
}

/**
 * A reusable component for handling async data fetching states
 */
export function AsyncDataDisplay<T>({
  queryFn,
  children,
  ...props
}: Omit<DataDisplayProps<T>, "data" | "isLoading" | "isError" | "error"> & {
  queryFn: () => { data: T | undefined | null; isLoading: boolean; isError: boolean; error?: any };
}) {
  const { data, isLoading, isError, error } = queryFn();

  return (
    <DataDisplay
      data={data}
      isLoading={isLoading}
      isError={isError}
      error={error}
      {...props}
    >
      {children}
    </DataDisplay>
  );
}
