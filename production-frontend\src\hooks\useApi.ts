import { useState, useEffect, useCallback } from 'react'
import { backendApiClient } from '@/services/backend-api-client'

/**
 * API Hook
 * Manages API calls with loading, error, and data states
 */

export interface UseApiOptions<T> {
  immediate?: boolean
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  dependencies?: any[]
}

export interface UseApiResult<T> {
  data: T | null
  isLoading: boolean
  error: Error | null
  refetch: () => Promise<void>
  reset: () => void
}

export function useApi<T>(
  endpoint: string,
  options: UseApiOptions<T> = {}
): UseApiResult<T> {
  const { immediate = true, onSuccess, onError, dependencies = [] } = options
  
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(immediate)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request<T>(endpoint)
      setData(response)
      onSuccess?.(response)
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      onError?.(error)

      // Log network errors but don't throw to prevent unhandled rejections
      if (error.message.includes('Network error') || error.message.includes('NETWORK_ERROR')) {
        console.warn(`Network error for ${endpoint}:`, error.message)
      }
    } finally {
      setLoading(false)
    }
  }, [endpoint, onSuccess, onError])

  const reset = useCallback(() => {
    setData(null)
    setLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (immediate) {
      fetchData()
    }
  }, [fetchData, immediate, ...dependencies])

  return {
    data,
    isLoading: loading,
    error,
    refetch: fetchData,
    reset,
  }
}

/**
 * API hook with query parameters
 */
export function useApiWithParams<T>(
  endpoint: string,
  params: Record<string, any> = {},
  options: UseApiOptions<T> = {}
): UseApiResult<T> {
  const queryString = new URLSearchParams(params).toString()
  const fullEndpoint = queryString ? `${endpoint}?${queryString}` : endpoint

  return useApi<T>(fullEndpoint, options)
}

// Re-export useMutation from React Query for convenience
export { useMutation } from '@tanstack/react-query'
