"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  CheckCircle2,
  XCircle,
  User,
  Clock,
  Calendar
} from "lucide-react";
import { format } from "date-fns";
import { WorkflowStep } from "@/types/workflow";
import { cn } from "@/lib/utils";

interface WorkflowStepCardProps {
  step: WorkflowStep;
  isCurrentStep?: boolean;
  className?: string;
  onApprove?: (stepId: string, comment: string) => void;
  onReject?: (stepId: string, comment: string) => void;
  onComplete?: (stepId: string, comment: string) => void;
}

export function WorkflowStepCard({
  step,
  isCurrentStep = false,
  className,
  onApprove,
  onReject,
  onComplete
}: Work<PERSON>StepCardProps) {
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Format status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return { variant: "outline" as const, label: "Pending" };
      case "IN_PROGRESS":
        return { variant: "secondary" as const, label: "In Progress" };
      case "COMPLETED":
        return { variant: "success" as const, label: "Completed" };
      case "APPROVED":
        return { variant: "success" as const, label: "Approved" };
      case "REJECTED":
        return { variant: "destructive" as const, label: "Rejected" };
      case "SKIPPED":
        return { variant: "default" as const, label: "Skipped" };
      default:
        return { variant: "outline" as const, label: status };
    }
  };

  const statusBadge = getStatusBadge(step.status || 'pending');

  // Handle approve
  const handleApprove = () => {
    if (onApprove) {
      setIsSubmitting(true);
      try {
        onApprove(step.id, comment);
        setComment("");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Handle reject
  const handleReject = () => {
    if (onReject) {
      setIsSubmitting(true);
      try {
        onReject(step.id, comment);
        setComment("");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Handle complete
  const handleComplete = () => {
    if (onComplete) {
      setIsSubmitting(true);
      try {
        onComplete(step.id, comment);
        setComment("");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Check if step is actionable
  const isActionable = isCurrentStep &&
    (step.status === "pending" || step.status === "in_progress");

  // Check if step is approval type
  const isApprovalType = step.type === "approval";

  return (
    <Card className={cn(
      "w-full transition-shadow",
      isCurrentStep ? "ring-2 ring-primary/20" : "",
      className
    )}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base flex items-center gap-2">
            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium">
              {step.order}
            </span>
            {step.name}
          </CardTitle>
          <Badge variant={statusBadge.variant}>{statusBadge.label}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {step.description && (
          <p className="text-sm text-muted-foreground">{step.description}</p>
        )}

        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <User size={12} className="text-muted-foreground" />
            <span className="text-muted-foreground">Assignee: {step.assigneeId || "Unassigned"}</span>
          </div>

          {step.dueDate && (
            <div className="flex items-center gap-1">
              <Calendar size={12} className="text-muted-foreground" />
              <span className="text-muted-foreground">
                Due: {format(new Date(step.dueDate), 'MMM d, yyyy')}
              </span>
            </div>
          )}

          {step.completedAt && (
            <div className="flex items-center gap-1 col-span-2">
              <Clock size={12} className="text-muted-foreground" />
              <span className="text-muted-foreground">
                Completed: {format(new Date(step.completedAt), 'MMM d, yyyy')}
              </span>
            </div>
          )}
        </div>

        {isActionable && (
          <div className="pt-2">
            <Textarea
              placeholder="Add a comment (optional)"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className="mb-3 text-sm"
              rows={2}
            />
          </div>
        )}
      </CardContent>

      {isActionable && (
        <CardFooter className="flex gap-2">
          {isApprovalType ? (
            <>
              <Button
                variant="default"
                className="flex-1"
                onClick={handleApprove}
                disabled={isSubmitting}
              >
                <CheckCircle2 className="mr-1 h-4 w-4" />
                Approve
              </Button>
              <Button
                variant="destructive"
                className="flex-1"
                onClick={handleReject}
                disabled={isSubmitting}
              >
                <XCircle className="mr-1 h-4 w-4" />
                Reject
              </Button>
            </>
          ) : (
            <Button
              variant="default"
              className="flex-1"
              onClick={handleComplete}
              disabled={isSubmitting}
            >
              <CheckCircle2 className="mr-1 h-4 w-4" />
              Complete
            </Button>
          )}
        </CardFooter>
      )}
    </Card>
  );
}
