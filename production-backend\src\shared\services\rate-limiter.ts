/**
 * Production-Ready Rate Limiter with Redis Backend
 * Implements sliding window, token bucket, and fixed window algorithms
 * with comprehensive monitoring and circuit breaker patterns
 */

import { redis } from './redis';
import { logger } from '../utils/logger';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  algorithm: 'sliding-window' | 'token-bucket' | 'fixed-window';
  keyGenerator?: (identifier: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
  headers?: boolean;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  totalHits: number;
  retryAfter?: number;
}

export interface RateLimitMetrics {
  totalRequests: number;
  blockedRequests: number;
  allowedRequests: number;
  averageRequestsPerSecond: number;
  peakRequestsPerSecond: number;
  lastUpdated: Date;
}

export class RateLimiter {
  private static instance: RateLimiter;
  private metrics: Map<string, RateLimitMetrics> = new Map();
  private circuitBreakers: Map<string, { isOpen: boolean; lastFailure: Date; failureCount: number }> = new Map();

  public static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  /**
   * Check rate limit for identifier
   */
  async checkRateLimit(
    identifier: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`;
    
    try {
      switch (config.algorithm) {
        case 'sliding-window':
          return await this.slidingWindowCheck(key, config);
        case 'token-bucket':
          return await this.tokenBucketCheck(key, config);
        case 'fixed-window':
        default:
          return await this.fixedWindowCheck(key, config);
      }
    } catch (error) {
      logger.error('Rate limit check failed', {
        identifier,
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // Fail open - allow request if rate limiter is down
      return {
        allowed: true,
        remaining: config.maxRequests,
        resetTime: new Date(Date.now() + config.windowMs),
        totalHits: 0
      };
    }
  }

  /**
   * Sliding window rate limiting (most accurate)
   */
  private async slidingWindowCheck(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Use Redis sorted set for sliding window
    const pipeline = await redis.multi();

    // Remove expired entries
    pipeline.zremrangebyscore(key, 0, windowStart);

    // Add current request
    pipeline.zadd(key, now, `${now}-${Math.random()}`);

    // Count requests in window
    pipeline.zcard(key);

    // Set expiration
    pipeline.expire(key, Math.ceil(config.windowMs / 1000) + 1);

    const results = await pipeline.exec();
    const totalHits = results?.[2]?.[1] as number || 0;
    
    const allowed = totalHits <= config.maxRequests;
    const remaining = Math.max(0, config.maxRequests - totalHits);
    const resetTime = new Date(now + config.windowMs);
    
    this.updateMetrics(key, allowed);
    
    return {
      allowed,
      remaining,
      resetTime,
      totalHits,
      retryAfter: allowed ? undefined : Math.ceil(config.windowMs / 1000)
    };
  }

  /**
   * Token bucket rate limiting (burst-friendly)
   */
  private async tokenBucketCheck(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const bucketKey = `${key}:bucket`;
    
    // Get current bucket state
    const bucketData = await redis.hmget(bucketKey, 'tokens', 'lastRefill');
    let tokens = parseInt(bucketData[0] || config.maxRequests.toString());
    let lastRefill = parseInt(bucketData[1] || now.toString());
    
    // Calculate tokens to add based on time elapsed
    const timePassed = now - lastRefill;
    const tokensToAdd = Math.floor(timePassed / (config.windowMs / config.maxRequests));
    tokens = Math.min(config.maxRequests, tokens + tokensToAdd);
    
    const allowed = tokens > 0;
    if (allowed) {
      tokens--;
    }
    
    // Update bucket state
    await redis.hmset(bucketKey, 'tokens', tokens, 'lastRefill', now);
    await redis.expire(bucketKey, Math.ceil(config.windowMs / 1000) * 2);
    
    const resetTime = new Date(now + (config.maxRequests - tokens) * (config.windowMs / config.maxRequests));
    
    this.updateMetrics(key, allowed);
    
    return {
      allowed,
      remaining: tokens,
      resetTime,
      totalHits: config.maxRequests - tokens,
      retryAfter: allowed ? undefined : Math.ceil((config.windowMs / config.maxRequests) / 1000)
    };
  }

  /**
   * Production fixed window rate limiting with Redis-backed persistence and high performance
   */
  private async fixedWindowCheck(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
    const windowKey = `${key}:${windowStart}`;
    
    // Increment counter
    const totalHits = await redis.incr(windowKey);
    
    // Set expiration on first request in window
    if (totalHits === 1) {
      await redis.expire(windowKey, Math.ceil(config.windowMs / 1000));
    }
    
    const allowed = totalHits <= config.maxRequests;
    const remaining = Math.max(0, config.maxRequests - totalHits);
    const resetTime = new Date(windowStart + config.windowMs);
    
    this.updateMetrics(key, allowed);
    
    return {
      allowed,
      remaining,
      resetTime,
      totalHits,
      retryAfter: allowed ? undefined : Math.ceil((resetTime.getTime() - now) / 1000)
    };
  }

  /**
   * Create rate limit middleware for Azure Functions
   */
  createMiddleware(config: RateLimitConfig) {
    return async (identifier: string): Promise<{ allowed: boolean; headers: Record<string, string>; message?: string }> => {
      const result = await this.checkRateLimit(identifier, config);
      
      const headers: Record<string, string> = {};
      
      if (config.headers !== false) {
        headers['X-RateLimit-Limit'] = config.maxRequests.toString();
        headers['X-RateLimit-Remaining'] = result.remaining.toString();
        headers['X-RateLimit-Reset'] = Math.ceil(result.resetTime.getTime() / 1000).toString();
        
        if (result.retryAfter) {
          headers['Retry-After'] = result.retryAfter.toString();
        }
      }
      
      return {
        allowed: result.allowed,
        headers,
        message: result.allowed ? undefined : (config.message || 'Too many requests')
      };
    };
  }

  /**
   * Get rate limit metrics
   */
  getMetrics(key?: string): RateLimitMetrics | Map<string, RateLimitMetrics> {
    if (key) {
      return this.metrics.get(key) || {
        totalRequests: 0,
        blockedRequests: 0,
        allowedRequests: 0,
        averageRequestsPerSecond: 0,
        peakRequestsPerSecond: 0,
        lastUpdated: new Date()
      };
    }
    return new Map(this.metrics);
  }

  /**
   * Reset rate limit for identifier
   */
  async resetRateLimit(identifier: string, config: RateLimitConfig): Promise<void> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`;
    
    try {
      switch (config.algorithm) {
        case 'sliding-window':
          await redis.del(key);
          break;
        case 'token-bucket':
          await redis.del(`${key}:bucket`);
          break;
        case 'fixed-window':
          // Delete all possible window keys (approximate)
          const now = Date.now();
          const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
          await redis.del(`${key}:${windowStart}`);
          break;
      }
      
      logger.info('Rate limit reset', { identifier, key });
    } catch (error) {
      logger.error('Failed to reset rate limit', {
        identifier,
        key,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Update metrics for monitoring
   */
  private updateMetrics(key: string, allowed: boolean): void {
    const current = this.metrics.get(key) || {
      totalRequests: 0,
      blockedRequests: 0,
      allowedRequests: 0,
      averageRequestsPerSecond: 0,
      peakRequestsPerSecond: 0,
      lastUpdated: new Date()
    };
    
    current.totalRequests++;
    if (allowed) {
      current.allowedRequests++;
    } else {
      current.blockedRequests++;
    }
    
    // Production requests per second calculation using exponential moving average for real-time metrics
    const now = Date.now();
    const timeDiff = (now - current.lastUpdated.getTime()) / 1000;
    if (timeDiff > 0) {
      const currentRps = 1 / timeDiff;
      current.averageRequestsPerSecond = (current.averageRequestsPerSecond * 0.9) + (currentRps * 0.1);
      current.peakRequestsPerSecond = Math.max(current.peakRequestsPerSecond, currentRps);
    }
    
    current.lastUpdated = new Date();
    this.metrics.set(key, current);
  }

  /**
   * Circuit breaker for rate limiter health
   */
  private checkCircuitBreaker(key: string): boolean {
    const breaker = this.circuitBreakers.get(key);
    if (!breaker) return true;
    
    // Reset circuit breaker after 1 minute
    if (breaker.isOpen && Date.now() - breaker.lastFailure.getTime() > 60000) {
      breaker.isOpen = false;
      breaker.failureCount = 0;
    }
    
    return !breaker.isOpen;
  }

  /**
   * Record circuit breaker failure
   */
  private recordFailure(key: string): void {
    const breaker = this.circuitBreakers.get(key) || {
      isOpen: false,
      lastFailure: new Date(),
      failureCount: 0
    };
    
    breaker.failureCount++;
    breaker.lastFailure = new Date();
    
    // Open circuit breaker after 5 failures
    if (breaker.failureCount >= 5) {
      breaker.isOpen = true;
      logger.warn('Rate limiter circuit breaker opened', { key });
    }
    
    this.circuitBreakers.set(key, breaker);
  }
}

// Export singleton instance
export const rateLimiter = RateLimiter.getInstance();

// Common rate limit configurations
export const RateLimitConfigs = {
  // API endpoints
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    algorithm: 'sliding-window' as const,
    message: 'Too many API requests'
  },
  
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    algorithm: 'fixed-window' as const,
    message: 'Too many authentication attempts'
  },
  
  // File upload endpoints
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    algorithm: 'token-bucket' as const,
    message: 'Too many upload requests'
  },
  
  // Heavy operations
  processing: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 3,
    algorithm: 'fixed-window' as const,
    message: 'Too many processing requests'
  }
};
