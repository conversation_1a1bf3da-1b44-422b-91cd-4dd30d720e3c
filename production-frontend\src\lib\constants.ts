/**
 * Application Constants
 * Centralized configuration for the application
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || '/api',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const

// Authentication Configuration
export const AUTH_CONFIG = {
  TOKEN_KEY: 'auth_token',
  REFRESH_TOKEN_KEY: 'refresh_token',
  USER_KEY: 'user_data',
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 minutes before expiry
} as const

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'app_theme',
  LANGUAGE: 'app_language',
  USER_PREFERENCES: 'user_preferences',
  DASHBOARD_LAYOUT: 'dashboard_layout',
  SIDEBAR_STATE: 'sidebar_state',
  RECENT_DOCUMENTS: 'recent_documents',
  RECENT_PROJECTS: 'recent_projects',
} as const

// Application Routes
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  DASHBOARD: '/dashboard',
  DOCUMENTS: '/documents',
  PROJECTS: '/projects',
  TEMPLATES: '/templates',
  WORKFLOWS: '/workflows',
  ANALYTICS: '/analytics',
  SETTINGS: '/settings',
  PROFILE: '/profile',
} as const

// API Endpoints - ALIGNED WITH BACKEND
export const ENDPOINTS = {
  // Authentication - Matches backend exactly
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/me', // ✅ FIXED: Backend uses /auth/me
    REGISTER: '/auth/register',
  },

  // Users - Matches backend structure
  USERS: {
    BASE: '/users',
    PROFILE: '/users/{userId?}/profile', // ✅ FIXED: Backend uses optional userId
    PROFILE_UPDATE: '/users/profile/update', // ✅ ADDED: Separate update endpoint
    PREFERENCES: '/users/preferences',
    PREFERENCES_UPDATE: '/users/preferences/update', // ✅ FIXED: Backend has separate update
    AVATAR: '/users/avatar/upload', // ✅ FIXED: Backend uses /upload suffix
    ACTIVITY: '/users/activity/track', // ✅ ADDED: Activity tracking
    PERMISSIONS: '/users/{userId?}/permissions', // ✅ ADDED: User permissions
  },

  // Organizations - Matches backend structure
  ORGANIZATIONS: {
    BASE: '/organizations',
    LIST: '/organizations/list', // ✅ ADDED: Backend has separate list endpoint
    MANAGE: '/organizations/{organizationId}', // ✅ FIXED: Backend uses organizationId
    MEMBERS_INVITE: '/organizations/{organizationId}/members/invite', // ✅ FIXED: Backend structure
    SETTINGS: '/organizations/{organizationId}/settings', // ✅ FIXED: Backend uses organizationId
    ANALYTICS: '/organizations/analytics', // ✅ ADDED: Analytics endpoint
    BILLING: '/organizations/{organizationId}/billing', // ✅ ADDED: Billing endpoint
  },

  // Projects - Matches backend structure
  PROJECTS: {
    BASE: '/projects',
    LIST: '/projects/list', // ✅ ADDED: Backend has separate list endpoint
    MANAGE: '/projects/{projectId}', // ✅ FIXED: Backend uses projectId
    MEMBERS: '/projects/{projectId}/members', // ✅ FIXED: Backend uses projectId
    MEMBERS_ADD: '/projects/members', // ✅ ADDED: Backend endpoint for adding members
    MEMBERS_UPDATE: '/projects/members/update', // ✅ ADDED: Backend endpoint for updating members
    SETTINGS: '/projects/{projectId}/settings', // ✅ FIXED: Backend uses projectId
    ANALYTICS: '/projects/analytics', // ✅ ADDED: Analytics endpoint
  },
  
  // Documents - ✅ ALIGNED WITH BACKEND
  DOCUMENTS: {
    BASE: '/documents',
    LIST: '/documents', // ✅ Backend: GET /documents
    UPLOAD: '/documents/upload',
    UPLOAD_COMPLETE: '/documents/{documentId}/upload/complete', // ✅ Backend endpoint
    RETRIEVE: '/documents/{id}', // ✅ Backend endpoint
    AI_ANALYSIS: '/documents/{id}/ai-analysis', // ✅ Backend AI analysis
    CLASSIFY: '/documents/{documentId}/classify', // ✅ Backend classification
    PROCESS: '/documents/process', // ✅ Backend processing
    TRANSFORM: '/documents/transform', // ✅ Backend transform
    SIGN: '/documents/sign', // ✅ Backend signing
    VERSIONS: '/documents/{id}/versions',
    VERSION_CREATE: '/documents/{documentId}/versions', // ✅ Backend version creation
    SHARE: '/documents/{id}/share',
    SHARE_CREATE: '/documents/{documentId}/share', // ✅ Backend share creation
    COMMENTS: '/documents/{documentId}/comments', // ✅ Backend comments
    METADATA: '/documents/{documentId}/metadata', // ✅ Backend metadata
    ARCHIVE: '/documents/{documentId}/archive', // ✅ Backend archive
    RESTORE: '/documents/{documentId}/restore', // ✅ Backend restore
  },

  // Templates - ✅ ALIGNED WITH BACKEND
  TEMPLATES: {
    BASE: '/templates',
    MANAGE: '/templates/manage', // ✅ Backend: /templates/manage
    GENERATE: '/templates/generate', // ✅ Backend endpoint
    APPLY: '/templates/{templateId}/apply', // ✅ Backend apply endpoint
  },

  // AI Services - ✅ ALIGNED WITH BACKEND
  AI: {
    CONTENT_GENERATE: '/ai/content/generate', // ✅ Backend endpoint
    OPERATIONS: '/ai/operations',
    OPERATION_STATUS: '/ai/operations/{operationId}', // ✅ Backend status
    MODELS: '/ai/models',
    MODEL_TRAIN: '/ai/models/{modelId}/train', // ✅ Backend training
    MODEL_DEPLOY: '/ai/models/{modelId}/deploy', // ✅ Backend deployment
    BATCH_JOBS: '/ai/batch-jobs', // ✅ Backend batch processing
    BATCH_STATUS: '/ai/batch-jobs/{batchJobId}/status', // ✅ Backend batch status
    FORMS_PROCESS: '/ai/forms/process', // ✅ Backend form processing
  },

  // Analytics - ✅ ALIGNED WITH BACKEND
  ANALYTICS: {
    BASE: '/analytics',
    DASHBOARD: '/analytics/dashboard',
    ADVANCED: '/analytics/advanced', // ✅ Backend advanced analytics
    BI_REPORTS: '/analytics/bi/reports', // ✅ Backend BI reports
    BI_DASHBOARD: '/analytics/bi/dashboard', // ✅ Backend BI dashboard
    PREDICTIONS: '/analytics/predictions', // ✅ Backend predictions
  },

  // Collaboration - ✅ ALIGNED WITH BACKEND
  COLLABORATION: {
    SESSIONS: '/collaboration/sessions',
    SESSION_JOIN: '/collaboration/sessions/join', // ✅ Backend join endpoint
  },

  // System - ✅ ALIGNED WITH BACKEND
  SYSTEM: {
    HEALTH: '/system/health',
    HEALTH_STATUS: '/system/health-status', // ✅ Backend health status
    METRICS: '/system/metrics',
  },

  // SignalR - ✅ ALIGNED WITH BACKEND
  SIGNALR: {
    NEGOTIATE: '/signalr/negotiate',
    BROADCAST: '/signalr/broadcast', // ✅ Backend broadcast
    GROUPS: '/signalr/groups', // ✅ Backend groups
  },
  
  // Notifications
  NOTIFICATIONS: {
    BASE: '/notifications',
    MARK_READ: '/notifications/{id}/read',
    MARK_ALL_READ: '/notifications/read-all',
    PREFERENCES: '/notifications/preferences',
  },
} as const

// File Upload Configuration
export const FILE_CONFIG = {
  MAX_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif',
    'text/plain',
    'text/csv',
  ],
  CHUNK_SIZE: 1024 * 1024, // 1MB chunks for upload
} as const

// Pagination Configuration
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const

// Theme Configuration
export const THEME = {
  DEFAULT: 'light',
  OPTIONS: ['light', 'dark', 'system'] as const,
} as const

// Language Configuration
export const LANGUAGE = {
  DEFAULT: 'en',
  OPTIONS: [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
  ],
} as const

// Document Processing Status
export const DOCUMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const

// Project Status
export const PROJECT_STATUS = {
  ACTIVE: 'active',
  COMPLETED: 'completed',
  ON_HOLD: 'on_hold',
  CANCELLED: 'cancelled',
  ARCHIVED: 'archived',
} as const

// Workflow Status
export const WORKFLOW_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  USER: 'user',
  VIEWER: 'viewer',
} as const

// Permissions
export const PERMISSIONS = {
  // Document permissions
  DOCUMENTS_READ: 'documents:read',
  DOCUMENTS_WRITE: 'documents:write',
  DOCUMENTS_DELETE: 'documents:delete',
  DOCUMENTS_SHARE: 'documents:share',
  
  // Project permissions
  PROJECTS_READ: 'projects:read',
  PROJECTS_WRITE: 'projects:write',
  PROJECTS_DELETE: 'projects:delete',
  PROJECTS_MANAGE: 'projects:manage',
  
  // Template permissions
  TEMPLATES_READ: 'templates:read',
  TEMPLATES_WRITE: 'templates:write',
  TEMPLATES_DELETE: 'templates:delete',
  TEMPLATES_SHARE: 'templates:share',
  
  // Workflow permissions
  WORKFLOWS_READ: 'workflows:read',
  WORKFLOWS_WRITE: 'workflows:write',
  WORKFLOWS_DELETE: 'workflows:delete',
  WORKFLOWS_EXECUTE: 'workflows:execute',
  
  // Analytics permissions
  ANALYTICS_READ: 'analytics:read',
  ANALYTICS_EXPORT: 'analytics:export',
  
  // Admin permissions
  ADMIN_USERS: 'admin:users',
  ADMIN_ORGANIZATIONS: 'admin:organizations',
  ADMIN_SETTINGS: 'admin:settings',
} as const

// Notification Types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
} as const

// Real-time Event Types
export const REALTIME_EVENTS = {
  DOCUMENT_PROCESSED: 'document:processed',
  WORKFLOW_COMPLETED: 'workflow:completed',
  PROJECT_UPDATED: 'project:updated',
  NOTIFICATION_RECEIVED: 'notification:received',
  USER_ACTIVITY: 'user:activity',
  COLLABORATION_UPDATE: 'collaboration:update',
} as const

// Error Codes
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
} as const

// Success Messages
export const SUCCESS_MESSAGES = {
  DOCUMENT_UPLOADED: 'Document uploaded successfully',
  DOCUMENT_PROCESSED: 'Document processed successfully',
  PROJECT_CREATED: 'Project created successfully',
  PROJECT_UPDATED: 'Project updated successfully',
  TEMPLATE_SAVED: 'Template saved successfully',
  WORKFLOW_EXECUTED: 'Workflow executed successfully',
  SETTINGS_SAVED: 'Settings saved successfully',
} as const

// Error Messages
export const ERROR_MESSAGES = {
  GENERIC: 'An unexpected error occurred',
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action',
  FORBIDDEN: 'Access denied',
  NOT_FOUND: 'The requested resource was not found',
  VALIDATION: 'Please check your input and try again',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit',
  INVALID_FILE_TYPE: 'Invalid file type',
  UPLOAD_FAILED: 'File upload failed',
} as const

// Loading States
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const

// Animation Durations (in milliseconds)
export const ANIMATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const

// Z-Index Layers
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const
