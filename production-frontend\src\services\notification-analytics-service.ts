/**
 * Notification Analytics Service
 * Service for notification analytics and metrics
 */

import { backendApiClient } from './backend-api-client'

export interface NotificationAnalytics {
  totalSent: number
  totalDelivered: number
  totalOpened: number
  totalClicked: number
  deliveryRate: number
  openRate: number
  clickRate: number
  bounceRate: number
  unsubscribeRate: number
  byChannel: {
    email: NotificationChannelMetrics
    sms: NotificationChannelMetrics
    push: NotificationChannelMetrics
    inApp: NotificationChannelMetrics
  }
  byType: Record<string, NotificationTypeMetrics>
  timeline: Array<{
    date: string
    sent: number
    delivered: number
    opened: number
    clicked: number
  }>
  topPerformers: Array<{
    templateId: string
    templateName: string
    sent: number
    openRate: number
    clickRate: number
  }>
}

export interface NotificationChannelMetrics {
  sent: number
  delivered: number
  opened: number
  clicked: number
  bounced: number
  unsubscribed: number
  deliveryRate: number
  openRate: number
  clickRate: number
  bounceRate: number
  unsubscribeRate: number
}

export interface NotificationTypeMetrics {
  sent: number
  delivered: number
  opened: number
  clicked: number
  deliveryRate: number
  openRate: number
  clickRate: number
  avgEngagementTime: number
}

export interface NotificationPerformanceReport {
  id: string
  name: string
  dateRange: {
    start: string
    end: string
  }
  metrics: NotificationAnalytics
  insights: Array<{
    type: 'improvement' | 'warning' | 'success'
    title: string
    description: string
    recommendation?: string
  }>
  comparisons: {
    previousPeriod: {
      deliveryRate: number
      openRate: number
      clickRate: number
      change: {
        deliveryRate: number
        openRate: number
        clickRate: number
      }
    }
  }
  createdAt: string
}

export interface AnalyticsFilters {
  organizationId: string
  dateRange?: {
    start: string
    end: string
  }
  channels?: string[]
  types?: string[]
  templateIds?: string[]
  userSegments?: string[]
}

class NotificationAnalyticsService {
  /**
   * Get notification analytics
   */
  async getAnalytics(filters: AnalyticsFilters): Promise<NotificationAnalytics> {
    return await backendApiClient.request<NotificationAnalytics>('/notifications/analytics', {
      params: filters
    })
  }

  /**
   * Get channel-specific analytics
   */
  async getChannelAnalytics(
    channel: string, 
    filters: AnalyticsFilters
  ): Promise<NotificationChannelMetrics> {
    return await backendApiClient.request<NotificationChannelMetrics>(
      `/notifications/analytics/channels/${channel}`,
      { params: filters }
    )
  }

  /**
   * Get notification type analytics
   */
  async getTypeAnalytics(
    type: string, 
    filters: AnalyticsFilters
  ): Promise<NotificationTypeMetrics> {
    return await backendApiClient.request<NotificationTypeMetrics>(
      `/notifications/analytics/types/${type}`,
      { params: filters }
    )
  }

  /**
   * Get performance report
   */
  async getPerformanceReport(
    reportId: string,
    organizationId: string
  ): Promise<NotificationPerformanceReport> {
    return await backendApiClient.request<NotificationPerformanceReport>(
      `/notifications/analytics/reports/${reportId}`,
      { params: { organizationId } }
    )
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport(data: {
    name: string
    filters: AnalyticsFilters
    includeInsights?: boolean
    includeComparisons?: boolean
  }): Promise<NotificationPerformanceReport> {
    return await backendApiClient.request<NotificationPerformanceReport>(
      '/notifications/analytics/reports',
      {
        method: 'POST',
        body: JSON.stringify(data)
      }
    )
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics(organizationId: string): Promise<{
    activeCampaigns: number
    messagesInQueue: number
    deliveryRate: number
    errorRate: number
    lastUpdated: string
  }> {
    return await backendApiClient.request('/notifications/analytics/realtime', {
      params: { organizationId }
    })
  }

  /**
   * Get engagement trends
   */
  async getEngagementTrends(filters: AnalyticsFilters & {
    metric: 'delivery' | 'open' | 'click' | 'unsubscribe'
    granularity: 'hour' | 'day' | 'week' | 'month'
  }): Promise<Array<{
    date: string
    value: number
    change: number
  }>> {
    return await backendApiClient.request('/notifications/analytics/trends', {
      params: filters
    })
  }

  /**
   * Get user engagement analytics
   */
  async getUserEngagementAnalytics(filters: AnalyticsFilters): Promise<{
    totalUsers: number
    activeUsers: number
    engagedUsers: number
    segments: Array<{
      name: string
      userCount: number
      engagementRate: number
      avgOpenRate: number
      avgClickRate: number
    }>
    cohorts: Array<{
      cohort: string
      userCount: number
      retentionRate: number
      engagementRate: number
    }>
  }> {
    return await backendApiClient.request('/notifications/analytics/users', {
      params: filters
    })
  }

  /**
   * Get template performance comparison
   */
  async compareTemplatePerformance(data: {
    templateIds: string[]
    organizationId: string
    dateRange?: {
      start: string
      end: string
    }
  }): Promise<Array<{
    templateId: string
    templateName: string
    metrics: NotificationTypeMetrics
    ranking: number
    insights: string[]
  }>> {
    return await backendApiClient.request('/notifications/analytics/templates/compare', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Export analytics data
   */
  async exportAnalytics(data: {
    filters: AnalyticsFilters
    format: 'csv' | 'excel' | 'pdf'
    includeCharts?: boolean
  }): Promise<{
    downloadUrl: string
    expiresAt: string
  }> {
    return await backendApiClient.request('/notifications/analytics/export', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Get A/B test results
   */
  async getABTestResults(testId: string, organizationId: string): Promise<{
    testId: string
    testName: string
    status: 'running' | 'completed' | 'paused'
    variants: Array<{
      id: string
      name: string
      trafficPercentage: number
      metrics: NotificationTypeMetrics
      isWinner?: boolean
      confidence?: number
    }>
    insights: Array<{
      type: 'statistical' | 'practical'
      message: string
      significance: number
    }>
    recommendations: string[]
  }> {
    return await backendApiClient.request(`/notifications/analytics/ab-tests/${testId}`, {
      params: { organizationId }
    })
  }

  /**
   * Get delivery analytics
   */
  async getDeliveryAnalytics(filters: AnalyticsFilters): Promise<{
    deliveryStatus: {
      delivered: number
      pending: number
      failed: number
      bounced: number
    }
    failureReasons: Array<{
      reason: string
      count: number
      percentage: number
    }>
    providerPerformance: Array<{
      provider: string
      deliveryRate: number
      avgDeliveryTime: number
      errorRate: number
    }>
    geographicDistribution: Array<{
      country: string
      sent: number
      delivered: number
      deliveryRate: number
    }>
  }> {
    return await backendApiClient.request('/notifications/analytics/delivery', {
      params: filters
    })
  }
}

export const notificationAnalyticsService = new NotificationAnalyticsService()
