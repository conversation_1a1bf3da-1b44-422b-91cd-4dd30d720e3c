"use client";

import React from "react";
import { <PERSON>ert<PERSON>ircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { cn } from "@/lib/utils";

export interface ErrorDisplayProps {
  title?: string;
  description?: string;
  error?: Error | string | null;
  onRetry?: () => void;
  className?: string;
  variant?: "default" | "inline" | "minimal" | "card";
  showDetails?: boolean;
  actions?: React.ReactNode;
}

/**
 * A reusable component for displaying errors with optional retry functionality
 */
export function ErrorDisplay({
  title = "An error occurred",
  description = "We encountered a problem while processing your request.",
  error,
  onRetry,
  className,
  variant = "default",
  showDetails = false,
  actions,
}: ErrorDisplayProps) {
  // Get error message from error object or string
  const errorMessage = React.useMemo(() => {
    if (!error) return null;
    if (typeof error === "string") return error;
    return error.message || "Unknown error";
  }, [error]);

  // Render different variants
  if (variant === "inline") {
    return (
      <div className={cn("flex items-center gap-2 text-destructive", className)}>
        <AlertCircle className="h-4 w-4" />
        <span>{description}</span>
        {onRetry && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 px-2"
            onClick={onRetry}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </div>
    );
  }

  if (variant === "minimal") {
    return (
      <div className={cn("text-destructive", className)}>
        <div className="flex items-center gap-2">
          <AlertCircle className="h-4 w-4" />
          <span className="font-medium">{title}</span>
        </div>
        <p className="mt-1 text-sm">{description}</p>
        {showDetails && errorMessage && (
          <p className="mt-1 text-xs opacity-80">{errorMessage}</p>
        )}
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={onRetry}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </div>
    );
  }

  if (variant === "card") {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-destructive" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        {showDetails && errorMessage && (
          <CardContent>
            <pre className="text-xs bg-muted p-2 rounded overflow-auto">
              {errorMessage}
            </pre>
          </CardContent>
        )}
        {(onRetry || actions) && (
          <CardFooter className="flex gap-2">
            {onRetry && (
              <Button variant="outline" onClick={onRetry}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
            {actions}
          </CardFooter>
        )}
      </Card>
    );
  }

  // Default variant
  return (
    <Alert variant="destructive" className={cn("", className)}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>
        {description}
        {showDetails && errorMessage && (
          <div className="mt-2 text-xs opacity-80">{errorMessage}</div>
        )}
        {onRetry && (
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={onRetry}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </AlertDescription>
    </Alert>
  );
}
