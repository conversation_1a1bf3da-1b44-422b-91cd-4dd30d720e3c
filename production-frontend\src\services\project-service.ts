/**
 * Project Service
 * Handles all project-related API calls using backend API client
 */

import { backendApiClient, type PaginatedResponse } from './backend-api-client'
import type { Project, ProjectStatus } from '../types/backend'

class ProjectService {
  /**
   * Get all projects with optional filters
   */
  async getProjects(params?: {
    organizationId?: string
    status?: ProjectStatus[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Project>> {
    return await backendApiClient.getProjects(params)
  }

  /**
   * Get project by ID
   */
  async getProject(projectId: string): Promise<Project> {
    return await backendApiClient.getProject(projectId)
  }

  /**
   * Create new project
   */
  async createProject(projectData: {
    name: string
    description?: string
    organizationId: string
    visibility?: any
    status?: ProjectStatus
    startDate?: string
    endDate?: string
    tags?: string[]
  }): Promise<Project> {
    // Transform the data to match backend expectations
    const backendData = {
      name: projectData.name,
      description: projectData.description,
      organizationId: projectData.organizationId,
      visibility: projectData.visibility || 'PRIVATE',
      settings: {}
    }
    return await backendApiClient.createProject(backendData)
  }

  /**
   * Update project
   */
  async updateProject(projectId: string, updateData: Partial<Project>): Promise<Project> {
    return await backendApiClient.updateProject(projectId, updateData)
  }

  /**
   * Delete project
   */
  async deleteProject(projectId: string): Promise<void> {
    return await backendApiClient.deleteProject(projectId)
  }

  /**
   * Archive project
   */
  async archiveProject(projectId: string): Promise<Project> {
    return await backendApiClient.archiveProject(projectId)
  }

  /**
   * Restore archived project
   */
  async restoreProject(projectId: string): Promise<Project> {
    return await backendApiClient.restoreProject(projectId)
  }

  /**
   * Get project members
   */
  async getProjectMembers(projectId: string): Promise<Array<{
    id: string
    userId: string
    projectId: string
    role: string
    permissions: string[]
    joinedAt: string
    user: {
      id: string
      email: string
      firstName: string
      lastName: string
      avatar?: string
    }
  }>> {
    return await backendApiClient.getProjectMembers(projectId)
  }

  /**
   * Add member to project
   */
  async addProjectMember(projectId: string, memberData: {
    userId: string
    role: string
    permissions?: string[]
  }): Promise<void> {
    return await backendApiClient.addProjectMember(projectId, memberData)
  }

  /**
   * Update member role
   */
  async updateProjectMember(projectId: string, userId: string, updateData: {
    role?: string
    permissions?: string[]
  }): Promise<void> {
    return await backendApiClient.updateProjectMember(projectId, userId, updateData)
  }

  /**
   * Remove member from project
   */
  async removeProjectMember(projectId: string, userId: string): Promise<void> {
    return await backendApiClient.removeProjectMember(projectId, userId)
  }

  /**
   * Get project documents
   */
  async getProjectDocuments(projectId: string, params?: {
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<any>> {
    return await backendApiClient.getDocuments({ projectId, ...params })
  }

  /**
   * Get project analytics
   */
  async getProjectAnalytics(projectId: string): Promise<{
    totalDocuments: number
    totalMembers: number
    documentsProcessed: number
    storageUsed: number
    activityStats: {
      documentsUploaded: number
      documentsProcessed: number
      collaborationSessions: number
    }
    timelineStats: Array<{
      date: string
      documentsUploaded: number
      documentsProcessed: number
    }>
  }> {
    return await backendApiClient.getProjectAnalytics(projectId)
  }

  /**
   * Search projects
   */
  async searchProjects(query: string, filters?: {
    organizationId?: string
    status?: ProjectStatus[]
    tags?: string[]
  }): Promise<PaginatedResponse<Project>> {
    return await backendApiClient.searchProjects(query, filters)
  }

  /**
   * Get project activity
   */
  async getProjectActivity(projectId: string, params?: {
    page?: number
    pageSize?: number
    activityType?: string
  }): Promise<PaginatedResponse<{
    id: string
    type: string
    description: string
    userId: string
    projectId: string
    metadata: Record<string, any>
    createdAt: string
    user: {
      id: string
      email: string
      firstName: string
      lastName: string
    }
  }>> {
    return await backendApiClient.getProjectActivity(projectId, params)
  }

  /**
   * Export project data
   */
  async exportProject(projectId: string, format: 'json' | 'excel' | 'pdf'): Promise<{
    downloadUrl: string
    expiresAt: string
  }> {
    return await backendApiClient.exportProject(projectId, format)
  }

  // Aliases for backward compatibility
  async addMember(projectId: string, memberData: any): Promise<any> {
    return await this.addProjectMember(projectId, memberData)
  }

  async removeMember(projectId: string, userId: string): Promise<void> {
    return await this.removeProjectMember(projectId, userId)
  }

  async updateMemberRole(projectId: string, userId: string, role: string): Promise<any> {
    return await this.updateProjectMember(projectId, userId, { role })
  }
}

// Export singleton instance
export const projectService = new ProjectService()
