import { useEffect, useRef, useCallback } from 'react'

/**
 * Announcer Hook
 * Provides screen reader announcements for accessibility
 */

export type AnnouncementPriority = 'polite' | 'assertive'

export interface UseAnnouncerOptions {
  priority?: AnnouncementPriority
  timeout?: number
}

export interface UseAnnouncerResult {
  announce: (message: string, priority?: AnnouncementPriority) => void
  clear: () => void
}

export function useAnnouncer(
  options: UseAnnouncerOptions = {}
): UseAnnouncerResult {
  const { priority = 'polite', timeout = 1000 } = options
  
  const politeRef = useRef<HTMLDivElement | null>(null)
  const assertiveRef = useRef<HTMLDivElement | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Create announcement regions
  useEffect(() => {
    // Create polite announcement region
    if (!politeRef.current) {
      const politeDiv = document.createElement('div')
      politeDiv.setAttribute('aria-live', 'polite')
      politeDiv.setAttribute('aria-atomic', 'true')
      politeDiv.style.position = 'absolute'
      politeDiv.style.left = '-10000px'
      politeDiv.style.width = '1px'
      politeDiv.style.height = '1px'
      politeDiv.style.overflow = 'hidden'
      document.body.appendChild(politeDiv)
      politeRef.current = politeDiv
    }

    // Create assertive announcement region
    if (!assertiveRef.current) {
      const assertiveDiv = document.createElement('div')
      assertiveDiv.setAttribute('aria-live', 'assertive')
      assertiveDiv.setAttribute('aria-atomic', 'true')
      assertiveDiv.style.position = 'absolute'
      assertiveDiv.style.left = '-10000px'
      assertiveDiv.style.width = '1px'
      assertiveDiv.style.height = '1px'
      assertiveDiv.style.overflow = 'hidden'
      document.body.appendChild(assertiveDiv)
      assertiveRef.current = assertiveDiv
    }

    return () => {
      if (politeRef.current) {
        document.body.removeChild(politeRef.current)
        politeRef.current = null
      }
      if (assertiveRef.current) {
        document.body.removeChild(assertiveRef.current)
        assertiveRef.current = null
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const announce = useCallback((message: string, announcementPriority?: AnnouncementPriority) => {
    const targetPriority = announcementPriority || priority
    const targetRef = targetPriority === 'assertive' ? assertiveRef : politeRef

    if (!targetRef.current) return

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Set the message
    targetRef.current.textContent = message

    // Clear the message after timeout to allow for repeated announcements
    timeoutRef.current = setTimeout(() => {
      if (targetRef.current) {
        targetRef.current.textContent = ''
      }
    }, timeout)
  }, [priority, timeout])

  const clear = useCallback(() => {
    if (politeRef.current) {
      politeRef.current.textContent = ''
    }
    if (assertiveRef.current) {
      assertiveRef.current.textContent = ''
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  return {
    announce,
    clear,
  }
}

/**
 * Status announcer hook for form validation and loading states
 */
export interface UseStatusAnnouncerOptions {
  successMessage?: string
  errorMessage?: string
  loadingMessage?: string
}

export function useStatusAnnouncer(options: UseStatusAnnouncerOptions = {}) {
  const {
    successMessage = 'Operation completed successfully',
    errorMessage = 'An error occurred',
    loadingMessage = 'Loading...'
  } = options

  const { announce } = useAnnouncer()

  const announceSuccess = useCallback((message?: string) => {
    announce(message || successMessage, 'polite')
  }, [announce, successMessage])

  const announceError = useCallback((message?: string) => {
    announce(message || errorMessage, 'assertive')
  }, [announce, errorMessage])

  const announceLoading = useCallback((message?: string) => {
    announce(message || loadingMessage, 'polite')
  }, [announce, loadingMessage])

  return {
    announceSuccess,
    announceError,
    announceLoading,
    announce,
  }
}

/**
 * Form announcer hook for form validation
 */
export function useFormAnnouncer() {
  const { announce } = useAnnouncer()

  const announceValidationError = useCallback((fieldName: string, error: string) => {
    announce(`${fieldName}: ${error}`, 'assertive')
  }, [announce])

  const announceFormSubmitted = useCallback(() => {
    announce('Form submitted successfully', 'polite')
  }, [announce])

  const announceFormError = useCallback((error?: string) => {
    announce(error || 'Form submission failed. Please check for errors.', 'assertive')
  }, [announce])

  const announceFieldChange = useCallback((fieldName: string, value: string) => {
    announce(`${fieldName} changed to ${value}`, 'polite')
  }, [announce])

  return {
    announceValidationError,
    announceFormSubmitted,
    announceFormError,
    announceFieldChange,
    announce,
  }
}

/**
 * Navigation announcer hook
 */
export function useNavigationAnnouncer() {
  const { announce } = useAnnouncer()

  const announcePageChange = useCallback((pageName: string) => {
    announce(`Navigated to ${pageName}`, 'polite')
  }, [announce])

  const announceRouteChange = useCallback((route: string) => {
    announce(`Page changed to ${route}`, 'polite')
  }, [announce])

  const announceModalOpen = useCallback((modalTitle: string) => {
    announce(`${modalTitle} dialog opened`, 'polite')
  }, [announce])

  const announceModalClose = useCallback(() => {
    announce('Dialog closed', 'polite')
  }, [announce])

  return {
    announcePageChange,
    announceRouteChange,
    announceModalOpen,
    announceModalClose,
    announce,
  }
}

/**
 * Data announcer hook for dynamic content
 */
export function useDataAnnouncer() {
  const { announce } = useAnnouncer()

  const announceDataLoaded = useCallback((itemCount: number, itemType: string) => {
    announce(`${itemCount} ${itemType} loaded`, 'polite')
  }, [announce])

  const announceDataUpdated = useCallback((itemType: string) => {
    announce(`${itemType} updated`, 'polite')
  }, [announce])

  const announceDataDeleted = useCallback((itemType: string) => {
    announce(`${itemType} deleted`, 'polite')
  }, [announce])

  const announceSearchResults = useCallback((resultCount: number, query: string) => {
    announce(`${resultCount} results found for "${query}"`, 'polite')
  }, [announce])

  return {
    announceDataLoaded,
    announceDataUpdated,
    announceDataDeleted,
    announceSearchResults,
    announce,
  }
}
