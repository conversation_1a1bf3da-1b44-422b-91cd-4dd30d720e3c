/**
 * Azure Identity Service
 * Centralized credential management for all Azure services
 * Uses Azure Identity best practices with fallback authentication methods
 */

import {
  DefaultAzureCredential,
  ChainedTokenCredential,
  ManagedIdentityCredential,
  AzureCliCredential,
  EnvironmentCredential,
  VisualStudioCodeCredential,
  TokenCredential
} from '@azure/identity';
import { config } from '../../env';
import { logger } from '../utils/logger';

export interface AzureIdentityConfig {
  managedIdentityClientId?: string;
  tenantId?: string;
  subscriptionId?: string;
}

/**
 * Azure Identity Service for centralized credential management
 */
export class AzureIdentityService {
  private static instance: AzureIdentityService;
  private credential: TokenCredential | null = null;
  private isInitialized = false;
  private config: AzureIdentityConfig;

  private constructor() {
    this.config = {
      managedIdentityClientId: config.azure.managedIdentityClientId,
      tenantId: config.azure.tenantId,
      subscriptionId: config.azure.subscriptionId
    };
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): AzureIdentityService {
    if (!AzureIdentityService.instance) {
      AzureIdentityService.instance = new AzureIdentityService();
    }
    return AzureIdentityService.instance;
  }

  /**
   * Initialize Azure Identity with appropriate credential chain
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create credential chain based on environment
      if (config.app.environment === 'production') {
        // Production: Prefer Managed Identity
        this.credential = this.createProductionCredential();
      } else {
        // Development: Use comprehensive chain
        this.credential = this.createDevelopmentCredential();
      }

      // Test the credential by getting a token for Azure Resource Manager
      await this.testCredential();

      this.isInitialized = true;
      logger.info('Azure Identity Service initialized successfully', {
        environment: config.app.environment,
        managedIdentityType: this.config.managedIdentityClientId ? 'user-assigned' : 'system-assigned'
      });
    } catch (error) {
      logger.error('Failed to initialize Azure Identity Service', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get the configured credential
   */
  public getCredential(): TokenCredential {
    if (!this.credential) {
      throw new Error('Azure Identity Service not initialized. Call initialize() first.');
    }
    return this.credential;
  }

  /**
   * Create production credential chain (Managed Identity focused)
   */
  private createProductionCredential(): TokenCredential {
    const credentials: TokenCredential[] = [];

    // Add user-assigned managed identity if configured
    if (this.config.managedIdentityClientId) {
      credentials.push(new ManagedIdentityCredential({
        clientId: this.config.managedIdentityClientId
      }));
      logger.info('Added user-assigned managed identity to credential chain', {
        clientId: this.config.managedIdentityClientId
      });
    }

    // Add system-assigned managed identity
    credentials.push(new ManagedIdentityCredential());
    logger.info('Added system-assigned managed identity to credential chain');

    // Fallback to environment credential for service principal
    credentials.push(new EnvironmentCredential());

    return new ChainedTokenCredential(...credentials);
  }

  /**
   * Create development credential chain (comprehensive)
   */
  private createDevelopmentCredential(): TokenCredential {
    const credentials: TokenCredential[] = [];

    logger.info('Creating development credential chain for local development');

    // 1. Azure CLI credential (primary for local development)
    credentials.push(new AzureCliCredential());
    logger.info('Added Azure CLI credential to chain (primary for local dev)');

    // 2. Environment credential (for service principal auth)
    credentials.push(new EnvironmentCredential());
    logger.info('Added environment credential to chain');

    // 3. Visual Studio Code credential (for VS Code development)
    try {
      credentials.push(new VisualStudioCodeCredential());
      logger.info('Added Visual Studio Code credential to chain');
    } catch (error) {
      logger.warn('VS Code credential not available', { error: error instanceof Error ? error.message : String(error) });
    }

    // 4. Managed Identity (last resort - will fail in local dev but that's expected)
    if (this.config.managedIdentityClientId) {
      credentials.push(new ManagedIdentityCredential({
        clientId: this.config.managedIdentityClientId
      }));
      logger.info('Added user-assigned managed identity to credential chain', {
        clientId: this.config.managedIdentityClientId
      });
    } else {
      credentials.push(new ManagedIdentityCredential());
      logger.info('Added system-assigned managed identity to credential chain');
    }

    logger.info('Created development credential chain', {
      credentialCount: credentials.length,
      environment: 'development'
    });

    return new ChainedTokenCredential(...credentials);
  }

  /**
   * Test the credential by attempting to get a token
   */
  private async testCredential(): Promise<void> {
    if (!this.credential) {
      throw new Error('No credential configured');
    }

    try {
      // Test with Azure Resource Manager scope
      const token = await this.credential.getToken('https://management.azure.com/.default');
      if (!token) {
        throw new Error('Failed to obtain access token');
      }

      logger.info('Azure Identity credential test successful', {
        tokenExpiry: new Date(token.expiresOnTimestamp).toISOString(),
        environment: config.app.environment
      });
    } catch (error) {
      logger.error('Azure Identity credential test failed', {
        error: error instanceof Error ? error.message : String(error),
        environment: config.app.environment
      });

      // In development, we can be more forgiving - warn but don't fail completely
      if (config.app.environment === 'development') {
        logger.warn('Azure Identity authentication failed in development mode. Some features may not work properly.');
        logger.warn('To fix this, please run "az login" or configure service principal credentials.');
        // Don't throw in development - let the app start with limited functionality
        return;
      }

      // In production, this is a critical error
      throw new Error(`Azure Identity authentication failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get a token for a specific scope
   */
  public async getToken(scope: string): Promise<string> {
    if (!this.credential) {
      throw new Error('Azure Identity Service not initialized');
    }

    try {
      const token = await this.credential.getToken(scope);
      if (!token) {
        throw new Error(`Failed to obtain token for scope: ${scope}`);
      }
      return token.token;
    } catch (error) {
      logger.error('Failed to get token', {
        scope,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Check if the service is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.credential !== null;
  }

  /**
   * Get credential for specific Azure service
   */
  public getServiceCredential(serviceName: string): TokenCredential {
    if (!this.credential) {
      throw new Error(`Azure Identity Service not initialized for ${serviceName}`);
    }

    logger.debug('Providing credential for Azure service', { serviceName });
    return this.credential;
  }
}

// Export singleton instance
export const azureIdentityService = AzureIdentityService.getInstance();

// Export common Azure scopes
export const AzureScopes = {
  STORAGE: 'https://storage.azure.com/.default',
  COSMOS_DB: 'https://cosmos.azure.com/.default',
  SERVICE_BUS: 'https://servicebus.azure.net/.default',
  EVENT_GRID: 'https://eventgrid.azure.net/.default',
  COGNITIVE_SERVICES: 'https://cognitiveservices.azure.com/.default',
  SEARCH: 'https://search.azure.com/.default',
  SIGNALR: 'https://signalr.azure.com/.default',
  KEY_VAULT: 'https://vault.azure.net/.default',
  RESOURCE_MANAGER: 'https://management.azure.com/.default'
} as const;
