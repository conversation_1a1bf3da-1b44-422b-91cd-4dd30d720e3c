import type { Metadata, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { AppProviders } from '@/components/providers/app-providers';

import SentryInit from '@/components/sentry-init';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'HEPZ - Enterprise Document Management Platform',
  description: 'Production-ready document management with AI-powered features, real-time collaboration, and Azure AD B2C authentication',
  keywords: ['document management', 'AI', 'collaboration', 'Azure', 'Next.js', 'enterprise', 'productivity'],
  authors: [{ name: 'HEPZ Development Team' }],
  openGraph: {
    title: 'HEPZ - Enterprise Document Management Platform',
    description: 'Production-ready document management with AI-powered features',
    type: 'website',
  },
  robots: {
    index: false, // Don't index in production until ready
    follow: false,
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#000000" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/logo-145498266.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={inter.className}>
        <AppProviders>
          <SentryInit />
          {children}
        </AppProviders>
      </body>
    </html>
  );
}
