/**
 * PKI Digital Signature Service
 * Frontend service for PKI-enhanced digital signatures
 */

import { backendApiClient } from './backend-api-client';
import { memoryCache } from '../lib/cache';

// PKI Signature Types
export interface PKISignature {
  digitalSignature: string;
  certificate: string;
  certificateChain?: string[];
  algorithm: string;
  timestamp: string;
  hashAlgorithm: string;
  keyId: string;
}

export interface EnhancedDigitalSignature {
  id: string;
  documentId: string;
  signerId: string;
  signerEmail: string;
  signerName: string;
  visualSignature?: string;
  pkiSignature: PKISignature;
  documentHash: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  complianceLevel: 'basic' | 'advanced' | 'qualified';
  status: 'pending' | 'completed' | 'failed' | 'revoked';
  validationResults: string[];
  organizationId: string;
  certificateSerialNumber: string;
  revocationStatus?: 'valid' | 'revoked' | 'unknown';
}

export interface CertificateInfo {
  name: string;
  version: string;
  createdOn: string;
  expiresOn: string;
  enabled: boolean;
  tags: Record<string, string>;
}

export interface SignatureVerificationResult {
  signatureId: string;
  isValid: boolean;
  validationResults: string[];
  certificateStatus: 'valid' | 'expired' | 'revoked' | 'unknown';
  complianceLevel: 'basic' | 'advanced' | 'qualified';
  timestamp: string;
}

export interface CreatePKISignatureRequest {
  documentId: string;
  visualSignature?: string;
  algorithm?: 'RS256' | 'RS384' | 'RS512';
  profileName?: string;
  includeTimestamp?: boolean;
}

export interface CreatePKISignatureResponse {
  signatureId: string;
  operationId?: string;
  status: string;
  certificateInfo: CertificateInfo;
  validationResults: string[];
  complianceLevel: string;
  timestamp: string;
  legalValidity?: string;
}

// Removed Trusted Signing interfaces - using PKI as primary

class PKISignatureService {
  private readonly cachePrefix = 'pki-signature';
  private readonly cacheTTL = 300; // 5 minutes

  /**
   * Create PKI-enhanced digital signature (Primary Method)
   */
  async createPKISignature(request: CreatePKISignatureRequest): Promise<CreatePKISignatureResponse> {
    try {
      const response = await backendApiClient.request('/signatures/pki', {
        method: 'POST',
        body: JSON.stringify(request)
      });

      // Cache the signature for quick access
      if (response.signatureId) {
        const cacheKey = `${this.cachePrefix}:${response.signatureId}`;
        memoryCache.set(cacheKey, response, this.cacheTTL);
      }

      return response;
    } catch (error) {
      console.error('Failed to create PKI signature:', error);
      throw new Error('Failed to create PKI signature');
    }
  }

  /**
   * Create digital signature (Primary method using Azure Key Vault PKI)
   */
  async createSignature(request: CreatePKISignatureRequest): Promise<CreatePKISignatureResponse> {
    return await this.createPKISignature(request);
  }

  /**
   * Verify PKI signature
   */
  async verifyPKISignature(signatureId: string): Promise<SignatureVerificationResult> {
    try {
      // Check cache first
      const cacheKey = `${this.cachePrefix}:verify:${signatureId}`;
      const cached = memoryCache.get(cacheKey) as SignatureVerificationResult | null;
      if (cached) {
        return cached;
      }

      const response = await backendApiClient.request(`/signatures/pki/${signatureId}/verify`, {
        method: 'POST'
      });

      // Cache verification result
      memoryCache.set(cacheKey, response, this.cacheTTL);

      return response;
    } catch (error) {
      console.error('Failed to verify PKI signature:', error);
      throw new Error('Failed to verify PKI signature');
    }
  }

  /**
   * Get signature details
   */
  async getSignatureDetails(signatureId: string): Promise<EnhancedDigitalSignature> {
    try {
      // Check cache first
      const cacheKey = `${this.cachePrefix}:details:${signatureId}`;
      const cached = memoryCache.get(cacheKey) as EnhancedDigitalSignature | null;
      if (cached) {
        return cached;
      }

      const response = await backendApiClient.request(`/signatures/pki/${signatureId}`, {
        method: 'GET'
      });

      // Cache signature details
      memoryCache.set(cacheKey, response, this.cacheTTL);

      return response;
    } catch (error) {
      console.error('Failed to get signature details:', error);
      throw new Error('Failed to get signature details');
    }
  }

  /**
   * List user's PKI signatures
   */
  async listUserSignatures(documentId?: string): Promise<EnhancedDigitalSignature[]> {
    try {
      const params = new URLSearchParams();
      if (documentId) {
        params.append('documentId', documentId);
      }

      const response = await backendApiClient.request(`/signatures/pki?${params.toString()}`, {
        method: 'GET'
      });

      return response.signatures || [];
    } catch (error) {
      console.error('Failed to list PKI signatures:', error);
      throw new Error('Failed to list PKI signatures');
    }
  }

  /**
   * Get certificate information
   */
  async getCertificateInfo(certificateId: string): Promise<CertificateInfo> {
    try {
      // Check cache first
      const cacheKey = `${this.cachePrefix}:cert:${certificateId}`;
      const cached = memoryCache.get(cacheKey) as CertificateInfo | null;
      if (cached) {
        return cached;
      }

      const response = await backendApiClient.request(`/certificates/${certificateId}`, {
        method: 'GET'
      });

      // Cache certificate info for longer (certificates don't change often)
      memoryCache.set(cacheKey, response, 3600); // 1 hour

      return response;
    } catch (error) {
      console.error('Failed to get certificate info:', error);
      throw new Error('Failed to get certificate info');
    }
  }

  /**
   * Check certificate status (revocation, expiry)
   */
  async checkCertificateStatus(certificateId: string): Promise<{
    status: 'valid' | 'expired' | 'revoked' | 'unknown';
    expiresOn?: string;
    revokedOn?: string;
    reason?: string;
  }> {
    try {
      const response = await backendApiClient.request(`/certificates/${certificateId}/status`, {
        method: 'GET'
      });

      return response;
    } catch (error) {
      console.error('Failed to check certificate status:', error);
      return { status: 'unknown' };
    }
  }

  /**
   * Download signature certificate
   */
  async downloadCertificate(signatureId: string): Promise<Blob> {
    try {
      const response = await fetch(`${(backendApiClient as any).baseURL}/signatures/pki/${signatureId}/certificate`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${(backendApiClient as any).getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to download certificate');
      }

      return await response.blob();
    } catch (error) {
      console.error('Failed to download certificate:', error);
      throw new Error('Failed to download certificate');
    }
  }

  /**
   * Validate signature compliance level
   */
  getComplianceInfo(complianceLevel: string): {
    level: string;
    description: string;
    legalValidity: string;
    standards: string[];
  } {
    switch (complianceLevel) {
      case 'basic':
        return {
          level: 'Basic',
          description: 'Simple electronic signature with basic authentication',
          legalValidity: 'Limited legal validity',
          standards: ['ESIGN Act (US)']
        };
      case 'advanced':
        return {
          level: 'Advanced',
          description: 'PKI-based signature with certificate authentication',
          legalValidity: 'Strong legal validity',
          standards: ['eIDAS (EU)', 'ESIGN Act (US)', 'UETA (US)']
        };
      case 'qualified':
        return {
          level: 'Qualified',
          description: 'Qualified electronic signature with QSCD',
          legalValidity: 'Equivalent to handwritten signature',
          standards: ['eIDAS Article 25 (EU)', 'ZertES (Switzerland)']
        };
      default:
        return {
          level: 'Unknown',
          description: 'Unknown compliance level',
          legalValidity: 'Unknown',
          standards: []
        };
    }
  }

  /**
   * Format validation results for display
   */
  formatValidationResults(validationResults: string[]): Array<{
    check: string;
    status: 'passed' | 'failed' | 'warning';
    description: string;
  }> {
    const resultMap: Record<string, { status: 'passed' | 'failed' | 'warning'; description: string }> = {
      'certificate_format_valid': {
        status: 'passed',
        description: 'Certificate format is valid'
      },
      'signature_format_valid': {
        status: 'passed',
        description: 'Digital signature format is valid'
      },
      'timestamp_valid': {
        status: 'passed',
        description: 'Signature timestamp is valid'
      },
      'timestamp_invalid': {
        status: 'failed',
        description: 'Signature timestamp is invalid or suspicious'
      },
      'algorithm_secure': {
        status: 'passed',
        description: 'Cryptographic algorithm is secure'
      },
      'algorithm_weak': {
        status: 'failed',
        description: 'Cryptographic algorithm is weak or deprecated'
      },
      'certificate_expired': {
        status: 'failed',
        description: 'Certificate has expired'
      },
      'certificate_revoked': {
        status: 'failed',
        description: 'Certificate has been revoked'
      },
      'verification_error': {
        status: 'failed',
        description: 'Signature verification failed'
      }
    };

    return validationResults.map(result => ({
      check: result,
      ...resultMap[result] || {
        status: 'warning' as const,
        description: `Unknown validation result: ${result}`
      }
    }));
  }

  /**
   * Clear signature cache
   */
  clearCache(signatureId?: string): void {
    if (signatureId) {
      memoryCache.delete(`${this.cachePrefix}:${signatureId}`);
      memoryCache.delete(`${this.cachePrefix}:verify:${signatureId}`);
      memoryCache.delete(`${this.cachePrefix}:details:${signatureId}`);
    } else {
      // Clear all PKI signature cache
      (memoryCache as any).clear(this.cachePrefix);
    }
  }
}

// Export singleton instance
export const pkiSignatureService = new PKISignatureService();
export default pkiSignatureService;
