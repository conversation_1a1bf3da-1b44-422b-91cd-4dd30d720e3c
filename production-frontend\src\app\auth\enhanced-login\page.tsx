'use client'

/**
 * Enhanced Login Page
 * Production-ready authentication with Azure AD B2C supporting all user flows
 */

import { useSearchParams } from 'next/navigation'
import { EnhancedLoginComponent } from '@/components/auth/enhanced-login-component'

export default function EnhancedLoginPage() {
  const searchParams = useSearchParams()

  const tab = searchParams.get('tab') as 'signin' | 'signup' | 'reset' | 'profile' || 'signin'
  const returnUrl = searchParams.get('returnUrl') || '/dashboard'

  return (
    <EnhancedLoginComponent
      defaultTab={tab}
      redirectUrl={returnUrl}
      showTabs={true}
      title="Welcome to HEPZ"
      subtitle="Complete document management platform"
    />
  )
}
