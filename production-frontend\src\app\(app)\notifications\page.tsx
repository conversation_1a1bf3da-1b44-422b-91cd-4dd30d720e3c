"use client";

import { useState } from "react";
import { NotificationCenter } from "@/components/notifications/notification-center";
import { PageHeader } from "@/components/ui/page-header";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Bell,
  Settings,
  Clock,
  Trash2,
  Archive,
  Filter,
  SlidersHorizontal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import notificationService from "@/services/notification-service";

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState<string>("all");
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);
  const [notificationPreferences, setNotificationPreferences] = useState({
    email: true,
    inApp: true,
    push: true,
    documentUploaded: true,
    documentProcessed: true,
    commentAdded: true,
    mentionedInComment: true,
    workflowAssigned: true,
    workflowCompleted: true,
    projectInvitation: true,
    organizationInvitation: true
  });

  const { toast } = useToast();

  // Handle clearing notifications
  const handleClearNotifications = async () => {
    try {
      await notificationService.deleteAllNotifications();

      toast({
        title: "Success",
        description: "All notifications have been cleared",
      });
    } catch (error) {
      console.error("Failed to clear notifications:", error);

      toast({
        title: "Error",
        description: "Failed to clear notifications. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle archiving notifications
  const handleArchiveNotifications = async () => {
    try {
      await notificationService.archiveOldNotifications(0); // Archive all notifications

      toast({
        title: "Success",
        description: "All notifications have been archived",
      });
    } catch (error) {
      console.error("Failed to archive notifications:", error);

      toast({
        title: "Error",
        description: "Failed to archive notifications. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle saving notification preferences
  const handleSavePreferences = async () => {
    try {
      await notificationService.updatePreferences(notificationPreferences as any);

      setIsSettingsOpen(false);

      toast({
        title: "Success",
        description: "Notification preferences have been updated",
      });
    } catch (error) {
      console.error("Failed to update notification preferences:", error);

      toast({
        title: "Error",
        description: "Failed to update notification preferences. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container py-6 space-y-6">
      <PageHeader
        title="Notifications"
        description="Manage your notifications and preferences"
        actions={
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <SlidersHorizontal className="h-4 w-4 mr-2" />
                  Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setIsSettingsOpen(true)}>
                  <Settings className="h-4 w-4 mr-2" />
                  Notification Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleArchiveNotifications}>
                  <Archive className="h-4 w-4 mr-2" />
                  Archive All
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleClearNotifications}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Notification Settings</DialogTitle>
                  <DialogDescription>
                    Customize how and when you receive notifications.
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6 py-4">
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Notification Channels</h4>
                    <div className="grid gap-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="in-app">In-App Notifications</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive notifications within the application
                          </p>
                        </div>
                        <Switch
                          id="in-app"
                          checked={notificationPreferences.inApp}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, inApp: checked }))
                          }
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="email">Email Notifications</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive notifications via email
                          </p>
                        </div>
                        <Switch
                          id="email"
                          checked={notificationPreferences.email}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, email: checked }))
                          }
                        />
                      </div>

                      <Separator />

                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="push">Push Notifications</Label>
                          <p className="text-sm text-muted-foreground">
                            Receive push notifications on your devices
                          </p>
                        </div>
                        <Switch
                          id="push"
                          checked={notificationPreferences.push}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, push: checked }))
                          }
                        />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Notification Types</h4>
                    <div className="grid gap-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="document-processed">Document Processing</Label>
                        <Switch
                          id="document-processed"
                          checked={notificationPreferences.documentProcessed}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, documentProcessed: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="workflow-assigned">Workflow Assignments</Label>
                        <Switch
                          id="workflow-assigned"
                          checked={notificationPreferences.workflowAssigned}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, workflowAssigned: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="comment-added">Comments</Label>
                        <Switch
                          id="comment-added"
                          checked={notificationPreferences.commentAdded}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, commentAdded: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="mentioned">Mentions</Label>
                        <Switch
                          id="mentioned"
                          checked={notificationPreferences.mentionedInComment}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({ ...prev, mentionedInComment: checked }))
                          }
                        />
                      </div>

                      <div className="flex items-center justify-between">
                        <Label htmlFor="invitations">Invitations</Label>
                        <Switch
                          id="invitations"
                          checked={notificationPreferences.projectInvitation && notificationPreferences.organizationInvitation}
                          onCheckedChange={(checked) =>
                            setNotificationPreferences(prev => ({
                              ...prev,
                              projectInvitation: checked,
                              organizationInvitation: checked
                            }))
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsSettingsOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSavePreferences}>
                    Save Changes
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        }
      />

      <Card>
        <CardContent className="p-6">
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
            <div className="flex items-center justify-between mb-6">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="unread">Unread</TabsTrigger>
                <TabsTrigger value="archived">Archived</TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2">
                <Select defaultValue="7days">
                  <SelectTrigger className="w-[180px]">
                    <Clock className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Time Period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="7days">Last 7 Days</SelectItem>
                    <SelectItem value="30days">Last 30 Days</SelectItem>
                    <SelectItem value="all">All Time</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <TabsContent value={activeTab} className="mt-0">
              <NotificationCenter
                showBadge={false}
                showPopover={false}
                maxNotifications={100}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
