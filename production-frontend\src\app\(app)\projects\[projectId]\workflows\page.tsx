'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useProject } from '@/hooks/projects/useProject';
import { useProjectWorkflows } from '@/hooks/workflows/useWorkflows';
import { Plus, Search } from 'lucide-react';
import { WorkflowCard } from '@/components/workflows/workflow-card';
import { EmptyState } from '@/components/empty-state';

export default function ProjectWorkflowsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();

  // Ensure params exists and get project ID
  if (!params || !params.projectId) {
    return <div>Loading...</div>;
  }

  const projectId = Array.isArray(params.projectId) ? params.projectId[0] : params.projectId;
  const [searchQuery, setSearchQuery] = useState('');

  const { project: _project, isLoading: projectLoading, error: projectError } = useProject({ projectId });
  const { data: workflows, isLoading: workflowsLoading, error: workflowsError } = useProjectWorkflows(projectId);

  useEffect(() => {
    if (projectError || workflowsError) {
      toast({
        title: 'Error',
        description: 'Failed to load project workflows',
        variant: 'destructive',
      });
    }
  }, [projectError, workflowsError, toast]);

  const isLoading = projectLoading || workflowsLoading;

  // Filter workflows by search query
  const filteredWorkflows = searchQuery && workflows
    ? workflows.filter(workflow =>
        workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        workflow.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : workflows || [];

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{_project?.name} Workflows</h1>
          <p className="text-muted-foreground">Manage document workflows for this project</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/projects/${projectId}`)}
          >
            Back to Project
          </Button>
          <Button
            onClick={() => router.push(`/projects/${projectId}/workflows/create`)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Workflow
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search workflows..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-muted rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-muted rounded w-full mb-2"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredWorkflows.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkflows.map((workflow) => (
            <WorkflowCard
              key={workflow.id}
              workflow={workflow as any}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          title="No workflows found"
          description={searchQuery ? "Try a different search term" : "Create your first workflow to get started"}
          action={
            <Button onClick={() => router.push(`/projects/${projectId}/workflows/create`)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Workflow
            </Button>
          }
        />
      )}
    </div>
  );
}
