"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import { useCreateExport } from "@/hooks/exports/useExports";
import { ExportFormat, ExportType } from "@/services/export-service";

// Form validation schema
const exportFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  format: z.nativeEnum(ExportFormat),
  includeHeaders: z.boolean().default(true),
});

type ExportFormValues = z.infer<typeof exportFormSchema>;

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  exportType: string;
}

export function ExportDialog({ open, onOpenChange, exportType }: ExportDialogProps) {
  const { toast } = useToast();
  const { mutate: createExport, isPending } = useCreateExport();
  
  // Initialize form
  const form = useForm<ExportFormValues>({
    resolver: zodResolver(exportFormSchema),
    defaultValues: {
      name: `${exportType.charAt(0).toUpperCase() + exportType.slice(1).toLowerCase()} Export - ${new Date().toLocaleDateString()}`,
      description: "",
      format: ExportFormat.CSV,
      includeHeaders: true,
    },
  });

  // Form submission handler
  const onSubmit = (data: ExportFormValues) => {
    createExport({
      ...data,
      type: exportType as ExportType,
    }, {
      onSuccess: () => {
        onOpenChange(false);
        form.reset();
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Export {exportType.toLowerCase()}</DialogTitle>
          <DialogDescription>
            Create an export of your {exportType.toLowerCase()} data in various formats.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Export Name</FormLabel>
                  <FormControl>
                    <Input placeholder="My Export" {...field} />
                  </FormControl>
                  <FormDescription>
                    A name to identify this export
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Export description" {...field} />
                  </FormControl>
                  <FormDescription>
                    A brief description of this export
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="format"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Export Format</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select format" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value={ExportFormat.CSV}>CSV</SelectItem>
                      <SelectItem value={ExportFormat.EXCEL}>Excel</SelectItem>
                      <SelectItem value={ExportFormat.PDF}>PDF</SelectItem>
                      <SelectItem value={ExportFormat.JSON}>JSON</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The file format for the exported data
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="includeHeaders"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Include Headers</FormLabel>
                    <FormDescription>
                      Include column headers in the exported file
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? "Creating..." : "Create Export"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
