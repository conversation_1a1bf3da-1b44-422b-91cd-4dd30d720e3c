"use client";

import React, { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { useDashboardStore } from "@/stores/dashboard-store";
import {
  useAIOperationsSummary,
  useSessionStatus
} from "@/stores";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import {
  Building2,
  FolderKanban,
  FileText,
  Plus,
  ArrowRight,
  AlertCircle,
  RefreshCw,
  Loader2,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MessageCircle,
  Cpu,
  Users,
  Activity,
  BarChart3,
  Search
} from "lucide-react";
import { OrganizationCard } from "@/components/organizations/organization-card";
import { ProjectCard } from "@/components/projects/project-card";
import { DocumentCard } from "@/components/documents/document-card";
import { EmptyState } from "@/components/empty-state";
// Lazy load dashboard widgets for better performance
import { Suspense, lazy } from "react";

const PerformanceWidget = lazy(() => import("@/components/dashboard/performance-widget").then(module => ({ default: module.PerformanceWidget })));
const AIInsightsWidget = lazy(() => import("@/components/dashboard/ai-insights-widget").then(module => ({ default: module.AIInsightsWidget })));
const SearchAnalyticsWidget = lazy(() => import("@/components/dashboard/search-analytics-widget").then(module => ({ default: module.SearchAnalyticsWidget })));

// Error boundary for dashboard widgets
class DashboardWidgetErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard widget error:', error, errorInfo);
    logger.error(`Dashboard widget error: ${error.message}`, error.stack);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Widget Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              This widget failed to load. Please refresh the page.
            </p>
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}
import { GlobalSearch } from "@/components/search/global-search";
import { AIChat } from "@/components/ai/ai-chat";
import { FloatingActionButton } from "@/components/ui/floating-action-button";
import { OnboardingTour, dashboardTourSteps, useOnboarding } from "@/components/onboarding/onboarding-tour";
import { AuthDebug } from "@/components/auth-debug";
import { DashboardLoadingTest } from "@/components/dashboard/dashboard-loading-test";

import { logger } from "@/lib/logger";
import { errorMonitoring } from "@/lib/error-monitoring";

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [retryCount, setRetryCount] = useState(0);
  const [isInitializing, setIsInitializing] = useState(true);
  const { toast } = useToast();

  // Debug auth state - Enhanced debugging
  console.log('[Dashboard] Auth state:', {
    isAuthenticated,
    authLoading,
    user: !!user,
    userDetails: user ? { id: user.id, email: user.email, name: user.displayName } : null
  });

  // Enhanced UX state management
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [selectedChatContext, setSelectedChatContext] = useState<{
    documentIds?: string[]
    projectId?: string
    organizationId?: string
  } | undefined>(undefined);

  // New store hooks for AI and collaboration
  const aiSummary = useAIOperationsSummary();
  const sessionStatus = useSessionStatus();
  // Removed unused storeUser import

  // Set up logging context
  useEffect(() => {
    logger.info('Dashboard page loaded', `isAuthenticated: ${isAuthenticated}, authLoading: ${authLoading}, activeTab: ${activeTab}`);
  }, [user, isAuthenticated, authLoading, activeTab]);

  // Remove B2C initialization - handled by useAuth hook
  useEffect(() => {
    setIsInitializing(false)
  }, []);

  // Enhanced UX: Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd/Ctrl + K for search
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        setIsSearchOpen(true);
      }
      // Cmd/Ctrl + J for AI chat
      if ((event.metaKey || event.ctrlKey) && event.key === 'j') {
        event.preventDefault();
        setIsChatOpen(true);
      }
      // Escape to close modals
      if (event.key === 'Escape') {
        setIsSearchOpen(false);
        setIsChatOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Enhanced UX: Smart context setting for AI chat
  const openChatWithContext = useCallback((context: typeof selectedChatContext) => {
    setSelectedChatContext(context);
    setIsChatOpen(true);
  }, []);

  // Onboarding management
  const { hasCompletedOnboarding, markOnboardingComplete } = useOnboarding();

  // Only fetch data if user is authenticated and not loading
  const shouldFetchData = isAuthenticated && !authLoading && user;

  // Debug shouldFetchData
  console.log('[Dashboard] shouldFetchData:', shouldFetchData, {
    isAuthenticated,
    authLoading,
    hasUser: !!user
  });

  // Additional debugging for auth state
  useEffect(() => {
    console.log('[Dashboard] Auth state changed:', {
      isAuthenticated,
      authLoading,
      user: user ? {
        id: user.id,
        email: user.email,
        displayName: user.displayName
      } : null,
      shouldFetchData
    });
  }, [isAuthenticated, authLoading, user, shouldFetchData]);





  // Use Zustand store for dashboard data
  const {
    organizations,
    organizationsLoading: isLoadingOrgs,
    organizationsError: orgsError,
    projects,
    projectsLoading: isLoadingProjects,
    projectsError,
    documents,
    documentsLoading: isLoadingDocuments,
    documentsError,
    fetchOrganizations,
    fetchProjects,
    fetchDocuments,
    clearError
  } = useDashboardStore();

  // Debug dashboard store state
  useEffect(() => {
    console.log('[Dashboard] Store state:', {
      organizations: organizations.length,
      isLoadingOrgs,
      orgsError,
      projects: projects.length,
      isLoadingProjects,
      projectsError,
      documents: documents.length,
      isLoadingDocuments,
      documentsError
    });
  }, [organizations, isLoadingOrgs, orgsError, projects, isLoadingProjects, projectsError, documents, isLoadingDocuments, documentsError]);

  // Get the first organization and project for documents
  const firstOrg = organizations.length > 0 ? organizations[0] : null;
  const firstProject = projects.length > 0 ? projects[0] : null;

  // Test backend sync when authenticated
  useEffect(() => {
    if (shouldFetchData) {
      console.log('[Dashboard] Testing backend sync...');

      // Import and call syncUserWithBackend directly
      import('@/stores/auth-store').then(({ useAuthStore }) => {
        const { syncUserWithBackend } = useAuthStore.getState();
        syncUserWithBackend().then(() => {
          console.log('[Dashboard] Backend sync test completed');
        }).catch(error => {
          console.error('[Dashboard] Backend sync test failed:', error);
        });
      });
    }
  }, [shouldFetchData]);

  // Fetch data when authenticated
  useEffect(() => {
    console.log('[Dashboard] Organizations fetch effect triggered:', { shouldFetchData });
    if (shouldFetchData) {
      logger.info('Fetching dashboard data');
      console.log('[Dashboard] Calling fetchOrganizations...');
      fetchOrganizations();
    } else {
      console.log('[Dashboard] Not fetching organizations - shouldFetchData is false');
    }
  }, [shouldFetchData, fetchOrganizations]);

  // Fetch projects when organizations are available
  useEffect(() => {
    console.log('[Dashboard] Projects fetch effect triggered:', { shouldFetchData, firstOrg: !!firstOrg });
    if (shouldFetchData && firstOrg) {
      logger.info(`Fetching projects for organization: ${firstOrg.id}`);
      console.log('[Dashboard] Calling fetchProjects...');
      fetchProjects(firstOrg.id);
    } else {
      console.log('[Dashboard] Not fetching projects - conditions not met');
    }
  }, [shouldFetchData, firstOrg, fetchProjects]);

  // Fetch documents when project and organization are available
  useEffect(() => {
    console.log('[Dashboard] Documents fetch effect triggered:', { shouldFetchData, firstProject: !!firstProject, firstOrg: !!firstOrg });
    if (shouldFetchData && firstProject && firstOrg) {
      logger.info(`Fetching documents for project: ${firstProject.id}, organization: ${firstOrg.id}`);
      console.log('[Dashboard] Calling fetchDocuments...');
      fetchDocuments(firstProject.id, firstOrg.id);
    } else {
      console.log('[Dashboard] Not fetching documents - conditions not met');
    }
  }, [shouldFetchData, firstProject, firstOrg, fetchDocuments]);

  // Enhanced error logging and monitoring
  useEffect(() => {
    if (orgsError) {
      logger.error(`Organizations fetch error: ${orgsError}, retryCount: ${retryCount}`);
      errorMonitoring.captureException(new Error(orgsError), {
        component: 'DashboardPage',
        userId: user?.id
      });
    }
    if (projectsError) {
      logger.error(`Projects fetch error: ${projectsError}, retryCount: ${retryCount}`);
      errorMonitoring.captureException(new Error(projectsError), {
        component: 'DashboardPage',
        userId: user?.id
      });
    }
    if (documentsError) {
      logger.error(`Documents fetch error: ${documentsError}, retryCount: ${retryCount}`);
      errorMonitoring.captureException(new Error(documentsError), {
        component: 'DashboardPage',
        userId: user?.id
      });
    }
  }, [orgsError, projectsError, documentsError, retryCount, user?.id]);

  // Handle document download
  const handleDownloadDocument = useCallback(async (document: any) => {
    try {
      logger.info(`Document download initiated: ${document.id}`);

      const response = await fetch(`/documents/${document.id}`);

      if (!response.ok) {
        throw new Error('Failed to download document');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = document.name || `document-${document.id}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Download Started",
        description: "Document download has started.",
      });
    } catch (error) {
      logger.error(`Document download failed: ${error}, documentId: ${document.id}`);
      toast({
        title: "Download Failed",
        description: "Unable to download document. Please try again.",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Enhanced retry mechanism
  const handleRetry = useCallback(async (section: 'organizations' | 'projects' | 'documents') => {
    try {
      setRetryCount(prev => prev + 1);
      logger.info(`Retrying ${section} fetch, retryCount: ${retryCount + 1}`);

      // Clear the error first
      clearError();

      switch (section) {
        case 'organizations':
          await fetchOrganizations();
          break;
        case 'projects':
          await fetchProjects();
          break;
        case 'documents':
          await fetchDocuments();
          break;
      }

      // Only show success toast if retry count is high (user manually retried multiple times)
      if (retryCount > 1) {
        toast({
          title: "Retry Successful",
          description: `${section} data refreshed successfully.`,
        });
      }
    } catch (error) {
      logger.error(`Retry failed for ${section}: ${error}, retryCount: ${retryCount}`);
      // Only show error toast if user manually retried (not automatic retries)
      if (retryCount > 0) {
        toast({
          title: "Retry Failed",
          description: `Unable to refresh ${section}. Please check your connection.`,
          variant: "destructive"
        });
      }
    }
  }, [fetchOrganizations, fetchProjects, fetchDocuments, firstOrg, firstProject, retryCount, toast, clearError]);

  // Enhanced Error Alert Component with user-friendly messages
  const ErrorAlert = ({ error, onRetry, title }: {
    error: string;
    onRetry: () => void;
    title: string;
  }) => {
    // Convert technical errors to user-friendly messages
    const getUserFriendlyMessage = (error: string) => {
      if (error.includes('Network Error') || error.includes('ERR_NETWORK')) {
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      }
      if (error.includes('500') || error.includes('Internal Server Error')) {
        return 'The server is temporarily unavailable. Please try again in a few moments.';
      }
      if (error.includes('401') || error.includes('Unauthorized')) {
        return 'Your session has expired. Please refresh the page and sign in again.';
      }
      if (error.includes('403') || error.includes('Forbidden')) {
        return 'You don\'t have permission to access this data.';
      }
      if (error.includes('404') || error.includes('Not Found')) {
        return 'The requested data could not be found.';
      }
      if (error.includes('timeout') || error.includes('TIMEOUT')) {
        return 'The request took too long to complete. Please try again.';
      }
      // For any other error, show a generic message
      return 'Something went wrong while loading this data. Please try again.';
    };

    return (
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <div className="flex-1">
            <p className="font-medium">{title}</p>
            <p className="text-sm text-muted-foreground mt-1">
              {getUserFriendlyMessage(error)}
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            disabled={isLoadingOrgs || isLoadingProjects || isLoadingDocuments}
          >
            {(isLoadingOrgs || isLoadingProjects || isLoadingDocuments) ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  };

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64 mt-2" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-40 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Show loading while checking authentication or initializing B2C
  if (authLoading || isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Loading...</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <p className="text-muted-foreground">
                {authLoading ? 'Checking authentication...' : 'Initializing session...'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // The (app) layout will handle authentication redirects
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user.displayName || user.firstName || user.lastName || user.email}
          </p>
        </div>

        {/* Enhanced Quick Actions */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsSearchOpen(true)}
            className="hidden md:flex"
            data-tour="global-search"
          >
            <Search className="h-4 w-4 mr-2" />
            Search
            <kbd className="ml-2 pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
              <span className="text-xs">⌘</span>K
            </kbd>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsChatOpen(true)}
            className="hidden md:flex"
            data-tour="ai-chat"
          >
            <Brain className="h-4 w-4 mr-2" />
            AI Chat
            <kbd className="ml-2 pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
              <span className="text-xs">⌘</span>J
            </kbd>
          </Button>

          {/* Mobile quick actions */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsSearchOpen(true)}
            className="md:hidden"
          >
            <Search className="h-4 w-4" />
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsChatOpen(true)}
            className="md:hidden"
          >
            <Brain className="h-4 w-4" />
          </Button>
        </div>
      </div>



      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 lg:grid-cols-9">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="ai-operations">AI Operations</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="organizations">Organizations</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="debug">Debug</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Organizations Section */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-muted-foreground" />
                  <CardTitle className="text-lg">Organizations</CardTitle>
                  {organizations.length > 0 && (
                    <span className="text-sm text-muted-foreground">({organizations.length})</span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/organizations/create">
                      <Plus className="mr-2 h-4 w-4" />
                      Create
                    </Link>
                  </Button>
                  {organizations.length > 0 && (
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/organizations">
                        View all
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {orgsError ? (
                <ErrorAlert
                  error={orgsError}
                  onRetry={() => handleRetry('organizations')}
                  title="Failed to load organizations"
                />
              ) : isLoadingOrgs ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-40 w-full" />
                  ))}
                </div>
              ) : organizations && organizations.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {organizations.slice(0, 3).map((org: any) => (
                    <OrganizationCard
                      key={org.id}
                      organization={org}
                    />
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={<Building2 className="h-12 w-12 text-muted-foreground" />}
                  title="No organizations yet"
                  description="Organizations help you manage teams and projects. Create your first organization to get started."
                  action={
                    <Button asChild size="lg">
                      <Link href="/organizations/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Your First Organization
                      </Link>
                    </Button>
                  }
                />
              )}
            </CardContent>
          </Card>

          {/* Projects Section */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FolderKanban className="h-5 w-5 text-muted-foreground" />
                  <CardTitle className="text-lg">Recent Projects</CardTitle>
                  {projects.length > 0 && (
                    <span className="text-sm text-muted-foreground">({projects.length})</span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {organizations.length > 0 ? (
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/projects/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create
                      </Link>
                    </Button>
                  ) : (
                    <Button variant="outline" size="sm" disabled>
                      <Plus className="mr-2 h-4 w-4" />
                      Create
                    </Button>
                  )}
                  {projects.length > 0 && (
                    <Button variant="ghost" size="sm" asChild>
                      <Link href="/projects">
                        View all
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {projectsError ? (
                <ErrorAlert
                  error={projectsError}
                  onRetry={() => handleRetry('projects')}
                  title="Failed to load projects"
                />
              ) : isLoadingProjects ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-40 w-full" />
                  ))}
                </div>
              ) : projects && projects.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {projects.slice(0, 3).map((project: any) => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                    />
                  ))}
                </div>
              ) : organizations.length === 0 ? (
                <EmptyState
                  icon={<Building2 className="h-12 w-12 text-muted-foreground" />}
                  title="Create an organization first"
                  description="You need to create an organization before you can create projects."
                  action={
                    <Button asChild size="lg">
                      <Link href="/organizations/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Organization
                      </Link>
                    </Button>
                  }
                />
              ) : (
                <EmptyState
                  icon={<FolderKanban className="h-12 w-12 text-muted-foreground" />}
                  title="No projects yet"
                  description="Projects help you organize your documents and workflows. Create your first project to get started."
                  action={
                    <Button asChild size="lg">
                      <Link href="/projects/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Your First Project
                      </Link>
                    </Button>
                  }
                />
              )}
            </CardContent>
          </Card>

          {/* Documents Section */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <CardTitle className="text-lg">Recent Documents</CardTitle>
                  {documents.length > 0 && (
                    <span className="text-sm text-muted-foreground">({documents.length})</span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {firstProject ? (
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/projects/${firstProject.id}/documents/upload`}>
                        <Plus className="mr-2 h-4 w-4" />
                        Upload
                      </Link>
                    </Button>
                  ) : projects.length > 0 ? (
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/projects">
                        <Plus className="mr-2 h-4 w-4" />
                        Upload
                      </Link>
                    </Button>
                  ) : (
                    <Button variant="outline" size="sm" disabled>
                      <Plus className="mr-2 h-4 w-4" />
                      Upload
                    </Button>
                  )}
                  {documents.length > 0 && firstProject && (
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/projects/${firstProject.id}/documents`}>
                        View all
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {documentsError ? (
                <ErrorAlert
                  error={documentsError}
                  onRetry={() => handleRetry('documents')}
                  title="Failed to load documents"
                />
              ) : isLoadingDocuments ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-40 w-full" />
                  ))}
                </div>
              ) : documents && documents.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {documents.slice(0, 3).map((document: any) => (
                    <Link
                      key={document.id}
                      href={`/projects/${document.projectId}/documents/${document.id}`}
                    >
                      <DocumentCard
                        document={document}
                        onDownload={handleDownloadDocument}
                      />
                    </Link>
                  ))}
                </div>
              ) : projects.length === 0 ? (
                <EmptyState
                  icon={<FolderKanban className="h-12 w-12 text-muted-foreground" />}
                  title="Create a project first"
                  description="You need to create a project before you can upload documents."
                  action={
                    organizations.length > 0 ? (
                      <Button asChild size="lg">
                        <Link href="/projects/create">
                          <Plus className="mr-2 h-4 w-4" />
                          Create Project
                        </Link>
                      </Button>
                    ) : (
                      <Button asChild size="lg">
                        <Link href="/organizations/create">
                          <Plus className="mr-2 h-4 w-4" />
                          Create Organization
                        </Link>
                      </Button>
                    )
                  }
                />
              ) : (
                <EmptyState
                  icon={<FileText className="h-12 w-12 text-muted-foreground" />}
                  title="No documents yet"
                  description="Upload your first document to start processing and managing your files."
                  action={
                    <Button asChild size="lg">
                      <Link href={firstProject ? `/projects/${firstProject.id}/documents/upload` : "/projects"}>
                        <Plus className="mr-2 h-4 w-4" />
                        Upload Your First Document
                      </Link>
                    </Button>
                  }
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Enhanced Analytics Dashboard */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Performance Metrics */}
            <div className="lg:col-span-1">
              <DashboardWidgetErrorBoundary>
                <Suspense fallback={
                  <Card>
                    <CardHeader>
                      <Skeleton className="h-6 w-32" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-32 w-full" />
                    </CardContent>
                  </Card>
                }>
                  <PerformanceWidget />
                </Suspense>
              </DashboardWidgetErrorBoundary>
            </div>

            {/* AI Insights */}
            <div className="lg:col-span-1">
              <DashboardWidgetErrorBoundary>
                <Suspense fallback={
                  <Card>
                    <CardHeader>
                      <Skeleton className="h-6 w-32" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-32 w-full" />
                    </CardContent>
                  </Card>
                }>
                  <AIInsightsWidget />
                </Suspense>
              </DashboardWidgetErrorBoundary>
            </div>

            {/* Search Analytics */}
            <div className="lg:col-span-1">
              <DashboardWidgetErrorBoundary>
                <Suspense fallback={
                  <Card>
                    <CardHeader>
                      <Skeleton className="h-6 w-32" />
                    </CardHeader>
                    <CardContent>
                      <Skeleton className="h-32 w-full" />
                    </CardContent>
                  </Card>
                }>
                  <SearchAnalyticsWidget />
                </Suspense>
              </DashboardWidgetErrorBoundary>
            </div>
          </div>

          {/* Additional Analytics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{documents.length}</div>
                <p className="text-xs text-muted-foreground">
                  +{Math.floor(documents.length * 0.1)} from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{projects.length}</div>
                <p className="text-xs text-muted-foreground">
                  +{Math.floor(projects.length * 0.2)} from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">AI Operations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{aiSummary.total}</div>
                <p className="text-xs text-muted-foreground">
                  {Math.round((aiSummary.completed / Math.max(aiSummary.total, 1)) * 100)}% success rate
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Collaboration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{sessionStatus.participantCount}</div>
                <p className="text-xs text-muted-foreground">
                  Active participants
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ai-operations" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            {/* AI Operations Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Cpu className="mr-2 h-5 w-5" />
                  AI Operations Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {aiSummary.completed}
                    </div>
                    <div className="text-xs text-muted-foreground">Completed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {aiSummary.processing}
                    </div>
                    <div className="text-xs text-muted-foreground">Processing</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {aiSummary.pending}
                    </div>
                    <div className="text-xs text-muted-foreground">Pending</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {aiSummary.failed}
                    </div>
                    <div className="text-xs text-muted-foreground">Failed</div>
                  </div>
                </div>

                {aiSummary.total > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Success Rate</span>
                      <span>{Math.round((aiSummary.completed / aiSummary.total) * 100)}%</span>
                    </div>
                    <Progress
                      value={(aiSummary.completed / aiSummary.total) * 100}
                      className="h-2"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* AI Models */}
            <Card>
              <CardHeader>
                <CardTitle>AI Models</CardTitle>
                <CardDescription>
                  Available AI models and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Document Analysis</span>
                    <Badge variant="default">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Form Processing</span>
                    <Badge variant="default">Active</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Content Generation</span>
                    <Badge variant="secondary">Training</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Classification</span>
                    <Badge variant="default">Active</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick AI Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick AI Actions</CardTitle>
              <CardDescription>
                Start AI operations on your documents
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" variant="outline">
                <Brain className="mr-2 h-4 w-4" />
                Analyze Documents
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Process Forms
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Activity className="mr-2 h-4 w-4" />
                Batch Processing
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <BarChart3 className="mr-2 h-4 w-4" />
                Generate Reports
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="collaboration" className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Collaboration Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="mr-2 h-5 w-5" />
                  Collaboration Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Connection Status</span>
                  <Badge variant={sessionStatus.isConnected ? "default" : "destructive"}>
                    {sessionStatus.isConnected ? "Connected" : "Disconnected"}
                  </Badge>
                </div>

                {sessionStatus.hasActiveSession && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Session</span>
                      <Badge variant="default">Yes</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Participants</span>
                      <span className="text-sm font-medium">
                        {sessionStatus.participantCount}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Users</span>
                      <span className="text-sm font-medium">
                        {sessionStatus.activeUserCount}
                      </span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Recent Collaborations */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Collaborations</CardTitle>
                <CardDescription>
                  Your recent collaboration sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm text-muted-foreground text-center py-4">
                    No recent collaboration sessions
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Collaboration Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Start Collaboration</CardTitle>
              <CardDescription>
                Begin real-time collaboration on documents
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button className="w-full justify-start" variant="outline">
                <MessageSquare className="mr-2 h-4 w-4" />
                Start New Session
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Users className="mr-2 h-4 w-4" />
                Join Existing Session
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Share Document
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="organizations">
          {/* Organizations Tab Content */}
        </TabsContent>

        <TabsContent value="projects">
          {/* Projects Tab Content */}
        </TabsContent>

        <TabsContent value="documents">
          {/* Documents Tab Content */}
          <div className="grid grid-cols-1 gap-6">
            {documentsError ? (
              <ErrorAlert
                error={documentsError}
                onRetry={() => handleRetry('documents')}
                title="Failed to load documents"
              />
            ) : isLoadingDocuments ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-40 w-full" />
                ))}
              </div>
            ) : documents && documents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {documents.map((document: any) => (
                  <Link
                    key={document.id}
                    href={`/projects/${document.projectId}/documents/${document.id}`}
                  >
                    <DocumentCard
                      document={document}
                      onDownload={handleDownloadDocument}
                    />
                  </Link>
                ))}
              </div>
            ) : (
              <EmptyState
                icon={<FileText className="h-10 w-10 text-muted-foreground" />}
                title="No documents"
                description="You don't have any documents yet. Upload your first document to get started."
                action={
                  <Button asChild>
                    {firstProject ? (
                      <Link href={`/projects/${firstProject.id}/documents/upload`}>
                        <Plus className="mr-2 h-4 w-4" />
                        Upload Document
                      </Link>
                    ) : (
                      <Link href="/projects/create">
                        <Plus className="mr-2 h-4 w-4" />
                        Create Project
                      </Link>
                    )}
                  </Button>
                }
              />
            )}
          </div>
        </TabsContent>

        <TabsContent value="activity">
          {/* Activity Tab Content - Temporarily disabled to prevent network errors */}
          <Card>
            <CardHeader>
              <CardTitle>Activity Feed</CardTitle>
              <CardDescription>
                Activity tracking is currently being optimized for better performance.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Activity feed will be available soon with improved caching and performance.
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="debug" className="space-y-6">
          <DashboardLoadingTest />
          <AuthDebug />
        </TabsContent>
      </Tabs>

      {/* Enhanced Global Search Modal */}
      <GlobalSearch
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        onResultClick={(result) => {
          // Navigate to result and close search
          window.location.href = result.url;
          setIsSearchOpen(false);
        }}
      />

      {/* Enhanced AI Chat Modal */}
      {isChatOpen && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed right-4 top-4 bottom-4 w-96 max-w-[calc(100vw-2rem)]">
            <Card className="h-full flex flex-col">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg">AI Assistant</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsChatOpen(false)}
                >
                  ×
                </Button>
              </CardHeader>
              <CardContent className="flex-1 p-0">
                <AIChat
                  context={selectedChatContext}
                  className="h-full border-0"
                />
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Enhanced Floating Action Button */}
      <FloatingActionButton
        data-tour="fab"
        actions={[
          {
            icon: <Search className="h-4 w-4" />,
            label: 'Global Search',
            onClick: () => setIsSearchOpen(true),
            shortcut: '⌘K'
          },
          {
            icon: <Brain className="h-4 w-4" />,
            label: 'AI Assistant',
            onClick: () => openChatWithContext(firstProject ? {
              projectId: firstProject.id,
              organizationId: firstOrg?.id,
              documentIds: documents.slice(0, 3).map(d => d.id)
            } : undefined),
            shortcut: '⌘J'
          },
          {
            icon: <Plus className="h-4 w-4" />,
            label: 'Upload Document',
            onClick: () => {
              if (firstProject) {
                window.location.href = `/projects/${firstProject.id}/documents/upload`
              } else {
                window.location.href = '/projects/create'
              }
            }
          },
          {
            icon: <MessageCircle className="h-4 w-4" />,
            label: 'Start Collaboration',
            onClick: () => {
              // Open collaboration with current context
              openChatWithContext({
                projectId: firstProject?.id,
                organizationId: firstOrg?.id
              })
            }
          }
        ]}
      />

      {/* Enhanced Onboarding Tour */}
      {!hasCompletedOnboarding && (
        <OnboardingTour
          steps={dashboardTourSteps}
          onComplete={markOnboardingComplete}
          onSkip={markOnboardingComplete}
        />
      )}
    </div>
  );
}
