/**
 * Collaborative Features Hook - Production-ready SignalR for Collaboration Only
 * This hook should only be used by components that require real-time collaboration
 * It provides lazy-loaded SignalR connections and proper resource management
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { collaborationService } from '@/services/collaboration-service'
import { toast } from 'sonner'

export interface CollaborativeUser {
  userId: string
  userName: string
  cursor?: { x: number; y: number; page?: number }
  lastSeen: number
  isTyping?: boolean
}

export interface DocumentLockInfo {
  userId: string
  userName: string
  documentId: string
  lockedAt: number
}

export interface CollaborationMessage {
  id: string
  userId: string
  userName: string
  content: string
  timestamp: number
  type: 'message' | 'system' | 'edit'
}

interface UseCollaborativeDocumentOptions {
  documentId: string
  autoConnect?: boolean
  onUserJoined?: (user: CollaborativeUser) => void
  onUserLeft?: (userId: string) => void
  onDocumentLocked?: (lock: DocumentLockInfo) => void
  onDocumentUnlocked?: (documentId: string) => void
  onCollaborationEvent?: (event: any) => void
}

/**
 * Hook for document collaboration features
 * Only connects to SignalR when explicitly used by collaborative components
 */
export function useCollaborativeDocument(options: UseCollaborativeDocumentOptions) {
  const { documentId, autoConnect = true } = options
  const { user } = useAuth()
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [activeUsers, setActiveUsers] = useState<Map<string, CollaborativeUser>>(new Map())
  const [documentLock, setDocumentLock] = useState<DocumentLockInfo | null>(null)
  const [messages, setMessages] = useState<CollaborationMessage[]>([])
  const [error, setError] = useState<string | null>(null)
  const eventHandlersRef = useRef<Map<string, Function>>(new Map())

  // Connect to collaboration service
  const connect = useCallback(async () => {
    if (isConnecting || isConnected || !user) return

    setIsConnecting(true)
    setError(null)

    try {
      await collaborationService.connect()
      await collaborationService.joinDocument(documentId)
      setIsConnected(true)
      
      console.info(`Connected to document collaboration: ${documentId}`)
      toast.success('Connected to collaboration service')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to connect'
      setError(errorMessage)
      console.error('Failed to connect to document collaboration:', err)
      toast.error('Failed to connect to collaboration service')
    } finally {
      setIsConnecting(false)
    }
  }, [documentId, user, isConnecting, isConnected])

  // Disconnect from collaboration service
  const disconnect = useCallback(async () => {
    if (!isConnected) return

    try {
      await collaborationService.leaveDocument(documentId)
      setIsConnected(false)
      setActiveUsers(new Map())
      setDocumentLock(null)
      setMessages([])
      
      console.info(`Disconnected from document collaboration: ${documentId}`)
    } catch (err) {
      console.error('Failed to disconnect from document collaboration:', err)
    }
  }, [documentId, isConnected])

  // Send cursor update
  const updateCursor = useCallback(async (position: { x: number; y: number; page?: number }) => {
    if (!isConnected || !user) return

    try {
      await collaborationService.sendMessage('UpdateCursor', documentId, user.id, position)
    } catch (err) {
      console.error('Failed to update cursor:', err)
    }
  }, [documentId, user, isConnected])

  // Lock document
  const lockDocument = useCallback(async (): Promise<boolean> => {
    if (!isConnected || !user) return false

    try {
      await collaborationService.sendMessage('LockDocument', documentId, user.id, user.displayName || user.email)
      return true
    } catch (err) {
      console.error('Failed to lock document:', err)
      toast.error('Failed to lock document')
      return false
    }
  }, [documentId, user, isConnected])

  // Unlock document
  const unlockDocument = useCallback(async (): Promise<boolean> => {
    if (!isConnected || !user) return false

    try {
      await collaborationService.sendMessage('UnlockDocument', documentId)
      return true
    } catch (err) {
      console.error('Failed to unlock document:', err)
      toast.error('Failed to unlock document')
      return false
    }
  }, [documentId, user, isConnected])

  // Send message
  const sendMessage = useCallback(async (content: string, type: 'message' | 'system' = 'message') => {
    if (!isConnected || !user) return

    try {
      await collaborationService.sendMessage('SendMessage', documentId, {
        userId: user.id,
        userName: user.displayName || user.email,
        content,
        type,
        timestamp: Date.now()
      })
    } catch (err) {
      console.error('Failed to send message:', err)
      toast.error('Failed to send message')
    }
  }, [documentId, user, isConnected])

  // Set up event handlers
  useEffect(() => {
    if (!isConnected) return

    const handleUserJoined = (userData: CollaborativeUser) => {
      setActiveUsers(prev => new Map(prev.set(userData.userId, userData)))
      options.onUserJoined?.(userData)
    }

    const handleUserLeft = (userId: string) => {
      setActiveUsers(prev => {
        const newMap = new Map(prev)
        newMap.delete(userId)
        return newMap
      })
      options.onUserLeft?.(userId)
    }

    const handleDocumentLocked = (lock: DocumentLockInfo) => {
      setDocumentLock(lock)
      options.onDocumentLocked?.(lock)
    }

    const handleDocumentUnlocked = (docId: string) => {
      if (docId === documentId) {
        setDocumentLock(null)
        options.onDocumentUnlocked?.(docId)
      }
    }

    const handleCursorUpdate = (userId: string, position: { x: number; y: number; page?: number }) => {
      setActiveUsers(prev => {
        const user = prev.get(userId)
        if (user) {
          return new Map(prev.set(userId, { ...user, cursor: position, lastSeen: Date.now() }))
        }
        return prev
      })
    }

    const handleMessageReceived = (message: CollaborationMessage) => {
      setMessages(prev => [...prev, message].slice(-50)) // Keep last 50 messages
    }

    const handleCollaborationEvent = (event: any) => {
      options.onCollaborationEvent?.(event)
    }

    // Register event handlers
    collaborationService.on('UserJoined', handleUserJoined)
    collaborationService.on('UserLeft', handleUserLeft)
    collaborationService.on('DocumentLocked', handleDocumentLocked)
    collaborationService.on('DocumentUnlocked', handleDocumentUnlocked)
    collaborationService.on('CursorUpdate', handleCursorUpdate)
    collaborationService.on('MessageReceived', handleMessageReceived)
    collaborationService.on('CollaborationEvent', handleCollaborationEvent)

    // Store handlers for cleanup
    eventHandlersRef.current.set('UserJoined', handleUserJoined)
    eventHandlersRef.current.set('UserLeft', handleUserLeft)
    eventHandlersRef.current.set('DocumentLocked', handleDocumentLocked)
    eventHandlersRef.current.set('DocumentUnlocked', handleDocumentUnlocked)
    eventHandlersRef.current.set('CursorUpdate', handleCursorUpdate)
    eventHandlersRef.current.set('MessageReceived', handleMessageReceived)
    eventHandlersRef.current.set('CollaborationEvent', handleCollaborationEvent)

    return () => {
      // Clean up event handlers
      eventHandlersRef.current.forEach((handler, eventName) => {
        collaborationService.off(eventName, handler)
      })
      eventHandlersRef.current.clear()
    }
  }, [isConnected, documentId, options])

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && user && !isConnected && !isConnecting) {
      connect()
    }
  }, [autoConnect, user, isConnected, isConnecting, connect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isConnected) {
        disconnect()
      }
    }
  }, [isConnected, disconnect])

  return {
    // Connection state
    isConnected,
    isConnecting,
    error,
    
    // Connection management
    connect,
    disconnect,
    
    // Collaboration data
    activeUsers,
    documentLock,
    messages,
    
    // Actions
    updateCursor,
    lockDocument,
    unlockDocument,
    sendMessage,
    
    // Computed values
    isDocumentLocked: !!documentLock,
    isLockedByCurrentUser: documentLock?.userId === user?.id,
    activeUserCount: activeUsers.size,
    canEdit: !documentLock || documentLock.userId === user?.id,
  }
}

/**
 * Hook for collaboration session management
 * For broader collaboration beyond single documents
 */
export function useCollaborativeSession(sessionId?: string) {
  const { user } = useAuth()
  const [isConnected, setIsConnected] = useState(false)
  const [participants, setParticipants] = useState<CollaborativeUser[]>([])

  const joinSession = useCallback(async () => {
    if (!sessionId || !user) return

    try {
      await collaborationService.connect()
      await collaborationService.joinSession(sessionId)
      setIsConnected(true)
      
      console.info(`Joined collaboration session: ${sessionId}`)
      toast.success('Joined collaboration session')
    } catch (err) {
      console.error('Failed to join collaboration session:', err)
      toast.error('Failed to join collaboration session')
    }
  }, [sessionId, user])

  const leaveSession = useCallback(async () => {
    if (!sessionId) return

    try {
      await collaborationService.leaveSession(sessionId)
      setIsConnected(false)
      setParticipants([])
      
      console.info(`Left collaboration session: ${sessionId}`)
    } catch (err) {
      console.error('Failed to leave collaboration session:', err)
    }
  }, [sessionId])

  return {
    isConnected,
    participants,
    joinSession,
    leaveSession,
  }
}
