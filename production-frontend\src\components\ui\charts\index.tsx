import React from 'react';

// These are placeholder components for charts
// In a real application, you would use a charting library like Chart.js, Recharts, or D3.js

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    borderWidth?: number;
    tension?: number;
  }[];
}

interface ChartProps {
  data: ChartData;
  height?: number;
  width?: number;
}

// Tremor-style chart props
interface TremorChartProps {
  data: any[];
  index?: string;
  categories?: string[];
  category?: string;
  colors?: string[];
  valueFormatter?: (value: any) => string;
  showLegend?: boolean;
  showGridLines?: boolean;
  showAnimation?: boolean;
  title?: string;
  height?: number;
  width?: number;
}

// Union type for both chart interfaces
type UnifiedChartProps = ChartProps | TremorChartProps;

// Helper function to check if props are Tremor-style
function isTremorProps(props: any): props is TremorChartProps {
  return Array.isArray(props.data) && !props.data.labels;
}

export function LineChart(props: UnifiedChartProps) {
  const { height = 300, width = 500 } = props;

  if (isTremorProps(props)) {
    const { data, title, index, categories } = props;
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-muted-foreground">{title || 'Line Chart'}</p>
          <p className="text-xs text-muted-foreground mt-2">
            {data.length} data points
          </p>
        </div>
      </div>
    );
  }

  const { data } = props as ChartProps;
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <p className="text-muted-foreground">Line Chart Placeholder</p>
        <p className="text-xs text-muted-foreground mt-2">
          {data.datasets[0].label}: {data.datasets[0].data.length} data points
        </p>
      </div>
    </div>
  );
}

export function BarChart(props: UnifiedChartProps) {
  const { height = 300, width = 500 } = props;

  if (isTremorProps(props)) {
    const { data, title, index, categories } = props;
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-muted-foreground">{title || 'Bar Chart'}</p>
          <p className="text-xs text-muted-foreground mt-2">
            {data.length} data points
          </p>
        </div>
      </div>
    );
  }

  const { data } = props as ChartProps;
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <p className="text-muted-foreground">Bar Chart Placeholder</p>
        <p className="text-xs text-muted-foreground mt-2">
          {data.datasets[0].label}: {data.datasets[0].data.length} data points
        </p>
      </div>
    </div>
  );
}

export function PieChart(props: UnifiedChartProps) {
  const { height = 300, width = 300 } = props;

  if (isTremorProps(props)) {
    const { data, title, category } = props;
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <p className="text-muted-foreground">{title || 'Pie Chart'}</p>
          <p className="text-xs text-muted-foreground mt-2">
            {data.length} data points
          </p>
        </div>
      </div>
    );
  }

  const { data } = props as ChartProps;
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <p className="text-muted-foreground">Pie Chart Placeholder</p>
        <p className="text-xs text-muted-foreground mt-2">
          {data.datasets[0].label}: {data.datasets[0].data.length} data points
        </p>
      </div>
    </div>
  );
}

export function DonutChart({ data, height = 300, width = 300 }: ChartProps) {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <p className="text-muted-foreground">Donut Chart Placeholder</p>
        <p className="text-xs text-muted-foreground mt-2">
          {data.datasets[0].label}: {data.datasets[0].data.length} data points
        </p>
      </div>
    </div>
  );
}

export function AreaChart({ data, height = 300, width = 500 }: ChartProps) {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <p className="text-muted-foreground">Area Chart Placeholder</p>
        <p className="text-xs text-muted-foreground mt-2">
          {data.datasets[0].label}: {data.datasets[0].data.length} data points
        </p>
      </div>
    </div>
  );
}
