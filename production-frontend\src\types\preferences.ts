/**
 * User Preferences Types
 * Type definitions for user settings and preferences
 */

export interface UserPreferences {
  // Theme and appearance
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  dateFormat: string
  timeFormat: '12h' | '24h'
  
  // Dashboard preferences
  dashboard: DashboardPreferences
  
  // Notification preferences
  notifications: NotificationPreferences
  
  // Privacy preferences
  privacy: PrivacyPreferences
  
  // Accessibility preferences
  accessibility: AccessibilityPreferences
  
  // Editor preferences
  editor: EditorPreferences
}

export interface DashboardPreferences {
  layout: 'grid' | 'list' | 'compact'
  widgets: string[]
  refreshInterval: number
  showWelcome: boolean
  compactMode: boolean
  defaultView?: 'dashboard' | 'projects' | 'documents' | 'workflows'
  itemsPerPage?: number
  pinnedItems?: string[]
}

export interface NotificationPreferences {
  // Channels
  email: boolean
  push: boolean
  inApp: boolean
  sound: boolean
  
  // Types
  documentUpdates: boolean
  projectInvites: boolean
  aiOperations: boolean
  systemAlerts: boolean
  marketing: boolean
  
  // Additional notification types
  documentUploaded?: boolean
  documentProcessed?: boolean
  commentAdded?: boolean
  mentionedInComment?: boolean
  projectInvitation?: boolean
  organizationInvitation?: boolean
  workflowAssigned?: boolean
  workflowCompleted?: boolean
  systemUpdates?: boolean
  securityAlerts?: boolean
  marketingEmails?: boolean
  
  // Frequency
  digestFrequency?: 'immediate' | 'hourly' | 'daily' | 'weekly'
  
  // Quiet hours
  quietHours?: {
    enabled: boolean
    start: string
    end: string
    timezone: string
  }
}

export interface PrivacyPreferences {
  shareAnalytics: boolean
  shareUsageData: boolean
  allowTracking: boolean
  showOnlineStatus: boolean
  
  // Additional privacy settings
  profileVisibility?: 'public' | 'organization' | 'private'
  allowDirectMessages?: boolean
  dataProcessingConsent?: boolean
  analyticsConsent?: boolean
}

export interface AccessibilityPreferences {
  highContrast: boolean
  largeText: boolean
  reduceMotion: boolean
  screenReader: boolean
  
  // Additional accessibility settings
  keyboardNavigation?: boolean
  colorBlindSupport?: boolean
  fontSize?: number
  focusIndicator?: boolean
  skipLinks?: boolean
}

export interface EditorPreferences {
  fontSize: number
  fontFamily: string
  lineHeight: number
  tabSize: number
  wordWrap: boolean
  showLineNumbers: boolean
  autoSave: boolean
  autoSaveInterval: number
  
  // Additional editor settings
  theme?: string
  highlightActiveLine?: boolean
  showInvisibles?: boolean
  enableSnippets?: boolean
  enableEmmet?: boolean
  enableLinting?: boolean
  keyBindings?: 'default' | 'vim' | 'emacs'
}

// Preference categories for organization
export interface PreferenceCategory {
  id: string
  name: string
  description: string
  icon: string
  preferences: PreferenceItem[]
}

export interface PreferenceItem {
  id: string
  name: string
  description: string
  type: 'boolean' | 'string' | 'number' | 'select' | 'multiselect' | 'range'
  value: any
  defaultValue: any
  options?: PreferenceOption[]
  min?: number
  max?: number
  step?: number
  required?: boolean
  validation?: PreferenceValidation
}

export interface PreferenceOption {
  label: string
  value: any
  description?: string
  disabled?: boolean
}

export interface PreferenceValidation {
  pattern?: string
  min?: number
  max?: number
  required?: boolean
  custom?: (value: any) => boolean | string
}

// Preference sync and backup
export interface PreferenceSync {
  enabled: boolean
  lastSyncAt?: string
  syncConflicts?: PreferenceSyncConflict[]
}

export interface PreferenceSyncConflict {
  key: string
  localValue: any
  remoteValue: any
  timestamp: string
  resolved: boolean
}

// Preference export/import
export interface PreferenceExport {
  preferences: UserPreferences
  metadata: {
    exportedAt: string
    version: string
    userId: string
  }
}

export interface PreferenceImport {
  file: File
  overwriteExisting: boolean
  selectiveImport?: string[]
}

// Preference history and versioning
export interface PreferenceHistory {
  id: string
  changes: PreferenceChange[]
  timestamp: string
  source: 'user' | 'admin' | 'system' | 'import'
}

export interface PreferenceChange {
  key: string
  oldValue: any
  newValue: any
  category: string
}

// Organization-level preference policies
export interface PreferencePolicy {
  id: string
  name: string
  description: string
  category: string
  enforced: boolean
  allowUserOverride: boolean
  defaultValue: any
  validValues?: any[]
  organizationId: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

// Preference templates for quick setup
export interface PreferenceTemplate {
  id: string
  name: string
  description: string
  category: string
  preferences: Partial<UserPreferences>
  isPublic: boolean
  organizationId?: string
  createdBy: string
  createdAt: string
  usageCount: number
}

// Preference analytics
export interface PreferenceAnalytics {
  mostChangedSettings: Array<{
    key: string
    changeCount: number
  }>
  popularValues: Array<{
    key: string
    value: any
    userCount: number
  }>
  adoptionRates: Array<{
    feature: string
    adoptionRate: number
  }>
}

// Preference validation results
export interface PreferenceValidationResult {
  isValid: boolean
  errors: Array<{
    key: string
    message: string
  }>
  warnings: Array<{
    key: string
    message: string
  }>
}

// Preference migration for version updates
export interface PreferenceMigration {
  fromVersion: string
  toVersion: string
  migrations: Array<{
    key: string
    transform: (oldValue: any) => any
  }>
}
