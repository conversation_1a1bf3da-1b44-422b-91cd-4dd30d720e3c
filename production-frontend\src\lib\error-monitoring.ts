/**
 * Error Monitoring Utility
 * Provides comprehensive error tracking and monitoring capabilities
 */

import { logger } from './logger'

export interface ErrorContext {
  userId?: string
  sessionId?: string
  url?: string
  userAgent?: string
  timestamp?: string
  component?: string
  action?: string
  additionalData?: Record<string, any>
}

export interface ErrorReport {
  id: string
  message: string
  stack?: string
  type: 'javascript' | 'api' | 'component' | 'network' | 'custom'
  severity: 'low' | 'medium' | 'high' | 'critical'
  context: ErrorContext
  fingerprint: string
  count: number
  firstSeen: string
  lastSeen: string
}

export interface ErrorMonitoringConfig {
  enabled: boolean
  apiEndpoint?: string
  maxErrors: number
  enableStackTrace: boolean
  enableUserTracking: boolean
  enablePerformanceTracking: boolean
  sampleRate: number
  ignorePatterns: RegExp[]
  userContext?: {
    userId: string
    [key: string]: any
  }
}

class ErrorMonitoring {
  private config: ErrorMonitoringConfig
  private errors: Map<string, ErrorReport> = new Map()
  private errorQueue: ErrorReport[] = []
  private isOnline = navigator.onLine

  constructor(config: Partial<ErrorMonitoringConfig> = {}) {
    this.config = {
      enabled: true,
      maxErrors: 100,
      enableStackTrace: true,
      enableUserTracking: true,
      enablePerformanceTracking: true,
      sampleRate: 1.0,
      ignorePatterns: [
        /Script error/,
        /Non-Error promise rejection captured/,
        /ResizeObserver loop limit exceeded/,
        /ChunkLoadError/
      ],
      ...config
    }

    if (this.config.enabled) {
      this.setupErrorHandlers()
      this.setupNetworkMonitoring()
    }
  }

  private setupErrorHandlers(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.captureError({
        message: event.message,
        stack: event.error?.stack,
        type: 'javascript',
        severity: 'high',
        context: {
          url: window.location.href,
          userAgent: navigator.userAgent,
          component: 'global',
          additionalData: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        }
      })
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      // Prevent default to avoid console errors
      event.preventDefault()

      // Only capture if it's not a common API error (to avoid circular logging)
      const reason = event.reason
      const isCommonApiError = reason && (
        reason.code === 'NETWORK_ERROR' ||
        reason.code === 'NOT_FOUND' ||
        reason.code === 'UNAUTHORIZED' ||
        reason.message?.includes('Network error') ||
        reason.message?.includes('Request failed with status code') ||
        reason.message?.includes('fetch')
      )

      if (!isCommonApiError) {
        this.captureError({
          message: `Unhandled promise rejection: ${reason}`,
          stack: reason?.stack,
          type: 'javascript',
          severity: 'high',
          context: {
            url: window.location.href,
            userAgent: navigator.userAgent,
            component: 'promise',
            additionalData: {
              reason: reason
            }
          }
        })
      }
    })
  }

  private setupNetworkMonitoring(): void {
    // Monitor online/offline status
    window.addEventListener('online', () => {
      this.isOnline = true
      this.flushErrorQueue()
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })
  }

  private shouldIgnoreError(message: string): boolean {
    return this.config.ignorePatterns.some(pattern => pattern.test(message))
  }

  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate
  }

  private generateFingerprint(message: string, stack?: string): string {
    // Create a unique fingerprint for grouping similar errors
    const content = stack || message
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  private createErrorReport(error: {
    message: string
    stack?: string
    type: ErrorReport['type']
    severity: ErrorReport['severity']
    context: ErrorContext
  }): ErrorReport {
    const fingerprint = this.generateFingerprint(error.message, error.stack)
    const timestamp = new Date().toISOString()

    return {
      id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      message: error.message,
      stack: this.config.enableStackTrace ? error.stack : undefined,
      type: error.type,
      severity: error.severity,
      context: {
        timestamp,
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...error.context
      },
      fingerprint,
      count: 1,
      firstSeen: timestamp,
      lastSeen: timestamp
    }
  }

  private async sendErrorReport(report: ErrorReport): Promise<void> {
    if (!this.config.apiEndpoint || !this.isOnline) {
      this.errorQueue.push(report)
      return
    }

    try {
      await fetch(this.config.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report)
      })
    } catch (error) {
      // Queue for retry when online
      this.errorQueue.push(report)
      // Use console.warn instead of logger to avoid circular logging
      console.warn('Failed to send error report:', error)
    }
  }

  private async flushErrorQueue(): Promise<void> {
    if (!this.isOnline || this.errorQueue.length === 0) return

    const errors = [...this.errorQueue]
    this.errorQueue = []

    for (const error of errors) {
      try {
        await this.sendErrorReport(error)
      } catch (err) {
        // Re-queue failed errors
        this.errorQueue.push(error)
      }
    }
  }

  // Public methods
  captureError(error: {
    message: string
    stack?: string
    type?: ErrorReport['type']
    severity?: ErrorReport['severity']
    context?: Partial<ErrorContext>
  }): void {
    if (!this.config.enabled || this.shouldIgnoreError(error.message) || !this.shouldSample()) {
      return
    }

    const report = this.createErrorReport({
      message: error.message,
      stack: error.stack,
      type: error.type || 'custom',
      severity: error.severity || 'medium',
      context: error.context || {}
    })

    // Check if we've seen this error before
    const existingError = this.errors.get(report.fingerprint)
    if (existingError) {
      existingError.count++
      existingError.lastSeen = report.lastSeen
      this.errors.set(report.fingerprint, existingError)
    } else {
      this.errors.set(report.fingerprint, report)
    }

    // Limit stored errors
    if (this.errors.size > this.config.maxErrors) {
      const oldestKey = this.errors.keys().next().value
      if (oldestKey) {
        this.errors.delete(oldestKey)
      }
    }

    // Log the error to console to avoid circular logging
    console.error(`[ERROR-MONITORING] ${error.message}`, {
      type: report.type,
      severity: report.severity,
      fingerprint: report.fingerprint
    })

    // Send to remote service
    this.sendErrorReport(report)
  }

  captureException(error: Error, context?: Partial<ErrorContext>): void {
    this.captureError({
      message: error.message,
      stack: error.stack,
      type: 'javascript',
      severity: 'high',
      context
    })
  }

  captureApiError(
    method: string,
    url: string,
    status: number,
    response?: any,
    context?: Partial<ErrorContext>
  ): void {
    this.captureError({
      message: `API Error: ${method} ${url} - ${status}`,
      type: 'api',
      severity: status >= 500 ? 'critical' : 'medium',
      context: {
        ...context,
        action: `${method} ${url}`,
        additionalData: {
          status,
          response
        }
      }
    })
  }

  captureComponentError(
    componentName: string,
    error: Error,
    errorInfo?: any,
    context?: Partial<ErrorContext>
  ): void {
    this.captureError({
      message: `Component Error: ${componentName} - ${error.message}`,
      stack: error.stack,
      type: 'component',
      severity: 'high',
      context: {
        ...context,
        component: componentName,
        additionalData: errorInfo
      }
    })
  }

  captureNetworkError(url: string, error: Error, context?: Partial<ErrorContext>): void {
    this.captureError({
      message: `Network Error: ${url} - ${error.message}`,
      stack: error.stack,
      type: 'network',
      severity: 'medium',
      context: {
        ...context,
        additionalData: {
          url,
          networkError: true
        }
      }
    })
  }

  // User context management
  setUserContext(userId: string, additionalData?: Record<string, any>): void {
    if (this.config.enableUserTracking) {
      // Store user context for future error reports
      this.config = {
        ...this.config,
        userContext: {
          userId,
          ...additionalData
        }
      }
    }
  }

  clearUserContext(): void {
    if (this.config.enableUserTracking) {
      delete (this.config as any).userContext
    }
  }

  // Performance monitoring
  capturePerformanceIssue(
    metric: string,
    value: number,
    threshold: number,
    context?: Partial<ErrorContext>
  ): void {
    if (!this.config.enablePerformanceTracking || value <= threshold) return

    this.captureError({
      message: `Performance Issue: ${metric} (${value}) exceeded threshold (${threshold})`,
      type: 'custom',
      severity: 'medium',
      context: {
        ...context,
        additionalData: {
          metric,
          value,
          threshold,
          performanceIssue: true
        }
      }
    })
  }

  // Error retrieval and management
  getErrors(): ErrorReport[] {
    return Array.from(this.errors.values()).sort((a, b) => 
      new Date(b.lastSeen).getTime() - new Date(a.lastSeen).getTime()
    )
  }

  getErrorsByType(type: ErrorReport['type']): ErrorReport[] {
    return this.getErrors().filter(error => error.type === type)
  }

  getErrorsBySeverity(severity: ErrorReport['severity']): ErrorReport[] {
    return this.getErrors().filter(error => error.severity === severity)
  }

  clearErrors(): void {
    this.errors.clear()
    this.errorQueue = []
  }

  // Configuration
  updateConfig(config: Partial<ErrorMonitoringConfig>): void {
    this.config = { ...this.config, ...config }
  }

  getConfig(): ErrorMonitoringConfig {
    return { ...this.config }
  }

  // Health check
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy'
    errorCount: number
    queuedErrors: number
    isOnline: boolean
    config: ErrorMonitoringConfig
  } {
    const errorCount = this.errors.size
    const queuedErrors = this.errorQueue.length
    
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    if (errorCount > 50 || queuedErrors > 20) {
      status = 'degraded'
    }
    if (errorCount > 100 || queuedErrors > 50) {
      status = 'unhealthy'
    }

    return {
      status,
      errorCount,
      queuedErrors,
      isOnline: this.isOnline,
      config: this.config
    }
  }
}

// Create and export singleton instance
export const errorMonitoring = new ErrorMonitoring({
  enabled: true,
  apiEndpoint: process.env.NEXT_PUBLIC_ERROR_MONITORING_ENDPOINT,
  sampleRate: process.env.NODE_ENV === 'development' ? 1.0 : 0.1,
  enableStackTrace: true,
  enableUserTracking: true,
  enablePerformanceTracking: true
})

export default errorMonitoring
