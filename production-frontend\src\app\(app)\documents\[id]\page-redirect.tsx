'use client';

import { useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useDocuments } from '@/hooks/documents/useDocuments';

export default function DocumentRedirectPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const documentId = params?.id as string;

  // Get projectId from query params or from document data
  const projectIdFromQuery = searchParams?.get('projectId');

  // Use the document hook to get document data
  const { documents: documentsResponse, loading: isLoading } = useDocuments({
    projectId: projectIdFromQuery || '',
    organizationId: undefined
  });

  const document = documentsResponse?.find((d: any) => d.id === documentId);

  useEffect(() => {
    // If we have a projectId from query params, redirect immediately
    if (projectIdFromQuery) {
      router.replace(`/projects/${projectIdFromQuery}/documents/${documentId}`);
      return;
    }

    // If document is loaded and has a projectId, redirect to the new URL
    if (document && document.projectId) {
      router.replace(`/projects/${document.projectId}/documents/${documentId}`);
    }
  }, [document, documentId, projectIdFromQuery, router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
      <p>Redirecting to document...</p>
    </div>
  );
}
