'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useProject } from '@/hooks/projects';
import { useTemplate } from '@/hooks/templates';
import { ArrowLeft, Edit, FileText } from 'lucide-react';
import { TemplatePreview } from '@/components/templates/template-preview';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';

export default function TemplateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;
  const templateId = params?.templateId as string;

  const { project } = useProject(projectId);
  const { data: template, isLoading, error } = useTemplate(templateId);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load template details',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading template details...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <Button
            variant="ghost"
            size="sm"
            className="mb-2"
            onClick={() => router.push(`/projects/${projectId}/templates`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Templates
          </Button>
          <h1 className="text-3xl font-bold">{(template as any)?.name || 'Template Details'}</h1>
          <p className="text-muted-foreground">Project: {project?.name}</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/projects/${projectId}/templates/${templateId}/edit`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Template
          </Button>
          <Button
            onClick={() => {
              // Create document from template
              toast({
                title: 'Creating document',
                description: 'Creating a new document from this template...',
              });
              // Redirect to the new document after creation
              // This would typically involve an API call
              setTimeout(() => {
                router.push(`/projects/${projectId}/documents`);
              }, 1500);
            }}
          >
            <FileText className="mr-2 h-4 w-4" />
            Use Template
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Template Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <TemplatePreview template={template! as any} />
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Template Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Description</h3>
                <p className="text-sm text-muted-foreground">{(template as any)?.description || 'No description provided'}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Template Type</h3>
                <Badge variant="outline" className="mt-1">
                  {(template as any)?.type || 'Standard'}
                </Badge>
              </div>

              <div>
                <h3 className="text-sm font-medium">Created</h3>
                <p className="text-sm text-muted-foreground">
                  {(template as any)?.createdAt ? formatDate(new Date((template as any).createdAt)) : 'Unknown'}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Last Updated</h3>
                <p className="text-sm text-muted-foreground">
                  {(template as any)?.updatedAt ? formatDate(new Date((template as any).updatedAt)) : 'Unknown'}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Created By</h3>
                <p className="text-sm text-muted-foreground">{((template as any)?.createdBy as any)?.name || (template as any)?.createdBy || 'Unknown'}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
