"use client";

import { useState } from "react";
import Image from "next/image";
import { FileText, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface DocumentThumbnailProps {
  url: string;
  alt: string;
  className?: string;
  fallbackClassName?: string;
}

export function DocumentThumbnail({
  url,
  alt,
  className,
  fallbackClassName
}: DocumentThumbnailProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className={cn(
        "w-full h-full flex flex-col items-center justify-center bg-muted/50",
        fallbackClassName
      )}>
        <AlertCircle className="h-6 w-6 text-muted-foreground mb-1" />
        <span className="text-xs text-muted-foreground">Failed to load</span>
      </div>
    );
  }

  return (
    <div className={cn("relative w-full h-full", className)}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted/50">
          <FileText className="h-6 w-6 text-muted-foreground animate-pulse" />
        </div>
      )}
      <Image
        src={url}
        alt={alt}
        fill
        className={cn(
          "object-contain transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100"
        )}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
}
