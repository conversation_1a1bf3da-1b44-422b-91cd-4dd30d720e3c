import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  {
    ignores: [
      "**/node_modules/**",
      "**/build/**",
      "**/dist/**",
      "**/out/**",
      "**/.next/**",
      "**/coverage/**",
      "**/storybook-static/**",
      "**/.cache/**",
      "**/.temp/**",
      "**/tmp/**",
      "**/__snapshots__/**",
      "*.lock",
      "*.log",
      ".env",
      ".env.*.local",
      "**/public/assets/**",
      "**/generated/**",
      "**/vendor/**",
      "**/docs/**",
      "**/tests/fixtures/**",
      "**/e2e/**",
      "**/cypress/**",
      "**/playwright-report/**",
      "**/android/app/build/**",
      "**/ios/build/**",
      "**/*.min.js",
      "**/*.bundle.js",
      "**/*.d.ts",
    ],
  },
  ...compat.extends(
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:@typescript-eslint/recommended-type-checked", // Requires type information
    "plugin:@typescript-eslint/stylistic",
    "plugin:@typescript-eslint/stylistic-type-checked"   // Requires type information
  ),
  {
    parserOptions: {
      project: './tsconfig.json', // Specify your tsconfig.json for type-aware rules
    },
    rules: {
      // --- TypeScript Specific Rules ---

      // Require explicit return types on functions and class methods
      "@typescript-eslint/explicit-function-return-type": "off",

      // Require explicit accessibility modifiers on class members and interfaces
      "@typescript-eslint/explicit-member-accessibility": "off",

      // Disallow the use of @ts-ignore comments
      "@typescript-eslint/ban-ts-comment": "off",

      // Enforces consistent usage of type imports
      "@typescript-eslint/consistent-type-imports": "off",

      // Enforces consistent usage of type assertions
      "@typescript-eslint/consistent-type-assertions": "off",

      // Disallow non-null assertion using the `!` postfix operator
      "@typescript-eslint/no-non-null-assertion": "off",

      // Disallow unused variables
      "no-unused-vars": "off", // Disable the base JavaScript rule
      "@typescript-eslint/no-unused-vars": "off",

      // Disallow the use of any type
      "@typescript-eslint/no-explicit-any": "off",

      // Disallow the declaration of empty interfaces
      "@typescript-eslint/no-empty-interface": "off",

      // Require interface names to begin with a capital `I`
      // "@typescript-eslint/interface-name-prefix": "off", // Deprecated in favor of consistent-type-definitions

      // Enforce consistent naming conventions for variables, functions, etc.
      "@typescript-eslint/naming-convention": [
        "off",
        {
          selector: "default",
          format: ["camelCase"],
        },
        {
          selector: "variable",
          format: ["camelCase", "UPPER_CASE"],
        },
        {
          selector: "parameter",
          format: ["camelCase"],
          leadingUnderscore: "allow",
        },
        {
          selector: "memberLike",
          modifiers: ["private"],
          format: ["camelCase"],
          leadingUnderscore: "require",
        },
        {
          selector: "typeLike",
          format: ["PascalCase"],
        },
        {
          selector: "enumMember",
          format: ["PascalCase", "UPPER_CASE"],
        },
        {
          selector: "interface",
          format: ["PascalCase"], // or ["PascalCase"] if you prefer the 'I' prefix
          // prefix: ["I"], // Uncomment if you want to enforce 'I' prefix
        },
      ],

      // Enforce the use of `readonly` modifier on class properties where applicable
      "@typescript-eslint/prefer-readonly": "off",

      // Disallow the use of require statements except in import statements
      "@typescript-eslint/no-var-requires": "off",

      // --- Stylistic TypeScript Rules (from @typescript-eslint/stylistic) ---
      "@typescript-eslint/member-delimiter-style": "off",
      "@typescript-eslint/type-annotation-spacing": "off",
      "@typescript-eslint/consistent-indexed-object-style": "off",
      "@typescript-eslint/prefer-ts-expect-error": "off",

      // --- General ESLint Rules (overrides for TypeScript) ---
      "no-console": "off", // Suggest using a proper logging mechanism
      "no-debugger": "off",
      "semi": "off", // Disable the base JavaScript rule for semicolons
      "@typescript-eslint/semi": "off", // Enforce semicolons

      "quotes": "off", // Disable the base JavaScript rule for quotes
      "@typescript-eslint/quotes": ["off", "single", { "avoidEscape": true }],

      "comma-dangle": "off", // Disable the base JavaScript rule for trailing commas
      "@typescript-eslint/comma-dangle": ["off", "always-multiline"],

      "object-curly-spacing": "off", // Disable the base JavaScript rule
      "@typescript-eslint/object-curly-spacing": ["off", "always"],

      "brace-style": "off", // Disable the base JavaScript rule
      "@typescript-eslint/brace-style": ["off", "1tbs", { "allowSingleLine": true }],

      "indent": "off", // Disable the base JavaScript rule
      "@typescript-eslint/indent": ["off", 2], // Or your preferred indentation

      "keyword-spacing": "off", // Disable the base JavaScript rule
      "@typescript-eslint/keyword-spacing": "off",

      "space-before-blocks": "off", // Disable the base JavaScript rule
      "@typescript-eslint/space-before-blocks": "off",

      "space-before-function-paren": "off", // Disable the base JavaScript rule
      "@typescript-eslint/space-before-function-paren": ["off", { "anonymous": "always", "named": "never", "asyncArrow": "always" }],

      "space-infix-ops": "off", // Disable the base JavaScript rule
      "@typescript-eslint/space-infix-ops": "off",
    },
  },
];

export default eslintConfig;