/**
 * Organization Service
 * Handles all organization-related API calls
 */

import { backendApiClient } from './backend-api-client'
import type {
  OrganizationMember,
  ID,
} from '../types'
import type { Organization } from '../types/backend'

class OrganizationService {
  /**
   * Get all organizations for current user
   */
  async getOrganizations(): Promise<{ data: Organization[] }> {
    const organizations = await backendApiClient.getOrganizations()
    return { data: organizations }
  }

  /**
   * Get organization by ID
   */
  async getOrganization(organizationId: ID): Promise<Organization> {
    return await backendApiClient.getOrganization(organizationId)
  }

  /**
   * Create new organization
   */
  async createOrganization(organizationData: {
    name: string
    description?: string
    domain?: string
    settings?: any
  }): Promise<Organization> {
    return await backendApiClient.createOrganization(organizationData)
  }

  /**
   * Update organization
   */
  async updateOrganization(
    organizationId: ID,
    updateData: {
      name?: string
      description?: string
      domain?: string
      settings?: any
    }
  ): Promise<Organization> {
    return await backendApiClient.updateOrganization(organizationId, updateData)
  }

  /**
   * Delete organization
   */
  async deleteOrganization(organizationId: ID): Promise<void> {
    await backendApiClient.deleteOrganization(organizationId)
  }

  /**
   * Get organization members
   */
  async getMembers(organizationId: ID): Promise<OrganizationMember[]> {
    return await backendApiClient.getOrganizationMembers(organizationId)
  }

  /**
   * Invite member to organization
   */
  async inviteMember(
    organizationId: ID,
    inviteData: { email: string; role: string; permissions?: string[] }
  ): Promise<void> {
    await backendApiClient.inviteOrganizationMember(organizationId, inviteData)
  }

  /**
   * Add member to organization
   */
  async addMember(
    organizationId: ID,
    memberData: { userId: ID; role: string }
  ): Promise<OrganizationMember> {
    return await backendApiClient.addOrganizationMember(organizationId, memberData)
  }

  /**
   * Update member role
   */
  async updateMemberRole(
    organizationId: ID,
    userId: ID,
    role: string
  ): Promise<OrganizationMember> {
    return await backendApiClient.updateOrganizationMemberRole(organizationId, userId, role)
  }

  /**
   * Remove member from organization
   */
  async removeMember(organizationId: ID, userId: ID): Promise<void> {
    await backendApiClient.removeOrganizationMember(organizationId, userId)
  }

  /**
   * Get organization settings
   */
  async getSettings(organizationId: ID): Promise<any> {
    return await backendApiClient.getOrganizationSettings(organizationId)
  }

  /**
   * Update organization settings
   */
  async updateSettings(organizationId: ID, settings: any): Promise<any> {
    return await backendApiClient.updateOrganizationSettings(organizationId, settings)
  }
}

// Export singleton instance
export const organizationService = new OrganizationService()
export default organizationService
