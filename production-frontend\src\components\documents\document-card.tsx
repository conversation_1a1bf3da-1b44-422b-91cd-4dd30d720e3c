"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  FileText,
  Tag,
  History,
  Download,
  Eye,
  FileType,
  Clock
} from "lucide-react";
import { format } from "date-fns";
import { Document } from "@/types/document";
import { DocumentThumbnail } from "./document-thumbnail";
import { cn } from "@/lib/utils";

interface DocumentCardProps {
  document: Document;
  className?: string;
  showActions?: boolean;
  onDownload?: (document: Document) => void;
}

export function DocumentCard({
  document,
  className,
  showActions = true,
  onDownload
}: DocumentCardProps) {
  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} K<PERSON>`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  // Get file extension
  const getFileExtension = (fileName: string) => {
    return fileName.split('.').pop()?.toUpperCase() || '';
  };

  // Determine status badge variant
  const statusVariant =
    document.status === 'processed' ? "success" :
    document.status === 'processing' ? "secondary" :
    document.status === 'uploaded' ? "outline" :
    document.status === 'uploading' ? "outline" :
    "destructive";

  // Determine status badge label
  const statusLabel =
    document.status === 'processed' ? "Processed" :
    document.status === 'processing' ? "Processing" :
    document.status === 'uploaded' ? "Uploaded" :
    document.status === 'uploading' ? "Uploading" :
    "Failed";

  // Handle download click
  const handleDownload = () => {
    if (onDownload) {
      onDownload(document);
    }
  };

  return (
    <Card className={cn("w-full hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg line-clamp-1 flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            {document.name || document.originalName}
          </CardTitle>
          <Badge variant={statusVariant}>{statusLabel}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-4">
          {/* Document thumbnail */}
          <div className="w-20 h-24 bg-muted rounded overflow-hidden flex-shrink-0 border">
            {document.thumbnailUrl ? (
              <DocumentThumbnail url={document.thumbnailUrl} alt={document.name || document.originalName} />
            ) : (
              <div className="w-full h-full flex flex-col items-center justify-center p-2">
                <FileType className="text-muted-foreground h-8 w-8 mb-1" />
                <span className="text-xs font-medium">{getFileExtension(document.originalName)}</span>
              </div>
            )}
          </div>

          {/* Document metadata */}
          <div className="space-y-2 flex-1">
            <div className="flex items-center gap-2 text-sm">
              <FileText size={14} className="text-muted-foreground" />
              <span>{document.type || "DOCUMENT"}</span>
              <span className="mx-1 text-muted-foreground">•</span>
              <span className="text-muted-foreground">{formatFileSize(document.size)}</span>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <Clock size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">
                {format(new Date(document.createdAt), 'MMM d, yyyy')}
              </span>
            </div>

            <div className="flex items-center gap-2 text-sm">
              <History size={14} className="text-muted-foreground" />
              <span className="text-muted-foreground">Version {document.version || 1}</span>
              {/* Comment count would come from collaboration data if available */}
            </div>
          </div>
        </div>

        {/* Tags section */}
        {document.tags && document.tags.length > 0 && (
          <div className="flex items-center gap-2 text-sm">
            <Tag size={14} className="text-muted-foreground flex-shrink-0" />
            <div className="flex flex-wrap gap-1">
              {document.tags.slice(0, 3).map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">{tag}</Badge>
              ))}
              {document.tags.length > 3 && (
                <Badge variant="outline" className="text-xs">+{document.tags.length - 3}</Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>

      {showActions && (
        <CardFooter className="flex gap-2">
          <Button asChild variant="default" className="flex-1">
            <Link href={`/documents/${document.id}?projectId=${document.projectId}&organizationId=${document.organizationId}`}>
              <Eye className="mr-1 h-4 w-4" />
              View
            </Link>
          </Button>
          <Button variant="outline" className="flex-1" onClick={handleDownload}>
            <Download className="mr-1 h-4 w-4" />
            Download
          </Button>
          <Button asChild variant="outline" className="w-10 px-0">
            <Link href={`/documents/${document.id}/history?projectId=${document.projectId}&organizationId=${document.organizationId}`}>
              <History className="h-4 w-4" />
            </Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
