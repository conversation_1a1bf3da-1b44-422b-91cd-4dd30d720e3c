"use client";

import { useState, useEffect, useRef } from "react";
import dynamic from "next/dynamic";

import { Spinner } from "@/components/ui/spinner";

// Dynamically import the EditorJS implementation to avoid SSR issues
const EditorJSEditorImpl = dynamic(
  () => import('./editorjs-editor-impl').then(mod => ({ default: mod.EditorJSEditorImpl })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center py-8">
        <Spinner className="mr-2" />
        <span>Loading editor...</span>
      </div>
    )
  }
);

interface TemplateEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  height?: number;
}

export function EditorJSTemplateEditor(props: TemplateEditorProps) {
  return <EditorJSEditorImpl {...props} />;
}
