"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useMSALAuth } from "@/components/auth/msal-auth-provider";

export function AuthDebug() {
  const { isAuthenticated, user, getAccessToken, isLoading: authLoading } = useMSALAuth();
  const [testResults, setTestResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testBackendConnection = async () => {
    setLoading(true);
    setTestResults(null);

    try {
      // Get access token for API calls
      const accessToken = await getAccessToken();

      // Test 1: Check authentication state
      console.log("Auth status:", isAuthenticated);
      console.log("User data:", user);
      console.log("Access token:", accessToken ? 'Token present' : 'No token');

      const results: any = {
        authentication: {
          isAuthenticated,
          hasUser: !!user,
          hasToken: !!accessToken,
          user: user ? {
            email: user.email,
            name: user.name,
            id: user.id
          } : null
        },
        tests: []
      };

      // Test 2: Direct backend call to organizations
      try {
        const orgResponse = await fetch('http://localhost:7071/organizations', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': accessToken ? `Bearer ${accessToken}` : '',
          },
        });

        results.tests.push({
          name: 'Organizations Endpoint',
          status: orgResponse.status,
          statusText: orgResponse.statusText,
          success: orgResponse.ok,
          response: orgResponse.ok ? await orgResponse.json() : await orgResponse.text()
        });
      } catch (error) {
        results.tests.push({
          name: 'Organizations Endpoint',
          error: error instanceof Error ? error.message : String(error),
          success: false
        });
      }

      // Test 3: Direct backend call to user tenants
      try {
        const tenantsResponse = await fetch('http://localhost:7071/users/tenants', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': accessToken ? `Bearer ${accessToken}` : '',
          },
        });

        results.tests.push({
          name: 'User Tenants Endpoint',
          status: tenantsResponse.status,
          statusText: tenantsResponse.statusText,
          success: tenantsResponse.ok,
          response: tenantsResponse.ok ? await tenantsResponse.json() : await tenantsResponse.text()
        });
      } catch (error) {
        results.tests.push({
          name: 'User Tenants Endpoint',
          error: error instanceof Error ? error.message : String(error),
          success: false
        });
      }

      // Test 4: Backend auth test
      try {
        const authTestResponse = await fetch('http://localhost:7071/test-auth', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': accessToken ? `Bearer ${accessToken}` : '',
          },
        });

        results.tests.push({
          name: 'Backend Auth Test',
          status: authTestResponse.status,
          statusText: authTestResponse.statusText,
          success: authTestResponse.ok,
          response: authTestResponse.ok ? await authTestResponse.json() : await authTestResponse.text()
        });
      } catch (error) {
        results.tests.push({
          name: 'Backend Auth Test',
          error: error instanceof Error ? error.message : String(error),
          success: false
        });
      }

      // Test 5: Backend health check
      try {
        const healthResponse = await fetch('http://localhost:7071/health', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        results.tests.push({
          name: 'Backend Health Check',
          status: healthResponse.status,
          statusText: healthResponse.statusText,
          success: healthResponse.ok,
          response: healthResponse.ok ? await healthResponse.json() : await healthResponse.text()
        });
      } catch (error) {
        results.tests.push({
          name: 'Backend Health Check',
          error: error instanceof Error ? error.message : String(error),
          success: false
        });
      }

      setTestResults(results);
    } catch (error) {
      setTestResults({
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Authentication Debug</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p><strong>Authentication Status:</strong> {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
          <p><strong>Has User:</strong> {user ? 'Yes' : 'No'}</p>
          <p><strong>User Email:</strong> {user?.email || 'N/A'}</p>
          <p><strong>Loading:</strong> {authLoading ? 'Yes' : 'No'}</p>
        </div>

        <Button onClick={testBackendConnection} disabled={loading}>
          {loading ? 'Testing...' : 'Test Backend Connection'}
        </Button>

        {testResults && (
          <div className="mt-4">
            <h3 className="text-lg font-semibold mb-2">Test Results:</h3>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
              {JSON.stringify(testResults, null, 2)}
            </pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
