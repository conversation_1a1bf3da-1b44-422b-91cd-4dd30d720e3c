/**
 * Generic Cache-Aside Service
 * Provides a reusable pattern for cache-aside operations with database fallback
 * Ensures data consistency and graceful degradation when Redis is unavailable
 */

import { redis } from './redis';
import { db } from './database';
import { logger } from '../utils/logger';
import { EventEmitter } from 'events';

export interface CacheAsideOptions {
  ttlSeconds?: number;
  enableFallback?: boolean;
  cachePrefix?: string;
  invalidatePatterns?: string[];
  enableWarming?: boolean;
  warmingPriority?: 'high' | 'medium' | 'low';
  eventDriven?: boolean;
}

export interface DatabaseQuery {
  containerName: string;
  query?: string;
  parameters?: any[];
  itemId?: string;
  partitionKey?: string;
}

export interface CacheEvent {
  type: 'invalidate' | 'warm' | 'update' | 'delete';
  key: string;
  pattern?: string;
  data?: any;
  priority?: 'high' | 'medium' | 'low';
  timestamp: Date;
  source: string;
}

export interface WarmingRule {
  pattern: string;
  dbQuery: DatabaseQuery;
  options: CacheAsideOptions;
  frequency: number; // minutes
  lastWarmed?: Date;
  priority: 'high' | 'medium' | 'low';
}

export class CacheAsideService extends EventEmitter {
  private static instance: CacheAsideService;
  private warmingRules: Map<string, WarmingRule> = new Map();
  private warmingQueue: CacheEvent[] = [];
  private isWarmingActive = false;
  private warmingInterval: NodeJS.Timeout | null = null;
  private eventQueue: CacheEvent[] = [];
  private isProcessingEvents = false;

  private constructor() {
    super();
    this.setupEventProcessing();
    this.setupCacheWarming();
  }

  public static getInstance(): CacheAsideService {
    if (!CacheAsideService.instance) {
      CacheAsideService.instance = new CacheAsideService();
    }
    return CacheAsideService.instance;
  }

  /**
   * Setup event-driven cache processing
   */
  private setupEventProcessing(): void {
    // Process events every 100ms
    setInterval(() => {
      if (!this.isProcessingEvents && this.eventQueue.length > 0) {
        this.processEventQueue();
      }
    }, 100);
  }

  /**
   * Setup cache warming system
   */
  private setupCacheWarming(): void {
    // Check for warming opportunities every 5 minutes
    this.warmingInterval = setInterval(() => {
      if (!this.isWarmingActive) {
        this.processWarmingRules();
      }
    }, 5 * 60 * 1000);
  }

  /**
   * Add cache warming rule
   */
  public addWarmingRule(ruleId: string, rule: WarmingRule): void {
    this.warmingRules.set(ruleId, rule);
    logger.info('Cache warming rule added', { ruleId, pattern: rule.pattern, priority: rule.priority });
  }

  /**
   * Remove cache warming rule
   */
  public removeWarmingRule(ruleId: string): void {
    this.warmingRules.delete(ruleId);
    logger.info('Cache warming rule removed', { ruleId });
  }

  /**
   * Emit cache event for event-driven invalidation
   */
  public emitCacheEvent(event: CacheEvent): void {
    this.eventQueue.push(event);
    this.emit('cacheEvent', event);
    logger.debug('Cache event emitted', { type: event.type, key: event.key, source: event.source });
  }

  /**
   * Add event to warming queue
   */
  private addToWarmingQueue(event: CacheEvent): void {
    this.warmingQueue.push(event);
    logger.debug('Event added to warming queue', { type: event.type, key: event.key, priority: event.priority });
  }

  /**
   * Generic get operation with cache-aside pattern
   */
  public async get<T extends Record<string, any>>(
    cacheKey: string,
    dbQuery: DatabaseQuery,
    options: CacheAsideOptions = {}
  ): Promise<T | null> {
    const {
      ttlSeconds = 3600,
      enableFallback = true,
      cachePrefix = '',
      enableWarming = false,
      warmingPriority = 'medium',
      eventDriven = false
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      // Try Redis first
      const cachedData = await redis.getJson<T>(fullCacheKey);
      if (cachedData) {
        logger.debug('Data found in Redis cache', { cacheKey: fullCacheKey });

        // Emit cache hit event if event-driven
        if (eventDriven) {
          this.emitCacheEvent({
            type: 'update',
            key: fullCacheKey,
            timestamp: new Date(),
            source: 'cache-hit'
          });
        }

        return cachedData;
      }

      // Cache miss - emit event if event-driven
      if (eventDriven) {
        this.emitCacheEvent({
          type: 'warm',
          key: fullCacheKey,
          priority: warmingPriority,
          timestamp: new Date(),
          source: 'cache-miss'
        });
      }

      // Fallback to database if enabled
      if (enableFallback) {
        const dbData = await this.queryDatabase<T>(dbQuery);

        if (dbData) {
          // Cache the data back to Redis for future requests
          await redis.setJson(fullCacheKey, dbData, ttlSeconds);
          logger.info('Data retrieved from database and cached', { cacheKey: fullCacheKey });

          // Add to warming queue if warming is enabled
          if (enableWarming) {
            this.addToWarmingQueue({
              type: 'warm',
              key: fullCacheKey,
              data: dbData,
              priority: warmingPriority,
              timestamp: new Date(),
              source: 'database-fallback'
            });
          }

          return dbData;
        }
      }

      logger.debug('Data not found in cache or database', { cacheKey: fullCacheKey });
      return null;

    } catch (error) {
      logger.error('Cache-aside get operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });

      // If Redis fails, try database directly
      if (enableFallback) {
        try {
          return await this.queryDatabase<T>(dbQuery);
        } catch (dbError) {
          logger.error('Database fallback also failed', {
            error: dbError instanceof Error ? dbError.message : String(dbError),
            cacheKey: fullCacheKey
          });
        }
      }

      return null;
    }
  }

  /**
   * Generic set operation with cache invalidation
   */
  public async set<T>(
    cacheKey: string,
    data: T,
    options: CacheAsideOptions = {}
  ): Promise<boolean> {
    const {
      ttlSeconds = 3600,
      cachePrefix = '',
      invalidatePatterns = [],
      eventDriven = false
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      // Set data in Redis
      const success = await redis.setJson(fullCacheKey, data, ttlSeconds);

      if (success) {
        // Emit update event if event-driven
        if (eventDriven) {
          this.emitCacheEvent({
            type: 'update',
            key: fullCacheKey,
            data,
            timestamp: new Date(),
            source: 'cache-set'
          });
        }

        // Invalidate related caches
        for (const pattern of invalidatePatterns) {
          await this.invalidatePattern(pattern);

          // Emit invalidation events if event-driven
          if (eventDriven) {
            this.emitCacheEvent({
              type: 'invalidate',
              key: fullCacheKey,
              pattern,
              timestamp: new Date(),
              source: 'pattern-invalidation'
            });
          }
        }

        logger.debug('Data cached and related caches invalidated', {
          cacheKey: fullCacheKey,
          invalidatedPatterns: invalidatePatterns
        });
      }

      return success;

    } catch (error) {
      logger.error('Cache-aside set operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      return false;
    }
  }

  /**
   * Get list of items with cache-aside pattern
   */
  public async getList<T extends Record<string, any>>(
    cacheKey: string,
    dbQuery: DatabaseQuery,
    options: CacheAsideOptions = {}
  ): Promise<T[]> {
    const {
      ttlSeconds = 3600,
      enableFallback = true,
      cachePrefix = ''
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      // Try Redis first
      const cachedList = await redis.getJson<T[]>(fullCacheKey);
      if (cachedList && Array.isArray(cachedList)) {
        logger.debug('List found in Redis cache', { cacheKey: fullCacheKey, count: cachedList.length });
        return cachedList;
      }

      // Fallback to database if enabled
      if (enableFallback) {
        const dbList = await this.queryDatabaseList<T>(dbQuery);
        
        if (dbList && dbList.length > 0) {
          // Cache the list back to Redis for future requests
          await redis.setJson(fullCacheKey, dbList, ttlSeconds);
          logger.info('List retrieved from database and cached', { 
            cacheKey: fullCacheKey, 
            count: dbList.length 
          });
          return dbList;
        }
      }

      logger.debug('List not found in cache or database', { cacheKey: fullCacheKey });
      return [];

    } catch (error) {
      logger.error('Cache-aside getList operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      
      // If Redis fails, try database directly
      if (enableFallback) {
        try {
          return await this.queryDatabaseList<T>(dbQuery);
        } catch (dbError) {
          logger.error('Database fallback also failed for list', {
            error: dbError instanceof Error ? dbError.message : String(dbError),
            cacheKey: fullCacheKey
          });
        }
      }
      
      return [];
    }
  }

  /**
   * Delete from cache and invalidate related patterns
   */
  public async delete(
    cacheKey: string,
    options: CacheAsideOptions = {}
  ): Promise<boolean> {
    const {
      cachePrefix = '',
      invalidatePatterns = []
    } = options;

    const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;

    try {
      const success = await redis.delete(fullCacheKey);
      
      // Invalidate related caches
      for (const pattern of invalidatePatterns) {
        await this.invalidatePattern(pattern);
      }
      
      logger.debug('Cache deleted and related caches invalidated', { 
        cacheKey: fullCacheKey,
        invalidatedPatterns: invalidatePatterns
      });
      
      return success;

    } catch (error) {
      logger.error('Cache-aside delete operation failed', {
        error: error instanceof Error ? error.message : String(error),
        cacheKey: fullCacheKey
      });
      return false;
    }
  }

  /**
   * Query database for single item
   */
  private async queryDatabase<T extends Record<string, any>>(dbQuery: DatabaseQuery): Promise<T | null> {
    try {
      if (dbQuery.itemId && dbQuery.partitionKey) {
        // Direct item read
        return await db.readItem<T>(dbQuery.containerName, dbQuery.itemId, dbQuery.partitionKey);
      } else if (dbQuery.query && dbQuery.parameters) {
        // Query with parameters
        const results = await db.queryItems(dbQuery.containerName, dbQuery.query, dbQuery.parameters);
        return results.length > 0 ? results[0] as T : null;
      } else {
        throw new Error('Invalid database query configuration');
      }
    } catch (error) {
      logger.error('Database query failed', {
        error: error instanceof Error ? error.message : String(error),
        containerName: dbQuery.containerName
      });
      throw error;
    }
  }

  /**
   * Query database for list of items
   */
  private async queryDatabaseList<T extends Record<string, any>>(dbQuery: DatabaseQuery): Promise<T[]> {
    try {
      if (dbQuery.query && dbQuery.parameters) {
        const results = await db.queryItems(dbQuery.containerName, dbQuery.query, dbQuery.parameters);
        return results as T[];
      } else {
        throw new Error('Invalid database query configuration for list');
      }
    } catch (error) {
      logger.error('Database list query failed', {
        error: error instanceof Error ? error.message : String(error),
        containerName: dbQuery.containerName
      });
      throw error;
    }
  }

  /**
   * Invalidate cache pattern using production-safe approach
   */
  private async invalidatePattern(pattern: string): Promise<void> {
    try {
      // Use production cache manager for safe invalidation
      const { cacheManager: productionCacheManager } = await import('./cache-manager');

      // Convert pattern to tags for efficient invalidation
      const tags = this.extractTagsFromPattern(pattern);

      if (tags.length > 0) {
        await productionCacheManager.invalidateByTags(tags);
        logger.debug('Cache invalidated by tags', { pattern, tags });
      } else {
        // Fallback to Redis pattern deletion for non-tagged patterns
        logger.warn('Using fallback pattern deletion', { pattern });
        if (redis.isAvailable()) {
          await (redis as any).deleteByPattern(pattern);
        }
      }
    } catch (error) {
      logger.error('Failed to invalidate cache pattern', {
        error: error instanceof Error ? error.message : String(error),
        pattern
      });
    }
  }

  /**
   * Extract cache tags from pattern for efficient invalidation
   */
  private extractTagsFromPattern(pattern: string): string[] {
    const tags: string[] = [];

    if (pattern.startsWith('document:')) {
      tags.push('document', 'content');
    } else if (pattern.startsWith('user:')) {
      tags.push('user', 'activity');
    } else if (pattern.startsWith('session:')) {
      tags.push('session', 'collaboration');
    } else if (pattern.startsWith('config:')) {
      tags.push('configuration', 'system');
    } else if (pattern.startsWith('feature_flag:')) {
      tags.push('feature', 'configuration');
    } else if (pattern.startsWith('device:')) {
      tags.push('device', 'user');
    } else if (pattern.startsWith('bi_report:')) {
      tags.push('analytics', 'report');
    }

    return tags;
  }

  /**
   * Process event queue for event-driven cache operations
   */
  private async processEventQueue(): Promise<void> {
    if (this.isProcessingEvents || this.eventQueue.length === 0) {
      return;
    }

    this.isProcessingEvents = true;

    try {
      const events = this.eventQueue.splice(0, 10); // Process up to 10 events at a time

      for (const event of events) {
        await this.processEvent(event);
      }
    } catch (error) {
      logger.error('Failed to process event queue', {
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      this.isProcessingEvents = false;
    }
  }

  /**
   * Process individual cache event
   */
  private async processEvent(event: CacheEvent): Promise<void> {
    try {
      switch (event.type) {
        case 'invalidate':
          if (event.pattern) {
            await this.invalidatePattern(event.pattern);
          } else {
            await redis.delete(event.key);
          }
          break;

        case 'warm':
          await this.warmCache(event);
          break;

        case 'update':
          // Event already processed during set operation
          break;

        case 'delete':
          await redis.delete(event.key);
          break;
      }

      logger.debug('Cache event processed', {
        type: event.type,
        key: event.key,
        source: event.source
      });
    } catch (error) {
      logger.error('Failed to process cache event', {
        error: error instanceof Error ? error.message : String(error),
        event
      });
    }
  }

  /**
   * Process cache warming rules
   */
  private async processWarmingRules(): Promise<void> {
    if (this.isWarmingActive || this.warmingRules.size === 0) {
      return;
    }

    this.isWarmingActive = true;

    try {
      const now = new Date();

      for (const [ruleId, rule] of this.warmingRules) {
        const shouldWarm = !rule.lastWarmed ||
          (now.getTime() - rule.lastWarmed.getTime()) >= (rule.frequency * 60 * 1000);

        if (shouldWarm) {
          await this.executeWarmingRule(ruleId, rule);
          rule.lastWarmed = now;
        }
      }
    } catch (error) {
      logger.error('Failed to process warming rules', {
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      this.isWarmingActive = false;
    }
  }

  /**
   * Execute cache warming rule
   */
  private async executeWarmingRule(ruleId: string, rule: WarmingRule): Promise<void> {
    try {
      logger.info('Executing cache warming rule', { ruleId, pattern: rule.pattern });

      const data = await this.queryDatabase(rule.dbQuery);
      if (data) {
        const cacheKey = rule.pattern.replace('*', 'warmed');
        await redis.setJson(cacheKey, data, rule.options.ttlSeconds || 3600);

        logger.info('Cache warming completed', {
          ruleId,
          cacheKey,
          priority: rule.priority
        });
      }
    } catch (error) {
      logger.error('Failed to execute warming rule', {
        error: error instanceof Error ? error.message : String(error),
        ruleId
      });
    }
  }

  /**
   * Warm specific cache entry
   */
  private async warmCache(event: CacheEvent): Promise<void> {
    try {
      if (event.data) {
        await redis.setJson(event.key, event.data, 3600);
        logger.debug('Cache warmed from event', { key: event.key, priority: event.priority });
      }
    } catch (error) {
      logger.error('Failed to warm cache', {
        error: error instanceof Error ? error.message : String(error),
        key: event.key
      });
    }
  }

  /**
   * Get cache warming statistics
   */
  public getWarmingStats(): { rulesCount: number; queueSize: number; isActive: boolean } {
    return {
      rulesCount: this.warmingRules.size,
      queueSize: this.warmingQueue.length,
      isActive: this.isWarmingActive
    };
  }

  /**
   * Get event processing statistics
   */
  public getEventStats(): { queueSize: number; isProcessing: boolean } {
    return {
      queueSize: this.eventQueue.length,
      isProcessing: this.isProcessingEvents
    };
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.warmingInterval) {
      clearInterval(this.warmingInterval);
      this.warmingInterval = null;
    }
    this.warmingRules.clear();
    this.warmingQueue.length = 0;
    this.eventQueue.length = 0;
  }
}

// Export singleton instance
export const cacheAside = CacheAsideService.getInstance();
export default cacheAside;
