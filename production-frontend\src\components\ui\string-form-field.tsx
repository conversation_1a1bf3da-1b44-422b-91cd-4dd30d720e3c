'use client';

import * as React from 'react';
import { Controller, useFormContext, type ControllerProps, type FieldValues } from 'react-hook-form';
import { FormFieldContext } from './form';

// This is a custom FormField component that allows string keys
export const StringFormField = <TFieldValues extends FieldValues = FieldValues>({
  ...props
}: Omit<ControllerProps<TFieldValues, any>, 'name'> & { name: string }) => {
  return (
    <FormFieldContext.Provider value={{ name: props.name as any }}>
      <Controller {...(props as any)} />
    </FormFieldContext.Provider>
  );
};
