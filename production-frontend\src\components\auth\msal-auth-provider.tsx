'use client'

/**
 * MSAL Authentication Provider
 * Comprehensive Azure AD B2C provider supporting all user flows
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { msalAuthService, B2CPolicyType, AuthMethod } from '@/lib/auth/msal-service'
import { useAuthStore } from '@/stores/auth-store'
import { useNotificationStore } from '@/stores/notification-store'
import type { StandardToken, UserContext } from '@/types/backend'

// MSAL Auth Context Interface
interface MSALAuthContextType {
  // State
  isInitialized: boolean
  isAuthenticated: boolean
  isLoading: boolean
  user: UserContext | null
  error: string | null

  // Authentication Methods
  signIn: (policy?: B2CPolicyType, method?: AuthMethod) => Promise<void>
  signUp: (method?: AuthMethod) => Promise<void>
  signOut: (method?: AuthMethod) => Promise<void>
  resetPassword: (method?: AuthMethod) => Promise<void>
  editProfile: (method?: AuthMethod) => Promise<void>

  // Token Management
  getAccessToken: () => Promise<string | null>
  refreshToken: () => Promise<void>

  // Utility Methods
  clearError: () => void
  hasRole: (role: string) => boolean
  hasPermission: (permission: string) => boolean
  isInOrganization: (organizationId: string) => boolean
}

// Create Context
const MSALAuthContext = createContext<MSALAuthContextType | null>(null)

// Provider Props
interface MSALAuthProviderProps {
  children: ReactNode
}

/**
 * MSAL Authentication Provider Component
 */
export function MSALAuthProvider({ children }: MSALAuthProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Zustand stores
  const {
    user,
    isAuthenticated,
    setToken,
    updateProfile,
    clearAuth,
  } = useAuthStore()

  const { addNotification } = useNotificationStore()

  /**
   * Initialize MSAL service
   */
  useEffect(() => {
    const initializeMSAL = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Initialize MSAL service first
        await msalAuthService.initialize()
        console.log('[MSAL Provider] Service initialized successfully')

        // Small delay to ensure initialization is complete
        await new Promise(resolve => setTimeout(resolve, 100))

        // Handle any pending redirect promise
        const redirectResult = await msalAuthService.handleRedirectPromise()
        if (redirectResult) {
          console.log('[MSAL Provider] Redirect authentication completed')
          await handleAuthenticationResult(redirectResult)
        } else {
          // Check if user is already authenticated
          if (msalAuthService.isAuthenticated()) {
            await syncUserData()
          } else {
            clearAuth()
          }
        }

        setIsInitialized(true)
      } catch (error) {
        console.error('[MSAL Provider] Initialization failed:', error)
        setError(error instanceof Error ? error.message : 'Authentication initialization failed')
        setIsInitialized(true)
      } finally {
        setIsLoading(false)
      }
    }

    initializeMSAL()
  }, [clearAuth])

  /**
   * Handle authentication result and update stores
   */
  const handleAuthenticationResult = async (result: any) => {
    try {
      // Create StandardToken from MSAL result
      const tokenData: StandardToken = {
        accessToken: result.accessToken,
        refreshToken: '', // B2C doesn't provide refresh tokens in browser
        userId: result.account?.localAccountId || '',
        email: result.account?.username || '',
        roles: (result.idTokenClaims as any)?.roles || [],
        organizationIds: (result.idTokenClaims as any)?.organizations || [],
        tenantId: (result.idTokenClaims as any)?.tid || '',
        expiresAt: result.expiresOn?.getTime() || (Date.now() + 3600000),
        scope: result.scopes?.join(' ') || '',
        tokenType: 'Bearer'
      }
      
      // Update auth store
      setToken(tokenData)

      // Get and update user profile
      const userProfile = await msalAuthService.getCurrentUser()
      if (userProfile) {
        await updateProfile(userProfile)
      }
    } catch (error) {
      console.error('[MSAL Provider] Failed to handle authentication result:', error)
      throw error
    }
  }

  /**
   * Sync user data from MSAL
   */
  const syncUserData = async () => {
    try {
      const currentUser = await msalAuthService.getCurrentUser()
      const accessToken = await msalAuthService.getAccessToken()
      
      if (currentUser && accessToken) {
        // Update user profile
        await updateProfile(currentUser)
        
        // Create token object
        const tokenData: StandardToken = {
          accessToken,
          refreshToken: '',
          userId: currentUser.id,
          email: currentUser.email,
          roles: currentUser.roles,
          organizationIds: currentUser.organizationIds || [],
          tenantId: currentUser.tenantId || '',
          expiresAt: Date.now() + 3600000, // 1 hour default
          scope: 'openid profile email',
          tokenType: 'Bearer'
        }
        
        setToken(tokenData)
      }
    } catch (error) {
      console.error('[MSAL Provider] Failed to sync user data:', error)
      clearAuth()
    }
  }

  /**
   * Sign in with specified policy
   */
  const signIn = async (policy: B2CPolicyType = B2CPolicyType.SIGN_UP_SIGN_IN, method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      throw new Error('MSAL not initialized')
    }

    setError(null)
    setIsLoading(true)

    try {
      const result = await msalAuthService.signIn(policy, method)
      await handleAuthenticationResult(result)

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Welcome!',
        message: `Successfully signed in as ${result.email}`
      })

    } catch (error: any) {
      console.error('[MSAL Provider] Sign in failed:', error)
      const errorMessage = error.message || 'Failed to sign in. Please try again.'
      setError(errorMessage)
      
      addNotification({
        type: 'error',
        title: 'Sign In Failed',
        message: errorMessage
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Sign up new user
   */
  const signUp = async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      throw new Error('MSAL not initialized')
    }

    setError(null)
    setIsLoading(true)

    try {
      const result = await msalAuthService.signUp(method)
      await handleAuthenticationResult(result)

      addNotification({
        type: 'success',
        title: 'Account Created!',
        message: `Welcome ${result.email}! Your account has been created successfully.`
      })

    } catch (error: any) {
      console.error('[MSAL Provider] Sign up failed:', error)
      const errorMessage = error.message || 'Failed to create account. Please try again.'
      setError(errorMessage)
      
      addNotification({
        type: 'error',
        title: 'Sign Up Failed',
        message: errorMessage
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Sign out user
   */
  const signOut = async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      throw new Error('MSAL not initialized')
    }

    setError(null)
    setIsLoading(true)

    try {
      await msalAuthService.signOut(method)
      clearAuth()

      addNotification({
        type: 'success',
        title: 'Signed Out',
        message: 'You have been successfully signed out.'
      })

    } catch (error: any) {
      console.error('[MSAL Provider] Sign out failed:', error)
      const errorMessage = error.message || 'Failed to sign out. Please try again.'
      setError(errorMessage)
      
      addNotification({
        type: 'error',
        title: 'Sign Out Failed',
        message: errorMessage
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Reset password
   */
  const resetPassword = async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      throw new Error('MSAL not initialized')
    }

    setError(null)
    setIsLoading(true)

    try {
      await msalAuthService.resetPassword(method)
      
      addNotification({
        type: 'success',
        title: 'Password Reset',
        message: 'Password reset process has been initiated. Please follow the instructions.'
      })

    } catch (error: any) {
      console.error('[MSAL Provider] Password reset failed:', error)
      const errorMessage = error.message || 'Failed to initiate password reset. Please try again.'
      setError(errorMessage)
      
      addNotification({
        type: 'error',
        title: 'Password Reset Failed',
        message: errorMessage
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Edit profile
   */
  const editProfile = async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      throw new Error('MSAL not initialized')
    }

    setError(null)
    setIsLoading(true)

    try {
      const result = await msalAuthService.editProfile(method)

      // If we got a new token, update the store
      if (result && 'accessToken' in result) {
        setToken(result)
      }

      // Refresh user profile
      await syncUserData()

      addNotification({
        type: 'success',
        title: 'Profile Updated',
        message: 'Your profile has been updated successfully.'
      })

    } catch (error: any) {
      console.error('[MSAL Provider] Profile edit failed:', error)
      const errorMessage = error.message || 'Failed to update profile. Please try again.'
      setError(errorMessage)

      addNotification({
        type: 'error',
        title: 'Profile Update Failed',
        message: errorMessage
      })
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Get access token
   */
  const getAccessToken = async (): Promise<string | null> => {
    if (!isInitialized) return null
    return await msalAuthService.getAccessToken()
  }

  /**
   * Refresh access token
   */
  const refreshToken = async () => {
    if (!isInitialized) return

    try {
      await syncUserData()
    } catch (error) {
      console.error('[MSAL Provider] Token refresh failed:', error)
      // Don't show notification for silent failures
    }
  }

  /**
   * Clear error state
   */
  const clearError = () => {
    setError(null)
  }

  /**
   * Check if user has specific role
   */
  const hasRole = (role: string): boolean => {
    return user?.roles?.includes(role) || false
  }

  /**
   * Check if user has specific permission
   */
  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false
  }

  /**
   * Check if user is in specific organization
   */
  const isInOrganization = (organizationId: string): boolean => {
    return user?.organizationIds?.includes(organizationId) || false
  }

  // Context value
  const contextValue: MSALAuthContextType = {
    // State
    isInitialized,
    isAuthenticated: isAuthenticated && isInitialized,
    isLoading: isLoading || !isInitialized,
    user,
    error,

    // Authentication Methods
    signIn,
    signUp,
    signOut,
    resetPassword,
    editProfile,

    // Token Management
    getAccessToken,
    refreshToken,

    // Utility Methods
    clearError,
    hasRole,
    hasPermission,
    isInOrganization,
  }

  return (
    <MSALAuthContext.Provider value={contextValue}>
      {children}
    </MSALAuthContext.Provider>
  )
}

/**
 * Hook to use MSAL Auth Context
 */
export function useMSALAuth(): MSALAuthContextType {
  const context = useContext(MSALAuthContext)

  if (!context) {
    throw new Error('useMSALAuth must be used within a MSALAuthProvider')
  }

  return context
}

// Export types for external use
export type { MSALAuthContextType }
export { B2CPolicyType, AuthMethod }
