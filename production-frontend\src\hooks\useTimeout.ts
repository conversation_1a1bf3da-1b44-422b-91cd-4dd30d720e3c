import { useEffect, useRef, useCallback } from 'react'

/**
 * Timeout Hook
 * Manages setTimeout with automatic cleanup
 */

export function useTimeout(
  callback: () => void,
  delay: number | null,
  options: {
    enabled?: boolean
  } = {}
): void {
  const { enabled = true } = options
  const savedCallback = useRef<() => void>()

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  // Set up the timeout
  useEffect(() => {
    if (!enabled || delay === null) return

    const tick = () => {
      savedCallback.current?.()
    }

    const id = setTimeout(tick, delay)
    return () => clearTimeout(id)
  }, [delay, enabled])
}

/**
 * Advanced timeout hook with controls
 */
export function useTimeoutControls(
  callback: () => void,
  delay: number | null
) {
  const savedCallback = useRef<() => void>()
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  const start = useCallback(() => {
    if (delay === null) return

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    const tick = () => {
      savedCallback.current?.()
      timeoutRef.current = undefined
    }

    timeoutRef.current = setTimeout(tick, delay)
  }, [delay])

  const clear = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = undefined
    }
  }, [])

  const reset = useCallback(() => {
    clear()
    start()
  }, [clear, start])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clear()
    }
  }, [clear])

  return {
    start,
    clear,
    reset,
    isActive: timeoutRef.current !== undefined,
  }
}
