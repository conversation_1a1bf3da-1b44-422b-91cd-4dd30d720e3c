import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import { notificationAnalyticsService } from '../../services/notification-analytics-service';

/**
 * Notification Analytics Dashboard Component
 *
 * Displays notification analytics data for administrators
 */
const NotificationAnalyticsDashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'7d' | '24h' | '30d' | '90d'>('7d');
  const analyticsService = notificationAnalyticsService;

  // Load analytics data
  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        setLoading(true);
        const data = await analyticsService.getAnalytics({
          organizationId: 'default-org',
          dateRange: {
            start: new Date(Date.now() - (timeRange === '24h' ? 24 * 60 * 60 * 1000 :
                                        timeRange === '7d' ? 7 * 24 * 60 * 60 * 1000 :
                                        timeRange === '30d' ? 30 * 24 * 60 * 60 * 1000 :
                                        90 * 24 * 60 * 60 * 1000)).toISOString(),
            end: new Date().toISOString()
          }
        });
        setAnalytics(data);
        setError(null);
      } catch (err) {
        setError('Failed to load notification analytics');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadAnalytics();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <Spinner />
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {error || 'Failed to load notification analytics'}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Notification Analytics Dashboard</h2>
      </div>

      <div className="flex gap-4">
        <Select value={timeRange} onValueChange={(value: '7d' | '24h' | '30d' | '90d') => setTimeRange(value)}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="24h">Last 24 Hours</SelectItem>
            <SelectItem value="7d">Last 7 Days</SelectItem>
            <SelectItem value="30d">Last 30 Days</SelectItem>
            <SelectItem value="90d">Last 90 Days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.counts?.delivery || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">View Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.rates?.view || 0}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.rates?.click || 0}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Dismiss Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.rates?.dismiss || 0}%</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Analytics Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Detailed analytics charts and breakdowns will be available in a future update.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationAnalyticsDashboard;
