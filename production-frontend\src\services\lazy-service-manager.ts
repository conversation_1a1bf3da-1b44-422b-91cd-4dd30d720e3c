/**
 * Lazy Service Manager - Production-Ready Service Initialization
 * Manages lazy loading of services to optimize performance and reduce unnecessary resource usage
 * Services are only initialized when explicitly requested by components that need them
 */

interface ServiceConfig {
  name: string
  initializer: () => Promise<any>
  dependencies?: string[]
  scope: 'global' | 'session' | 'component'
  autoCleanup?: boolean
  maxIdleTime?: number // milliseconds
}

interface ServiceInstance {
  service: any
  lastAccessed: number
  referenceCount: number
  scope: string
  autoCleanup: boolean
  maxIdleTime: number
}

class LazyServiceManager {
  private services = new Map<string, ServiceInstance>()
  private serviceConfigs = new Map<string, ServiceConfig>()
  private initializationPromises = new Map<string, Promise<any>>()
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor() {
    // Start cleanup interval to remove idle services
    this.startCleanupInterval()
  }

  /**
   * Register a service configuration for lazy loading
   */
  registerService(config: ServiceConfig): void {
    this.serviceConfigs.set(config.name, config)
  }

  /**
   * Get a service instance, initializing it if necessary
   */
  async getService<T = any>(serviceName: string): Promise<T> {
    // Check if service is already initialized
    const existingService = this.services.get(serviceName)
    if (existingService) {
      existingService.lastAccessed = Date.now()
      existingService.referenceCount++
      return existingService.service as T
    }

    // Check if initialization is in progress
    const existingInitPromise = this.initializationPromises.get(serviceName)
    if (existingInitPromise) {
      return await existingInitPromise as T
    }

    // Get service configuration
    const config = this.serviceConfigs.get(serviceName)
    if (!config) {
      throw new Error(`Service '${serviceName}' is not registered`)
    }

    // Initialize dependencies first
    if (config.dependencies) {
      await Promise.all(
        config.dependencies.map(dep => this.getService(dep))
      )
    }

    // Initialize the service
    const serviceInitPromise = this.initializeService(config)
    this.initializationPromises.set(serviceName, serviceInitPromise)

    try {
      const service = await serviceInitPromise
      
      // Store the initialized service
      this.services.set(serviceName, {
        service,
        lastAccessed: Date.now(),
        referenceCount: 1,
        scope: config.scope,
        autoCleanup: config.autoCleanup ?? true,
        maxIdleTime: config.maxIdleTime ?? 300000 // 5 minutes default
      })

      console.info(`Service '${serviceName}' initialized successfully`)
      return service as T
    } catch (error) {
      console.error(`Failed to initialize service '${serviceName}':`, error)
      throw error
    } finally {
      this.initializationPromises.delete(serviceName)
    }
  }

  /**
   * Release a service reference
   */
  releaseService(serviceName: string): void {
    const service = this.services.get(serviceName)
    if (service) {
      service.referenceCount = Math.max(0, service.referenceCount - 1)
      
      // If no more references and component-scoped, cleanup immediately
      if (service.referenceCount === 0 && service.scope === 'component') {
        this.cleanupService(serviceName)
      }
    }
  }

  /**
   * Force cleanup of a service
   */
  cleanupService(serviceName: string): void {
    const service = this.services.get(serviceName)
    if (service) {
      // Call cleanup method if available
      if (service.service && typeof service.service.cleanup === 'function') {
        try {
          service.service.cleanup()
        } catch (error) {
          console.warn(`Error during cleanup of service '${serviceName}':`, error)
        }
      }

      this.services.delete(serviceName)
      console.info(`Service '${serviceName}' cleaned up`)
    }
  }

  /**
   * Get service status
   */
  getServiceStatus(serviceName: string): {
    initialized: boolean
    lastAccessed?: number
    referenceCount?: number
    scope?: string
  } {
    const service = this.services.get(serviceName)
    return {
      initialized: !!service,
      lastAccessed: service?.lastAccessed,
      referenceCount: service?.referenceCount,
      scope: service?.scope
    }
  }

  /**
   * Get all active services
   */
  getActiveServices(): string[] {
    return Array.from(this.services.keys())
  }

  /**
   * Initialize a service using its configuration
   */
  private async initializeService(config: ServiceConfig): Promise<any> {
    try {
      return await config.initializer()
    } catch (error) {
      console.error(`Service initialization failed for '${config.name}':`, error)
      throw error
    }
  }

  /**
   * Start the cleanup interval for idle services
   */
  private startCleanupInterval(): void {
    if (this.cleanupInterval) return

    this.cleanupInterval = setInterval(() => {
      const now = Date.now()
      
      for (const [serviceName, service] of this.services.entries()) {
        const isIdle = (now - service.lastAccessed) > service.maxIdleTime
        const shouldCleanup = service.autoCleanup && 
                             service.referenceCount === 0 && 
                             isIdle &&
                             service.scope !== 'global'

        if (shouldCleanup) {
          console.info(`Cleaning up idle service: ${serviceName}`)
          this.cleanupService(serviceName)
        }
      }
    }, 60000) // Check every minute
  }

  /**
   * Stop the cleanup interval
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }

    // Cleanup all services
    for (const serviceName of this.services.keys()) {
      this.cleanupService(serviceName)
    }
  }
}

// Export singleton instance
export const lazyServiceManager = new LazyServiceManager()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    lazyServiceManager.destroy()
  })
}

/**
 * React hook for lazy service access
 */
import { useEffect, useRef, useState } from 'react'

export function useLazyService<T = any>(serviceName: string) {
  const [service, setService] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const serviceRef = useRef<T | null>(null)

  useEffect(() => {
    let mounted = true

    const initializeService = async () => {
      if (serviceRef.current) return // Already initialized

      setIsLoading(true)
      setError(null)

      try {
        const serviceInstance = await lazyServiceManager.getService<T>(serviceName)
        
        if (mounted) {
          serviceRef.current = serviceInstance
          setService(serviceInstance)
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err : new Error(String(err)))
        }
      } finally {
        if (mounted) {
          setIsLoading(false)
        }
      }
    }

    initializeService()

    return () => {
      mounted = false
      if (serviceRef.current) {
        lazyServiceManager.releaseService(serviceName)
        serviceRef.current = null
      }
    }
  }, [serviceName])

  return {
    service,
    isLoading,
    error,
    isInitialized: !!service
  }
}

/**
 * React hook for conditional service loading
 */
export function useConditionalService<T = any>(
  serviceName: string, 
  condition: boolean
) {
  const [service, setService] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const serviceRef = useRef<T | null>(null)

  useEffect(() => {
    if (!condition) {
      // Release service if condition is false
      if (serviceRef.current) {
        lazyServiceManager.releaseService(serviceName)
        serviceRef.current = null
        setService(null)
      }
      return
    }

    let mounted = true

    const initializeService = async () => {
      if (serviceRef.current) return // Already initialized

      setIsLoading(true)
      setError(null)

      try {
        const serviceInstance = await lazyServiceManager.getService<T>(serviceName)
        
        if (mounted) {
          serviceRef.current = serviceInstance
          setService(serviceInstance)
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err : new Error(String(err)))
        }
      } finally {
        if (mounted) {
          setIsLoading(false)
        }
      }
    }

    initializeService()

    return () => {
      mounted = false
      if (serviceRef.current) {
        lazyServiceManager.releaseService(serviceName)
        serviceRef.current = null
      }
    }
  }, [serviceName, condition])

  return {
    service,
    isLoading,
    error,
    isInitialized: !!service
  }
}
