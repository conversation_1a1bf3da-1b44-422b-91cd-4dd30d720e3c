/**
 * User Preferences Hooks
 * Manages user preferences and settings
 */

import { useCallback } from 'react'
import { useAuthStore } from './useAuthStore'
import type { UserPreferences, NotificationPreferences, DashboardPreferences } from '../types'

/**
 * Main user preferences hook
 */
export function useUserPreferences() {
  const { user, updateProfile } = useAuthStore()
  
  // UserContext doesn't have preferences, use default values
  const preferences = {
    notifications: {
      email: { enabled: true, frequency: 'daily' as const, types: [] },
      push: { enabled: true, types: [] },
      inApp: { enabled: true, types: [] }
    },
    dashboard: {
      layout: 'grid' as const,
      widgets: [],
      defaultView: 'overview',
      autoRefresh: true,
      refreshInterval: 300
    },
    privacy: {
      profileVisibility: 'organization' as const,
      activityVisibility: 'organization' as const,
      allowAnalytics: true,
      allowMarketing: false
    },
    accessibility: {
      fontSize: 'medium' as const,
      highContrast: false,
      reduceMotion: false,
      screenReader: false
    }
  }

  const updatePreferences = useCallback(async (newPreferences: Partial<UserPreferences>) => {
    if (!user) return

    const updatedUser = {
      ...user,
      preferences: {
        ...preferences,
        ...newPreferences
      }
    }

    await updateProfile(updatedUser)
  }, [user, preferences, updateProfile])

  return {
    preferences,
    updatePreferences,
  }
}

/**
 * Notification preferences hook
 */
export function useNotificationPreferences() {
  const { preferences, updatePreferences } = useUserPreferences()
  
  const notificationPrefs = preferences.notifications

  const updateNotificationPreferences = useCallback(async (updates: Partial<NotificationPreferences>) => {
    await updatePreferences({
      notifications: {
        ...notificationPrefs,
        ...updates
      }
    })
  }, [notificationPrefs, updatePreferences])

  const toggleEmailNotifications = useCallback(async (enabled: boolean) => {
    await updateNotificationPreferences({
      email: {
        ...notificationPrefs.email,
        enabled
      }
    })
  }, [notificationPrefs.email, updateNotificationPreferences])

  const togglePushNotifications = useCallback(async (enabled: boolean) => {
    await updateNotificationPreferences({
      push: {
        ...notificationPrefs.push,
        enabled
      }
    })
  }, [notificationPrefs.push, updateNotificationPreferences])

  const toggleInAppNotifications = useCallback(async (enabled: boolean) => {
    await updateNotificationPreferences({
      inApp: {
        ...notificationPrefs.inApp,
        enabled
      }
    })
  }, [notificationPrefs.inApp, updateNotificationPreferences])

  return {
    notificationPrefs,
    updateNotificationPreferences,
    toggleEmailNotifications,
    togglePushNotifications,
    toggleInAppNotifications,
  }
}

/**
 * Dashboard preferences hook
 */
export function useDashboardPreferences() {
  const { preferences, updatePreferences } = useUserPreferences()
  
  const dashboardPrefs = preferences.dashboard

  const updateDashboardPreferences = useCallback(async (updates: Partial<DashboardPreferences>) => {
    await updatePreferences({
      dashboard: {
        ...dashboardPrefs,
        ...updates
      }
    })
  }, [dashboardPrefs, updatePreferences])

  const setLayout = useCallback(async (_layout: 'grid' | 'list') => {
    // TODO: Implement layout setting when backend supports it
    // await updateDashboardPreferences({ layout })
  }, [updateDashboardPreferences])

  const setDefaultView = useCallback(async (defaultView: string) => {
    await updateDashboardPreferences({ defaultView: defaultView as 'grid' | 'list' })
  }, [updateDashboardPreferences])

  const toggleAutoRefresh = useCallback(async (autoRefresh: boolean) => {
    await updateDashboardPreferences({ autoRefresh })
  }, [updateDashboardPreferences])

  const setRefreshInterval = useCallback(async (refreshInterval: number) => {
    await updateDashboardPreferences({ refreshInterval })
  }, [updateDashboardPreferences])

  return {
    dashboardPrefs,
    updateDashboardPreferences,
    setLayout,
    setDefaultView,
    toggleAutoRefresh,
    setRefreshInterval,
  }
}

/**
 * Theme preferences hook
 */
export function useThemePreferences() {
  const { user, updateProfile } = useAuthStore()

  // Get current theme from user preferences or localStorage
  const getCurrentTheme = (): 'light' | 'dark' | 'system' => {
    // UserContext doesn't have preferences, check localStorage instead
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('theme-preference')
      if (stored && ['light', 'dark', 'system'].includes(stored)) {
        return stored as 'light' | 'dark' | 'system'
      }
    }

    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('theme-preference')
      if (stored && ['light', 'dark', 'system'].includes(stored)) {
        return stored as 'light' | 'dark' | 'system'
      }
    }

    return 'system'
  }

  const currentTheme = getCurrentTheme()

  const updateTheme = useCallback(async (theme: 'light' | 'dark' | 'system') => {
    if (!user) return

    try {
      // Update theme preference via API
      const response = await fetch('/user/preferences/theme', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ theme })
      })

      if (!response.ok) {
        throw new Error(`Failed to update theme: ${response.statusText}`)
      }

      // Update local storage for immediate effect
      localStorage.setItem('theme-preference', theme)

      // Apply theme to document
      const root = document.documentElement
      if (theme === 'dark') {
        root.classList.add('dark')
      } else if (theme === 'light') {
        root.classList.remove('dark')
      } else {
        // System theme
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        if (prefersDark) {
          root.classList.add('dark')
        } else {
          root.classList.remove('dark')
        }
      }

      // Theme preference is stored in localStorage for now
      // UserContext doesn't support preferences yet
    } catch (error) {
      console.error('Failed to update theme preference:', error)
    }
  }, [user, updateProfile])

  return {
    currentTheme,
    updateTheme,
  }
}

/**
 * Convenience hook for updating user preferences
 */
export function useUpdateUserPreferences() {
  const { updatePreferences } = useUserPreferences()
  return updatePreferences
}

/**
 * Convenience hook for updating theme
 */
export function useUpdateTheme() {
  const { updateTheme } = useThemePreferences()
  return updateTheme
}

/**
 * Privacy preferences hook
 */
export function usePrivacyPreferences() {
  const { preferences, updatePreferences } = useUserPreferences()
  
  const privacyPrefs = preferences.privacy

  const updatePrivacyPreferences = useCallback(async (updates: Partial<typeof privacyPrefs>) => {
    await updatePreferences({
      privacy: {
        ...privacyPrefs,
        ...updates
      }
    })
  }, [privacyPrefs, updatePreferences])

  return {
    privacyPrefs,
    updatePrivacyPreferences,
  }
}

/**
 * Accessibility preferences hook
 */
export function useAccessibilityPreferences() {
  const { preferences, updatePreferences } = useUserPreferences()
  
  const accessibilityPrefs = preferences.accessibility

  const updateAccessibilityPreferences = useCallback(async (updates: Partial<typeof accessibilityPrefs>) => {
    await updatePreferences({
      accessibility: {
        ...accessibilityPrefs,
        ...updates
      }
    })
  }, [accessibilityPrefs, updatePreferences])

  return {
    accessibilityPrefs,
    updateAccessibilityPreferences,
  }
}
