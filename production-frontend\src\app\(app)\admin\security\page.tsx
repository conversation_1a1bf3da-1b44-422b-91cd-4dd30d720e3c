"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { backendApiClient } from "@/services/backend-api-client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import {
  <PERSON>,
  <PERSON>,
  Globe,
  Clock,
  Save
} from "lucide-react";
import { AuditLogViewer } from "@/components/security/audit-log-viewer";
import { AdminOnly, SecurityDashboard } from "@/components/security";

// Schema for security settings
const securitySettingsSchema = z.object({
  // Authentication settings
  mfaRequired: z.boolean().default(false),
  mfaGracePeriodDays: z.number().min(0).max(90).default(30),
  passwordExpiryDays: z.number().min(0).max(365).default(90),
  passwordMinLength: z.number().min(8).max(128).default(12),
  passwordRequireUppercase: z.boolean().default(true),
  passwordRequireLowercase: z.boolean().default(true),
  passwordRequireNumbers: z.boolean().default(true),
  passwordRequireSymbols: z.boolean().default(true),
  passwordHistoryCount: z.number().min(0).max(24).default(5),

  // Session settings
  sessionTimeoutMinutes: z.number().min(5).max(1440).default(60),
  sessionInactivityTimeoutMinutes: z.number().min(1).max(60).default(15),

  // IP restrictions
  enableIpRestrictions: z.boolean().default(false),
  allowedIpRanges: z.string().optional(),

  // Audit settings
  enableAuditLogging: z.boolean().default(true),
  auditLogRetentionDays: z.number().min(30).max(3650).default(365),

  // Security alerts
  enableSecurityAlerts: z.boolean().default(true),
  alertOnFailedLogins: z.boolean().default(true),
  alertOnSuspiciousActivity: z.boolean().default(true),
  alertEmailRecipients: z.string().optional(),
});

type SecuritySettings = z.infer<typeof securitySettingsSchema>;

export default function SecuritySettingsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("authentication");

  // Form setup
  const form = useForm<SecuritySettings>({
    resolver: zodResolver(securitySettingsSchema),
    defaultValues: {
      mfaRequired: false,
      mfaGracePeriodDays: 30,
      passwordExpiryDays: 90,
      passwordMinLength: 12,
      passwordRequireUppercase: true,
      passwordRequireLowercase: true,
      passwordRequireNumbers: true,
      passwordRequireSymbols: true,
      passwordHistoryCount: 5,
      sessionTimeoutMinutes: 60,
      sessionInactivityTimeoutMinutes: 15,
      enableIpRestrictions: false,
      enableAuditLogging: true,
      auditLogRetentionDays: 365,
      enableSecurityAlerts: true,
      alertOnFailedLogins: true,
      alertOnSuspiciousActivity: true,
    },
  });

  // Query to get current security settings
  const { isLoading } = useQuery({
    queryKey: ['securitySettings'],
    queryFn: async () => {
      const response = await backendApiClient.getSecuritySettings();
      form.reset(response);
      return response;
    }
  });

  // Mutation to update security settings
  const mutation = useMutation({
    mutationFn: async (data: SecuritySettings) => {
      return await backendApiClient.updateSecuritySettings(data);
    },
    onSuccess: () => {
      toast({
        title: "Security settings updated",
        description: "The security settings have been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['securitySettings'] });
    },
    onError: () => {
      toast({
        title: "Error updating security settings",
        description: "There was a problem updating the security settings.",
        variant: "destructive",
      });
    },
  });

  // Form submission handler
  const onSubmit = (data: SecuritySettings) => {
    mutation.mutate(data);
  };

  return (
    <AdminOnly>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Security Settings</h1>
          <p className="text-muted-foreground">
            Configure security settings for your organization
          </p>
        </div>

        <SecurityDashboard />

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 md:grid-cols-4 lg:w-[600px]">
            <TabsTrigger value="authentication">
              <Key className="h-4 w-4 mr-2" /> Authentication
            </TabsTrigger>
            <TabsTrigger value="sessions">
              <Clock className="h-4 w-4 mr-2" /> Sessions
            </TabsTrigger>
            <TabsTrigger value="restrictions">
              <Globe className="h-4 w-4 mr-2" /> Restrictions
            </TabsTrigger>
            <TabsTrigger value="audit">
              <Shield className="h-4 w-4 mr-2" /> Audit
            </TabsTrigger>
          </TabsList>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <TabsContent value="authentication">
                <Card>
                  <CardHeader>
                    <CardTitle>Authentication Settings</CardTitle>
                    <CardDescription>
                      Configure authentication requirements and password policies
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Multi-Factor Authentication</h3>
                      <FormField
                        control={form.control}
                        name="mfaRequired"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Require MFA
                              </FormLabel>
                              <FormDescription>
                                Require all users to set up multi-factor authentication
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      {form.watch("mfaRequired") && (
                        <FormField
                          control={form.control}
                          name="mfaGracePeriodDays"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>MFA Grace Period (Days)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                />
                              </FormControl>
                              <FormDescription>
                                Number of days users have to set up MFA after it's required
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Password Policy</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="passwordMinLength"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Minimum Password Length</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="passwordExpiryDays"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Password Expiry (Days)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                />
                              </FormControl>
                              <FormDescription>
                                0 = never expire
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="passwordRequireUppercase"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel>Require Uppercase</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="passwordRequireLowercase"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel>Require Lowercase</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="passwordRequireNumbers"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel>Require Numbers</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="passwordRequireSymbols"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel>Require Symbols</FormLabel>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="sessions">
                <Card>
                  <CardHeader>
                    <CardTitle>Session Settings</CardTitle>
                    <CardDescription>
                      Configure session timeouts and security
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="sessionTimeoutMinutes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Session Timeout (Minutes)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Maximum session duration before requiring re-authentication
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="sessionInactivityTimeoutMinutes"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Inactivity Timeout (Minutes)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Time of inactivity before session is terminated
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="restrictions">
                <Card>
                  <CardHeader>
                    <CardTitle>Access Restrictions</CardTitle>
                    <CardDescription>
                      Configure IP restrictions and access controls
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <FormField
                      control={form.control}
                      name="enableIpRestrictions"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Enable IP Restrictions
                            </FormLabel>
                            <FormDescription>
                              Restrict access to specific IP addresses or ranges
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    {form.watch("enableIpRestrictions") && (
                      <FormField
                        control={form.control}
                        name="allowedIpRanges"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Allowed IP Ranges</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="e.g. ***********/24, ********" />
                            </FormControl>
                            <FormDescription>
                              Comma-separated list of IP addresses or CIDR ranges
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="audit">
                <Card>
                  <CardHeader>
                    <CardTitle>Audit & Logging</CardTitle>
                    <CardDescription>
                      Configure audit logging and security alerts
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Audit Logging</h3>
                      <FormField
                        control={form.control}
                        name="enableAuditLogging"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Enable Audit Logging
                              </FormLabel>
                              <FormDescription>
                                Log security events and user activities
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      {form.watch("enableAuditLogging") && (
                        <FormField
                          control={form.control}
                          name="auditLogRetentionDays"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Log Retention (Days)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                />
                              </FormControl>
                              <FormDescription>
                                Number of days to retain audit logs
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>

                    <Separator />

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Security Alerts</h3>
                      <FormField
                        control={form.control}
                        name="enableSecurityAlerts"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Enable Security Alerts
                              </FormLabel>
                              <FormDescription>
                                Send alerts for security events
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      {form.watch("enableSecurityAlerts") && (
                        <>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name="alertOnFailedLogins"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel>Alert on Failed Logins</FormLabel>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="alertOnSuspiciousActivity"
                              render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                  <div className="space-y-0.5">
                                    <FormLabel>Alert on Suspicious Activity</FormLabel>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                          </div>

                          <FormField
                            control={form.control}
                            name="alertEmailRecipients"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Alert Email Recipients</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="<EMAIL>, <EMAIL>" />
                                </FormControl>
                                <FormDescription>
                                  Comma-separated list of email addresses to receive alerts
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <div className="mt-6">
                  <AuditLogViewer
                    showFilters={true}
                    // Using pageSize instead of deprecated maxLogs
                    initialFilter={{ pageSize: 10 }}
                  />
                </div>
              </TabsContent>

              <div className="mt-6 flex justify-end">
                <Button
                  type="submit"
                  disabled={isLoading || mutation.isPending}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  Save Settings
                </Button>
              </div>
            </form>
          </Form>
        </Tabs>
      </div>
    </AdminOnly>
  );
}
