'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function TemplatesRedirectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get projectId from query params
  const projectId = searchParams?.get('projectId');

  useEffect(() => {
    // If we have a projectId, redirect to project templates
    if (projectId) {
      router.replace(`/projects/${projectId}/templates`);
    } else {
      // Otherwise, redirect to projects page
      router.replace('/projects');
    }
  }, [projectId, router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
      <p>Redirecting to templates...</p>
    </div>
  );
}
