/**
 * AI Provider - Lazy-loaded AI Services for AI Features Only
 * This provider should only be used by components that require AI functionality
 * It prevents unnecessary AI service initialization on non-AI pages
 */

"use client"

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { useAIServices } from '@/hooks/useLazyServices'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'

interface AIContextType {
  aiService: any | null
  documentAnalysis: any | null
  isLoading: boolean
  hasAccess: boolean
  isAvailable: boolean
  error: Error | null
  
  // AI Operations
  analyzeDocument: (documentId: string, options?: any) => Promise<any>
  generateSummary: (content: string) => Promise<string>
  extractEntities: (content: string) => Promise<any[]>
  askQuestion: (question: string, context?: string) => Promise<string>
  
  // Document Analysis
  processDocument: (file: File, options?: any) => Promise<any>
  getAnalysisResults: (documentId: string) => Promise<any>
}

const AIContext = createContext<AIContextType | null>(null)

interface AIProviderProps {
  children: ReactNode
  autoLoad?: boolean
}

export function AIProvider({ children, autoLoad = false }: AIProviderProps) {
  const { user } = useAuth()
  const [error, setError] = useState<Error | null>(null)
  
  // Only load AI services when explicitly enabled
  const {
    aiService,
    documentAnalysis,
    isLoading,
    hasAccess,
    isAvailable
  } = useAIServices(autoLoad)

  // AI Operations
  const analyzeDocument = useCallback(async (documentId: string, options?: any) => {
    if (!documentAnalysis) {
      throw new Error('Document analysis service not available')
    }

    try {
      setError(null)
      const result = await documentAnalysis.analyzeDocument(documentId, options)
      toast.success('Document analysis completed')
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Document analysis failed')
      throw error
    }
  }, [documentAnalysis])

  const generateSummary = useCallback(async (content: string): Promise<string> => {
    if (!aiService) {
      throw new Error('AI service not available')
    }

    try {
      setError(null)
      const summary = await aiService.generateSummary(content)
      return summary
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Summary generation failed')
      throw error
    }
  }, [aiService])

  const extractEntities = useCallback(async (content: string): Promise<any[]> => {
    if (!aiService) {
      throw new Error('AI service not available')
    }

    try {
      setError(null)
      const entities = await aiService.extractEntities(content)
      return entities
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Entity extraction failed')
      throw error
    }
  }, [aiService])

  const askQuestion = useCallback(async (question: string, context?: string): Promise<string> => {
    if (!aiService) {
      throw new Error('AI service not available')
    }

    try {
      setError(null)
      const answer = await aiService.askQuestion(question, context)
      return answer
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Question answering failed')
      throw error
    }
  }, [aiService])

  const processDocument = useCallback(async (file: File, options?: any) => {
    if (!documentAnalysis) {
      throw new Error('Document analysis service not available')
    }

    try {
      setError(null)
      const result = await documentAnalysis.processDocument(file, options)
      toast.success('Document processing started')
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Document processing failed')
      throw error
    }
  }, [documentAnalysis])

  const getAnalysisResults = useCallback(async (documentId: string) => {
    if (!documentAnalysis) {
      throw new Error('Document analysis service not available')
    }

    try {
      setError(null)
      return await documentAnalysis.getAnalysisResults(documentId)
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      throw error
    }
  }, [documentAnalysis])

  // Show access denied message for unauthorized users
  useEffect(() => {
    if (autoLoad && !hasAccess && user) {
      toast.error('AI features are not available for your account')
    }
  }, [autoLoad, hasAccess, user])

  const value: AIContextType = {
    aiService,
    documentAnalysis,
    isLoading,
    hasAccess,
    isAvailable,
    error,
    
    // AI Operations
    analyzeDocument,
    generateSummary,
    extractEntities,
    askQuestion,
    
    // Document Analysis
    processDocument,
    getAnalysisResults,
  }

  return (
    <AIContext.Provider value={value}>
      {children}
    </AIContext.Provider>
  )
}

// Hook to use AI context
export function useAI() {
  const context = useContext(AIContext)
  if (!context) {
    throw new Error('useAI must be used within an AIProvider')
  }
  return context
}

// Hook for conditional AI loading
export function useConditionalAI(enabled: boolean) {
  const { user } = useAuth()
  const [shouldLoad, setShouldLoad] = useState(false)

  useEffect(() => {
    setShouldLoad(enabled && !!user)
  }, [enabled, user])

  return useAIServices(shouldLoad)
}

// Hook for AI feature detection
export function useAIFeatures() {
  const { user } = useAuth()
  const [features, setFeatures] = useState({
    documentAnalysis: false,
    textGeneration: false,
    entityExtraction: false,
    questionAnswering: false,
    summarization: false
  })

  useEffect(() => {
    if (!user) {
      setFeatures({
        documentAnalysis: false,
        textGeneration: false,
        entityExtraction: false,
        questionAnswering: false,
        summarization: false
      })
      return
    }

    // Check user permissions for AI features
    const userRoles = user.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
    const hasAIAccess = userRoles.includes('user') || userRoles.includes('admin') || userRoles.includes('editor')

    if (hasAIAccess) {
      setFeatures({
        documentAnalysis: true,
        textGeneration: true,
        entityExtraction: true,
        questionAnswering: true,
        summarization: true
      })
    }
  }, [user])

  return features
}

// Component wrapper for AI features
interface AIFeatureWrapperProps {
  children: ReactNode
  feature: keyof ReturnType<typeof useAIFeatures>
  fallback?: ReactNode
}

export function AIFeatureWrapper({ children, feature, fallback }: AIFeatureWrapperProps) {
  const features = useAIFeatures()
  const hasFeature = features[feature]

  if (!hasFeature) {
    return fallback ? <>{fallback}</> : null
  }

  return (
    <AIProvider autoLoad={true}>
      {children}
    </AIProvider>
  )
}

// Higher-order component for AI-enabled components
export function withAI<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    feature?: keyof ReturnType<typeof useAIFeatures>
    fallback?: React.ComponentType<P>
  } = {}
) {
  return function AIEnabledComponent(props: P) {
    const features = useAIFeatures()
    const hasFeature = options.feature ? features[options.feature] : true

    if (!hasFeature) {
      return options.fallback ? <options.fallback {...props} /> : null
    }

    return (
      <AIProvider autoLoad={true}>
        <Component {...props} />
      </AIProvider>
    )
  }
}
