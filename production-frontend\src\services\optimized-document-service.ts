/**
 * Optimized Document Service
 * Service for optimized document operations and processing
 */

import { backendApiClient } from './backend-api-client'

export interface DocumentComparison {
  id: string
  document1Id: string
  document2Id: string
  comparisonType: 'content' | 'structure' | 'metadata' | 'comprehensive'
  results: {
    similarity: number
    differences: Array<{
      type: 'added' | 'removed' | 'modified'
      section: string
      content: string
      position?: { line: number; column: number }
    }>
    summary: {
      addedLines: number
      removedLines: number
      modifiedLines: number
      totalChanges: number
    }
    insights: string[]
  }
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: string
  completedAt?: string
  organizationId: string
}

export interface DocumentOptimization {
  id: string
  documentId: string
  optimizationType: 'performance' | 'quality' | 'accessibility' | 'seo' | 'comprehensive'
  results: {
    originalMetrics: DocumentMetrics
    optimizedMetrics: DocumentMetrics
    improvements: Array<{
      category: string
      description: string
      impact: 'low' | 'medium' | 'high'
      implemented: boolean
    }>
    recommendations: string[]
  }
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: string
  completedAt?: string
  organizationId: string
}

export interface DocumentMetrics {
  fileSize: number
  loadTime: number
  readabilityScore: number
  accessibilityScore: number
  seoScore: number
  qualityScore: number
  performanceScore: number
}

export interface DocumentProcessingResult {
  documentId: string
  processingType: string
  status: 'success' | 'failed' | 'partial'
  jobId?: string
  results: any
  metrics: {
    processingTime: number
    confidence: number
    accuracy?: number
  }
  errors?: string[]
  warnings?: string[]
}

class OptimizedDocumentService {
  /**
   * Compare document versions
   */
  async compareDocumentVersions(
    documentId: string,
    sourceVersionId: string,
    targetVersionId: string,
    projectId?: string,
    organizationId?: string,
    options?: {
      comparisonType?: 'content' | 'structure' | 'metadata' | 'comprehensive'
    }
  ): Promise<DocumentComparison> {
    return await backendApiClient.request<DocumentComparison>('/documents/versions/compare', {
      method: 'POST',
      body: JSON.stringify({
        documentId,
        sourceVersionId,
        targetVersionId,
        projectId,
        organizationId,
        comparisonType: options?.comparisonType || 'comprehensive'
      })
    })
  }

  /**
   * Get document versions
   */
  async getDocumentVersions(
    documentId: string,
    projectId?: string,
    organizationId?: string
  ): Promise<{
    documentId: string
    versions: Array<{
      id: string
      versionNumber: number
      createdAt: string
      createdBy: string
      size: number
      changes?: string
    }>
  }> {
    const params = new URLSearchParams()
    if (projectId) params.append('projectId', projectId)
    if (organizationId) params.append('organizationId', organizationId)

    const queryString = params.toString()
    const url = `/documents/${documentId}/versions${queryString ? `?${queryString}` : ''}`

    return await backendApiClient.request(url)
  }

  /**
   * Get document download URL
   */
  async getDocumentDownloadUrl(documentId: string, options?: {
    version?: string
    format?: string
  }): Promise<{
    url: string
    expiresAt: string
    filename: string
  }> {
    return await backendApiClient.request(`/documents/${documentId}/download-url`, {
      method: 'POST',
      body: JSON.stringify(options || {})
    })
  }



  /**
   * Upload signature
   */
  async uploadSignature(formData: FormData): Promise<{
    id: string
    name: string
    imageUrl: string
  }> {
    // Use fetch directly for FormData uploads
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/signatures`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('accessToken') || ''}`,
      }
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * Sign document
   */
  async signDocument(payload: {
    documentId: string
    signatureId: string
    position: {
      x: number
      y: number
      page: number
    }
    reason?: string
  }): Promise<{
    id: string
    signedDocumentId: string
    signedDocumentUrl: string
  }> {
    const response = await backendApiClient.request(`/documents/${payload.documentId}/sign`, {
      method: 'POST',
      body: JSON.stringify(payload)
    })

    return {
      id: response.id || response.signedDocumentId,
      signedDocumentId: response.signedDocumentId,
      signedDocumentUrl: response.signedDocumentUrl
    }
  }

  /**
   * Process document with AI/ML capabilities
   */
  async processDocument(payload: {
    documentId: string
    processingType: string
    organizationId: string
    options: {
      extractText?: boolean
      extractEntities?: boolean
      extractTables?: boolean
      extractKeyValuePairs?: boolean
      classify?: boolean
      vectorize?: boolean
      indexToKnowledgeGraph?: boolean
    }
  }): Promise<DocumentProcessingResult> {
    const response = await backendApiClient.request(`/documents/${payload.documentId}/process`, {
      method: 'POST',
      body: JSON.stringify({
        processingType: payload.processingType,
        organizationId: payload.organizationId,
        options: payload.options
      })
    })

    return {
      documentId: payload.documentId,
      processingType: payload.processingType,
      status: 'success',
      jobId: response.jobId || response.id,
      results: response.results || {},
      metrics: {
        processingTime: response.processingTime || 0,
        confidence: response.confidence || 0.95,
        accuracy: response.accuracy
      },
      errors: response.errors,
      warnings: response.warnings
    }
  }


  /**
   * Get document comparison result
   */
  async getComparison(comparisonId: string): Promise<DocumentComparison> {
    return await backendApiClient.request<DocumentComparison>(`/documents/comparisons/${comparisonId}`)
  }

  /**
   * Optimize document
   */
  async optimizeDocument(data: {
    documentId: string
    optimizationType?: 'performance' | 'quality' | 'accessibility' | 'seo' | 'comprehensive'
    organizationId: string
    options?: {
      compressImages?: boolean
      minifyContent?: boolean
      improveAccessibility?: boolean
      enhanceSEO?: boolean
      optimizeStructure?: boolean
    }
  }): Promise<DocumentOptimization> {
    return await backendApiClient.request<DocumentOptimization>('/documents/optimize', {
      method: 'POST',
      body: JSON.stringify({
        documentId: data.documentId,
        optimizationType: data.optimizationType || 'comprehensive',
        organizationId: data.organizationId,
        options: data.options || {}
      })
    })
  }

  /**
   * Get document optimization result
   */
  async getOptimization(optimizationId: string): Promise<DocumentOptimization> {
    return await backendApiClient.request<DocumentOptimization>(`/documents/optimizations/${optimizationId}`)
  }

  /**
   * Get document metrics
   */
  async getDocumentMetrics(documentId: string): Promise<DocumentMetrics> {
    return await backendApiClient.request<DocumentMetrics>(`/documents/${documentId}/metrics`)
  }



  /**
   * Batch process documents
   */
  async batchProcessDocuments(data: {
    documentIds: string[]
    processingType: string
    organizationId: string
    options?: Record<string, any>
  }): Promise<Array<DocumentProcessingResult>> {
    return await backendApiClient.request<Array<DocumentProcessingResult>>('/documents/batch-process-optimized', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Get processing status
   */
  async getProcessingStatus(jobId: string): Promise<{
    jobId: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    progress: number
    results?: any
    error?: string
  }> {
    return await backendApiClient.request(`/documents/processing/${jobId}/status`)
  }

  /**
   * Cancel processing job
   */
  async cancelProcessing(jobId: string): Promise<{
    jobId: string
    status: 'cancelled'
    message: string
  }> {
    return await backendApiClient.request(`/documents/processing/${jobId}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Get document insights
   */
  async getDocumentInsights(documentId: string): Promise<{
    documentId: string
    insights: Array<{
      category: string
      title: string
      description: string
      severity: 'info' | 'warning' | 'error'
      recommendation?: string
    }>
    score: number
    lastAnalyzed: string
  }> {
    return await backendApiClient.request(`/documents/${documentId}/insights`)
  }

  /**
   * Generate document preview
   */
  async generatePreview(documentId: string, options?: {
    format?: 'thumbnail' | 'preview' | 'full'
    page?: number
    quality?: 'low' | 'medium' | 'high'
  }): Promise<{
    previewUrl: string
    thumbnailUrl?: string
    metadata: {
      pages: number
      format: string
      size: number
    }
  }> {
    return await backendApiClient.request(`/documents/${documentId}/preview`, {
      method: 'POST',
      body: JSON.stringify(options || {})
    })
  }

  /**
   * Extract document metadata
   */
  async extractMetadata(documentId: string): Promise<{
    documentId: string
    metadata: {
      title?: string
      author?: string
      subject?: string
      keywords?: string[]
      createdDate?: string
      modifiedDate?: string
      pageCount?: number
      wordCount?: number
      language?: string
      format: string
      size: number
      customProperties?: Record<string, any>
    }
    extractedAt: string
  }> {
    return await backendApiClient.request(`/documents/${documentId}/metadata/extract`, {
      method: 'POST'
    })
  }

  /**
   * Update document metadata
   */
  async updateMetadata(documentId: string, metadata: Record<string, any>): Promise<{
    documentId: string
    updatedMetadata: Record<string, any>
    updatedAt: string
  }> {
    return await backendApiClient.request(`/documents/${documentId}/metadata`, {
      method: 'PUT',
      body: JSON.stringify({ metadata })
    })
  }

  /**
   * Validate document
   */
  async validateDocument(documentId: string, validationType?: string): Promise<{
    documentId: string
    isValid: boolean
    validationResults: Array<{
      rule: string
      passed: boolean
      message: string
      severity: 'info' | 'warning' | 'error'
    }>
    score: number
    validatedAt: string
  }> {
    return await backendApiClient.request(`/documents/${documentId}/validate`, {
      method: 'POST',
      body: JSON.stringify({ validationType })
    })
  }

  /**
   * Upload document
   */
  async uploadDocument(payload: {
    file: File
    name?: string
    description?: string
    type: string
    organizationId: string
    projectId?: string
    tags?: string[]
    metadata?: Record<string, any>
  }): Promise<{
    documentId: string
    name: string
    status: string
    uploadUrl?: string
  }> {
    const formData = new FormData()
    formData.append('file', payload.file)
    formData.append('name', payload.name || payload.file.name)
    formData.append('type', payload.type)
    formData.append('organizationId', payload.organizationId)

    if (payload.description) formData.append('description', payload.description)
    if (payload.projectId) formData.append('projectId', payload.projectId)
    if (payload.tags) formData.append('tags', JSON.stringify(payload.tags))
    if (payload.metadata) formData.append('metadata', JSON.stringify(payload.metadata))

    // Use fetch directly for file uploads
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/documents/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('accessToken') || ''}`,
      }
    })

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * Compare two documents
   */
  async compareDocuments(
    document1Id: string,
    document2Id: string,
    projectId?: string,
    organizationId?: string,
    options?: {
      comparisonType?: 'content' | 'structure' | 'metadata' | 'comprehensive'
    }
  ): Promise<DocumentComparison> {
    return await backendApiClient.request<DocumentComparison>('/documents/compare', {
      method: 'POST',
      body: JSON.stringify({
        document1Id,
        document2Id,
        projectId,
        organizationId,
        comparisonType: options?.comparisonType || 'comprehensive'
      })
    })
  }

  /**
   * Get documents with filtering and pagination
   */
  async getDocuments(params?: {
    organizationId?: string
    projectId?: string
    status?: string
    type?: string
    search?: string
    tags?: string[]
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  }): Promise<{
    documents: Document[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }> {
    const queryParams = new URLSearchParams()

    if (params?.organizationId) queryParams.append('organizationId', params.organizationId)
    if (params?.projectId) queryParams.append('projectId', params.projectId)
    if (params?.status) queryParams.append('status', params.status)
    if (params?.type) queryParams.append('type', params.type)
    if (params?.search) queryParams.append('search', params.search)
    if (params?.tags) queryParams.append('tags', params.tags.join(','))
    if (params?.page) queryParams.append('page', params.page.toString())
    if (params?.limit) queryParams.append('limit', params.limit.toString())
    if (params?.sortBy) queryParams.append('sortBy', params.sortBy)
    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder)

    const queryString = queryParams.toString()
    const url = `/documents${queryString ? `?${queryString}` : ''}`

    return await backendApiClient.request(url)
  }

  /**
   * Get user signatures
   */
  async getUserSignatures(userId?: string): Promise<Array<{
    id: string
    name: string
    imageUrl: string
    url?: string
    createdAt: string
    isDefault: boolean
  }>> {
    const url = userId ? `/users/${userId}/signatures` : '/users/me/signatures'
    const response = await backendApiClient.request(url)

    // Ensure each signature has a url property for backward compatibility
    return response.map((signature: any) => ({
      ...signature,
      url: signature.url || signature.imageUrl
    }))
  }

  /**
   * Get document comments
   */
  async getDocumentComments(
    documentId: string,
    projectId?: string,
    organizationId?: string
  ): Promise<any[]> {
    const params = new URLSearchParams()
    if (projectId) params.append('projectId', projectId)
    if (organizationId) params.append('organizationId', organizationId)

    const queryString = params.toString()
    const url = `/documents/${documentId}/comments/list${queryString ? `?${queryString}` : ''}`

    return await backendApiClient.request(url)
  }

  /**
   * Add document comment
   */
  async addDocumentComment(
    documentId: string,
    projectId: string,
    organizationId: string,
    content: string
  ): Promise<any> {
    return await backendApiClient.request(`/documents/${documentId}/comments/create`, {
      method: 'POST',
      body: JSON.stringify({
        content,
        projectId,
        organizationId
      })
    })
  }

  /**
   * Update document comment
   */
  async updateDocumentComment(
    documentId: string,
    commentId: string,
    updates: any,
    projectId?: string,
    organizationId?: string
  ): Promise<any> {
    return await backendApiClient.request(`/documents/${documentId}/comments/${commentId}/update`, {
      method: 'PATCH',
      body: JSON.stringify({
        ...updates,
        projectId,
        organizationId
      })
    })
  }

  /**
   * Delete document comment
   */
  async deleteDocumentComment(
    documentId: string,
    commentId: string,
    projectId?: string,
    organizationId?: string
  ): Promise<void> {
    const params = new URLSearchParams()
    if (projectId) params.append('projectId', projectId)
    if (organizationId) params.append('organizationId', organizationId)

    const queryString = params.toString()
    const url = `/documents/${documentId}/comments/${commentId}/delete${queryString ? `?${queryString}` : ''}`

    await backendApiClient.request(url, { method: 'DELETE' })
  }

  /**
   * Add comment reply
   */
  async addCommentReply(
    documentId: string,
    commentId: string,
    reply: any,
    projectId?: string,
    organizationId?: string
  ): Promise<any> {
    return await backendApiClient.request(`/documents/${documentId}/comments/${commentId}/replies`, {
      method: 'POST',
      body: JSON.stringify({
        ...reply,
        projectId,
        organizationId
      })
    })
  }

  /**
   * Get version content
   */
  async getVersionContent(
    documentId: string,
    versionId: string
  ): Promise<Blob> {
    const response = await backendApiClient.request(`/documents/${documentId}/versions/${versionId}/content`, {
      method: 'GET'
    })

    // Convert response to blob if it's not already
    if (response instanceof Blob) {
      return response
    }

    // Create a blob from the response
    return new Blob([JSON.stringify(response)], { type: 'application/json' })
  }

  /**
   * Restore document version
   */
  async restoreDocumentVersion(
    documentId: string,
    versionId: string,
    projectId?: string,
    organizationId?: string
  ): Promise<any> {
    return await backendApiClient.request(`/documents/${documentId}/versions/${versionId}/restore`, {
      method: 'POST',
      body: JSON.stringify({
        projectId,
        organizationId
      })
    })
  }

  /**
   * Update document content
   */
  async updateDocumentContent(
    documentId: string,
    content: string,
    projectId?: string,
    organizationId?: string
  ): Promise<any> {
    return await backendApiClient.request(`/documents/${documentId}/content`, {
      method: 'PUT',
      body: JSON.stringify({
        content,
        projectId,
        organizationId
      })
    })
  }
}

export const optimizedDocumentService = new OptimizedDocumentService()

// Export as documentService for backward compatibility
export const documentService = optimizedDocumentService

// Default export
export default optimizedDocumentService
