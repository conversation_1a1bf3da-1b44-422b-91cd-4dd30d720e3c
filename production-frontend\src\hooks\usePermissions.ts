import { useMemo } from 'react'
import { useAuth } from './useAuthStore'
import { useTenant } from '@/stores/tenant-store'

/**
 * Permissions Hook
 * Provides permission checking functionality
 */

export interface UsePermissionsResult {
  hasPermission: (permission: string, resource?: any) => boolean
  hasRole: (role: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  hasAllPermissions: (permissions: string[]) => boolean
  canAccess: (resource: string, action: string) => boolean
  isAdmin: boolean
  isSuperAdmin: boolean
  permissions: string[]
  roles: string[]
}

export function usePermissions(): UsePermissionsResult {
  const { user } = useAuth()
  const { currentTenant: _currentTenant } = useTenant()

  const permissions = useMemo(() => {
    if (!user) return []
    
    // Combine user permissions with role permissions
    const userPermissions = user.permissions || []
    // user.roles is string[] in UserContext, not Role[]
    const rolePermissions: string[] = []
    
    return [...new Set([...userPermissions, ...rolePermissions])]
  }, [user])

  const roles = useMemo(() => {
    if (!user?.roles) return []
    // user.roles is already string[] in UserContext
    return user.roles
  }, [user])

  const hasPermission = (permission: string, resource?: any): boolean => {
    if (!user) return false
    
    // Super admin has all permissions
    if (roles.includes('super_admin')) return true
    
    // Check direct permission
    if (permissions.includes(permission)) return true
    
    // Check wildcard permissions
    const wildcardPermission = permission.split(':')[0] + ':*'
    if (permissions.includes(wildcardPermission)) return true
    
    // Resource-specific permission checks
    if (resource) {
      // Check if user owns the resource
      if (resource.createdBy === user.id || resource.ownerId === user.id) {
        return permissions.includes('own:' + permission)
      }
      
      // Check organization-level permissions
      if (resource.organizationId && user.organizationIds?.includes(resource.organizationId)) {
        return permissions.includes('org:' + permission)
      }
    }
    
    return false
  }

  const hasRole = (role: string): boolean => {
    return roles.includes(role)
  }

  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }

  const canAccess = (resource: string, action: string): boolean => {
    const permission = `${resource}:${action}`
    return hasPermission(permission)
  }

  const isAdmin = useMemo(() => {
    return hasRole('admin') || hasRole('super_admin')
  }, [roles])

  const isSuperAdmin = useMemo(() => {
    return hasRole('super_admin')
  }, [roles])

  return {
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    canAccess,
    isAdmin,
    isSuperAdmin,
    permissions,
    roles,
  }
}

/**
 * Resource-specific permission hooks
 */
export const useDocumentPermissions = (document?: any) => {
  const { hasPermission } = usePermissions()
  
  return {
    canView: hasPermission('documents:read', document),
    canEdit: hasPermission('documents:write', document),
    canDelete: hasPermission('documents:delete', document),
    canShare: hasPermission('documents:share', document),
    canComment: hasPermission('documents:comment', document),
  }
}

export const useProjectPermissions = (project?: any) => {
  const { hasPermission } = usePermissions()
  
  return {
    canView: hasPermission('projects:read', project),
    canEdit: hasPermission('projects:write', project),
    canDelete: hasPermission('projects:delete', project),
    canManageMembers: hasPermission('projects:manage_members', project),
    canCreateDocuments: hasPermission('documents:create', project),
  }
}

export const useWorkflowPermissions = (workflow?: any) => {
  const { hasPermission } = usePermissions()
  
  return {
    canView: hasPermission('workflows:read', workflow),
    canEdit: hasPermission('workflows:write', workflow),
    canDelete: hasPermission('workflows:delete', workflow),
    canExecute: hasPermission('workflows:execute', workflow),
    canApprove: hasPermission('workflows:approve', workflow),
  }
}
