/**
 * Enhanced Document Store - Production Ready
 * Integrates with Azure Functions backend for document management
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '../services/backend-api-client'
import type {
  Document,
  DocumentStatus,
  DocumentType,
  AIOperation,
  PaginatedResponse
} from '../types/backend'

export interface DocumentFilters {
  organizationId?: string
  projectId?: string
  status?: DocumentStatus[]
  type?: DocumentType[]
  search?: string
  tags?: string[]
  dateRange?: {
    start: string
    end: string
  }
}

export interface DocumentState {
  // Document data
  documents: Document[]
  selectedDocument: Document | null

  // Pagination
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }

  // Filters and search
  filters: DocumentFilters
  sortBy: string
  sortOrder: 'asc' | 'desc'

  // UI state
  loading: boolean
  error: string | null
  lastUpdated: string | null

  // Upload state
  uploading: boolean
  uploadProgress: Record<string, number>
  uploadErrors: Record<string, string>

  // Processing state
  processing: Record<string, AIOperation>

  // Cache management
  cache: {
    documents: Record<string, Document>
    lastFetch: Record<string, number>
    ttl: number // Time to live in milliseconds
  }

  // Collaboration
  collaborationSession: any | null

  // Internal state
  _hydrated: boolean
}

export interface DocumentActions {
  // Fetch operations
  fetchDocuments: (filters?: DocumentFilters, page?: number, pageSize?: number) => Promise<void>
  fetchDocument: (documentId: string, forceRefresh?: boolean) => Promise<Document>
  refreshDocuments: () => Promise<void>

  // CRUD operations
  uploadDocument: (file: File, metadata?: {
    name?: string
    description?: string
    projectId?: string
    tags?: string[]
    autoProcess?: boolean
  }) => Promise<Document>
  updateDocument: (documentId: string, updates: Partial<Document>) => Promise<void>
  deleteDocument: (documentId: string) => Promise<void>

  // Bulk operations
  bulkUpload: (files: File[], options?: {
    batchSize?: number
    parallelProcessing?: boolean
    autoProcess?: boolean
  }) => Promise<void>
  bulkDelete: (documentIds: string[]) => Promise<void>

  // Processing operations
  processDocument: (documentId: string, options?: {
    analysisType?: string
    extractTables?: boolean
    extractKeyValuePairs?: boolean
    extractEntities?: boolean
    forceReprocess?: boolean
  }) => Promise<void>
  getProcessingStatus: (documentId: string) => AIOperation | null

  // Filter and search
  setFilters: (filters: Partial<DocumentFilters>) => void
  clearFilters: () => void
  setSort: (sortBy: string, sortOrder?: 'asc' | 'desc') => void

  // Selection
  selectDocument: (document: Document | null) => void

  // Cache management
  clearCache: () => void
  invalidateDocument: (documentId: string) => void

  // Collaboration
  setCollaborationContext: (session: any) => void

  // Error handling
  clearError: () => void
}

export type DocumentStore = DocumentState & DocumentActions

const DEFAULT_PAGE_SIZE = 20
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

export const useDocumentStore = create<DocumentStore>()(
  persist(
    (set, get) => ({
      // Initial state
      documents: [],
      selectedDocument: null,
      pagination: {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false,
      },
      filters: {},
      sortBy: 'updatedAt',
      sortOrder: 'desc',
      loading: false,
      error: null,
      lastUpdated: null,
      uploading: false,
      uploadProgress: {},
      uploadErrors: {},
      processing: {},
      cache: {
        documents: {},
        lastFetch: {},
        ttl: CACHE_TTL,
      },
      collaborationSession: null,
      _hydrated: false,

      // Fetch operations
      fetchDocuments: async (filters = {}, page = 1, pageSize = DEFAULT_PAGE_SIZE) => {
        set({ loading: true, error: null })

        try {
          const { sortBy, sortOrder } = get()
          const cacheKey = JSON.stringify({ filters, page, pageSize, sortBy, sortOrder })
          const { cache } = get()

          // Check cache
          const lastFetch = cache.lastFetch[cacheKey]
          if (lastFetch && Date.now() - lastFetch < cache.ttl) {
            set({ loading: false })
            return
          }

          const response = await backendApiClient.getDocuments({
            ...filters,
            page,
            pageSize,
          })

          // Update cache
          const updatedCache = { ...cache }
          response.data.forEach(doc => {
            updatedCache.documents[doc.id] = doc
          })
          updatedCache.lastFetch[cacheKey] = Date.now()

          set({
            documents: response.data,
            pagination: response.pagination,
            filters,
            cache: updatedCache,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to fetch documents',
          })
          throw error
        }
      },

      fetchDocument: async (documentId, forceRefresh = false) => {
        const { cache } = get()

        // Check cache first
        if (!forceRefresh && cache.documents[documentId]) {
          const cachedDoc = cache.documents[documentId]
          set({ selectedDocument: cachedDoc })
          return cachedDoc
        }

        set({ loading: true, error: null })

        try {
          const document = await backendApiClient.getDocument(documentId)

          // Update cache
          const updatedCache = { ...cache }
          updatedCache.documents[documentId] = document

          set({
            selectedDocument: document,
            cache: updatedCache,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })

          return document
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to fetch document',
          })
          throw error
        }
      },

      refreshDocuments: async () => {
        const { filters, pagination } = get()
        await get().fetchDocuments(filters, pagination.page, pagination.pageSize)
      },

      // CRUD operations
      uploadDocument: async (file, metadata = {}) => {
        const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

        set(state => ({
          uploading: true,
          uploadProgress: { ...state.uploadProgress, [uploadId]: 0 },
          uploadErrors: { ...state.uploadErrors },
          error: null,
        }))

        try {
          const document = await backendApiClient.uploadDocument(file, metadata)

          // Update cache and documents list
          set(state => {
            const updatedCache = { ...state.cache }
            updatedCache.documents[document.id] = document

            const { [uploadId]: _, ...remainingProgress } = state.uploadProgress
            const { [uploadId]: __, ...remainingErrors } = state.uploadErrors

            return {
              documents: [document, ...state.documents],
              cache: updatedCache,
              uploading: Object.keys(remainingProgress).length > 0,
              uploadProgress: remainingProgress,
              uploadErrors: remainingErrors,
              lastUpdated: new Date().toISOString(),
            }
          })

          return document
        } catch (error: any) {
          set(state => ({
            uploadErrors: {
              ...state.uploadErrors,
              [uploadId]: error.message || 'Upload failed'
            },
            uploading: false,
          }))
          throw error
        }
      },

      updateDocument: async (documentId, updates) => {
        set({ loading: true, error: null })

        try {
          const updatedDocument = await backendApiClient.updateDocument(documentId, updates)

          set(state => {
            const updatedCache = { ...state.cache }
            updatedCache.documents[documentId] = updatedDocument

            return {
              documents: state.documents.map(doc =>
                doc.id === documentId ? updatedDocument : doc
              ),
              selectedDocument: state.selectedDocument?.id === documentId
                ? updatedDocument
                : state.selectedDocument,
              cache: updatedCache,
              loading: false,
              error: null,
              lastUpdated: new Date().toISOString(),
            }
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to update document',
          })
          throw error
        }
      },

      deleteDocument: async (documentId) => {
        set({ loading: true, error: null })

        try {
          await backendApiClient.deleteDocument(documentId)

          set(state => {
            const updatedCache = { ...state.cache }
            delete updatedCache.documents[documentId]

            return {
              documents: state.documents.filter(doc => doc.id !== documentId),
              selectedDocument: state.selectedDocument?.id === documentId
                ? null
                : state.selectedDocument,
              cache: updatedCache,
              loading: false,
              error: null,
              lastUpdated: new Date().toISOString(),
            }
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to delete document',
          })
          throw error
        }
      },

      // Bulk operations
      bulkUpload: async (files, options = {}) => {
        set({ uploading: true, error: null })

        try {
          const operation = await backendApiClient.bulkUpload(files, options)

          // Poll for operation status
          const pollOperation = async () => {
            try {
              const status = await (backendApiClient as any).getOperationStatus(operation.id);

              if (status.status === 'completed') {
                // Refresh documents when upload is complete
                get().refreshDocuments();
                set({ uploading: false });
              } else if (status.status === 'failed') {
                throw new Error(status.error || 'Bulk upload failed');
              } else {
                // Continue polling if still in progress
                setTimeout(pollOperation, 1000);
              }
            } catch (error) {
              console.error('Error polling operation status:', error);
              // Fallback: refresh documents after delay
              setTimeout(() => {
                get().refreshDocuments();
              }, 2000);
              set({ uploading: false });
            }
          };

          // Start polling
          setTimeout(pollOperation, 1000);

          set({ uploading: false })
        } catch (error: any) {
          set({
            uploading: false,
            error: error.message || 'Bulk upload failed',
          })
          throw error
        }
      },

      bulkDelete: async (documentIds) => {
        set({ loading: true, error: null })

        try {
          // Delete documents one by one (could be optimized with a bulk endpoint)
          await Promise.all(documentIds.map(id => backendApiClient.deleteDocument(id)))

          set(state => {
            const updatedCache = { ...state.cache }
            documentIds.forEach(id => delete updatedCache.documents[id])

            return {
              documents: state.documents.filter(doc => !documentIds.includes(doc.id)),
              selectedDocument: documentIds.includes(state.selectedDocument?.id || '')
                ? null
                : state.selectedDocument,
              cache: updatedCache,
              loading: false,
              error: null,
              lastUpdated: new Date().toISOString(),
            }
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Bulk delete failed',
          })
          throw error
        }
      },

      // Processing operations
      processDocument: async (documentId, options = {}) => {
        try {
          const operation = await backendApiClient.processDocument(documentId, options)

          set(state => ({
            processing: { ...state.processing, [documentId]: operation }
          }))
        } catch (error: any) {
          set({ error: error.message || 'Failed to start document processing' })
          throw error
        }
      },

      getProcessingStatus: (documentId) => {
        const { processing } = get()
        return processing[documentId] || null
      },

      // Filter and search
      setFilters: (newFilters) => {
        set(state => ({
          filters: { ...state.filters, ...newFilters },
          pagination: { ...state.pagination, page: 1 }, // Reset to first page
        }))

        // Auto-fetch with new filters
        const { filters, pagination } = get()
        get().fetchDocuments(filters, 1, pagination.pageSize)
      },

      clearFilters: () => {
        set({
          filters: {},
          pagination: { ...get().pagination, page: 1 },
        })

        get().fetchDocuments({}, 1, get().pagination.pageSize)
      },

      setSort: (sortBy, sortOrder = 'desc') => {
        set({ sortBy, sortOrder })

        // Auto-fetch with new sort
        const { filters, pagination } = get()
        get().fetchDocuments(filters, pagination.page, pagination.pageSize)
      },

      // Selection
      selectDocument: (document) => {
        set({ selectedDocument: document })
      },

      // Cache management
      clearCache: () => {
        set(state => ({
          cache: {
            ...state.cache,
            documents: {},
            lastFetch: {},
          }
        }))
      },

      invalidateDocument: (documentId) => {
        set(state => {
          const updatedCache = { ...state.cache }
          delete updatedCache.documents[documentId]
          return { cache: updatedCache }
        })
      },

      // Collaboration
      setCollaborationContext: (session: any) => {
        set({ collaborationSession: session })
      },

      // Error handling
      clearError: () => {
        set({ error: null })
      },

    }),
    {
      name: 'document-store-v2',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        filters: state.filters,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
        cache: {
          ...state.cache,
          // Only persist documents for a short time
          documents: Object.fromEntries(
            Object.entries(state.cache.documents).filter(
              ([_, doc]) => Date.now() - new Date(doc.updatedAt).getTime() < 60000 // 1 minute
            )
          ),
        },
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true

          // Clear temporary state
          state.uploading = false
          state.uploadProgress = {}
          state.uploadErrors = {}
          state.processing = {}
          state.loading = false
          state.error = null
        }
      },
    }
  )
)

// ============================================================================
// SELECTOR HOOKS
// ============================================================================

// Core selectors
export const useDocuments = () => useDocumentStore((state) => state.documents)
export const useSelectedDocument = () => useDocumentStore((state) => state.selectedDocument)
export const useDocumentLoading = () => useDocumentStore((state) => state.loading)
export const useDocumentError = () => useDocumentStore((state) => state.error)
export const useDocumentPagination = () => useDocumentStore((state) => state.pagination)
export const useDocumentFilters = () => useDocumentStore((state) => state.filters)

// Upload and processing selectors
export const useUploadProgress = () => useDocumentStore((state) => state.uploadProgress)
export const useUploadErrors = () => useDocumentStore((state) => state.uploadErrors)
export const useUploading = () => useDocumentStore((state) => state.uploading)
export const useProcessingStatus = () => useDocumentStore((state) => state.processing)

// Action hooks
export const useFetchDocuments = () => useDocumentStore((state) => state.fetchDocuments)
export const useFetchDocument = () => useDocumentStore((state) => state.fetchDocument)
export const useRefreshDocuments = () => useDocumentStore((state) => state.refreshDocuments)
export const useUploadDocument = () => useDocumentStore((state) => state.uploadDocument)
export const useUpdateDocument = () => useDocumentStore((state) => state.updateDocument)
export const useDeleteDocument = () => useDocumentStore((state) => state.deleteDocument)
export const useBulkUpload = () => useDocumentStore((state) => state.bulkUpload)
export const useBulkDelete = () => useDocumentStore((state) => state.bulkDelete)
export const useProcessDocument = () => useDocumentStore((state) => state.processDocument)
export const useSelectDocument = () => useDocumentStore((state) => state.selectDocument)

// Filter and search hooks
export const useSetFilters = () => useDocumentStore((state) => state.setFilters)
export const useClearFilters = () => useDocumentStore((state) => state.clearFilters)
export const useSetSort = () => useDocumentStore((state) => state.setSort)

// Cache management hooks
export const useClearCache = () => useDocumentStore((state) => state.clearCache)
export const useInvalidateDocument = () => useDocumentStore((state) => state.invalidateDocument)

// Computed hooks
export const useDocumentsByProject = (projectId: string) => {
  const documents = useDocuments()
  return documents.filter(doc => doc.projectId === projectId)
}

export const useRecentDocuments = (limit: number = 10) => {
  const documents = useDocuments()
  return documents
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, limit)
}

export const useDocumentStats = () => {
  const documents = useDocuments()

  return {
    total: documents.length,
    byStatus: documents.reduce((acc, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    byType: documents.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    totalSize: documents.reduce((acc, doc) => acc + doc.size, 0),
  }
}

// Document search hook
export const useDocumentSearch = () => {
  const { filters, sortBy, sortOrder } = useDocumentStore()
  const setFilters = useSetFilters()
  const setSort = useSetSort()
  const clearFilters = useClearFilters()

  return {
    filters,
    sortBy,
    sortOrder,
    setFilters,
    setSort,
    clearFilters,
    search: (query: string) => setFilters({ search: query }),
    filterByStatus: (status: DocumentStatus[]) => setFilters({ status }),
    filterByType: (type: DocumentType[]) => setFilters({ type }),
    filterByProject: (projectId: string) => setFilters({ projectId }),
  }
}
