/**
 * Tenants Admin Hook
 * Manages tenant operations for admin users
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID, Tenant, TenantSettings, TenantBranding } from '@/types'

export interface TenantUsage {
  users: number
  organizations: number
  documents: number
  storage: number
  apiCalls: number
  bandwidth: number
}

export interface TenantLimits {
  maxUsers: number
  maxOrganizations: number
  maxDocuments: number
  maxStorage: number
  maxApiCalls: number
  maxBandwidth: number
}

export interface TenantPlan {
  id: ID
  name: string
  features: string[]
  limits: TenantLimits
  price: number
  billing: 'monthly' | 'yearly'
}

export interface CreateTenantData {
  name: string
  displayName: string
  domain?: string
  planId: ID
  adminEmail: string
  adminName: string
  settings?: Partial<TenantSettings>
  branding?: Partial<TenantBranding>
}

export interface UseTenantResult {
  // State
  tenants: Tenant[]
  loading: boolean
  error: string | null
  
  // Single tenant operations
  createTenant: (data: CreateTenantData) => Promise<Tenant>
  updateTenant: (tenantId: ID, updates: Partial<Tenant>) => Promise<void>
  deleteTenant: (tenantId: ID) => Promise<void>
  suspendTenant: (tenantId: ID, reason: string) => Promise<void>
  activateTenant: (tenantId: ID) => Promise<void>
  
  // Tenant settings
  getTenantSettings: (tenantId: ID) => Promise<TenantSettings>
  updateTenantSettings: (tenantId: ID, settings: Partial<TenantSettings>) => Promise<void>
  
  // Tenant branding
  getTenantBranding: (tenantId: ID) => Promise<TenantBranding>
  updateTenantBranding: (tenantId: ID, branding: Partial<TenantBranding>) => Promise<void>
  
  // Tenant usage and limits
  getTenantUsage: (tenantId: ID) => Promise<TenantUsage>
  getTenantLimits: (tenantId: ID) => Promise<TenantLimits>
  updateTenantLimits: (tenantId: ID, limits: Partial<TenantLimits>) => Promise<void>
  
  // Tenant plans
  getAvailablePlans: () => Promise<TenantPlan[]>
  changeTenantPlan: (tenantId: ID, planId: ID) => Promise<void>
  
  // Bulk operations
  bulkSuspend: (tenantIds: ID[], reason: string) => Promise<{ success: ID[]; failed: ID[] }>
  bulkActivate: (tenantIds: ID[]) => Promise<{ success: ID[]; failed: ID[] }>
  bulkDelete: (tenantIds: ID[]) => Promise<{ success: ID[]; failed: ID[] }>
  
  // Refresh
  refresh: () => Promise<void>
}

export function useTenants(): UseTenantResult {
  const { toast } = useToast()
  
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load tenants
  const loadTenants = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request<Tenant[]>('/management/tenants')
      setTenants(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load tenants'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Create tenant
  const createTenant = useCallback(async (data: CreateTenantData): Promise<Tenant> => {
    try {
      const response = await backendApiClient.request<Tenant>('/tenants', {
        method: 'POST',
        body: JSON.stringify(data)
      })
      await loadTenants()

      toast({
        title: 'Tenant created',
        description: `Tenant "${data.displayName}" has been created successfully.`,
      })

      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create tenant'
      
      toast({
        type: 'error',
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadTenants, toast])

  // Update tenant
  const updateTenant = useCallback(async (tenantId: ID, updates: Partial<Tenant>) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/update`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      await loadTenants()

      toast({
        title: 'Tenant updated',
        description: 'Tenant has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update tenant'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTenants, toast])

  // Delete tenant
  const deleteTenant = useCallback(async (tenantId: ID) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/delete`, {
        method: 'DELETE'
      })
      await loadTenants()

      toast({
        title: 'Tenant deleted',
        description: 'Tenant has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete tenant'

      toast({
        title: 'Deletion failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTenants, toast])

  // Suspend tenant
  const suspendTenant = useCallback(async (tenantId: ID, reason: string) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/suspend`, {
        method: 'POST',
        body: JSON.stringify({ reason })
      })
      await loadTenants()

      toast({
        title: 'Tenant suspended',
        description: 'Tenant has been suspended successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to suspend tenant'

      toast({
        title: 'Suspension failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTenants, toast])

  // Activate tenant
  const activateTenant = useCallback(async (tenantId: ID) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/activate`, {
        method: 'POST'
      })
      await loadTenants()

      toast({
        title: 'Tenant activated',
        description: 'Tenant has been activated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to activate tenant'

      toast({
        title: 'Activation failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTenants, toast])

  // Tenant settings
  const getTenantSettings = useCallback(async (tenantId: ID): Promise<TenantSettings> => {
    return await backendApiClient.request<TenantSettings>(`/management/tenants/${tenantId}/settings`)
  }, [])

  const updateTenantSettings = useCallback(async (tenantId: ID, settings: Partial<TenantSettings>) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/settings`, {
        method: 'PUT',
        body: JSON.stringify(settings)
      })

      toast({
        title: 'Settings updated',
        description: 'Tenant settings have been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update settings'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [toast])

  // Tenant branding
  const getTenantBranding = useCallback(async (tenantId: ID): Promise<TenantBranding> => {
    return await backendApiClient.request<TenantBranding>(`/management/tenants/${tenantId}/branding`)
  }, [])

  const updateTenantBranding = useCallback(async (tenantId: ID, branding: Partial<TenantBranding>) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/branding`, {
        method: 'PUT',
        body: JSON.stringify(branding)
      })

      toast({
        title: 'Branding updated',
        description: 'Tenant branding has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update branding'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [toast])

  // Usage and limits
  const getTenantUsage = useCallback(async (tenantId: ID): Promise<TenantUsage> => {
    return await backendApiClient.request<TenantUsage>(`/management/tenants/${tenantId}/usage`)
  }, [])

  const getTenantLimits = useCallback(async (tenantId: ID): Promise<TenantLimits> => {
    return await backendApiClient.request<TenantLimits>(`/management/tenants/${tenantId}/limits`)
  }, [])

  const updateTenantLimits = useCallback(async (tenantId: ID, limits: Partial<TenantLimits>) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/limits`, {
        method: 'PUT',
        body: JSON.stringify(limits)
      })

      toast({
        title: 'Limits updated',
        description: 'Tenant limits have been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update limits'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [toast])

  // Plans
  const getAvailablePlans = useCallback(async (): Promise<TenantPlan[]> => {
    return await backendApiClient.request<TenantPlan[]>('/management/plans')
  }, [])

  const changeTenantPlan = useCallback(async (tenantId: ID, planId: ID) => {
    try {
      await backendApiClient.request(`/management/tenants/${tenantId}/plan`, {
        method: 'PUT',
        body: JSON.stringify({ planId })
      })
      await loadTenants()

      toast({
        title: 'Plan changed',
        description: 'Tenant plan has been changed successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to change plan'

      toast({
        title: 'Plan change failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTenants, toast])

  // Bulk operations
  const bulkSuspend = useCallback(async (tenantIds: ID[], reason: string): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const response = await backendApiClient.request<{ success: ID[]; failed: ID[] }>('/management/tenants/bulk/suspend', {
        method: 'POST',
        body: JSON.stringify({
          tenantIds,
          reason
        })
      })

      await loadTenants()

      toast({
        title: 'Bulk suspension completed',
        description: `${response.success.length} tenants suspended successfully.`,
      })

      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to suspend tenants'

      toast({
        title: 'Bulk suspension failed',
        description: errorMessage,
        variant: 'destructive',
      })

      throw err
    }
  }, [loadTenants, toast])

  const bulkActivate = useCallback(async (tenantIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const response = await backendApiClient.request<{ success: ID[]; failed: ID[] }>('/management/tenants/bulk/activate', {
        method: 'POST',
        body: JSON.stringify({
          tenantIds
        })
      })

      await loadTenants()

      toast({
        title: 'Bulk activation completed',
        description: `${response.success.length} tenants activated successfully.`,
      })

      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to activate tenants'

      toast({
        title: 'Bulk activation failed',
        description: errorMessage,
        variant: 'destructive',
      })

      throw err
    }
  }, [loadTenants, toast])

  const bulkDelete = useCallback(async (tenantIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const response = await backendApiClient.request<{ success: ID[]; failed: ID[] }>('/management/tenants/bulk/delete', {
        method: 'POST',
        body: JSON.stringify({
          tenantIds
        })
      })

      await loadTenants()

      toast({
        title: 'Bulk deletion completed',
        description: `${response.success.length} tenants deleted successfully.`,
      })

      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete tenants'

      toast({
        title: 'Bulk deletion failed',
        description: errorMessage,
        variant: 'destructive',
      })

      throw err
    }
  }, [loadTenants, toast])

  // Load tenants on mount
  useEffect(() => {
    loadTenants()
  }, [loadTenants])

  return {
    // State
    tenants,
    loading,
    error,
    
    // Single tenant operations
    createTenant,
    updateTenant,
    deleteTenant,
    suspendTenant,
    activateTenant,
    
    // Tenant settings
    getTenantSettings,
    updateTenantSettings,
    
    // Tenant branding
    getTenantBranding,
    updateTenantBranding,
    
    // Usage and limits
    getTenantUsage,
    getTenantLimits,
    updateTenantLimits,
    
    // Plans
    getAvailablePlans,
    changeTenantPlan,
    
    // Bulk operations
    bulkSuspend,
    bulkActivate,
    bulkDelete,
    
    // Refresh
    refresh: loadTenants,
  }
}

// Additional hooks for specific operations
export function useDeleteTenant() {
  const { deleteTenant } = useTenants()
  return deleteTenant
}

export function useUpdateTenant() {
  const { updateTenant } = useTenants()
  return updateTenant
}

export function useTenant(tenantId: ID) {
  const [tenant, setTenant] = useState<Tenant | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const loadTenant = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request<Tenant>(`/management/tenants/${tenantId}`)
      setTenant(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load tenant'
      setError(errorMessage)

      toast({
        title: 'Loading failed',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }, [tenantId, toast])

  const refetch = useCallback(() => {
    return loadTenant()
  }, [loadTenant])

  useEffect(() => {
    if (tenantId) {
      loadTenant()
    }
  }, [tenantId, loadTenant])

  return {
    data: tenant,
    isLoading,
    error,
    refetch
  }
}
