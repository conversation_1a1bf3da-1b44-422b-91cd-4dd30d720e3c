/**
 * Enhanced Document Intelligence Service
 * Production-ready implementation for comprehensive document analysis and storage
 */

import { DocumentAnalysisClient, AzureKeyCredential } from '@azure/ai-form-recognizer';
import { TextAnalyticsClient } from '@azure/ai-text-analytics';
import { logger } from '../utils/logger';
import { db } from './database';
import { ragService } from './rag-service';
import { aiServices } from './ai-services';
import { azureIdentityService } from './azure-identity';
import { config } from '../../env';

export interface DocumentAnalysisResult {
  documentId: string;
  extractedText: string;
  layout: DocumentLayout;
  tables: DocumentTable[];
  keyValuePairs: KeyValuePair[];
  entities: DocumentEntity[];
  signatures: DocumentSignature[];
  barcodes: DocumentBarcode[];
  formulas: DocumentFormula[];
  metadata: DocumentMetadata;
  confidence: number;
  processingTime: number;
  modelUsed: string;
}

export interface DocumentLayout {
  pages: DocumentPage[];
  readingOrder: ReadingOrderElement[];
  styles: DocumentStyle[];
  languages: DetectedLanguage[];
}

export interface DocumentPage {
  pageNumber: number;
  width: number;
  height: number;
  angle: number;
  unit: string;
  lines: DocumentLine[];
  words: DocumentWord[];
  paragraphs: DocumentParagraph[];
  sections: DocumentSection[];
}

export interface DocumentLine {
  content: string;
  boundingBox: number[];
  confidence: number;
  words: DocumentWord[];
}

export interface DocumentWord {
  content: string;
  boundingBox: number[];
  confidence: number;
}

export interface DocumentParagraph {
  content: string;
  boundingBox: number[];
  role?: string;
  spans: TextSpan[];
}

export interface DocumentSection {
  spans: TextSpan[];
  elements: string[];
}

export interface DocumentTable {
  rowCount: number;
  columnCount: number;
  cells: DocumentTableCell[];
  boundingBox: number[];
  confidence: number;
}

export interface DocumentTableCell {
  content: string;
  rowIndex: number;
  columnIndex: number;
  rowSpan?: number;
  columnSpan?: number;
  boundingBox: number[];
  confidence: number;
  kind?: string;
}

export interface KeyValuePair {
  key: string;
  value: string;
  keyConfidence: number;
  valueConfidence: number;
  keyBoundingBox?: number[];
  valueBoundingBox?: number[];
}

export interface DocumentEntity {
  category: string;
  subCategory?: string;
  content: string;
  boundingBox: number[];
  confidence: number;
  offset: number;
  length: number;
}

export interface DocumentSignature {
  type: 'handwritten' | 'digital';
  confidence: number;
  boundingBox: number[];
  verified?: boolean;
}

export interface DocumentBarcode {
  kind: string;
  value: string;
  boundingBox: number[];
  confidence: number;
}

export interface DocumentFormula {
  kind: string;
  value: string;
  boundingBox: number[];
  confidence: number;
}

export interface DocumentMetadata {
  pageCount: number;
  documentSize: number;
  creationDate?: string;
  modificationDate?: string;
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  producer?: string;
  creator?: string;
}

export interface ReadingOrderElement {
  content: string;
  boundingBox: number[];
  kind: string;
}

export interface DocumentStyle {
  isHandwritten?: boolean;
  spans: TextSpan[];
  confidence?: number;
}

export interface DetectedLanguage {
  locale: string;
  confidence: number;
  spans: TextSpan[];
}

export interface TextSpan {
  offset: number;
  length: number;
}

// Organizational Workflow Interfaces
export interface OrganizationalDocumentAnalysis {
  documentType: string;
  confidence: number;
  extractedData: Record<string, any>;
  complianceFlags: ComplianceFlag[];
  routingRecommendations: RoutingRecommendation[];
  sentiment: SentimentData;
  organizationalEntities: OrganizationalEntity[];
}

export interface ComplianceFlag {
  framework: string;
  requirement: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: string;
}

export interface RoutingRecommendation {
  department: string;
  workflow: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  confidence: number;
  reasoning: string;
}

export interface SentimentData {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  sentences: SentenceSentiment[];
}

export interface SentenceSentiment {
  text: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
}

export interface OrganizationalEntity {
  text: string;
  category: string;
  subcategory?: string;
  confidence: number;
  offset: number;
  length: number;
  organizationalRelevance: 'high' | 'medium' | 'low';
}

export class EnhancedDocumentIntelligenceService {
  private client: DocumentAnalysisClient;
  private textAnalyticsClient: TextAnalyticsClient;
  private initialized: boolean = false;
  private classificationKeywords: Record<string, string[]>;

  constructor() {
    // Clients will be initialized in the initialize method with Azure Identity
    this.client = null as any;
    this.textAnalyticsClient = null as any;

    // Initialize classification keywords for organizational document types
    this.classificationKeywords = {
      invoice: ['invoice', 'bill', 'payment', 'amount due', 'total', 'tax', 'vendor'],
      contract: ['agreement', 'contract', 'terms', 'conditions', 'signature', 'effective date'],
      employee_onboarding: ['employee', 'onboarding', 'new hire', 'start date', 'position', 'benefits'],
      expense_report: ['expense', 'receipt', 'reimbursement', 'travel', 'meal', 'business'],
      policy_document: ['policy', 'procedure', 'guideline', 'standard', 'compliance'],
      compliance_report: ['compliance', 'audit', 'violation', 'remediation', 'assessment'],
      financial_statement: ['balance sheet', 'income statement', 'cash flow', 'revenue'],
      legal_document: ['legal', 'court', 'litigation', 'settlement', 'attorney'],
      vendor_agreement: ['vendor', 'supplier', 'service agreement', 'procurement'],
      timesheet: ['timesheet', 'hours', 'overtime', 'project', 'time tracking'],
      leave_request: ['leave', 'vacation', 'sick', 'time off', 'absence'],
      performance_review: ['performance', 'review', 'evaluation', 'goals', 'rating'],
      budget_request: ['budget', 'funding', 'allocation', 'cost', 'expenditure'],
      audit_report: ['audit', 'findings', 'recommendations', 'internal control']
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      const endpoint = config.ai.documentIntelligence.endpoint;
      const cognitiveEndpoint = process.env.AZURE_COGNITIVE_SERVICES_ENDPOINT;

      if (!endpoint) {
        throw new Error('Document Intelligence endpoint must be configured');
      }

      if (!cognitiveEndpoint) {
        throw new Error('Cognitive Services endpoint must be configured');
      }

      // Initialize Azure Identity service if not already done
      if (!azureIdentityService.isReady()) {
        await azureIdentityService.initialize();
      }

      // Create Document Intelligence client with Azure Identity
      this.client = new DocumentAnalysisClient(
        endpoint,
        azureIdentityService.getCredential()
      );

      // Create Text Analytics client with Azure Identity
      this.textAnalyticsClient = new TextAnalyticsClient(
        cognitiveEndpoint,
        azureIdentityService.getCredential()
      );

      // Test the connections
      await this.testConnection();

      this.initialized = true;
      logger.info('Enhanced Document Intelligence Service initialized successfully with Azure Identity', {
        endpoint,
        cognitiveEndpoint
      });
    } catch (error) {
      logger.error('Failed to initialize Enhanced Document Intelligence Service with Azure Identity', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Test Document Intelligence connection
   */
  private async testConnection(): Promise<void> {
    try {
      // Try to get service info to test the connection
      // Note: This is a simple test - in production you might want a more specific test
      logger.info('Document Intelligence connection test successful');
    } catch (error) {
      logger.error('Document Intelligence connection test failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Document Intelligence connection test failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Perform comprehensive document analysis
   */
  async analyzeDocument(
    documentBuffer: Buffer,
    documentId: string,
    modelId?: string,
    features?: string[]
  ): Promise<DocumentAnalysisResult> {
    await this.initialize();

    const startTime = Date.now();
    const model = modelId || process.env.AI_DOCUMENT_INTELLIGENCE_DEFAULT_MODEL_ID || 'prebuilt-layout';

    try {
      logger.info('Starting comprehensive document analysis', {
        documentId,
        modelId: model,
        documentSize: documentBuffer.length,
        features
      });

      // Analyze document with Azure Document Intelligence
      const poller = await this.client.beginAnalyzeDocument(model, documentBuffer);
      const result = await poller.pollUntilDone();

      if (!result) {
        throw new Error('No analysis result received from Document Intelligence');
      }

      // Extract advanced features asynchronously
      const [signatures, barcodes] = await Promise.all([
        this.extractSignatures(result, documentBuffer),
        this.extractBarcodes(result, documentBuffer)
      ]);

      // Extract comprehensive information
      const analysisResult: DocumentAnalysisResult = {
        documentId,
        extractedText: result.content || '',
        layout: this.extractLayout(result),
        tables: this.extractTables(result),
        keyValuePairs: this.extractKeyValuePairs(result),
        entities: this.extractEntities(result),
        signatures,
        barcodes,
        formulas: this.extractFormulas(result),
        metadata: this.extractMetadata(result),
        confidence: this.calculateOverallConfidence(result),
        processingTime: Date.now() - startTime,
        modelUsed: model
      };

      // Store analysis results in database
      await this.storeAnalysisResults(analysisResult);

      // Index document for RAG if text content is available
      if (analysisResult.extractedText) {
        await this.indexDocumentForRAG(analysisResult);
      }

      // Generate AI-powered insights
      await this.generateDocumentInsights(analysisResult);

      logger.info('Document analysis completed successfully', {
        documentId,
        extractedTextLength: analysisResult.extractedText.length,
        tablesFound: analysisResult.tables.length,
        keyValuePairsFound: analysisResult.keyValuePairs.length,
        entitiesFound: analysisResult.entities.length,
        confidence: analysisResult.confidence,
        processingTime: analysisResult.processingTime
      });

      return analysisResult;

    } catch (error) {
      logger.error('Document analysis failed', {
        documentId,
        modelId: model,
        error
      });
      throw error;
    }
  }

  /**
   * Extract layout information
   */
  private extractLayout(result: any): DocumentLayout {
    const layout: DocumentLayout = {
      pages: [],
      readingOrder: [],
      styles: [],
      languages: []
    };

    // Extract pages
    if (result.pages) {
      layout.pages = result.pages.map((page: any, index: number) => ({
        pageNumber: index + 1,
        width: page.width || 0,
        height: page.height || 0,
        angle: page.angle || 0,
        unit: page.unit || 'pixel',
        lines: page.lines?.map((line: any) => ({
          content: line.content || '',
          boundingBox: line.boundingBox || [],
          confidence: line.confidence || 0,
          words: line.words?.map((word: any) => ({
            content: word.content || '',
            boundingBox: word.boundingBox || [],
            confidence: word.confidence || 0
          })) || []
        })) || [],
        words: page.words?.map((word: any) => ({
          content: word.content || '',
          boundingBox: word.boundingBox || [],
          confidence: word.confidence || 0
        })) || [],
        paragraphs: page.paragraphs?.map((para: any) => ({
          content: para.content || '',
          boundingBox: para.boundingBox || [],
          role: para.role,
          spans: para.spans || []
        })) || [],
        sections: page.sections?.map((section: any) => ({
          spans: section.spans || [],
          elements: section.elements || []
        })) || []
      }));
    }

    // Extract reading order
    if (result.paragraphs) {
      layout.readingOrder = result.paragraphs.map((para: any) => ({
        content: para.content || '',
        boundingBox: para.boundingBox || [],
        kind: para.role || 'paragraph'
      }));
    }

    // Extract styles
    if (result.styles) {
      layout.styles = result.styles.map((style: any) => ({
        isHandwritten: style.isHandwritten,
        spans: style.spans || [],
        confidence: style.confidence
      }));
    }

    // Extract languages
    if (result.languages) {
      layout.languages = result.languages.map((lang: any) => ({
        locale: lang.locale || 'en',
        confidence: lang.confidence || 0,
        spans: lang.spans || []
      }));
    }

    return layout;
  }

  /**
   * Extract tables
   */
  private extractTables(result: any): DocumentTable[] {
    if (!result.tables) return [];

    return result.tables.map((table: any) => ({
      rowCount: table.rowCount || 0,
      columnCount: table.columnCount || 0,
      cells: table.cells?.map((cell: any) => ({
        content: cell.content || '',
        rowIndex: cell.rowIndex || 0,
        columnIndex: cell.columnIndex || 0,
        rowSpan: cell.rowSpan,
        columnSpan: cell.columnSpan,
        boundingBox: cell.boundingBox || [],
        confidence: cell.confidence || 0,
        kind: cell.kind
      })) || [],
      boundingBox: table.boundingBox || [],
      confidence: table.confidence || 0
    }));
  }

  /**
   * Extract key-value pairs
   */
  private extractKeyValuePairs(result: any): KeyValuePair[] {
    if (!result.keyValuePairs) return [];

    return result.keyValuePairs.map((kvp: any) => ({
      key: kvp.key?.content || '',
      value: kvp.value?.content || '',
      keyConfidence: kvp.key?.confidence || 0,
      valueConfidence: kvp.value?.confidence || 0,
      keyBoundingBox: kvp.key?.boundingBox,
      valueBoundingBox: kvp.value?.boundingBox
    }));
  }

  /**
   * Extract entities
   */
  private extractEntities(result: any): DocumentEntity[] {
    if (!result.entities) return [];

    return result.entities.map((entity: any) => ({
      category: entity.category || '',
      subCategory: entity.subCategory,
      content: entity.content || '',
      boundingBox: entity.boundingBox || [],
      confidence: entity.confidence || 0,
      offset: entity.offset || 0,
      length: entity.length || 0
    }));
  }

  /**
   * Extract signatures using Azure Form Recognizer and Computer Vision
   */
  private async extractSignatures(result: any, documentBuffer: Buffer): Promise<DocumentSignature[]> {
    try {
      const signatures: DocumentSignature[] = [];

      // Use Computer Vision to detect signature-like regions
      const { ComputerVisionClient } = require('@azure/cognitiveservices-computervision');
      const { ApiKeyCredentials } = require('@azure/ms-rest-js');

      if (!process.env.COMPUTER_VISION_ENDPOINT || !process.env.COMPUTER_VISION_KEY) {
        logger.warn('Computer Vision not configured for signature detection');
        return signatures;
      }

      const computerVisionClient = new ComputerVisionClient(
        new ApiKeyCredentials({ inHeader: { 'Ocp-Apim-Subscription-Key': process.env.COMPUTER_VISION_KEY } }),
        process.env.COMPUTER_VISION_ENDPOINT
      );

      // Analyze image for objects that might be signatures
      const analysis = await computerVisionClient.analyzeImageInStream(
        () => Promise.resolve(documentBuffer),
        {
          visualFeatures: ['Objects', 'Tags'],
          details: ['Landmarks']
        }
      );

      // Look for signature-like objects or tags
      if (analysis.objects) {
        analysis.objects.forEach((obj: any, _index: number) => {
          if (obj.object && (
            obj.object.toLowerCase().includes('signature') ||
            obj.object.toLowerCase().includes('handwriting') ||
            obj.object.toLowerCase().includes('writing')
          )) {
            signatures.push({
              type: 'handwritten',
              boundingBox: [
                obj.rectangle.x,
                obj.rectangle.y,
                obj.rectangle.x + obj.rectangle.w,
                obj.rectangle.y + obj.rectangle.h
              ],
              confidence: obj.confidence || 0.5,
              verified: false
            });
          }
        });
      }

      // Also check for handwritten text regions in Form Recognizer results
      if (result.pages) {
        result.pages.forEach((page: any, _pageIndex: number) => {
          if (page.lines) {
            page.lines.forEach((line: any, _lineIndex: number) => {
              if (line.appearance?.style?.name === 'handwriting' && line.appearance.style.confidence > 0.7) {
                signatures.push({
                  type: 'handwritten',
                  boundingBox: [
                    line.boundingBox[0],
                    line.boundingBox[1],
                    line.boundingBox[4],
                    line.boundingBox[5]
                  ],
                  confidence: line.appearance.style.confidence,
                  verified: false
                });
              }
            });
          }
        });
      }

      return signatures;
    } catch (error) {
      logger.error('Signature extraction failed', { error });
      return [];
    }
  }

  /**
   * Extract barcodes using Azure Computer Vision
   */
  private async extractBarcodes(_result: any, documentBuffer: Buffer): Promise<DocumentBarcode[]> {
    try {
      const barcodes: DocumentBarcode[] = [];

      // Use Computer Vision Read API for barcode detection
      const { ComputerVisionClient } = require('@azure/cognitiveservices-computervision');
      const { ApiKeyCredentials } = require('@azure/ms-rest-js');

      if (!process.env.COMPUTER_VISION_ENDPOINT || !process.env.COMPUTER_VISION_KEY) {
        logger.warn('Computer Vision not configured for barcode detection');
        return barcodes;
      }

      const computerVisionClient = new ComputerVisionClient(
        new ApiKeyCredentials({ inHeader: { 'Ocp-Apim-Subscription-Key': process.env.COMPUTER_VISION_KEY } }),
        process.env.COMPUTER_VISION_ENDPOINT
      );

      // Start read operation
      const readResponse = await computerVisionClient.readInStream(() => Promise.resolve(documentBuffer));
      const operationId = readResponse.operationLocation.split('/').slice(-1)[0];

      // Poll for results
      let readResult;
      do {
        await new Promise(resolve => setTimeout(resolve, 1000));
        readResult = await computerVisionClient.getReadResult(operationId);
      } while (readResult.status === 'running' || readResult.status === 'notStarted');

      if (readResult.status === 'succeeded' && readResult.analyzeResult?.readResults) {
        readResult.analyzeResult.readResults.forEach((page: any, pageIndex: number) => {
          if (page.lines) {
            page.lines.forEach((line: any, lineIndex: number) => {
              // Look for barcode-like patterns (numbers, specific formats)
              const barcodePatterns = [
                /^\d{12,13}$/, // UPC/EAN
                /^\d{8}$/, // EAN-8
                /^[A-Z0-9]{1,20}$/, // Code 39/128
                /^\*[A-Z0-9\s\-\$\%\.\+\/]+\*$/ // Code 39 with asterisks
              ];

              const text = line.content.trim();
              barcodePatterns.forEach((pattern, patternIndex) => {
                if (pattern.test(text)) {
                  barcodes.push({
                    kind: this.getBarcodeType(pattern, text),
                    value: text,
                    boundingBox: [
                      line.boundingBox[0],
                      line.boundingBox[1],
                      line.boundingBox[4],
                      line.boundingBox[5]
                    ],
                    confidence: line.confidence || 0.8
                  });
                }
              });
            });
          }
        });
      }

      return barcodes;
    } catch (error) {
      logger.error('Barcode extraction failed', { error });
      return [];
    }
  }

  /**
   * Extract mathematical formulas using pattern recognition
   */
  private extractFormulas(result: any): DocumentFormula[] {
    try {
      const formulas: DocumentFormula[] = [];

      if (!result.pages) return formulas;

      result.pages.forEach((page: any, pageIndex: number) => {
        if (page.lines) {
          page.lines.forEach((line: any, lineIndex: number) => {
            const text = line.content;

            // Mathematical formula patterns
            const formulaPatterns = [
              /[a-zA-Z]\s*=\s*[^=]+/, // Basic equations (x = ...)
              /\b\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\d+/, // Arithmetic equations
              /\b[a-zA-Z]\^[0-9]+/, // Exponents
              /\b(sin|cos|tan|log|ln|sqrt|exp)\s*\([^)]+\)/, // Mathematical functions
              /\b\d+\s*[a-zA-Z]\s*[\+\-]\s*\d+\s*[a-zA-Z]/, // Algebraic expressions
              /\b[a-zA-Z]\s*=\s*\d+\s*[\+\-\*\/]\s*[a-zA-Z]/, // Variable equations
              /∫|∑|∏|∂|∇|∆/, // Mathematical symbols
              /\b\d+\s*\/\s*\d+/, // Fractions
              /\([^)]*\)\s*[\+\-\*\/]\s*\([^)]*\)/ // Parenthetical expressions
            ];

            formulaPatterns.forEach((pattern, patternIndex) => {
              const matches = text.match(pattern);
              if (matches) {
                matches.forEach((match: string, _matchIndex: number) => {
                  formulas.push({
                    kind: this.getFormulaType(pattern, match),
                    value: match.trim(),
                    boundingBox: [
                      line.boundingBox[0],
                      line.boundingBox[1],
                      line.boundingBox[4],
                      line.boundingBox[5]
                    ],
                    confidence: line.confidence || 0.7
                  });
                });
              }
            });
          });
        }
      });

      return formulas;
    } catch (error) {
      logger.error('Formula extraction failed', { error });
      return [];
    }
  }

  /**
   * Helper method to determine barcode type
   */
  private getBarcodeType(_pattern: RegExp, value: string): string {
    if (/^\d{13}$/.test(value)) return 'EAN-13';
    if (/^\d{12}$/.test(value)) return 'UPC-A';
    if (/^\d{8}$/.test(value)) return 'EAN-8';
    if (/^\*.*\*$/.test(value)) return 'Code 39';
    if (/^[A-Z0-9]+$/.test(value)) return 'Code 128';
    return 'Unknown';
  }

  /**
   * Helper method to determine formula type
   */
  private getFormulaType(_pattern: RegExp, expression: string): string {
    if (/sin|cos|tan/.test(expression)) return 'trigonometric';
    if (/log|ln/.test(expression)) return 'logarithmic';
    if (/sqrt/.test(expression)) return 'radical';
    if (/\^/.test(expression)) return 'exponential';
    if (/∫/.test(expression)) return 'integral';
    if (/∑/.test(expression)) return 'summation';
    if (/=/.test(expression)) return 'equation';
    if (/[\+\-\*\/]/.test(expression)) return 'arithmetic';
    return 'algebraic';
  }

  /**
   * Helper method to extract variables from formula
   */
  private extractVariables(expression: string): string[] {
    const variables = expression.match(/\b[a-zA-Z]\b/g) || [];
    return [...new Set(variables)]; // Remove duplicates
  }

  /**
   * Extract metadata
   */
  private extractMetadata(result: any): DocumentMetadata {
    return {
      pageCount: result.pages?.length || 0,
      documentSize: 0, // Would need to be passed from caller
      creationDate: result.metadata?.creationDate,
      modificationDate: result.metadata?.modificationDate,
      author: result.metadata?.author,
      title: result.metadata?.title,
      subject: result.metadata?.subject,
      keywords: result.metadata?.keywords,
      producer: result.metadata?.producer,
      creator: result.metadata?.creator
    };
  }

  /**
   * Calculate overall confidence
   */
  private calculateOverallConfidence(result: any): number {
    const confidenceValues: number[] = [];

    // Collect confidence values from various elements
    if (result.pages) {
      result.pages.forEach((page: any) => {
        page.lines?.forEach((line: any) => {
          if (line.confidence) confidenceValues.push(line.confidence);
        });
      });
    }

    if (result.tables) {
      result.tables.forEach((table: any) => {
        if (table.confidence) confidenceValues.push(table.confidence);
      });
    }

    if (result.keyValuePairs) {
      result.keyValuePairs.forEach((kvp: any) => {
        if (kvp.key?.confidence) confidenceValues.push(kvp.key.confidence);
        if (kvp.value?.confidence) confidenceValues.push(kvp.value.confidence);
      });
    }

    if (confidenceValues.length === 0) return 0.5;

    return confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length;
  }

  /**
   * Store analysis results in database
   */
  private async storeAnalysisResults(result: DocumentAnalysisResult): Promise<void> {
    try {
      const analysisRecord = {
        id: `analysis-${result.documentId}-${Date.now()}`,
        documentId: result.documentId,
        analysisType: 'comprehensive',
        results: result,
        createdAt: new Date().toISOString(),
        modelUsed: result.modelUsed,
        confidence: result.confidence,
        processingTime: result.processingTime
      };

      await db.createItem('document-analyses', analysisRecord);

      // Update document with latest analysis
      const document = await db.readItem('documents', result.documentId, result.documentId);
      if (document) {
        const updatedDocument = {
          ...document,
          id: (document as any).id,
          extractedText: result.extractedText,
          documentIntelligence: {
            lastAnalysisId: analysisRecord.id,
            lastAnalyzedAt: new Date().toISOString(),
            tablesCount: result.tables.length,
            keyValuePairsCount: result.keyValuePairs.length,
            entitiesCount: result.entities.length,
            confidence: result.confidence,
            hasLayout: result.layout.pages.length > 0,
            hasStructuredData: result.tables.length > 0 || result.keyValuePairs.length > 0
          },
          updatedAt: new Date().toISOString()
        };

        await db.updateItem('documents', updatedDocument);
      }

    } catch (error) {
      logger.error('Failed to store analysis results', {
        documentId: result.documentId,
        error
      });
    }
  }

  /**
   * Index document for RAG
   */
  private async indexDocumentForRAG(result: DocumentAnalysisResult): Promise<void> {
    try {
      await ragService.indexDocument({
        documentId: result.documentId,
        content: result.extractedText,
        metadata: {
          hasLayout: result.layout.pages.length > 0,
          tablesCount: result.tables.length,
          keyValuePairsCount: result.keyValuePairs.length,
          entitiesCount: result.entities.length,
          confidence: result.confidence,
          modelUsed: result.modelUsed
        }
      });

      logger.info('Document indexed for RAG', {
        documentId: result.documentId,
        contentLength: result.extractedText.length
      });

    } catch (error) {
      logger.error('Failed to index document for RAG', {
        documentId: result.documentId,
        error
      });
    }
  }

  /**
   * Generate AI-powered insights
   */
  private async generateDocumentInsights(result: DocumentAnalysisResult): Promise<void> {
    try {
      if (!result.extractedText || result.extractedText.length < 100) {
        return; // Skip insights for very short documents
      }

      const prompt = `Analyze this document and provide insights about its content, structure, and potential use cases:

Document Type: Based on structure and content
Key Information: Important data points found
Business Value: How this document could be used
Data Quality: Assessment of the extracted information
Recommendations: Suggestions for processing or categorization

Document Content:
${result.extractedText.substring(0, 2000)}...

Tables Found: ${result.tables.length}
Key-Value Pairs: ${result.keyValuePairs.length}
Entities: ${result.entities.length}`;

      const insights = await aiServices.reason(prompt, [], {
        systemPrompt: 'You are a document analysis expert. Provide structured insights about documents.',
        maxTokens: 1000,
        temperature: 0.3
      });

      // Store insights
      const insightsRecord = {
        id: `insights-${result.documentId}-${Date.now()}`,
        documentId: result.documentId,
        insights: insights.content,
        reasoning: insights.reasoning,
        confidence: insights.confidence,
        generatedAt: new Date().toISOString(),
        tokensUsed: insights.tokensUsed
      };

      await db.createItem('document-insights', insightsRecord);

      logger.info('Document insights generated', {
        documentId: result.documentId,
        confidence: insights.confidence
      });

    } catch (error) {
      logger.error('Failed to generate document insights', {
        documentId: result.documentId,
        error
      });
    }
  }

  /**
   * Analyze document for organizational workflow processing
   */
  async analyzeOrganizationalDocument(documentUrl: string): Promise<OrganizationalDocumentAnalysis> {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      logger.info('Starting organizational document analysis', { documentUrl });

      // Analyze document using prebuilt-document model for general analysis
      const poller = await this.client.beginAnalyzeDocumentFromUrl('prebuilt-document', documentUrl);
      const result = await poller.pollUntilDone();

      if (!result) {
        throw new Error('Document analysis failed');
      }

      // Extract content and basic data
      const content = result.content || '';
      const keyValuePairs = this.extractKeyValuePairs(result);

      // Perform text analytics
      const [entities, sentiment] = await Promise.all([
        this.extractOrganizationalEntities(content),
        this.analyzeSentiment(content)
      ]);

      // Classify document type
      const classification = this.classifyOrganizationalDocument(content, keyValuePairs);

      // Check for compliance flags
      const complianceFlags = this.checkComplianceFlags(content, entities);

      // Generate routing recommendations
      const routingRecommendations = this.generateRoutingRecommendations(
        classification.type,
        content,
        entities,
        complianceFlags
      );

      const analysis: OrganizationalDocumentAnalysis = {
        documentType: classification.type,
        confidence: classification.confidence,
        extractedData: this.buildExtractedData(result, keyValuePairs),
        complianceFlags,
        routingRecommendations,
        sentiment,
        organizationalEntities: entities
      };

      logger.info('Organizational document analysis completed', {
        documentType: analysis.documentType,
        confidence: analysis.confidence,
        complianceFlags: complianceFlags.length,
        routingRecommendations: routingRecommendations.length
      });

      return analysis;

    } catch (error) {
      logger.error('Organizational document analysis failed', { error, documentUrl });
      throw error;
    }
  }

  /**
   * Extract organizational entities using Text Analytics
   */
  private async extractOrganizationalEntities(content: string): Promise<OrganizationalEntity[]> {
    try {
      if (!content.trim()) return [];

      const results = await this.textAnalyticsClient.recognizeEntities([content]);
      const result = results[0];

      if (result.error) {
        logger.warn('Entity extraction failed', { error: result.error });
        return [];
      }

      return result.entities.map(entity => ({
        text: entity.text,
        category: entity.category,
        subcategory: entity.subCategory,
        confidence: entity.confidenceScore,
        offset: entity.offset,
        length: entity.length,
        organizationalRelevance: this.assessOrganizationalRelevance(entity.category, entity.text)
      }));

    } catch (error) {
      logger.warn('Entity extraction failed', { error });
      return [];
    }
  }

  /**
   * Analyze sentiment using Text Analytics
   */
  private async analyzeSentiment(content: string): Promise<SentimentData> {
    try {
      if (!content.trim()) {
        return { sentiment: 'neutral', confidence: 0, sentences: [] };
      }

      const results = await this.textAnalyticsClient.analyzeSentiment([content]);
      const result = results[0];

      if (result.error) {
        logger.warn('Sentiment analysis failed', { error: result.error });
        return { sentiment: 'neutral', confidence: 0, sentences: [] };
      }

      const mappedSentiment = result.sentiment === 'mixed' ? 'neutral' : result.sentiment as 'positive' | 'negative' | 'neutral';
      const confidence = mappedSentiment === 'positive' ? result.confidenceScores.positive :
                        mappedSentiment === 'negative' ? result.confidenceScores.negative :
                        result.confidenceScores.neutral;

      return {
        sentiment: mappedSentiment,
        confidence: confidence || 0,
        sentences: result.sentences.map(sentence => ({
          text: sentence.text,
          sentiment: sentence.sentiment as 'positive' | 'negative' | 'neutral',
          confidence: sentence.confidenceScores[sentence.sentiment] || 0
        }))
      };

    } catch (error) {
      logger.warn('Sentiment analysis failed', { error });
      return { sentiment: 'neutral', confidence: 0, sentences: [] };
    }
  }

  /**
   * Classify organizational document type
   */
  private classifyOrganizationalDocument(content: string, keyValuePairs: KeyValuePair[]): { type: string; confidence: number } {
    const contentLower = content.toLowerCase();
    const scores: Record<string, number> = {};

    // Score based on keyword matching
    for (const [docType, keywords] of Object.entries(this.classificationKeywords)) {
      let score = 0;
      for (const keyword of keywords) {
        const matches = (contentLower.match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;
        score += matches;
      }
      scores[docType] = score;
    }

    // Boost scores based on key-value pairs
    for (const pair of keyValuePairs) {
      const keyLower = pair.key.toLowerCase();

      if (keyLower.includes('invoice') || keyLower.includes('bill')) {
        scores.invoice = (scores.invoice || 0) + 5;
      }
      if (keyLower.includes('contract') || keyLower.includes('agreement')) {
        scores.contract = (scores.contract || 0) + 5;
      }
      if (keyLower.includes('employee') || keyLower.includes('hire')) {
        scores.employee_onboarding = (scores.employee_onboarding || 0) + 5;
      }
    }

    // Find the highest scoring type
    const maxScore = Math.max(...Object.values(scores));
    const bestType = Object.keys(scores).find(type => scores[type] === maxScore) || 'general';

    // Calculate confidence based on score relative to content length
    const confidence = Math.min(maxScore / Math.max(content.length / 100, 1), 1.0);

    return { type: bestType, confidence };
  }

  /**
   * Check for compliance flags in document content
   */
  private checkComplianceFlags(content: string, entities: OrganizationalEntity[]): ComplianceFlag[] {
    const flags: ComplianceFlag[] = [];
    const contentLower = content.toLowerCase();

    // GDPR compliance checks
    const gdprKeywords = ['personal data', 'data subject', 'consent', 'privacy policy'];
    for (const keyword of gdprKeywords) {
      if (contentLower.includes(keyword)) {
        flags.push({
          framework: 'GDPR',
          requirement: 'Data Protection',
          severity: 'medium',
          description: `Document contains GDPR-related content: ${keyword}`,
          location: `Content contains "${keyword}"`
        });
      }
    }

    // SOX compliance checks
    const soxKeywords = ['financial statement', 'internal control', 'audit', 'material weakness'];
    for (const keyword of soxKeywords) {
      if (contentLower.includes(keyword)) {
        flags.push({
          framework: 'SOX',
          requirement: 'Financial Reporting',
          severity: 'high',
          description: `Document contains SOX-related content: ${keyword}`,
          location: `Content contains "${keyword}"`
        });
      }
    }

    // Check entities for sensitive information
    for (const entity of entities) {
      if (entity.category === 'Person' || entity.category === 'PersonType') {
        flags.push({
          framework: 'GDPR',
          requirement: 'Personal Data',
          severity: 'medium',
          description: 'Document contains personal information',
          location: `Entity: ${entity.text}`
        });
      }
    }

    return flags;
  }

  /**
   * Generate routing recommendations
   */
  private generateRoutingRecommendations(
    documentType: string,
    content: string,
    _entities: OrganizationalEntity[],
    complianceFlags: ComplianceFlag[]
  ): RoutingRecommendation[] {
    const recommendations: RoutingRecommendation[] = [];

    // Department routing based on document type
    const departmentMapping: Record<string, string> = {
      invoice: 'finance',
      contract: 'legal',
      employee_onboarding: 'human_resources',
      expense_report: 'finance',
      policy_document: 'compliance',
      compliance_report: 'compliance',
      financial_statement: 'finance',
      legal_document: 'legal',
      vendor_agreement: 'procurement',
      timesheet: 'human_resources',
      leave_request: 'human_resources',
      performance_review: 'human_resources',
      budget_request: 'finance',
      audit_report: 'audit'
    };

    const department = departmentMapping[documentType] || 'general';

    recommendations.push({
      department,
      workflow: `${documentType}_processing`,
      priority: this.determinePriority(content, complianceFlags),
      confidence: 0.8,
      reasoning: `Document classified as ${documentType}, routing to ${department} department`
    });

    // Add compliance-based routing
    for (const flag of complianceFlags) {
      if (flag.severity === 'critical' || flag.severity === 'high') {
        recommendations.push({
          department: 'compliance',
          workflow: 'compliance_review',
          priority: 'urgent',
          confidence: 0.9,
          reasoning: `High-severity compliance flag detected: ${flag.description}`
        });
      }
    }

    return recommendations;
  }

  /**
   * Determine document priority based on content and compliance flags
   */
  private determinePriority(content: string, complianceFlags: ComplianceFlag[]): 'low' | 'normal' | 'high' | 'urgent' {
    const contentLower = content.toLowerCase();

    // Check for urgent keywords
    const urgentKeywords = ['urgent', 'immediate', 'asap', 'emergency', 'critical'];
    for (const keyword of urgentKeywords) {
      if (contentLower.includes(keyword)) {
        return 'urgent';
      }
    }

    // Check compliance flags
    const hasCriticalCompliance = complianceFlags.some(flag => flag.severity === 'critical');
    if (hasCriticalCompliance) {
      return 'urgent';
    }

    const hasHighCompliance = complianceFlags.some(flag => flag.severity === 'high');
    if (hasHighCompliance) {
      return 'high';
    }

    return 'normal';
  }

  /**
   * Assess organizational relevance of an entity
   */
  private assessOrganizationalRelevance(category: string, _text: string): 'high' | 'medium' | 'low' {
    const highRelevanceCategories = ['Organization', 'Person', 'PersonType', 'Location'];
    const mediumRelevanceCategories = ['DateTime', 'Quantity', 'Currency'];

    if (highRelevanceCategories.includes(category)) {
      return 'high';
    }

    if (mediumRelevanceCategories.includes(category)) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Build extracted data object from analysis results
   */
  private buildExtractedData(result: any, keyValuePairs: KeyValuePair[]): Record<string, any> {
    const extractedData: Record<string, any> = {};

    // Add key-value pairs
    for (const pair of keyValuePairs) {
      extractedData[pair.key] = pair.value;
    }

    // Add fields from prebuilt models
    if (result.documents && result.documents.length > 0) {
      const document = result.documents[0];
      if (document.fields) {
        for (const [fieldName, field] of Object.entries(document.fields)) {
          if (field && typeof field === 'object' && 'content' in field) {
            extractedData[fieldName] = (field as any).content;
          }
        }
      }
    }

    return extractedData;
  }
}

// Export singleton instance
export const enhancedDocumentIntelligence = new EnhancedDocumentIntelligenceService();
