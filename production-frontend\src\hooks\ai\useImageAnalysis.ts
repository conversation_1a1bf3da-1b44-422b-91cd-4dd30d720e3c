/**
 * Image Analysis Hooks
 * React hooks for AI-powered image analysis using Azure Computer Vision and Document Intelligence
 */

import { useCallback } from 'react'
import { useAIStore, useStartAIOperation, useAILoading, useAIError } from '@/stores/ai-store'
import { useToast } from '@/hooks/use-toast'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import backendApiClient from '@/services/backend-api-client'
export interface ImageAnalysisRequest {
  documentId?: string
  imageFile?: File
  imageUrl?: string
  analysisTypes: ImageAnalysisType[]
  options?: {
    extractText?: boolean
    detectObjects?: boolean
    analyzeFaces?: boolean
    extractSignatures?: boolean
    detectBarcodes?: boolean
    analyzeLayout?: boolean
    extractTables?: boolean
    detectHandwriting?: boolean
    classifyImage?: boolean
    generateDescription?: boolean
    extractMetadata?: boolean
  }
  organizationId: string
  projectId?: string
}

export type ImageAnalysisType = 
  | 'OCR' 
  | 'OBJECT_DETECTION' 
  | 'FACE_ANALYSIS' 
  | 'SIGNATURE_DETECTION'
  | 'BARCODE_DETECTION'
  | 'LAYOUT_ANALYSIS'
  | 'TABLE_EXTRACTION'
  | 'HANDWRITING_RECOGNITION'
  | 'IMAGE_CLASSIFICATION'
  | 'DESCRIPTION_GENERATION'
  | 'COMPREHENSIVE'

export interface ImageAnalysisResult {
  id: string
  documentId?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  analysisTypes: ImageAnalysisType[]
  results: {
    extractedText?: string
    objects?: Array<{
      name: string
      confidence: number
      boundingBox: {
        x: number
        y: number
        width: number
        height: number
      }
      tags?: string[]
    }>
    faces?: Array<{
      age?: number
      gender?: string
      emotion?: string
      confidence: number
      boundingBox: {
        x: number
        y: number
        width: number
        height: number
      }
    }>
    signatures?: Array<{
      confidence: number
      boundingBox: {
        x: number
        y: number
        width: number
        height: number
      }
      type: 'signature' | 'initial'
    }>
    barcodes?: Array<{
      type: string
      value: string
      confidence: number
      boundingBox: {
        x: number
        y: number
        width: number
        height: number
      }
    }>
    layout?: {
      pages: Array<{
        pageNumber: number
        width: number
        height: number
        angle: number
        lines: Array<{
          text: string
          boundingBox: number[]
          words: Array<{
            text: string
            confidence: number
            boundingBox: number[]
          }>
        }>
      }>
    }
    tables?: Array<{
      rowCount: number
      columnCount: number
      cells: Array<{
        text: string
        rowIndex: number
        columnIndex: number
        confidence: number
        boundingBox: number[]
      }>
    }>
    classification?: {
      category: string
      confidence: number
      subcategories: Array<{
        name: string
        confidence: number
      }>
    }
    description?: {
      text: string
      confidence: number
      tags: string[]
    }
    metadata?: {
      width: number
      height: number
      format: string
      colorSpace: string
      hasAlpha: boolean
      fileSize: number
    }
  }
  confidence: number
  processingTime: number
  modelUsed: string
  organizationId: string
  createdAt: string
  completedAt?: string
  error?: string
}

export interface OCRRequest {
  documentId?: string
  imageFile?: File
  imageUrl?: string
  language?: string
  detectOrientation?: boolean
  organizationId: string
}

export interface ObjectDetectionRequest {
  documentId?: string
  imageFile?: File
  imageUrl?: string
  minConfidence?: number
  maxObjects?: number
  organizationId: string
}

/**
 * Hook to analyze images comprehensively
 */
export function useAnalyzeImage() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ImageAnalysisRequest) => {
      const formData = new FormData()
      
      if (data.imageFile) {
        formData.append('file', data.imageFile)
      }
      
      formData.append('analysisTypes', JSON.stringify(data.analysisTypes))
      formData.append('options', JSON.stringify(data.options || {}))
      formData.append('organizationId', data.organizationId)
      
      if (data.projectId) {
        formData.append('projectId', data.projectId)
      }
      if (data.documentId) {
        formData.append('documentId', data.documentId)
      }
      if (data.imageUrl) {
        formData.append('imageUrl', data.imageUrl)
      }

      return await backendApiClient.request<ImageAnalysisResult>('/ai/images/analyze', {
        method: 'POST',
        body: formData as any
      })
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['image-analysis'] })
      if (result.documentId) {
        queryClient.invalidateQueries({ queryKey: ['document', result.documentId] })
      }
      toast({
        title: 'Image analysis started',
        description: 'Image analysis has been started. You will be notified when it\'s complete.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error analyzing image',
        description: 'There was a problem analyzing the image. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to perform OCR on images
 */
export function useImageOCR() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: OCRRequest) => {
      const formData = new FormData()
      
      if (data.imageFile) {
        formData.append('file', data.imageFile)
      }
      
      formData.append('analysisTypes', JSON.stringify(['OCR']))
      formData.append('options', JSON.stringify({
        extractText: true,
        detectHandwriting: true,
        language: data.language || 'en'
      }))
      formData.append('organizationId', data.organizationId)
      
      if (data.documentId) {
        formData.append('documentId', data.documentId)
      }
      if (data.imageUrl) {
        formData.append('imageUrl', data.imageUrl)
      }

      return await backendApiClient.request<ImageAnalysisResult>('/ai/images/analyze', {
        method: 'POST',
        body: formData as any
      })
    },
    onSuccess: (result) => {
      toast({
        title: 'OCR completed',
        description: 'Text extraction from image has been completed successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error in OCR',
        description: 'There was a problem extracting text from the image. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to detect objects in images
 */
export function useObjectDetection() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ObjectDetectionRequest) => {
      const formData = new FormData()
      
      if (data.imageFile) {
        formData.append('file', data.imageFile)
      }
      
      formData.append('analysisTypes', JSON.stringify(['OBJECT_DETECTION']))
      formData.append('options', JSON.stringify({
        detectObjects: true,
        minConfidence: data.minConfidence || 0.5,
        maxObjects: data.maxObjects || 50
      }))
      formData.append('organizationId', data.organizationId)
      
      if (data.documentId) {
        formData.append('documentId', data.documentId)
      }
      if (data.imageUrl) {
        formData.append('imageUrl', data.imageUrl)
      }

      return await backendApiClient.request<ImageAnalysisResult>('/ai/images/analyze', {
        method: 'POST',
        body: formData as any
      })
    },
    onSuccess: (result) => {
      toast({
        title: 'Object detection completed',
        description: 'Objects have been detected in the image successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error in object detection',
        description: 'There was a problem detecting objects in the image. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get image analysis results
 */
export function useImageAnalysisResults(params?: {
  documentId?: string
  organizationId?: string
  projectId?: string
  analysisType?: ImageAnalysisType
  status?: string
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['image-analysis', params],
    queryFn: async () => {
      return await backendApiClient.request<ImageAnalysisResult[]>('/ai/images/analysis/results', {
        params
      })
    },
  })
}

/**
 * Hook to get a specific image analysis result
 */
export function useImageAnalysisResult(analysisId: string) {
  return useQuery({
    queryKey: ['image-analysis-result', analysisId],
    queryFn: async () => {
      return await backendApiClient.request<ImageAnalysisResult>(`/ai/images/analysis/${analysisId}`)
    },
    enabled: !!analysisId,
    refetchInterval: 3000, // Refetch every 3 seconds
  })
}

/**
 * Hook to extract signatures from documents
 */
export function useSignatureDetection() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      documentId, 
      imageFile, 
      organizationId 
    }: { 
      documentId?: string
      imageFile?: File
      organizationId: string 
    }) => {
      const formData = new FormData()
      
      if (imageFile) {
        formData.append('file', imageFile)
      }
      
      formData.append('analysisTypes', JSON.stringify(['SIGNATURE_DETECTION']))
      formData.append('options', JSON.stringify({
        extractSignatures: true,
        detectHandwriting: true
      }))
      formData.append('organizationId', organizationId)
      
      if (documentId) {
        formData.append('documentId', documentId)
      }

      return await backendApiClient.request<ImageAnalysisResult>('/ai/images/analyze', {
        method: 'POST',
        body: formData as any
      })
    },
    onSuccess: (result) => {
      toast({
        title: 'Signature detection completed',
        description: 'Signature detection has been completed successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error in signature detection',
        description: 'There was a problem detecting signatures. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to detect and decode barcodes
 */
export function useBarcodeDetection() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      documentId, 
      imageFile, 
      organizationId 
    }: { 
      documentId?: string
      imageFile?: File
      organizationId: string 
    }) => {
      const formData = new FormData()
      
      if (imageFile) {
        formData.append('file', imageFile)
      }
      
      formData.append('analysisTypes', JSON.stringify(['BARCODE_DETECTION']))
      formData.append('options', JSON.stringify({
        detectBarcodes: true
      }))
      formData.append('organizationId', organizationId)
      
      if (documentId) {
        formData.append('documentId', documentId)
      }

      return await backendApiClient.request<ImageAnalysisResult>('/ai/images/analyze', {
        method: 'POST',
        body: formData as any
      })
    },
    onSuccess: (result) => {
      toast({
        title: 'Barcode detection completed',
        description: 'Barcode detection has been completed successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error in barcode detection',
        description: 'There was a problem detecting barcodes. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
