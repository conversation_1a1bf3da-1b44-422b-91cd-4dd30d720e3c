/**
 * Infrastructure Hooks
 * React hooks for monitoring system health, performance, and infrastructure
 */

import { useState, useEffect, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { backendApiClient } from '@/services/backend-api-client'
import { useInfrastructureServices } from '@/hooks/useLazyServices'
import { useToast } from '@/components/ui/use-toast'
// Event types placeholder
interface EventData {
  id: string
  type: string
  data: any
  timestamp: string
}

// Event types placeholder - removed unused interface

// ============================================================================
// SYSTEM HEALTH HOOKS
// ============================================================================

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  services: {
    redis: ServiceStatus
    database: ServiceStatus
    serviceBus: ServiceStatus
    storage: ServiceStatus
  }
  performance: PerformanceOverview
  circuitBreakers: any
  cacheStats: any
  rateLimitStats: any
  timestamp: string
}

export interface ServiceStatus {
  status: 'up' | 'down' | 'degraded'
  responseTime: number
  lastCheck: string
  errorRate?: number
  details?: any
}

export interface PerformanceOverview {
  averageResponseTime: number
  requestsPerMinute: number
  errorRate: number
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  topSlowFunctions: Array<{
    name: string
    averageTime: number
    callCount: number
  }>
}

/**
 * Hook for monitoring system health
 */
export function useSystemHealth(refreshInterval = 30000) {
  return useQuery({
    queryKey: ['system', 'health'],
    queryFn: async () => {
      return await backendApiClient.getSystemHealth()
    },
    refetchInterval: refreshInterval,
    staleTime: 10000, // Consider data stale after 10 seconds
  })
}

/**
 * Hook for monitoring performance metrics
 */
export function usePerformanceMetrics(refreshInterval = 60000) {
  return useQuery({
    queryKey: ['system', 'performance'],
    queryFn: async () => {
      return await backendApiClient.getPerformanceMetrics()
    },
    refetchInterval: refreshInterval,
    staleTime: 30000,
  })
}

/**
 * Hook for monitoring system alerts
 */
export function useSystemAlerts() {
  return useQuery({
    queryKey: ['system', 'alerts'],
    queryFn: async () => {
      return await backendApiClient.getSystemAlerts()
    },
    refetchInterval: 15000, // Check alerts frequently
    staleTime: 5000,
  })
}

// ============================================================================
// EVENT GRID MONITORING HOOKS
// ============================================================================

/**
 * Hook for monitoring Event Grid health - Lazy loaded
 */
export function useEventGridHealth(refreshInterval = 30000, enabled = false) {
  const { eventGrid } = useInfrastructureServices(enabled)

  return useQuery({
    queryKey: ['eventgrid', 'health'],
    queryFn: async () => {
      if (!eventGrid) throw new Error('Event Grid service not available')
      return await eventGrid.getHealthStatus()
    },
    refetchInterval: refreshInterval,
    staleTime: 10000,
    enabled: !!eventGrid,
  })
}

/**
 * Hook for Event Grid metrics - Lazy loaded
 */
export function useEventGridMetrics(timeRange = '1h', enabled = false) {
  const { eventGrid } = useInfrastructureServices(enabled)

  return useQuery({
    queryKey: ['eventgrid', 'metrics', timeRange],
    queryFn: async () => {
      if (!eventGrid) throw new Error('Event Grid service not available')
      return await eventGrid.getEventMetrics(timeRange)
    },
    refetchInterval: 60000,
    staleTime: 30000,
    enabled: !!eventGrid,
  })
}

/**
 * Hook for Event Grid metrics summary - Lazy loaded
 */
export function useEventGridSummary(timeRange = '24h', enabled = false) {
  const { eventGrid } = useInfrastructureServices(enabled)

  return useQuery({
    queryKey: ['eventgrid', 'summary', timeRange],
    queryFn: async () => {
      if (!eventGrid) throw new Error('Event Grid service not available')
      return await eventGrid.getMetricsSummary(timeRange)
    },
    refetchInterval: 300000, // 5 minutes
    staleTime: 120000, // 2 minutes
    enabled: !!eventGrid,
  })
}

// ============================================================================
// INFRASTRUCTURE OPERATIONS HOOKS
// ============================================================================

/**
 * Hook for cache operations
 */
export function useCacheOperations() {
  const queryClient = useQueryClient()

  const clearCache = useMutation({
    mutationFn: async (cacheKey?: string) => {
      return await backendApiClient.clearCache(cacheKey)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system'] })
    },
  })

  const getCacheStats = useQuery({
    queryKey: ['system', 'cache', 'stats'],
    queryFn: async () => {
      return await backendApiClient.getCacheStats()
    },
    refetchInterval: 60000,
  })

  return {
    clearCache,
    getCacheStats,
  }
}

/**
 * Hook for rate limit monitoring
 */
export function useRateLimitMonitoring() {
  return useQuery({
    queryKey: ['system', 'ratelimit', 'stats'],
    queryFn: async () => {
      return await backendApiClient.getRateLimitStats()
    },
    refetchInterval: 30000,
    staleTime: 15000,
  })
}

/**
 * Hook for circuit breaker monitoring
 */
export function useCircuitBreakerStatus() {
  return useQuery({
    queryKey: ['system', 'circuitbreakers'],
    queryFn: async () => {
      return await backendApiClient.getCircuitBreakerStatus()
    },
    refetchInterval: 30000,
    staleTime: 15000,
  })
}

// ============================================================================
// INFRASTRUCTURE CONFIGURATION HOOKS
// ============================================================================

/**
 * Hook for managing infrastructure configuration
 */
export function useInfrastructureConfig() {
  const queryClient = useQueryClient()

  const getConfig = useQuery({
    queryKey: ['infrastructure', 'config'],
    queryFn: async () => {
      return await backendApiClient.getInfrastructureConfig()
    },
    staleTime: 300000, // 5 minutes
  })

  const updateConfig = useMutation({
    mutationFn: async (config: Record<string, any>) => {
      return await backendApiClient.updateInfrastructureConfig(config)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['infrastructure', 'config'] })
      queryClient.invalidateQueries({ queryKey: ['system'] })
    },
  })

  return {
    config: getConfig.data,
    isLoading: getConfig.isLoading,
    error: getConfig.error,
    updateConfig,
  }
}

// ============================================================================
// REAL-TIME MONITORING HOOKS
// ============================================================================

/**
 * Hook for real-time infrastructure monitoring
 */
export function useRealTimeInfrastructureMonitoring() {
  const [isConnected, setIsConnected] = useState(false)
  const [metrics, _setMetrics] = useState<any>(null)
  const [alerts, _setAlerts] = useState<any[]>([])

  useEffect(() => {
    // Set up SignalR connection for real-time monitoring
    const setupRealTimeConnection = async () => {
      try {
        // This would connect to SignalR hub for real-time updates
        // Implementation depends on SignalR setup
        setIsConnected(true)
      } catch (error) {
        console.error('Failed to connect to real-time monitoring:', error)
        setIsConnected(false)
      }
    }

    setupRealTimeConnection()

    return () => {
      // Cleanup connection
      setIsConnected(false)
    }
  }, [])

  const subscribeToMetrics = useCallback((_callback: (metrics: any) => void) => {
    // Subscribe to real-time metrics updates
    // Implementation would use SignalR or WebSocket
  }, [])

  const subscribeToAlerts = useCallback((_callback: (alert: any) => void) => {
    // Subscribe to real-time alert updates
    // Implementation would use SignalR or WebSocket
  }, [])

  return {
    isConnected,
    metrics,
    alerts,
    subscribeToMetrics,
    subscribeToAlerts,
  }
}

// ============================================================================
// BACKUP & RECOVERY HOOKS
// ============================================================================

/**
 * Hook for backup operations
 */
export function useBackupOperations() {
  const queryClient = useQueryClient()

  const createBackup = useMutation({
    mutationFn: async (backupConfig: {
      type: 'full' | 'incremental'
      includeFiles?: boolean
      includeDatabase?: boolean
      description?: string
    }) => {
      return await backendApiClient.createBackup(backupConfig)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] })
    },
  })

  const getBackups = useQuery({
    queryKey: ['backups'],
    queryFn: async () => {
      return await backendApiClient.getBackups()
    },
    staleTime: 60000,
  })

  const getBackupStatus = useCallback(async (backupId: string) => {
    return await backendApiClient.getBackupStatus(backupId)
  }, [])

  return {
    createBackup,
    backups: getBackups.data,
    isLoading: getBackups.isLoading,
    getBackupStatus,
  }
}

/**
 * Hook for health check operations
 */
export function useHealthCheckOperations() {
  const runHealthCheck = useMutation({
    mutationFn: async (checkType?: string) => {
      return await backendApiClient.runHealthCheck(checkType)
    },
  })

  const getHealthCheckHistory = useQuery({
    queryKey: ['healthchecks', 'history'],
    queryFn: async () => {
      return await backendApiClient.getHealthCheckHistory()
    },
    staleTime: 60000,
  })

  return {
    runHealthCheck,
    history: getHealthCheckHistory.data,
    isLoading: getHealthCheckHistory.isLoading,
  }
}

// ============================================================================
// MISSING HOOKS FOR COMPONENTS
// ============================================================================

/**
 * Hook for SignalR connection - DEPRECATED
 * Use CollaborationProvider for collaborative features instead
 */
export function useSignalRConnection() {
  console.warn('useSignalRConnection is deprecated. Use CollaborationProvider for collaborative features.')
  return {
    isConnected: false,
    connectionState: null,
    connectionId: null
  }
}

/**
 * Hook for event subscription - Lazy loaded
 */
export function useEventSubscription(eventTypes: any[] | any, callback: (event: EventData) => void, enabled = false) {
  const { toast } = useToast()
  const { eventGrid } = useInfrastructureServices(enabled)

  useEffect(() => {
    if (!eventGrid) return

    const types = Array.isArray(eventTypes) ? eventTypes : [eventTypes]

    const unsubscribe = eventGrid.subscribe(types, (event: EventData) => {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in event callback:', error)
        toast({
          title: 'Event Processing Error',
          description: 'An error occurred while processing an event.',
          variant: 'destructive',
        })
      }
    })

    return () => {
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [eventTypes, callback, toast, eventGrid])
}

/**
 * Hook for Service Bus operations - Lazy loaded
 */
export function useServiceBus(enabled = false) {
  const { toast } = useToast()
  const { serviceBus } = useInfrastructureServices(enabled)

  const sendMessage = useCallback(async (message: any, queueName?: string) => {
    if (!serviceBus) {
      throw new Error('Service Bus not available')
    }

    try {
      return await serviceBus.sendMessage(message, queueName)
    } catch (error) {
      console.error('Failed to send message:', error)
      toast({
        title: 'Message Send Failed',
        description: 'Failed to send message to service bus.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast, serviceBus])

  const subscribeToMessages = useCallback((messageType: string, callback: (message: any) => void) => {
    if (!serviceBus) {
      console.warn('Service Bus not available for message subscription')
      return () => {} // Return empty unsubscribe function
    }
    return serviceBus.subscribeToMessages(messageType, callback)
  }, [serviceBus])

  return {
    sendMessage,
    subscribeToMessages,
    isAvailable: !!serviceBus
  }
}

/**
 * Hook for notifications
 */
export function useNotifications() {
  const [notifications, setNotifications] = useState<any[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await backendApiClient.markNotificationAsRead(notificationId)
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }, [])

  const markAllAsRead = useCallback(async () => {
    try {
      await backendApiClient.markAllNotificationsAsRead()
      setNotifications(prev => prev.map(n => ({ ...n, read: true })))
      setUnreadCount(0)
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }, [])

  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      await backendApiClient.deleteNotification(notificationId)
      setNotifications(prev => prev.filter(n => n.id !== notificationId))
    } catch (error) {
      console.error('Failed to delete notification:', error)
    }
  }, [])

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
  }
}

/**
 * Hook for document processing
 */
export function useProcessDocument() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, options }: {
      documentId: string
      options?: {
        analysisType?: string
        extractTables?: boolean
        extractKeyValuePairs?: boolean
        extractEntities?: boolean
        forceReprocess?: boolean
      }
    }) => {
      return await backendApiClient.processDocument(documentId, options)
    },
    onSuccess: () => {
      toast({
        title: 'Document Processing Started',
        description: 'Document processing has been initiated.',
      })
    },
    onError: (error) => {
      console.error('Document processing failed:', error)
      toast({
        title: 'Processing Failed',
        description: 'Failed to start document processing.',
        variant: 'destructive',
      })
    },
  })
}
