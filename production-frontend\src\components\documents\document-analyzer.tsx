"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Spinner } from '@/components/ui/spinner';
import {
  Brain,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { Document } from '@/types/backend';

import { useToast } from '@/hooks/use-toast';

interface DocumentAnalyzerProps {
  document: Document;
  onAnalysisComplete?: (analysis: any) => void;
}

interface AnalysisResult {
  id: string;
  type: 'entity' | 'sentiment' | 'classification' | 'summary' | 'keywords';
  title: string;
  confidence: number;
  data: any;
  status: 'pending' | 'completed' | 'failed';
}

export function DocumentAnalyzer({ document, onAnalysisComplete }: DocumentAnalyzerProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult[]>([]);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('overview');

  const { toast } = useToast();

  // Real document analysis function using backend API
  const analyzeDocuments = async (documentIds: string[], analysisType: string, options: any) => {
    try {
      const response = await fetch('/documents/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentIds,
          analysisType,
          options
        })
      });

      if (!response.ok) {
        throw new Error(`Analysis failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.results || {};
    } catch (error) {
      console.error('Document analysis failed:', error);

      // Fallback to basic analysis structure
      return {
        entities: [],
        sentiment: "Unknown",
        categories: [],
        summary: "Analysis could not be completed at this time.",
        keywords: [],
        error: error instanceof Error ? error.message : 'Analysis failed'
      };
    }
  };

  const isAnalyzing = false;

  // Start comprehensive analysis
  const startAnalysis = async () => {
    setIsProcessing(true);
    setProgress(0);
    setAnalysisResults([]);

    try {
      // Initialize analysis tasks
      const analysisTypes = [
        { type: 'entity', title: 'Entity Extraction' },
        { type: 'sentiment', title: 'Sentiment Analysis' },
        { type: 'classification', title: 'Document Classification' },
        { type: 'summary', title: 'Content Summary' },
        { type: 'keywords', title: 'Keyword Extraction' }
      ];

      // Create pending results
      const pendingResults: AnalysisResult[] = analysisTypes.map((analysis) => ({
        id: `${analysis.type}-${Date.now()}`,
        type: analysis.type as any,
        title: analysis.title,
        confidence: 0,
        data: null,
        status: 'pending'
      }));

      setAnalysisResults(pendingResults);

      // Run analysis for each type
      for (let i = 0; i < analysisTypes.length; i++) {
        const analysisType = analysisTypes[i];

        try {
          const result = await analyzeDocuments([document.id], 'COMPREHENSIVE', {
            depth: 'standard',
            includeVisualizations: false
          });

          // Update result
          setAnalysisResults(prev => prev.map(r =>
            r.type === analysisType.type
              ? { ...r, data: result, confidence: 0.8, status: 'completed' }
              : r
          ));

          setProgress(((i + 1) / analysisTypes.length) * 100);
        } catch (error) {
          // Mark as failed
          setAnalysisResults(prev => prev.map(r =>
            r.type === analysisType.type
              ? { ...r, status: 'failed' }
              : r
          ));
        }
      }

      toast({
        title: "Analysis Complete",
        description: "Document analysis has been completed successfully.",
      });

      if (onAnalysisComplete) {
        onAnalysisComplete(analysisResults);
      }
    } catch (error) {
      toast({
        title: "Analysis Failed",
        description: "Failed to analyze document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get analysis status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // Render analysis result
  const renderAnalysisResult = (result: AnalysisResult) => {
    if (result.status === 'pending') {
      return (
        <div className="flex items-center justify-center p-8">
          <Spinner className="h-6 w-6" />
          <span className="ml-2">Analyzing...</span>
        </div>
      );
    }

    if (result.status === 'failed') {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Analysis failed. Please try again.
          </AlertDescription>
        </Alert>
      );
    }

    // Render based on analysis type
    switch (result.type) {
      case 'entity':
        return (
          <div className="space-y-4">
            {result.data?.entities?.map((entity: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded">
                <div>
                  <span className="font-medium">{entity.text}</span>
                  <Badge variant="outline" className="ml-2">{entity.type}</Badge>
                </div>
                <span className="text-sm text-muted-foreground">
                  {Math.round(entity.confidence * 100)}%
                </span>
              </div>
            ))}
          </div>
        );

      case 'sentiment':
        return (
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-bold mb-2">
                {result.data?.sentiment || 'Neutral'}
              </div>
              <Progress value={result.confidence * 100} className="w-full" />
              <p className="text-sm text-muted-foreground mt-2">
                Confidence: {Math.round(result.confidence * 100)}%
              </p>
            </div>
          </div>
        );

      case 'classification':
        return (
          <div className="space-y-4">
            {result.data?.categories?.map((category: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded">
                <span className="font-medium">{category.name}</span>
                <Badge variant="secondary">
                  {Math.round(category.confidence * 100)}%
                </Badge>
              </div>
            ))}
          </div>
        );

      case 'summary':
        return (
          <div className="prose max-w-none">
            <p>{result.data?.summary || 'No summary available'}</p>
          </div>
        );

      case 'keywords':
        return (
          <div className="flex flex-wrap gap-2">
            {result.data?.keywords?.map((keyword: any, index: number) => (
              <Badge key={index} variant="outline">
                {keyword.text} ({Math.round(keyword.confidence * 100)}%)
              </Badge>
            ))}
          </div>
        );

      default:
        return <div>No data available</div>;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Document Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="font-medium">{document.name}</h3>
              <p className="text-sm text-muted-foreground">
                Analyze document content using AI
              </p>
            </div>
            <Button
              onClick={startAnalysis}
              disabled={isProcessing || isAnalyzing}
              className="flex items-center gap-2"
            >
              {isProcessing ? (
                <Spinner className="h-4 w-4" />
              ) : (
                <Zap className="h-4 w-4" />
              )}
              {isProcessing ? 'Analyzing...' : 'Start Analysis'}
            </Button>
          </div>

          {isProcessing && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm">Analysis Progress</span>
                <span className="text-sm">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {analysisResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Analysis Results</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-5 w-full">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="entities">Entities</TabsTrigger>
                <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
                <TabsTrigger value="classification">Categories</TabsTrigger>
                <TabsTrigger value="summary">Summary</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {analysisResults.map((result) => (
                    <Card key={result.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{result.title}</span>
                          {getStatusIcon(result.status)}
                        </div>
                        {result.status === 'completed' && (
                          <div className="text-sm text-muted-foreground">
                            Confidence: {Math.round(result.confidence * 100)}%
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="entities">
                {renderAnalysisResult(analysisResults.find(r => r.type === 'entity') || {} as AnalysisResult)}
              </TabsContent>

              <TabsContent value="sentiment">
                {renderAnalysisResult(analysisResults.find(r => r.type === 'sentiment') || {} as AnalysisResult)}
              </TabsContent>

              <TabsContent value="classification">
                {renderAnalysisResult(analysisResults.find(r => r.type === 'classification') || {} as AnalysisResult)}
              </TabsContent>

              <TabsContent value="summary">
                {renderAnalysisResult(analysisResults.find(r => r.type === 'summary') || {} as AnalysisResult)}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
