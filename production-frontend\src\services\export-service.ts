/**
 * Export Service
 * Handles data export operations
 */

import { backendApiClient } from './backend-api-client'
import type { ID } from '../types'
import type { ExportJob, ExportRequest, ExportTemplate } from '../hooks/exports/useExports'

// Export status enum
export enum ExportStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Export type enum
export enum ExportType {
  DOCUMENTS = 'documents',
  PROJECTS = 'projects',
  TEMPLATES = 'templates',
  WORKFLOWS = 'workflows',
  ANALYTICS = 'analytics',
  USERS = 'users',
  ORGANIZATIONS = 'organizations'
}

// Export format enum
export enum ExportFormat {
  CSV = 'csv',
  JSON = 'json',
  XLSX = 'xlsx',
  EXCEL = 'xlsx',
  PDF = 'pdf',
  ZIP = 'zip'
}

export interface ExportProgress {
  exportId: ID
  progress: number
  status: string
  message?: string
  estimatedTimeRemaining?: number
}

export interface ExportStatistics {
  totalExports: number
  completedExports: number
  failedExports: number
  totalDataExported: number
  averageExportTime: number
  popularFormats: Array<{
    format: string
    count: number
    percentage: number
  }>
  popularTypes: Array<{
    type: string
    count: number
    percentage: number
  }>
}

class ExportService {
  /**
   * Create a new export job
   */
  async createExport(request: ExportRequest): Promise<ExportJob> {
    return await backendApiClient.request('/exports', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Get export job by ID
   */
  async getExport(exportId: ID): Promise<ExportJob> {
    return await backendApiClient.request(`/exports/${exportId}`)
  }

  /**
   * Get all export jobs
   */
  async getExports(options?: {
    status?: string
    type?: string
    format?: string
    limit?: number
    offset?: number
    startDate?: string
    endDate?: string
  }): Promise<{
    exports: ExportJob[]
    total: number
    hasMore: boolean
  }> {
    return await backendApiClient.request('/exports', {
      method: 'GET',
      params: options
    })
  }

  /**
   * Cancel an export job
   */
  async cancelExport(exportId: ID): Promise<void> {
    await backendApiClient.request(`/exports/${exportId}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Delete an export job
   */
  async deleteExport(exportId: ID): Promise<void> {
    await backendApiClient.request(`/exports/${exportId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Download export file
   */
  async downloadExport(exportId: ID): Promise<Blob> {
    try {
      const response = await backendApiClient.request(`/exports/${exportId}/download`, {
        method: 'GET',
        responseType: 'blob'
      })

      if (response instanceof Blob) {
        return response
      }

      // If response is not a blob, convert it
      return new Blob([response], { type: 'application/octet-stream' })
    } catch (error) {
      console.error('Failed to download export:', error)
      throw new Error(`Failed to download export ${exportId}`)
    }
  }

  /**
   * Get export progress
   */
  async getExportProgress(exportId: ID): Promise<ExportProgress> {
    return await backendApiClient.request(`/exports/${exportId}/progress`)
  }

  /**
   * Retry a failed export
   */
  async retryExport(exportId: ID): Promise<ExportJob> {
    return await backendApiClient.request(`/exports/${exportId}/retry`, {
      method: 'POST'
    })
  }

  /**
   * Get export templates
   */
  async getExportTemplates(): Promise<ExportTemplate[]> {
    return await backendApiClient.request('/exports/templates')
  }

  /**
   * Create export template
   */
  async createExportTemplate(template: Omit<ExportTemplate, 'id' | 'createdAt' | 'usageCount'>): Promise<ExportTemplate> {
    return await backendApiClient.request('/exports/templates', {
      method: 'POST',
      body: JSON.stringify(template)
    })
  }

  /**
   * Update export template
   */
  async updateExportTemplate(templateId: ID, updates: Partial<ExportTemplate>): Promise<ExportTemplate> {
    return await backendApiClient.request(`/exports/templates/${templateId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    })
  }

  /**
   * Delete export template
   */
  async deleteExportTemplate(templateId: ID): Promise<void> {
    await backendApiClient.request(`/exports/templates/${templateId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Use export template to create export
   */
  async useExportTemplate(templateId: ID, parameters?: Record<string, any>): Promise<ExportJob> {
    return await backendApiClient.request(`/exports/templates/${templateId}/use`, {
      method: 'POST',
      body: JSON.stringify({ parameters })
    })
  }

  /**
   * Bulk operations
   */
  async bulkCancelExports(exportIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> {
    return await backendApiClient.request('/exports/bulk/cancel', {
      method: 'POST',
      body: JSON.stringify({ exportIds })
    })
  }

  async bulkDeleteExports(exportIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> {
    return await backendApiClient.request('/exports/bulk/delete', {
      method: 'POST',
      body: JSON.stringify({ exportIds })
    })
  }

  async bulkDownloadExports(exportIds: ID[]): Promise<Blob> {
    try {
      const response = await backendApiClient.request('/exports/bulk/download', {
        method: 'POST',
        body: JSON.stringify({ exportIds }),
        responseType: 'blob'
      })

      if (response instanceof Blob) {
        return response
      }

      // If response is not a blob, convert it
      return new Blob([response], { type: 'application/zip' })
    } catch (error) {
      console.error('Failed to bulk download exports:', error)
      throw new Error(`Failed to bulk download exports: ${exportIds.join(', ')}`)
    }
  }

  /**
   * Export specific data types
   */
  async exportDocuments(options: {
    documentIds?: ID[]
    projectId?: ID
    organizationId?: ID
    format: 'pdf' | 'zip' | 'json'
    includeMetadata?: boolean
    includeVersions?: boolean
    dateRange?: {
      start: string
      end: string
    }
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/documents', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  async exportProjects(options: {
    projectIds?: ID[]
    organizationId?: ID
    format: 'json' | 'csv' | 'xlsx'
    includeDocuments?: boolean
    includeMembers?: boolean
    includeTemplates?: boolean
    dateRange?: {
      start: string
      end: string
    }
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/projects', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  async exportTemplates(options: {
    templateIds?: ID[]
    organizationId?: ID
    format: 'json' | 'zip'
    includeFields?: boolean
    includeVersions?: boolean
    status?: string[]
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/templates', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  async exportWorkflows(options: {
    workflowIds?: ID[]
    organizationId?: ID
    format: 'json' | 'csv'
    includeExecutions?: boolean
    includeSteps?: boolean
    dateRange?: {
      start: string
      end: string
    }
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/workflows', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  async exportAnalytics(options: {
    type: 'usage' | 'performance' | 'errors' | 'users' | 'documents'
    format: 'csv' | 'xlsx' | 'json'
    dateRange: {
      start: string
      end: string
    }
    organizationId?: ID
    projectId?: ID
    granularity?: 'hour' | 'day' | 'week' | 'month'
    metrics?: string[]
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/analytics', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  async exportUsers(options: {
    userIds?: ID[]
    organizationId?: ID
    format: 'csv' | 'xlsx' | 'json'
    includeProfile?: boolean
    includeActivity?: boolean
    includePermissions?: boolean
    status?: string[]
    roles?: string[]
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/users', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  async exportOrganizations(options: {
    organizationIds?: ID[]
    format: 'csv' | 'xlsx' | 'json'
    includeMembers?: boolean
    includeProjects?: boolean
    includeSettings?: boolean
    status?: string[]
  }): Promise<ExportJob> {
    return await backendApiClient.request('/exports/organizations', {
      method: 'POST',
      body: JSON.stringify(options)
    })
  }

  /**
   * Get export statistics
   */
  async getExportStatistics(options?: {
    dateRange?: {
      start: string
      end: string
    }
    organizationId?: ID
  }): Promise<ExportStatistics> {
    return await backendApiClient.request('/exports/statistics', {
      method: 'GET',
      params: {
        startDate: options?.dateRange?.start,
        endDate: options?.dateRange?.end,
        organizationId: options?.organizationId
      }
    })
  }

  /**
   * Validate export request
   */
  async validateExportRequest(request: ExportRequest): Promise<{
    valid: boolean
    errors: string[]
    warnings: string[]
    estimatedSize?: number
    estimatedTime?: number
  }> {
    return await backendApiClient.request('/exports/validate', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Get supported export formats for a data type
   */
  getSupportedFormats(type: string): Array<{
    format: string
    label: string
    description: string
    maxSize?: number
    features: string[]
  }> {
    const formatConfigs = {
      documents: [
        {
          format: 'pdf',
          label: 'PDF',
          description: 'Individual PDF files for each document',
          maxSize: 100 * 1024 * 1024, // 100MB
          features: ['preserves-formatting', 'searchable', 'printable']
        },
        {
          format: 'zip',
          label: 'ZIP Archive',
          description: 'All documents in a compressed archive',
          maxSize: 1024 * 1024 * 1024, // 1GB
          features: ['bulk-download', 'preserves-structure', 'includes-metadata']
        },
        {
          format: 'json',
          label: 'JSON',
          description: 'Document metadata and content in JSON format',
          maxSize: 50 * 1024 * 1024, // 50MB
          features: ['machine-readable', 'includes-metadata', 'version-history']
        }
      ],
      projects: [
        {
          format: 'json',
          label: 'JSON',
          description: 'Project data in JSON format',
          features: ['complete-data', 'machine-readable', 'includes-relationships']
        },
        {
          format: 'csv',
          label: 'CSV',
          description: 'Project data in comma-separated values',
          features: ['spreadsheet-compatible', 'simple-format', 'tabular-data']
        },
        {
          format: 'xlsx',
          label: 'Excel',
          description: 'Project data in Excel spreadsheet',
          features: ['spreadsheet-native', 'multiple-sheets', 'formatted-data']
        }
      ],
      // Add more type configurations as needed
    }

    return formatConfigs[type as keyof typeof formatConfigs] || []
  }

  /**
   * Get export type configurations
   */
  getExportTypes(): Array<{
    type: string
    label: string
    description: string
    supportedFormats: string[]
    defaultFormat: string
  }> {
    return [
      {
        type: 'documents',
        label: 'Documents',
        description: 'Export document files and metadata',
        supportedFormats: ['pdf', 'zip', 'json'],
        defaultFormat: 'zip'
      },
      {
        type: 'projects',
        label: 'Projects',
        description: 'Export project data and structure',
        supportedFormats: ['json', 'csv', 'xlsx'],
        defaultFormat: 'json'
      },
      {
        type: 'templates',
        label: 'Templates',
        description: 'Export template definitions and fields',
        supportedFormats: ['json', 'zip'],
        defaultFormat: 'json'
      },
      {
        type: 'workflows',
        label: 'Workflows',
        description: 'Export workflow definitions and execution data',
        supportedFormats: ['json', 'csv'],
        defaultFormat: 'json'
      },
      {
        type: 'analytics',
        label: 'Analytics',
        description: 'Export usage and performance analytics',
        supportedFormats: ['csv', 'xlsx', 'json'],
        defaultFormat: 'csv'
      },
      {
        type: 'users',
        label: 'Users',
        description: 'Export user profiles and activity data',
        supportedFormats: ['csv', 'xlsx', 'json'],
        defaultFormat: 'csv'
      },
      {
        type: 'organizations',
        label: 'Organizations',
        description: 'Export organization data and settings',
        supportedFormats: ['csv', 'xlsx', 'json'],
        defaultFormat: 'csv'
      }
    ]
  }
}

// Export singleton instance
export const exportService = new ExportService()
export default exportService
