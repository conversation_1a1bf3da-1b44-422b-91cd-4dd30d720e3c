"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { AdminOnly } from "@/components/permission-guard";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { TenantSettings } from "@/types/tenant";
import { backendApiClient } from "@/services/backend-api-client";

// Form schema
const formSchema = z.object({
  // Features
  multiOrganization: z.boolean(),
  customRoles: z.boolean(),
  customBranding: z.boolean(),
  advancedSecurity: z.boolean(),
  dataIsolation: z.boolean(),

  // Security
  mfaRequired: z.boolean(),
  passwordMinLength: z.number().min(6).max(32),
  requireUppercase: z.boolean(),
  requireLowercase: z.boolean(),
  requireNumbers: z.boolean(),
  requireSpecialChars: z.boolean(),
  sessionTimeout: z.number().min(300).max(86400),

  // Branding
  logoUrl: z.string().url().optional().or(z.literal('')),
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Must be a valid hex color").optional().or(z.literal('')),
  secondaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Must be a valid hex color").optional().or(z.literal('')),
  faviconUrl: z.string().url().optional().or(z.literal('')),

  // Limits
  maxUsers: z.number().min(1).max(1000),
  maxOrganizations: z.number().min(1).max(100),
  maxProjects: z.number().min(1).max(500),
  maxStorage: z.number().min(1).max(1024)
});

type FormValues = z.infer<typeof formSchema>;

export default function TenantSettingsPage() {
  const params = useParams();
  const { toast } = useToast();

  // Ensure params is not null
  if (!params) {
    return <div>Loading...</div>;
  }

  const tenantId = params.tenantId as string;

  const [activeTab, setActiveTab] = useState("features");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load tenant data from API
  const [tenant, setTenant] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load tenant data from API
    const loadTenant = async () => {
      try {
        const response = await fetch(`/management/tenants/${tenantId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to load tenant: ${response.statusText}`);
        }

        const data = await response.json();
        setTenant(data.tenant);
      } catch (error) {
        console.error('Error loading tenant:', error);
        toast({
          title: "Error loading tenant",
          description: "Failed to load tenant data. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadTenant();
  }, [tenantId]);

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      // Features
      multiOrganization: false,
      customRoles: false,
      customBranding: false,
      advancedSecurity: false,
      dataIsolation: true,

      // Security
      mfaRequired: false,
      passwordMinLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
      sessionTimeout: 3600,

      // Branding
      logoUrl: '',
      primaryColor: '',
      secondaryColor: '',
      faviconUrl: '',

      // Limits
      maxUsers: 50,
      maxOrganizations: 5,
      maxProjects: 20,
      maxStorage: 10 // 10 GB
    }
  });

  // Update form values when tenant data is loaded
  useEffect(() => {
    if (tenant) {
      form.reset({
        // Features
        multiOrganization: tenant.settings.features.multiOrganization,
        customRoles: tenant.settings.features.customRoles,
        customBranding: tenant.settings.features.customBranding,
        advancedSecurity: tenant.settings.features.advancedSecurity,
        dataIsolation: tenant.settings.features.dataIsolation,

        // Security
        mfaRequired: tenant.settings.security.mfaRequired,
        passwordMinLength: tenant.settings.security.passwordPolicy.minLength,
        requireUppercase: tenant.settings.security.passwordPolicy.requireUppercase,
        requireLowercase: tenant.settings.security.passwordPolicy.requireLowercase,
        requireNumbers: tenant.settings.security.passwordPolicy.requireNumbers,
        requireSpecialChars: tenant.settings.security.passwordPolicy.requireSpecialChars,
        sessionTimeout: tenant.settings.security.sessionTimeout,

        // Branding
        logoUrl: tenant.settings.branding?.logoUrl || '',
        primaryColor: tenant.settings.branding?.primaryColor || '',
        secondaryColor: tenant.settings.branding?.secondaryColor || '',
        faviconUrl: tenant.settings.branding?.faviconUrl || '',

        // Limits
        maxUsers: tenant.settings.limits.maxUsers,
        maxOrganizations: tenant.settings.limits.maxOrganizations,
        maxProjects: tenant.settings.limits.maxProjects,
        maxStorage: Math.floor(tenant.settings.limits.maxStorage / (1024 * 1024 * 1024)) // Convert bytes to GB
      });
    }
  }, [tenant, form]);

  // Update tenant via API
  const updateTenant = async (data: any) => {
    const response = await fetch(`/admin/tenants/${data.tenantId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data.settings)
    });

    if (!response.ok) {
      throw new Error(`Failed to update tenant: ${response.statusText}`);
    }

    return response.json();
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Convert form values to tenant settings
      const features: string[] = [];
      if (values.multiOrganization) features.push('multiOrganization');
      if (values.customRoles) features.push('customRoles');
      if (values.customBranding) features.push('customBranding');
      if (values.advancedSecurity) features.push('advancedSecurity');
      if (values.dataIsolation) features.push('dataIsolation');

      const settings: Partial<TenantSettings> = {
        features,
        mfaRequired: values.mfaRequired,
        sessionTimeout: values.sessionTimeout,
        passwordPolicy: {
          minLength: values.passwordMinLength,
          requireUppercase: values.requireUppercase,
          requireLowercase: values.requireLowercase,
          requireNumbers: values.requireNumbers,
          requireSpecialChars: values.requireSpecialChars
        },
        branding: {
          primaryColor: values.primaryColor || undefined,
          secondaryColor: values.secondaryColor || undefined,
          logo: values.logoUrl || undefined,
          favicon: values.faviconUrl || undefined
        },
        maxUsers: values.maxUsers,
        maxProjects: values.maxProjects,
        maxStorage: values.maxStorage * 1024 * 1024 * 1024 // Convert GB to bytes
      };

      // Update tenant via API
      try {
        await updateTenant({ tenantId, settings });
        toast({
          title: "Settings updated",
          description: "Tenant settings have been updated successfully."
        });
      } catch (error: any) {
        console.error("Error updating tenant settings:", error);
        toast({
          title: "Error updating settings",
          description: error.message || "An error occurred while updating tenant settings. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsSubmitting(false);
      }
    } catch (error) {
      console.error("Error preparing tenant settings update:", error);
      toast({
        title: "Error updating settings",
        description: "An error occurred while preparing the settings update. Please try again.",
        variant: "destructive"
      });
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  if (!tenant) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/tenants">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Tenant not found</h1>
        </div>
        <p>The tenant you are looking for does not exist or you do not have permission to view it.</p>
        <Button asChild>
          <Link href="/admin/tenants">Back to Tenants</Link>
        </Button>
      </div>
    );
  }

  return (
    <AdminOnly>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" asChild>
              <Link href={`/admin/tenants/${tenantId}`}>
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">Tenant Settings</h1>
          </div>
          <Button
            type="submit"
            form="tenant-settings-form"
            disabled={isSubmitting}
          >
            <Save className="mr-2 h-4 w-4" />
            Save Settings
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{tenant.displayName}</CardTitle>
            <CardDescription>{tenant.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm">
              <p><strong>Tenant ID:</strong> {tenant.id}</p>
              <p><strong>Domain:</strong> {tenant.domain}</p>
              <p><strong>Status:</strong> {tenant.status}</p>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form id="tenant-settings-form" onSubmit={form.handleSubmit(onSubmit)}>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-4 mb-6">
                <TabsTrigger value="features">Features</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
                <TabsTrigger value="branding">Branding</TabsTrigger>
                <TabsTrigger value="limits">Limits</TabsTrigger>
              </TabsList>

              <TabsContent value="features">
                <Card>
                  <CardHeader>
                    <CardTitle>Features</CardTitle>
                    <CardDescription>
                      Configure which features are available for this tenant
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="multiOrganization"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Multiple Organizations
                            </FormLabel>
                            <FormDescription>
                              Allow users to create and manage multiple organizations
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="customRoles"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Custom Roles
                            </FormLabel>
                            <FormDescription>
                              Allow administrators to create custom roles and permissions
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="customBranding"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Custom Branding
                            </FormLabel>
                            <FormDescription>
                              Allow customization of logos, colors, and other branding elements
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="advancedSecurity"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Advanced Security
                            </FormLabel>
                            <FormDescription>
                              Enable advanced security features like IP restrictions and audit logs
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dataIsolation"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Data Isolation
                            </FormLabel>
                            <FormDescription>
                              Strictly isolate tenant data from other tenants
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="security">
                <Card>
                  <CardHeader>
                    <CardTitle>Security</CardTitle>
                    <CardDescription>
                      Configure security settings for this tenant
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="mfaRequired"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">
                              Require Multi-Factor Authentication
                            </FormLabel>
                            <FormDescription>
                              Require all users to set up MFA
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="space-y-4 rounded-lg border p-4">
                      <h3 className="text-base font-medium">Password Policy</h3>

                      <FormField
                        control={form.control}
                        name="passwordMinLength"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minimum Length</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={6}
                                max={32}
                                {...field}
                                onChange={e => field.onChange(parseInt(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Minimum number of characters required for passwords
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="requireUppercase"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between space-x-2">
                              <FormLabel>Require Uppercase</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="requireLowercase"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between space-x-2">
                              <FormLabel>Require Lowercase</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="requireNumbers"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between space-x-2">
                              <FormLabel>Require Numbers</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="requireSpecialChars"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between space-x-2">
                              <FormLabel>Require Special Characters</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="sessionTimeout"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Session Timeout (seconds)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={300}
                              max={86400}
                              {...field}
                              onChange={e => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Time in seconds before an inactive session expires (minimum 5 minutes, maximum 24 hours)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="branding">
                <Card>
                  <CardHeader>
                    <CardTitle>Branding</CardTitle>
                    <CardDescription>
                      Configure branding settings for this tenant
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="logoUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Logo URL</FormLabel>
                          <FormControl>
                            <Input placeholder="https://example.com/logo.png" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormDescription>
                            URL to the tenant's logo image (recommended size: 200x50px)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="primaryColor"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Primary Color</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input placeholder="#1a73e8" {...field} value={field.value || ''} />
                              </FormControl>
                              <div
                                className="h-10 w-10 rounded-md border"
                                style={{ backgroundColor: field.value || '#ffffff' }}
                              />
                            </div>
                            <FormDescription>
                              Primary brand color (hex format)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="secondaryColor"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Secondary Color</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input placeholder="#f1f3f4" {...field} value={field.value || ''} />
                              </FormControl>
                              <div
                                className="h-10 w-10 rounded-md border"
                                style={{ backgroundColor: field.value || '#ffffff' }}
                              />
                            </div>
                            <FormDescription>
                              Secondary brand color (hex format)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="faviconUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Favicon URL</FormLabel>
                          <FormControl>
                            <Input placeholder="https://example.com/favicon.ico" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormDescription>
                            URL to the tenant's favicon (recommended size: 32x32px)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="limits">
                <Card>
                  <CardHeader>
                    <CardTitle>Limits</CardTitle>
                    <CardDescription>
                      Configure resource limits for this tenant
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="maxUsers"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Users</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={1000}
                              {...field}
                              onChange={e => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum number of users allowed in this tenant
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxOrganizations"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Organizations</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={100}
                              {...field}
                              onChange={e => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum number of organizations allowed in this tenant
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxProjects"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Projects</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={500}
                              {...field}
                              onChange={e => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum number of projects allowed in this tenant
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maxStorage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum Storage (GB)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min={1}
                              max={1024}
                              {...field}
                              onChange={e => field.onChange(parseInt(e.target.value))}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum storage space allowed for this tenant (in gigabytes)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Saving..." : "Save Settings"}
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </div>
    </AdminOnly>
  );
}
