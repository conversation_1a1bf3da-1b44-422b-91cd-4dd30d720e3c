"use client";

import { useState, useEffect } from "react";
import { useR<PERSON>er, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON><PERSON>, Plus, X } from "lucide-react";
import { useProjects } from "@/hooks/projects";
import { useOrganizations } from "@/hooks/organizations";
import { ProjectVisibility } from "@/types/backend";

// Form validation schema
const createProjectSchema = z.object({
  name: z.string().min(2, {
    message: "Project name must be at least 2 characters",
  }).max(50, {
    message: "Project name must be at most 50 characters",
  }),
  description: z.string().max(500, {
    message: "Description must be at most 500 characters",
  }).optional(),
  organizationId: z.string().min(1, {
    message: "Organization is required",
  }),
  visibility: z.nativeEnum(ProjectVisibility),
  tags: z.array(z.string()).optional(),
});

type CreateProjectFormValues = z.infer<typeof createProjectSchema>;

export default function CreateProjectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const { createProject, isCreating } = useProjects();
  const { organizations, currentOrganization } = useOrganizations();

  const [tagInput, setTagInput] = useState("");
  const [tags, setTags] = useState<string[]>([]);

  // Get organization ID from query params
  const organizationIdParam = searchParams ? searchParams.get("organizationId") : null;

  // Initialize form
  const form = useForm<CreateProjectFormValues>({
    resolver: zodResolver(createProjectSchema),
    defaultValues: {
      name: "",
      description: "",
      organizationId: organizationIdParam || currentOrganization?.id || "",
      visibility: ProjectVisibility.PRIVATE,
      tags: [],
    },
  });

  // Update form when organization ID changes
  useEffect(() => {
    if (organizationIdParam) {
      form.setValue("organizationId", organizationIdParam);
    } else if (currentOrganization?.id) {
      form.setValue("organizationId", currentOrganization.id);
    }
  }, [organizationIdParam, currentOrganization, form]);

  // Form submission handler
  const onSubmit = async (data: CreateProjectFormValues) => {
    try {
      // Add tags to form data
      data.tags = tags;

      createProject(data);
      toast({
        title: "Project created",
        description: `${data.name} has been created successfully.`,
      });
      router.push("/projects");
    } catch (error: any) {
      toast({
        title: "Failed to create project",
        description: error.message || "An error occurred while creating the project.",
      });
    }
  };

  // Tag management
  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-2"
          asChild
        >
          <Link href="/projects">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Projects
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Create Project</h1>
        <p className="text-muted-foreground">
          Create a new project to organize your documents
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Project Details</CardTitle>
              <CardDescription>
                Basic information about your project
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Name</FormLabel>
                    <FormControl>
                      <Input placeholder="My Project" {...field} />
                    </FormControl>
                    <FormDescription>
                      This is the name that will be displayed to your team members.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="A brief description of your project"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description to help team members understand the purpose of this project.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="organizationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an organization" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {organizations.map((org: any) => (
                          <SelectItem key={org.id} value={org.id}>
                            {org.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      The organization this project belongs to.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="visibility"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Visibility</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select visibility" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={ProjectVisibility.PRIVATE}>
                          Private (Only invited members)
                        </SelectItem>
                        <SelectItem value={ProjectVisibility.ORGANIZATION}>
                          Organization (All organization members)
                        </SelectItem>
                        <SelectItem value={ProjectVisibility.PUBLIC}>
                          Public (Anyone with the link)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Who can access this project.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <FormLabel>Tags</FormLabel>
                <div className="flex flex-wrap gap-2 mb-2">
                  {tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-muted-foreground hover:text-foreground"
                      >
                        <X size={14} />
                      </button>
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagInputKeyDown}
                    placeholder="Add tags..."
                    className="flex-1"
                  />
                  <Button type="button" size="icon" onClick={addTag}>
                    <Plus size={16} />
                  </Button>
                </div>
                <FormDescription>
                  Optional tags to categorize your project.
                </FormDescription>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button type="submit" disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Project"}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  );
}
