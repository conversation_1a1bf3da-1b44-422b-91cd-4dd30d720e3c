import React, { useState, useEffect } from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Loader2, RefreshCw, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { backendApiClient } from '../../services/backend-api-client';
import { logger } from '../../lib/logger';

interface EventMetrics {
  eventType: string;
  count: number;
  successCount: number;
  failureCount: number;
  averageProcessingTime: number;
  lastProcessed: string;
}

interface EventHealthStatus {
  isHealthy: boolean;
  eventGridConnected: boolean;
  serviceBusConnected: boolean;
  processingErrors: number;
  lastHealthCheck: string;
  metrics: EventMetrics[];
  detailedMetrics?: EventMetrics[];
}

interface MetricsSummary {
  totalEvents: number;
  totalSuccesses: number;
  totalFailures: number;
  successRate: number;
  failureRate: number;
  averageProcessingTime: number;
  eventTypes: number;
}

const EventGridMonitor: React.FC = () => {
  const [healthStatus, setHealthStatus] = useState<EventHealthStatus | null>(null);
  const [metrics, setMetrics] = useState<{ summary: MetricsSummary; metrics: EventMetrics[] } | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { toast } = useToast();

  const fetchHealthStatus = async () => {
    try {
      const response = await backendApiClient.request<{ data: EventHealthStatus }>('/monitoring/event-grid/health', { method: 'GET' });
      setHealthStatus(response.data);
    } catch (error) {
      logger.error('Error fetching Event Grid health status:', error as string);
      toast({
        title: 'Error',
        description: 'Failed to fetch Event Grid health status',
        variant: 'destructive'
      });
    }
  };

  const fetchMetrics = async () => {
    try {
      const response = await backendApiClient.request<{ data: { summary: MetricsSummary; metrics: EventMetrics[] } }>('/monitoring/event-grid/metrics', { method: 'GET' });
      setMetrics(response.data);
    } catch (error) {
      logger.error('Error fetching Event Grid metrics:', error as string);
      toast({
        title: 'Error',
        description: 'Failed to fetch Event Grid metrics',
        variant: 'destructive'
      });
    }
  };

  const triggerHealthCheck = async () => {
    setRefreshing(true);
    try {
      await backendApiClient.request('/monitoring/event-grid/health', { method: 'POST' });
      await fetchHealthStatus();
      toast({
        title: 'Success',
        description: 'Health check completed successfully'
      });
    } catch (error) {
      logger.error('Error triggering health check:', error as string);
      toast({
        title: 'Error',
        description: 'Failed to trigger health check',
        variant: 'destructive'
      });
    } finally {
      setRefreshing(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    try {
      await Promise.all([fetchHealthStatus(), fetchMetrics()]);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await Promise.all([fetchHealthStatus(), fetchMetrics()]);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000);
    return () => clearInterval(interval);
  }, []);

  const getHealthStatusIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  const getConnectionStatusBadge = (connected: boolean) => {
    return (
      <Badge variant={connected ? 'default' : 'destructive'}>
        {connected ? 'Connected' : 'Disconnected'}
      </Badge>
    );
  };

  const formatProcessingTime = (timeMs: number) => {
    if (timeMs < 1000) {
      return `${Math.round(timeMs)}ms`;
    }
    return `${(timeMs / 1000).toFixed(2)}s`;
  };

  const formatSuccessRate = (rate: number) => {
    return `${rate.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading Event Grid monitoring data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Event Grid Monitoring</h1>
        <div className="flex gap-2">
          <Button
            onClick={triggerHealthCheck}
            disabled={refreshing}
            variant="outline"
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Health Check
          </Button>
          <Button onClick={refreshData} disabled={refreshing} variant="outline">
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Health Status */}
      {healthStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getHealthStatusIcon(healthStatus.isHealthy)}
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Overall Status</label>
                <div className="flex items-center gap-2 mt-1">
                  {getHealthStatusIcon(healthStatus.isHealthy)}
                  <span className={healthStatus.isHealthy ? 'text-green-600' : 'text-red-600'}>
                    {healthStatus.isHealthy ? 'Healthy' : 'Unhealthy'}
                  </span>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Event Grid</label>
                <div className="mt-1">
                  {getConnectionStatusBadge(healthStatus.eventGridConnected)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Service Bus</label>
                <div className="mt-1">
                  {getConnectionStatusBadge(healthStatus.serviceBusConnected)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Processing Errors</label>
                <div className="flex items-center gap-2 mt-1">
                  {healthStatus.processingErrors > 0 && (
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  )}
                  <span className={healthStatus.processingErrors > 5 ? 'text-red-600' : 'text-gray-900'}>
                    {healthStatus.processingErrors}
                  </span>
                </div>
              </div>
            </div>
            <div className="mt-4 text-sm text-gray-500">
              Last checked: {new Date(healthStatus.lastHealthCheck).toLocaleString()}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Metrics Summary */}
      {metrics && (
        <Card>
          <CardHeader>
            <CardTitle>Processing Metrics (Last 24 Hours)</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Total Events</label>
                <div className="text-2xl font-bold mt-1">{metrics.summary.totalEvents.toLocaleString()}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Success Rate</label>
                <div className="text-2xl font-bold mt-1 text-green-600">
                  {formatSuccessRate(metrics.summary.successRate)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Failure Rate</label>
                <div className="text-2xl font-bold mt-1 text-red-600">
                  {formatSuccessRate(metrics.summary.failureRate)}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Avg Processing Time</label>
                <div className="text-2xl font-bold mt-1">
                  {formatProcessingTime(metrics.summary.averageProcessingTime)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Event Type Metrics */}
      {metrics && metrics.metrics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Event Type Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Event Type</th>
                    <th className="text-right py-2">Total</th>
                    <th className="text-right py-2">Success</th>
                    <th className="text-right py-2">Failed</th>
                    <th className="text-right py-2">Success Rate</th>
                    <th className="text-right py-2">Avg Time</th>
                    <th className="text-right py-2">Last Processed</th>
                  </tr>
                </thead>
                <tbody>
                  {metrics.metrics.map((metric) => (
                    <tr key={metric.eventType} className="border-b">
                      <td className="py-2 font-medium">{metric.eventType}</td>
                      <td className="text-right py-2">{metric.count.toLocaleString()}</td>
                      <td className="text-right py-2 text-green-600">{metric.successCount.toLocaleString()}</td>
                      <td className="text-right py-2 text-red-600">{metric.failureCount.toLocaleString()}</td>
                      <td className="text-right py-2">
                        {metric.count > 0 ? formatSuccessRate((metric.successCount / metric.count) * 100) : 'N/A'}
                      </td>
                      <td className="text-right py-2">{formatProcessingTime(metric.averageProcessingTime)}</td>
                      <td className="text-right py-2 text-sm text-gray-500">
                        {new Date(metric.lastProcessed).toLocaleString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      {healthStatus && !healthStatus.isHealthy && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Event Grid system is experiencing issues. Please check the connection status and processing errors above.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default EventGridMonitor;
