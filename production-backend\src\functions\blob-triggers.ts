/**
 * Blob Storage Triggered Azure Functions
 * Handles blob creation, deletion, and processing events
 */

import { InvocationContext, app } from '@azure/functions';
import { BlobServiceClient } from '@azure/storage-blob';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';
import <PERSON><PERSON> from 'jimp';

/**
 * Document blob trigger - processes new documents when uploaded
 */
async function documentBlobTrigger(blob: unknown, context: InvocationContext): Promise<void> {
  const blobBuffer = blob as Buffer;
  const blobName = context.triggerMetadata?.name as string;
  const blobUri = context.triggerMetadata?.uri as string;

  logger.info('Document blob trigger activated', {
    blobName,
    blobUri,
    blobSize: blobBuffer.length
  });

  try {
    // Extract file information
    const fileExtension = blobName.split('.').pop()?.toLowerCase();
    const fileName = blobName.split('/').pop() || blobName;

    // Create document record in database
    const documentId = `doc-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const document = {
      id: documentId,
      fileName,
      blobName,
      blobUri,
      size: blobBuffer.length,
      fileType: fileExtension,
      status: 'uploaded',
      createdAt: new Date().toISOString(),
      metadata: {
        triggerSource: 'blob-trigger',
        containerName: context.triggerMetadata?.containerName
      }
    };

    await db.createItem('documents', document);

    // Generate thumbnail for image files
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension || '')) {
      await generateThumbnail(blobBuffer, blobName);
    }

    // Trigger document processing based on file type
    await triggerDocumentProcessing(documentId, fileExtension || '', blobBuffer);

    // Publish document uploaded event
    await publishEvent(
      EventType.DOCUMENT_UPLOADED,
      `documents/${documentId}`,
      {
        documentId,
        fileName,
        blobName,
        fileType: fileExtension,
        size: blobBuffer.length,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Document blob processing completed', {
      documentId,
      fileName,
      fileType: fileExtension
    });

  } catch (error) {
    logger.error('Document blob trigger failed', {
      blobName,
      error: error instanceof Error ? error.message : String(error)
    });

    // Update document status to failed if it was created
    try {
      const docs = await db.queryItems<any>('documents',
        'SELECT * FROM c WHERE c.blobName = @blobName',
        [blobName]
      );

      if (docs.length > 0) {
        const doc = docs[0];
        await db.updateItem('documents', {
          ...doc,
          status: 'processing_failed',
          error: error instanceof Error ? error.message : String(error),
          updatedAt: new Date().toISOString()
        });
      }
    } catch (updateError) {
      logger.error('Failed to update document status', { updateError });
    }
  }
}

/**
 * Generate thumbnail for image files
 */
async function generateThumbnail(imageBuffer: Buffer, originalBlobName: string): Promise<void> {
  try {
    // Read image with Jimp
    const image = await Jimp.read(imageBuffer);

    // Resize to thumbnail size (200x200) maintaining aspect ratio
    const thumbnail = image.scaleToFit(200, 200);

    // Convert to JPEG with quality 80
    const thumbnailBuffer = await thumbnail.quality(80).getBufferAsync(Jimp.MIME_JPEG);

    // Upload thumbnail to thumbnails container
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );

    const thumbnailContainer = blobServiceClient.getContainerClient(
      process.env.THUMBNAIL_CONTAINER || 'thumbnails'
    );

    const thumbnailBlobName = `thumb_${originalBlobName.replace(/\.[^/.]+$/, '.jpg')}`;
    const thumbnailBlobClient = thumbnailContainer.getBlockBlobClient(thumbnailBlobName);

    await thumbnailBlobClient.upload(thumbnailBuffer, thumbnailBuffer.length, {
      blobHTTPHeaders: {
        blobContentType: 'image/jpeg'
      }
    });

    logger.info('Thumbnail generated successfully', {
      originalBlobName,
      thumbnailBlobName,
      thumbnailSize: thumbnailBuffer.length
    });

  } catch (error) {
    logger.error('Failed to generate thumbnail', {
      originalBlobName,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Trigger document processing based on file type
 */
async function triggerDocumentProcessing(
  documentId: string,
  fileType: string,
  content: Buffer
): Promise<void> {
  try {
    let processingType = 'unknown';

    // Determine processing type based on file extension
    switch (fileType) {
      case 'pdf':
        processingType = 'pdf-extraction';
        break;
      case 'doc':
      case 'docx':
        processingType = 'word-processing';
        break;
      case 'xls':
      case 'xlsx':
        processingType = 'excel-processing';
        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        processingType = 'image-analysis';
        break;
      case 'txt':
        processingType = 'text-analysis';
        break;
      default:
        processingType = 'generic-processing';
    }

    // Create processing job
    const processingJob = {
      id: `job-${documentId}-${Date.now()}`,
      documentId,
      processingType,
      status: 'queued',
      createdAt: new Date().toISOString(),
      metadata: {
        fileType,
        contentSize: content.length
      }
    };

    await db.createItem('processing-jobs', processingJob);

    // For immediate processing of small text files
    if (fileType === 'txt' && content.length < 1024 * 1024) { // Less than 1MB
      await processTextFile(documentId, content);
    }

    logger.info('Document processing triggered', {
      documentId,
      processingType,
      jobId: processingJob.id
    });

  } catch (error) {
    logger.error('Failed to trigger document processing', {
      documentId,
      fileType,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Process text files immediately
 */
async function processTextFile(documentId: string, content: Buffer): Promise<void> {
  try {
    const textContent = content.toString('utf-8');
    const wordCount = textContent.split(/\s+/).length;
    const lineCount = textContent.split('\n').length;

    // Update document with extracted text and metadata
    const documents = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.id = @documentId',
      [documentId]
    );

    if (documents.length > 0) {
      const document = documents[0];
      await db.updateItem('documents', {
        ...document,
        extractedText: textContent,
        status: 'processed',
        processedAt: new Date().toISOString(),
        analysis: {
          wordCount,
          lineCount,
          characterCount: textContent.length
        }
      });

      // Publish processing completed event
      await publishEvent(
        EventType.DOCUMENT_APPROVED,
        `documents/${documentId}`,
        {
          documentId,
          processingType: 'text-analysis',
          analysis: { wordCount, lineCount, characterCount: textContent.length },
          timestamp: new Date().toISOString()
        }
      );
    }

    logger.info('Text file processed successfully', {
      documentId,
      wordCount,
      lineCount
    });

  } catch (error) {
    logger.error('Failed to process text file', {
      documentId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Template blob trigger - processes template uploads
 */
async function templateBlobTrigger(blob: unknown, context: InvocationContext): Promise<void> {
  const blobBuffer = blob as Buffer;
  const blobName = context.triggerMetadata?.name as string;
  const blobUri = context.triggerMetadata?.uri as string;

  logger.info('Template blob trigger activated', {
    blobName,
    blobUri,
    blobSize: blobBuffer.length
  });

  try {
    const fileName = blobName.split('/').pop() || blobName;
    const fileExtension = blobName.split('.').pop()?.toLowerCase();

    // Create template record
    const templateId = `template-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const template = {
      id: templateId,
      name: fileName,
      blobName,
      blobUri,
      size: blobBuffer.length,
      fileType: fileExtension,
      status: 'uploaded',
      createdAt: new Date().toISOString(),
      metadata: {
        triggerSource: 'blob-trigger'
      }
    };

    await db.createItem('templates', template);

    logger.info('Template processed successfully', {
      templateId,
      fileName
    });

  } catch (error) {
    logger.error('Template blob trigger failed', {
      blobName,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Export blob trigger - handles export file generation
 */
async function exportBlobTrigger(blob: unknown, context: InvocationContext): Promise<void> {
  const blobBuffer = blob as Buffer;
  const blobName = context.triggerMetadata?.name as string;

  logger.info('Export blob trigger activated', {
    blobName,
    blobSize: blobBuffer.length
  });

  try {
    // Find the export job that created this blob
    const exportJobs = await db.queryItems<any>('export-jobs',
      'SELECT * FROM c WHERE c.outputBlobName = @blobName',
      [blobName]
    );

    if (exportJobs.length > 0) {
      const exportJob = exportJobs[0];

      // Update export job status
      await db.updateItem('export-jobs', {
        ...exportJob,
        status: 'completed',
        completedAt: new Date().toISOString(),
        outputSize: blobBuffer.length
      });

      // Send notification to user
      await publishEvent(
        EventType.NOTIFICATION_SENT,
        `exports/${exportJob.id}`,
        {
          exportJobId: exportJob.id,
          userId: exportJob.userId,
          fileName: blobName,
          size: blobBuffer.length,
          downloadUrl: `${process.env.AZURE_STORAGE_CONNECTION_STRING}/${process.env.EXPORTS_CONTAINER}/${blobName}`,
          timestamp: new Date().toISOString()
        }
      );

      logger.info('Export job completed', {
        exportJobId: exportJob.id,
        fileName: blobName
      });
    }

  } catch (error) {
    logger.error('Export blob trigger failed', {
      blobName,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Register blob triggers
app.storageBlob('documentBlobTrigger', {
  path: 'documents/{name}',
  connection: 'AzureWebJobsStorage',
  handler: documentBlobTrigger
});

app.storageBlob('templateBlobTrigger', {
  path: 'templates/{name}',
  connection: 'AzureWebJobsStorage',
  handler: templateBlobTrigger
});

app.storageBlob('exportBlobTrigger', {
  path: 'exports/{name}',
  connection: 'AzureWebJobsStorage',
  handler: exportBlobTrigger
});
