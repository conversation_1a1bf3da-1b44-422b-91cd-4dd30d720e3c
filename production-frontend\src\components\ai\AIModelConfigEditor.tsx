'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { JsonView } from '@/components/common/json-view';
import { Settings, Code } from 'lucide-react';

interface AIModelConfig {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  systemPrompt?: string;
  enableLogging?: boolean;
  enableCaching?: boolean;
  [key: string]: any;
}

interface AIModelConfigEditorProps {
  config: AIModelConfig;
  onChange: (config: AIModelConfig) => void;
  className?: string;
}

/**
 * Component for editing AI model configuration
 */
export function AIModelConfigEditor({
  config,
  onChange,
  className,
}: AIModelConfigEditorProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [stopSequence, setStopSequence] = useState('');

  // Update a specific config property
  const updateConfig = (key: string, value: any) => {
    onChange({
      ...config,
      [key]: value,
    });
  };

  // Add a stop sequence
  const addStopSequence = () => {
    if (!stopSequence.trim()) return;
    
    const currentStopSequences = config.stopSequences || [];
    if (!currentStopSequences.includes(stopSequence)) {
      updateConfig('stopSequences', [...currentStopSequences, stopSequence]);
      setStopSequence('');
    }
  };

  // Remove a stop sequence
  const removeStopSequence = (sequence: string) => {
    const currentStopSequences = config.stopSequences || [];
    updateConfig(
      'stopSequences',
      currentStopSequences.filter((s) => s !== sequence)
    );
  };

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="basic">Basic Settings</TabsTrigger>
          <TabsTrigger value="advanced">Advanced Settings</TabsTrigger>
          <TabsTrigger value="json">JSON Config</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="temperature">Temperature: {config.temperature?.toFixed(2) || '0.70'}</Label>
              </div>
              <Slider
                id="temperature"
                min={0}
                max={1}
                step={0.01}
                value={[config.temperature || 0.7]}
                onValueChange={(value) => updateConfig('temperature', value[0])}
              />
              <p className="text-xs text-muted-foreground">
                Controls randomness: Lower values are more deterministic, higher values are more creative.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTokens">Max Tokens</Label>
              <Input
                id="maxTokens"
                type="number"
                min={1}
                max={8192}
                value={config.maxTokens || 1024}
                onChange={(e) => updateConfig('maxTokens', parseInt(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Maximum number of tokens to generate. One token is roughly 4 characters.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="systemPrompt">System Prompt</Label>
              <Textarea
                id="systemPrompt"
                value={config.systemPrompt || ''}
                onChange={(e) => updateConfig('systemPrompt', e.target.value)}
                placeholder="You are a helpful assistant..."
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                Instructions that set the behavior of the AI model.
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="topP">Top P: {config.topP?.toFixed(2) || '0.95'}</Label>
              </div>
              <Slider
                id="topP"
                min={0}
                max={1}
                step={0.01}
                value={[config.topP || 0.95]}
                onValueChange={(value) => updateConfig('topP', value[0])}
              />
              <p className="text-xs text-muted-foreground">
                Controls diversity via nucleus sampling: 0.5 means half of all likelihood-weighted options are considered.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="frequencyPenalty">Frequency Penalty: {config.frequencyPenalty?.toFixed(2) || '0.00'}</Label>
              </div>
              <Slider
                id="frequencyPenalty"
                min={0}
                max={2}
                step={0.01}
                value={[config.frequencyPenalty || 0]}
                onValueChange={(value) => updateConfig('frequencyPenalty', value[0])}
              />
              <p className="text-xs text-muted-foreground">
                Reduces repetition by penalizing tokens that have already appeared in the text.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="presencePenalty">Presence Penalty: {config.presencePenalty?.toFixed(2) || '0.00'}</Label>
              </div>
              <Slider
                id="presencePenalty"
                min={0}
                max={2}
                step={0.01}
                value={[config.presencePenalty || 0]}
                onValueChange={(value) => updateConfig('presencePenalty', value[0])}
              />
              <p className="text-xs text-muted-foreground">
                Reduces repetition by penalizing tokens that have appeared at all, regardless of frequency.
              </p>
            </div>

            <div className="space-y-2">
              <Label>Stop Sequences</Label>
              <div className="flex space-x-2">
                <Input
                  value={stopSequence}
                  onChange={(e) => setStopSequence(e.target.value)}
                  placeholder="Enter a stop sequence"
                  onKeyDown={(e) => e.key === 'Enter' && addStopSequence()}
                />
                <button
                  onClick={addStopSequence}
                  className="px-3 py-2 bg-primary text-primary-foreground rounded-md"
                >
                  Add
                </button>
              </div>
              <div className="flex flex-wrap gap-2 mt-2">
                {(config.stopSequences || []).map((sequence, index) => (
                  <div
                    key={index}
                    className="flex items-center bg-muted px-2 py-1 rounded-md text-sm"
                  >
                    <span className="mr-1">{sequence}</span>
                    <button
                      onClick={() => removeStopSequence(sequence)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                Sequences where the API will stop generating further tokens.
              </p>
            </div>

            <div className="flex items-center space-x-2 pt-2">
              <Switch
                id="enableLogging"
                checked={config.enableLogging || false}
                onCheckedChange={(checked) => updateConfig('enableLogging', checked)}
              />
              <Label htmlFor="enableLogging">Enable Logging</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="enableCaching"
                checked={config.enableCaching || false}
                onCheckedChange={(checked) => updateConfig('enableCaching', checked)}
              />
              <Label htmlFor="enableCaching">Enable Response Caching</Label>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="json">
          <Card>
            <CardContent className="pt-6">
              <JsonView
                data={config}
                title="Model Configuration"
                collapsible={false}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
