"use client";

import { useState } from "react";
import Link from "next/link";
import { useOrganizations } from "@/hooks/organizations";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Building2, Plus, Search } from "lucide-react";
import { OrganizationCard } from "@/components/organizations/organization-card";
import { EmptyState } from "@/components/empty-state";
import { OrganizationTier } from "@/types/organization";

export default function OrganizationsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<OrganizationTier | "ALL">("ALL");

  const {
    organizations,
    isLoading
  } = useOrganizations();

  // Filter organizations based on search query and active tab
  const filteredOrganizations = organizations.filter((org: any) => {
    const matchesSearch = org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (org.description && org.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesTab = activeTab === "ALL" || org.tier === activeTab;

    return matchesSearch && matchesTab;
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Organizations</h1>
          <p className="text-muted-foreground">
            Manage your organizations and their projects
          </p>
        </div>
        <Button asChild>
          <Link href="/organizations/create">
            <Plus className="mr-2 h-4 w-4" />
            Create Organization
          </Link>
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search organizations..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button asChild>
          <Link href="/organizations/create">
            Create Organization
          </Link>
        </Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      ) : filteredOrganizations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredOrganizations.map((organization: any) => (
            <OrganizationCard
              key={organization.id}
              organization={organization}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          icon={<Building2 className="h-10 w-10 text-muted-foreground" />}
          title={searchQuery ? "No organizations found" : "No organizations"}
          description={
            searchQuery
              ? `No organizations match "${searchQuery}"`
              : "You don't have any organizations yet. Create your first organization to get started."
          }
          action={
            <Button asChild>
              <Link href="/organizations/create">
                <Plus className="mr-2 h-4 w-4" />
                Create Organization
              </Link>
            </Button>
          }
        />
      )}
    </div>
  );
}
