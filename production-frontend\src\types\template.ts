/**
 * Template System Types
 */

import type { ID, Timestamp, URL, User, Organization } from './index'

// Core template interface
export interface Template {
  id: ID
  name: string
  description?: string
  category: TemplateCategory
  type: TemplateType
  status: TemplateStatus
  version: number
  versions: TemplateVersion[]
  fields: TemplateField[]
  layout: TemplateLayout
  styling: TemplateStyling
  validation: TemplateValidation
  metadata: TemplateMetadata
  tags: string[]
  isPublic: boolean
  isPremium: boolean
  organizationId: ID
  organization: Organization
  createdBy: ID
  creator: User
  permissions: TemplatePermission[]
  sharing: TemplateSharing
  usage: TemplateUsage
  createdAt: Timestamp
  updatedAt: Timestamp
  publishedAt?: Timestamp

  // Additional properties expected by components
  content?: string
  projectId?: string
  isDefault?: boolean
}

// Template types
export type TemplateType =
  | 'form'
  | 'document'
  | 'report'
  | 'contract'
  | 'invoice'
  | 'proposal'
  | 'workflow'
  | 'email'
  | 'presentation'
  | 'project'
  | 'custom'

// Template type enum for compatibility with components
export const TemplateType = {
  FORM: 'form' as const,
  DOCUMENT: 'document' as const,
  REPORT: 'report' as const,
  CONTRACT: 'contract' as const,
  INVOICE: 'invoice' as const,
  PROPOSAL: 'proposal' as const,
  WORKFLOW: 'workflow' as const,
  EMAIL: 'email' as const,
  PRESENTATION: 'presentation' as const,
  PROJECT: 'project' as const,
  CUSTOM: 'custom' as const,
} as const

// Template status
export type TemplateStatus =
  | 'draft'
  | 'review'
  | 'published'
  | 'archived'
  | 'deprecated'

// Template status enum for compatibility with components
export const TemplateStatus = {
  DRAFT: 'draft' as const,
  REVIEW: 'review' as const,
  PUBLISHED: 'published' as const,
  ARCHIVED: 'archived' as const,
  DEPRECATED: 'deprecated' as const,
} as const

// Template category
export interface TemplateCategory {
  id: ID
  name: string
  description?: string
  icon?: string
  color?: string
  parentId?: ID
  children?: TemplateCategory[]
  templateCount: number
}

// Template version
export interface TemplateVersion {
  id: ID
  version: number
  name?: string
  description?: string
  changes: string[]
  fields: TemplateField[]
  layout: TemplateLayout
  createdBy: ID
  createdAt: Timestamp
  isActive: boolean
}

// Template field
export interface TemplateField {
  id: string
  name: string
  label: string
  type: TemplateFieldType
  required: boolean
  readonly: boolean
  hidden: boolean
  order: number
  group?: string
  description?: string
  placeholder?: string
  defaultValue?: any
  options?: TemplateFieldOption[]
  validation: TemplateFieldValidation
  formatting: TemplateFieldFormatting
  conditional: TemplateFieldConditional
  position?: TemplateFieldPosition
  styling?: TemplateFieldStyling
  metadata?: Record<string, any>
}

// Template field types
export type TemplateFieldType = 
  | 'text'
  | 'textarea'
  | 'email'
  | 'password'
  | 'number'
  | 'decimal'
  | 'currency'
  | 'percentage'
  | 'date'
  | 'datetime'
  | 'time'
  | 'boolean'
  | 'checkbox'
  | 'radio'
  | 'select'
  | 'multiselect'
  | 'file'
  | 'image'
  | 'richtext'
  | 'signature'
  | 'rating'
  | 'slider'
  | 'color'
  | 'url'
  | 'phone'
  | 'address'
  | 'location'
  | 'table'
  | 'repeater'
  | 'section'
  | 'divider'
  | 'html'
  | 'calculation'

// Template field option
export interface TemplateFieldOption {
  label: string
  value: any
  description?: string
  icon?: string
  color?: string
  disabled?: boolean
  group?: string
}

// Template field validation
export interface TemplateFieldValidation {
  required?: boolean
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  pattern?: string
  email?: boolean
  url?: boolean
  phone?: boolean
  custom?: string // Custom validation function
  message?: string
  rules?: TemplateValidationRule[]
}

export interface TemplateValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom'
  value?: any
  message: string
  condition?: string // When to apply this rule
}

// Template field formatting
export interface TemplateFieldFormatting {
  mask?: string
  prefix?: string
  suffix?: string
  thousandsSeparator?: string
  decimalSeparator?: string
  decimalPlaces?: number
  dateFormat?: string
  timeFormat?: string
  currency?: string
  locale?: string
}

// Template field conditional logic
export interface TemplateFieldConditional {
  show?: TemplateCondition[]
  hide?: TemplateCondition[]
  require?: TemplateCondition[]
  readonly?: TemplateCondition[]
  calculate?: TemplateCalculation
}

export interface TemplateCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater' | 'less' | 'greater_equal' | 'less_equal' | 'empty' | 'not_empty'
  value?: any
  logic?: 'and' | 'or'
}

export interface TemplateCalculation {
  formula: string
  fields: string[]
  format?: TemplateFieldFormatting
}

// Template field position (for visual templates)
export interface TemplateFieldPosition {
  page?: number
  x: number
  y: number
  width: number
  height: number
  zIndex?: number
}

// Template field styling
export interface TemplateFieldStyling {
  fontSize?: number
  fontFamily?: string
  fontWeight?: string
  color?: string
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  borderRadius?: number
  padding?: number
  margin?: number
  textAlign?: 'left' | 'center' | 'right' | 'justify'
  className?: string
}

// Template layout
export interface TemplateLayout {
  type: 'form' | 'document' | 'grid' | 'custom'
  columns?: number
  rows?: number
  width?: number
  height?: number
  orientation?: 'portrait' | 'landscape'
  margins?: {
    top: number
    right: number
    bottom: number
    left: number
  }
  spacing?: {
    horizontal: number
    vertical: number
  }
  sections?: TemplateSection[]
  pages?: TemplatePage[]
}

export interface TemplateSection {
  id: string
  name: string
  title?: string
  description?: string
  collapsible?: boolean
  collapsed?: boolean
  fields: string[]
  styling?: TemplateFieldStyling
  conditional?: TemplateFieldConditional
}

export interface TemplatePage {
  id: string
  name: string
  title?: string
  width: number
  height: number
  margins: {
    top: number
    right: number
    bottom: number
    left: number
  }
  fields: string[]
  background?: {
    color?: string
    image?: URL
  }
}

// Template styling
export interface TemplateStyling {
  theme: string
  primaryColor: string
  secondaryColor: string
  fontFamily: string
  fontSize: number
  customCSS?: string
  logo?: URL
  header?: TemplateHeader
  footer?: TemplateFooter
}

export interface TemplateHeader {
  enabled: boolean
  content: string
  height: number
  styling?: TemplateFieldStyling
}

export interface TemplateFooter {
  enabled: boolean
  content: string
  height: number
  styling?: TemplateFieldStyling
}

// Template validation
export interface TemplateValidation {
  enabled: boolean
  rules: TemplateValidationRule[]
  customValidation?: string
  errorMessages: Record<string, string>
}

// Template metadata
export interface TemplateMetadata {
  author?: string
  company?: string
  department?: string
  purpose?: string
  audience?: string
  language?: string
  keywords?: string[]
  estimatedTime?: number // minutes to complete
  difficulty?: 'easy' | 'medium' | 'hard'
  customFields: Record<string, any>
}

// Template permissions
export interface TemplatePermission {
  userId: ID
  user: User
  permission: 'view' | 'use' | 'edit' | 'admin'
  grantedBy: ID
  grantedAt: Timestamp
  expiresAt?: Timestamp
}

// Template sharing
export interface TemplateSharing {
  isPublic: boolean
  publicUrl?: URL
  allowCopy: boolean
  allowModification: boolean
  requireAttribution: boolean
  shareLinks: TemplateShareLink[]
  embeddable: boolean
  embedCode?: string
}

export interface TemplateShareLink {
  id: ID
  url: URL
  permissions: string[]
  createdBy: ID
  createdAt: Timestamp
  expiresAt?: Timestamp
  usageCount: number
  lastUsed?: Timestamp
}

// Template usage statistics
export interface TemplateUsage {
  totalUses: number
  uniqueUsers: number
  averageCompletionTime: number
  completionRate: number
  lastUsed?: Timestamp
  popularFields: {
    fieldId: string
    fieldName: string
    usageCount: number
  }[]
  userFeedback: TemplateUserFeedback[]
}

export interface TemplateUserFeedback {
  id: ID
  userId: ID
  user: User
  rating: number
  comment?: string
  suggestions?: string[]
  createdAt: Timestamp
}

// Template operations
export interface CreateTemplateRequest {
  name: string
  description?: string
  categoryId: ID
  type: TemplateType
  fields: Omit<TemplateField, 'id'>[]
  layout?: TemplateLayout
  styling?: TemplateStyling
  isPublic?: boolean
  tags?: string[]
  metadata?: Partial<TemplateMetadata>
}

export interface UpdateTemplateRequest {
  name?: string
  description?: string
  categoryId?: ID
  fields?: TemplateField[]
  layout?: TemplateLayout
  styling?: TemplateStyling
  isPublic?: boolean
  tags?: string[]
  metadata?: Partial<TemplateMetadata>
}

export interface TemplateSearchQuery {
  query?: string
  categoryId?: ID
  type?: TemplateType[]
  status?: TemplateStatus[]
  isPublic?: boolean
  isPremium?: boolean
  organizationId?: ID
  createdBy?: ID
  tags?: string[]
  dateRange?: {
    start: Timestamp
    end: Timestamp
  }
  minRating?: number
  hasFields?: string[]
}

export interface TemplateSearchResult {
  templates: Template[]
  total: number
  facets: {
    categories: { categoryId: ID; categoryName: string; count: number }[]
    types: { type: TemplateType; count: number }[]
    tags: { tag: string; count: number }[]
    organizations: { organizationId: ID; organizationName: string; count: number }[]
  }
  suggestions: string[]
}

// Template instance (filled template)
export interface TemplateInstance {
  id: ID
  templateId: ID
  template: Template
  name: string
  status: 'draft' | 'in_progress' | 'completed' | 'submitted' | 'approved' | 'rejected'
  data: Record<string, any>
  validation: TemplateInstanceValidation
  metadata: TemplateInstanceMetadata
  assignedTo?: ID[]
  assignedBy?: ID
  dueDate?: Timestamp
  submittedAt?: Timestamp
  approvedAt?: Timestamp
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface TemplateInstanceValidation {
  isValid: boolean
  errors: {
    fieldId: string
    message: string
  }[]
  warnings: {
    fieldId: string
    message: string
  }[]
}

export interface TemplateInstanceMetadata {
  source?: string
  context?: Record<string, any>
  workflow?: {
    stepId: string
    stepName: string
  }
  integration?: {
    type: string
    externalId: string
  }
}

// Template workflow integration
export interface TemplateWorkflow {
  id: ID
  templateId: ID
  workflowId: ID
  stepId: string
  autoSubmit: boolean
  approvalRequired: boolean
  approvers: ID[]
  notifications: TemplateWorkflowNotification[]
  conditions: TemplateCondition[]
}

export interface TemplateWorkflowNotification {
  event: 'created' | 'submitted' | 'approved' | 'rejected' | 'overdue'
  recipients: ID[]
  template: string
  delay?: number // minutes
}

// Template analytics
export interface TemplateAnalytics {
  overview: TemplateAnalyticsOverview
  usage: TemplateAnalyticsUsage
  performance: TemplateAnalyticsPerformance
  feedback: TemplateAnalyticsFeedback
}

export interface TemplateAnalyticsOverview {
  totalInstances: number
  completedInstances: number
  averageCompletionTime: number
  completionRate: number
  popularityRank: number
  trendDirection: 'up' | 'down' | 'stable'
}

export interface TemplateAnalyticsUsage {
  daily: { date: string; count: number }[]
  byUser: { userId: ID; userName: string; count: number }[]
  byOrganization: { organizationId: ID; organizationName: string; count: number }[]
  bySource: { source: string; count: number }[]
}

export interface TemplateAnalyticsPerformance {
  fieldCompletionRates: { fieldId: string; fieldName: string; rate: number }[]
  abandonmentPoints: { fieldId: string; fieldName: string; rate: number }[]
  timeSpentByField: { fieldId: string; fieldName: string; averageTime: number }[]
  errorRates: { fieldId: string; fieldName: string; rate: number }[]
}

export interface TemplateAnalyticsFeedback {
  averageRating: number
  ratingDistribution: { rating: number; count: number }[]
  commonSuggestions: { suggestion: string; count: number }[]
  satisfactionScore: number
}
