/**
 * Unified Notification Management Function
 * Consolidates all notification CRUD operations, preferences, and delivery tracking
 * Replaces: notification-send.ts, notification-list.ts, notification-mark-read.ts, 
 *          notification-preferences-management.ts, notification-tracking.ts
 * 
 * Production-ready implementation with comprehensive error handling,
 * caching, and Azure best practices
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { cacheManager } from '../shared/services/cache-manager';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
// import { eventGridIntegration } from '../shared/services/event-grid-integration'; // TODO: Implement EventGrid integration
import { signalRService } from '../shared/services/signalr';

// Unified notification types and enums
enum NotificationType {
  DOCUMENT_UPLOADED = 'DOCUMENT_UPLOADED',
  DOCUMENT_PROCESSED = 'DOCUMENT_PROCESSED',
  DOCUMENT_SHARED = 'DOCUMENT_SHARED',
  WORKFLOW_ASSIGNED = 'WORKFLOW_ASSIGNED',
  WORKFLOW_COMPLETED = 'WORKFLOW_COMPLETED',
  WORKFLOW_OVERDUE = 'WORKFLOW_OVERDUE',
  COMMENT_ADDED = 'COMMENT_ADDED',
  MENTION = 'MENTION',
  PROJECT_INVITATION = 'PROJECT_INVITATION',
  ORGANIZATION_INVITATION = 'ORGANIZATION_INVITATION',
  SECURITY_ALERT = 'SECURITY_ALERT',
  SYSTEM_UPDATE = 'SYSTEM_UPDATE',
  COLLABORATION_STARTED = 'COLLABORATION_STARTED',
  DOCUMENT_PROCESSING_FAILED = 'DOCUMENT_PROCESSING_FAILED'
}

enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

enum NotificationChannel {
  EMAIL = 'email',
  IN_APP = 'in_app',
  PUSH = 'push',
  SMS = 'sms'
}

enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  NEVER = 'never'
}

// Comprehensive interfaces
interface NotificationPreferences {
  id: string;
  userId: string;
  channels: {
    email: boolean;
    in_app: boolean;
    push: boolean;
    sms: boolean;
  };
  types: {
    [key in NotificationType]: {
      email: boolean;
      in_app: boolean;
      push: boolean;
      sms: boolean;
      enabled: boolean;
    };
  };
  frequency: {
    digest: NotificationFrequency;
    summary: NotificationFrequency;
    reminders: NotificationFrequency;
  };
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
    timezone: string;
    weekendsOnly: boolean;
  };
  delivery: {
    maxEmailsPerHour: number;
    maxPushPerHour: number;
    batchSimilarNotifications: boolean;
    priorityOverride: boolean;
  };
  categories: {
    documents: boolean;
    workflows: boolean;
    projects: boolean;
    collaboration: boolean;
    system: boolean;
    security: boolean;
  };
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  recipientId: string;
  senderId?: string;
  organizationId?: string;
  projectId?: string;
  documentId?: string;
  workflowId?: string;
  priority: NotificationPriority;
  channels: NotificationChannel[];
  isRead: boolean;
  readAt?: string;
  actionUrl?: string;
  actionText?: string;
  metadata?: any;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  deliveryResults?: DeliveryResult[];
  deliveredAt?: string;
}

interface DeliveryResult {
  channel: NotificationChannel;
  success: boolean;
  deliveredAt?: string;
  error?: string;
  messageId?: string;
  provider?: string;
}

// Validation schemas
const sendNotificationSchema = Joi.object({
  recipientIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
  type: Joi.string().valid(...Object.values(NotificationType)).required(),
  title: Joi.string().required().max(200),
  message: Joi.string().required().max(1000),
  priority: Joi.string().valid(...Object.values(NotificationPriority)).default(NotificationPriority.MEDIUM),
  channels: Joi.array().items(Joi.string().valid(...Object.values(NotificationChannel))).optional(),
  actionUrl: Joi.string().uri().optional(),
  actionText: Joi.string().max(50).optional(),
  metadata: Joi.object().optional(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  documentId: Joi.string().uuid().optional(),
  workflowId: Joi.string().uuid().optional(),
  expiresAt: Joi.date().iso().optional()
});

const listNotificationsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  isRead: Joi.boolean().optional(),
  type: Joi.string().valid(...Object.values(NotificationType)).optional(),
  priority: Joi.string().valid(...Object.values(NotificationPriority)).optional(),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional(),
  includeExpired: Joi.boolean().default(false)
});

const markReadSchema = Joi.object({
  notificationIds: Joi.array().items(Joi.string().uuid()).min(1).optional(),
  markAll: Joi.boolean().default(false),
  isRead: Joi.boolean().default(true),
  organizationId: Joi.string().uuid().optional(),
  projectId: Joi.string().uuid().optional()
}).or('notificationIds', 'markAll');

const updatePreferencesSchema = Joi.object({
  channels: Joi.object({
    email: Joi.boolean(),
    in_app: Joi.boolean(),
    push: Joi.boolean(),
    sms: Joi.boolean()
  }).optional(),
  types: Joi.object().pattern(
    Joi.string().valid(...Object.values(NotificationType)),
    Joi.object({
      email: Joi.boolean(),
      in_app: Joi.boolean(),
      push: Joi.boolean(),
      sms: Joi.boolean(),
      enabled: Joi.boolean()
    })
  ).optional(),
  frequency: Joi.object({
    digest: Joi.string().valid(...Object.values(NotificationFrequency)),
    summary: Joi.string().valid(...Object.values(NotificationFrequency)),
    reminders: Joi.string().valid(...Object.values(NotificationFrequency))
  }).optional(),
  quietHours: Joi.object({
    enabled: Joi.boolean(),
    start: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    end: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    timezone: Joi.string(),
    weekendsOnly: Joi.boolean()
  }).optional(),
  delivery: Joi.object({
    maxEmailsPerHour: Joi.number().integer().min(1).max(100),
    maxPushPerHour: Joi.number().integer().min(1).max(200),
    batchSimilarNotifications: Joi.boolean(),
    priorityOverride: Joi.boolean()
  }).optional(),
  categories: Joi.object({
    documents: Joi.boolean(),
    workflows: Joi.boolean(),
    projects: Joi.boolean(),
    collaboration: Joi.boolean(),
    system: Joi.boolean(),
    security: Joi.boolean()
  }).optional()
});

const trackEngagementSchema = Joi.object({
  notificationId: Joi.string().uuid().required(),
  action: Joi.string().valid('opened', 'clicked', 'dismissed', 'expired').required(),
  channel: Joi.string().valid(...Object.values(NotificationChannel)).optional(),
  metadata: Joi.object().optional()
});

/**
 * Unified Notification Management Class
 * Handles all notification operations with comprehensive error handling and caching
 */
class UnifiedNotificationManager {
  private serviceBusService: ServiceBusEnhancedService;
  private signalRServiceInstance: signalRService;
  private isInitialized = false;

  constructor() {
    this.serviceBusService = new ServiceBusEnhancedService();
    this.signalRServiceInstance = signalRService.getInstance();
  }

  /**
   * Initialize all services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.serviceBusService.initialize();
      this.isInitialized = true;
      logger.info('UnifiedNotificationManager initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize UnifiedNotificationManager', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Ensure services are initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }
  
  /**
   * Send notifications to multiple recipients
   */
  async sendNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    // Ensure services are initialized
    await this.ensureInitialized();

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = sendNotificationSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const {
        recipientIds,
        type,
        title,
        message,
        priority,
        channels,
        actionUrl,
        actionText,
        metadata,
        organizationId,
        projectId,
        documentId,
        workflowId,
        expiresAt
      } = value;

      const notificationIds: string[] = [];
      const results: any[] = [];

      // Process each recipient
      for (const recipientId of recipientIds) {
        try {
          // Get user preferences
          const preferences = await this.getUserNotificationPreferences(recipientId);
          
          // Check if notification should be sent based on preferences
          if (!this.shouldSendNotification(type, preferences)) {
            results.push({
              recipientId,
              success: false,
              skipped: true,
              reason: 'user_preferences'
            });
            continue;
          }

          // Create notification record
          const notification = await this.createNotificationRecord({
            type,
            title,
            message,
            recipientId,
            senderId: user.id,
            organizationId,
            projectId,
            documentId,
            workflowId,
            priority,
            channels: channels || this.getDefaultChannels(type, preferences),
            actionUrl,
            actionText,
            metadata,
            expiresAt,
            tenantId: user.tenantId
          });

          notificationIds.push(notification.id);

          // Queue for delivery via Service Bus
          await this.queueNotificationForDelivery(notification, correlationId);

          results.push({
            recipientId,
            notificationId: notification.id,
            success: true
          });

        } catch (error) {
          logger.error('Failed to process notification for recipient', {
            recipientId,
            error: error instanceof Error ? error.message : String(error),
            correlationId
          });

          results.push({
            recipientId,
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Log activity
      await this.logNotificationActivity(user.id, 'notifications_sent', {
        type,
        recipientCount: recipientIds.length,
        successCount: results.filter(r => r.success).length,
        organizationId,
        projectId
      });

      logger.info('Notifications sent successfully', {
        correlationId,
        userId: user.id,
        type,
        recipientCount: recipientIds.length,
        successCount: results.filter(r => r.success).length,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          notificationIds,
          results,
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Send notification failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * List notifications for a user with filtering and pagination
   */
  async listNotifications(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Parse query parameters
      const url = new URL(request.url);
      const queryParams: any = Object.fromEntries(url.searchParams.entries());

      // Convert string values to appropriate types
      if (queryParams.page) queryParams.page = parseInt(queryParams.page);
      if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);
      if (queryParams.isRead) queryParams.isRead = queryParams.isRead === 'true';
      if (queryParams.includeExpired) queryParams.includeExpired = queryParams.includeExpired === 'true';

      const { error, value } = listNotificationsSchema.validate(queryParams);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const { page, limit, isRead, type, priority, organizationId, projectId, includeExpired } = value;

      // Check cache first using cache manager
      const cacheKey = `notifications:${user.id}:${JSON.stringify(value)}`;
      const cached = await cacheManager.get(cacheKey, { namespace: 'notifications' });
      if (cached) {
        return addCorsHeaders({
          status: 200,
          jsonBody: cached
        }, request);
      }

      // Build query
      let queryText = 'SELECT * FROM c WHERE c.recipientId = @userId';
      const parameters: any[] = [{ name: '@userId', value: user.id }];

      // Add tenant isolation
      if (user.tenantId) {
        queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
        parameters.push({ name: '@tenantId', value: user.tenantId });
      }

      // Add filters
      if (isRead !== undefined) {
        queryText += ' AND c.isRead = @isRead';
        parameters.push({ name: '@isRead', value: isRead });
      }

      if (type) {
        queryText += ' AND c.type = @type';
        parameters.push({ name: '@type', value: type });
      }

      if (priority) {
        queryText += ' AND c.priority = @priority';
        parameters.push({ name: '@priority', value: priority });
      }

      if (organizationId) {
        queryText += ' AND c.organizationId = @organizationId';
        parameters.push({ name: '@organizationId', value: organizationId });
      }

      if (projectId) {
        queryText += ' AND c.projectId = @projectId';
        parameters.push({ name: '@projectId', value: projectId });
      }

      if (!includeExpired) {
        queryText += ' AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
        parameters.push({ name: '@now', value: new Date().toISOString() });
      }

      // Add ordering and pagination
      queryText += ' ORDER BY c.createdAt DESC';
      queryText += ` OFFSET ${(page - 1) * limit} LIMIT ${limit}`;

      // Execute query
      const notifications = await db.queryItems<Notification>('notifications', queryText, parameters);

      // Get total count for pagination
      const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)').split(' ORDER BY')[0];
      const totalCountResult = await db.queryItems<number>('notifications', countQuery, parameters);
      const totalCount = totalCountResult[0] || 0;

      // Calculate unread count
      const unreadCountQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)')
        .replace('c.isRead = @isRead', 'c.isRead = false')
        .split(' ORDER BY')[0];
      const unreadCountResult = await db.queryItems<number>('notifications', unreadCountQuery,
        parameters.filter(p => p.name !== '@isRead'));
      const unreadCount = unreadCountResult[0] || 0;

      const result = {
        notifications,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        },
        summary: {
          unreadCount,
          totalCount
        }
      };

      // Cache result for 1 minute using cache manager
      await cacheManager.set(cacheKey, result, {
        ttl: 60,
        tags: [`user:${user.id}`, `notifications`],
        namespace: 'notifications'
      });

      logger.info('Notifications listed successfully', {
        correlationId,
        userId: user.id,
        page,
        limit,
        totalCount,
        unreadCount
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: result
      }, request);

    } catch (error) {
      logger.error('List notifications failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Mark notifications as read/unread
   */
  async markNotificationsRead(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = markReadSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const { notificationIds, markAll, isRead, organizationId, projectId } = value;

      let updatedCount = 0;
      let notificationsToUpdate: any[] = [];

      if (markAll) {
        // Mark all notifications as read for the user
        let queryText = 'SELECT * FROM c WHERE c.recipientId = @userId AND c.isRead != @isRead';
        const parameters: any[] = [
          { name: '@userId', value: user.id },
          { name: '@isRead', value: isRead }
        ];

        // Add tenant isolation
        if (user.tenantId) {
          queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
          parameters.push({ name: '@tenantId', value: user.tenantId });
        }

        // Filter by organization if provided
        if (organizationId) {
          queryText += ' AND c.organizationId = @organizationId';
          parameters.push({ name: '@organizationId', value: organizationId });
        }

        // Filter by project if provided
        if (projectId) {
          queryText += ' AND c.projectId = @projectId';
          parameters.push({ name: '@projectId', value: projectId });
        }

        notificationsToUpdate = await db.queryItems<any>('notifications', queryText, parameters);
      } else {
        // Mark specific notifications
        for (const notificationId of notificationIds!) {
          const notification = await db.readItem('notifications', notificationId, user.tenantId || 'default');
          if (notification && notification.recipientId === user.id) {
            notificationsToUpdate.push(notification);
          }
        }
      }

      // Update notifications
      for (const notification of notificationsToUpdate) {
        const updatedNotification = {
          ...notification,
          isRead,
          readAt: isRead ? new Date().toISOString() : undefined,
          updatedAt: new Date().toISOString()
        };

        await db.updateItem('notifications', updatedNotification);
        updatedCount++;

        // Send real-time update
        await this.signalRServiceInstance.sendToUser(user.id, {
          target: 'notificationUpdated',
          arguments: [{
            notificationId: notification.id,
            isRead,
            readAt: updatedNotification.readAt
          }]
        });
      }

      // Invalidate cache
      await this.invalidateNotificationCache(user.id);

      // Log activity
      if (updatedCount > 0) {
        await this.logNotificationActivity(user.id, 'notifications_marked_read', {
          updatedCount,
          isRead,
          markAll,
          organizationId,
          projectId
        });
      }

      logger.info('Notifications marked read successfully', {
        correlationId,
        userId: user.id,
        updatedCount,
        isRead,
        markAll
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          updatedCount,
          isRead
        }
      }, request);

    } catch (error) {
      logger.error('Mark notifications read failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Helper Methods
   */
  private async getUserNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    try {
      // Use cache-aside pattern with cache manager
      const cacheKey = `notification-preferences:${userId}`;

      return await cacheManager.getOrSet(
        cacheKey,
        async () => {
          // Load from database
          const preferences = await db.readItem('notification-preferences', userId, userId);
          if (preferences) {
            return preferences as NotificationPreferences;
          }

          // Create and return default preferences
          const defaultPreferences = this.getDefaultNotificationPreferences(userId);
          await db.createItem('notification-preferences', defaultPreferences);
          return defaultPreferences;
        },
        {
          ttl: 3600, // 1 hour
          tags: [`user:${userId}`, `preferences`],
          namespace: 'notifications'
        }
      );
    } catch (error) {
      logger.error('Error getting user notification preferences', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return this.getDefaultNotificationPreferences(userId);
    }
  }

  private shouldSendNotification(type: NotificationType, preferences: NotificationPreferences): boolean {
    // Check if notification type is enabled
    if (!preferences.types[type]?.enabled) {
      return false;
    }

    // Check category preferences
    const categoryMap: Record<string, keyof NotificationPreferences['categories']> = {
      [NotificationType.DOCUMENT_UPLOADED]: 'documents',
      [NotificationType.DOCUMENT_PROCESSED]: 'documents',
      [NotificationType.DOCUMENT_SHARED]: 'documents',
      [NotificationType.WORKFLOW_ASSIGNED]: 'workflows',
      [NotificationType.WORKFLOW_COMPLETED]: 'workflows',
      [NotificationType.COMMENT_ADDED]: 'collaboration',
      [NotificationType.MENTION]: 'collaboration',
      [NotificationType.PROJECT_INVITATION]: 'projects',
      [NotificationType.SECURITY_ALERT]: 'security',
      [NotificationType.SYSTEM_UPDATE]: 'system'
    };

    const category = categoryMap[type];
    if (category && !preferences.categories[category]) {
      return false;
    }

    return true;
  }

  private getDefaultChannels(type: NotificationType, preferences: NotificationPreferences): NotificationChannel[] {
    const channels: NotificationChannel[] = [];
    const typePrefs = preferences.types[type];

    if (typePrefs?.in_app && preferences.channels.in_app) {
      channels.push(NotificationChannel.IN_APP);
    }
    if (typePrefs?.email && preferences.channels.email) {
      channels.push(NotificationChannel.EMAIL);
    }
    if (typePrefs?.push && preferences.channels.push) {
      channels.push(NotificationChannel.PUSH);
    }
    if (typePrefs?.sms && preferences.channels.sms) {
      channels.push(NotificationChannel.SMS);
    }

    return channels.length > 0 ? channels : [NotificationChannel.IN_APP];
  }

  private async createNotificationRecord(data: any): Promise<Notification> {
    const notification: Notification = {
      id: uuidv4(),
      type: data.type,
      title: data.title,
      message: data.message,
      recipientId: data.recipientId,
      senderId: data.senderId,
      organizationId: data.organizationId,
      projectId: data.projectId,
      documentId: data.documentId,
      workflowId: data.workflowId,
      priority: data.priority,
      channels: data.channels,
      isRead: false,
      actionUrl: data.actionUrl,
      actionText: data.actionText,
      metadata: data.metadata,
      expiresAt: data.expiresAt,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tenantId: data.tenantId
    };

    await db.createItem('notifications', notification);
    return notification;
  }

  private async queueNotificationForDelivery(notification: Notification, correlationId: string): Promise<void> {
    const success = await this.serviceBusService.sendToQueue('notification-delivery', {
      body: {
        notificationId: notification.id,
        recipientId: notification.recipientId,
        organizationId: notification.organizationId,
        channels: notification.channels,
        priority: notification.priority.toLowerCase(),
        content: {
          title: notification.title,
          message: notification.message,
          actionUrl: notification.actionUrl,
          actionText: notification.actionText,
          data: notification.metadata
        }
      },
      correlationId,
      messageId: `notification-${notification.id}-${Date.now()}`
    });

    if (!success) {
      logger.error('Failed to queue notification for delivery', {
        notificationId: notification.id,
        recipientId: notification.recipientId
      });
      throw new Error('Failed to queue notification for delivery');
    }
  }

  private async invalidateNotificationCache(userId: string): Promise<void> {
    try {
      // Invalidate all notification-related cache entries for the user
      await cacheManager.invalidateByTags([`user:${userId}`, `notifications`]);

      logger.debug('Notification cache invalidated', { userId });
    } catch (error) {
      logger.error('Error invalidating notification cache', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async logNotificationActivity(userId: string, activity: string, details: any): Promise<void> {
    try {
      await db.createItem('activities', {
        id: uuidv4(),
        type: activity,
        userId,
        organizationId: details.organizationId,
        projectId: details.projectId,
        timestamp: new Date().toISOString(),
        details,
        tenantId: userId
      });
    } catch (error) {
      logger.error('Error logging notification activity', {
        userId,
        activity,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private getDefaultNotificationPreferences(userId: string): NotificationPreferences {
    const now = new Date().toISOString();

    return {
      id: userId,
      userId,
      channels: {
        email: true,
        in_app: true,
        push: true,
        sms: false
      },
      types: Object.values(NotificationType).reduce((acc, type) => {
        acc[type] = {
          email: this.getDefaultChannelSetting(type, 'email'),
          in_app: this.getDefaultChannelSetting(type, 'in_app'),
          push: this.getDefaultChannelSetting(type, 'push'),
          sms: this.getDefaultChannelSetting(type, 'sms'),
          enabled: true
        };
        return acc;
      }, {} as any),
      frequency: {
        digest: NotificationFrequency.DAILY,
        summary: NotificationFrequency.WEEKLY,
        reminders: NotificationFrequency.IMMEDIATE
      },
      quietHours: {
        enabled: false,
        start: "22:00",
        end: "08:00",
        timezone: "UTC",
        weekendsOnly: false
      },
      delivery: {
        maxEmailsPerHour: 10,
        maxPushPerHour: 20,
        batchSimilarNotifications: true,
        priorityOverride: true
      },
      categories: {
        documents: true,
        workflows: true,
        projects: true,
        collaboration: true,
        system: true,
        security: true
      },
      createdAt: now,
      updatedAt: now,
      tenantId: userId
    };
  }

  private getDefaultChannelSetting(type: NotificationType, channel: string): boolean {
    const highPriorityTypes = [
      NotificationType.WORKFLOW_ASSIGNED,
      NotificationType.WORKFLOW_OVERDUE,
      NotificationType.DOCUMENT_PROCESSING_FAILED,
      NotificationType.SECURITY_ALERT,
      NotificationType.MENTION
    ];

    // Email-only notification types for future use
    // const emailOnlyTypes = [
    //   NotificationType.DOCUMENT_PROCESSED,
    //   NotificationType.SYSTEM_UPDATE
    // ];

    const inAppOnlyTypes = [
      NotificationType.DOCUMENT_UPLOADED,
      NotificationType.COLLABORATION_STARTED
    ];

    if (channel === 'email') {
      return !inAppOnlyTypes.includes(type);
    }

    if (channel === 'in_app') {
      return true; // All notifications should be available in-app
    }

    if (channel === 'push') {
      return highPriorityTypes.includes(type);
    }

    if (channel === 'sms') {
      return type === NotificationType.SECURITY_ALERT;
    }

    return false;
  }
}

// Create instance of the manager
const notificationManager = new UnifiedNotificationManager();

// Initialize the manager on startup
notificationManager.initialize().catch(error => {
  logger.error('Failed to initialize notification manager', { error });
});

// SignalR service is initialized centrally in index.ts
// No need for redundant initialization here

/**
 * Notification Preferences Management
 */
async function manageNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const method = request.method.toUpperCase();

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    if (method === 'GET') {
      // Get user preferences
      const preferences = await notificationManager['getUserNotificationPreferences'](user.id);

      return addCorsHeaders({
        status: 200,
        jsonBody: { preferences }
      }, request);

    } else if (method === 'PUT' || method === 'PATCH') {
      // Update user preferences
      const body = await request.json();
      const { error, value } = updatePreferencesSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Get current preferences
      const currentPreferences = await notificationManager['getUserNotificationPreferences'](user.id);

      // Merge updates
      const updatedPreferences = {
        ...currentPreferences,
        ...value,
        updatedAt: new Date().toISOString()
      };

      // Save to database
      await db.updateItem('notification-preferences', updatedPreferences);

      // Update cache
      const cacheKey = `notification-preferences:${user.id}`;
      await redis.setex(cacheKey, 3600, JSON.stringify(updatedPreferences));

      // Log activity
      await notificationManager['logNotificationActivity'](user.id, 'preferences_updated', {
        changes: value,
        organizationId: user.organizationId
      });

      logger.info('Notification preferences updated', {
        correlationId,
        userId: user.id,
        changes: Object.keys(value)
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          preferences: updatedPreferences
        }
      }, request);
    }

    return addCorsHeaders({
      status: 405,
      jsonBody: { error: 'Method not allowed' }
    }, request);

  } catch (error) {
    logger.error('Notification preferences management failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * Notification Engagement Tracking
 */
async function trackNotificationEngagement(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;

  try {
    // Validate request
    const body = await request.json();
    const { error, value } = trackEngagementSchema.validate(body);
    if (error) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: error.details[0].message }
      }, request);
    }

    const { notificationId, action, channel, metadata } = value;

    // Record engagement
    const engagement = {
      id: uuidv4(),
      notificationId,
      action,
      channel,
      metadata,
      timestamp: new Date().toISOString(),
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown'
    };

    await db.createItem('notification-engagements', engagement);

    // Update notification if it's a read action
    if (action === 'opened') {
      try {
        const notification = await db.readItem('notifications', notificationId, 'default');
        if (notification && !notification.isRead) {
          await db.updateItem('notifications', {
            id: notification.id,
            ...notification,
            isRead: true,
            readAt: new Date().toISOString()
          });

          // Send real-time update via centralized SignalR service
          const signalRService = require('../shared/services/signalr').signalRService.getInstance();
          await signalRService.sendToUser(notification.recipientId, {
            target: 'notificationUpdated',
            arguments: [{
              notificationId,
              isRead: true,
              readAt: new Date().toISOString()
            }]
          });
        }
      } catch (error) {
        logger.warn('Failed to update notification read status', {
          notificationId,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    logger.info('Notification engagement tracked', {
      correlationId,
      notificationId,
      action,
      channel
    });

    return addCorsHeaders({
      status: 200,
      jsonBody: { success: true }
    }, request);

  } catch (error) {
    logger.error('Track notification engagement failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * Notification Analytics
 */
async function getNotificationAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    // Parse query parameters
    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId') || user.organizationId;
    const days = parseInt(url.searchParams.get('days') || '30');

    // Check cache
    const cacheKey = `notification-analytics:${organizationId}:${days}`;
    const cached = await redis.get(cacheKey);
    if (cached) {
      return addCorsHeaders({
        status: 200,
        jsonBody: JSON.parse(cached)
      }, request);
    }

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get notification metrics
    const [notifications, engagements] = await Promise.all([
      db.queryItems<any>('notifications',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate',
        [
          { name: '@orgId', value: organizationId },
          { name: '@startDate', value: startDate.toISOString() }
        ]
      ),
      db.queryItems<any>('notification-engagements',
        'SELECT * FROM c WHERE c.timestamp >= @startDate',
        [{ name: '@startDate', value: startDate.toISOString() }]
      )
    ]);

    // Calculate analytics
    const analytics = {
      summary: {
        totalSent: notifications.length,
        totalRead: notifications.filter((n: any) => n.isRead).length,
        totalEngagements: engagements.length,
        readRate: notifications.length > 0 ? (notifications.filter((n: any) => n.isRead).length / notifications.length * 100).toFixed(2) : 0
      },
      byType: groupByField(notifications, 'type'),
      byChannel: groupByField(notifications, 'channels'),
      byPriority: groupByField(notifications, 'priority'),
      engagementsByAction: groupByField(engagements, 'action'),
      dailyTrends: calculateDailyTrends(notifications, days)
    };

    // Cache for 1 hour
    await redis.setex(cacheKey, 3600, JSON.stringify(analytics));

    return addCorsHeaders({
      status: 200,
      jsonBody: analytics
    }, request);

  } catch (error) {
    logger.error('Get notification analytics failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

// Helper methods for analytics
function groupByField(items: any[], field: string): any[] {
  const grouped = items.reduce((acc: any, item: any) => {
    const key = item[field] || 'unknown';
    acc[key] = (acc[key] || 0) + 1;
    return acc;
  }, {});

  return Object.entries(grouped).map(([key, count]) => ({ key, count }));
}

function calculateDailyTrends(notifications: any[], days: number): any[] {
  const trends = [];
  const today = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];

    const dayNotifications = notifications.filter((n: any) =>
      n.createdAt.startsWith(dateStr)
    );

    trends.push({
      date: dateStr,
      sent: dayNotifications.length,
      read: dayNotifications.filter((n: any) => n.isRead).length
    });
  }

  return trends;
}

// Register HTTP functions
app.http('notification-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/send',
  handler: (request, context) => notificationManager.sendNotification(request, context)
});

app.http('notification-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications',
  handler: (request, context) => notificationManager.listNotifications(request, context)
});

app.http('notification-mark-read', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/mark-read',
  handler: (request, context) => notificationManager.markNotificationsRead(request, context)
});

app.http('notification-preferences', {
  methods: ['GET', 'PUT', 'PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/preferences',
  handler: manageNotificationPreferences
});

app.http('notification-tracking', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'anonymous', // Allow anonymous tracking for email opens, etc.
  route: 'notifications/track',
  handler: trackNotificationEngagement
});

app.http('notification-analytics', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/analytics',
  handler: getNotificationAnalytics
});
