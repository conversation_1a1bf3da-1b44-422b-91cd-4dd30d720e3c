/**
 * Event Monitoring Dashboard
 * Provides a UI for monitoring events and managing the dead-letter queue
 */
import React, { useState, useEffect, useMemo } from 'react';
import { format } from 'date-fns';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { Loader2, MoreVertical, RefreshCw, Search, AlertTriangle } from 'lucide-react';
import { EventType } from '@/services/event-grid-service';
import { adminService } from '@/services';
import { useDebounce } from '@/hooks/common';

// Event types grouped by category
const EVENT_TYPE_CATEGORIES = {
  Document: [
    EventType.DOCUMENT_UPLOADED,
    EventType.DOCUMENT_PROCESSED,
    EventType.DOCUMENT_UPDATED,
    EventType.DOCUMENT_DELETED,
    EventType.DOCUMENT_SHARED,
    EventType.DOCUMENT_COMMENTED,
  ],
  Project: [
    EventType.PROJECT_CREATED,
    EventType.PROJECT_UPDATED,
    EventType.PROJECT_DELETED,
    EventType.PROJECT_MEMBER_ADDED,
    EventType.PROJECT_MEMBER_REMOVED,
    EventType.PROJECT_MEMBER_ROLE_UPDATED,
  ],
  Organization: [
    EventType.ORGANIZATION_CREATED,
    EventType.ORGANIZATION_UPDATED,
    EventType.ORGANIZATION_DELETED,
    EventType.ORGANIZATION_MEMBER_ADDED,
    EventType.ORGANIZATION_MEMBER_REMOVED,
    EventType.ORGANIZATION_MEMBER_ROLE_UPDATED,
  ],
  User: [
    EventType.USER_REGISTERED,
    EventType.USER_LOGGED_IN,
    EventType.USER_LOGGED_OUT,
    EventType.USER_PROFILE_UPDATED,
  ],
  Comment: [
    EventType.COMMENT_CREATED,
    EventType.COMMENT_UPDATED,
    EventType.COMMENT_DELETED,
    EventType.COMMENT_RESOLVED,
    EventType.COMMENT_REOPENED,
  ],
  Workflow: [
    EventType.WORKFLOW_CREATED,
    EventType.WORKFLOW_UPDATED,
    EventType.WORKFLOW_DELETED,
    EventType.WORKFLOW_EXECUTED,
    EventType.WORKFLOW_COMPLETED,
    EventType.WORKFLOW_FAILED,
  ],
};

// Time range options
const TIME_RANGE_OPTIONS = [
  { value: '15m', label: 'Last 15 minutes' },
  { value: '1h', label: 'Last hour' },
  { value: '6h', label: 'Last 6 hours' },
  { value: '24h', label: 'Last 24 hours' },
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
];

// Event status badge component
const EventStatusBadge = ({ status }: { status: string }) => {
  const variant =
    status === 'success' ? 'success' :
    status === 'failed' ? 'destructive' :
    status === 'processing' ? 'default' :
    status === 'retrying' ? 'warning' : 'secondary';

  return (
    <Badge variant={variant}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

// Dead letter queue item interface
interface DeadLetterQueueItem {
  id: string;
  eventId: string;
  originalEventId: string;
  eventType: EventType;
  failureReason: string;
  failureTimestamp: string;
  processingAttempts: number;
  metadata: Record<string, any>;
}

// Event monitoring dashboard props
interface EventMonitoringDashboardProps {
  onEventSelect?: (eventId: string, isDeadLetterQueue: boolean) => void;
}

// Event monitoring dashboard component
export function EventMonitoringDashboard({ onEventSelect }: EventMonitoringDashboardProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('events');
  const [timeRange, setTimeRange] = useState('1h');
  const [eventTypeFilter, setEventTypeFilter] = useState<EventType | undefined>(undefined);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [events, setEvents] = useState<any[]>([]);
  const [deadLetterItems, setDeadLetterItems] = useState<DeadLetterQueueItem[]>([]);
  const [eventMetrics, setEventMetrics] = useState<any>({
    total: 0,
    success: 0,
    failed: 0,
    processing: 0,
    byType: {}
  });
  const [deadLetterMetrics, setDeadLetterMetrics] = useState<any>({
    total: 0,
    byType: {}
  });

  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'events') {
      loadEvents();
    } else if (activeTab === 'dead-letter-queue') {
      loadDeadLetterQueue();
    }
  }, [activeTab, timeRange, eventTypeFilter, debouncedSearchQuery]);

  // Load events
  const loadEvents = async () => {
    try {
      setIsLoading(true);
      const response = await adminService.getEvents({
        timeRange,
        eventType: eventTypeFilter || undefined,
        searchQuery: debouncedSearchQuery || undefined,
        limit: 100
      });
      setEvents(response.events);
      setEventMetrics(response.metrics);
    } catch (error) {
      toast({
        title: 'Error loading events',
        description: 'Failed to load events. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load dead letter queue
  const loadDeadLetterQueue = async () => {
    try {
      setIsLoading(true);
      const response = await adminService.getDeadLetterQueue({
        messageType: eventTypeFilter || undefined,
        failureReason: debouncedSearchQuery || undefined,
        limit: 100
      });
      setDeadLetterItems(response.items);
      setDeadLetterMetrics(response.metrics);
    } catch (error) {
      toast({
        title: 'Error loading dead letter queue',
        description: 'Failed to load dead letter queue. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Retry a dead letter queue item
  const retryDeadLetterItem = async (id: string) => {
    try {
      setIsRetrying(true);
      await adminService.retryDeadLetterItem(id);
      toast({
        title: 'Event retried',
        description: 'The event has been successfully retried.',
        variant: 'default'
      });
      loadDeadLetterQueue();
    } catch (error) {
      toast({
        title: 'Error retrying event',
        description: 'Failed to retry the event. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsRetrying(false);
    }
  };

  // Retry all dead letter queue items
  const retryAllDeadLetterItems = async () => {
    try {
      setIsRetrying(true);
      await adminService.retryAllDeadLetterItems({
        messageType: eventTypeFilter || undefined
      });
      toast({
        title: 'Events retried',
        description: 'All matching events have been queued for retry.',
        variant: 'default'
      });
      loadDeadLetterQueue();
    } catch (error) {
      toast({
        title: 'Error retrying events',
        description: 'Failed to retry events. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsRetrying(false);
    }
  };

  // Filtered events
  const filteredEvents = useMemo(() => {
    return events;
  }, [events]);

  // Filtered dead letter items
  const filteredDeadLetterItems = useMemo(() => {
    return deadLetterItems;
  }, [deadLetterItems]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Event Monitoring</h1>
        <Button
          variant="outline"
          onClick={() => activeTab === 'events' ? loadEvents() : loadDeadLetterQueue()}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="dead-letter-queue">Dead Letter Queue</TabsTrigger>
        </TabsList>

        <div className="my-4 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger>
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                {TIME_RANGE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex-1">
            <Select
              value={eventTypeFilter || ""}
              onValueChange={(value) => setEventTypeFilter(value ? value as EventType : undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Filter by event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All event types</SelectItem>
                {Object.entries(EVENT_TYPE_CATEGORIES).map(([category, types]) => (
                  <React.Fragment key={category}>
                    <SelectItem value={`__category_${category}`} disabled>
                      {category}
                    </SelectItem>
                    {types.map((type) => (
                      <SelectItem key={type} value={type} className="pl-6">
                        {type.replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                  </React.Fragment>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
        </div>

        <TabsContent value="events" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Total Events</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{eventMetrics.total}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Success</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-500">{eventMetrics.success}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Failed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-red-500">{eventMetrics.failed}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Processing</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-500">{eventMetrics.processing}</div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>
                Showing the most recent events in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : filteredEvents.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No events found matching the current filters
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event Type</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Resource</TableHead>
                      <TableHead>Timestamp</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEvents.map((event) => (
                      <TableRow key={event.id}>
                        <TableCell>
                          {event.eventType.replace(/_/g, ' ')}
                        </TableCell>
                        <TableCell>
                          <EventStatusBadge status={event.status} />
                        </TableCell>
                        <TableCell>
                          {event.subject}
                        </TableCell>
                        <TableCell>
                          {format(new Date(event.eventTime), 'MMM d, yyyy HH:mm:ss')}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => onEventSelect?.(event.id, false)}>
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => window.open(`/admin/${event.subject.split('/')[0]}s/${event.subject.split('/')[1]}`, '_blank')}>
                                View Resource
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dead-letter-queue" className="space-y-4">
          <div className="flex justify-between items-center">
            <Card className="w-full">
              <CardHeader className="pb-2">
                <CardTitle>Dead Letter Queue</CardTitle>
                <CardDescription>
                  Events that failed processing and require attention
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-red-500">{deadLetterMetrics.total}</div>
              </CardContent>
              <CardFooter>
                <Button
                  onClick={retryAllDeadLetterItems}
                  disabled={isRetrying || deadLetterMetrics.total === 0}
                  className="w-full"
                >
                  {isRetrying ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Retry All
                </Button>
              </CardFooter>
            </Card>
          </div>

          {deadLetterMetrics.total > 10 && (
            <Alert variant="warning">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                There are {deadLetterMetrics.total} items in the dead letter queue. This may indicate a systemic issue.
              </AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Dead Letter Queue Items</CardTitle>
              <CardDescription>
                Events that failed processing and require manual intervention
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              ) : filteredDeadLetterItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No dead letter queue items found matching the current filters
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event Type</TableHead>
                      <TableHead>Failure Reason</TableHead>
                      <TableHead>Attempts</TableHead>
                      <TableHead>Timestamp</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDeadLetterItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          {item.eventType.replace(/_/g, ' ')}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {item.failureReason}
                        </TableCell>
                        <TableCell>
                          {item.processingAttempts}
                        </TableCell>
                        <TableCell>
                          {format(new Date(item.failureTimestamp), 'MMM d, yyyy HH:mm:ss')}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => onEventSelect?.(item.id, true)}>
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => retryDeadLetterItem(item.id)}
                                disabled={isRetrying}
                              >
                                Retry Event
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
