/**
 * Service Bus Service
 * Service for Azure Service Bus messaging and queue operations
 */

import { backendApiClient } from './backend-api-client'

export interface ServiceBusMessage {
  id: string
  messageId: string
  sessionId?: string
  correlationId?: string
  subject?: string
  body: any
  properties: Record<string, any>
  timeToLive?: number
  scheduledEnqueueTime?: string
  partitionKey?: string
  replyTo?: string
  replyToSessionId?: string
  contentType?: string
  label?: string
}

export interface QueueInfo {
  name: string
  messageCount: number
  activeMessageCount: number
  deadLetterMessageCount: number
  scheduledMessageCount: number
  transferMessageCount: number
  transferDeadLetterMessageCount: number
  sizeInBytes: number
  maxSizeInMegabytes: number
  status: 'Active' | 'Disabled' | 'SendDisabled' | 'ReceiveDisabled'
  createdAt: string
  updatedAt: string
  accessedAt: string
}

export interface TopicInfo {
  name: string
  subscriptions: SubscriptionInfo[]
  messageCount: number
  sizeInBytes: number
  maxSizeInMegabytes: number
  status: 'Active' | 'Disabled' | 'SendDisabled' | 'ReceiveDisabled'
  createdAt: string
  updatedAt: string
  accessedAt: string
}

export interface SubscriptionInfo {
  name: string
  topicName: string
  messageCount: number
  activeMessageCount: number
  deadLetterMessageCount: number
  status: 'Active' | 'Disabled' | 'ReceiveDisabled'
  maxDeliveryCount: number
  lockDuration: string
  defaultMessageTimeToLive: string
  createdAt: string
  updatedAt: string
  accessedAt: string
}

export interface MessageProcessingResult {
  messageId: string
  status: 'processed' | 'failed' | 'deadlettered' | 'abandoned'
  processingTime: number
  error?: string
  result?: any
}

export enum MessageStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  FAILED = 'failed',
  DEADLETTERED = 'deadlettered',
  ABANDONED = 'abandoned',
  COMPLETED = 'completed',
  QUEUED = 'queued',
  CANCELLED = 'cancelled'
}

export enum MessageType {
  DOCUMENT_PROCESSING = 'document-processing',
  DOCUMENT_PROCESSING_RESULT = 'document-processing-result',
  NOTIFICATION = 'notification',
  WORKFLOW_EXECUTION = 'workflow-execution',
  SYSTEM_ALERT = 'system-alert',
  USER_ACTION = 'user-action',
  AUDIT_LOG = 'audit-log'
}

class ServiceBusService {
  private messageSubscriptions = new Map<string, ((message: any) => void)[]>()

  /**
   * Send a message to the service bus
   */
  async sendMessage(message: any, queueName = 'default'): Promise<{
    messageId: string
    enqueuedTime: string
    sequenceNumber: number
  }> {
    return this.sendToQueue(queueName, {
      body: message,
      properties: {},
      contentType: 'application/json'
    })
  }

  /**
   * Subscribe to messages of a specific type
   */
  subscribeToMessages(messageType: string, callback: (message: any) => void): () => void {
    if (!this.messageSubscriptions.has(messageType)) {
      this.messageSubscriptions.set(messageType, [])
    }

    const callbacks = this.messageSubscriptions.get(messageType)!
    callbacks.push(callback)

    // Return unsubscribe function
    return () => {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * Emit a message to subscribers
   */
  private emitMessage(messageType: string, message: any): void {
    const callbacks = this.messageSubscriptions.get(messageType)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(message)
        } catch (error) {
          console.error('Error in message callback:', error)
        }
      })
    }
  }
  /**
   * Send message to queue
   */
  async sendToQueue(queueName: string, message: Omit<ServiceBusMessage, 'id' | 'messageId'>): Promise<{
    messageId: string
    enqueuedTime: string
    sequenceNumber: number
  }> {
    return await backendApiClient.request('/service-bus/queues/send', {
      method: 'POST',
      body: JSON.stringify({
        queueName,
        message
      })
    })
  }

  /**
   * Send message to topic
   */
  async sendToTopic(topicName: string, message: Omit<ServiceBusMessage, 'id' | 'messageId'>): Promise<{
    messageId: string
    enqueuedTime: string
    sequenceNumber: number
  }> {
    return await backendApiClient.request('/service-bus/topics/send', {
      method: 'POST',
      body: JSON.stringify({
        topicName,
        message
      })
    })
  }

  /**
   * Receive messages from queue
   */
  async receiveFromQueue(queueName: string, options?: {
    maxMessageCount?: number
    maxWaitTime?: number
    receiveMode?: 'peekLock' | 'receiveAndDelete'
  }): Promise<ServiceBusMessage[]> {
    return await backendApiClient.request('/service-bus/queues/receive', {
      method: 'POST',
      body: JSON.stringify({
        queueName,
        options: options || {}
      })
    })
  }

  /**
   * Receive messages from subscription
   */
  async receiveFromSubscription(topicName: string, subscriptionName: string, options?: {
    maxMessageCount?: number
    maxWaitTime?: number
    receiveMode?: 'peekLock' | 'receiveAndDelete'
  }): Promise<ServiceBusMessage[]> {
    return await backendApiClient.request('/service-bus/subscriptions/receive', {
      method: 'POST',
      body: JSON.stringify({
        topicName,
        subscriptionName,
        options: options || {}
      })
    })
  }

  /**
   * Complete message processing
   */
  async completeMessage(messageId: string): Promise<{
    messageId: string
    status: 'completed'
    completedAt: string
  }> {
    return await backendApiClient.request(`/service-bus/messages/${messageId}/complete`, {
      method: 'POST'
    })
  }

  /**
   * Abandon message
   */
  async abandonMessage(messageId: string, reason?: string): Promise<{
    messageId: string
    status: 'abandoned'
    abandonedAt: string
  }> {
    return await backendApiClient.request(`/service-bus/messages/${messageId}/abandon`, {
      method: 'POST',
      body: JSON.stringify({ reason })
    })
  }

  /**
   * Dead letter message
   */
  async deadLetterMessage(messageId: string, reason?: string, description?: string): Promise<{
    messageId: string
    status: 'deadlettered'
    deadLetteredAt: string
  }> {
    return await backendApiClient.request(`/service-bus/messages/${messageId}/deadletter`, {
      method: 'POST',
      body: JSON.stringify({ reason, description })
    })
  }

  /**
   * Get queue information
   */
  async getQueueInfo(queueName: string): Promise<QueueInfo> {
    return await backendApiClient.request<QueueInfo>(`/service-bus/queues/${queueName}/info`)
  }

  /**
   * Get topic information
   */
  async getTopicInfo(topicName: string): Promise<TopicInfo> {
    return await backendApiClient.request<TopicInfo>(`/service-bus/topics/${topicName}/info`)
  }

  /**
   * Get subscription information
   */
  async getSubscriptionInfo(topicName: string, subscriptionName: string): Promise<SubscriptionInfo> {
    return await backendApiClient.request<SubscriptionInfo>(`/service-bus/topics/${topicName}/subscriptions/${subscriptionName}/info`)
  }

  /**
   * List all queues
   */
  async listQueues(): Promise<QueueInfo[]> {
    return await backendApiClient.request<QueueInfo[]>('/service-bus/queues')
  }

  /**
   * List all topics
   */
  async listTopics(): Promise<TopicInfo[]> {
    return await backendApiClient.request<TopicInfo[]>('/service-bus/topics')
  }

  /**
   * Peek messages in queue
   */
  async peekMessages(queueName: string, maxMessageCount = 10): Promise<ServiceBusMessage[]> {
    return await backendApiClient.request('/service-bus/queues/peek', {
      method: 'POST',
      body: JSON.stringify({
        queueName,
        maxMessageCount
      })
    })
  }

  /**
   * Get dead letter messages
   */
  async getDeadLetterMessages(queueName: string, maxMessageCount = 10): Promise<ServiceBusMessage[]> {
    return await backendApiClient.request('/service-bus/queues/deadletter', {
      method: 'POST',
      body: JSON.stringify({
        queueName,
        maxMessageCount
      })
    })
  }

  /**
   * Schedule message
   */
  async scheduleMessage(queueName: string, message: Omit<ServiceBusMessage, 'id' | 'messageId'>, scheduledEnqueueTime: string): Promise<{
    messageId: string
    sequenceNumber: number
    scheduledEnqueueTime: string
  }> {
    return await backendApiClient.request('/service-bus/queues/schedule', {
      method: 'POST',
      body: JSON.stringify({
        queueName,
        message: {
          ...message,
          scheduledEnqueueTime
        }
      })
    })
  }

  /**
   * Cancel scheduled message
   */
  async cancelScheduledMessage(sequenceNumber: number): Promise<{
    sequenceNumber: number
    status: 'cancelled'
    cancelledAt: string
  }> {
    return await backendApiClient.request(`/service-bus/scheduled/${sequenceNumber}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Process messages with handler
   */
  async processMessages(queueName: string, handler: (message: ServiceBusMessage) => Promise<MessageProcessingResult>): Promise<{
    processedCount: number
    failedCount: number
    results: MessageProcessingResult[]
  }> {
    // This would typically be handled on the backend, but we can simulate it
    const messages = await this.receiveFromQueue(queueName, { maxMessageCount: 10 })
    const results: MessageProcessingResult[] = []
    let processedCount = 0
    let failedCount = 0

    for (const message of messages) {
      try {
        const result = await handler(message)
        results.push(result)
        
        if (result.status === 'processed') {
          await this.completeMessage(message.messageId)
          processedCount++
        } else if (result.status === 'failed') {
          await this.abandonMessage(message.messageId, result.error)
          failedCount++
        } else if (result.status === 'deadlettered') {
          await this.deadLetterMessage(message.messageId, result.error)
          failedCount++
        }
      } catch (error) {
        await this.deadLetterMessage(message.messageId, error instanceof Error ? error.message : 'Unknown error')
        failedCount++
        results.push({
          messageId: message.messageId,
          status: 'failed',
          processingTime: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return {
      processedCount,
      failedCount,
      results
    }
  }

  /**
   * Get service bus metrics
   */
  async getMetrics(): Promise<{
    totalQueues: number
    totalTopics: number
    totalMessages: number
    totalDeadLetterMessages: number
    throughput: {
      messagesPerSecond: number
      bytesPerSecond: number
    }
    errors: {
      count: number
      rate: number
    }
  }> {
    return await backendApiClient.request('/service-bus/metrics')
  }

  /**
   * Get message status
   */
  async getMessageStatus(messageId: string): Promise<{
    messageId: string
    status: MessageStatus
    enqueuedTime?: string
    processedTime?: string
    error?: string
    retryCount?: number
  }> {
    return await backendApiClient.request(`/service-bus/messages/${messageId}/status`)
  }

  /**
   * Process document via service bus
   */
  async processDocument(documentId: string, options?: {
    processingType?: string
    priority?: 'low' | 'normal' | 'high'
    organizationId?: string
    extractText?: boolean
    extractEntities?: boolean
    classify?: boolean
  }): Promise<string> {
    const result = await backendApiClient.request('/service-bus/document-processing/enqueue', {
      method: 'POST',
      body: JSON.stringify({
        documentId,
        options: options || {}
      })
    })

    // Return just the messageId as expected by the upload component
    return result.messageId || result.jobId || result.id
  }
}

export const serviceBusService = new ServiceBusService()

// Default export for backward compatibility
export default serviceBusService
