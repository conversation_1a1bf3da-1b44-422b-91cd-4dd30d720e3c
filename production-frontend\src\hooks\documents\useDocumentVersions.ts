/**
 * Document Versions Hooks
 * React hooks for document version management
 */

import { useState, useCallback } from 'react'
import { useDocumentStore } from '@/stores/document-store'
import { useToast } from '@/hooks/use-toast'
import backendApiClient from '@/services/backend-api-client'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

export interface DocumentVersion {
  id: string
  documentId: string
  version: string
  name?: string
  description?: string
  size: number
  contentType: string
  url: string
  downloadUrl?: string
  thumbnailUrl?: string
  checksum: string
  isActive: boolean
  isMajor: boolean
  changes: {
    summary: string
    details?: string[]
    changeType: 'content' | 'metadata' | 'structure' | 'formatting'
  }
  createdBy: string
  createdAt: string
  metadata: Record<string, any>
}

export interface CreateVersionRequest {
  documentId: string
  name?: string
  description?: string
  isMajor?: boolean
  changes?: {
    summary: string
    details?: string[]
    changeType: 'content' | 'metadata' | 'structure' | 'formatting'
  }
  file?: File
  content?: any
}

export interface VersionComparison {
  fromVersion: DocumentVersion
  toVersion: DocumentVersion
  differences: {
    type: 'added' | 'removed' | 'modified'
    section: string
    content: string
    position?: { line: number; column: number }
  }[]
  summary: {
    addedLines: number
    removedLines: number
    modifiedLines: number
    totalChanges: number
  }
}

export interface RestoreVersionRequest {
  documentId: string
  versionId: string
  createBackup?: boolean
  reason?: string
}

/**
 * Hook to get document versions
 */
export function useDocumentVersions(documentId: string) {
  return useQuery({
    queryKey: ['document-versions', documentId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentVersion[]>(`/documents/${documentId}/versions`)
    },
    enabled: !!documentId,
  })
}

/**
 * Hook to get a specific document version
 */
export function useDocumentVersion(documentId: string, versionId: string) {
  return useQuery({
    queryKey: ['document-version', documentId, versionId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentVersion>(`/documents/${documentId}/versions/${versionId}`)
    },
    enabled: !!documentId && !!versionId,
  })
}

/**
 * Hook to create a new document version
 */
export function useCreateDocumentVersion() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateVersionRequest) => {
      if (data.file) {
        const formData = new FormData()
        formData.append('file', data.file)
        if (data.name) formData.append('name', data.name)
        if (data.description) formData.append('description', data.description)
        if (data.isMajor !== undefined) formData.append('isMajor', data.isMajor.toString())
        if (data.changes) formData.append('changes', JSON.stringify(data.changes))

        return await backendApiClient.request<DocumentVersion>(`/documents/${data.documentId}/versions`, {
          method: 'POST',
          body: formData as any
        })
      } else {
        return await backendApiClient.request<DocumentVersion>(`/documents/${data.documentId}/versions`, {
          method: 'POST',
          body: JSON.stringify(data)
        })
      }
    },
    onSuccess: (version) => {
      queryClient.invalidateQueries({ queryKey: ['document-versions', version.documentId] })
      queryClient.invalidateQueries({ queryKey: ['document', version.documentId] })
      toast({
        title: 'Version created',
        description: `Document version ${version.version} has been created successfully.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error creating version',
        description: 'There was a problem creating the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to restore a document version
 */
export function useRestoreDocumentVersion() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: RestoreVersionRequest) => {
      return await backendApiClient.request<DocumentVersion>(`/documents/${data.documentId}/versions/${data.versionId}/restore`, {
        method: 'POST',
        body: JSON.stringify({
          createBackup: data.createBackup,
          reason: data.reason
        })
      })
    },
    onSuccess: (version) => {
      queryClient.invalidateQueries({ queryKey: ['document-versions', version.documentId] })
      queryClient.invalidateQueries({ queryKey: ['document', version.documentId] })
      toast({
        title: 'Version restored',
        description: `Document has been restored to version ${version.version}.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error restoring version',
        description: 'There was a problem restoring the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a document version
 */
export function useDeleteDocumentVersion() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, versionId }: { documentId: string; versionId: string }) => {
      await backendApiClient.request(`/documents/${documentId}/versions/${versionId}`, {
        method: 'DELETE'
      })
      return { documentId, versionId }
    },
    onSuccess: ({ documentId, versionId }) => {
      queryClient.invalidateQueries({ queryKey: ['document-versions', documentId] })
      queryClient.removeQueries({ queryKey: ['document-version', documentId, versionId] })
      toast({
        title: 'Version deleted',
        description: 'The document version has been deleted successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error deleting version',
        description: 'There was a problem deleting the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to compare document versions
 */
export function useCompareDocumentVersions() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      documentId, 
      fromVersionId, 
      toVersionId 
    }: { 
      documentId: string
      fromVersionId: string
      toVersionId: string 
    }) => {
      return await backendApiClient.request<VersionComparison>(`/documents/${documentId}/versions/compare`, {
        method: 'POST',
        body: JSON.stringify({
          fromVersionId,
          toVersionId
        })
      })
    },
    onError: (error) => {
      toast({
        title: 'Error comparing versions',
        description: 'There was a problem comparing the document versions. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to download a specific version
 */
export function useDownloadDocumentVersion() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, versionId }: { documentId: string; versionId: string }) => {
      return await backendApiClient.request(`/documents/${documentId}/versions/${versionId}/download`, {
        method: 'GET'
      })
    },
    onSuccess: (result) => {
      if (result.downloadUrl) {
        window.open(result.downloadUrl, '_blank')
      }
    },
    onError: (error) => {
      toast({
        title: 'Error downloading version',
        description: 'There was a problem downloading the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get version history statistics
 */
export function useVersionStats(documentId: string) {
  return useQuery({
    queryKey: ['version-stats', documentId],
    queryFn: async () => {
      return await backendApiClient.request(`/documents/${documentId}/versions/stats`)
    },
    enabled: !!documentId,
  })
}

/**
 * Hook to set active version
 */
export function useSetActiveVersion() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, versionId }: { documentId: string; versionId: string }) => {
      return await backendApiClient.request<DocumentVersion>(`/documents/${documentId}/versions/${versionId}/activate`, {
        method: 'POST'
      })
    },
    onSuccess: (version) => {
      queryClient.invalidateQueries({ queryKey: ['document-versions', version.documentId] })
      queryClient.invalidateQueries({ queryKey: ['document', version.documentId] })
      toast({
        title: 'Version activated',
        description: `Version ${version.version} is now the active version.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error activating version',
        description: 'There was a problem activating the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get version content
 */
export function useVersionContent(documentId: string, versionId: string) {
  return useQuery({
    queryKey: ['version-content', documentId, versionId],
    queryFn: async () => {
      return await backendApiClient.request(`/documents/${documentId}/versions/${versionId}/content`)
    },
    enabled: !!documentId && !!versionId,
  })
}

/**
 * Comprehensive document versions hook with all functionality
 */
export function useDocumentVersionsWithActions(documentId: string) {
  const [selectedVersion, setSelectedVersion] = useState<DocumentVersion | null>(null)
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false)

  const versionsQuery = useDocumentVersions(documentId)
  const restoreVersion = useRestoreDocumentVersion()
  const downloadVersion = useDownloadDocumentVersion()

  const formatFileSize = useCallback((bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }, [])

  const handleViewVersion = useCallback((version: DocumentVersion) => {
    // Implementation for viewing version
    console.log('View version:', version)
  }, [])

  const handleDownloadVersion = useCallback((version: DocumentVersion) => {
    downloadVersion.mutate({
      documentId: version.documentId,
      versionId: version.id
    })
  }, [downloadVersion])

  const handleRestoreVersion = useCallback((version: DocumentVersion) => {
    setSelectedVersion(version)
    setIsRestoreDialogOpen(true)
  }, [])

  const confirmRestore = useCallback(() => {
    if (selectedVersion) {
      restoreVersion.mutate({
        documentId: selectedVersion.documentId,
        versionId: selectedVersion.id,
        createBackup: true
      })
      setIsRestoreDialogOpen(false)
      setSelectedVersion(null)
    }
  }, [selectedVersion, restoreVersion])

  return {
    versions: versionsQuery.data || [],
    isLoading: versionsQuery.isLoading,
    error: versionsQuery.error,
    selectedVersion,
    isRestoreDialogOpen,
    setIsRestoreDialogOpen,
    formatFileSize,
    handleViewVersion,
    handleDownloadVersion,
    handleRestoreVersion,
    confirmRestore,
    isRestoring: restoreVersion.isPending,
  }
}
