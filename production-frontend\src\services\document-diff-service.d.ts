/**
 * Document Diff Service Type Declarations
 */

export interface DiffResult {
  unified: Array<{
    added?: boolean
    removed?: boolean
    value: string
    count?: number
  }>
  split: {
    left: Array<{
      added?: boolean
      removed?: boolean
      value: string
      count?: number
    }>
    right: Array<{
      added?: boolean
      removed?: boolean
      value: string
      count?: number
    }>
  }
  statistics: {
    addedLines: number
    removedLines: number
    modifiedLines: number
    totalChanges: number
    similarity: number
  }
}

export interface DiffOptions {
  ignoreWhitespace?: boolean
  ignoreCase?: boolean
  contextLines?: number
  algorithm?: 'myers' | 'patience' | 'histogram'
}

export interface DocumentVersion {
  id: string
  versionNumber: number
  content: string
  createdAt: string
  createdBy: {
    id: string
    name: string
    avatarUrl?: string
  }
  changes?: string
  size?: number
}

export class DocumentDiffService {
  constructor()

  compareVersions(
    leftVersion: DocumentVersion,
    rightVersion: DocumentVersion,
    options?: DiffOptions
  ): Promise<DiffResult>

  compareContent(
    leftContent: string,
    rightContent: string,
    options?: DiffOptions
  ): Promise<DiffResult>

  generatePatch(
    originalContent: string,
    modifiedContent: string
  ): Promise<string>

  applyPatch(
    originalContent: string,
    patch: string
  ): Promise<string>

  getChangesSummary(diffResult: DiffResult): {
    summary: string
    details: string[]
    impact: 'minor' | 'moderate' | 'major'
  }
}

export const documentDiffService: DocumentDiffService
export default documentDiffService
