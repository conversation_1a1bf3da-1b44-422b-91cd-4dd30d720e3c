/**
 * Chat Completion Hooks
 * React hooks for AI chat completion using DeepSeek R1 and Llama models
 */

import { useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAIStore, useStartAIOperation, useAILoading, useAIError } from '@/stores/ai-store'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: {
    model?: string
    tokensUsed?: number
    confidence?: number
    reasoning?: string
    processingTime?: number
  }
}

export interface ChatSession {
  id: string
  title: string
  messages: ChatMessage[]
  organizationId: string
  projectId?: string
  userId: string
  model: 'deepseek-r1' | 'llama' | 'auto'
  systemPrompt?: string
  settings: {
    maxTokens: number
    temperature: number
    topP: number
    enableReasoning: boolean
    enableContext: boolean
  }
  createdAt: string
  updatedAt: string
  isActive: boolean
}

export interface ChatCompletionRequest {
  sessionId?: string
  message: string
  model?: 'deepseek-r1' | 'llama' | 'auto'
  systemPrompt?: string
  context?: string[]
  options?: {
    maxTokens?: number
    temperature?: number
    topP?: number
    enableReasoning?: boolean
    enableStreaming?: boolean
    includeContext?: boolean
  }
  organizationId: string
  projectId?: string
}

export interface ChatCompletionResponse {
  id: string
  sessionId: string
  message: ChatMessage
  usage: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  model: string
  processingTime: number
  reasoning?: string
  confidence: number
}

export interface CreateChatSessionRequest {
  title?: string
  model?: 'deepseek-r1' | 'llama' | 'auto'
  systemPrompt?: string
  settings?: {
    maxTokens?: number
    temperature?: number
    topP?: number
    enableReasoning?: boolean
    enableContext?: boolean
  }
  organizationId: string
  projectId?: string
}

/**
 * Hook to get chat sessions
 */
export function useChatSessions(params?: {
  organizationId?: string
  projectId?: string
  isActive?: boolean
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['chat-sessions', params],
    queryFn: async () => {
      // Use AI operations list to get chat sessions
      return await backendApiClient.request<ChatSession[]>('/ai/operations/list', {
        params: {
          ...params,
          type: 'CONTENT_GENERATION',
          subType: 'CHAT_SESSION'
        }
      })
    },
  })
}

/**
 * Hook to get a specific chat session
 */
export function useChatSession(sessionId: string) {
  const operations = useAIStore(state => state.operations)
  const loading = useAILoading()
  const error = useAIError()

  const session = operations.find(op => op.id === sessionId && op.type === 'CHAT_SESSION')

  return {
    data: session,
    isLoading: loading,
    error,
    enabled: !!sessionId
  }
}

/**
 * Hook to create a new chat session
 */
export function useCreateChatSession() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateChatSessionRequest) => {
      // Create a chat session using AI operations endpoint
      return await backendApiClient.request<ChatSession>('/ai/operations', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt: `Initialize chat session: ${data.title || 'New Chat'}`,
            contentType: 'chat_session',
            outputFormat: 'json',
            options: {
              useAdvancedAI: data.model === 'deepseek-r1' || data.settings?.enableReasoning,
              maxTokens: data.settings?.maxTokens || 4000,
              temperature: data.settings?.temperature || 0.7,
              topP: data.settings?.topP || 0.9,
              systemPrompt: data.systemPrompt
            }
          },
          organizationId: data.organizationId,
          projectId: data.projectId,
          metadata: {
            sessionTitle: data.title || 'New Chat',
            sessionType: 'chat',
            model: data.model || 'auto'
          }
        })
      })
    },
    onSuccess: (session) => {
      queryClient.invalidateQueries({ queryKey: ['chat-sessions'] })
      toast({
        title: 'Chat session created',
        description: `Chat session "${session.title}" has been created successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error creating chat session',
        description: 'There was a problem creating the chat session. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to send a chat message
 */
export function useSendChatMessage() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ChatCompletionRequest) => {
      // Use the content generation endpoint for chat completions
      return await backendApiClient.request<ChatCompletionResponse>('/ai/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt: data.message,
            contentType: 'chat_response',
            outputFormat: 'text',
            options: {
              useAdvancedAI: data.model === 'deepseek-r1' || data.options?.enableReasoning,
              maxTokens: data.options?.maxTokens || 4000,
              temperature: data.options?.temperature || 0.7,
              topP: data.options?.topP || 0.9,
              systemPrompt: data.systemPrompt,
              context: data.context
            }
          },
          organizationId: data.organizationId,
          projectId: data.projectId,
          metadata: {
            sessionId: data.sessionId,
            messageType: 'chat_completion'
          }
        })
      })
    },
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['chat-session', response.sessionId] })
      queryClient.invalidateQueries({ queryKey: ['chat-sessions'] })
    },
    onError: (_error) => {
      toast({
        title: 'Error sending message',
        description: 'There was a problem sending your message. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to stream chat completion
 */
export function useStreamChatCompletion() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ChatCompletionRequest & { 
      onChunk?: (chunk: string) => void 
      onComplete?: (response: ChatCompletionResponse) => void
    }) => {
      // Use content generation endpoint for streaming (note: streaming may not be fully supported)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/ai/content/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt: data.message,
            contentType: 'analysis',
            outputFormat: 'text',
            options: {
              useAdvancedAI: data.model === 'deepseek-r1' || data.options?.enableReasoning,
              maxTokens: data.options?.maxTokens || 4000,
              temperature: data.options?.temperature || 0.7,
              enableStreaming: true
            }
          },
          sessionId: data.sessionId,
          organizationId: data.organizationId,
          projectId: data.projectId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to start streaming')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let fullResponse = ''
      const decoder = new TextDecoder()

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n')

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonStr = line.slice(6)
              if (jsonStr === '[DONE]') {
                data.onComplete?.({
                  id: `msg-${Date.now()}`,
                  sessionId: data.sessionId || '',
                  message: {
                    id: `msg-${Date.now()}`,
                    role: 'assistant',
                    content: fullResponse,
                    timestamp: new Date().toISOString()
                  },
                  usage: { promptTokens: 0, completionTokens: 0, totalTokens: 0 },
                  model: data.model || 'auto',
                  processingTime: 0,
                  confidence: 0.9
                })
                return fullResponse
              }

              try {
                const parsed = JSON.parse(jsonStr)
                if (parsed.content) {
                  fullResponse += parsed.content
                  data.onChunk?.(parsed.content)
                }
              } catch (e) {
                // Ignore parsing errors for partial chunks
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return fullResponse
    },
    onSuccess: (_response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['chat-session', variables.sessionId] })
      queryClient.invalidateQueries({ queryKey: ['chat-sessions'] })
    },
    onError: (_error) => {
      toast({
        title: 'Error in streaming',
        description: 'There was a problem with the streaming response. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update chat session settings
 */
export function useUpdateChatSession() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      sessionId, 
      updates 
    }: { 
      sessionId: string
      updates: Partial<Pick<ChatSession, 'title' | 'model' | 'systemPrompt' | 'settings'>>
    }) => {
      // Use AI operations endpoint to update session
      return await backendApiClient.request<ChatSession>(`/ai/operations/${sessionId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
    },
    onSuccess: (session) => {
      queryClient.invalidateQueries({ queryKey: ['chat-session', session.id] })
      queryClient.invalidateQueries({ queryKey: ['chat-sessions'] })
      toast({
        title: 'Session updated',
        description: 'Chat session settings have been updated successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error updating session',
        description: 'There was a problem updating the chat session. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a chat session
 */
export function useDeleteChatSession() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (sessionId: string) => {
      // Use AI operations endpoint to delete session
      await backendApiClient.request(`/ai/operations/${sessionId}`, {
        method: 'DELETE'
      })
      return sessionId
    },
    onSuccess: (sessionId) => {
      queryClient.invalidateQueries({ queryKey: ['chat-sessions'] })
      queryClient.removeQueries({ queryKey: ['chat-session', sessionId] })
      toast({
        title: 'Session deleted',
        description: 'The chat session has been deleted successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error deleting session',
        description: 'There was a problem deleting the chat session. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get chat usage statistics
 */
export function useChatUsageStats(organizationId: string, params?: {
  projectId?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: ['chat-usage-stats', organizationId, params],
    queryFn: async () => {
      // Use AI analytics endpoint for chat usage stats
      return await backendApiClient.request('/ai/analytics', {
        params: {
          organizationId,
          operationType: 'CONTENT_GENERATION',
          subType: 'CHAT_SESSION',
          ...params
        }
      })
    },
    enabled: !!organizationId,
  })
}
