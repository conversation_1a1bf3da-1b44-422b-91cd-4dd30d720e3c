/**
 * Enhanced Event Hooks
 * React hooks for easy integration with Event Grid and Service Bus
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { enhancedEventIntegration, EnhancedEventType, ServiceBusQueue } from '@/services/enhanced-event-integration'
import { eventGridService } from '@/services/event-grid-service'
import { serviceBusService } from '@/services/service-bus-service'

// Hook for subscribing to specific Event Grid events
export function useEventSubscription(
  eventTypes: EnhancedEventType[],
  handler: (event: any) => void,
  dependencies: any[] = []
) {
  const handlerRef = useRef(handler)
  handlerRef.current = handler

  useEffect(() => {
    const unsubscribe = eventGridService.subscribe(
      eventTypes as any,
      (event) => handlerRef.current(event)
    )

    return unsubscribe
  }, [eventTypes.join(','), ...dependencies])
}

// Hook for subscribing to Service Bus messages
export function useServiceBusSubscription(
  queueName: ServiceBusQueue,
  handler: (message: any) => void,
  dependencies: any[] = []
) {
  const handlerRef = useRef(handler)
  handlerRef.current = handler

  useEffect(() => {
    const unsubscribe = serviceBusService.subscribeToMessages(
      queueName,
      (message) => handlerRef.current(message)
    )

    return unsubscribe
  }, [queueName, ...dependencies])
}

// Hook for real-time document status updates
export function useDocumentStatusUpdates(documentId?: string) {
  const [status, setStatus] = useState<string>()
  const [progress, setProgress] = useState<number>(0)
  const [error, setError] = useState<string | null>(null)

  useEventSubscription([
    EnhancedEventType.DOCUMENT_PROCESSED,
    EnhancedEventType.DOCUMENT_SIGNED,
    EnhancedEventType.DOCUMENT_SHARED
  ], (event) => {
    if (!documentId || event.data.documentId === documentId) {
      setStatus(event.data.status)
      setProgress(event.data.progress || 100)
      setError(null)
    }
  }, [documentId])

  return { status, progress, error }
}

// Hook for real-time workflow execution updates
export function useWorkflowExecutionUpdates(executionId?: string) {
  const [execution, setExecution] = useState<any>(null)
  const [currentStep, setCurrentStep] = useState<string>()
  const [progress, setProgress] = useState<number>(0)

  useEventSubscription([
    EnhancedEventType.WORKFLOW_STARTED,
    EnhancedEventType.WORKFLOW_STEP_COMPLETED,
    EnhancedEventType.WORKFLOW_COMPLETED,
    EnhancedEventType.WORKFLOW_FAILED
  ], (event) => {
    if (!executionId || event.data.executionId === executionId) {
      setExecution(event.data)
      setCurrentStep(event.data.stepId)
      setProgress(event.data.progress || 0)
    }
  }, [executionId])

  return { execution, currentStep, progress }
}

// Hook for real-time collaboration updates
export function useCollaborationUpdates(documentId?: string) {
  const [participants, setParticipants] = useState<any[]>([])
  const [cursors, setCursors] = useState<Record<string, any>>({})
  const [edits, setEdits] = useState<any[]>([])

  useEventSubscription([
    EnhancedEventType.COLLABORATION_USER_JOINED,
    EnhancedEventType.COLLABORATION_USER_LEFT,
    EnhancedEventType.COLLABORATION_CURSOR_MOVED,
    EnhancedEventType.COLLABORATION_EDIT_MADE
  ], (event) => {
    if (!documentId || event.data.documentId === documentId) {
      switch (event.type) {
        case EnhancedEventType.COLLABORATION_USER_JOINED:
          setParticipants(prev => [...prev.filter(p => p.userId !== event.data.userId), {
            userId: event.data.userId,
            userName: event.data.userName,
            joinedAt: event.data.timestamp
          }])
          break

        case EnhancedEventType.COLLABORATION_USER_LEFT:
          setParticipants(prev => prev.filter(p => p.userId !== event.data.userId))
          setCursors(prev => {
            const newCursors = { ...prev }
            delete newCursors[event.data.userId]
            return newCursors
          })
          break

        case EnhancedEventType.COLLABORATION_CURSOR_MOVED:
          setCursors(prev => ({
            ...prev,
            [event.data.userId]: event.data.data.position
          }))
          break

        case EnhancedEventType.COLLABORATION_EDIT_MADE:
          setEdits(prev => [...prev, event.data.data.edit])
          break
      }
    }
  }, [documentId])

  return { participants, cursors, edits }
}

// Hook for real-time AI operation updates
export function useAIOperationUpdates(operationId?: string) {
  const [operation, setOperation] = useState<any>(null)
  const [progress, setProgress] = useState<number>(0)
  const [results, setResults] = useState<any>(null)

  useEventSubscription([
    EnhancedEventType.AI_OPERATION_STARTED,
    EnhancedEventType.AI_OPERATION_COMPLETED
  ], (event) => {
    if (!operationId || event.data.operationId === operationId) {
      setOperation(event.data)
      setProgress(event.data.progress || 0)
      
      if (event.type === EnhancedEventType.AI_OPERATION_COMPLETED) {
        setResults(event.data.results)
      }
    }
  }, [operationId])

  useServiceBusSubscription(ServiceBusQueue.AI_OPERATIONS, (message) => {
    if (!operationId || message.operationId === operationId) {
      if (message.action === 'progress') {
        setProgress(message.progress)
      }
    }
  }, [operationId])

  return { operation, progress, results }
}

// Hook for system health monitoring
export function useSystemHealthMonitoring() {
  const [health, setHealth] = useState<Record<string, any>>({})
  const [alerts, setAlerts] = useState<any[]>([])

  useEventSubscription([
    EnhancedEventType.SYSTEM_HEALTH_CHANGED,
    EnhancedEventType.SYSTEM_ALERT
  ], (event) => {
    switch (event.type) {
      case EnhancedEventType.SYSTEM_HEALTH_CHANGED:
        setHealth(prev => ({
          ...prev,
          [event.data.service]: {
            status: event.data.status,
            message: event.data.message,
            timestamp: event.data.timestamp
          }
        }))
        break

      case EnhancedEventType.SYSTEM_ALERT:
        setAlerts(prev => [...prev, {
          id: Date.now(),
          severity: event.data.severity,
          message: event.data.message,
          timestamp: event.data.timestamp
        }])
        break
    }
  })

  useServiceBusSubscription(ServiceBusQueue.MONITORING_EVENTS, (message) => {
    if (message.health) {
      setHealth(message.health)
    }
    
    if (message.alerts) {
      setAlerts(prev => [...prev, ...message.alerts.map((alert: any) => ({
        ...alert,
        id: Date.now() + Math.random()
      }))])
    }
  })

  const clearAlert = useCallback((alertId: number) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }, [])

  return { health, alerts, clearAlert }
}

// Hook for real-time analytics updates
export function useRealTimeAnalytics() {
  const [metrics, setMetrics] = useState<Record<string, any>>({})
  const [reports, setReports] = useState<any[]>([])

  useEventSubscription([
    EnhancedEventType.ANALYTICS_METRIC_UPDATED,
    EnhancedEventType.ANALYTICS_REPORT_GENERATED
  ], (event) => {
    switch (event.type) {
      case EnhancedEventType.ANALYTICS_METRIC_UPDATED:
        setMetrics(prev => ({
          ...prev,
          [event.data.metric]: event.data.value
        }))
        break

      case EnhancedEventType.ANALYTICS_REPORT_GENERATED:
        setReports(prev => [...prev, event.data.report])
        break
    }
  })

  useServiceBusSubscription(ServiceBusQueue.ANALYTICS_EVENTS, (message) => {
    switch (message.type) {
      case 'metric_update':
        setMetrics(prev => ({
          ...prev,
          [message.metric]: message.value
        }))
        break

      case 'report_generated':
        setReports(prev => [...prev, message.report])
        break
    }
  })

  return { metrics, reports }
}

// Hook for real-time notifications
export function useRealTimeNotifications() {
  const [notifications, setNotifications] = useState<any[]>([])

  useServiceBusSubscription(ServiceBusQueue.NOTIFICATION_DELIVERY, (message) => {
    const notification = {
      id: message.notificationId,
      type: message.priority === 'high' ? 'error' : 'info',
      title: message.content.title,
      message: message.content.message,
      timestamp: new Date().toISOString(),
      data: message.content.data,
      actions: message.content.actions
    }

    setNotifications(prev => [notification, ...prev])
  })

  const removeNotification = useCallback((notificationId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }, [])

  const clearAllNotifications = useCallback(() => {
    setNotifications([])
  }, [])

  return { notifications, removeNotification, clearAllNotifications }
}

// Hook for enhanced event integration initialization
export function useEnhancedEventIntegration() {
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    let mounted = true

    const initialize = async () => {
      try {
        await enhancedEventIntegration.initialize()
        
        if (mounted) {
          setIsInitialized(true)
          setError(null)
        }
      } catch (err) {
        if (mounted) {
          setError(err instanceof Error ? err.message : 'Failed to initialize event integration')
          setIsInitialized(false)
        }
      }
    }

    initialize()

    return () => {
      mounted = false
      enhancedEventIntegration.cleanup()
    }
  }, [])

  return { isInitialized, error }
}

// Hook for publishing events
export function useEventPublisher() {
  const publishEvent = useCallback(async (eventType: EnhancedEventType, data: any) => {
    try {
      await eventGridService.publishEvent({ type: eventType, data } as any)
    } catch (error) {
      console.error('Failed to publish event:', error)
      throw error
    }
  }, [])

  const sendMessage = useCallback(async (queueName: ServiceBusQueue, message: any) => {
    try {
      await serviceBusService.sendMessage(message, queueName)
    } catch (error) {
      console.error('Failed to send message:', error)
      throw error
    }
  }, [])

  return { publishEvent, sendMessage }
}

// Hook for batch event operations
export function useBatchEventOperations() {
  const [operations, setOperations] = useState<any[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  const addOperation = useCallback((operation: any) => {
    setOperations(prev => [...prev, { ...operation, id: Date.now() + Math.random() }])
  }, [])

  const processBatch = useCallback(async () => {
    if (operations.length === 0) return

    setIsProcessing(true)
    
    try {
      // Process all operations
      await Promise.all(operations.map(async (op) => {
        if (op.type === 'event') {
          await eventGridService.publishEvent({ type: op.eventType, data: op.data } as any)
        } else if (op.type === 'message') {
          await serviceBusService.sendMessage(op.message, op.queueName)
        }
      }))

      setOperations([])
    } catch (error) {
      console.error('Failed to process batch operations:', error)
      throw error
    } finally {
      setIsProcessing(false)
    }
  }, [operations])

  const clearOperations = useCallback(() => {
    setOperations([])
  }, [])

  return {
    operations,
    isProcessing,
    addOperation,
    processBatch,
    clearOperations
  }
}
