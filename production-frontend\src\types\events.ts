/**
 * Event Types
 * Based on backend event system and Event Grid integration
 */

// ============================================================================
// CORE EVENT TYPES
// ============================================================================

export type EventType =
  | 'document.created'
  | 'document.updated'
  | 'document.deleted'
  | 'document.shared'
  | 'document.processed'
  | 'document.version.created'
  | 'project.created'
  | 'project.updated'
  | 'project.deleted'
  | 'user.created'
  | 'user.updated'
  | 'user.deleted'
  | 'organization.created'
  | 'organization.updated'
  | 'workflow.started'
  | 'workflow.completed'
  | 'workflow.failed'
  | 'security.threat_detected'
  | 'security.login_failed'
  | 'security.access_denied'
  | 'system.health_check'
  | 'system.performance_alert'
  | 'notification.sent'
  | 'integration.connected'
  | 'integration.failed'

export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical'

export type SecurityEventType = 
  | 'login_attempt'
  | 'login_success'
  | 'login_failure'
  | 'logout'
  | 'password_change'
  | 'mfa_enabled'
  | 'mfa_disabled'
  | 'permission_granted'
  | 'permission_revoked'
  | 'data_access'
  | 'data_export'
  | 'suspicious_activity'
  | 'threat_detected'
  | 'vulnerability_found'
  | 'compliance_violation'

export type AuditEventType = 
  | 'user_action'
  | 'system_action'
  | 'data_change'
  | 'configuration_change'
  | 'security_event'
  | 'compliance_event'

// ============================================================================
// EVENT INTERFACES
// ============================================================================

export interface BaseEvent {
  id: string
  eventType: EventType
  subject: string
  eventTime: string
  data: any
  dataVersion?: string
  source?: string
  correlationId?: string
  tenantId?: string
  organizationId?: string
  userId?: string
}

export interface SecurityEvent {
  id: string
  eventType: SecurityEventType
  severity: AlertSeverity
  source: string
  userId?: string
  organizationId: string
  ipAddress?: string
  userAgent?: string
  location?: string
  description: string
  details: Record<string, any>
  timestamp: string
  processed: boolean
  alertGenerated: boolean
  tenantId: string
}

export interface AuditEvent {
  id: string
  eventType: AuditEventType
  userId?: string
  organizationId: string
  resourceType: string
  resourceId: string
  action: string
  description: string
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: string
  tenantId: string
}

export interface SystemEvent {
  id: string
  eventType: EventType
  severity: AlertSeverity
  component: string
  message: string
  details: Record<string, any>
  timestamp: string
  resolved: boolean
  resolvedAt?: string
  resolvedBy?: string
}

export interface DocumentEvent extends BaseEvent {
  data: {
    documentId: string
    projectId?: string
    organizationId: string
    fileName: string
    fileSize?: number
    mimeType?: string
    version?: number
    changes?: string[]
    metadata?: Record<string, any>
  }
}

export interface WorkflowEvent extends BaseEvent {
  data: {
    workflowId: string
    executionId: string
    projectId?: string
    organizationId: string
    status: 'started' | 'running' | 'completed' | 'failed' | 'cancelled'
    progress?: number
    error?: string
    results?: any
    duration?: number
  }
}

export interface UserEvent extends BaseEvent {
  data: {
    userId: string
    organizationId?: string
    action: string
    changes?: Record<string, any>
    metadata?: Record<string, any>
  }
}

export interface NotificationEvent extends BaseEvent {
  data: {
    notificationId: string
    recipientId: string
    organizationId?: string
    type: string
    channel: string
    status: 'sent' | 'delivered' | 'failed' | 'read'
    error?: string
  }
}

// ============================================================================
// EVENT MONITORING & METRICS
// ============================================================================

export interface EventMetrics {
  totalEvents: number
  successfulEvents: number
  failedEvents: number
  averageProcessingTime: number
  lastEventTime?: Date
  eventsFiltered: number
  eventsDeadLettered: number
  eventsBatched: number
  throttleCount: number
}

export interface EventHealthStatus {
  isHealthy: boolean
  eventGridConnected: boolean
  serviceBusConnected: boolean
  processingErrors: number
  lastHealthCheck: string
  metrics: EventMetrics[]
  detailedMetrics?: EventMetrics[]
}

export interface EventSubscription {
  id: string
  name: string
  eventTypes: EventType[]
  endpoint: string
  filters?: EventFilter[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface EventFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn'
  value: string | string[]
}

export interface EventSchema {
  eventType: EventType
  version: string
  schema: any
  required: string[]
  description?: string
  examples?: any[]
}

// ============================================================================
// EVENT PROCESSING
// ============================================================================

export interface EventProcessingResult {
  eventId: string
  success: boolean
  processingTime: number
  error?: string
  retryCount?: number
  nextRetryAt?: string
}

export interface EventBatch {
  id: string
  events: BaseEvent[]
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: string
  processedAt?: string
  results?: EventProcessingResult[]
}

export interface DeadLetterEvent {
  id: string
  originalEventId: string
  originalEventType: EventType
  failureReason: string
  failureTime: string
  retryCount: number
  maxRetries: number
  data: any
  canRetry: boolean
}

// ============================================================================
// EVENT HANDLERS
// ============================================================================

export interface EventHandler {
  eventType: EventType
  handler: (event: BaseEvent) => Promise<void>
  priority?: number
  retryPolicy?: {
    maxRetries: number
    retryDelay: number
    exponentialBackoff: boolean
  }
}

export interface EventHandlerRegistry {
  register(handler: EventHandler): void
  unregister(eventType: EventType): void
  handle(event: BaseEvent): Promise<EventProcessingResult>
  getHandlers(eventType: EventType): EventHandler[]
}

// ============================================================================
// REAL-TIME EVENT STREAMING
// ============================================================================

export interface EventStream {
  id: string
  eventTypes: EventType[]
  filters?: EventFilter[]
  isActive: boolean
  lastEventTime?: string
  eventCount: number
}

export interface EventStreamMessage {
  streamId: string
  event: BaseEvent
  timestamp: string
  sequenceNumber: number
}

// ============================================================================
// EVENT REPLAY & RECOVERY
// ============================================================================

export interface EventReplayRequest {
  eventIds?: string[]
  eventTypes?: EventType[]
  startTime?: string
  endTime?: string
  filters?: EventFilter[]
  targetEndpoint?: string
}

export interface EventReplayResult {
  replayId: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  totalEvents: number
  processedEvents: number
  failedEvents: number
  startedAt: string
  completedAt?: string
  error?: string
}
