"use client";

import { TemplatePreview, TemplateSharingDialog } from "@/components/templates";
import { EmptyState } from "@/components/empty-state";
import { useToast } from "@/hooks/use-toast";
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription, AlertDialogFooter, AlertDialogCancel, AlertDialogAction } from '@/components/ui/alert-dialog';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useTemplate, useTemplateVersions, usePublishTemplate, useArchiveTemplate, useDeleteTemplate } from '@/hooks';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@radix-ui/react-tabs';
import { formatDistanceToNow } from 'date-fns';
import { FileStack, ArrowLeft, Share, Copy, Pencil, Eye, Settings, CheckCircle, Archive, Trash2, FileText, Calendar, Clock, User, Tag, Download, History } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';

export default function TemplateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("preview");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSharingDialogOpen, setIsSharingDialogOpen] = useState(false);

  // Get template ID from params
  if (!params) {
    return <div>Loading...</div>;
  }

  const templateId = params.id as string;

  // Fetch template data
  const {
    data: template,
    isLoading: isLoadingTemplate,
    error: templateError
  } = useTemplate(templateId);

  // Fetch template versions
  const {
    data: versions,
    isLoading: isLoadingVersions
  } = useTemplateVersions(templateId);

  // Template actions
  const { mutate: publishTemplate, isLoading: isPublishing } = usePublishTemplate();
  const { mutate: archiveTemplate, isLoading: isArchiving } = useArchiveTemplate();
  const { mutate: deleteTemplate, isLoading: isDeleting } = useDeleteTemplate();

  // Handle publish
  const handlePublish = async () => {
    try {
      await publishTemplate(templateId);
      toast({
        title: "Template published",
        description: "The template has been published successfully."
      });
    } catch (error: any) {
      toast({
        title: "Failed to publish template",
        description: error.message || "An error occurred while publishing the template.",
        variant: "destructive"
      });
    }
  };

  // Handle archive
  const handleArchive = async () => {
    try {
      await archiveTemplate(templateId);
      toast({
        title: "Template archived",
        description: "The template has been archived successfully."
      });
    } catch (error: any) {
      toast({
        title: "Failed to archive template",
        description: error.message || "An error occurred while archiving the template.",
        variant: "destructive"
      });
    }
  };

  // Handle delete
  const handleDelete = async () => {
    try {
      await deleteTemplate(templateId);
      toast({
        title: "Template deleted",
        description: "The template has been deleted successfully."
      });
      router.push("/templates");
    } catch (error: any) {
      toast({
        title: "Failed to delete template",
        description: error.message || "An error occurred while deleting the template.",
        variant: "destructive"
      });
    }
  };

  // Handle share
  const handleShare = async (data: any) => {
    try {
      const response = await fetch(`/templates/${templateId}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Failed to share template');
      }

      toast({
        title: "Template shared",
        description: "The template has been shared successfully."
      });
    } catch (error: any) {
      toast({
        title: "Failed to share template",
        description: error.message || "An error occurred while sharing the template.",
        variant: "destructive"
      });
    }
  };

  // Handle duplicate
  const handleDuplicate = async () => {
    try {
      const response = await fetch(`/templates/${templateId}/clone`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: `${template?.name || 'Template'} (Copy)` })
      });

      if (!response.ok) {
        throw new Error('Failed to duplicate template');
      }

      const result = await response.json();
      const duplicatedTemplate = result.template;

      toast({
        title: "Template duplicated",
        description: "The template has been duplicated successfully."
      });

      // Navigate to the new template
      router.push(`/templates/${duplicatedTemplate.id}`);
    } catch (error: any) {
      toast({
        title: "Failed to duplicate template",
        description: error.message || "An error occurred while duplicating the template.",
        variant: "destructive"
      });
    }
  };

  // Loading state
  if (isLoadingTemplate) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  // Error state
  if (templateError || !template) {
    return (
      <EmptyState
        icon={<FileStack className="h-10 w-10 text-muted-foreground" />}
        title="Template not found"
        description="The template you are looking for does not exist or you don't have permission to view it."
        action={
          <Button asChild>
            <Link href="/templates">
              Back to Templates
            </Link>
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/templates">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">{template.name}</h1>
            <p className="text-muted-foreground">
              {template.description || "No description provided"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setIsSharingDialogOpen(true)}
          >
            <Share className="mr-2 h-4 w-4" />
            Share
          </Button>

          <Button
            variant="outline"
            onClick={handleDuplicate}
          >
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </Button>

          <Button asChild>
            <Link href={`/templates/${templateId}/edit`}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 w-full md:w-[400px]">
              <TabsTrigger value="preview">
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="versions">
                <History className="mr-2 h-4 w-4" />
                Versions
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="preview" className="mt-6">
              <TemplatePreview
                template={template as any}
                previewResult={{
                  content: template.content ? JSON.stringify(template.content) : "<p>No content available</p>",
                  html: template.content && typeof template.content === 'object' && (template.content as any).sections
                    ? `<div>${(template.content as any).sections.map((s: any) => s.name || '').join('<br>')}</div>`
                    : "<p>No content available</p>"
                }}
                onPreview={async (values) => {
                  try {
                    const response = await fetch(`/templates/${templateId}/preview`, {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ values })
                    });

                    if (!response.ok) {
                      throw new Error('Failed to preview template');
                    }

                    const result = await response.json();
                    // The preview component will handle displaying the preview
                    return result.preview;
                  } catch (error: any) {
                    toast({
                      title: "Preview failed",
                      description: error.message || "Failed to generate template preview.",
                      variant: "destructive"
                    });
                  }
                }}
              />
            </TabsContent>

            <TabsContent value="versions" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Versions</CardTitle>
                  <CardDescription>
                    History of changes to this template
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingVersions ? (
                    <div className="space-y-4">
                      {Array.from({ length: 3 }).map((_, i) => (
                        <Skeleton key={i} className="h-16 w-full" />
                      ))}
                    </div>
                  ) : versions && versions.length > 0 ? (
                    <div className="space-y-4">
                      {versions.map((version: any) => (
                        <div
                          key={version.id}
                          className="flex items-center justify-between p-4 border rounded-lg"
                        >
                          <div className="flex items-center gap-4">
                            <div className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10">
                              <History className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <h3 className="font-medium">Version {version.version}</h3>
                              <p className="text-sm text-muted-foreground">
                                Created {formatDistanceToNow(new Date(version.createdAt))} ago
                              </p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/templates/${templateId}/versions/${version.id}`}>
                              View
                            </Link>
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
                      <History className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-muted-foreground text-center">
                        No versions available for this template.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Settings</CardTitle>
                  <CardDescription>
                    Manage template settings and actions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-sm font-medium mb-2">Status</h3>
                      <div className="flex items-center gap-4">
                        <Badge
                          variant={
                            (template.status as string) === 'PUBLISHED'
                              ? "default"
                              : (template.status as string) === 'ARCHIVED'
                                ? "secondary"
                                : "outline"
                          }
                        >
                          {template.status}
                        </Badge>

                        {(template.status as string) === 'DRAFT' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handlePublish}
                            disabled={isPublishing}
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            {isPublishing ? "Publishing..." : "Publish"}
                          </Button>
                        )}

                        {(template.status as string) !== 'ARCHIVED' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleArchive}
                            disabled={isArchiving}
                          >
                            <Archive className="mr-2 h-4 w-4" />
                            {isArchiving ? "Archiving..." : "Archive"}
                          </Button>
                        )}
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Default Template</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant={(template as any).isDefault ? "default" : "outline"}>
                          {(template as any).isDefault ? "Default" : "Not Default"}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <h3 className="text-sm font-medium text-destructive mb-2">Danger Zone</h3>
                    <Button
                      variant="destructive"
                      onClick={() => setIsDeleteDialogOpen(true)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Template
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Template Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-1">Type</h3>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span>{template.type}</span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {template.tags && template.tags.length > 0 ? (
                    template.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">{tag}</Badge>
                    ))
                  ) : (
                    <span className="text-muted-foreground text-sm">No tags</span>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Created</h3>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDistanceToNow(new Date(template.createdAt))} ago</span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Last Updated</h3>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{formatDistanceToNow(new Date(template.updatedAt))} ago</span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Created By</h3>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span>{template.createdBy}</span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium mb-1">Version</h3>
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <span>{(template as any).version || '1.0'}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={async () => {
                  try {
                    const response = await fetch(`/templates/${templateId}/download`);

                    if (!response.ok) {
                      throw new Error('Failed to download template');
                    }

                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${template?.name || 'template'}.json`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);

                    toast({
                      title: "Download started",
                      description: "Template download has started."
                    });
                  } catch (error: any) {
                    toast({
                      title: "Download failed",
                      description: error.message || "Failed to download template.",
                      variant: "destructive"
                    });
                  }
                }}
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the template "{template.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {template && (
        <TemplateSharingDialog
          template={template as any}
          open={isSharingDialogOpen}
          onOpenChange={setIsSharingDialogOpen}
          onShare={handleShare}
        />
      )}
    </div>
  );
}
