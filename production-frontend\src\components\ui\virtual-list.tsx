"use client";

import React, { useRef, useEffect, useState } from "react";
import { useVirtualizer } from "@tanstack/react-virtual";
import { cn } from "@/lib/utils";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorDisplay } from "@/components/ui/error-display";

export interface VirtualListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  height?: number | string;
  width?: number | string;
  itemHeight?: number;
  overscan?: number;
  className?: string;
  isLoading?: boolean;
  isError?: boolean;
  error?: any;
  onRetry?: () => void;
  emptyComponent?: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  scrollToIndex?: number;
}

/**
 * A virtualized list component for efficiently rendering large lists
 */
export function VirtualList<T>({
  items,
  renderItem,
  height = 400,
  width = "100%",
  itemHeight = 50,
  overscan = 10,
  className,
  isLoading = false,
  isError = false,
  error,
  onRetry,
  emptyComponent,
  loadingComponent,
  errorComponent,
  onEndReached,
  endReachedThreshold = 0.8,
  scrollToIndex,
}: VirtualListProps<T>) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [hasCalledEndReached, setHasCalledEndReached] = useState(false);

  // Create virtualizer
  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => itemHeight,
    overscan,
  });

  // Handle scroll to index
  useEffect(() => {
    if (scrollToIndex !== undefined && items.length > 0) {
      virtualizer.scrollToIndex(scrollToIndex, { align: "center" });
    }
  }, [scrollToIndex, items.length, virtualizer]);

  // Handle end reached
  useEffect(() => {
    if (!onEndReached || isLoading || hasCalledEndReached) return;

    const scrollElement = parentRef.current;
    if (!scrollElement) return;

    const { scrollHeight, scrollTop, clientHeight } = scrollElement;
    const scrollPosition = scrollTop + clientHeight;
    const scrollThreshold = scrollHeight * endReachedThreshold;

    if (scrollPosition >= scrollThreshold) {
      setHasCalledEndReached(true);
      onEndReached();
    }
  }, [onEndReached, isLoading, hasCalledEndReached, endReachedThreshold]);

  // Reset end reached flag when items change
  useEffect(() => {
    setHasCalledEndReached(false);
  }, [items]);

  // Handle loading state
  if (isLoading && items.length === 0) {
    return loadingComponent || <LoadingState variant="skeleton" count={5} />;
  }

  // Handle error state
  if (isError) {
    return (
      errorComponent || (
        <ErrorDisplay
          title="Failed to load data"
          error={error}
          onRetry={onRetry}
          variant="card"
        />
      )
    );
  }

  // Handle empty state
  if (items.length === 0) {
    return (
      emptyComponent || (
        <div className="text-center p-6">
          <p className="text-muted-foreground">No items to display</p>
        </div>
      )
    );
  }

  return (
    <div
      ref={parentRef}
      className={cn("overflow-auto", className)}
      style={{
        height: typeof height === "string" ? height : `${height}px`,
        width: typeof width === "string" ? width : `${width}px`,
      }}
      onScroll={() => {
        if (hasCalledEndReached) return;
        
        const scrollElement = parentRef.current;
        if (!scrollElement) return;
        
        const { scrollHeight, scrollTop, clientHeight } = scrollElement;
        const scrollPosition = scrollTop + clientHeight;
        const scrollThreshold = scrollHeight * endReachedThreshold;
        
        if (scrollPosition >= scrollThreshold && onEndReached) {
          setHasCalledEndReached(true);
          onEndReached();
        }
      }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: "100%",
          position: "relative",
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          >
            {renderItem(items[virtualItem.index], virtualItem.index)}
          </div>
        ))}
      </div>
      {isLoading && items.length > 0 && (
        <div className="py-4">
          <LoadingState variant="inline" title="Loading more..." />
        </div>
      )}
    </div>
  );
}
