/**
 * Role and Permission Types
 * Defines role-based access control types
 */

import type { ID, Timestamp } from './index'

// Re-export ID for external use
export type { ID }

// Permission types
export interface Permission {
  id: ID
  name: string
  description: string
  resource: string
  action: string
  scope?: 'global' | 'organization' | 'project' | 'own'
  conditions?: Record<string, any>
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Role types
export interface Role {
  id: ID
  name: string
  displayName: string
  description: string
  permissions: Permission[]
  isSystem: boolean
  isSystemRole: boolean // Alias for isSystem
  isDefault: boolean
  scope: RoleScope
  organizationId?: ID
  metadata?: Record<string, any>
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: ID
  updatedBy: ID
}

// Role scope enum
export enum RoleScope {
  GLOBAL = 'global',
  SYSTEM = 'system',
  ORGANIZATION = 'organization',
  PROJECT = 'project'
}

// Role assignment types
export interface RoleAssignment {
  id: ID
  userId: ID
  roleId: ID
  organizationId?: ID
  projectId?: ID
  scope: RoleScope
  assignedBy: ID
  assignedAt: Timestamp
  expiresAt?: Timestamp
  isActive: boolean
  metadata?: Record<string, any>
}

// Role assignment with populated role data
export interface RoleAssignmentWithRole extends RoleAssignment {
  role: Role
  user?: {
    id: ID
    email: string
    firstName: string
    lastName: string
    displayName: string
    avatar?: string
  }
  assignedByUser?: {
    id: ID
    email: string
    firstName: string
    lastName: string
    displayName: string
  }
}

// User with roles
export interface UserWithRoles {
  id: ID
  email: string
  firstName: string
  lastName: string
  displayName: string
  roles: Role[]
  roleAssignments: RoleAssignment[]
  permissions: Permission[]
  effectivePermissions: Permission[]
}

// Role creation and update types
export interface CreateRoleRequest {
  name: string
  displayName: string
  description: string
  permissionIds: ID[]
  organizationId?: ID
  isDefault?: boolean
  metadata?: Record<string, any>
}

export interface UpdateRoleRequest {
  name?: string
  displayName?: string
  description?: string
  permissionIds?: ID[]
  isDefault?: boolean
  metadata?: Record<string, any>
}

// Role assignment requests
export interface CreateRoleAssignmentRequest {
  userId: ID
  roleId: ID
  organizationId?: ID
  projectId?: ID
  scope: RoleScope
  expiresAt?: Timestamp
  metadata?: Record<string, any>
}

export interface UpdateRoleAssignmentRequest {
  expiresAt?: Timestamp
  isActive?: boolean
  metadata?: Record<string, any>
}

// Permission check types
export interface PermissionCheck {
  permission: string
  resource?: any
  context?: {
    organizationId?: ID
    projectId?: ID
    userId?: ID
  }
}

export interface PermissionResult {
  allowed: boolean
  reason?: string
  conditions?: Record<string, any>
}

// Role hierarchy types
export interface RoleHierarchy {
  parentRoleId: ID
  childRoleId: ID
  inheritPermissions: boolean
  createdAt: Timestamp
}

// System roles enum
export enum SystemRole {
  SUPER_ADMIN = 'super_admin',
  SYSTEM_ADMIN = 'system_admin',
  ORGANIZATION_ADMIN = 'organization_admin',
  PROJECT_ADMIN = 'project_admin',
  ADMIN = 'admin',
  MANAGER = 'manager',
  USER = 'user',
  VIEWER = 'viewer',
  GUEST = 'guest'
}

// Team roles enum
export enum TeamRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

// Permission scopes
export enum PermissionScope {
  GLOBAL = 'global',
  ORGANIZATION = 'organization',
  PROJECT = 'project',
  OWN = 'own'
}

// Common permission actions
export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage',
  EXECUTE = 'execute',
  APPROVE = 'approve',
  SHARE = 'share',
  COMMENT = 'comment',
  EXPORT = 'export',
  IMPORT = 'import',

  // Specific actions
  CREATE_ORGANIZATION = 'create_organization',
  UPDATE_ORGANIZATION = 'update_organization',
  DELETE_ORGANIZATION = 'delete_organization',
  VIEW_ORGANIZATION = 'view_organization',
  MANAGE_ORGANIZATION = 'manage_organization',
  VIEW_ORGANIZATION_MEMBERS = 'view_organization_members',
  INVITE_ORGANIZATION_MEMBER = 'invite_organization_member',
  REMOVE_ORGANIZATION_MEMBER = 'remove_organization_member',
  MANAGE_ORGANIZATION_MEMBERS = 'manage_organization_members',

  CREATE_PROJECT = 'create_project',
  UPDATE_PROJECT = 'update_project',
  DELETE_PROJECT = 'delete_project',
  VIEW_PROJECT = 'view_project',
  MANAGE_PROJECT = 'manage_project',
  ADD_PROJECT_MEMBER = 'add_project_member',
  REMOVE_PROJECT_MEMBER = 'remove_project_member',
  MANAGE_PROJECT_MEMBERS = 'manage_project_members',

  CREATE_USER = 'create_user',
  UPDATE_USER = 'update_user',
  DELETE_USER = 'delete_user',
  VIEW_USER = 'view_user',
  MANAGE_USER = 'manage_user',

  CREATE_ROLE = 'create_role',
  UPDATE_ROLE = 'update_role',
  DELETE_ROLE = 'delete_role',
  VIEW_ROLE = 'view_role',
  MANAGE_ROLE = 'manage_role',

  // Document permissions
  UPLOAD_DOCUMENT = 'upload_document',
  VIEW_DOCUMENT = 'view_document',
  UPDATE_DOCUMENT = 'update_document',
  DELETE_DOCUMENT = 'delete_document',
  SHARE_DOCUMENT = 'share_document',
  DOWNLOAD_DOCUMENT = 'download_document',
  PROCESS_DOCUMENT = 'process_document',
  COMMENT_DOCUMENT = 'comment_document',
  EDIT_ANY_COMMENT = 'edit_any_comment',
  DELETE_ANY_COMMENT = 'delete_any_comment',

  // Workflow permissions
  CREATE_WORKFLOW = 'create_workflow',
  UPDATE_WORKFLOW = 'update_workflow',
  DELETE_WORKFLOW = 'delete_workflow',
  EXECUTE_WORKFLOW = 'execute_workflow',
  VIEW_WORKFLOW = 'view_workflow',
  MANAGE_WORKFLOW = 'manage_workflow',
  APPROVE_WORKFLOW = 'approve_workflow',
  REJECT_WORKFLOW = 'reject_workflow',
  ASSIGN_WORKFLOW = 'assign_workflow',

  // Template permissions
  CREATE_TEMPLATE = 'create_template',
  UPDATE_TEMPLATE = 'update_template',
  DELETE_TEMPLATE = 'delete_template',
  SHARE_TEMPLATE = 'share_template',
  VIEW_TEMPLATE = 'view_template',
  USE_TEMPLATE = 'use_template',

  // Analytics and reporting permissions
  VIEW_ANALYTICS = 'view_analytics',
  CREATE_REPORT = 'create_report',
  UPDATE_REPORT = 'update_report',
  DELETE_REPORT = 'delete_report',
  GENERATE_REPORT = 'generate_report',
  EXPORT_DATA = 'export_data',

  // Permission management
  VIEW_USER_PERMISSIONS = 'view_user_permissions',
  CHECK_USER_PERMISSIONS = 'check_user_permissions',
  GRANT_PERMISSION = 'grant_permission',
  REVOKE_PERMISSION = 'revoke_permission'
}

// Common resources
export enum PermissionResource {
  USERS = 'users',
  ROLES = 'roles',
  PERMISSIONS = 'permissions',
  ORGANIZATIONS = 'organizations',
  PROJECTS = 'projects',
  DOCUMENTS = 'documents',
  TEMPLATES = 'templates',
  WORKFLOWS = 'workflows',
  REPORTS = 'reports',
  SETTINGS = 'settings',
  AUDIT_LOGS = 'audit_logs'
}

// Role filter and search types
export interface RoleFilter {
  organizationId?: ID
  isSystem?: boolean
  isDefault?: boolean
  hasPermission?: string
  search?: string
}

export interface RoleSearchResult {
  roles: Role[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// Role analytics types
export interface RoleAnalytics {
  roleId: ID
  roleName: string
  userCount: number
  permissionCount: number
  assignmentCount: number
  lastAssignedAt?: Timestamp
  usage: {
    daily: number[]
    weekly: number[]
    monthly: number[]
  }
}

// Bulk operations
export interface BulkRoleAssignmentRequest {
  userIds: ID[]
  roleId: ID
  organizationId?: ID
  projectId?: ID
  scope: RoleScope
  expiresAt?: Timestamp
}

export interface BulkRoleAssignmentResult {
  successful: ID[]
  failed: Array<{
    userId: ID
    error: string
  }>
  total: number
  successCount: number
  failureCount: number
}
