/**
 * Shared MSAL Node.js Configuration for Azure AD B2C
 * Handles B2C-specific endpoint resolution and metadata
 */

import { Configuration } from '@azure/msal-node'

// Validate required environment variables
const validateEnvVars = () => {
  const required = [
    'AZURE_AD_B2C_CLIENT_ID',
    'AZURE_AD_B2C_CLIENT_SECRET',
    'AZURE_AD_B2C_TENANT_NAME',
    'AZURE_AD_B2C_AUTHORITY_DOMAIN'
  ]
  
  const missing = required.filter(key => !process.env[key])
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

// Create B2C authority metadata for a specific policy
const createB2CAuthorityMetadata = (policy: string) => {
  const tenantName = process.env.AZURE_AD_B2C_TENANT_NAME!
  const authorityDomain = process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN!
  
  return JSON.stringify({
    "token_endpoint": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/oauth2/v2.0/token`,
    "token_endpoint_auth_methods_supported": ["client_secret_post", "private_key_jwt", "client_secret_basic"],
    "jwks_uri": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/discovery/v2.0/keys`,
    "response_modes_supported": ["query", "fragment", "form_post"],
    "subject_types_supported": ["pairwise"],
    "id_token_signing_alg_values_supported": ["RS256"],
    "response_types_supported": ["code", "id_token", "code id_token", "id_token token"],
    "scopes_supported": ["openid", "profile", "email", "offline_access"],
    "issuer": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/v2.0/`,
    "request_uri_parameter_supported": false,
    "userinfo_endpoint": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/openid/v2.0/userinfo`,
    "authorization_endpoint": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/oauth2/v2.0/authorize`,
    "device_authorization_endpoint": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/oauth2/v2.0/devicecode`,
    "http_logout_supported": true,
    "frontchannel_logout_supported": true,
    "end_session_endpoint": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/oauth2/v2.0/logout`,
    "claims_supported": ["sub", "iss", "cloud_instance_name", "cloud_instance_host_name", "cloud_graph_host_name", "msgraph_host", "aud", "exp", "iat", "auth_time", "acr", "nonce", "preferred_username", "name", "tid", "ver", "at_hash", "c_hash", "email"]
  })
}

// Create B2C cloud discovery metadata
const createB2CCloudDiscoveryMetadata = () => {
  const tenantName = process.env.AZURE_AD_B2C_TENANT_NAME!
  const authorityDomain = process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN!
  
  return JSON.stringify({
    "tenant_discovery_endpoint": `https://${authorityDomain}/${tenantName}.onmicrosoft.com/v2.0/.well-known/openid_configuration`,
    "api-version": "1.1",
    "metadata": [
      {
        "preferred_network": authorityDomain,
        "preferred_cache": authorityDomain,
        "aliases": [authorityDomain]
      }
    ]
  })
}

// Create MSAL configuration for a specific B2C policy
export const createMSALConfig = (policy: string): Configuration => {
  validateEnvVars()
  
  const tenantName = process.env.AZURE_AD_B2C_TENANT_NAME!
  const authorityDomain = process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN!
  const clientId = process.env.AZURE_AD_B2C_CLIENT_ID!
  const clientSecret = process.env.AZURE_AD_B2C_CLIENT_SECRET!
  
  return {
    auth: {
      clientId,
      clientSecret,
      authority: `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}`,
      knownAuthorities: [authorityDomain],
      cloudDiscoveryMetadata: createB2CCloudDiscoveryMetadata(),
      authorityMetadata: createB2CAuthorityMetadata(policy)
    },
    system: {
      loggerOptions: {
        loggerCallback: (level, message, containsPii) => {
          if (containsPii) return
          console.log(`[MSAL Server] ${message}`)
        },
        piiLoggingEnabled: false,
        logLevel: process.env.NODE_ENV === 'development' ? 4 : 2, // Verbose in dev, Warning in prod
      },
    },
  }
}

// Default policies
export const B2C_POLICIES = {
  SIGN_IN: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNIN_POLICY || 'B2C_1_SI',
  SIGN_UP: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SIGNUP_POLICY || 'B2C_1_SU',
  SIGN_UP_SIGN_IN: process.env.NEXT_PUBLIC_AZURE_AD_B2C_SUSI_POLICY || 'B2C_1_SUSI',
  PASSWORD_RESET: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PASSWORD_RESET_POLICY || 'B2C_1_passwordreset1',
  PROFILE_EDIT: process.env.NEXT_PUBLIC_AZURE_AD_B2C_PROFILE_EDIT_POLICY || 'B2C_1_profileedit1'
}

// Default scopes for B2C
export const B2C_SCOPES = ['openid', 'profile', 'email', 'offline_access']

// Helper function to get redirect URI
export const getRedirectUri = () => {
  return process.env.NEXT_PUBLIC_AZURE_AD_B2C_REDIRECT_URI || 'http://localhost:3000/api/auth/callback'
}

// Helper function to get post logout redirect URI
export const getPostLogoutRedirectUri = () => {
  return process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI || 'http://localhost:3000'
}
