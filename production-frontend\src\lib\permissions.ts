/**
 * Permissions Library
 * Centralized permission definitions and utilities
 */

import type { Permission, PermissionCheck, PermissionResult } from '../types/role'

// Permission definitions
export const PERMISSIONS = {
  // User management
  USERS_CREATE: 'users:create',
  USERS_READ: 'users:read',
  USERS_UPDATE: 'users:update',
  USERS_DELETE: 'users:delete',
  USERS_MANAGE: 'users:manage',
  USERS_IMPERSONATE: 'users:impersonate',

  // Role management
  ROLES_CREATE: 'roles:create',
  ROLES_READ: 'roles:read',
  ROLES_UPDATE: 'roles:update',
  ROLES_DELETE: 'roles:delete',
  ROLES_ASSIGN: 'roles:assign',
  R<PERSON><PERSON>_MANAGE: 'roles:manage',

  // Organization management
  ORGANIZATIONS_CREATE: 'organizations:create',
  ORGANIZATIONS_READ: 'organizations:read',
  ORGANIZATIONS_UPDATE: 'organizations:update',
  ORGANIZATIONS_DELETE: 'organizations:delete',
  ORGANIZATIONS_MANAGE: 'organizations:manage',
  ORGANIZATIONS_INVITE: 'organizations:invite',

  // Project management
  PROJECTS_CREATE: 'projects:create',
  PROJECTS_READ: 'projects:read',
  PROJECTS_UPDATE: 'projects:update',
  PROJECTS_DELETE: 'projects:delete',
  PROJECTS_MANAGE: 'projects:manage',
  PROJECTS_SHARE: 'projects:share',

  // Document management
  DOCUMENTS_CREATE: 'documents:create',
  DOCUMENTS_READ: 'documents:read',
  DOCUMENTS_UPDATE: 'documents:update',
  DOCUMENTS_DELETE: 'documents:delete',
  DOCUMENTS_SHARE: 'documents:share',
  DOCUMENTS_COMMENT: 'documents:comment',
  DOCUMENTS_EXPORT: 'documents:export',
  DOCUMENTS_PROCESS: 'documents:process',

  // Template management
  TEMPLATES_CREATE: 'templates:create',
  TEMPLATES_READ: 'templates:read',
  TEMPLATES_UPDATE: 'templates:update',
  TEMPLATES_DELETE: 'templates:delete',
  TEMPLATES_PUBLISH: 'templates:publish',
  TEMPLATES_SHARE: 'templates:share',

  // Workflow management
  WORKFLOWS_CREATE: 'workflows:create',
  WORKFLOWS_READ: 'workflows:read',
  WORKFLOWS_UPDATE: 'workflows:update',
  WORKFLOWS_DELETE: 'workflows:delete',
  WORKFLOWS_EXECUTE: 'workflows:execute',
  WORKFLOWS_APPROVE: 'workflows:approve',
  WORKFLOWS_MANAGE: 'workflows:manage',

  // System administration
  SYSTEM_SETTINGS: 'system:settings',
  SYSTEM_AUDIT: 'system:audit',
  SYSTEM_BACKUP: 'system:backup',
  SYSTEM_MAINTENANCE: 'system:maintenance',

  // Analytics and reporting
  ANALYTICS_VIEW: 'analytics:view',
  ANALYTICS_EXPORT: 'analytics:export',
  REPORTS_CREATE: 'reports:create',
  REPORTS_VIEW: 'reports:view',
  REPORTS_EXPORT: 'reports:export',
} as const

// Permission groups for easier management
export const PERMISSION_GROUPS = {
  USER_MANAGEMENT: [
    PERMISSIONS.USERS_CREATE,
    PERMISSIONS.USERS_READ,
    PERMISSIONS.USERS_UPDATE,
    PERMISSIONS.USERS_DELETE,
    PERMISSIONS.USERS_MANAGE,
  ],
  
  ROLE_MANAGEMENT: [
    PERMISSIONS.ROLES_CREATE,
    PERMISSIONS.ROLES_READ,
    PERMISSIONS.ROLES_UPDATE,
    PERMISSIONS.ROLES_DELETE,
    PERMISSIONS.ROLES_ASSIGN,
    PERMISSIONS.ROLES_MANAGE,
  ],
  
  ORGANIZATION_MANAGEMENT: [
    PERMISSIONS.ORGANIZATIONS_CREATE,
    PERMISSIONS.ORGANIZATIONS_READ,
    PERMISSIONS.ORGANIZATIONS_UPDATE,
    PERMISSIONS.ORGANIZATIONS_DELETE,
    PERMISSIONS.ORGANIZATIONS_MANAGE,
    PERMISSIONS.ORGANIZATIONS_INVITE,
  ],
  
  PROJECT_MANAGEMENT: [
    PERMISSIONS.PROJECTS_CREATE,
    PERMISSIONS.PROJECTS_READ,
    PERMISSIONS.PROJECTS_UPDATE,
    PERMISSIONS.PROJECTS_DELETE,
    PERMISSIONS.PROJECTS_MANAGE,
    PERMISSIONS.PROJECTS_SHARE,
  ],
  
  DOCUMENT_MANAGEMENT: [
    PERMISSIONS.DOCUMENTS_CREATE,
    PERMISSIONS.DOCUMENTS_READ,
    PERMISSIONS.DOCUMENTS_UPDATE,
    PERMISSIONS.DOCUMENTS_DELETE,
    PERMISSIONS.DOCUMENTS_SHARE,
    PERMISSIONS.DOCUMENTS_COMMENT,
    PERMISSIONS.DOCUMENTS_EXPORT,
    PERMISSIONS.DOCUMENTS_PROCESS,
  ],
  
  TEMPLATE_MANAGEMENT: [
    PERMISSIONS.TEMPLATES_CREATE,
    PERMISSIONS.TEMPLATES_READ,
    PERMISSIONS.TEMPLATES_UPDATE,
    PERMISSIONS.TEMPLATES_DELETE,
    PERMISSIONS.TEMPLATES_PUBLISH,
    PERMISSIONS.TEMPLATES_SHARE,
  ],
  
  WORKFLOW_MANAGEMENT: [
    PERMISSIONS.WORKFLOWS_CREATE,
    PERMISSIONS.WORKFLOWS_READ,
    PERMISSIONS.WORKFLOWS_UPDATE,
    PERMISSIONS.WORKFLOWS_DELETE,
    PERMISSIONS.WORKFLOWS_EXECUTE,
    PERMISSIONS.WORKFLOWS_APPROVE,
    PERMISSIONS.WORKFLOWS_MANAGE,
  ],
  
  SYSTEM_ADMINISTRATION: [
    PERMISSIONS.SYSTEM_SETTINGS,
    PERMISSIONS.SYSTEM_AUDIT,
    PERMISSIONS.SYSTEM_BACKUP,
    PERMISSIONS.SYSTEM_MAINTENANCE,
  ],
  
  ANALYTICS_REPORTING: [
    PERMISSIONS.ANALYTICS_VIEW,
    PERMISSIONS.ANALYTICS_EXPORT,
    PERMISSIONS.REPORTS_CREATE,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_EXPORT,
  ],
}

// Default role permissions
export const DEFAULT_ROLE_PERMISSIONS = {
  super_admin: Object.values(PERMISSIONS),
  
  admin: [
    ...PERMISSION_GROUPS.USER_MANAGEMENT,
    ...PERMISSION_GROUPS.ROLE_MANAGEMENT,
    ...PERMISSION_GROUPS.ORGANIZATION_MANAGEMENT,
    ...PERMISSION_GROUPS.PROJECT_MANAGEMENT,
    ...PERMISSION_GROUPS.DOCUMENT_MANAGEMENT,
    ...PERMISSION_GROUPS.TEMPLATE_MANAGEMENT,
    ...PERMISSION_GROUPS.WORKFLOW_MANAGEMENT,
    ...PERMISSION_GROUPS.ANALYTICS_REPORTING,
  ],
  
  manager: [
    PERMISSIONS.USERS_READ,
    ...PERMISSION_GROUPS.PROJECT_MANAGEMENT,
    ...PERMISSION_GROUPS.DOCUMENT_MANAGEMENT,
    ...PERMISSION_GROUPS.TEMPLATE_MANAGEMENT,
    ...PERMISSION_GROUPS.WORKFLOW_MANAGEMENT,
    PERMISSIONS.ANALYTICS_VIEW,
    PERMISSIONS.REPORTS_VIEW,
  ],
  
  user: [
    PERMISSIONS.USERS_READ,
    PERMISSIONS.PROJECTS_READ,
    PERMISSIONS.PROJECTS_CREATE,
    ...PERMISSION_GROUPS.DOCUMENT_MANAGEMENT,
    PERMISSIONS.TEMPLATES_READ,
    PERMISSIONS.TEMPLATES_CREATE,
    PERMISSIONS.WORKFLOWS_READ,
    PERMISSIONS.WORKFLOWS_EXECUTE,
  ],
  
  viewer: [
    PERMISSIONS.USERS_READ,
    PERMISSIONS.PROJECTS_READ,
    PERMISSIONS.DOCUMENTS_READ,
    PERMISSIONS.TEMPLATES_READ,
    PERMISSIONS.WORKFLOWS_READ,
  ],
  
  guest: [
    PERMISSIONS.DOCUMENTS_READ,
    PERMISSIONS.TEMPLATES_READ,
  ],
}

// Permission utility functions
export class PermissionUtils {
  /**
   * Check if a permission string matches a pattern
   */
  static matchesPattern(permission: string, pattern: string): boolean {
    if (pattern === '*') return true
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1)
      return permission.startsWith(prefix)
    }
    return permission === pattern
  }

  /**
   * Check if user has permission
   */
  static hasPermission(
    userPermissions: string[],
    requiredPermission: string,
    resource?: any
  ): boolean {
    // Check direct permission
    if (userPermissions.includes(requiredPermission)) {
      return true
    }

    // Check wildcard permissions
    const [resourceType, action] = requiredPermission.split(':')
    const wildcardPermission = `${resourceType}:*`
    if (userPermissions.includes(wildcardPermission)) {
      return true
    }

    // Check global wildcard
    if (userPermissions.includes('*')) {
      return true
    }

    // Check resource-specific permissions
    if (resource) {
      const ownPermission = `own:${requiredPermission}`
      if (userPermissions.includes(ownPermission) && 
          (resource.createdBy === resource.currentUserId || 
           resource.ownerId === resource.currentUserId)) {
        return true
      }
    }

    return false
  }

  /**
   * Get effective permissions for a user
   */
  static getEffectivePermissions(
    rolePermissions: string[][],
    directPermissions: string[] = []
  ): string[] {
    const allPermissions = new Set([
      ...directPermissions,
      ...rolePermissions.flat()
    ])
    return Array.from(allPermissions)
  }

  /**
   * Check if user has any of the required permissions
   */
  static hasAnyPermission(
    userPermissions: string[],
    requiredPermissions: string[]
  ): boolean {
    return requiredPermissions.some(permission =>
      this.hasPermission(userPermissions, permission)
    )
  }

  /**
   * Check if user has all required permissions
   */
  static hasAllPermissions(
    userPermissions: string[],
    requiredPermissions: string[]
  ): boolean {
    return requiredPermissions.every(permission =>
      this.hasPermission(userPermissions, permission)
    )
  }

  /**
   * Filter permissions by resource type
   */
  static filterByResource(
    permissions: string[],
    resourceType: string
  ): string[] {
    return permissions.filter(permission =>
      permission.startsWith(`${resourceType}:`)
    )
  }

  /**
   * Get permission description
   */
  static getPermissionDescription(permission: string): string {
    const descriptions: Record<string, string> = {
      [PERMISSIONS.USERS_CREATE]: 'Create new users',
      [PERMISSIONS.USERS_READ]: 'View user information',
      [PERMISSIONS.USERS_UPDATE]: 'Update user information',
      [PERMISSIONS.USERS_DELETE]: 'Delete users',
      [PERMISSIONS.USERS_MANAGE]: 'Full user management',
      [PERMISSIONS.DOCUMENTS_CREATE]: 'Create new documents',
      [PERMISSIONS.DOCUMENTS_READ]: 'View documents',
      [PERMISSIONS.DOCUMENTS_UPDATE]: 'Edit documents',
      [PERMISSIONS.DOCUMENTS_DELETE]: 'Delete documents',
      [PERMISSIONS.DOCUMENTS_SHARE]: 'Share documents',
      // Add more descriptions as needed
    }
    
    return descriptions[permission] || permission
  }
}

// Re-export enums for convenience
export { PermissionAction, PermissionResource, PermissionScope } from '../types/role'

// Additional utility functions
export function getUserPermissions(user: any): string[] {
  if (!user) return []

  const rolePermissions = user.roles?.flatMap((role: any) => role.permissions || []) || []
  const directPermissions = user.permissions || []

  return [...new Set([...rolePermissions, ...directPermissions])]
}

/**
 * Check if user has a specific permission
 */
export function hasPermission(
  user: any,
  permission: string,
  resource?: any
): boolean {
  if (!user) return false

  const userPermissions = getUserPermissions(user)
  return PermissionUtils.hasPermission(userPermissions, permission, resource)
}

/**
 * Check if user has all required permissions
 */
export function hasAllPermissions(
  user: any,
  permissions: string[],
  resource?: any
): boolean {
  if (!user) return false

  const userPermissions = getUserPermissions(user)
  return PermissionUtils.hasAllPermissions(userPermissions, permissions)
}

/**
 * Check if user has any of the required permissions
 */
export function hasAnyPermission(
  user: any,
  permissions: string[],
  resource?: any
): boolean {
  if (!user) return false

  const userPermissions = getUserPermissions(user)
  return PermissionUtils.hasAnyPermission(userPermissions, permissions)
}

export default PermissionUtils
