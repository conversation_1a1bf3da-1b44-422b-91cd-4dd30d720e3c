'use client'

/**
 * System Health Monitor Component
 * Real-time system monitoring with Event Grid and Service Bus integration
 */

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Activity,
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Server,
  Database,
  Zap,
  Bell,
  X
} from 'lucide-react'
import { useSystemHealthMonitoring, useRealTimeAnalytics } from '@/hooks/useEnhancedEvents'

export function SystemHealthMonitor() {
  const { health, alerts, clearAlert } = useSystemHealthMonitoring()
  const { metrics } = useRealTimeAnalytics()

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <Server className="h-4 w-4 text-gray-500" />
    }
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'unhealthy':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <Bell className="h-4 w-4 text-blue-500" />
    }
  }

  const services = [
    { name: 'API Gateway', key: 'api-gateway', icon: Server },
    { name: 'Document Service', key: 'document-service', icon: Database },
    { name: 'Workflow Engine', key: 'workflow-engine', icon: Zap },
    { name: 'AI Service', key: 'ai-service', icon: Activity },
    { name: 'Notification Service', key: 'notification-service', icon: Bell },
    { name: 'Storage Service', key: 'storage-service', icon: Database }
  ]

  return (
    <div className="space-y-6">
      {/* System Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Health Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {services.map((service) => {
              const serviceHealth = health[service.key]
              const Icon = service.icon
              
              return (
                <div key={service.key} className="text-center space-y-2">
                  <div className="flex flex-col items-center gap-1">
                    <Icon className="h-8 w-8 text-gray-600" />
                    <span className="text-xs font-medium">{service.name}</span>
                  </div>
                  
                  <Badge className={getHealthColor(serviceHealth?.status || 'unknown')}>
                    <div className="flex items-center gap-1">
                      {getHealthIcon(serviceHealth?.status || 'unknown')}
                      <span className="text-xs">
                        {serviceHealth?.status || 'Unknown'}
                      </span>
                    </div>
                  </Badge>
                  
                  {serviceHealth?.message && (
                    <p className="text-xs text-muted-foreground">
                      {serviceHealth.message}
                    </p>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Real-time Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center space-y-1">
              <p className="text-2xl font-bold text-blue-600">
                {metrics.activeUsers || 0}
              </p>
              <p className="text-sm text-muted-foreground">Active Users</p>
            </div>
            
            <div className="text-center space-y-1">
              <p className="text-2xl font-bold text-green-600">
                {metrics.documentsProcessed || 0}
              </p>
              <p className="text-sm text-muted-foreground">Documents Processed</p>
            </div>
            
            <div className="text-center space-y-1">
              <p className="text-2xl font-bold text-purple-600">
                {metrics.workflowsExecuted || 0}
              </p>
              <p className="text-sm text-muted-foreground">Workflows Executed</p>
            </div>
            
            <div className="text-center space-y-1">
              <p className="text-2xl font-bold text-orange-600">
                {metrics.averageResponseTime || 0}ms
              </p>
              <p className="text-sm text-muted-foreground">Avg Response Time</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Alerts */}
      {alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>System Alerts</span>
              <Badge variant="destructive">{alerts.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {alerts.map((alert) => (
              <Alert key={alert.id} className="relative">
                <div className="flex items-start gap-2">
                  {getAlertIcon(alert.severity)}
                  <div className="flex-1">
                    <AlertDescription>
                      <div className="flex items-center justify-between">
                        <span>{alert.message}</span>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => clearAlert(alert.id)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Service Details */}
      <div className="grid gap-4 md:grid-cols-2">
        {Object.entries(health).map(([serviceKey, serviceHealth]) => {
          const service = services.find(s => s.key === serviceKey)
          if (!service || !serviceHealth) return null

          return (
            <Card key={serviceKey}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-base">
                  <div className="flex items-center gap-2">
                    <service.icon className="h-4 w-4" />
                    {service.name}
                  </div>
                  <Badge className={getHealthColor(serviceHealth.status)}>
                    {getHealthIcon(serviceHealth.status)}
                    <span className="ml-1">{serviceHealth.status}</span>
                  </Badge>
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-2">
                {serviceHealth.message && (
                  <p className="text-sm text-muted-foreground">
                    {serviceHealth.message}
                  </p>
                )}
                
                <div className="text-xs text-muted-foreground">
                  Last updated: {new Date(serviceHealth.timestamp).toLocaleString()}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Status Footer */}
      <div className="text-center text-xs text-muted-foreground">
        Real-time monitoring powered by Event Grid and Service Bus • 
        Last refresh: {new Date().toLocaleTimeString()}
      </div>
    </div>
  )
}
