/**
 * AI Models Hooks
 * React hooks for AI model management
 */

import { useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAIStore, useAILoading, useAIError } from '@/stores/ai-store'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface AIModel {
  id: string
  name: string
  description?: string
  type: 'text' | 'image' | 'audio' | 'multimodal'
  provider: 'deepseek' | 'llama' | 'cohere' | 'azure_ai' | 'azure_openai' | 'openai' | 'anthropic' | 'custom'
  version: string
  capabilities: string[]
  isActive: boolean
  configuration: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface CreateAIModelRequest {
  name: string
  description?: string
  type: 'text' | 'image' | 'audio' | 'multimodal'
  provider: 'deepseek' | 'llama' | 'cohere' | 'azure_ai' | 'azure_openai' | 'openai' | 'anthropic' | 'custom'
  version: string
  capabilities: string[]
  configuration: Record<string, any>
}

export interface UpdateAIModelRequest {
  name?: string
  description?: string
  capabilities?: string[]
  isActive?: boolean
  configuration?: Record<string, any>
}

/**
 * Hook to get all AI models
 */
export function useAIModels(params?: {
  type?: string
  provider?: string
  isActive?: boolean
}) {
  return useQuery({
    queryKey: ['ai-models', params],
    queryFn: async () => {
      return await backendApiClient.request<AIModel[]>('/ai/models', {
        params
      })
    },
  })
}

/**
 * Hook to get a specific AI model
 */
export function useAIModel(modelId: string) {
  return useQuery({
    queryKey: ['ai-model', modelId],
    queryFn: async () => {
      return await backendApiClient.request<AIModel>(`/ai/models/${modelId}`)
    },
    enabled: !!modelId,
  })
}

/**
 * Hook to create a new AI model
 */
export function useCreateAIModel() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateAIModelRequest) => {
      return await backendApiClient.request<AIModel>('/ai/models', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (model: AIModel) => {
      queryClient.invalidateQueries({ queryKey: ['ai-models'] })
      toast({
        title: 'AI Model created',
        description: `AI Model "${model.name}" has been created successfully.`,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating AI model',
        description: 'There was a problem creating the AI model. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update an AI model
 */
export function useUpdateAIModel() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ modelId, data }: { modelId: string; data: UpdateAIModelRequest }) => {
      return await backendApiClient.request<AIModel>(`/ai/models/${modelId}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (model: AIModel) => {
      queryClient.invalidateQueries({ queryKey: ['ai-model', model.id] })
      queryClient.invalidateQueries({ queryKey: ['ai-models'] })
      toast({
        title: 'AI Model updated',
        description: `AI Model "${model.name}" has been updated successfully.`,
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error updating AI model',
        description: 'There was a problem updating the AI model. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete an AI model
 */
export function useDeleteAIModel() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (modelId: string) => {
      await backendApiClient.request(`/ai/models/${modelId}`, {
        method: 'DELETE'
      })
      return modelId
    },
    onSuccess: (modelId: string) => {
      queryClient.invalidateQueries({ queryKey: ['ai-models'] })
      queryClient.removeQueries({ queryKey: ['ai-model', modelId] })
      toast({
        title: 'AI Model deleted',
        description: 'The AI model has been deleted successfully.',
      })
    },
    onError: (error: any) => {
      toast({
        title: 'Error deleting AI model',
        description: 'There was a problem deleting the AI model. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to test an AI model
 */
export function useTestAIModel() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ modelId, testData }: { modelId: string; testData: any }) => {
      return await backendApiClient.request(`/ai/models/${modelId}/test`, {
        method: 'POST',
        body: JSON.stringify(testData)
      })
    },
    onSuccess: (result) => {
      toast({
        title: 'Model test completed',
        description: 'The AI model test has been completed successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error testing model',
        description: 'There was a problem testing the AI model. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get AI model metrics
 */
export function useAIModelMetrics(modelId: string) {
  return useQuery({
    queryKey: ['ai-model', modelId, 'metrics'],
    queryFn: async () => {
      return await backendApiClient.request(`/ai/models/${modelId}/metrics`)
    },
    enabled: !!modelId,
  })
}

/**
 * Hook to get available AI providers
 */
export function useAIProviders() {
  return useQuery({
    queryKey: ['ai-providers'],
    queryFn: async () => {
      return await backendApiClient.request('/ai/providers')
    },
  })
}
