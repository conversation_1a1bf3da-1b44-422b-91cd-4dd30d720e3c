/**
 * Azure Search Service
 * Provides document indexing and search capabilities for organizational workflows
 */

import { SearchClient, SearchIndexClient, AzureKeyCredential } from '@azure/search-documents';
import { logger } from '../utils/logger';

export interface DocumentSearchResult {
  id: string;
  fileName: string;
  content: string;
  documentType: string;
  department: string;
  tags: string[];
  createdAt: string;
  modifiedAt: string;
  organizationId: string;
  complianceFlags: string[];
  score: number;
}

export interface WorkflowSearchResult {
  id: string;
  name: string;
  description: string;
  category: string;
  department: string;
  status: string;
  documentTypes: string[];
  complianceFramework: string[];
  organizationId: string;
  createdAt: string;
  score: number;
}

export interface SearchFilters {
  documentType?: string;
  department?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  complianceFramework?: string[];
  tags?: string[];
  organizationId?: string;
}

export interface SearchOptions {
  top?: number;
  skip?: number;
  orderBy?: string[];
  facets?: string[];
  highlightFields?: string[];
  searchMode?: 'any' | 'all';
  queryType?: 'simple' | 'full';
}

class AzureSearchService {
  private documentsClient: SearchClient<DocumentSearchResult>;
  private workflowsClient: SearchClient<WorkflowSearchResult>;
  private indexClient: SearchIndexClient;
  private initialized: boolean = false;

  constructor() {
    const endpoint = process.env.AZURE_SEARCH_SERVICE_NAME;
    const key = process.env.AZURE_SEARCH_SERVICE_KEY;

    if (!endpoint || !key) {
      throw new Error('Azure Search service name and key are required');
    }

    const searchEndpoint = `https://${endpoint}.search.windows.net`;
    const credential = new AzureKeyCredential(key);

    this.documentsClient = new SearchClient<DocumentSearchResult>(
      searchEndpoint,
      'documents-index',
      credential
    );

    this.workflowsClient = new SearchClient<WorkflowSearchResult>(
      searchEndpoint,
      'workflows-index',
      credential
    );

    this.indexClient = new SearchIndexClient(searchEndpoint, credential);
  }

  /**
   * Initialize the search service
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Test connection by getting index statistics
      await this.indexClient.getIndex('documents-index');
      await this.indexClient.getIndex('workflows-index');

      this.initialized = true;
      logger.info('Azure Search Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Azure Search Service', { error });
      throw error;
    }
  }

  /**
   * Index a document for search
   */
  async indexDocument(document: {
    id: string;
    fileName: string;
    content: string;
    documentType: string;
    department: string;
    tags: string[];
    createdAt: string;
    modifiedAt: string;
    organizationId: string;
    complianceFlags: string[];
  }): Promise<void> {
    try {
      const documentWithScore = { ...document, score: 1.0 };
      await this.documentsClient.uploadDocuments([documentWithScore]);
      logger.info('Document indexed successfully', { documentId: document.id });
    } catch (error) {
      logger.error('Failed to index document', { documentId: document.id, error });
      throw error;
    }
  }

  /**
   * Index a workflow for search
   */
  async indexWorkflow(workflow: {
    id: string;
    name: string;
    description: string;
    category: string;
    department: string;
    status: string;
    documentTypes: string[];
    complianceFramework: string[];
    organizationId: string;
    createdAt: string;
  }): Promise<void> {
    try {
      const workflowWithScore = { ...workflow, score: 1.0 };
      await this.workflowsClient.uploadDocuments([workflowWithScore]);
      logger.info('Workflow indexed successfully', { workflowId: workflow.id });
    } catch (error) {
      logger.error('Failed to index workflow', { workflowId: workflow.id, error });
      throw error;
    }
  }

  /**
   * Search documents
   */
  async searchDocuments(
    query: string,
    filters?: SearchFilters,
    options?: SearchOptions
  ): Promise<{
    results: DocumentSearchResult[];
    count: number;
    facets?: Record<string, any>;
  }> {
    try {
      const searchOptions: any = {
        top: options?.top || 50,
        skip: options?.skip || 0,
        orderBy: options?.orderBy,
        facets: options?.facets,
        highlightFields: options?.highlightFields,
        searchMode: options?.searchMode || 'any',
        queryType: options?.queryType || 'simple',
        includeTotalCount: true
      };

      // Build filter expression
      if (filters) {
        const filterExpressions: string[] = [];

        if (filters.organizationId) {
          filterExpressions.push(`organizationId eq '${filters.organizationId}'`);
        }

        if (filters.documentType) {
          filterExpressions.push(`documentType eq '${filters.documentType}'`);
        }

        if (filters.department) {
          filterExpressions.push(`department eq '${filters.department}'`);
        }

        if (filters.dateRange) {
          filterExpressions.push(
            `createdAt ge ${filters.dateRange.start} and createdAt le ${filters.dateRange.end}`
          );
        }

        if (filters.tags && filters.tags.length > 0) {
          const tagFilters = filters.tags.map(tag => `tags/any(t: t eq '${tag}')`);
          filterExpressions.push(`(${tagFilters.join(' or ')})`);
        }

        if (filters.complianceFramework && filters.complianceFramework.length > 0) {
          const complianceFilters = filters.complianceFramework.map(
            framework => `complianceFlags/any(c: c eq '${framework}')`
          );
          filterExpressions.push(`(${complianceFilters.join(' or ')})`);
        }

        if (filterExpressions.length > 0) {
          searchOptions.filter = filterExpressions.join(' and ');
        }
      }

      const searchResults = await this.documentsClient.search(query, searchOptions);

      const results: DocumentSearchResult[] = [];
      for await (const result of searchResults.results) {
        results.push({
          ...result.document,
          score: result.score || 0
        });
      }

      return {
        results,
        count: searchResults.count || 0,
        facets: searchResults.facets
      };

    } catch (error) {
      logger.error('Document search failed', { query, error });
      throw error;
    }
  }

  /**
   * Search workflows
   */
  async searchWorkflows(
    query: string,
    filters?: SearchFilters,
    options?: SearchOptions
  ): Promise<{
    results: WorkflowSearchResult[];
    count: number;
    facets?: Record<string, any>;
  }> {
    try {
      const searchOptions: any = {
        top: options?.top || 50,
        skip: options?.skip || 0,
        orderBy: options?.orderBy,
        facets: options?.facets,
        highlightFields: options?.highlightFields,
        searchMode: options?.searchMode || 'any',
        queryType: options?.queryType || 'simple',
        includeTotalCount: true
      };

      // Build filter expression
      if (filters) {
        const filterExpressions: string[] = [];

        if (filters.organizationId) {
          filterExpressions.push(`organizationId eq '${filters.organizationId}'`);
        }

        if (filters.department) {
          filterExpressions.push(`department eq '${filters.department}'`);
        }

        if (filters.complianceFramework && filters.complianceFramework.length > 0) {
          const complianceFilters = filters.complianceFramework.map(
            framework => `complianceFramework/any(c: c eq '${framework}')`
          );
          filterExpressions.push(`(${complianceFilters.join(' or ')})`);
        }

        if (filterExpressions.length > 0) {
          searchOptions.filter = filterExpressions.join(' and ');
        }
      }

      const searchResults = await this.workflowsClient.search(query, searchOptions);

      const results: WorkflowSearchResult[] = [];
      for await (const result of searchResults.results) {
        results.push({
          ...result.document,
          score: result.score || 0
        });
      }

      return {
        results,
        count: searchResults.count || 0,
        facets: searchResults.facets
      };

    } catch (error) {
      logger.error('Workflow search failed', { query, error });
      throw error;
    }
  }

  /**
   * Get search suggestions for documents
   */
  async suggestDocuments(query: string, organizationId: string): Promise<string[]> {
    try {
      const suggestions = await this.documentsClient.suggest(query, 'document-suggester', {
        top: 10,
        filter: `organizationId eq '${organizationId}'`
      });

      return suggestions.results.map(result => result.text);
    } catch (error) {
      logger.error('Document suggestion failed', { query, error });
      return [];
    }
  }

  /**
   * Get search suggestions for workflows
   */
  async suggestWorkflows(query: string, organizationId: string): Promise<string[]> {
    try {
      const suggestions = await this.workflowsClient.suggest(query, 'workflow-suggester', {
        top: 10,
        filter: `organizationId eq '${organizationId}'`
      });

      return suggestions.results.map(result => result.text);
    } catch (error) {
      logger.error('Workflow suggestion failed', { query, error });
      return [];
    }
  }

  /**
   * Delete document from index
   */
  async deleteDocument(documentId: string): Promise<void> {
    try {
      await this.documentsClient.deleteDocuments('id', [documentId]);
      logger.info('Document deleted from index', { documentId });
    } catch (error) {
      logger.error('Failed to delete document from index', { documentId, error });
      throw error;
    }
  }

  /**
   * Delete workflow from index
   */
  async deleteWorkflow(workflowId: string): Promise<void> {
    try {
      await this.workflowsClient.deleteDocuments('id', [workflowId]);
      logger.info('Workflow deleted from index', { workflowId });
    } catch (error) {
      logger.error('Failed to delete workflow from index', { workflowId, error });
      throw error;
    }
  }

  /**
   * Get index statistics
   */
  async getIndexStatistics(): Promise<{
    documentsCount: number;
    workflowsCount: number;
    storageSize: number;
  }> {
    try {
      const [documentsStats, workflowsStats] = await Promise.all([
        this.indexClient.getIndexStatistics('documents-index'),
        this.indexClient.getIndexStatistics('workflows-index')
      ]);

      return {
        documentsCount: documentsStats.documentCount,
        workflowsCount: workflowsStats.documentCount,
        storageSize: documentsStats.storageSize + workflowsStats.storageSize
      };
    } catch (error) {
      logger.error('Failed to get index statistics', { error });
      throw error;
    }
  }
}

// Create and export singleton instance
export const azureSearchService = new AzureSearchService();
export default azureSearchService;
