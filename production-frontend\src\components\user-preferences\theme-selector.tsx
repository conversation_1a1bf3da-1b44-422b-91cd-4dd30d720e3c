"use client";

import { useTheme } from "next-themes";
import { useUpdateTheme } from "@/hooks/user-preferences";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Sun, Moon, Monitor } from "lucide-react";

interface ThemeSelectorProps {
  currentTheme?: string;
}

export function ThemeSelector({ currentTheme }: ThemeSelectorProps) {
  const { theme, setTheme } = useTheme();
  const updateTheme = useUpdateTheme();

  const handleThemeChange = (value: string) => {
    // Update theme in next-themes
    setTheme(value);

    // Update theme in user preferences
    updateTheme(value as 'light' | 'dark' | 'system');
  };

  return (
    <RadioGroup
      defaultValue={currentTheme || theme || "system"}
      onValueChange={handleThemeChange}
      className="grid grid-cols-3 gap-4"
    >
      <div>
        <RadioGroupItem
          value="light"
          id="theme-light"
          className="peer sr-only"
        />
        <Label
          htmlFor="theme-light"
          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
        >
          <Sun className="mb-3 h-6 w-6" />
          Light
        </Label>
      </div>
      <div>
        <RadioGroupItem
          value="dark"
          id="theme-dark"
          className="peer sr-only"
        />
        <Label
          htmlFor="theme-dark"
          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
        >
          <Moon className="mb-3 h-6 w-6" />
          Dark
        </Label>
      </div>
      <div>
        <RadioGroupItem
          value="system"
          id="theme-system"
          className="peer sr-only"
        />
        <Label
          htmlFor="theme-system"
          className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
        >
          <Monitor className="mb-3 h-6 w-6" />
          System
        </Label>
      </div>
    </RadioGroup>
  );
}
