'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Users, Lock, Unlock, Eye, Edit, MessageCircle, Share2, Clock } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Separator } from '@/components/ui/separator'
import { CollaborationProvider, useCollaborativeSignalR } from '@/providers/collaboration-provider'
import { useToast } from '@/hooks/use-toast'

interface PresenceInfo {
  userId: string
  userName: string
  cursor?: { x: number; y: number; page?: number }
  lastSeen: number
}

interface DocumentLock {
  userId: string
  userName: string
  lockedAt: number
}

interface CollaborationEvent {
  type: 'cursor' | 'selection' | 'edit' | 'comment' | 'presence'
  userId: string
  userName: string
  documentId: string
  data: any
  timestamp: number
}

interface DocumentCollaborationProps {
  documentId: string
  currentUserId: string
  currentUserName: string
  onLockChange?: (isLocked: boolean) => void
  className?: string
}

// Internal component that uses the collaboration hook
function DocumentCollaborationContent({
  documentId,
  currentUserId,
  currentUserName,
  onLockChange,
  className
}: DocumentCollaborationProps) {
  const [presence, setPresence] = useState<PresenceInfo[]>([])
  const [documentLock, setDocumentLock] = useState<DocumentLock | null>(null)
  const [recentActivity, setRecentActivity] = useState<CollaborationEvent[]>([])
  const { toast } = useToast()

  // Use the collaborative SignalR hook with document-specific group
  const collaboration = useCollaborativeSignalR(`document:${documentId}`)

  // Set up SignalR event handlers
  useEffect(() => {
    if (!collaboration.isConnected) return

    // Set up event listeners for document collaboration
    collaboration.on('PresenceUpdated', handlePresenceUpdate)
    collaboration.on('DocumentLocked', handleDocumentLocked)
    collaboration.on('DocumentUnlocked', handleDocumentUnlocked)
    collaboration.on('CollaborationEvent', handleCollaborationEvent)

    // Send initial presence
    collaboration.sendMessage('UpdatePresence', documentId, {
      userId: currentUserId,
      userName: currentUserName,
      timestamp: Date.now()
    }).catch(console.error)

    // Cleanup event listeners
    return () => {
      collaboration.off('PresenceUpdated', handlePresenceUpdate)
      collaboration.off('DocumentLocked', handleDocumentLocked)
      collaboration.off('DocumentUnlocked', handleDocumentUnlocked)
      collaboration.off('CollaborationEvent', handleCollaborationEvent)
    }
  }, [collaboration.isConnected, documentId, currentUserId, currentUserName, collaboration])

  const handlePresenceUpdate = (updatedPresence: PresenceInfo[]) => {
    setPresence(updatedPresence.filter(p => p.userId !== currentUserId))
  }

  const handleDocumentLocked = (lock: DocumentLock) => {
    setDocumentLock(lock)
    onLockChange?.(true)
    
    if (lock.userId !== currentUserId) {
      toast({
        title: 'Document Locked',
        description: `${lock.userName} has locked this document for editing`,
      })
    }
  }

  const handleDocumentUnlocked = () => {
    setDocumentLock(null)
    onLockChange?.(false)
    
    toast({
      title: 'Document Unlocked',
      description: 'Document is now available for editing',
    })
  }

  const handleCollaborationEvent = ({ type, event }: { type: string; event: CollaborationEvent }) => {
    if (event.userId === currentUserId) return

    setRecentActivity(prev => [event, ...prev.slice(0, 9)]) // Keep last 10 events

    switch (type) {
      case 'edit':
        toast({
          title: 'Document Updated',
          description: `${event.userName} made changes to the document`,
        })
        break
      case 'comment':
        toast({
          title: 'New Comment',
          description: `${event.userName} added a comment`,
        })
        break
    }
  }

  const handleLockToggle = async () => {
    try {
      if (documentLock) {
        if (documentLock.userId === currentUserId) {
          await collaboration.sendMessage('UnlockDocument', documentId)
        } else {
          toast({
            title: 'Cannot Unlock',
            description: 'Document is locked by another user',
            variant: 'destructive'
          })
        }
      } else {
        await collaboration.sendMessage('LockDocument', documentId, currentUserId, currentUserName)
      }
    } catch (error) {
      console.error('Lock toggle error:', error)
      toast({
        title: 'Error',
        description: 'Failed to toggle document lock',
        variant: 'destructive'
      })
    }
  }

  const getPresenceColor = (userId: string) => {
    const colors = [
      'bg-red-500',
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-orange-500'
    ]
    const index = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    return colors[index % colors.length]
  }

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    if (minutes > 0) return `${minutes}m ago`
    return 'Just now'
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Collaboration
            <Badge variant={collaboration.isConnected ? 'default' : 'destructive'}>
              {collaboration.isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={documentLock ? 'destructive' : 'outline'}
                  size="sm"
                  onClick={handleLockToggle}
                  disabled={!collaboration.isConnected || (documentLock !== null && documentLock.userId !== currentUserId)}
                >
                  {documentLock ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {documentLock
                  ? documentLock.userId === currentUserId
                    ? 'Unlock document'
                    : `Locked by ${documentLock.userName}`
                  : 'Lock document for editing'
                }
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Document Lock Status */}
        {documentLock && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2">
              <Lock className="h-4 w-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                {documentLock.userId === currentUserId
                  ? 'You have locked this document'
                  : `Locked by ${documentLock.userName}`
                }
              </span>
            </div>
            <p className="text-xs text-yellow-600 mt-1">
              Locked {formatTimeAgo(documentLock.lockedAt)}
            </p>
          </div>
        )}

        {/* Active Users */}
        <div>
          <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Active Users ({presence.length + 1})
          </h4>
          
          <div className="space-y-2">
            {/* Current User */}
            <div className="flex items-center gap-3 p-2 bg-muted/50 rounded-lg">
              <div className="relative">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {currentUserName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">{currentUserName} (You)</p>
                <p className="text-xs text-muted-foreground">
                  {documentLock?.userId === currentUserId ? 'Editing' : 'Viewing'}
                </p>
              </div>
            </div>

            {/* Other Users */}
            {presence.map((user) => (
              <div key={user.userId} className="flex items-center gap-3 p-2 rounded-lg">
                <div className="relative">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className={getPresenceColor(user.userId)}>
                      {user.userName.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{user.userName}</p>
                  <p className="text-xs text-muted-foreground">
                    {user.cursor ? 'Editing' : 'Viewing'} • {formatTimeAgo(user.lastSeen)}
                  </p>
                </div>
              </div>
            ))}

            {presence.length === 0 && (
              <p className="text-sm text-muted-foreground text-center py-4">
                No other users currently viewing this document
              </p>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        {recentActivity.length > 0 && (
          <>
            <Separator />
            <div>
              <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Recent Activity
              </h4>
              
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-2 text-xs">
                    <div className={`w-2 h-2 rounded-full ${getPresenceColor(activity.userId)}`} />
                    <span className="font-medium">{activity.userName}</span>
                    <span className="text-muted-foreground">
                      {activity.type === 'edit' && 'edited the document'}
                      {activity.type === 'comment' && 'added a comment'}
                      {activity.type === 'cursor' && 'moved cursor'}
                    </span>
                    <span className="text-muted-foreground ml-auto">
                      {formatTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Collaboration Actions */}
        <Separator />
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex-1">
            <MessageCircle className="h-4 w-4 mr-2" />
            Comments
          </Button>
          <Button variant="outline" size="sm" className="flex-1">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Main component with CollaborationProvider wrapper
export function DocumentCollaboration(props: DocumentCollaborationProps) {
  return (
    <CollaborationProvider>
      <DocumentCollaborationContent {...props} />
    </CollaborationProvider>
  )
}
