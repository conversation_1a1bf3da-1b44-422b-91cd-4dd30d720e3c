"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const sidebarVariants = cva(
  "h-screen bg-background border-r flex flex-col",
  {
    variants: {
      variant: {
        default: "border-r",
        floating: "border rounded-lg shadow-md m-2 h-[calc(100vh-16px)]",
        minimal: "border-none",
      },
      size: {
        default: "w-64",
        sm: "w-48",
        lg: "w-80",
        collapsed: "w-16",
      },
      position: {
        left: "left-0",
        right: "right-0",
      },
      fixed: {
        true: "fixed top-0 bottom-0",
        false: "relative",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      position: "left",
      fixed: false,
    },
  }
)

export interface SidebarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof sidebarVariants> {
  children: React.ReactNode
  header?: React.ReactNode
  footer?: React.ReactNode
  collapsible?: boolean
  defaultCollapsed?: boolean
  onCollapseChange?: (collapsed: boolean) => void
}

const Sidebar = React.forwardRef<HTMLDivElement, SidebarProps>(
  ({ 
    className, 
    variant, 
    size, 
    position, 
    fixed, 
    header, 
    footer, 
    collapsible = false,
    defaultCollapsed = false,
    onCollapseChange,
    children, 
    ...props 
  }, ref) => {
    const [collapsed, setCollapsed] = React.useState(defaultCollapsed)
    
    const toggleCollapse = () => {
      const newCollapsed = !collapsed
      setCollapsed(newCollapsed)
      onCollapseChange?.(newCollapsed)
    }
    
    return (
      <div
        ref={ref}
        className={cn(sidebarVariants({ 
          variant, 
          size: collapsed ? "collapsed" : size, 
          position, 
          fixed, 
          className 
        }))}
        {...props}
      >
        {header && (
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              {!collapsed && header}
              
              {collapsible && (
                <button
                  className="p-1 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
                  onClick={toggleCollapse}
                >
                  {collapsed ? (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  )}
                </button>
              )}
            </div>
          </div>
        )}
        
        <div className="flex-1 overflow-y-auto p-4">
          {children}
        </div>
        
        {footer && (
          <div className="p-4 border-t">
            {!collapsed && footer}
          </div>
        )}
      </div>
    )
  }
)
Sidebar.displayName = "Sidebar"

export interface SidebarItemProps extends React.HTMLAttributes<HTMLDivElement> {
  icon?: React.ReactNode
  title: string
  active?: boolean
  collapsed?: boolean
  badge?: React.ReactNode
}

const SidebarItem = React.forwardRef<HTMLDivElement, SidebarItemProps>(
  ({ className, icon, title, active, collapsed, badge, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center px-3 py-2 rounded-md cursor-pointer transition-colors mb-1",
          active 
            ? "bg-primary/10 text-primary" 
            : "text-muted-foreground hover:text-foreground hover:bg-accent",
          className
        )}
        {...props}
      >
        {icon && (
          <div className="mr-2 h-5 w-5">
            {icon}
          </div>
        )}
        
        {!collapsed && (
          <div className="flex-1 truncate">
            {title}
          </div>
        )}
        
        {!collapsed && badge && (
          <div className="ml-2">
            {badge}
          </div>
        )}
      </div>
    )
  }
)
SidebarItem.displayName = "SidebarItem"

export interface SidebarSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  collapsed?: boolean
}

const SidebarSection = React.forwardRef<HTMLDivElement, SidebarSectionProps>(
  ({ className, title, collapsed, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("mb-6", className)}
        {...props}
      >
        {title && !collapsed && (
          <h3 className="mb-2 px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            {title}
          </h3>
        )}
        
        <div className="space-y-1">
          {children}
        </div>
      </div>
    )
  }
)
SidebarSection.displayName = "SidebarSection"

export { Sidebar, SidebarItem, SidebarSection, sidebarVariants }
