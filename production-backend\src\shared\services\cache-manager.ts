/**
 * Production-Ready Cache Manager with Cache-Aside Pattern
 * Implements tag-based invalidation, Redis cluster compatibility,
 * and comprehensive performance monitoring
 */

import { redis } from './redis';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[]; // Tags for invalidation
  compress?: boolean; // Enable compression for large values
  namespace?: string; // Cache namespace
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  invalidations: number;
  errors: number;
  averageResponseTime: number;
  hitRatio: number;
  lastUpdated: Date;
}

export interface CacheEntry<T = any> {
  value: T;
  createdAt: Date;
  expiresAt?: Date;
  tags: string[];
  compressed: boolean;
  size: number;
}

export class CacheManager {
  private static instance: CacheManager;
  private metrics: CacheMetrics;
  private defaultTtl: number = 3600; // 1 hour default
  private defaultNamespace: string = 'app';
  private compressionThreshold: number = 1024; // Compress values > 1KB

  constructor() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      invalidations: 0,
      errors: 0,
      averageResponseTime: 0,
      hitRatio: 0,
      lastUpdated: new Date()
    };
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Get value from cache with cache-aside pattern
   */
  async get<T>(key: string, options?: { namespace?: string }): Promise<T | null> {
    const startTime = Date.now();
    const fullKey = this.buildKey(key, options?.namespace);

    try {
      const cached = await redis.get(fullKey);
      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);

      if (cached === null) {
        this.metrics.misses++;
        this.updateMetrics();
        
        logger.debug('Cache miss', { key: fullKey, responseTime });
        return null;
      }

      const entry: CacheEntry<T> = JSON.parse(cached);
      
      // Check if entry has expired (additional safety check)
      if (entry.expiresAt && new Date() > new Date(entry.expiresAt)) {
        await this.delete(key, options);
        this.metrics.misses++;
        this.updateMetrics();
        return null;
      }

      // Decompress if needed
      let value = entry.value;
      if (entry.compressed && typeof value === 'string') {
        value = this.decompress(value);
      }

      this.metrics.hits++;
      this.updateMetrics();
      
      logger.debug('Cache hit', { 
        key: fullKey, 
        responseTime, 
        compressed: entry.compressed,
        size: entry.size 
      });
      
      return value;
    } catch (error) {
      this.metrics.errors++;
      this.updateMetrics();
      
      logger.error('Cache get error', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return null;
    }
  }

  /**
   * Set value in cache with options
   */
  async set<T>(
    key: string, 
    value: T, 
    options: CacheOptions = {}
  ): Promise<boolean> {
    const startTime = Date.now();
    const fullKey = this.buildKey(key, options.namespace);
    const ttl = options.ttl || this.defaultTtl;
    const tags = options.tags || [];

    try {
      // Prepare cache entry
      let processedValue = value;
      let compressed = false;
      const serialized = JSON.stringify(value);
      
      // Compress large values
      if (options.compress !== false && serialized.length > this.compressionThreshold) {
        processedValue = this.compress(serialized) as T;
        compressed = true;
      }

      const entry: CacheEntry<T> = {
        value: processedValue,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + ttl * 1000),
        tags,
        compressed,
        size: JSON.stringify(processedValue).length
      };

      // Set in Redis with TTL
      await redis.setex(fullKey, ttl, JSON.stringify(entry));

      // Store tags for invalidation
      if (tags.length > 0) {
        await this.storeTags(fullKey, tags, ttl);
      }

      const responseTime = Date.now() - startTime;
      this.updateResponseTime(responseTime);
      this.metrics.sets++;
      this.updateMetrics();

      logger.debug('Cache set', {
        key: fullKey,
        ttl,
        tags: tags.length,
        compressed,
        size: entry.size,
        responseTime
      });

      return true;
    } catch (error) {
      this.metrics.errors++;
      this.updateMetrics();
      
      logger.error('Cache set error', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return false;
    }
  }

  /**
   * Delete specific key from cache
   */
  async delete(key: string, options?: { namespace?: string }): Promise<boolean> {
    const fullKey = this.buildKey(key, options?.namespace);

    try {
      const result = await redis.del(fullKey);
      
      if (result > 0) {
        this.metrics.deletes++;
        this.updateMetrics();
        
        logger.debug('Cache delete', { key: fullKey });
        return true;
      }
      
      return false;
    } catch (error) {
      this.metrics.errors++;
      this.updateMetrics();
      
      logger.error('Cache delete error', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return false;
    }
  }

  /**
   * Invalidate cache entries by tags
   */
  async invalidateByTags(tags: string[]): Promise<number> {
    let invalidatedCount = 0;

    try {
      for (const tag of tags) {
        const tagKey = `cache:tags:${tag}`;
        const keys = await redis.smembers(tagKey);
        
        if (keys.length > 0) {
          // Delete all keys with this tag
          const deleted = await redis.del(...keys);
          invalidatedCount += deleted;
          
          // Clean up the tag set
          await redis.del(tagKey);
        }
      }

      this.metrics.invalidations += invalidatedCount;
      this.updateMetrics();

      logger.info('Cache invalidated by tags', {
        tags,
        invalidatedCount
      });

      return invalidatedCount;
    } catch (error) {
      this.metrics.errors++;
      this.updateMetrics();
      
      logger.error('Cache invalidation error', {
        tags,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return 0;
    }
  }

  /**
   * Get or set pattern (cache-aside)
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // Cache miss - get from source
    try {
      const value = await factory();
      
      // Store in cache for next time
      await this.set(key, value, options);
      
      return value;
    } catch (error) {
      logger.error('Cache factory error', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Clear all cache entries in namespace
   */
  async clear(namespace?: string): Promise<number> {
    const pattern = namespace 
      ? `${namespace}:*` 
      : `${this.defaultNamespace}:*`;

    try {
      const keys = await redis.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }

      const deleted = await redis.del(...keys);
      
      logger.info('Cache cleared', {
        namespace: namespace || this.defaultNamespace,
        deletedCount: deleted
      });

      return deleted;
    } catch (error) {
      this.metrics.errors++;
      this.updateMetrics();
      
      logger.error('Cache clear error', {
        namespace,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return 0;
    }
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      invalidations: 0,
      errors: 0,
      averageResponseTime: 0,
      hitRatio: 0,
      lastUpdated: new Date()
    };
  }

  /**
   * Build full cache key with namespace
   */
  private buildKey(key: string, namespace?: string): string {
    const ns = namespace || this.defaultNamespace;
    return `${ns}:${key}`;
  }

  /**
   * Store tags for invalidation
   */
  private async storeTags(key: string, tags: string[], ttl: number): Promise<void> {
    for (const tag of tags) {
      const tagKey = `cache:tags:${tag}`;
      await redis.sadd(tagKey, key);
      await redis.expire(tagKey, ttl + 60); // Tag expires slightly after content
    }
  }

  /**
   * Update response time metrics
   */
  private updateResponseTime(responseTime: number): void {
    const totalOperations = this.metrics.hits + this.metrics.misses + this.metrics.sets;
    if (totalOperations === 0) {
      this.metrics.averageResponseTime = responseTime;
    } else {
      this.metrics.averageResponseTime = 
        (this.metrics.averageResponseTime * (totalOperations - 1) + responseTime) / totalOperations;
    }
  }

  /**
   * Update hit ratio and last updated time
   */
  private updateMetrics(): void {
    const totalRequests = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRatio = totalRequests > 0 ? this.metrics.hits / totalRequests : 0;
    this.metrics.lastUpdated = new Date();
  }

  /**
   * Compress string data using zlib
   */
  private compress(data: string): string {
    try {
      const zlib = require('zlib');
      const buffer = Buffer.from(data, 'utf8');

      // Use gzip compression for better compression ratio
      const compressed = zlib.gzipSync(buffer);

      // Return base64 encoded compressed data with compression marker
      return `gzip:${compressed.toString('base64')}`;
    } catch (error) {
      logger.warn('Compression failed, storing uncompressed', {
        error: error instanceof Error ? error.message : String(error),
        dataSize: data.length
      });
      // Fallback to base64 encoding without compression
      return `raw:${Buffer.from(data).toString('base64')}`;
    }
  }

  /**
   * Decompress string data
   */
  private decompress(data: string): any {
    try {
      let decompressed: string;

      if (data.startsWith('gzip:')) {
        // Handle gzip compressed data
        const zlib = require('zlib');
        const compressedData = data.substring(5); // Remove 'gzip:' prefix
        const buffer = Buffer.from(compressedData, 'base64');
        const decompressedBuffer = zlib.gunzipSync(buffer);
        decompressed = decompressedBuffer.toString('utf8');
      } else if (data.startsWith('raw:')) {
        // Handle raw base64 encoded data
        const rawData = data.substring(4); // Remove 'raw:' prefix
        decompressed = Buffer.from(rawData, 'base64').toString('utf8');
      } else {
        // Legacy format - assume base64 encoded
        decompressed = Buffer.from(data, 'base64').toString('utf8');
      }

      // Try to parse as JSON, return string if parsing fails
      try {
        return JSON.parse(decompressed);
      } catch {
        return decompressed;
      }
    } catch (error) {
      logger.error('Decompression error', {
        error: error instanceof Error ? error.message : String(error),
        dataPrefix: data.substring(0, 20)
      });
      return data;
    }
  }

  /**
   * Calculate compression ratio for monitoring
   */
  private calculateCompressionRatio(original: string, compressed: string): number {
    const originalSize = Buffer.byteLength(original, 'utf8');
    const compressedSize = Buffer.byteLength(compressed, 'utf8');
    return compressedSize / originalSize;
  }

  /**
   * Determine if data should be compressed based on size and type
   */
  private shouldCompress(data: string): boolean {
    // Only compress data larger than 1KB to avoid overhead
    const minSizeForCompression = 1024;
    const dataSize = Buffer.byteLength(data, 'utf8');

    if (dataSize < minSizeForCompression) {
      return false;
    }

    // Don't compress already compressed or binary data
    if (data.startsWith('gzip:') || data.startsWith('raw:')) {
      return false;
    }

    // Check if data appears to be JSON or text (good compression candidates)
    try {
      JSON.parse(data);
      return true; // JSON compresses well
    } catch {
      // Check if it's mostly text
      const textRatio = (data.match(/[a-zA-Z0-9\s]/g) || []).length / data.length;
      return textRatio > 0.7; // Compress if mostly text
    }
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();
