"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Document } from "@/types/backend";
import { format } from "date-fns";

interface DocumentMetadataProps {
  document: Document;
}

export function DocumentMetadata({ document }: DocumentMetadataProps) {
  // Format file size to human-readable format
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Document Metadata</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-1 gap-3">
              <div className="space-y-1">
                <p className="text-sm font-medium">File Name</p>
                <p className="text-sm text-muted-foreground">{document.name}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">File Type</p>
                <p className="text-sm text-muted-foreground">{document.contentType}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">File Size</p>
                <p className="text-sm text-muted-foreground">{formatFileSize(document.size)}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Document Type</p>
                <p className="text-sm text-muted-foreground">{document.type}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Status</p>
                <p className="text-sm text-muted-foreground">{document.status}</p>
              </div>
            </div>
          </div>

          {/* Dates & Ownership */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Dates & Ownership</h3>
            <div className="grid grid-cols-1 gap-3">
              <div className="space-y-1">
                <p className="text-sm font-medium">Created By</p>
                <p className="text-sm text-muted-foreground">{document.createdBy}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Created Date</p>
                <p className="text-sm text-muted-foreground">
                  {format(new Date(document.createdAt), "PPP 'at' p")}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Last Updated By</p>
                <p className="text-sm text-muted-foreground">{document.updatedBy}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Last Updated Date</p>
                <p className="text-sm text-muted-foreground">
                  {format(new Date(document.updatedAt), "PPP 'at' p")}
                </p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Version</p>
                <p className="text-sm text-muted-foreground">{document.version}</p>
              </div>
            </div>
          </div>

          {/* Document-specific Metadata */}
          {document.metadata && Object.keys(document.metadata).length > 0 && (
            <div className="space-y-4 col-span-1 md:col-span-2">
              <h3 className="text-lg font-medium">Document-specific Metadata</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {Object.entries(document.metadata).map(([key, value]) => (
                  <div key={key} className="space-y-1">
                    <p className="text-sm font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</p>
                    <p className="text-sm text-muted-foreground">{value?.toString() || 'N/A'}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Processing Information */}
          {document.processing && (
            <div className="space-y-4 col-span-1 md:col-span-2">
              <h3 className="text-lg font-medium">Processing Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="space-y-1">
                  <p className="text-sm font-medium">Text Extracted</p>
                  <p className="text-sm text-muted-foreground">{document.processing.textExtracted ? 'Yes' : 'No'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">AI Processed</p>
                  <p className="text-sm text-muted-foreground">{document.processing.aiProcessed ? 'Yes' : 'No'}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">OCR Completed</p>
                  <p className="text-sm text-muted-foreground">{document.processing.ocrCompleted ? 'Yes' : 'No'}</p>
                </div>
                {document.processing.lastProcessedAt && (
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Last Processed</p>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(document.processing.lastProcessedAt), "PPP 'at' p")}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
