import React from 'react';

interface PageHeaderProps {
  /**
   * Page title
   */
  title: string;
  
  /**
   * Optional page description
   */
  description?: string;
  
  /**
   * Optional actions to display
   */
  actions?: React.ReactNode;
}

/**
 * Component for consistent page headers
 */
export function PageHeader({ title, description, actions }: PageHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground">{description}</p>
        )}
      </div>
      {actions && <div className="flex items-center gap-2">{actions}</div>}
    </div>
  );
}
