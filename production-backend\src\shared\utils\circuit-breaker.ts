/**
 * Circuit Breaker Pattern Implementation
 * Prevents cascading failures by monitoring external service calls
 */

import { redis } from '../services/redis';
import { logger } from './logger';

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Circuit is open, calls are rejected
  HALF_OPEN = 'HALF_OPEN' // Testing if service is back
}

export interface CircuitBreakerConfig {
  failureThreshold: number;     // Number of failures before opening
  recoveryTimeout: number;      // Time to wait before trying again (ms)
  monitoringPeriod: number;     // Time window for failure counting (ms)
  successThreshold: number;     // Successes needed to close from half-open
  timeout: number;              // Request timeout (ms)
}

export interface CircuitBreakerStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: number;
  lastSuccessTime?: number;
  totalRequests: number;
  totalFailures: number;
  totalSuccesses: number;
}

export class CircuitBreaker {
  private config: CircuitBreakerConfig;
  private serviceName: string;
  private state: CircuitState = CircuitState.CLOSED;
  private failureCount = 0;
  private successCount = 0;
  private lastFailureTime?: number;
  private nextAttemptTime = 0;

  constructor(serviceName: string, config: Partial<CircuitBreakerConfig> = {}) {
    this.serviceName = serviceName;
    this.config = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 300000, // 5 minutes
      successThreshold: 3,
      timeout: 30000, // 30 seconds
      ...config
    };
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    await this.updateStateFromRedis();

    if (this.state === CircuitState.OPEN) {
      if (Date.now() < this.nextAttemptTime) {
        throw new Error(`Circuit breaker is OPEN for ${this.serviceName}. Next attempt at ${new Date(this.nextAttemptTime).toISOString()}`);
      } else {
        this.state = CircuitState.HALF_OPEN;
        this.successCount = 0;
        await this.saveStateToRedis();
      }
    }

    try {
      const result = await this.executeWithTimeout(fn);
      await this.onSuccess();
      return result;
    } catch (error) {
      await this.onFailure(error);
      throw error;
    }
  }

  /**
   * Execute function with timeout
   */
  private async executeWithTimeout<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Request timeout after ${this.config.timeout}ms`));
      }, this.config.timeout);

      fn()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Handle successful execution
   */
  private async onSuccess(): Promise<void> {
    this.failureCount = 0;
    this.successCount++;

    if (this.state === CircuitState.HALF_OPEN) {
      if (this.successCount >= this.config.successThreshold) {
        this.state = CircuitState.CLOSED;
        this.successCount = 0;
        logger.info(`Circuit breaker CLOSED for ${this.serviceName}`);
      }
    }

    await this.saveStateToRedis();
    await this.recordMetric('success');
  }

  /**
   * Handle failed execution
   */
  private async onFailure(error: any): Promise<void> {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = Date.now() + this.config.recoveryTimeout;
      logger.warn(`Circuit breaker OPEN for ${this.serviceName} (half-open failure)`);
    } else if (this.state === CircuitState.CLOSED && this.failureCount >= this.config.failureThreshold) {
      this.state = CircuitState.OPEN;
      this.nextAttemptTime = Date.now() + this.config.recoveryTimeout;
      logger.warn(`Circuit breaker OPEN for ${this.serviceName} (threshold reached: ${this.failureCount})`);
    }

    await this.saveStateToRedis();
    await this.recordMetric('failure', error);
  }

  /**
   * Get current circuit breaker statistics
   */
  async getStats(): Promise<CircuitBreakerStats> {
    await this.updateStateFromRedis();
    
    const metrics = await this.getMetricsFromRedis();
    
    return {
      state: this.state,
      failureCount: this.failureCount,
      successCount: this.successCount,
      lastFailureTime: this.lastFailureTime,
      totalRequests: metrics.totalRequests,
      totalFailures: metrics.totalFailures,
      totalSuccesses: metrics.totalSuccesses,
      lastSuccessTime: metrics.lastSuccessTime
    };
  }

  /**
   * Force circuit breaker to specific state (for testing/admin)
   */
  async forceState(state: CircuitState): Promise<void> {
    this.state = state;
    if (state === CircuitState.CLOSED) {
      this.failureCount = 0;
      this.successCount = 0;
    } else if (state === CircuitState.OPEN) {
      this.nextAttemptTime = Date.now() + this.config.recoveryTimeout;
    }
    
    await this.saveStateToRedis();
    logger.info(`Circuit breaker for ${this.serviceName} forced to ${state}`);
  }

  /**
   * Reset circuit breaker statistics
   */
  async reset(): Promise<void> {
    this.state = CircuitState.CLOSED;
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = undefined;
    this.nextAttemptTime = 0;
    
    await this.saveStateToRedis();
    await this.clearMetrics();
    logger.info(`Circuit breaker for ${this.serviceName} reset`);
  }

  // Redis persistence methods
  private async saveStateToRedis(): Promise<void> {
    try {
      const key = `circuit_breaker:${this.serviceName}`;
      const state = {
        state: this.state,
        failureCount: this.failureCount,
        successCount: this.successCount,
        lastFailureTime: this.lastFailureTime,
        nextAttemptTime: this.nextAttemptTime,
        updatedAt: Date.now()
      };
      
      await redis.setex(key, 3600, JSON.stringify(state)); // 1 hour TTL
    } catch (error) {
      logger.error('Failed to save circuit breaker state to Redis', {
        serviceName: this.serviceName,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async updateStateFromRedis(): Promise<void> {
    try {
      const key = `circuit_breaker:${this.serviceName}`;
      const stateData = await redis.get(key);
      
      if (stateData) {
        const state = JSON.parse(stateData);
        this.state = state.state;
        this.failureCount = state.failureCount;
        this.successCount = state.successCount;
        this.lastFailureTime = state.lastFailureTime;
        this.nextAttemptTime = state.nextAttemptTime;
      }
    } catch (error) {
      logger.error('Failed to load circuit breaker state from Redis', {
        serviceName: this.serviceName,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async recordMetric(type: 'success' | 'failure', error?: any): Promise<void> {
    try {
      const metricsKey = `circuit_breaker_metrics:${this.serviceName}`;
      const dailyKey = `${metricsKey}:${new Date().toISOString().split('T')[0]}`;
      
      const pipeline = await redis.pipeline();
      pipeline.hincrby(metricsKey, 'totalRequests', 1);
      pipeline.hincrby(metricsKey, `total${type === 'success' ? 'Successes' : 'Failures'}`, 1);
      pipeline.hset(metricsKey, `last${type === 'success' ? 'Success' : 'Failure'}Time`, Date.now());
      pipeline.expire(metricsKey, 86400 * 7); // 7 days

      // Daily metrics
      pipeline.hincrby(dailyKey, type, 1);
      pipeline.expire(dailyKey, 86400 * 30); // 30 days

      if (error) {
        pipeline.lpush(`${metricsKey}:errors`, JSON.stringify({
          error: error.message,
          timestamp: Date.now()
        }));
        pipeline.ltrim(`${metricsKey}:errors`, 0, 99); // Keep last 100 errors
      }

      await pipeline.exec();
    } catch (err) {
      logger.error('Failed to record circuit breaker metrics', {
        serviceName: this.serviceName,
        error: err instanceof Error ? err.message : String(err)
      });
    }
  }

  private async getMetricsFromRedis(): Promise<any> {
    try {
      const metricsKey = `circuit_breaker_metrics:${this.serviceName}`;
      const metrics = await redis.hgetall(metricsKey);
      
      return {
        totalRequests: parseInt(metrics.totalRequests || '0'),
        totalFailures: parseInt(metrics.totalFailures || '0'),
        totalSuccesses: parseInt(metrics.totalSuccesses || '0'),
        lastSuccessTime: metrics.lastSuccessTime ? parseInt(metrics.lastSuccessTime) : undefined,
        lastFailureTime: metrics.lastFailureTime ? parseInt(metrics.lastFailureTime) : undefined
      };
    } catch (error) {
      return {
        totalRequests: 0,
        totalFailures: 0,
        totalSuccesses: 0
      };
    }
  }

  private async clearMetrics(): Promise<void> {
    try {
      const metricsKey = `circuit_breaker_metrics:${this.serviceName}`;
      await redis.del(metricsKey, `${metricsKey}:errors`);
    } catch (error) {
      logger.error('Failed to clear circuit breaker metrics', {
        serviceName: this.serviceName,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

/**
 * Circuit breaker factory for common services
 */
export class CircuitBreakerFactory {
  private static breakers = new Map<string, CircuitBreaker>();

  static getBreaker(serviceName: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (!this.breakers.has(serviceName)) {
      this.breakers.set(serviceName, new CircuitBreaker(serviceName, config));
    }
    return this.breakers.get(serviceName)!;
  }

  static async getAllStats(): Promise<Record<string, CircuitBreakerStats>> {
    const stats: Record<string, CircuitBreakerStats> = {};
    
    for (const [serviceName, breaker] of this.breakers) {
      stats[serviceName] = await breaker.getStats();
    }
    
    return stats;
  }
}

// Predefined circuit breakers for common services
export const circuitBreakers = {
  documentIntelligence: CircuitBreakerFactory.getBreaker('document-intelligence', {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    timeout: 60000
  }),
  
  aiServices: CircuitBreakerFactory.getBreaker('ai-services', {
    failureThreshold: 5,
    recoveryTimeout: 60000,
    timeout: 120000
  }),
  
  externalApi: CircuitBreakerFactory.getBreaker('external-api', {
    failureThreshold: 3,
    recoveryTimeout: 30000,
    timeout: 30000
  }),
  
  database: CircuitBreakerFactory.getBreaker('database', {
    failureThreshold: 10,
    recoveryTimeout: 10000,
    timeout: 15000
  })
};
