import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/components/ui/charts';
import { Skeleton } from '@/components/ui/skeleton';

interface SearchAnalyticsData {
  topSearches: { term: string; count: number }[];
  searchVolume: { date: string; count: number }[];
  searchBySource: { source: string; count: number }[];
  averageResultsClicked: number;
  noResultsRate: number;
  searchConversionRate: number;
}

interface SearchPanelProps {
  data?: SearchAnalyticsData;
  isLoading?: boolean;
}

export function SearchPanel({ data, isLoading = false }: SearchPanelProps) {
  if (isLoading) {
    return (
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-72" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Search Analytics</CardTitle>
          <CardDescription>No search data available</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px]">
          <p className="text-muted-foreground">No search activity recorded yet</p>
        </CardContent>
      </Card>
    );
  }

  const searchVolumeData = {
    labels: data.searchVolume.map(item => item.date),
    datasets: [
      {
        label: 'Search Volume',
        data: data.searchVolume.map(item => item.count),
        borderColor: 'hsl(var(--primary))',
        backgroundColor: 'hsl(var(--primary) / 0.2)',
        tension: 0.3,
      },
    ],
  };

  const topSearchesData = {
    labels: data.topSearches.map(item => item.term),
    datasets: [
      {
        label: 'Search Count',
        data: data.topSearches.map(item => item.count),
        backgroundColor: 'hsl(var(--primary))',
      },
    ],
  };

  const searchBySourceData = {
    labels: data.searchBySource.map(item => item.source),
    datasets: [
      {
        label: 'Searches',
        data: data.searchBySource.map(item => item.count),
        backgroundColor: [
          'hsl(var(--primary))',
          'hsl(var(--secondary))',
          'hsl(var(--accent))',
          'hsl(var(--muted))',
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Search Analytics</CardTitle>
        <CardDescription>
          Insights into search behavior and performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="volume">
          <TabsList className="mb-4">
            <TabsTrigger value="volume">Search Volume</TabsTrigger>
            <TabsTrigger value="top">Top Searches</TabsTrigger>
            <TabsTrigger value="source">Search Sources</TabsTrigger>
            <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
          </TabsList>
          <TabsContent value="volume" className="h-[300px]">
            <LineChart data={searchVolumeData} />
          </TabsContent>
          <TabsContent value="top" className="h-[300px]">
            <BarChart data={topSearchesData} />
          </TabsContent>
          <TabsContent value="source" className="h-[300px]">
            <PieChart data={searchBySourceData} />
          </TabsContent>
          <TabsContent value="metrics">
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-1">Avg. Results Clicked</h3>
                <p className="text-2xl font-bold">{data.averageResultsClicked.toFixed(1)}</p>
              </div>
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-1">No Results Rate</h3>
                <p className="text-2xl font-bold">{(data.noResultsRate * 100).toFixed(1)}%</p>
              </div>
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-1">Conversion Rate</h3>
                <p className="text-2xl font-bold">{(data.searchConversionRate * 100).toFixed(1)}%</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
