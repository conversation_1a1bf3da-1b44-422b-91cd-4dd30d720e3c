/**
 * Organization Store
 * Manages organization state with Zustand
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { organizationService } from '@/services/organization-service'
import type { Organization } from '@/types/backend'
import type { OrganizationMember } from '@/types/store'
import type { ID } from '@/types'

export interface OrganizationState {
  // Organization data
  organizations: Organization[]
  currentOrganization: Organization | null
  members: Record<string, OrganizationMember[]>

  // UI state
  loading: boolean
  error: string | null
  lastUpdated: string | null

  // Internal state
  _hydrated: boolean
}

export interface OrganizationActions {
  // Organization management
  fetchOrganizations: () => Promise<void>
  selectOrganization: (organizationId: ID) => void
  createOrganization: (data: {
    name: string
    description?: string
    domain?: string
    settings?: any
  }) => Promise<Organization>
  updateOrganization: (organizationId: ID, data: Partial<Organization>) => Promise<Organization>
  deleteOrganization: (organizationId: ID) => Promise<void>

  // Member management
  fetchMembers: (organizationId: ID) => Promise<void>
  inviteMember: (organizationId: ID, data: {
    email: string
    role: string
    permissions?: string[]
  }) => Promise<void>
  updateMemberRole: (organizationId: ID, userId: ID, role: string) => Promise<void>
  removeMember: (organizationId: ID, userId: ID) => Promise<void>

  // Utilities
  clearError: () => void
  refresh: () => Promise<void>
  reset: () => void
  setCurrentOrganization: (organizationId: string | Organization) => void
}

export type OrganizationStore = OrganizationState & OrganizationActions

export const useOrganizationStore = create<OrganizationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      organizations: [],
      currentOrganization: null,
      members: {},
      loading: false,
      error: null,
      lastUpdated: null,
      _hydrated: false,

      // Actions
      fetchOrganizations: async () => {
        set({ loading: true, error: null })

        try {
          const response = await organizationService.getOrganizations()
          const organizations = response.data

          // Set current organization if none selected
          const current = get().currentOrganization
          const newCurrent = current || organizations[0] || null

          set({
            organizations,
            currentOrganization: newCurrent,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to fetch organizations',
          })
          throw error
        }
      },

      selectOrganization: (organizationId: ID) => {
        const organizations = get().organizations
        const organization = organizations.find(org => org.id === organizationId)
        
        if (organization) {
          set({ currentOrganization: organization })
        }
      },

      createOrganization: async (data) => {
        set({ loading: true, error: null })

        try {
          const organization = await organizationService.createOrganization(data)
          
          set(state => ({
            organizations: [...state.organizations, organization],
            currentOrganization: organization,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))

          return organization
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to create organization',
          })
          throw error
        }
      },

      updateOrganization: async (organizationId: ID, data) => {
        set({ loading: true, error: null })

        try {
          const updatedOrganization = await organizationService.updateOrganization(organizationId, data)
          
          set(state => ({
            organizations: state.organizations.map(org =>
              org.id === organizationId ? updatedOrganization : org
            ),
            currentOrganization: state.currentOrganization?.id === organizationId
              ? updatedOrganization
              : state.currentOrganization,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))

          return updatedOrganization
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to update organization',
          })
          throw error
        }
      },

      deleteOrganization: async (organizationId: ID) => {
        set({ loading: true, error: null })

        try {
          await organizationService.deleteOrganization(organizationId)
          
          set(state => {
            const organizations = state.organizations.filter(org => org.id !== organizationId)
            const currentOrganization = state.currentOrganization?.id === organizationId
              ? organizations[0] || null
              : state.currentOrganization

            return {
              organizations,
              currentOrganization,
              loading: false,
              error: null,
              lastUpdated: new Date().toISOString(),
            }
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to delete organization',
          })
          throw error
        }
      },

      fetchMembers: async (organizationId: ID) => {
        try {
          const members = await organizationService.getMembers(organizationId)
          
          set(state => ({
            members: {
              ...state.members,
              [organizationId]: members
            },
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch members' })
          throw error
        }
      },

      inviteMember: async (organizationId: ID, data) => {
        try {
          await organizationService.inviteMember(organizationId, data)
          // Refresh members after invite
          await get().fetchMembers(organizationId)
        } catch (error: any) {
          set({ error: error.message || 'Failed to invite member' })
          throw error
        }
      },

      updateMemberRole: async (organizationId: ID, userId: ID, role: string) => {
        try {
          await organizationService.updateMemberRole(organizationId, userId, role)
          // Refresh members after update
          await get().fetchMembers(organizationId)
        } catch (error: any) {
          set({ error: error.message || 'Failed to update member role' })
          throw error
        }
      },

      removeMember: async (organizationId: ID, userId: ID) => {
        try {
          await organizationService.removeMember(organizationId, userId)
          // Refresh members after removal
          await get().fetchMembers(organizationId)
        } catch (error: any) {
          set({ error: error.message || 'Failed to remove member' })
          throw error
        }
      },

      clearError: () => {
        set({ error: null })
      },

      refresh: async () => {
        await get().fetchOrganizations()
      },

      reset: () => {
        set({
          organizations: [],
          currentOrganization: null,
          members: {},
          loading: false,
          error: null,
          lastUpdated: null,
        })
      },

      setCurrentOrganization: (organizationIdOrOrg: string | Organization) => {
        if (typeof organizationIdOrOrg === 'string') {
          get().selectOrganization(organizationIdOrOrg)
        } else {
          set({ currentOrganization: organizationIdOrOrg })
        }
      },
    }),
    {
      name: 'organization-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        organizations: state.organizations,
        currentOrganization: state.currentOrganization,
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
        }
      },
    }
  )
)

// Selector hooks
export const useOrganizations = () => useOrganizationStore((state) => state.organizations)
export const useCurrentOrganization = () => useOrganizationStore((state) => state.currentOrganization)
export const useOrganizationMembers = (organizationId: ID) => 
  useOrganizationStore((state) => state.members[organizationId] || [])
export const useOrganizationLoading = () => useOrganizationStore((state) => state.loading)
export const useOrganizationError = () => useOrganizationStore((state) => state.error)

// Action hooks
export const useFetchOrganizations = () => useOrganizationStore((state) => state.fetchOrganizations)
export const useSelectOrganization = () => useOrganizationStore((state) => state.selectOrganization)

// Main organization hook for components
export const useOrganization = () => {
  const organizations = useOrganizations()
  const currentOrganization = useCurrentOrganization()
  const loading = useOrganizationLoading()
  const error = useOrganizationError()
  const fetchOrganizations = useFetchOrganizations()
  const selectOrganization = useSelectOrganization()

  return {
    organizations,
    currentOrganization,
    isLoading: loading,
    error,
    fetchOrganizations,
    selectOrganization,
  }
}
