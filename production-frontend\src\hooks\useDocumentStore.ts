/**
 * Document Store Hook
 * Provides access to document store functionality
 */

import { useDocumentStore as useDocumentStoreBase } from '../stores/document-store'

// Re-export the main store hook
export const useDocumentStore = useDocumentStoreBase

// Selector hooks for specific document data
export const useDocuments = () => {
  const { 
    documents, 
    selectedDocument, 
    filters, 
    loading, 
    error,
    lastUpdated 
  } = useDocumentStore()
  
  return {
    documents,
    selectedDocument,
    filters,
    loading,
    error,
    lastUpdated,
  }
}

export const useDocumentList = () => useDocumentStore((state) => state.documents)
export const useSelectedDocument = () => useDocumentStore((state) => state.selectedDocument)
export const useDocumentFilters = () => useDocumentStore((state) => state.filters)
export const useDocumentLoading = () => useDocumentStore((state) => state.loading)
export const useDocumentError = () => useDocumentStore((state) => state.error)

// Action hooks
export const useSelectDocument = () => useDocumentStore((state) => state.selectDocument)
export const useAddDocument = () => useDocumentStore((state) => state.uploadDocument)
export const useUpdateDocument = () => useDocumentStore((state) => state.updateDocument)
export const useRemoveDocument = () => useDocumentStore((state) => state.deleteDocument)
export const useSetDocumentFilters = () => useDocumentStore((state) => state.setFilters)
export const useRefreshDocuments = () => useDocumentStore((state) => state.refreshDocuments)
