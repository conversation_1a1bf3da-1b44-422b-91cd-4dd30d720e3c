/**
 * Store Utilities
 * Utility functions for creating and managing Zustand stores
 */

import { create, StateCreator } from 'zustand'
import { persist, createJSONStorage, devtools, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// Base store interface
export interface BaseStore {
  loading: boolean
  error: string | null
  lastUpdated: string | null
  _hydrated: boolean
}

// Base store actions
export interface BaseStoreActions {
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
  reset: () => void
}

// Store configuration options
export interface StoreConfig {
  name: string
  version?: number
  enableDevtools?: boolean
  enablePersistence?: boolean
  enableImmer?: boolean
  enableSubscriptions?: boolean
  persistenceOptions?: {
    partialize?: (state: any) => any
    onRehydrateStorage?: () => (state: any) => void
    storage?: any
  }
}

// Create a basic store with common patterns
export function createStore<T extends BaseStore & BaseStoreActions>(
  stateCreator: StateCreator<T, [], [], T>,
  config: StoreConfig
) {
  let store = stateCreator

  // Add subscriptions middleware if enabled
  if (config.enableSubscriptions) {
    store = subscribeWithSelector(store) as any
  }

  // Add immer middleware if enabled
  if (config.enableImmer) {
    store = immer(store as any) as any
  }

  // Add devtools middleware if enabled
  if (config.enableDevtools && process.env.NODE_ENV === 'development') {
    store = devtools(store, { name: config.name }) as any
  }

  return create<T>()(store)
}

// Create a persistent store with common patterns
export function createPersistentStore<T extends BaseStore & BaseStoreActions>(
  stateCreator: StateCreator<T, [], [], T>,
  config: StoreConfig
) {
  let store = stateCreator

  // Add subscriptions middleware if enabled
  if (config.enableSubscriptions) {
    store = subscribeWithSelector(store) as any
  }

  // Add immer middleware if enabled
  if (config.enableImmer) {
    store = immer(store as any) as any
  }

  // Add persistence middleware
  store = persist(
    store,
    {
      name: `${config.name}-v${config.version || 1}`,
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: config.persistenceOptions?.partialize || ((state: any) => ({
        ...state,
        loading: false,
        error: null,
      })),
      onRehydrateStorage: config.persistenceOptions?.onRehydrateStorage || (() => (state: any) => {
        if (state) {
          state._hydrated = true
          state.loading = false
          state.error = null
        }
      }),
    }
  ) as any

  // Add devtools middleware if enabled
  if (config.enableDevtools && process.env.NODE_ENV === 'development') {
    store = devtools(store, { name: config.name }) as any
  }

  return create<T>()(store)
}

// Create a store with devtools enabled
export function createStoreWithDevtools<T extends BaseStore & BaseStoreActions>(
  stateCreator: StateCreator<T, [], [], T>,
  name: string
) {
  return create<T>()(
    devtools(
      subscribeWithSelector(stateCreator),
      { name }
    )
  )
}

// Base store creator with common functionality
export function createBaseStore<T extends Record<string, any>>(
  initialState: T,
  actions: (set: any, get: any) => Record<string, any>,
  config: StoreConfig
) {
  const stateCreator: StateCreator<T & BaseStore & BaseStoreActions, [], [], T & BaseStore & BaseStoreActions> = (set, get) => ({
    // Base state
    ...initialState,
    loading: false,
    error: null,
    lastUpdated: null,
    _hydrated: false,

    // Base actions
    setLoading: (loading: boolean) => set({ loading } as any),
    setError: (error: string | null) => set({ error } as any),
    clearError: () => set({ error: null } as any),
    reset: () => set({
      ...initialState,
      loading: false,
      error: null,
      lastUpdated: null,
    } as any),

    // Custom actions
    ...actions(set, get),
  })

  if (config.enablePersistence) {
    return createPersistentStore(stateCreator, config)
  } else {
    return createStore(stateCreator, config)
  }
}

// Store middleware helpers
export const withLoading = <T extends any[]>(
  fn: (...args: T) => Promise<any>
) => {
  return async function(this: any, ...args: T) {
    this.setLoading(true)
    this.clearError()
    
    try {
      const result = await fn.apply(this, args)
      return result
    } catch (error) {
      this.setError(error instanceof Error ? error.message : 'An error occurred')
      throw error
    } finally {
      this.setLoading(false)
    }
  }
}

// Store action wrapper for error handling
export const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => R,
  errorMessage?: string
) => {
  return function(this: any, ...args: T): R {
    try {
      this.clearError()
      return fn.apply(this, args)
    } catch (error) {
      const message = errorMessage || (error instanceof Error ? error.message : 'An error occurred')
      this.setError(message)
      throw error
    }
  }
}

// Store selector helpers
export const createSelectors = <T extends Record<string, any>>(store: any) => {
  const selectors = {} as any
  
  Object.keys(store.getState()).forEach((key) => {
    selectors[`use${key.charAt(0).toUpperCase()}${key.slice(1)}`] = () => store((state: T) => state[key])
  })
  
  return selectors
}

// Store subscription helpers
export const subscribeToChanges = <T>(
  store: any,
  selector: (state: T) => any,
  callback: (value: any, previousValue: any) => void
) => {
  return store.subscribe(selector, callback)
}

// Store reset helper
export const resetAllStores = (stores: Array<{ getState: () => any }>) => {
  stores.forEach(store => {
    const state = store.getState()
    if (typeof state.reset === 'function') {
      state.reset()
    }
  })
}

// Store hydration helper
export const waitForHydration = async (stores: Array<{ getState: () => any }>) => {
  const checkHydration = () => {
    return stores.every(store => {
      const state = store.getState()
      return state._hydrated === true
    })
  }

  if (checkHydration()) {
    return Promise.resolve()
  }

  return new Promise<void>((resolve) => {
    const interval = setInterval(() => {
      if (checkHydration()) {
        clearInterval(interval)
        resolve()
      }
    }, 50)
  })
}

// Store debugging helpers
export const logStoreState = (storeName: string, store: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🏪 ${storeName} Store State`)
    console.log(store.getState())
    console.groupEnd()
  }
}

export const logStoreAction = (storeName: string, actionName: string, payload?: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🎬 ${storeName}.${actionName}`, payload)
  }
}

// Store performance helpers
export const measureStoreAction = <T extends any[], R>(
  storeName: string,
  actionName: string,
  fn: (...args: T) => R
) => {
  return function(...args: T): R {
    if (process.env.NODE_ENV === 'development') {
      const start = performance.now()
      const result = fn(...args)
      const end = performance.now()
      console.log(`⏱️ ${storeName}.${actionName} took ${end - start}ms`)
      return result
    }
    return fn(...args)
  }
}

// Store validation helpers
export const validateStoreState = <T>(
  state: T,
  schema: Record<string, (value: any) => boolean>
): boolean => {
  return Object.entries(schema).every(([key, validator]) => {
    const value = (state as any)[key]
    return validator(value)
  })
}

// Export types (remove duplicate exports)
