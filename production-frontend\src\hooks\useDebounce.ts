/**
 * Debounce <PERSON>
 * Delays the execution of a function until after a specified delay
 */

import { useState, useEffect, useCallback, useRef } from 'react'

/**
 * Debounce a value - useful for search inputs
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Debounce a callback function
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): T {
  const timeoutRef = useRef<NodeJS.Timeout>()

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        callback(...args)
      }, delay)
    },
    [callback, delay, ...deps]
  ) as T

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return debouncedCallback
}

/**
 * Debounced state hook - combines useState with debouncing
 */
export function useDebouncedState<T>(
  initialValue: T,
  delay: number
): [T, T, React.Dispatch<React.SetStateAction<T>>] {
  const [value, setValue] = useState<T>(initialValue)
  const debouncedValue = useDebounce(value, delay)

  return [value, debouncedValue, setValue]
}

/**
 * Advanced debounce hook with immediate execution option
 */
export function useAdvancedDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  options: {
    immediate?: boolean // Execute immediately on first call
    maxWait?: number // Maximum time to wait before executing
  } = {}
): [T, () => void, () => void] {
  const timeoutRef = useRef<NodeJS.Timeout>()
  const maxTimeoutRef = useRef<NodeJS.Timeout>()
  const lastCallTimeRef = useRef<number>(0)
  const lastArgsRef = useRef<Parameters<T>>()

  const { immediate = false, maxWait } = options

  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = undefined
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current)
      maxTimeoutRef.current = undefined
    }
  }, [])

  const flush = useCallback(() => {
    if (lastArgsRef.current) {
      callback(...lastArgsRef.current)
      cancel()
    }
  }, [callback, cancel])

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now()
      lastArgsRef.current = args

      // Immediate execution on first call
      if (immediate && !timeoutRef.current) {
        callback(...args)
        lastCallTimeRef.current = now
      }

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // Set up max wait timeout if specified
      if (maxWait && !maxTimeoutRef.current) {
        maxTimeoutRef.current = setTimeout(() => {
          flush()
        }, maxWait)
      }

      // Set up regular debounce timeout
      timeoutRef.current = setTimeout(() => {
        if (!immediate || now - lastCallTimeRef.current >= delay) {
          callback(...args)
        }
        cancel()
      }, delay)
    },
    [callback, delay, immediate, maxWait, flush, cancel]
  ) as T

  // Cleanup on unmount
  useEffect(() => {
    return cancel
  }, [cancel])

  return [debouncedCallback, cancel, flush]
}

/**
 * Debounced async function hook
 */
export function useDebouncedAsync<T extends (...args: any[]) => Promise<any>>(
  asyncFunction: T,
  delay: number
): [T, boolean, Error | null, () => void] {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const abortControllerRef = useRef<AbortController>()

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setLoading(false)
    setError(null)
  }, [])

  const debouncedFunction = useDebouncedCallback(
    async (...args: Parameters<T>) => {
      // Cancel previous request
      cancel()

      // Create new abort controller
      abortControllerRef.current = new AbortController()
      
      setLoading(true)
      setError(null)

      try {
        const result = await asyncFunction(...args)
        
        // Check if request was aborted
        if (!abortControllerRef.current.signal.aborted) {
          setLoading(false)
          return result
        }
      } catch (err) {
        if (!abortControllerRef.current.signal.aborted) {
          setError(err as Error)
          setLoading(false)
        }
        throw err
      }
    },
    delay
  ) as T

  return [debouncedFunction, loading, error, cancel]
}

/**
 * Search hook with debouncing
 */
export function useDebouncedSearch<T>(
  searchFunction: (query: string) => Promise<T[]>,
  delay: number = 300
): {
  query: string
  setQuery: (query: string) => void
  results: T[]
  loading: boolean
  error: Error | null
  clear: () => void
} {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<T[]>([])
  const debouncedQuery = useDebounce(query, delay)
  
  const [search, loading, error, cancel] = useDebouncedAsync(searchFunction, 0)

  useEffect(() => {
    if (debouncedQuery.trim()) {
      search(debouncedQuery).then(setResults).catch(() => setResults([]))
    } else {
      setResults([])
      cancel()
    }
  }, [debouncedQuery, search, cancel])

  const clear = useCallback(() => {
    setQuery('')
    setResults([])
    cancel()
  }, [cancel])

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clear,
  }
}

/**
 * Form validation with debouncing
 */
export function useDebouncedValidation<T>(
  value: T,
  validator: (value: T) => string | null,
  delay: number = 500
): {
  error: string | null
  isValidating: boolean
  isValid: boolean
} {
  const [error, setError] = useState<string | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  
  const debouncedValidator = useDebouncedCallback(
    (val: T) => {
      setIsValidating(true)
      const validationError = validator(val)
      setError(validationError)
      setIsValidating(false)
    },
    delay
  )

  useEffect(() => {
    if (value !== undefined && value !== null && value !== '') {
      debouncedValidator(value)
    } else {
      setError(null)
      setIsValidating(false)
    }
  }, [value, debouncedValidator])

  return {
    error,
    isValidating,
    isValid: error === null && !isValidating,
  }
}
