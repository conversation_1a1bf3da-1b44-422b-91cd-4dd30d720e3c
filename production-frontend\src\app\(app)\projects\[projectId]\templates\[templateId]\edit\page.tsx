'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useProject } from '@/hooks/projects/useProject';
import { useTemplate } from '@/hooks/templates';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { TemplateEditor } from '@/components/templates/template-editor';

export default function EditTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;
  const templateId = params?.templateId as string;

  const { project } = useProject({ projectId });
  const { data: template, isLoading, error } = useTemplate(templateId);

  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateType, setTemplateType] = useState('standard');
  const [templateContent, setTemplateContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load template details',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  // Initialize form with template data when loaded
  useEffect(() => {
    if (template) {
      setTemplateName((template as any).name || 'Template Name');
      setTemplateDescription((template as any).description || '');
      setTemplateType((template as any).type || 'standard');
      // Handle content - convert to string if it's an object
      const content = (template as any).content;
      if (typeof content === 'string') {
        setTemplateContent(content);
      } else if (content && typeof content === 'object') {
        setTemplateContent(JSON.stringify(content, null, 2));
      } else {
        setTemplateContent('');
      }
    }
  }, [template]);

  const handleSave = async () => {
    if (!templateName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Template name is required',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);

    try {
      // This would be an API call in a real application
      // const response = await fetch(`/api/templates/${templateId}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     name: templateName,
      //     description: templateDescription,
      //     type: templateType,
      //     content: templateContent,
      //   })
      // });

      // if (!response.ok) throw new Error('Failed to update template');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Success',
        description: 'Template updated successfully',
      });

      router.push(`/projects/${projectId}/templates/${templateId}`);
    } catch (error) {
      console.error('Error updating template:', error);
      toast({
        title: 'Error',
        description: 'Failed to update template. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading template details...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <Button
          variant="ghost"
          size="sm"
          className="mb-2"
          onClick={() => router.push(`/projects/${projectId}/templates/${templateId}`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Template
        </Button>
        <h1 className="text-3xl font-bold">Edit Template</h1>
        <p className="text-muted-foreground">Project: {project?.name}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="space-y-6 md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Template Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="Enter template name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  placeholder="Enter template description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Template Type</Label>
                <Select
                  value={templateType}
                  onValueChange={setTemplateType}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select template type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="invoice">Invoice</SelectItem>
                    <SelectItem value="report">Report</SelectItem>
                    <SelectItem value="letter">Letter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="pt-4 space-y-2">
                <Button
                  className="w-full"
                  onClick={handleSave}
                  disabled={isSaving || !templateName.trim()}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push(`/projects/${projectId}/templates/${templateId}`)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Template Editor</CardTitle>
            </CardHeader>
            <CardContent>
              <TemplateEditor
                value={templateContent}
                onChange={setTemplateContent}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
