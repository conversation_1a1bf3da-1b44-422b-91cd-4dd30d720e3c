/**
 * Zustand Store Types
 */

import type {
  ID,
  Timestamp
} from './index'
import type { User, StandardToken, UserOrganization as Organization } from './user'
import type { Document } from './document'
import type { Project } from './project'
import type { Template } from './template'
import type { Workflow } from './workflow'

// Additional types for store
export interface Notification {
  id: ID
  type: string
  title: string
  message: string
  read: boolean
  createdAt: Timestamp
}

export interface NotificationPreferences {
  email: {
    enabled: boolean
    frequency: 'immediate' | 'daily' | 'weekly'
    types: string[]
  }
  inApp: {
    enabled: boolean
    types: string[]
  }
  documentUploaded: boolean
  documentProcessed: boolean
  commentAdded: boolean
  mentionedInComment: boolean
  projectInvitation: boolean
  organizationInvitation: boolean
}

export interface DashboardPreferences {
  showWelcomeMessage: boolean
  defaultView: 'grid' | 'list'
  itemsPerPage: number
  autoRefresh: boolean
  refreshInterval: number
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  timezone: string
  notifications: NotificationPreferences
  dashboard: DashboardPreferences
}

// Base store interface
export interface BaseStore {
  loading: boolean
  error: string | null
  lastUpdated?: Timestamp
}

// Auth Store
export interface AuthStore extends BaseStore {
  // State
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  expiresAt: number | null
  
  // Actions
  login: (credentials: { email: string; password: string }) => Promise<void>
  logout: () => Promise<void>
  refreshAuth: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  setToken: (token: StandardToken) => void
  clearAuth: () => void
  checkAuthStatus: () => boolean
  
  // Computed
  isTokenExpired: () => boolean
  timeUntilExpiry: () => number
}

// Dashboard Store
export interface DashboardStore extends BaseStore {
  // State
  metrics: DashboardMetrics | null
  widgets: DashboardWidget[]
  layout: DashboardLayout
  filters: DashboardFilters
  preferences?: DashboardPreferences
  lastUpdated?: Timestamp

  // Additional state for dashboard page
  organizations: any[]
  organizationsLoading: boolean
  organizationsError: string | null
  projects: any[]
  projectsLoading: boolean
  projectsError: string | null
  documents: any[]
  documentsLoading: boolean
  documentsError: string | null

  // Actions
  fetchMetrics: (filters?: DashboardFilters) => Promise<void>
  updateWidget: (widgetId: string, config: Partial<DashboardWidget>) => void
  addWidget: (widget: DashboardWidget) => void
  removeWidget: (widgetId: string) => void
  updateLayout: (layout: DashboardLayout) => void
  setFilters: (filters: Partial<DashboardFilters>) => void
  refreshData: () => Promise<void>

  // Additional actions for dashboard page
  fetchOrganizations: () => Promise<void>
  fetchProjects: (organizationId?: string) => Promise<void>
  fetchDocuments: (projectId?: string, organizationId?: string) => Promise<void>
  clearError: () => void

  // Additional actions expected by hooks
  updatePreferences?: (preferences: Partial<DashboardPreferences>) => Promise<void>
  refresh?: () => Promise<void>
}

export interface DashboardMetrics {
  totalDocuments: number
  totalProjects: number
  totalUsers: number
  activeWorkflows: number
  completedTasks: number
  recentActivity: ActivityItem[]
  performanceData: PerformanceMetric[]
}

export interface DashboardWidget {
  id: string
  type: 'chart' | 'metric' | 'list' | 'activity'
  title: string
  position: { x: number; y: number; width: number; height: number }
  config: Record<string, any>
  visible: boolean
}

export interface DashboardLayout {
  columns: number
  rowHeight: number
  margin: [number, number]
  containerPadding: [number, number]
}

export interface DashboardFilters {
  dateRange: { start: Timestamp; end: Timestamp }
  organizationId?: ID
  projectIds?: ID[]
  status?: string[]
}

export interface ActivityItem {
  id: ID
  type: string
  description: string
  timestamp: Timestamp
  user: { id: ID; name: string; avatar?: string }
}

export interface PerformanceMetric {
  name: string
  value: number
  change: number
  trend: 'up' | 'down' | 'stable'
}

// Document Store
export interface DocumentStore extends BaseStore {
  // State
  documents: Document[]
  selectedDocument: Document | null
  uploadProgress: Record<string, number>
  processingStatus: Record<ID, DocumentProcessingStatus>
  filters?: DocumentFilters

  // Actions
  fetchDocuments: (filters?: DocumentFilters) => Promise<void>
  uploadDocument: (file: File, metadata?: Record<string, any>) => Promise<Document>
  selectDocument: (documentId: ID) => Promise<void>
  updateDocument: (documentId: ID, data: Partial<Document>) => Promise<void>
  deleteDocument: (documentId: ID) => Promise<void>
  processDocument: (documentId: ID, options?: ProcessingOptions) => Promise<void>
  shareDocument: (documentId: ID, shareData: ShareData) => Promise<void>

  // Additional actions expected by hooks
  addDocument?: (document: Document) => void
  removeDocument?: (documentId: ID) => void
  setFilters?: (filters: DocumentFilters) => void
  refresh?: () => Promise<void>

  // Computed
  getDocumentsByProject: (projectId: ID) => Document[]
  getRecentDocuments: (limit?: number) => Document[]
}

export interface DocumentFilters {
  projectId?: ID
  status?: string[]
  type?: string[]
  dateRange?: { start: Timestamp; end: Timestamp }
  search?: string
}

export interface DocumentProcessingStatus {
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  message?: string
  result?: any
}

export interface ProcessingOptions {
  extractText: boolean
  analyzeLayout: boolean
  extractTables: boolean
  generateSummary: boolean
}

export interface ShareData {
  users: ID[]
  permissions: string[]
  expiresAt?: Timestamp
  message?: string
}

// Project Store
export interface ProjectStore extends BaseStore {
  // State
  projects: Project[]
  selectedProject: Project | null
  members: Record<ID, ProjectMember[]>

  // Actions
  fetchProjects: () => Promise<void>
  createProject: (data: CreateProjectData) => Promise<Project>
  updateProject: (projectId: ID, data: Partial<Project>) => Promise<void>
  deleteProject: (projectId: ID) => Promise<void>
  selectProject: (projectId: ID) => Promise<void>
  addMember: (projectId: ID, member: ProjectMemberData) => Promise<void>
  removeMember: (projectId: ID, userId: ID) => Promise<void>
  updateMemberRole: (projectId: ID, userId: ID, role: string) => Promise<void>

  // Additional actions expected by hooks
  refresh?: () => Promise<void>
  fetchMembers?: (projectId: ID) => Promise<void>

  // Computed
  getActiveProjects: () => Project[]
  getProjectsByOrganization: (organizationId: ID) => Project[]
}

export interface CreateProjectData {
  name: string
  description?: string
  organizationId?: ID
  visibility?: 'private' | 'public' | 'organization'
  tags?: string[]
  templateId?: ID
  members?: ProjectMemberData[]
  settings?: any
}

export interface ProjectMemberData {
  userId: ID
  role: string
  permissions?: string[]
}

export interface ProjectMember {
  userId: ID
  user: User
  role: string
  joinedAt: Timestamp
}

export interface ProjectMemberData {
  userId: ID
  role: string
}

// Template Store
export interface TemplateStore extends BaseStore {
  // State
  templates: Template[]
  selectedTemplate: Template | null
  categories: TemplateCategory[]
  
  // Actions
  fetchTemplates: (filters?: TemplateFilters) => Promise<void>
  createTemplate: (data: CreateTemplateData) => Promise<Template>
  updateTemplate: (templateId: ID, data: Partial<Template>) => Promise<void>
  deleteTemplate: (templateId: ID) => Promise<void>
  selectTemplate: (templateId: ID) => Promise<void>
  cloneTemplate: (templateId: ID, name: string) => Promise<Template>
  shareTemplate: (templateId: ID, shareData: ShareData) => Promise<void>
  
  // Computed
  getTemplatesByCategory: (categoryId: ID) => Template[]
  getPublicTemplates: () => Template[]
}

export interface TemplateFilters {
  categoryId?: ID
  isPublic?: boolean
  organizationId?: ID
  search?: string
}

export interface CreateTemplateData {
  name: string
  description?: string
  categoryId: ID
  fields: TemplateField[]
  isPublic: boolean
}

export interface TemplateCategory {
  id: ID
  name: string
  description?: string
  icon?: string
}

export interface TemplateField {
  id: string
  name: string
  type: FieldType
  required?: boolean
  helpText?: string
  options?: FieldOption[]
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

// Workflow Store
export interface WorkflowStore extends BaseStore {
  // State
  workflows: Workflow[]
  selectedWorkflow: Workflow | null
  executions: Record<ID, WorkflowExecution[]>
  
  // Actions
  fetchWorkflows: () => Promise<void>
  createWorkflow: (data: CreateWorkflowData) => Promise<Workflow>
  updateWorkflow: (workflowId: ID, data: Partial<Workflow>) => Promise<void>
  deleteWorkflow: (workflowId: ID) => Promise<void>
  selectWorkflow: (workflowId: ID) => Promise<void>
  executeWorkflow: (workflowId: ID, input?: any) => Promise<WorkflowExecution>
  pauseWorkflow: (workflowId: ID) => Promise<void>
  resumeWorkflow: (workflowId: ID) => Promise<void>
  
  // Computed
  getActiveWorkflows: () => Workflow[]
  getWorkflowsByProject: (projectId: ID) => Workflow[]
}

export interface CreateWorkflowData {
  name: string
  description?: string
  projectId?: ID
  steps: WorkflowStep[]
  triggers: WorkflowTrigger[]
}

export interface WorkflowExecution {
  id: ID
  workflowId: ID
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  startedAt: Timestamp
  completedAt?: Timestamp
  input?: any
  output?: any
  error?: string
  steps: WorkflowStepExecution[]
}

export interface WorkflowStep {
  id: string
  type: string
  name: string
  config: Record<string, any>
  position: { x: number; y: number }
}

export interface WorkflowTrigger {
  type: string
  config: Record<string, any>
}

export interface WorkflowStepExecution {
  stepId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  startedAt?: Timestamp
  completedAt?: Timestamp
  input?: any
  output?: any
  error?: string
}

// Notification Store
export interface NotificationStore extends BaseStore {
  // State
  notifications: Notification[]
  unreadCount: number
  preferences: NotificationPreferences
  
  // Actions
  fetchNotifications: () => Promise<void>
  markAsRead: (notificationId: ID) => Promise<void>
  markAllAsRead: () => Promise<void>
  deleteNotification: (notificationId: ID) => Promise<void>
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>
  addNotification: (notification: Notification) => void
  
  // Computed
  getUnreadNotifications: () => Notification[]
  getNotificationsByType: (type: string) => Notification[]
}



// User Preferences Store
export interface PreferencesStore extends BaseStore {
  // State
  preferences: UserPreferences
  
  // Actions
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>
  resetPreferences: () => Promise<void>
  
  // Theme
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  
  // Language
  setLanguage: (language: string) => void
  
  // Dashboard
  updateDashboardPreferences: (preferences: Partial<DashboardPreferences>) => void
}



// Organization Store
export interface OrganizationStore extends BaseStore {
  // State
  organizations: Organization[]
  selectedOrganization: Organization | null
  members: Record<ID, OrganizationMember[]>
  
  // Actions
  fetchOrganizations: () => Promise<void>
  selectOrganization: (organizationId: ID) => Promise<void>
  updateOrganization: (organizationId: ID, data: Partial<Organization>) => Promise<void>
  inviteMember: (organizationId: ID, email: string, role: string) => Promise<void>
  removeMember: (organizationId: ID, userId: ID) => Promise<void>
  updateMemberRole: (organizationId: ID, userId: ID, role: string) => Promise<void>
  
  // Computed
  getCurrentOrganization: () => Organization | null
  getUserOrganizations: () => Organization[]
}

export interface OrganizationMember {
  userId: ID
  user: User
  role: string
  joinedAt: Timestamp
}

// Global App Store (combines all stores)
export interface AppStore {
  auth: AuthStore
  dashboard: DashboardStore
  documents: DocumentStore
  projects: ProjectStore
  templates: TemplateStore
  workflows: WorkflowStore
  notifications: NotificationStore
  preferences: PreferencesStore
  organizations: OrganizationStore
}

// Store actions for persistence
export interface StoreActions {
  persist: () => void
  hydrate: () => void
  reset: () => void
}

// Store middleware types
export interface StoreMiddleware<T> {
  (config: T): T
}

export interface PersistOptions {
  name: string
  storage: Storage
  partialize?: (state: any) => any
  onRehydrateStorage?: () => void
}

// Additional store types for better type safety
export interface DevtoolsOptions {
  name?: string
  enabled?: boolean
}

export interface StoreSelector<T, U> {
  (state: T): U
}

export interface StoreSubscription<T> {
  (state: T, previousState: T): void
}

export interface StoreErrorHandler {
  (error: Error, context?: string): void
}

// Additional missing types for components
export type FieldType = 'text' | 'email' | 'number' | 'date' | 'select' | 'multiselect' | 'checkbox' | 'textarea' | 'richtext' | 'file' | 'signature' | 'table'

// Field types constants
export const FIELD_TYPES = {
  TEXT: 'text' as const,
  EMAIL: 'email' as const,
  NUMBER: 'number' as const,
  DATE: 'date' as const,
  SELECT: 'select' as const,
  MULTI_SELECT: 'multiselect' as const,
  CHECKBOX: 'checkbox' as const,
  TEXTAREA: 'textarea' as const,
  RICH_TEXT: 'richtext' as const,
  FILE: 'file' as const,
  SIGNATURE: 'signature' as const,
  TABLE: 'table' as const,
} as const

export interface FieldOption {
  label: string
  value: string | number
}

export interface TemplateVariable {
  name: string
  type: string
  description?: string
  defaultValue?: any
  required?: boolean
  category?: string
}

export interface TemplatePreviewResult {
  content: string
  html?: string
  variables: Record<string, any>
  errors?: string[]
}

export type TemplateStatus = 'draft' | 'published' | 'archived'
export type TemplateType = 'document' | 'form' | 'contract' | 'report'

// Template enums for components
export const TemplateStatus = {
  DRAFT: 'draft' as const,
  PUBLISHED: 'published' as const,
  ARCHIVED: 'archived' as const,
} as const

export const TemplateType = {
  DOCUMENT: 'document' as const,
  FORM: 'form' as const,
  CONTRACT: 'contract' as const,
  REPORT: 'report' as const,
  EMAIL: 'email' as const,
  WORKFLOW: 'workflow' as const,
  PROJECT: 'project' as const,
} as const

// Document status enum
export const DocumentStatus = {
  DRAFT: 'draft' as const,
  PROCESSING: 'processing' as const,
  PROCESSED: 'processed' as const,
  COMPLETED: 'completed' as const,
  FAILED: 'failed' as const,
  ARCHIVED: 'archived' as const,
} as const


