"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/hooks/useAuth";
import { backendApiClient } from "@/services/backend-api-client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import {
  Shield,
  Key,
  Clock,
  LogOut,
  AlertTriangle,
  Check,
  Globe
} from "lucide-react";
import { format } from "date-fns";
import { AuditLogViewer } from "@/components/security/audit-log-viewer";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

interface UserSession {
  id: string;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
  lastActiveAt: string;
  isCurrent: boolean;
}

interface UserSecuritySettings {
  mfaEnabled?: boolean;
  mfaVerified?: boolean;
  lastPasswordChange?: string;
  passwordExpiresAt?: string;
  sessions?: UserSession[];
  loginHistory?: {
    timestamp: string;
    ipAddress: string;
    userAgent: string;
    success: boolean;
    failureReason?: string;
  }[];
}

export default function UserSecurityPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { user } = useAuth();

  // Query to get user security settings
  const { data, isLoading } = useQuery({
    queryKey: ['userSecuritySettings'],
    queryFn: async () => {
      return await backendApiClient.request<UserSecuritySettings>('/user/security');
    }
  });

  // Query to get security info
  useQuery({
    queryKey: ['securityInfo'],
    queryFn: async () => {
      return { lastLogin: new Date().toISOString() };
    },
  });

  // Mutation to enable MFA
  const enableMfaMutation = useMutation({
    mutationFn: async () => {
      return await backendApiClient.request('/user/security/mfa/enable', { method: 'POST' });
    },
    onSuccess: () => {
      toast({
        title: "MFA setup initiated",
        description: "Follow the instructions to set up multi-factor authentication.",
      });
      queryClient.invalidateQueries({ queryKey: ['userSecuritySettings'] });
      queryClient.invalidateQueries({ queryKey: ['securityInfo'] });
    },
    onError: () => {
      toast({
        title: "Error enabling MFA",
        description: "There was a problem enabling multi-factor authentication.",
        variant: "destructive",
      });
    },
  });

  // Mutation to disable MFA
  const disableMfaMutation = useMutation({
    mutationFn: async () => {
      return await backendApiClient.request('/user/security/mfa/disable', { method: 'POST' });
    },
    onSuccess: () => {
      toast({
        title: "MFA disabled",
        description: "Multi-factor authentication has been disabled.",
      });
      queryClient.invalidateQueries({ queryKey: ['userSecuritySettings'] });
      queryClient.invalidateQueries({ queryKey: ['securityInfo'] });
    },
    onError: () => {
      toast({
        title: "Error disabling MFA",
        description: "There was a problem disabling multi-factor authentication.",
        variant: "destructive",
      });
    },
  });

  // Mutation to terminate session
  const terminateSessionMutation = useMutation({
    mutationFn: async (sessionId: string) => {
      return await backendApiClient.request(`/user/security/sessions/${sessionId}`, { method: 'DELETE' });
    },
    onSuccess: () => {
      toast({
        title: "Session terminated",
        description: "The session has been terminated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['userSecuritySettings'] });
    },
    onError: () => {
      toast({
        title: "Error terminating session",
        description: "There was a problem terminating the session.",
        variant: "destructive",
      });
    },
  });

  // Mutation to terminate all other sessions
  const terminateAllSessionsMutation = useMutation({
    mutationFn: async () => {
      return await backendApiClient.request('/user/security/sessions', { method: 'DELETE' });
    },
    onSuccess: () => {
      toast({
        title: "All sessions terminated",
        description: "All other sessions have been terminated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['userSecuritySettings'] });
    },
    onError: () => {
      toast({
        title: "Error terminating sessions",
        description: "There was a problem terminating the sessions.",
        variant: "destructive",
      });
    },
  });

  // Handle MFA toggle
  const handleMfaToggle = (enabled: boolean) => {
    if (enabled) {
      enableMfaMutation.mutate();
    } else {
      disableMfaMutation.mutate();
    }
  };

  // Handle session termination
  const handleTerminateSession = (sessionId: string) => {
    terminateSessionMutation.mutate(sessionId);
  };

  // Handle termination of all other sessions
  const handleTerminateAllSessions = () => {
    terminateAllSessionsMutation.mutate();
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Security Settings</h1>
        <p className="text-muted-foreground">
          Manage your account security settings
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" /> Account Security
            </CardTitle>
            <CardDescription>
              Manage your account security settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : (
              <>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <div className="text-base font-medium">Multi-Factor Authentication</div>
                    <div className="text-sm text-muted-foreground">
                      Add an extra layer of security to your account
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {data?.mfaEnabled ? (
                      <Badge variant="outline" className="gap-1 border-green-500 text-green-500">
                        <Check className="h-3 w-3" /> Enabled
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="gap-1 border-yellow-500 text-yellow-500">
                        <AlertTriangle className="h-3 w-3" /> Disabled
                      </Badge>
                    )}
                    <Switch
                      checked={data?.mfaEnabled || false}
                      onCheckedChange={handleMfaToggle}
                      disabled={enableMfaMutation.isPending || disableMfaMutation.isPending}
                    />
                  </div>
                </div>

                <div className="rounded-lg border p-4">
                  <div className="space-y-2">
                    <div className="text-base font-medium">Password Status</div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-muted-foreground">Last Changed</div>
                        <div className="font-medium">
                          {data?.lastPasswordChange ?
                            format(new Date(data.lastPasswordChange), 'MMM d, yyyy') :
                            'Never'}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Expires</div>
                        <div className="font-medium">
                          {data?.passwordExpiresAt ?
                            format(new Date(data.passwordExpiresAt), 'MMM d, yyyy') :
                            'Never'}
                        </div>
                      </div>
                    </div>
                    <div className="pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.location.href = `/api/auth/signin?callbackUrl=${encodeURIComponent(window.location.href)}`}
                      >
                        <Key className="h-4 w-4 mr-2" /> Change Password
                      </Button>
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" /> Active Sessions
            </CardTitle>
            <CardDescription>
              Manage your active sessions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
                <Skeleton className="h-12 w-full" />
              </div>
            ) : data?.sessions && data.sessions.length > 0 ? (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Device</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Last Active</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {data.sessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell className="font-medium">
                          {session.isCurrent ? (
                            <div className="flex items-center gap-1">
                              <span>Current Session</span>
                              <Badge variant="outline" className="ml-2">Current</Badge>
                            </div>
                          ) : (
                            <div>{session.userAgent.split(' ')[0]}</div>
                          )}
                        </TableCell>
                        <TableCell>{session.ipAddress}</TableCell>
                        <TableCell>
                          {format(new Date(session.lastActiveAt), 'MMM d, HH:mm')}
                        </TableCell>
                        <TableCell>
                          {!session.isCurrent && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleTerminateSession(session.id)}
                              disabled={terminateSessionMutation.isPending}
                            >
                              <LogOut className="h-4 w-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No active sessions found.
              </div>
            )}

            {data?.sessions && data.sessions.length > 1 && (
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTerminateAllSessions}
                  disabled={terminateAllSessionsMutation.isPending}
                >
                  <LogOut className="h-4 w-4 mr-2" /> Sign Out All Other Sessions
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" /> Recent Login Activity
          </CardTitle>
          <CardDescription>
            Review your recent login activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AuditLogViewer
            showFilters={false}
            initialFilter={{
              userId: user?.id,
              eventType: "login_success,login_failure",
              pageSize: 5
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
