'use client';

import { useEffect } from 'react';
import { logger } from '@/lib/logger';

// Extend Window interface to include Sentry
declare global {
  interface Window {
    Sentry?: {
      init: (options: any) => void;
      BrowserTracing: new () => any;
      Replay: new (options: any) => any;
    };
  }
}

/**
 * Sentry initialization component
 * This component initializes Sentry in the browser
 */
export function SentryInit() {
  useEffect(() => {
    // Initialize Sentry
    if (typeof window !== 'undefined' && process.env.NEXT_PUBLIC_SENTRY_DSN) {
      try {
        // Load Sentry script dynamically
        const script = document.createElement('script');
        script.src = 'https://js.sentry-cdn.com/cbbe0ebd92cf811cf7964014c99a4bad.min.js';
        script.crossOrigin = 'anonymous';
        script.async = true;
        
        script.onload = () => {
          if (window.Sentry) {
            // Initialize Sentry
            window.Sentry.init({
              dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
              environment: process.env.NODE_ENV,
              release: process.env.NEXT_PUBLIC_APP_VERSION || 'development',
              tracesSampleRate: 0.2,
              integrations: [
                new window.Sentry.BrowserTracing(),
                new window.Sentry.Replay({
                  maskAllText: true,
                  blockAllMedia: true,
                }),
              ],
              replaysSessionSampleRate: 0.1,
              replaysOnErrorSampleRate: 1.0,
            });
            
            logger.info('Sentry initialized successfully');
          } else {
            logger.warn('Sentry not available in window object after script load');
          }
        };
        
        script.onerror = (error) => {
          logger.error('Failed to load Sentry script: ' + (error?.toString() || 'Unknown error'));
        };

        document.head.appendChild(script);
      } catch (error) {
        logger.error('Failed to initialize Sentry: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    }
  }, []);

  // This component doesn't render anything
  return null;
}

export default SentryInit;
