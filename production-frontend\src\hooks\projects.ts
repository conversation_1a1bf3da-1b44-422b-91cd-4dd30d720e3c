/**
 * Projects Hooks
 * React hooks for project management based on backend API structure
 */

import { useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useProjectStore, useFetchProjects, useSelectProject } from '@/stores/project-store'
import { useCreateProject as useCreateProjectStore } from '@/stores/project-store'
import { useToast } from '@/hooks/use-toast'
import { projectService } from '@/services'
import type { Project as BackendProject, ProjectStatus } from '@/types/backend'

// Helper function to calculate project progress from backend data
function calculateProjectProgress(project: BackendProject): number {
  // Calculate progress based on project status and available data
  if (project.status === 'deleted') return 100; // Deleted projects are "complete"

  // For active projects, calculate based on completion metrics if available
  const metadata = project.metadata as any;
  if (metadata?.completionPercentage) {
    return Math.min(100, Math.max(0, metadata.completionPercentage));
  }

  // Default progress for active projects
  return project.status === 'active' ? 25 : 0;
}

// Enhanced project data that includes computed fields
function enhanceProjectData(backendProject: BackendProject): BackendProject & {
  progress: number;
  isArchived: boolean;
  documentCount: number;
  memberCount: number;
} {
  return {
    ...backendProject,
    progress: calculateProjectProgress(backendProject),
    isArchived: backendProject.status === 'archived',
    documentCount: (backendProject.metadata as any)?.documentCount || 0,
    memberCount: (backendProject.metadata as any)?.memberCount || 0,
  };
}

export interface CreateProjectRequest {
  name: string
  description?: string
  organizationId: string
  visibility?: any // Allow any visibility type
  tags?: string[]
  settings?: Record<string, any>
  templateId?: string
}

export interface UpdateProjectRequest {
  name?: string
  description?: string
  status?: ProjectStatus
  startDate?: string
  endDate?: string
  tags?: string[]
  settings?: Record<string, any>
}

export interface ProjectMember {
  id: string
  userId: string
  projectId: string
  role: string
  permissions: string[]
  joinedAt: string
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    avatar?: string
  }
}

export interface ProjectAnalytics {
  totalDocuments: number
  totalMembers: number
  documentsProcessed: number
  storageUsed: number
  activityStats: {
    documentsUploaded: number
    documentsProcessed: number
    collaborationSessions: number
  }
  timelineStats: Array<{
    date: string
    documentsUploaded: number
    documentsProcessed: number
  }>
}

/**
 * Hook to get all projects with optional filters
 */
export function useProjects(params?: {
  organizationId?: string
  status?: ProjectStatus[]
  page?: number
  pageSize?: number
  search?: string
}) {
  const projects = useProjectStore(state => state.projects)
  const loading = useProjectStore(state => state.loading)
  const error = useProjectStore(state => state.error)
  const fetchProjects = useFetchProjects()
  const createProjectStore = useCreateProjectStore()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchProjects()
    } catch (error: any) {
      toast({
        title: 'Error fetching projects',
        description: error.message || 'Failed to fetch projects',
        variant: 'destructive',
      })
    }
  }, [fetchProjects, toast])

  // Filter projects based on params
  const filteredProjects = projects.filter(project => {
    if (params?.organizationId && project.organizationId !== params.organizationId) return false
    if (params?.status && !params.status.includes(project.status as ProjectStatus)) return false
    if (params?.search && !project.name.toLowerCase().includes(params.search.toLowerCase())) return false
    return true
  }).map(enhanceProjectData)

  return {
    data: { data: filteredProjects },
    projects: filteredProjects,
    isLoading: loading,
    error,
    refetch,
    createProject: async (data: any) => {
      try {
        await createProjectStore(data)
      } catch (error: any) {
        throw error
      }
    },
    isCreating: loading
  }
}

/**
 * Hook to get a specific project by ID
 */
export function useProject(projectId: string) {
  const selectedProject = useProjectStore(state => state.selectedProject)
  const loading = useProjectStore(state => state.loading)
  const error = useProjectStore(state => state.error)
  const selectProject = useSelectProject()

  const project = selectedProject?.id === projectId ? enhanceProjectData(selectedProject) : null

  const refetch = useCallback(async () => {
    if (!projectId) return
    try {
      await selectProject(projectId)
    } catch (error: any) {
      // Error handling is done in the store
    }
  }, [selectProject, projectId])

  return {
    data: project,
    project,
    isLoading: loading,
    error,
    refetch,
    enabled: !!projectId
  }
}

/**
 * Hook to get project members
 */
export function useProjectMembers(projectId: string) {
  return useQuery({
    queryKey: ['project', projectId, 'members'],
    queryFn: async () => {
      return await projectService.getProjectMembers(projectId)
    },
    enabled: !!projectId,
  })
}

/**
 * Hook to get project analytics
 */
export function useProjectAnalytics(projectId: string) {
  return useQuery({
    queryKey: ['project', projectId, 'analytics'],
    queryFn: async () => {
      return await projectService.getProjectAnalytics(projectId)
    },
    enabled: !!projectId,
  })
}

/**
 * Hook to get project activity
 */
export function useProjectActivity(projectId: string, params?: {
  page?: number
  pageSize?: number
  activityType?: string
}) {
  return useQuery({
    queryKey: ['project', projectId, 'activity', params],
    queryFn: async () => {
      return await projectService.getProjectActivity(projectId, params)
    },
    enabled: !!projectId,
  })
}

/**
 * Hook to create a new project
 */
export function useCreateProject() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateProjectRequest) => {
      return await projectService.createProject(data)
    },
    onSuccess: (project) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      if (project.organizationId) {
        queryClient.invalidateQueries({ queryKey: ['projects', { organizationId: project.organizationId }] })
      }
      toast({
        title: 'Project created',
        description: `Project "${project.name}" has been created successfully.`,
      })
    },
    onError: (error: any) => {
      console.error('Create project error:', error);
      toast({
        title: 'Error creating project',
        description: error.message || 'There was a problem creating the project. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update a project
 */
export function useUpdateProject() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ projectId, data }: { projectId: string; data: UpdateProjectRequest }) => {
      // Convert ProjectStatus enum to backend-compatible status
      const convertedData: any = { ...data };
      if (data.status) {
        // Map ProjectStatus enum values to backend status values
        const statusMap: Record<string, string> = {
          'active': 'active',
          'inactive': 'archived', // Map inactive to archived
          'archived': 'archived',
          'draft': 'active', // Map draft to active
          'completed': 'archived' // Map completed to archived
        };
        convertedData.status = statusMap[data.status] || 'active';
      }
      return await projectService.updateProject(projectId, convertedData)
    },
    onSuccess: (project) => {
      queryClient.invalidateQueries({ queryKey: ['project', project.id] })
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      if (project.organizationId) {
        queryClient.invalidateQueries({ queryKey: ['projects', { organizationId: project.organizationId }] })
      }
      toast({
        title: 'Project updated',
        description: `Project "${project.name}" has been updated successfully.`,
      })
    },
    onError: (error: any) => {
      console.error('Update project error:', error);
      toast({
        title: 'Error updating project',
        description: error.message || 'There was a problem updating the project. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a project
 */
export function useDeleteProject() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (projectId: string) => {
      await projectService.deleteProject(projectId)
      return projectId
    },
    onSuccess: (projectId) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      queryClient.removeQueries({ queryKey: ['project', projectId] })
      toast({
        title: 'Project deleted',
        description: 'The project has been deleted successfully.',
      })
    },
    onError: (error: any) => {
      console.error('Delete project error:', error);
      toast({
        title: 'Error deleting project',
        description: error.message || 'There was a problem deleting the project. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to add a member to a project
 */
export function useAddProjectMember() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      projectId, 
      memberData 
    }: { 
      projectId: string
      memberData: {
        userId: string
        role: string
        permissions?: string[]
      }
    }) => {
      return await projectService.addProjectMember(projectId, memberData)
    },
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: ['project', projectId] })
      queryClient.invalidateQueries({ queryKey: ['project', projectId, 'members'] })
      toast({
        title: 'Member added',
        description: 'The member has been added to the project successfully.',
      })
    },
    onError: (error: any) => {
      console.error('Add project member error:', error);
      toast({
        title: 'Error adding member',
        description: error.message || 'There was a problem adding the member to the project. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to remove a member from a project
 */
export function useRemoveProjectMember() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ projectId, userId }: { projectId: string; userId: string }) => {
      await projectService.removeProjectMember(projectId, userId)
      return { projectId, userId }
    },
    onSuccess: ({ projectId }) => {
      queryClient.invalidateQueries({ queryKey: ['project', projectId] })
      queryClient.invalidateQueries({ queryKey: ['project', projectId, 'members'] })
      toast({
        title: 'Member removed',
        description: 'The member has been removed from the project successfully.',
      })
    },
    onError: (error: any) => {
      console.error('Remove project member error:', error);
      toast({
        title: 'Error removing member',
        description: error.message || 'There was a problem removing the member from the project. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to search projects
 */
export function useSearchProjects(query: string, filters?: {
  organizationId?: string
  status?: ProjectStatus[]
  tags?: string[]
}) {
  return useQuery({
    queryKey: ['projects', 'search', query, filters],
    queryFn: async () => {
      return await projectService.searchProjects(query, filters)
    },
    enabled: !!query && query.length > 0,
  })
}

/**
 * Hook to export project data
 */
export function useExportProject() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ projectId, format }: { projectId: string; format: 'json' | 'excel' | 'pdf' }) => {
      return await projectService.exportProject(projectId, format)
    },
    onSuccess: (result) => {
      if (result.downloadUrl) {
        window.open(result.downloadUrl, '_blank')
      }
      toast({
        title: 'Export started',
        description: 'Your project export is being prepared for download.',
      })
    },
    onError: (error: any) => {
      console.error('Export project error:', error);
      toast({
        title: 'Error exporting project',
        description: error.message || 'There was a problem exporting the project. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
