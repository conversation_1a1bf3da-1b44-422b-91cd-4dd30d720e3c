/**
 * Dashboard Store Hook
 * Provides access to dashboard store functionality
 */

import { useDashboardStore as useDashboardStoreBase } from '../stores/dashboard-store'

// Re-export the main store hook
export const useDashboardStore = useDashboardStoreBase

// Selector hooks for specific dashboard data
export const useDashboard = () => {
  const { 
    widgets, 
    layout, 
    preferences, 
    loading, 
    error,
    lastUpdated 
  } = useDashboardStore()
  
  return {
    widgets,
    layout,
    preferences,
    loading,
    error,
    lastUpdated,
  }
}

export const useDashboardWidgets = () => useDashboardStore((state) => state.widgets)
export const useDashboardLayout = () => useDashboardStore((state) => state.layout)
export const useDashboardPreferences = () => useDashboardStore((state) => state.preferences)
export const useDashboardLoading = () => useDashboardStore((state) => state.loading)
export const useDashboardError = () => useDashboardStore((state) => state.error)

// Action hooks
export const useAddWidget = () => useDashboardStore((state) => state.addWidget)
export const useRemoveWidget = () => useDashboardStore((state) => state.removeWidget)
export const useUpdateWidget = () => useDashboardStore((state) => state.updateWidget)
export const useUpdateLayout = () => useDashboardStore((state) => state.updateLayout)
export const useUpdatePreferences = () => useDashboardStore((state) => state.updatePreferences)
export const useRefreshDashboard = () => useDashboardStore((state) => state.refresh)
