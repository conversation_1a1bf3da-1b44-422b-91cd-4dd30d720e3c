/**
 * Document Processing Center
 * Advanced document processing interface with AI capabilities
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, 
  FileText, 
  Table, 
  Key, 
  Eye, 
  Languages, 
  BarChart3,
  Heart,
  Zap,
  Settings,
  Play,
  Pause,
  Download,
  RefreshCw
} from 'lucide-react';
import { useBatchProcessDocuments, useDocumentProcessingJobs } from '@/hooks/document-processing/useDocumentProcessing';

// Document Processing Options interface
interface DocumentProcessingOptions {
  extractTables: boolean;
  extractKeyValuePairs: boolean;
  extractEntities: boolean;
  performOCR: boolean;
  analyzeLayout: boolean;
  detectLanguage: boolean;
  generateSummary: boolean;
  analyzeSentiment: boolean;
}

interface DocumentProcessingCenterProps {
  documentIds: string[];
  projectId: string;
  organizationId: string;
  onProcessingComplete?: (results: any[]) => void;
}

export function DocumentProcessingCenter({ 
  documentIds, 
  projectId, 
  organizationId,
  onProcessingComplete 
}: DocumentProcessingCenterProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>(documentIds);
  const [processingOptions, setProcessingOptions] = useState<DocumentProcessingOptions>({
    extractTables: true,
    extractKeyValuePairs: true,
    extractEntities: false,
    performOCR: true,
    analyzeLayout: true,
    detectLanguage: false,
    generateSummary: false,
    analyzeSentiment: false,
  });
  const [customModelId, setCustomModelId] = useState<string>('');
  const [activeTab, setActiveTab] = useState('configure');

  // Use the available hooks
  const { mutate: processDocuments, isPending: isProcessing } = useBatchProcessDocuments();
  const { data: jobs = [] } = useDocumentProcessingJobs({ organizationId });

  // Get the latest processing job for this batch
  const processingJob = jobs.find(job =>
    selectedDocuments.includes(job.documentId) &&
    ['PENDING', 'PROCESSING'].includes(job.status)
  );

  // Get processing results from completed jobs
  const results = React.useMemo(() => {
    return jobs
      .filter(job => job.status === 'COMPLETED' && selectedDocuments.includes(job.documentId))
      .map(job => ({
        documentId: job.documentId,
        confidence: job.results?.classification?.confidence || 0,
        fields: job.results?.keyValuePairs || [],
        tables: job.results?.tables || [],
        entities: job.results?.entities || [],
        processingTime: 0, // Not available in results
        summary: '', // Not available in results
        extractedText: job.results?.extractedText || '',
        keyValuePairs: job.results?.keyValuePairs || [],
        sentiment: job.results?.sentiment || null,
        language: 'unknown' // Not available in results
      }))
  }, [jobs, selectedDocuments]);

  // Get any processing errors
  const error = React.useMemo(() => {
    const failedJob = jobs.find(job =>
      job.status === 'FAILED' && selectedDocuments.includes(job.documentId)
    );
    return failedJob?.error || null;
  }, [jobs, selectedDocuments]);

  const handleStartProcessing = async () => {
    try {
      processDocuments({
        documentIds: selectedDocuments,
        processingType: 'ANALYSIS',
        options: {
          extractTables: processingOptions.extractTables,
          extractKeyValuePairs: processingOptions.extractKeyValuePairs,
          extractEntities: processingOptions.extractEntities,
          extractText: processingOptions.performOCR,
          analyzeSentiment: processingOptions.analyzeSentiment,
          customOptions: customModelId ? { modelId: customModelId } : undefined
        },
        organizationId,
        projectId
      });
      setActiveTab('monitor');
    } catch (error) {
      console.error('Failed to start processing:', error);
    }
  };

  const handleOptionChange = (option: keyof DocumentProcessingOptions, value: boolean) => {
    setProcessingOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  const getProcessingIcon = (option: keyof DocumentProcessingOptions) => {
    switch (option) {
      case 'extractTables':
        return <Table className="h-4 w-4" />;
      case 'extractKeyValuePairs':
        return <Key className="h-4 w-4" />;
      case 'extractEntities':
        return <Eye className="h-4 w-4" />;
      case 'performOCR':
        return <FileText className="h-4 w-4" />;
      case 'analyzeLayout':
        return <BarChart3 className="h-4 w-4" />;
      case 'detectLanguage':
        return <Languages className="h-4 w-4" />;
      case 'generateSummary':
        return <Brain className="h-4 w-4" />;
      case 'analyzeSentiment':
        return <Heart className="h-4 w-4" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  const getOptionDescription = (option: keyof DocumentProcessingOptions) => {
    switch (option) {
      case 'extractTables':
        return 'Extract structured table data with preserved formatting';
      case 'extractKeyValuePairs':
        return 'Identify and extract key-value pairs from forms';
      case 'extractEntities':
        return 'Detect named entities like names, dates, and locations';
      case 'performOCR':
        return 'Extract text from images and scanned documents';
      case 'analyzeLayout':
        return 'Analyze document structure and layout elements';
      case 'detectLanguage':
        return 'Automatically detect document language';
      case 'generateSummary':
        return 'Generate AI-powered document summaries';
      case 'analyzeSentiment':
        return 'Analyze emotional tone and sentiment';
      default:
        return '';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Processing Center</h2>
          <p className="text-muted-foreground">
            Process {selectedDocuments.length} documents with AI-powered analysis
          </p>
        </div>
        <Badge variant="outline" className="flex items-center space-x-1">
          <Brain className="h-3 w-3" />
          <span>AI Powered</span>
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="configure">Configure</TabsTrigger>
          <TabsTrigger value="monitor">Monitor</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="configure" className="space-y-6">
          {/* Processing Options */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Processing Options</span>
              </CardTitle>
              <CardDescription>
                Select the AI analysis features to apply to your documents
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(processingOptions).map(([option, enabled]) => (
                  <div key={option} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <Checkbox
                      id={option}
                      checked={enabled}
                      onCheckedChange={(checked) => 
                        handleOptionChange(option as keyof DocumentProcessingOptions, !!checked)
                      }
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        {getProcessingIcon(option as keyof DocumentProcessingOptions)}
                        <Label htmlFor={option} className="font-medium capitalize">
                          {option.replace(/([A-Z])/g, ' $1').trim()}
                        </Label>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {getOptionDescription(option as keyof DocumentProcessingOptions)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Custom Model */}
              <div className="space-y-2">
                <Label htmlFor="customModel">Custom Model (Optional)</Label>
                <Select value={customModelId} onValueChange={setCustomModelId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a custom model or leave blank for default" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Default Model</SelectItem>
                    <SelectItem value="invoice-model-v2">Invoice Processing Model v2</SelectItem>
                    <SelectItem value="contract-model-v1">Contract Analysis Model v1</SelectItem>
                    <SelectItem value="receipt-model-v3">Receipt Processing Model v3</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Start Processing Button */}
              <div className="flex justify-end pt-4">
                <Button 
                  onClick={handleStartProcessing}
                  disabled={isProcessing || selectedDocuments.length === 0}
                  className="flex items-center space-x-2"
                >
                  <Play className="h-4 w-4" />
                  <span>Start Processing</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitor" className="space-y-6">
          {processingJob ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <RefreshCw className={`h-5 w-5 ${isProcessing ? 'animate-spin' : ''}`} />
                  <span>Processing Status</span>
                </CardTitle>
                <CardDescription>
                  Monitor the progress of your document processing job
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Job ID: {processingJob.id}</p>
                    <p className="text-sm text-muted-foreground">
                      Status: <Badge>{processingJob.status}</Badge>
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">{processingJob.progress.percentage}%</p>
                    <p className="text-sm text-muted-foreground">Complete</p>
                  </div>
                </div>

                <Progress value={processingJob.progress.percentage} className="h-2" />

                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {processingJob.progress.completedSteps}
                    </p>
                    <p className="text-sm text-muted-foreground">Processed</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-red-600">
                      0
                    </p>
                    <p className="text-sm text-muted-foreground">Failed</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">
                      {processingJob.progress.totalSteps}
                    </p>
                    <p className="text-sm text-muted-foreground">Total</p>
                  </div>
                </div>

                {processingJob.error && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-red-600">Error</h4>
                    <div className="text-sm p-2 bg-red-50 border border-red-200 rounded">
                      <p className="text-red-600">{processingJob.error}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No processing job active</p>
                <Button 
                  variant="outline" 
                  onClick={() => setActiveTab('configure')}
                  className="mt-4"
                >
                  Configure Processing
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="results" className="space-y-6">
          {results.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Processing Results</span>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export Results
                  </Button>
                </CardTitle>
                <CardDescription>
                  Review the AI analysis results for your documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {results.map((result: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Document {result.documentId}</h4>
                        <Badge variant="outline">
                          Confidence: {Math.round((result.confidence || 0) * 100)}%
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="font-medium">Fields Extracted</p>
                          <p className="text-muted-foreground">{result.fields?.length || 0}</p>
                        </div>
                        <div>
                          <p className="font-medium">Tables Found</p>
                          <p className="text-muted-foreground">{result.tables?.length || 0}</p>
                        </div>
                        <div>
                          <p className="font-medium">Entities</p>
                          <p className="text-muted-foreground">{result.entities?.length || 0}</p>
                        </div>
                        <div>
                          <p className="font-medium">Processing Time</p>
                          <p className="text-muted-foreground">{result.processingTime || 0}ms</p>
                        </div>
                      </div>

                      {result.summary && (
                        <div className="mt-3">
                          <p className="font-medium text-sm">Summary</p>
                          <p className="text-sm text-muted-foreground">{result.summary}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No results available</p>
                <p className="text-sm text-muted-foreground mt-2">
                  Start processing documents to see results here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
