/**
 * useRoles Hook
 * Manages role operations and state
 */

import { useState, useEffect, useCallback } from 'react'
import { useMutation } from '@tanstack/react-query'
import { useApi } from '../index'
import type { 
  Role, 
  CreateRoleRequest, 
  UpdateRoleRequest,
  RoleSearchQuery,
  ID 
} from '../../types'

export interface UseRolesOptions {
  organizationId?: ID
  projectId?: ID
  scope?: string
  autoFetch?: boolean
}

export interface UseRolesResult {
  roles: Role[]
  loading: boolean
  error: string | null
  selectedRole: Role | null

  // Additional properties expected by components
  isLoadingRoles: boolean
  isDeletingRole: boolean

  // CRUD operations
  createRole: (request: CreateRoleRequest) => Promise<Role>
  updateRole: (id: ID, request: UpdateRoleRequest) => Promise<Role>
  deleteRole: (id: ID) => Promise<void>
  selectRole: (roleId: ID) => Promise<void>

  // Utility functions
  refresh: () => Promise<void>
  getRoleById: (id: ID) => Role | null
  getRolesByScope: (scope: string) => Role[]
  getSystemRoles: () => Role[]
  getCustomRoles: () => Role[]
}

export function useRoles(options: UseRolesOptions = {}): UseRolesResult {
  const { organizationId, projectId, scope, autoFetch = true } = options
  
  const [roles, setRoles] = useState<Role[]>([])
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)

  // Build query parameters
  const queryParams = {
    ...(organizationId && { organizationId }),
    ...(projectId && { projectId }),
    ...(scope && { scope }),
  }
  
  const queryString = new URLSearchParams(queryParams).toString()
  const endpoint = `/management/roles${queryString ? `?${queryString}` : ''}`
  
  // Fetch roles
  const { data, isLoading: loading, error, refetch } = useApi<Role[]>(
    endpoint,
    { immediate: autoFetch }
  )
  
  // Update local state when data changes
  useEffect(() => {
    if (data) {
      setRoles(data)
    }
  }, [data])
  
  // Create role mutation
  const createMutation = useMutation({
    mutationFn: (request: CreateRoleRequest) => fetch('/management/advanced-roles', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => res.json())
  })
  
  // Update role mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, request }: { id: ID; request: UpdateRoleRequest }) => fetch(`/management/roles/${id}/update`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => res.json())
  })

  // Delete role mutation
  const deleteMutation = useMutation({
    mutationFn: (id: ID) => fetch(`/management/roles/${id}/delete`, {
      method: 'DELETE',
    }).then(res => {
      if (!res.ok) throw new Error('Failed to delete role')
    })
  })
  
  // Action functions
  const createRole = useCallback(async (request: CreateRoleRequest) => {
    return createMutation.mutateAsync(request)
  }, [createMutation])
  
  const updateRole = useCallback(async (id: ID, request: UpdateRoleRequest) => {
    return updateMutation.mutateAsync({ id, request })
  }, [updateMutation])
  
  const deleteRole = useCallback(async (id: ID) => {
    return deleteMutation.mutateAsync(id)
  }, [deleteMutation])

  const selectRole = useCallback(async (roleId: ID) => {
    const role = roles.find(r => r.id === roleId)
    if (role) {
      setSelectedRole(role)
    } else {
      // Fetch role if not in current list
      try {
        const response = await fetch(`/management/roles/${roleId}/details`)
        if (response.ok) {
          const result = await response.json()
          setSelectedRole(result.role)
        }
      } catch (error) {
        console.error('Failed to fetch role:', error)
      }
    }
  }, [roles])
  
  const refresh = useCallback(async () => {
    await refetch()
  }, [refetch])
  
  // Utility functions
  const getRoleById = useCallback((id: ID) => {
    return roles.find(role => role.id === id) || null
  }, [roles])
  
  const getRolesByScope = useCallback((scope: string) => {
    return roles.filter(role => role.scope === scope)
  }, [roles])
  
  const getSystemRoles = useCallback(() => {
    return roles.filter(role => role.isSystemRole)
  }, [roles])
  
  const getCustomRoles = useCallback(() => {
    return roles.filter(role => !role.isSystemRole)
  }, [roles])
  
  const isLoading = loading || createMutation.isPending || updateMutation.isPending || deleteMutation.isPending
  const isDeletingRole = deleteMutation.isPending

  return {
    roles,
    loading: isLoading,
    error: error || (createMutation.error as Error)?.message || (updateMutation.error as Error)?.message || (deleteMutation.error as Error)?.message || null,
    selectedRole,

    // Additional properties expected by components
    isLoadingRoles: isLoading,
    isDeletingRole,

    createRole,
    updateRole,
    deleteRole,
    selectRole,

    refresh,
    getRoleById,
    getRolesByScope,
    getSystemRoles,
    getCustomRoles,
  }
}
