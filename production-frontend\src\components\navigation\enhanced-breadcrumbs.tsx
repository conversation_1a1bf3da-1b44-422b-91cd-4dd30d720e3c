'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { ChevronRight, Home, Building2, FolderKanban, FileText, Users, Setting<PERSON>, Brain } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ReactNode
  isActive?: boolean
}

interface EnhancedBreadcrumbsProps {
  items?: BreadcrumbItem[]
  className?: string
}

export function EnhancedBreadcrumbs({ items, className }: EnhancedBreadcrumbsProps) {
  const pathname = usePathname()

  // Auto-generate breadcrumbs from pathname if items not provided
  const breadcrumbItems = items || generateBreadcrumbsFromPath(pathname)

  return (
    <nav className={cn("flex items-center space-x-1 text-sm text-muted-foreground", className)}>
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
          )}
          
          {item.href && !item.isActive ? (
            <Link
              href={item.href}
              className="flex items-center gap-1 hover:text-foreground transition-colors"
            >
              {item.icon}
              <span>{item.label}</span>
            </Link>
          ) : (
            <span className={cn(
              "flex items-center gap-1",
              item.isActive && "text-foreground font-medium"
            )}>
              {item.icon}
              <span>{item.label}</span>
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}

function generateBreadcrumbsFromPath(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean)
  const breadcrumbs: BreadcrumbItem[] = []

  // Always start with home
  breadcrumbs.push({
    label: 'Dashboard',
    href: '/dashboard',
    icon: <Home className="h-4 w-4" />
  })

  // Build breadcrumbs based on path segments
  let currentPath = ''
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const isLast = index === segments.length - 1
    
    // Skip the first segment if it's 'dashboard' since we already added it
    if (segment === 'dashboard' && index === 0) return

    const breadcrumb = getBreadcrumbForSegment(segment, currentPath, isLast)
    if (breadcrumb) {
      breadcrumbs.push(breadcrumb)
    }
  })

  return breadcrumbs
}

function getBreadcrumbForSegment(segment: string, path: string, isLast: boolean): BreadcrumbItem | null {
  // Handle dynamic segments (IDs)
  if (isUUID(segment)) {
    // For UUIDs, we'll show a shortened version
    return {
      label: `${segment.slice(0, 8)}...`,
      href: isLast ? undefined : path,
      isActive: isLast
    }
  }

  // Handle known route segments
  const routeMap: Record<string, { label: string; icon?: React.ReactNode }> = {
    'organizations': { label: 'Organizations', icon: <Building2 className="h-4 w-4" /> },
    'projects': { label: 'Projects', icon: <FolderKanban className="h-4 w-4" /> },
    'documents': { label: 'Documents', icon: <FileText className="h-4 w-4" /> },
    'members': { label: 'Members', icon: <Users className="h-4 w-4" /> },
    'settings': { label: 'Settings', icon: <Settings className="h-4 w-4" /> },
    'ai': { label: 'AI Operations', icon: <Brain className="h-4 w-4" /> },
    'workflows': { label: 'Workflows', icon: <FolderKanban className="h-4 w-4" /> },
    'templates': { label: 'Templates', icon: <FileText className="h-4 w-4" /> },
    'analytics': { label: 'Analytics', icon: <FileText className="h-4 w-4" /> },
    'collaboration': { label: 'Collaboration', icon: <Users className="h-4 w-4" /> },
    'create': { label: 'Create' },
    'edit': { label: 'Edit' },
    'upload': { label: 'Upload' },
    'share': { label: 'Share' },
    'invite': { label: 'Invite' }
  }

  const route = routeMap[segment]
  if (route) {
    return {
      label: route.label,
      icon: route.icon,
      href: isLast ? undefined : path,
      isActive: isLast
    }
  }

  // For unknown segments, capitalize and use as-is
  return {
    label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
    href: isLast ? undefined : path,
    isActive: isLast
  }
}

function isUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(str)
}

// Context-aware breadcrumb component that can be used with specific data
interface ContextualBreadcrumbsProps {
  organization?: { id: string; name: string }
  project?: { id: string; name: string }
  document?: { id: string; name: string }
  currentPage?: string
  className?: string
}

export function ContextualBreadcrumbs({
  organization,
  project,
  document,
  currentPage,
  className
}: ContextualBreadcrumbsProps) {
  const breadcrumbs: BreadcrumbItem[] = []

  // Always start with dashboard
  breadcrumbs.push({
    label: 'Dashboard',
    href: '/dashboard',
    icon: <Home className="h-4 w-4" />
  })

  // Add organization if provided
  if (organization) {
    breadcrumbs.push({
      label: organization.name,
      href: `/organizations/${organization.id}`,
      icon: <Building2 className="h-4 w-4" />
    })
  }

  // Add project if provided
  if (project) {
    breadcrumbs.push({
      label: project.name,
      href: organization 
        ? `/organizations/${organization.id}/projects/${project.id}`
        : `/projects/${project.id}`,
      icon: <FolderKanban className="h-4 w-4" />
    })
  }

  // Add document if provided
  if (document) {
    breadcrumbs.push({
      label: document.name,
      href: project 
        ? `/projects/${project.id}/documents/${document.id}`
        : `/documents/${document.id}`,
      icon: <FileText className="h-4 w-4" />
    })
  }

  // Add current page if provided
  if (currentPage) {
    breadcrumbs.push({
      label: currentPage,
      isActive: true
    })
  }

  return <EnhancedBreadcrumbs items={breadcrumbs} className={className} />
}

// Quick navigation component for common actions
interface QuickNavProps {
  organization?: { id: string; name: string }
  project?: { id: string; name: string }
  className?: string
}

export function QuickNav({ organization, project, className }: QuickNavProps) {
  const quickLinks = []

  if (organization) {
    quickLinks.push(
      { label: 'Projects', href: `/organizations/${organization.id}/projects`, icon: <FolderKanban className="h-4 w-4" /> },
      { label: 'Members', href: `/organizations/${organization.id}/members`, icon: <Users className="h-4 w-4" /> },
      { label: 'Settings', href: `/organizations/${organization.id}/settings`, icon: <Settings className="h-4 w-4" /> }
    )
  }

  if (project) {
    quickLinks.push(
      { label: 'Documents', href: `/projects/${project.id}/documents`, icon: <FileText className="h-4 w-4" /> },
      { label: 'Workflows', href: `/projects/${project.id}/workflows`, icon: <FolderKanban className="h-4 w-4" /> },
      { label: 'Analytics', href: `/projects/${project.id}/analytics`, icon: <FileText className="h-4 w-4" /> }
    )
  }

  if (quickLinks.length === 0) return null

  return (
    <nav className={cn("flex items-center gap-4", className)}>
      {quickLinks.map((link, index) => (
        <Link
          key={index}
          href={link.href}
          className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          {link.icon}
          <span>{link.label}</span>
        </Link>
      ))}
    </nav>
  )
}
