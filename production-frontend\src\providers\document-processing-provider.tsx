/**
 * Document Processing Provider - Lazy-loaded Document Processing Services
 * This provider should only be used by components that require document processing
 * It prevents unnecessary service initialization on pages that don't process documents
 */

"use client"

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { useDocumentProcessingServices } from '@/hooks/useLazyServices'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'

interface ProcessingJob {
  id: string
  documentId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  startedAt: string
  completedAt?: string
  error?: string
  results?: any
}

interface DocumentProcessingContextType {
  documentProcessor: any | null
  ocrService: any | null
  isLoading: boolean
  hasAccess: boolean
  isAvailable: boolean
  error: Error | null
  
  // Processing Operations
  processDocument: (file: File, options?: any) => Promise<ProcessingJob>
  getProcessingStatus: (jobId: string) => Promise<ProcessingJob>
  cancelProcessing: (jobId: string) => Promise<void>
  
  // OCR Operations
  extractText: (file: File) => Promise<string>
  extractTextFromImage: (imageUrl: string) => Promise<string>
  
  // Document Analysis
  analyzeLayout: (file: File) => Promise<any>
  extractTables: (file: File) => Promise<any[]>
  extractKeyValuePairs: (file: File) => Promise<any[]>
  
  // Job Management
  activeJobs: ProcessingJob[]
  completedJobs: ProcessingJob[]
}

const DocumentProcessingContext = createContext<DocumentProcessingContextType | null>(null)

interface DocumentProcessingProviderProps {
  children: ReactNode
  autoLoad?: boolean
}

export function DocumentProcessingProvider({ children, autoLoad = false }: DocumentProcessingProviderProps) {
  const { user } = useAuth()
  const [error, setError] = useState<Error | null>(null)
  const [activeJobs, setActiveJobs] = useState<ProcessingJob[]>([])
  const [completedJobs, setCompletedJobs] = useState<ProcessingJob[]>([])
  
  // Only load document processing services when explicitly enabled
  const {
    documentProcessor,
    ocrService,
    isLoading,
    hasAccess,
    isAvailable
  } = useDocumentProcessingServices(autoLoad)

  // Process document
  const processDocument = useCallback(async (file: File, options?: any): Promise<ProcessingJob> => {
    if (!documentProcessor) {
      throw new Error('Document processor service not available')
    }

    try {
      setError(null)
      const job = await documentProcessor.processDocument(file, options)
      
      setActiveJobs(prev => [...prev, job])
      toast.success('Document processing started')
      
      return job
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Failed to start document processing')
      throw error
    }
  }, [documentProcessor])

  // Get processing status
  const getProcessingStatus = useCallback(async (jobId: string): Promise<ProcessingJob> => {
    if (!documentProcessor) {
      throw new Error('Document processor service not available')
    }

    try {
      setError(null)
      const job = await documentProcessor.getProcessingStatus(jobId)
      
      // Update job status in state
      setActiveJobs(prev => prev.map(j => j.id === jobId ? job : j))
      
      // Move completed jobs
      if (job.status === 'completed' || job.status === 'failed') {
        setActiveJobs(prev => prev.filter(j => j.id !== jobId))
        setCompletedJobs(prev => {
          const existing = prev.find(j => j.id === jobId)
          if (existing) {
            return prev.map(j => j.id === jobId ? job : j)
          }
          return [...prev, job]
        })
      }
      
      return job
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      throw error
    }
  }, [documentProcessor])

  // Cancel processing
  const cancelProcessing = useCallback(async (jobId: string): Promise<void> => {
    if (!documentProcessor) {
      throw new Error('Document processor service not available')
    }

    try {
      setError(null)
      await documentProcessor.cancelProcessing(jobId)
      
      setActiveJobs(prev => prev.filter(j => j.id !== jobId))
      toast.success('Processing cancelled')
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Failed to cancel processing')
      throw error
    }
  }, [documentProcessor])

  // Extract text using OCR
  const extractText = useCallback(async (file: File): Promise<string> => {
    if (!ocrService) {
      throw new Error('OCR service not available')
    }

    try {
      setError(null)
      const text = await ocrService.extractText(file)
      toast.success('Text extraction completed')
      return text
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Text extraction failed')
      throw error
    }
  }, [ocrService])

  // Extract text from image URL
  const extractTextFromImage = useCallback(async (imageUrl: string): Promise<string> => {
    if (!ocrService) {
      throw new Error('OCR service not available')
    }

    try {
      setError(null)
      const text = await ocrService.extractTextFromImage(imageUrl)
      return text
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Image text extraction failed')
      throw error
    }
  }, [ocrService])

  // Analyze document layout
  const analyzeLayout = useCallback(async (file: File) => {
    if (!documentProcessor) {
      throw new Error('Document processor service not available')
    }

    try {
      setError(null)
      const layout = await documentProcessor.analyzeLayout(file)
      return layout
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Layout analysis failed')
      throw error
    }
  }, [documentProcessor])

  // Extract tables
  const extractTables = useCallback(async (file: File): Promise<any[]> => {
    if (!documentProcessor) {
      throw new Error('Document processor service not available')
    }

    try {
      setError(null)
      const tables = await documentProcessor.extractTables(file)
      return tables
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Table extraction failed')
      throw error
    }
  }, [documentProcessor])

  // Extract key-value pairs
  const extractKeyValuePairs = useCallback(async (file: File): Promise<any[]> => {
    if (!documentProcessor) {
      throw new Error('Document processor service not available')
    }

    try {
      setError(null)
      const pairs = await documentProcessor.extractKeyValuePairs(file)
      return pairs
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      toast.error('Key-value extraction failed')
      throw error
    }
  }, [documentProcessor])

  // Poll active jobs for status updates
  useEffect(() => {
    if (activeJobs.length === 0 || !documentProcessor) return

    const interval = setInterval(async () => {
      for (const job of activeJobs) {
        if (job.status === 'processing' || job.status === 'pending') {
          try {
            await getProcessingStatus(job.id)
          } catch (error) {
            console.error('Failed to update job status:', error)
          }
        }
      }
    }, 5000) // Poll every 5 seconds

    return () => clearInterval(interval)
  }, [activeJobs, documentProcessor, getProcessingStatus])

  // Show access denied message for unauthorized users
  useEffect(() => {
    if (autoLoad && !hasAccess && user) {
      toast.error('Document processing features are not available for your account')
    }
  }, [autoLoad, hasAccess, user])

  const value: DocumentProcessingContextType = {
    documentProcessor,
    ocrService,
    isLoading,
    hasAccess,
    isAvailable,
    error,
    
    // Processing Operations
    processDocument,
    getProcessingStatus,
    cancelProcessing,
    
    // OCR Operations
    extractText,
    extractTextFromImage,
    
    // Document Analysis
    analyzeLayout,
    extractTables,
    extractKeyValuePairs,
    
    // Job Management
    activeJobs,
    completedJobs,
  }

  return (
    <DocumentProcessingContext.Provider value={value}>
      {children}
    </DocumentProcessingContext.Provider>
  )
}

// Hook to use document processing context
export function useDocumentProcessing() {
  const context = useContext(DocumentProcessingContext)
  if (!context) {
    throw new Error('useDocumentProcessing must be used within a DocumentProcessingProvider')
  }
  return context
}

// Hook for conditional document processing
export function useConditionalDocumentProcessing(enabled: boolean) {
  const { user } = useAuth()
  const [shouldLoad, setShouldLoad] = useState(false)

  useEffect(() => {
    setShouldLoad(enabled && !!user)
  }, [enabled, user])

  return useDocumentProcessingServices(shouldLoad)
}

// Component wrapper for document processing features
interface DocumentProcessingWrapperProps {
  children: ReactNode
  fallback?: ReactNode
}

export function DocumentProcessingWrapper({ children, fallback }: DocumentProcessingWrapperProps) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? userRoles.some(role => ['user', 'admin', 'editor'].includes(role)) : false

  if (!hasAccess) {
    return fallback ? <>{fallback}</> : null
  }

  return (
    <DocumentProcessingProvider autoLoad={true}>
      {children}
    </DocumentProcessingProvider>
  )
}
