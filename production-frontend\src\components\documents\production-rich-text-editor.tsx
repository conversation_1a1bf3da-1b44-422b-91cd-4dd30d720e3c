"use client";

import { useState, useEffect, useRef } from "react";
import { Editor } from "@tinymce/tinymce-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
// Removed deprecated useSignalR import - use CollaborationProvider instead
// SignalR imports removed - use CollaborationProvider for collaborative features
// Define local constants for removed SignalR enums
const HubMethod = {
  JOIN_DOCUMENT_GROUP: 'JoinDocumentGroup',
  LEAVE_DOCUMENT_GROUP: 'LeaveDocumentGroup',
  SEND_CURSOR_POSITION: 'SendCursorPosition'
} as const

const HubEvent = {
  USER_JOINED_DOCUMENT: 'UserJoinedDocument',
  USER_LEFT_DOCUMENT: 'UserLeftDocument',
  DOCUMENT_CHANGED: 'DocumentChanged'
} as const
import { useToast } from "@/components/ui/use-toast";
import { useEventSubscription } from "@/hooks/infrastructure";
import { EventType } from "@/services/event-grid-service";
import { Save, Users, Clock, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { debounce } from "lodash";
import { documentService } from "@/services/optimized-document-service";

interface User {
  id: string;
  name: string;
  avatarUrl?: string;
  isActive: boolean;
  lastActive: string;
  cursorPosition?: {
    elementPath: string;
    offset: number;
  };
  selection?: {
    start: {
      elementPath: string;
      offset: number;
    };
    end: {
      elementPath: string;
      offset: number;
    };
  };
}

interface DocumentEdit {
  userId: string;
  timestamp: string;
  operations: Array<{
    type: "insert" | "delete" | "format" | "replace";
    path: string;
    offset?: number;
    value?: string;
    attributes?: Record<string, any>;
    length?: number;
  }>;
}

interface RichTextEditorProps {
  documentId: string;
  projectId: string;
  organizationId: string;
  documentName: string;
  initialContent: string;
  currentUser: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  readOnly?: boolean;
  autoSaveInterval?: number; // in milliseconds
  onSaved?: () => void;
}

export function ProductionRichTextEditor({
  documentId,
  projectId,
  organizationId,
  documentName,
  initialContent,
  currentUser,
  readOnly = false,
  autoSaveInterval = 30000, // Default to 30 seconds
  onSaved
}: RichTextEditorProps) {
  // Deprecated SignalR functionality - use CollaborationProvider instead
  const invoke = async (...args: any[]) => console.log('SignalR invoke deprecated:', args)
  const isConnected = false
  const subscribe = (...args: any[]) => console.log('SignalR subscribe deprecated:', args)
  const unsubscribe = (...args: any[]) => console.log('SignalR unsubscribe deprecated:', args)
  const { toast } = useToast();
  const [content, setContent] = useState(initialContent);
  const [activeUsers, setActiveUsers] = useState<User[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const editorRef = useRef<any>(null); // Using any for TinyMCE editor reference
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const contentRef = useRef<string>(initialContent);

  // Join document editing group on mount
  useEffect(() => {
    if (isConnected) {
      // Join document group
      invoke(HubMethod.JOIN_DOCUMENT_GROUP, documentId, {
        userId: currentUser.id,
        userName: currentUser.name,
        avatarUrl: currentUser.avatarUrl
      }).catch((error: any) => {
        console.error("Failed to join document group:", error);
      });

      // Define handlers
      const userPresenceHandler = (user: User) => {
        if (user.id !== currentUser.id) {
          setActiveUsers((prev) => {
            const existingUserIndex = prev.findIndex((u) => u.id === user.id);
            if (existingUserIndex >= 0) {
              const updatedUsers = [...prev];
              updatedUsers[existingUserIndex] = {
                ...updatedUsers[existingUserIndex],
                isActive: true,
                lastActive: new Date().toISOString()
              };
              return updatedUsers;
            } else {
              return [...prev, { ...user, isActive: true, lastActive: new Date().toISOString() }];
            }
          });

          toast({
            title: "User joined",
            description: `${user.name} is now editing this document`,
            duration: 3000
          });
        }
      };

      // Subscribe to user presence
      subscribe(HubEvent.USER_JOINED_DOCUMENT, userPresenceHandler);

      const userLeftHandler = (userId: string, userName: string) => {
        if (userId !== currentUser.id) {
          setActiveUsers((prev) => {
            const existingUserIndex = prev.findIndex((u) => u.id === userId);
            if (existingUserIndex >= 0) {
              const updatedUsers = [...prev];
              updatedUsers[existingUserIndex] = {
                ...updatedUsers[existingUserIndex],
                isActive: false,
                lastActive: new Date().toISOString()
              };
              return updatedUsers;
            }
            return prev;
          });

          toast({
            title: "User left",
            description: `${userName} is no longer editing this document`,
            duration: 3000
          });
        }
      };

      // Subscribe to user left
      subscribe(HubEvent.USER_LEFT_DOCUMENT, userLeftHandler);

      const documentEditHandler = (edit: DocumentEdit) => {
        if (edit.userId === currentUser.id) return;

        if (editorRef.current) {
          const editor = editorRef.current;

          // Apply operations to the editor
          edit.operations.forEach(op => {
            try {
              if (op.type === "insert" && op.value && op.path && op.offset !== undefined) {
                const element = editor.dom.select(op.path)[0];
                if (element) {
                  editor.selection.setCursorLocation(element, op.offset);
                  editor.insertContent(op.value);
                }
              } else if (op.type === "delete" && op.path && op.offset !== undefined && op.length) {
                const element = editor.dom.select(op.path)[0];
                if (element) {
                  const range = editor.dom.createRng();
                  range.setStart(element, op.offset);
                  range.setEnd(element, op.offset + op.length);
                  editor.selection.setRng(range);
                  editor.selection.setContent('');
                }
              } else if (op.type === "format" && op.path && op.attributes) {
                const element = editor.dom.select(op.path)[0];
                if (element) {
                  Object.entries(op.attributes).forEach(([attr, value]) => {
                    editor.dom.setAttrib(element, attr, value);
                  });
                }
              } else if (op.type === "replace" && op.path && op.value) {
                const element = editor.dom.select(op.path)[0];
                if (element) {
                  editor.dom.setHTML(element, op.value);
                }
              }
            } catch (error) {
              console.error("Error applying edit operation:", error, op);
            }
          });

          // Update content state
          setContent(editor.getContent());
          contentRef.current = editor.getContent();
        }
      };

      // Subscribe to document edits
      subscribe(HubEvent.DOCUMENT_CHANGED, documentEditHandler);

      // Clean up on unmount
      return () => {
        // Leave document group
        invoke(HubMethod.LEAVE_DOCUMENT_GROUP, documentId, currentUser.id).catch((error: any) => {
          console.error("Failed to leave document group:", error);
        });

        // Unsubscribe from events
        unsubscribe(HubEvent.USER_JOINED_DOCUMENT, userPresenceHandler);
        unsubscribe(HubEvent.USER_LEFT_DOCUMENT, userLeftHandler);
        unsubscribe(HubEvent.DOCUMENT_CHANGED, documentEditHandler);
      };
    }
  }, [isConnected, documentId, currentUser, invoke, subscribe, unsubscribe, toast]);

  // Auto-save functionality
  useEffect(() => {
    if (readOnly) return;

    const autoSaveTimer = setInterval(() => {
      if (hasUnsavedChanges && !isSaving) {
        handleSave();
      }
    }, autoSaveInterval);

    return () => clearInterval(autoSaveTimer);
  }, [hasUnsavedChanges, isSaving, readOnly, autoSaveInterval]);

  // Subscribe to document events
  useEventSubscription([EventType.DOCUMENT_UPDATED], (event) => {
    if (event.data.documentId === documentId && event.data.userId !== currentUser.id) {
      toast({
        title: "Document Updated",
        description: `${event.data.user?.name || "Someone"} updated this document`,
      });
    }
  });

  // Handle editor change
  const handleEditorChange = (newContent: string) => {
    if (readOnly) return;

    setContent(newContent);
    contentRef.current = newContent;
    setHasUnsavedChanges(true);

    // Set typing indicator
    setIsTyping(true);

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 2000);
  };

  // Send cursor position to other users
  const sendCursorPosition = debounce(() => {
    if (!editorRef.current || !isConnected || readOnly) return;

    const editor = editorRef.current;
    const selection = editor.selection.getRng();

    if (selection) {
      const startContainer = selection.startContainer;
      const startOffset = selection.startOffset;

      // Get path to element
      const path = editor.dom.getPath(startContainer);

      // Send cursor position to server
      invoke(HubMethod.SEND_CURSOR_POSITION, documentId, {
        userId: currentUser.id,
        path,
        offset: startOffset
      }).catch((error: any) => {
        console.error("Failed to send cursor position:", error);
      });
    }
  }, 500);

  // Handle save
  const handleSave = async () => {
    if (readOnly || !hasUnsavedChanges || isSaving) return;

    setIsSaving(true);

    try {
      // Save document content
      await documentService.updateDocumentContent(
        documentId,
        contentRef.current,
        projectId,
        organizationId
      );

      setLastSaved(new Date());
      setHasUnsavedChanges(false);

      toast({
        title: "Document Saved",
        description: "Your changes have been saved successfully",
        variant: "default"
      });

      if (onSaved) {
        onSaved();
      }
    } catch (error) {
      console.error("Error saving document:", error);

      toast({
        title: "Save Failed",
        description: "Failed to save document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Save className="h-5 w-5" />
              {documentName}
            </CardTitle>
            <CardDescription>
              {readOnly ? "Viewing document" : "Edit document in real-time with your team"}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {!readOnly && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={isSaving || !hasUnsavedChanges}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <Badge variant="outline" className="h-8 flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {activeUsers.filter(u => u.isActive).length + 1}
                    </Badge>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-2">
                    <p className="font-semibold">Active Users</p>
                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={currentUser.avatarUrl} alt={currentUser.name} />
                          <AvatarFallback>
                            {currentUser.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{currentUser.name} (You)</span>
                      </div>
                      {activeUsers
                        .filter(user => user.isActive)
                        .map(user => (
                          <div key={user.id} className="flex items-center gap-2">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={user.avatarUrl} alt={user.name} />
                              <AvatarFallback>
                                {user.name.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span>{user.name}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        {lastSaved && (
          <div className="flex items-center mt-2 text-xs text-muted-foreground">
            <Clock className="mr-1 h-3 w-3" />
            Last saved: {lastSaved.toLocaleTimeString()}
            {hasUnsavedChanges ? (
              <Badge variant="outline" className="ml-2 text-xs">
                <AlertCircle className="mr-1 h-3 w-3 text-amber-500" />
                Unsaved changes
              </Badge>
            ) : (
              <Badge variant="outline" className="ml-2 text-xs">
                <CheckCircle className="mr-1 h-3 w-3 text-green-500" />
                All changes saved
              </Badge>
            )}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="relative border rounded-md">
          <Editor
            apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY}
            onInit={(_: any, editor: any) => {
              editorRef.current = editor;
              editor.on('NodeChange', sendCursorPosition);
            }}
            initialValue={initialContent}
            value={content}
            onEditorChange={handleEditorChange}
            disabled={readOnly}
            init={{
              height: 500,
              menubar: !readOnly,
              plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount',
                'emoticons', 'hr', 'pagebreak', 'nonbreaking', 'template', 'paste'
              ],
              toolbar: readOnly ? false : (
                'undo redo | formatselect | ' +
                'bold italic backcolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | image table link | help'
              ),
              content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
              readonly: readOnly,
              branding: false,
              promotion: false,
              paste_data_images: true,
              image_advtab: true,
              automatic_uploads: true,
              file_picker_types: 'image',
              // Production-ready file upload handler
              file_picker_callback: (cb: any, _value: any, _meta: any) => {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');

                input.onchange = async () => {
                  const file = input.files?.[0];
                  if (!file) return;

                  // Show upload progress in editor
                  const progressId = 'upload-' + (new Date()).getTime();

                  try {
                    editorRef.current.insertContent(`
                      <div id="${progressId}" style="padding: 10px; background: #f1f5f9; border-radius: 4px; margin: 10px 0;">
                        <p>Uploading ${file.name}...</p>
                        <div style="height: 5px; background: #e2e8f0; border-radius: 2px; overflow: hidden;">
                          <div style="width: 10%; height: 100%; background: #3b82f6;"></div>
                        </div>
                      </div>
                    `);

                    // Update progress indicator
                    const updateProgress = (percent: number) => {
                      const progressBar = editorRef.current.dom.select(`#${progressId} div div`)[0];
                      if (progressBar) {
                        progressBar.style.width = `${percent}%`;
                      }
                    };

                    // Upload file to server
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('documentId', documentId);
                    formData.append('projectId', projectId);
                    formData.append('organizationId', organizationId);

                    // Upload image to backend
                    const uploadResponse = await fetch('/api/documents/upload-image', {
                      method: 'POST',
                      body: formData
                    });

                    if (!uploadResponse.ok) {
                      throw new Error(`Upload failed: ${uploadResponse.statusText}`);
                    }

                    const response = await uploadResponse.json();

                    // Validate response structure
                    if (!response.url) {
                      throw new Error('Invalid upload response: missing URL');
                    }

                    // Simulate upload progress
                    for (let i = 10; i <= 100; i += 10) {
                      await new Promise(resolve => setTimeout(resolve, 100));
                      updateProgress(i);
                    }

                    // Remove progress indicator
                    const progressElement = editorRef.current.dom.select(`#${progressId}`)[0];
                    if (progressElement) {
                      progressElement.parentNode.removeChild(progressElement);
                    }

                    // Insert the uploaded image
                    cb(response.url, {
                      title: file.name,
                      alt: file.name,
                      width: response.width || '',
                      height: response.height || ''
                    });

                    // Notify about successful upload
                    toast({
                      title: "Image Uploaded",
                      description: `${file.name} has been uploaded successfully`,
                      variant: "default"
                    });
                  } catch (error) {
                    console.error("Error uploading image:", error);

                    // Remove progress indicator if it exists
                    const progressElement = editorRef.current.dom.select(`#${progressId}`)[0];
                    if (progressElement) {
                      progressElement.parentNode.removeChild(progressElement);
                    }

                    // Show error message
                    toast({
                      title: "Upload Failed",
                      description: "Failed to upload image. Please try again.",
                      variant: "destructive"
                    });
                  }
                };

                input.click();
              }
            }}
          />
          {isTyping && (
            <div className="absolute bottom-2 right-2">
              <Badge variant="secondary" className="animate-pulse">
                Someone is typing...
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
