"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useOrganizations } from "@/hooks/organizations";
import { useSearch, usePopularSearchQueries } from "@/hooks/search";
import type { SearchQuery } from "@/services/search-service";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Search as SearchIcon,
  FileText,
  FolderKanban,
  Calendar,
  Tag,
  SlidersHorizontal,
  X,
  Sparkles
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from "@/components/ui/sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { SearchBar } from "@/components/search/SearchBar";
import { SearchResults } from "@/components/search/SearchResults";
import { DatePickerWithRange } from "@/components/date-range-picker";
import { DateRange } from "react-day-picker";

export default function SearchPage() {
  // Get search params from URL
  const searchParams = useSearchParams();
  const initialQuery = searchParams?.get('q') || '';

  // Organization context
  useOrganizations();

  // Search state
  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [activeTab, setActiveTab] = useState("all");
  const [hasSearched, setHasSearched] = useState(Boolean(initialQuery));

  // Use search hook
  const {
    results: searchResults,
    isLoading: isSearching,
    search: handleSearch
  } = useSearch();

  // Handle result click
  const handleResultClick = (resultId: string, position: number) => {
    // Track result click for analytics
    console.log('Result clicked:', resultId, position);
  };

  // Get popular queries
  const { popularQueries } = usePopularSearchQueries();

  // Filter states
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [selectedDocumentTypes, setSelectedDocumentTypes] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // Check if any filters are applied
  const hasFilters = Boolean(
    dateRange || selectedDocumentTypes.length > 0 || selectedTags.length > 0
  );

  // Initialize search if query is in URL
  useEffect(() => {
    if (initialQuery) {
      executeSearch();
    }
  }, [initialQuery]);

  // Execute search
  const executeSearch = () => {
    if (!searchQuery.trim()) return;

    setHasSearched(true);

    // Build scope based on active tab
    let scope: string[] | undefined;
    if (activeTab !== 'all') {
      scope = [activeTab.toUpperCase()];
    }

    // Build filters
    const filters: Record<string, any> = {};

    if (selectedDocumentTypes.length > 0) {
      filters.documentTypes = selectedDocumentTypes;
    }

    if (selectedTags.length > 0) {
      filters.tags = selectedTags;
    }

    if (dateRange?.from) {
      filters.dateRange = {
        start: dateRange.from,
        end: dateRange.to || dateRange.from
      };
    }

    // Execute search
    handleSearch({
      query: searchQuery,
      type: scope,
      tags: filters.tags,
      dateRange: filters.dateRange,
      sortBy: 'relevance',
      sortOrder: 'desc',
      page: 1,
      pageSize: 20
    } as SearchQuery);
  };

  // Handle suggested query click
  const handleSuggestedQueryClick = (query: string) => {
    setSearchQuery(query);
    handleSearch({
      query,
      sortBy: 'relevance',
      sortOrder: 'desc',
      page: 1,
      pageSize: 20
    } as SearchQuery);
  };

  // Clear all filters
  const clearFilters = () => {
    setDateRange(undefined);
    setSelectedDocumentTypes([]);
    setSelectedTags([]);
  };

  // Document types for filter
  const documentTypes = [
    "PDF", "DOCX", "XLSX", "PPTX", "TXT", "CSV", "IMAGE"
  ];

  // Common tags for filter
  const commonTags = [
    "financial", "quarterly", "2023", "legal", "contract",
    "agreement", "invoice", "finance", "paid", "reports"
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Search</h1>
        <p className="text-muted-foreground">
          Search across documents, projects, and more with AI-powered results
        </p>
      </div>

      <div className="flex gap-2">
        <div className="relative flex-1">
          <SearchBar
            placeholder="Search by keyword, tag, or content..."
            value={searchQuery}
            onChange={setSearchQuery}
            onSearch={executeSearch}
            autoFocus={!initialQuery}
          />
        </div>
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" className="gap-2">
              <SlidersHorizontal className="h-4 w-4" />
              <span className="hidden md:inline">Filters</span>
              {hasFilters && (
                <Badge variant="secondary" className="ml-1">
                  {selectedDocumentTypes.length + selectedTags.length + (dateRange ? 1 : 0)}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent className="sm:max-w-md">
            <SheetHeader>
              <SheetTitle>Search Filters</SheetTitle>
              <SheetDescription>
                Refine your search results with filters
              </SheetDescription>
            </SheetHeader>
            <div className="grid gap-6 py-6">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Date Range</h3>
                <DatePickerWithRange
                  selected={dateRange}
                  onSelect={setDateRange}
                />
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Document Types</h3>
                <div className="grid grid-cols-2 gap-2">
                  {documentTypes.map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={selectedDocumentTypes.includes(type)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedDocumentTypes([...selectedDocumentTypes, type]);
                          } else {
                            setSelectedDocumentTypes(selectedDocumentTypes.filter(t => t !== type));
                          }
                        }}
                      />
                      <Label htmlFor={`type-${type}`}>{type}</Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {commonTags.map((tag) => (
                    <Badge
                      key={tag}
                      variant={selectedTags.includes(tag) ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => {
                        if (selectedTags.includes(tag)) {
                          setSelectedTags(selectedTags.filter(t => t !== tag));
                        } else {
                          setSelectedTags([...selectedTags, tag]);
                        }
                      }}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
            <SheetFooter>
              <Button variant="outline" onClick={clearFilters} disabled={!hasFilters}>
                Clear Filters
              </Button>
              <SheetClose asChild>
                <Button onClick={executeSearch}>Apply Filters</Button>
              </SheetClose>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </div>

      {hasFilters && (
        <div className="flex flex-wrap gap-2 items-center">
          <span className="text-sm text-muted-foreground">Filters:</span>
          {dateRange && dateRange.from && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {dateRange.from.toLocaleDateString()} - {dateRange.to ? dateRange.to.toLocaleDateString() : dateRange.from.toLocaleDateString()}
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() => setDateRange(undefined)}
              />
            </Badge>
          )}
          {selectedDocumentTypes.map(type => (
            <Badge key={type} variant="secondary" className="flex items-center gap-1">
              <FileText className="h-3 w-3" />
              {type}
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() => setSelectedDocumentTypes(selectedDocumentTypes.filter(t => t !== type))}
              />
            </Badge>
          ))}
          {selectedTags.map(tag => (
            <Badge key={tag} variant="secondary" className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              {tag}
              <X
                className="h-3 w-3 ml-1 cursor-pointer"
                onClick={() => setSelectedTags(selectedTags.filter(t => t !== tag))}
              />
            </Badge>
          ))}
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            Clear All
          </Button>
        </div>
      )}

      {hasSearched && (
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 w-full md:w-auto">
            <TabsTrigger value="all" className="flex items-center gap-2">
              <SearchIcon className="h-4 w-4" />
              All
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Documents
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <FolderKanban className="h-4 w-4" />
              Projects
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            <SearchResults
              results={(searchResults as any)?.results || []}
              isLoading={isSearching}
              onResultClick={(result: any, position?: number) => handleResultClick(result.id, position || 0)}
              contextualResponse={(searchResults as any)?.contextualResponse}
              suggestedQueries={(searchResults as any)?.suggestedQueries}
              onSuggestedQueryClick={handleSuggestedQueryClick}
            />
          </TabsContent>

          <TabsContent value="documents" className="mt-6">
            <SearchResults
              results={((searchResults as any)?.results || []).filter((r: any) => r.type === 'document')}
              isLoading={isSearching}
              onResultClick={(result: any, position?: number) => handleResultClick(result.id, position || 0)}
              contextualResponse={(searchResults as any)?.contextualResponse}
              suggestedQueries={(searchResults as any)?.suggestedQueries}
              onSuggestedQueryClick={handleSuggestedQueryClick}
            />
          </TabsContent>

          <TabsContent value="projects" className="mt-6">
            <SearchResults
              results={((searchResults as any)?.results || []).filter((r: any) => r.type === 'project')}
              isLoading={isSearching}
              onResultClick={(result: any, position?: number) => handleResultClick(result.id, position || 0)}
              contextualResponse={(searchResults as any)?.contextualResponse}
              suggestedQueries={(searchResults as any)?.suggestedQueries}
              onSuggestedQueryClick={handleSuggestedQueryClick}
            />
          </TabsContent>
        </Tabs>
      )}

      {!hasSearched && (
        <div className="flex flex-col items-center justify-center py-12">
          <SearchIcon className="h-16 w-16 text-muted-foreground mb-6" />
          <h2 className="text-xl font-medium mb-2">AI-Powered Search</h2>
          <p className="text-muted-foreground text-center max-w-md mb-8">
            Enter keywords, tags, or content to search across your documents and projects.
            Our AI will analyze your query and provide relevant results and insights.
          </p>

          {popularQueries.length > 0 && (
            <div className="space-y-2 text-center">
              <h3 className="text-sm font-medium">Popular Searches</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {popularQueries.map((query: any, index: number) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchQuery(query.query);
                      handleSearch({
                        query: query.query,
                        sortBy: 'relevance',
                        sortOrder: 'desc',
                        page: 1,
                        pageSize: 20
                      } as SearchQuery);
                    }}
                    className="gap-2"
                  >
                    <Sparkles className="h-3 w-3" />
                    {query.query}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
