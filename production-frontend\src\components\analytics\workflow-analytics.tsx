"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  GitBranch,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  Bar<PERSON>hart,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Calendar
} from "lucide-react";
import {
  <PERSON><PERSON>hart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>rts<PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  Cell,
  LineChart as RechartsLineChart,
  Line
} from "recharts";
// import { WorkflowStatsDto } from "@/services/workflow-service";
import { useWorkflowAnalytics } from "@/hooks/analytics/useWorkflowAnalytics";

interface WorkflowAnalyticsProps {
  stats?: any; // WorkflowStatsDto;
  className?: string;
}

export function WorkflowAnalytics({ stats, className }: WorkflowAnalyticsProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Use the workflow analytics hook
  const { analytics } = useWorkflowAnalytics();

  // Create derived data for the component
  const [timeRange, setTimeRange] = useState<'7days' | '30days' | '90days' | 'year'>('30days');

  const statusData = [
    { name: 'Active', value: 45, color: '#3b82f6' },
    { name: 'Completed', value: 30, color: '#10b981' },
    { name: 'Draft', value: 15, color: '#f59e0b' },
    { name: 'Cancelled', value: 10, color: '#ef4444' }
  ];

  const completionTimeData = [
    { name: '< 1 hour', count: 25 },
    { name: '1-4 hours', count: 35 },
    { name: '4-24 hours', count: 20 },
    { name: '> 24 hours', count: 10 }
  ];

  const workflowTrendData = [
    { date: '2024-01-01', created: 12, completed: 8 },
    { date: '2024-01-02', created: 15, completed: 12 },
    { date: '2024-01-03', created: 18, completed: 15 },
    { date: '2024-01-04', created: 14, completed: 16 },
    { date: '2024-01-05', created: 20, completed: 18 }
  ];

  const totalWorkflows = analytics.length || 100;
  const completionRate = 85;
  const avgCompletionTime = '2.5 hours';

  return (
    <div className={className}>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Workflow Analytics</h2>
          <p className="text-muted-foreground">
            Track and analyze workflow performance and trends
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Select
            value={timeRange}
            onValueChange={(value) => setTimeRange(value as '7days' | '30days' | '90days' | 'year')}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4" />
                <span>Time Range</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline">
            <BarChart className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Workflows
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <GitBranch className="h-5 w-5 text-primary mr-2" />
              <span className="text-2xl font-bold">{totalWorkflows}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <CheckCircle2 className="h-5 w-5 text-success mr-2" />
              <span className="text-2xl font-bold">{completionRate}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Avg. Completion Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-amber-500 mr-2" />
              <span className="text-2xl font-bold">{avgCompletionTime}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 w-full md:w-[400px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="completion">Completion</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Status Distribution</CardTitle>
              <CardDescription>
                Current status of all workflows in the system
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    outerRadius={150}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completion" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Completion Time</CardTitle>
              <CardDescription>
                Distribution of time taken to complete workflows
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsBarChart
                  data={completionTimeData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" name="Number of Workflows" fill="#3b82f6" />
                </RechartsBarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Trends</CardTitle>
              <CardDescription>
                Created vs. completed workflows over time
              </CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsLineChart
                  data={workflowTrendData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="created"
                    name="Created"
                    stroke="#3b82f6"
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="completed"
                    name="Completed"
                    stroke="#10b981"
                  />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
