"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Building2, Check, ChevronsUpDown, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useTenant } from "@/components/tenant-provider";
import { AdminOnly } from "@/components/permission-guard";

interface TenantSelectorProps {
  className?: string;
}

export function TenantSelector({ className }: TenantSelectorProps) {
  const router = useRouter();
  const { currentTenant, tenants, isLoading, switchTenant } = useTenant();
  const [open, setOpen] = useState(false);
  const [isSwitching, setIsSwitching] = useState(false);

  const handleSelect = async (tenantId: string) => {
    if (currentTenant?.id === tenantId) {
      setOpen(false);
      return;
    }

    try {
      setIsSwitching(true);
      await switchTenant(tenantId);
      router.refresh();
    } catch (error) {
      console.error("Failed to switch tenant:", error);
    } finally {
      setIsSwitching(false);
      setOpen(false);
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select a tenant"
          className={cn("w-full justify-between", className)}
          disabled={isLoading || isSwitching}
        >
          {isLoading || isSwitching ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Building2 className="mr-2 h-4 w-4" />
          )}
          <span className="truncate">
            {currentTenant?.displayName || "Select tenant"}
          </span>
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput placeholder="Search tenant..." />
          <CommandList>
            <CommandEmpty>No tenant found.</CommandEmpty>
            <CommandGroup heading="Your tenants">
              {tenants.map((tenant) => (
                <CommandItem
                  key={tenant.id}
                  value={tenant.id}
                  onSelect={() => handleSelect(tenant.id)}
                  className="cursor-pointer"
                >
                  <Building2 className="mr-2 h-4 w-4" />
                  <span className="truncate">{tenant.displayName}</span>
                  {currentTenant?.id === tenant.id && (
                    <Check className="ml-auto h-4 w-4" />
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
            <CommandSeparator />
            <AdminOnly>
              <CommandGroup heading="Administration">
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    router.push("/admin/tenants");
                  }}
                  className="cursor-pointer"
                >
                  <Building2 className="mr-2 h-4 w-4" />
                  <span>Manage tenants</span>
                </CommandItem>
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    router.push("/admin/tenants/create");
                  }}
                  className="cursor-pointer"
                >
                  <Building2 className="mr-2 h-4 w-4" />
                  <span>Create new tenant</span>
                </CommandItem>
              </CommandGroup>
            </AdminOnly>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
