import { useEffect, useRef, useCallback, useState } from 'react'

/**
 * Keyboard Navigation Hook
 * Provides keyboard navigation for lists, grids, and menus
 */

export type NavigationDirection = 'horizontal' | 'vertical' | 'grid'

export interface UseKeyboardNavigationOptions {
  direction?: NavigationDirection
  loop?: boolean
  disabled?: boolean
  onSelect?: (index: number) => void
  onEscape?: () => void
}

export interface UseKeyboardNavigationResult {
  activeIndex: number
  setActiveIndex: (index: number) => void
  getItemProps: (index: number) => {
    ref: (element: HTMLElement | null) => void
    tabIndex: number
    onKeyDown: (event: React.KeyboardEvent) => void
    onFocus: () => void
    'aria-selected': boolean
  }
  containerProps: {
    role: string
    onKeyDown: (event: React.KeyboardEvent) => void
  }
}

export function useKeyboardNavigation(
  itemCount: number,
  options: UseKeyboardNavigationOptions = {}
): UseKeyboardNavigationResult {
  const {
    direction = 'vertical',
    loop = true,
    disabled = false,
    onSelect,
    onEscape
  } = options

  const [activeIndex, setActiveIndex] = useState(-1)
  const itemRefs = useRef<(HTMLElement | null)[]>([])

  // Focus the active item
  useEffect(() => {
    if (activeIndex >= 0 && activeIndex < itemCount && itemRefs.current[activeIndex]) {
      itemRefs.current[activeIndex]?.focus()
    }
  }, [activeIndex, itemCount])

  const moveToNext = useCallback(() => {
    setActiveIndex(prev => {
      if (prev >= itemCount - 1) {
        return loop ? 0 : prev
      }
      return prev + 1
    })
  }, [itemCount, loop])

  const moveToPrevious = useCallback(() => {
    setActiveIndex(prev => {
      if (prev <= 0) {
        return loop ? itemCount - 1 : prev
      }
      return prev - 1
    })
  }, [itemCount, loop])

  const moveToFirst = useCallback(() => {
    setActiveIndex(0)
  }, [])

  const moveToLast = useCallback(() => {
    setActiveIndex(itemCount - 1)
  }, [itemCount])

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (disabled) return

    switch (event.key) {
      case 'ArrowDown':
        if (direction === 'vertical' || direction === 'grid') {
          event.preventDefault()
          moveToNext()
        }
        break

      case 'ArrowUp':
        if (direction === 'vertical' || direction === 'grid') {
          event.preventDefault()
          moveToPrevious()
        }
        break

      case 'ArrowRight':
        if (direction === 'horizontal' || direction === 'grid') {
          event.preventDefault()
          moveToNext()
        }
        break

      case 'ArrowLeft':
        if (direction === 'horizontal' || direction === 'grid') {
          event.preventDefault()
          moveToPrevious()
        }
        break

      case 'Home':
        event.preventDefault()
        moveToFirst()
        break

      case 'End':
        event.preventDefault()
        moveToLast()
        break

      case 'Enter':
      case ' ':
        if (activeIndex >= 0) {
          event.preventDefault()
          onSelect?.(activeIndex)
        }
        break

      case 'Escape':
        event.preventDefault()
        onEscape?.()
        break
    }
  }, [
    disabled,
    direction,
    activeIndex,
    moveToNext,
    moveToPrevious,
    moveToFirst,
    moveToLast,
    onSelect,
    onEscape
  ])

  const getItemProps = useCallback((index: number) => ({
    ref: (element: HTMLElement | null) => {
      itemRefs.current[index] = element
    },
    tabIndex: activeIndex === index ? 0 : -1,
    onKeyDown: handleKeyDown,
    onFocus: () => setActiveIndex(index),
    'aria-selected': activeIndex === index,
  }), [activeIndex, handleKeyDown])

  const containerProps = {
    role: direction === 'grid' ? 'grid' : 'listbox',
    onKeyDown: handleKeyDown,
  }

  return {
    activeIndex,
    setActiveIndex,
    getItemProps,
    containerProps,
  }
}

/**
 * Menu keyboard navigation hook
 */
export interface UseMenuNavigationOptions {
  isOpen?: boolean
  onClose?: () => void
  onSelect?: (index: number) => void
}

export function useMenuNavigation(
  itemCount: number,
  options: UseMenuNavigationOptions = {}
) {
  const { isOpen = false, onClose, onSelect } = options

  const navigation = useKeyboardNavigation(itemCount, {
    direction: 'vertical',
    loop: true,
    disabled: !isOpen,
    onSelect,
    onEscape: onClose,
  })

  // Reset active index when menu opens
  useEffect(() => {
    if (isOpen) {
      navigation.setActiveIndex(0)
    } else {
      navigation.setActiveIndex(-1)
    }
  }, [isOpen, navigation])

  return navigation
}

/**
 * Grid keyboard navigation hook
 */
export interface UseGridNavigationOptions {
  columns: number
  onSelect?: (rowIndex: number, columnIndex: number) => void
  onEscape?: () => void
}

export function useGridNavigation(
  rowCount: number,
  options: UseGridNavigationOptions
) {
  const { columns, onSelect, onEscape } = options
  const [activeRow, setActiveRow] = useState(0)
  const [activeColumn, setActiveColumn] = useState(0)
  const itemRefs = useRef<(HTMLElement | null)[][]>([])

  const totalItems = rowCount * columns

  // Initialize refs array
  useEffect(() => {
    itemRefs.current = Array(rowCount).fill(null).map(() => Array(columns).fill(null))
  }, [rowCount, columns])

  // Focus the active cell
  useEffect(() => {
    const activeElement = itemRefs.current[activeRow]?.[activeColumn]
    if (activeElement) {
      activeElement.focus()
    }
  }, [activeRow, activeColumn])

  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        setActiveRow(prev => Math.min(prev + 1, rowCount - 1))
        break

      case 'ArrowUp':
        event.preventDefault()
        setActiveRow(prev => Math.max(prev - 1, 0))
        break

      case 'ArrowRight':
        event.preventDefault()
        setActiveColumn(prev => Math.min(prev + 1, columns - 1))
        break

      case 'ArrowLeft':
        event.preventDefault()
        setActiveColumn(prev => Math.max(prev - 1, 0))
        break

      case 'Home':
        event.preventDefault()
        if (event.ctrlKey) {
          setActiveRow(0)
          setActiveColumn(0)
        } else {
          setActiveColumn(0)
        }
        break

      case 'End':
        event.preventDefault()
        if (event.ctrlKey) {
          setActiveRow(rowCount - 1)
          setActiveColumn(columns - 1)
        } else {
          setActiveColumn(columns - 1)
        }
        break

      case 'Enter':
      case ' ':
        event.preventDefault()
        onSelect?.(activeRow, activeColumn)
        break

      case 'Escape':
        event.preventDefault()
        onEscape?.()
        break
    }
  }, [activeRow, activeColumn, rowCount, columns, onSelect, onEscape])

  const getCellProps = useCallback((rowIndex: number, columnIndex: number) => ({
    ref: (element: HTMLElement | null) => {
      if (!itemRefs.current[rowIndex]) {
        itemRefs.current[rowIndex] = []
      }
      itemRefs.current[rowIndex][columnIndex] = element
    },
    tabIndex: activeRow === rowIndex && activeColumn === columnIndex ? 0 : -1,
    onKeyDown: handleKeyDown,
    onFocus: () => {
      setActiveRow(rowIndex)
      setActiveColumn(columnIndex)
    },
    'aria-selected': activeRow === rowIndex && activeColumn === columnIndex,
  }), [activeRow, activeColumn, handleKeyDown])

  const gridProps = {
    role: 'grid',
    onKeyDown: handleKeyDown,
  }

  return {
    activeRow,
    activeColumn,
    setActivePosition: (row: number, column: number) => {
      setActiveRow(row)
      setActiveColumn(column)
    },
    getCellProps,
    gridProps,
  }
}
