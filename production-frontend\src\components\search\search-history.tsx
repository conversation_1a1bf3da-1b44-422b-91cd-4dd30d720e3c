'use client';

import React from 'react';
import { Clock, X, Search, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: string;
  resultCount?: number;
  filters?: Record<string, any>;
}

export interface PopularSearch {
  query: string;
  count: number;
  trend: 'up' | 'down' | 'stable';
}

export interface SearchHistoryProps {
  recentSearches: SearchHistoryItem[];
  popularSearches?: PopularSearch[];
  onSearchSelect: (query: string) => void;
  onRemoveSearch: (id: string) => void;
  onClearHistory: () => void;
  className?: string;
}

export function SearchHistory({
  recentSearches,
  popularSearches = [],
  onSearchSelect,
  onRemoveSearch,
  onClearHistory,
  className
}: SearchHistoryProps) {
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInHours / 24);
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
  };

  const getTrendIcon = (trend: PopularSearch['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down':
        return <TrendingUp className="h-3 w-3 text-red-600 rotate-180" />;
      default:
        return null;
    }
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Recent Searches */}
        {recentSearches.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Recent Searches
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearHistory}
                  className="text-muted-foreground hover:text-foreground"
                >
                  Clear all
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {recentSearches.slice(0, 10).map((search) => (
                  <div
                    key={search.id}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors group"
                  >
                    <div 
                      className="flex items-center space-x-3 flex-1 cursor-pointer"
                      onClick={() => onSearchSelect(search.query)}
                    >
                      <Search className="h-4 w-4 text-muted-foreground" />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{search.query}</div>
                        <div className="text-sm text-muted-foreground flex items-center space-x-2">
                          <span>{formatTimestamp(search.timestamp)}</span>
                          {search.resultCount !== undefined && (
                            <>
                              <span>•</span>
                              <span>{search.resultCount} results</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() => onRemoveSearch(search.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Popular Searches */}
        {popularSearches.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Popular Searches
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-2">
                {popularSearches.slice(0, 8).map((search, index) => (
                  <div
                    key={search.query}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() => onSearchSelect(search.query)}
                  >
                    <div className="flex items-center space-x-3 flex-1">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium">
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{search.query}</div>
                        <div className="text-sm text-muted-foreground">
                          {search.count} searches
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getTrendIcon(search.trend)}
                      <Badge variant="outline" className="text-xs">
                        {search.trend}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Empty State */}
        {recentSearches.length === 0 && popularSearches.length === 0 && (
          <Card>
            <CardContent className="py-8 text-center">
              <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No search history</h3>
              <p className="text-muted-foreground">
                Your recent searches will appear here
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
