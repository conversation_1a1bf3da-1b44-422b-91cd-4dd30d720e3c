'use client';

import { useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useWorkflow } from '@/hooks/workflows';

export default function WorkflowRedirectPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const workflowId = params?.id as string;

  // Get projectId from query params or from workflow data
  const projectIdFromQuery = searchParams?.get('projectId');

  // Use the workflow hook to get workflow data
  const { data: workflow } = useWorkflow(workflowId);

  useEffect(() => {
    // If we have a projectId from query params, redirect immediately
    if (projectIdFromQuery) {
      router.replace(`/projects/${projectIdFromQuery}/workflows/${workflowId}`);
      return;
    }

    // If workflow is loaded and has a projectId, redirect to the new URL
    if (workflow && workflow.projectId) {
      router.replace(`/projects/${workflow.projectId}/workflows/${workflowId}`);
    }
  }, [workflow, workflowId, projectIdFromQuery, router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
      <p>Redirecting to workflow...</p>
    </div>
  );
}
