"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Container } from "./container"

const navbarVariants = cva(
  "w-full border-b bg-background z-10",
  {
    variants: {
      variant: {
        default: "border-b",
        transparent: "bg-transparent border-transparent",
        sticky: "sticky top-0 border-b",
        fixed: "fixed top-0 left-0 right-0 border-b",
      },
      size: {
        default: "h-16",
        sm: "h-12",
        lg: "h-20",
      },
      shadow: {
        none: "",
        sm: "shadow-sm",
        md: "shadow-md",
        lg: "shadow-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      shadow: "none",
    },
  }
)

export interface NavbarProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof navbarVariants> {
  children: React.ReactNode
  containerSize?: "sm" | "md" | "lg" | "xl" | "full"
  logo?: React.ReactNode
  actions?: React.ReactNode
}

const Navbar = React.forwardRef<HTMLElement, NavbarProps>(
  ({ 
    className, 
    variant, 
    size, 
    shadow, 
    containerSize = "lg", 
    logo, 
    actions, 
    children, 
    ...props 
  }, ref) => {
    return (
      <nav
        ref={ref}
        className={cn(navbarVariants({ variant, size, shadow, className }))}
        {...props}
      >
        <Container size={containerSize} className="h-full">
          <div className="flex items-center justify-between h-full">
            {logo && (
              <div className="flex-shrink-0">
                {logo}
              </div>
            )}
            
            {children && (
              <div className="hidden md:flex items-center space-x-4 mx-4 flex-1">
                {children}
              </div>
            )}
            
            {actions && (
              <div className="flex items-center space-x-2">
                {actions}
              </div>
            )}
          </div>
        </Container>
      </nav>
    )
  }
)
Navbar.displayName = "Navbar"

export interface NavbarItemProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  active?: boolean
}

const NavbarItem = React.forwardRef<HTMLDivElement, NavbarItemProps>(
  ({ className, active, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "px-3 py-2 text-sm font-medium rounded-md cursor-pointer transition-colors",
          active 
            ? "bg-primary/10 text-primary" 
            : "text-muted-foreground hover:text-foreground hover:bg-accent",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
NavbarItem.displayName = "NavbarItem"

export interface NavbarMenuProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
}

const NavbarMenu = React.forwardRef<HTMLDivElement, NavbarMenuProps>(
  ({ className, children, ...props }, ref) => {
    const [isOpen, setIsOpen] = React.useState(false)
    
    return (
      <div ref={ref} className={cn("md:hidden", className)} {...props}>
        <button
          className="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
              <line x1="4" y1="12" x2="20" y2="12"></line>
              <line x1="4" y1="6" x2="20" y2="6"></line>
              <line x1="4" y1="18" x2="20" y2="18"></line>
            </svg>
          )}
        </button>
        
        {isOpen && (
          <div className="absolute top-full left-0 right-0 bg-background border-b shadow-md p-4 space-y-2">
            {children}
          </div>
        )}
      </div>
    )
  }
)
NavbarMenu.displayName = "NavbarMenu"

export { Navbar, NavbarItem, NavbarMenu, navbarVariants }
