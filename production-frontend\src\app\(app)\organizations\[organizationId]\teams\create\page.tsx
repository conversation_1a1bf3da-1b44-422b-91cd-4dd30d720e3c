"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { useCreateTeam } from "@/hooks/teams";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { TeamForm } from "@/components/teams";
import { CreateTeamDto, UpdateTeamDto } from "@/types/team";

export default function CreateTeamPage() {
  const params = useParams();
  const router = useRouter();

  // Ensure params is not null
  if (!params) {
    return <div>Loading...</div>;
  }

  const organizationId = params.organizationId as string;

  // Create team mutation
  const createTeamMutation = useCreateTeam();

  // Handle form submission
  const handleCreateTeam = (data: CreateTeamDto | UpdateTeamDto) => {
    // We know this is a CreateTeamDto because we're in the create page
    const createData = data as CreateTeamDto;
    createTeamMutation.mutate(
      {
        ...createData,
        organizationId,
      },
      {
        onSuccess: (team) => {
          router.push(`/organizations/${organizationId}/teams/${team.id}`);
        },
      }
    );
  };

  return (
    <div className="container mx-auto py-6 space-y-6 max-w-2xl">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href={`/organizations/${organizationId}/teams`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">Create Team</h1>
      </div>

      <div className="border rounded-lg p-6 bg-card">
        <TeamForm
          organizationId={organizationId}
          onSubmit={handleCreateTeam}
          isSubmitting={createTeamMutation.isPending}
        />
      </div>
    </div>
  );
}
