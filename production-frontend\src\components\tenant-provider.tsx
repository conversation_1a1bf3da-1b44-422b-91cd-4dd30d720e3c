"use client";

import { useEffect, ReactNode } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useSessionChecked } from "@/stores/auth-store";
import { initializeTenantStore } from "@/stores/tenant-store";

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const { user, isAuthenticated } = useAuth();
  const sessionChecked = useSessionChecked();

  // Initialize tenant store when user is authenticated
  useEffect(() => {
    if (sessionChecked && isAuthenticated && user) {
      initializeTenantStore().catch((error) => {
        console.error('Failed to initialize tenant store:', error);
      });
    }
  }, [sessionChecked, isAuthenticated, user]);

  // No need for Context Provider anymore - just return children
  // The Zustand store handles all tenant state globally
  return <>{children}</>;
}

// Re-export the Zustand-based useTenant hook for backward compatibility
export { useTenant } from "@/stores/tenant-store";
