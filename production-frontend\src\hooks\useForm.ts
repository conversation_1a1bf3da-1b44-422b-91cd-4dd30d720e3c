import { useState, useCallback, useRef } from 'react'

/**
 * Form Hook
 * Manages form state, validation, and submission
 */

export interface UseFormOptions<T> {
  initialValues: T
  validate?: (values: T) => Record<string, string>
  onSubmit?: (values: T) => void | Promise<void>
  validateOnChange?: boolean
  validateOnBlur?: boolean
}

export interface UseFormResult<T> {
  values: T
  errors: Record<string, string>
  touched: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
  isDirty: boolean
  
  // Field methods
  setValue: (name: keyof T, value: any) => void
  setValues: (values: Partial<T>) => void
  setError: (name: keyof T, error: string) => void
  setErrors: (errors: Record<string, string>) => void
  setTouched: (name: keyof T, touched?: boolean) => void
  
  // Form methods
  handleChange: (name: keyof T) => (value: any) => void
  handleBlur: (name: keyof T) => () => void
  handleSubmit: (e?: React.FormEvent) => Promise<void>
  reset: (values?: T) => void
  validate: () => boolean
}

export function useForm<T extends Record<string, any>>(
  options: UseFormOptions<T>
): UseFormResult<T> {
  const {
    initialValues,
    validate,
    onSubmit,
    validateOnChange = false,
    validateOnBlur = true,
  } = options

  const [values, setValuesState] = useState<T>(initialValues)
  const [errors, setErrorsState] = useState<Record<string, string>>({})
  const [touched, setTouchedState] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const initialValuesRef = useRef(initialValues)

  // Computed properties
  const isValid = Object.keys(errors).length === 0
  const isDirty = JSON.stringify(values) !== JSON.stringify(initialValuesRef.current)

  // Field methods
  const setValue = useCallback((name: keyof T, value: any) => {
    setValuesState(prev => ({ ...prev, [name]: value }))
    
    if (validateOnChange && validate) {
      const newValues = { ...values, [name]: value }
      const validationErrors = validate(newValues)
      setErrorsState(prev => ({
        ...prev,
        [name]: validationErrors[name as string] || ''
      }))
    }
  }, [values, validate, validateOnChange])

  const setValues = useCallback((newValues: Partial<T>) => {
    setValuesState(prev => ({ ...prev, ...newValues }))
    
    if (validateOnChange && validate) {
      const updatedValues = { ...values, ...newValues }
      const validationErrors = validate(updatedValues)
      setErrorsState(validationErrors)
    }
  }, [values, validate, validateOnChange])

  const setError = useCallback((name: keyof T, error: string) => {
    setErrorsState(prev => ({ ...prev, [name]: error }))
  }, [])

  const setErrors = useCallback((newErrors: Record<string, string>) => {
    setErrorsState(newErrors)
  }, [])

  const setTouched = useCallback((name: keyof T, isTouched = true) => {
    setTouchedState(prev => ({ ...prev, [name]: isTouched }))
  }, [])

  // Form methods
  const handleChange = useCallback((name: keyof T) => (value: any) => {
    setValue(name, value)
  }, [setValue])

  const handleBlur = useCallback((name: keyof T) => () => {
    setTouched(name, true)
    
    if (validateOnBlur && validate) {
      const validationErrors = validate(values)
      setError(name, validationErrors[name as string] || '')
    }
  }, [values, validate, validateOnBlur, setTouched, setError])

  const validateForm = useCallback(() => {
    if (!validate) return true
    
    const validationErrors = validate(values)
    setErrors(validationErrors)
    return Object.keys(validationErrors).length === 0
  }, [values, validate, setErrors])

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    e?.preventDefault()
    
    if (!validateForm()) return
    
    setIsSubmitting(true)
    try {
      await onSubmit?.(values)
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }, [values, validateForm, onSubmit])

  const reset = useCallback((newValues?: T) => {
    const resetValues = newValues || initialValues
    setValuesState(resetValues)
    setErrorsState({})
    setTouchedState({})
    setIsSubmitting(false)
    initialValuesRef.current = resetValues
  }, [initialValues])

  return {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    isDirty,
    
    setValue,
    setValues,
    setError,
    setErrors,
    setTouched,
    
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
    validate: validateForm,
  }
}
