'use client'

/**
 * Enhanced Login Component
 * Comprehensive authentication component supporting all Azure AD B2C user flows
 */

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import {
  AlertCircle,
  LogIn,
  UserPlus,
  Loader2,
  ArrowRight,
  CheckCircle2,
  Key,
  Settings,
  Shield,
  User
} from 'lucide-react'
import { useMSALAuth, B2CPolicyType, AuthMethod } from '@/components/auth/msal-auth-provider'

interface EnhancedLoginComponentProps {
  defaultTab?: 'signin' | 'signup' | 'reset' | 'profile'
  redirectUrl?: string
  showTabs?: boolean
  title?: string
  subtitle?: string
}

export function EnhancedLoginComponent({
  defaultTab = 'signin',
  redirectUrl = '/dashboard',
  showTabs = true,
  title = 'Welcome to HEPZ',
  subtitle = 'Secure authentication powered by Microsoft'
}: EnhancedLoginComponentProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [activeTab, setActiveTab] = useState(defaultTab)
  const [showSuccess, setShowSuccess] = useState(false)

  // Safely get MSAL auth context with error boundary
  let msalAuth
  try {
    msalAuth = useMSALAuth()
  } catch (error) {
    console.error('[Enhanced Login] MSAL Auth context not available:', error)
    // Return loading state if context is not available
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12">
        <div className="w-full max-w-md space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
            <p className="mt-2 text-gray-600">Initializing authentication...</p>
          </div>
          <Card className="shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto" />
                <p className="text-gray-600">Setting up secure authentication</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const {
    isAuthenticated,
    isLoading,
    user,
    error,
    clearError,
    signIn,
    signUp,
    signOut,
    resetPassword,
    editProfile
  } = msalAuth

  // Get return URL from search params
  const returnUrl = searchParams.get('returnUrl') || redirectUrl

  // Handle tab changes from URL
  useEffect(() => {
    const tab = searchParams.get('tab') as 'signin' | 'signup' | 'reset' | 'profile'
    if (tab && ['signin', 'signup', 'reset', 'profile'].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      setShowSuccess(true)
      setTimeout(() => {
        router.push(returnUrl)
      }, 1500)
    }
  }, [isAuthenticated, user, router, returnUrl])

  // Get returnUrl from URL params
  const getReturnUrl = () => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search)
      return urlParams.get('returnUrl') || '/dashboard'
    }
    return '/dashboard'
  }

  // Authentication handlers using server-side API
  const handleSignIn = async () => {
    try {
      clearError()
      const returnUrl = getReturnUrl()
      // Redirect to server-side authentication
      window.location.href = `/api/auth/login?policy=B2C_1_SUSI&returnUrl=${encodeURIComponent(returnUrl)}`
    } catch (error) {
      console.error('Sign in failed:', error)
    }
  }

  const handleSignUp = async () => {
    try {
      clearError()
      const returnUrl = getReturnUrl()
      // Redirect to server-side authentication with sign-up policy
      window.location.href = `/api/auth/login?policy=B2C_1_SU&returnUrl=${encodeURIComponent(returnUrl)}`
    } catch (error) {
      console.error('Sign up failed:', error)
    }
  }

  const handlePasswordReset = async () => {
    try {
      clearError()
      const returnUrl = getReturnUrl()
      // Redirect to server-side authentication with password reset policy
      window.location.href = `/api/auth/login?policy=B2C_1_passwordreset1&returnUrl=${encodeURIComponent(returnUrl)}`
    } catch (error) {
      console.error('Password reset failed:', error)
    }
  }

  const handleProfileEdit = async () => {
    try {
      clearError()
      const returnUrl = getReturnUrl()
      // Redirect to server-side authentication with profile edit policy
      window.location.href = `/api/auth/login?policy=B2C_1_profileedit1&returnUrl=${encodeURIComponent(returnUrl)}`
    } catch (error) {
      console.error('Profile edit failed:', error)
    }
  }

  const handleSignOut = async () => {
    try {
      clearError()
      // Clear cookies and redirect to logout
      document.cookie = 'msal_access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      document.cookie = 'msal_user_info=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      document.cookie = 'msal_session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

      // Clear auth store
      if (typeof signOut === 'function') {
        await signOut(AuthMethod.POPUP)
      }

      // Redirect to login
      window.location.href = '/auth/enhanced-login'
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  // Update URL when tab changes
  const handleTabChange = (tab: string) => {
    setActiveTab(tab as any)
    const newUrl = new URL(window.location.href)
    newUrl.searchParams.set('tab', tab)
    router.push(newUrl.toString())
  }

  // Show success state if authenticated
  if (showSuccess && isAuthenticated && user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12">
        <div className="w-full max-w-md space-y-8">
          <Card className="shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto" />
                <h2 className="text-xl font-semibold text-gray-900">Welcome Back!</h2>
                <p className="text-gray-600">
                  Successfully authenticated as {user.email}
                </p>
                <div className="text-sm text-gray-500">
                  Redirecting to dashboard...
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4 py-12">
      <div className="w-full max-w-md space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
          <p className="mt-2 text-gray-600">{subtitle}</p>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Authentication Card */}
        <Card className="shadow-lg">
          {showTabs ? (
            <Tabs value={activeTab} onValueChange={handleTabChange}>
              <CardHeader className="pb-4">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="signin" className="text-xs">
                    <LogIn className="h-3 w-3 mr-1" />
                    Sign In
                  </TabsTrigger>
                  <TabsTrigger value="signup" className="text-xs">
                    <UserPlus className="h-3 w-3 mr-1" />
                    Sign Up
                  </TabsTrigger>
                  <TabsTrigger value="reset" className="text-xs">
                    <Key className="h-3 w-3 mr-1" />
                    Reset
                  </TabsTrigger>
                  <TabsTrigger value="profile" className="text-xs">
                    <Settings className="h-3 w-3 mr-1" />
                    Profile
                  </TabsTrigger>
                </TabsList>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Sign In Tab */}
                <TabsContent value="signin" className="space-y-4">
                  <div className="text-center space-y-2">
                    <CardTitle className="text-xl">Sign In</CardTitle>
                    <CardDescription>
                      Access your documents and workflows
                    </CardDescription>
                  </div>
                  
                  <Button
                    onClick={handleSignIn}
                    disabled={isLoading}
                    className="w-full h-11 text-base"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing in...
                      </>
                    ) : (
                      <>
                        <LogIn className="mr-2 h-4 w-4" />
                        Sign in with Microsoft
                      </>
                    )}
                  </Button>

                  <div className="text-center text-sm text-gray-500">
                    <p>Secure authentication with Azure AD B2C</p>
                  </div>
                </TabsContent>

                {/* Sign Up Tab */}
                <TabsContent value="signup" className="space-y-4">
                  <div className="text-center space-y-2">
                    <CardTitle className="text-xl">Create Account</CardTitle>
                    <CardDescription>
                      Get started with your new account
                    </CardDescription>
                  </div>
                  
                  <Button
                    onClick={handleSignUp}
                    disabled={isLoading}
                    className="w-full h-11 text-base"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating account...
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Create account with Microsoft
                      </>
                    )}
                  </Button>

                  <div className="text-center text-sm text-gray-500">
                    <p>Join thousands of users managing documents efficiently</p>
                  </div>
                </TabsContent>

                {/* Password Reset Tab */}
                <TabsContent value="reset" className="space-y-4">
                  <div className="text-center space-y-2">
                    <CardTitle className="text-xl">Reset Password</CardTitle>
                    <CardDescription>
                      Recover access to your account
                    </CardDescription>
                  </div>
                  
                  <Button
                    onClick={handlePasswordReset}
                    disabled={isLoading}
                    className="w-full h-11 text-base"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Initiating reset...
                      </>
                    ) : (
                      <>
                        <Key className="mr-2 h-4 w-4" />
                        Reset password
                      </>
                    )}
                  </Button>

                  <div className="text-center text-sm text-gray-500">
                    <p>You'll receive instructions to reset your password</p>
                  </div>
                </TabsContent>

                {/* Profile Edit Tab */}
                <TabsContent value="profile" className="space-y-4">
                  <div className="text-center space-y-2">
                    <CardTitle className="text-xl">Edit Profile</CardTitle>
                    <CardDescription>
                      Update your account information
                    </CardDescription>
                  </div>
                  
                  {isAuthenticated ? (
                    <div className="space-y-4">
                      <Button
                        onClick={handleProfileEdit}
                        disabled={isLoading}
                        className="w-full h-11 text-base"
                        size="lg"
                      >
                        {isLoading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Opening profile editor...
                          </>
                        ) : (
                          <>
                            <Settings className="mr-2 h-4 w-4" />
                            Edit profile
                          </>
                        )}
                      </Button>

                      <Button
                        onClick={handleSignOut}
                        disabled={isLoading}
                        variant="outline"
                        className="w-full h-11 text-base"
                        size="lg"
                      >
                        <LogIn className="mr-2 h-4 w-4 rotate-180" />
                        Sign out
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center space-y-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <User className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          Please sign in first to edit your profile
                        </p>
                      </div>
                      
                      <Button
                        onClick={() => handleTabChange('signin')}
                        variant="outline"
                        className="w-full"
                      >
                        Go to Sign In
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </TabsContent>
              </CardContent>
            </Tabs>
          ) : (
            // Simple single-purpose login without tabs
            <>
              <CardHeader className="space-y-1 pb-4">
                <CardTitle className="text-xl text-center">Sign In</CardTitle>
                <CardDescription className="text-center">
                  Access your documents and workflows
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={handleSignIn}
                  disabled={isLoading}
                  className="w-full h-11 text-base"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    <>
                      <LogIn className="mr-2 h-4 w-4" />
                      Sign in with Microsoft
                    </>
                  )}
                </Button>
              </CardContent>
            </>
          )}
        </Card>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <div className="flex items-center justify-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Secure authentication powered by Microsoft</span>
          </div>
          
          {/* Supported Policies Badge */}
          <div className="mt-2 flex flex-wrap justify-center gap-1">
            <Badge variant="secondary" className="text-xs">Sign In</Badge>
            <Badge variant="secondary" className="text-xs">Sign Up</Badge>
            <Badge variant="secondary" className="text-xs">Password Reset</Badge>
            <Badge variant="secondary" className="text-xs">Profile Edit</Badge>
          </div>
        </div>
      </div>
    </div>
  )
}
