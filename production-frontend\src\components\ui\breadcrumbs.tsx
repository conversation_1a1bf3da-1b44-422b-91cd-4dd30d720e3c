"use client"

import * as React from "react"
import { ChevronRight, Home } from "lucide-react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const breadcrumbsVariants = cva(
  "flex items-center space-x-1 text-sm",
  {
    variants: {
      variant: {
        default: "text-muted-foreground",
        primary: "text-primary",
      },
      size: {
        default: "text-sm",
        sm: "text-xs",
        lg: "text-base",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface BreadcrumbsProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof breadcrumbsVariants> {
  items: {
    label: string
    href?: string
    icon?: React.ReactNode
  }[]
  homeHref?: string
  showHomeIcon?: boolean
  separator?: React.ReactNode
  activeItemClassName?: string
  itemClassName?: string
  maxItems?: number
  renderItem?: (item: { label: string; href?: string; icon?: React.ReactNode }, index: number, isLast: boolean) => React.ReactNode
}

const Breadcrumbs = React.forwardRef<HTMLDivElement, BreadcrumbsProps>(
  ({ 
    className, 
    variant, 
    size, 
    items, 
    homeHref = "/", 
    showHomeIcon = true,
    separator = <ChevronRight className="h-4 w-4" />,
    activeItemClassName,
    itemClassName,
    maxItems,
    renderItem,
    ...props 
  }, ref) => {
    // Handle max items with ellipsis
    const visibleItems = React.useMemo(() => {
      if (!maxItems || items.length <= maxItems) {
        return items
      }
      
      // Show first item, ellipsis, and last (maxItems - 2) items
      const firstItem = items[0]
      const lastItems = items.slice(-(maxItems - 1))
      
      return [
        firstItem,
        { label: "...", href: undefined },
        ...lastItems,
      ]
    }, [items, maxItems])
    
    return (
      <div
        ref={ref}
        className={cn(breadcrumbsVariants({ variant, size, className }))}
        {...props}
      >
        {showHomeIcon && (
          <>
            <a 
              href={homeHref} 
              className={cn(
                "flex items-center hover:text-foreground transition-colors",
                itemClassName
              )}
            >
              <Home className="h-4 w-4" />
            </a>
            <span className="text-muted-foreground">{separator}</span>
          </>
        )}
        
        {visibleItems.map((item, index) => {
          const isLast = index === visibleItems.length - 1
          
          if (renderItem) {
            return (
              <React.Fragment key={index}>
                {renderItem(item, index, isLast)}
                {!isLast && <span className="text-muted-foreground">{separator}</span>}
              </React.Fragment>
            )
          }
          
          return (
            <React.Fragment key={index}>
              {item.href && !isLast ? (
                <a 
                  href={item.href} 
                  className={cn(
                    "flex items-center hover:text-foreground transition-colors",
                    itemClassName
                  )}
                >
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  {item.label}
                </a>
              ) : (
                <span 
                  className={cn(
                    "flex items-center",
                    isLast && "font-medium text-foreground",
                    isLast && activeItemClassName,
                    itemClassName
                  )}
                >
                  {item.icon && <span className="mr-1">{item.icon}</span>}
                  {item.label}
                </span>
              )}
              {!isLast && <span className="text-muted-foreground">{separator}</span>}
            </React.Fragment>
          )
        })}
      </div>
    )
  }
)
Breadcrumbs.displayName = "Breadcrumbs"

export { Breadcrumbs, breadcrumbsVariants }
