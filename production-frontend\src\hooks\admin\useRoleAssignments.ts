/**
 * Role Assignments Hook
 * Manages role assignments for users
 */

import { useState, useCallback, useEffect } from 'react'
import { useMutation } from '@tanstack/react-query'
import { useApi } from '../useApi'
import type { 
  RoleAssignment, 
  CreateRoleAssignmentRequest, 
  UpdateRoleAssignmentRequest,
  BulkRoleAssignmentRequest,
  BulkRoleAssignmentResult,
  ID 
} from '../../types/role'

export interface UseRoleAssignmentsOptions {
  userId?: ID
  roleId?: ID
  organizationId?: ID
  projectId?: ID
  autoFetch?: boolean
}

export interface UseRoleAssignmentsResult {
  assignments: RoleAssignment[]
  loading: boolean
  error: string | null

  // Additional state for UI components
  role: any | null
  users: any[]
  isLoading: boolean
  selectedAssignmentId: ID | null
  setSelectedAssignmentId: (id: ID | null) => void

  // CRUD operations
  createAssignment: (request: CreateRoleAssignmentRequest) => Promise<RoleAssignment>
  updateAssignment: (id: ID, request: UpdateRoleAssignmentRequest) => Promise<RoleAssignment>
  deleteAssignment: (id: ID) => Promise<void>
  assignRole: (request: { userId: ID; roleId?: ID }) => Promise<RoleAssignment>

  // Additional operations expected by components
  isAssigningRole: boolean
  revokeRole: (assignmentId: ID) => Promise<void>
  isRevokingRole: boolean
  getUserById: (userId: ID) => any | null

  // Bulk operations
  bulkAssign: (request: BulkRoleAssignmentRequest) => Promise<BulkRoleAssignmentResult>
  bulkRevoke: (assignmentIds: ID[]) => Promise<void>

  // Utility functions
  refresh: () => Promise<void>
  getAssignmentsByUser: (userId: ID) => RoleAssignment[]
  getAssignmentsByRole: (roleId: ID) => RoleAssignment[]
  hasActiveAssignment: (userId: ID, roleId: ID) => boolean
}

export function useRoleAssignments(
  options: UseRoleAssignmentsOptions = {}
): UseRoleAssignmentsResult {
  const { userId, roleId, organizationId, projectId, autoFetch = true } = options
  
  const [assignments, setAssignments] = useState<RoleAssignment[]>([])
  const [selectedAssignmentId, setSelectedAssignmentId] = useState<ID | null>(null)
  const [role, setRole] = useState<any | null>(null)
  const [users, setUsers] = useState<any[]>([])

  // Build query parameters
  const queryParams = {
    ...(userId && { userId }),
    ...(roleId && { roleId }),
    ...(organizationId && { organizationId }),
    ...(projectId && { projectId }),
  }
  
  const queryString = new URLSearchParams(queryParams).toString()
  const endpoint = `/management/role-assignments/list${queryString ? `?${queryString}` : ''}`
  
  // Fetch assignments
  const { data, isLoading: loading, error, refetch } = useApi<{ assignments: RoleAssignment[] }>(
    endpoint,
    { immediate: autoFetch }
  )
  
  // Update local state when data changes
  useEffect(() => {
    if (data) {
      setAssignments(data.assignments || [])
    }
  }, [data])
  
  // Create assignment mutation
  const createMutation = useMutation({
    mutationFn: (request: CreateRoleAssignmentRequest) => fetch('/management/role-assignments/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => res.json()),
    onSuccess: (result: any) => {
      setAssignments(prev => [...prev, result.assignment])
    }
  })
  
  // Update assignment mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, request }: { id: ID; request: UpdateRoleAssignmentRequest }) =>
      fetch(`/management/role-assignments/${id}/update`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      }).then(res => res.json()),
    onSuccess: (updatedAssignment: RoleAssignment) => {
      setAssignments(prev =>
        prev.map(assignment =>
          assignment.id === updatedAssignment.id ? updatedAssignment : assignment
        )
      )
    }
  })
  
  // Delete assignment mutation
  const deleteMutation = useMutation({
    mutationFn: (id: ID) => fetch(`/management/role-assignments/${id}/delete`, {
      method: 'DELETE',
    }).then(res => {
      if (!res.ok) throw new Error('Failed to delete assignment')
    }),
    onSuccess: (_, id: ID) => {
      setAssignments(prev => prev.filter(assignment => assignment.id !== id))
    }
  })
  
  // Bulk assign mutation
  const bulkAssignMutation = useMutation({
    mutationFn: (request: BulkRoleAssignmentRequest) => fetch('/management/role-assignments/bulk-assign', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => res.json()),
    onSuccess: () => {
      // Refresh assignments after bulk operation
      refetch()
    }
  })
  
  // Bulk revoke mutation
  const bulkRevokeMutation = useMutation({
    mutationFn: (assignmentIds: ID[]) => fetch('/management/role-assignments/bulk-revoke', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ assignmentIds }),
    }).then(res => {
      if (!res.ok) throw new Error('Failed to revoke assignments')
    }),
    onSuccess: (_, assignmentIds: ID[]) => {
      setAssignments(prev =>
        prev.filter(assignment => !assignmentIds.includes(assignment.id))
      )
    }
  })
  
  // Action functions
  const createAssignment = useCallback(async (request: CreateRoleAssignmentRequest) => {
    return createMutation.mutateAsync(request)
  }, [createMutation])
  
  const updateAssignment = useCallback(async (id: ID, request: UpdateRoleAssignmentRequest) => {
    return updateMutation.mutateAsync({ id, request })
  }, [updateMutation])
  
  const deleteAssignment = useCallback(async (id: ID) => {
    return deleteMutation.mutateAsync(id)
  }, [deleteMutation])

  const assignRole = useCallback(async (request: { userId: ID; roleId?: ID }) => {
    const assignmentRequest: CreateRoleAssignmentRequest = {
      userId: request.userId,
      roleId: request.roleId || roleId!, // Use provided roleId or hook's roleId
      scope: 'organization' as any, // Default scope
      organizationId: organizationId,
    }
    return createAssignment(assignmentRequest)
  }, [createAssignment, organizationId, roleId])

  const revokeRole = useCallback(async (assignmentId: ID) => {
    return deleteAssignment(assignmentId)
  }, [deleteAssignment])

  const getUserById = useCallback((userId: ID) => {
    return users.find(user => user.id === userId) || null
  }, [users])
  
  const bulkAssign = useCallback(async (request: BulkRoleAssignmentRequest) => {
    return bulkAssignMutation.mutateAsync(request)
  }, [bulkAssignMutation])
  
  const bulkRevoke = useCallback(async (assignmentIds: ID[]) => {
    return bulkRevokeMutation.mutateAsync(assignmentIds)
  }, [bulkRevokeMutation])
  
  const refresh = useCallback(async () => {
    await refetch()
  }, [refetch])
  
  // Utility functions
  const getAssignmentsByUser = useCallback((userId: ID) => {
    return assignments.filter(assignment => assignment.userId === userId)
  }, [assignments])
  
  const getAssignmentsByRole = useCallback((roleId: ID) => {
    return assignments.filter(assignment => assignment.roleId === roleId)
  }, [assignments])
  
  const hasActiveAssignment = useCallback((userId: ID, roleId: ID) => {
    return assignments.some(assignment => 
      assignment.userId === userId && 
      assignment.roleId === roleId && 
      assignment.isActive &&
      (!assignment.expiresAt || new Date(assignment.expiresAt) > new Date())
    )
  }, [assignments])
  
  const isLoading = loading || createMutation.isPending || updateMutation.isPending || deleteMutation.isPending
  const isAssigningRole = createMutation.isPending
  const isRevokingRole = deleteMutation.isPending

  return {
    assignments,
    loading: isLoading,
    error: (error instanceof Error ? error.message : error) ||
           (createMutation.error instanceof Error ? createMutation.error.message : createMutation.error) ||
           (updateMutation.error instanceof Error ? updateMutation.error.message : updateMutation.error) ||
           (deleteMutation.error instanceof Error ? deleteMutation.error.message : deleteMutation.error) || null,

    // Additional state for UI components
    role,
    users,
    isLoading,
    selectedAssignmentId,
    setSelectedAssignmentId,

    createAssignment,
    updateAssignment,
    deleteAssignment,
    assignRole,

    // Additional operations expected by components
    isAssigningRole,
    revokeRole,
    isRevokingRole,
    getUserById,

    bulkAssign,
    bulkRevoke,

    refresh,
    getAssignmentsByUser,
    getAssignmentsByRole,
    hasActiveAssignment,
  }
}
