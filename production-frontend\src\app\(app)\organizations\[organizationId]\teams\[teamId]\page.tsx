"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { useTeam, useTeamMembers, useAddTeamMember, useUpdateTeamMember, useRemoveTeamMember } from "@/hooks/teams";
import { useQuery } from "@tanstack/react-query";
import { organizationService } from "@/services/organization-service";
import { backendApiClient } from "@/services/backend-api-client";

import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Users, FolderKanban, Pencil } from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import { Team<PERSON><PERSON>berList, AddTeamMemberDialog } from "@/components/teams";
import { TeamRole } from "@/types/role";

export default function TeamDetailsPage() {
  const params = useParams();
  // const router = useRouter(); // Uncomment when navigation is needed

  // Ensure params is not null
  if (!params) {
    return <div>Loading...</div>;
  }

  const organizationId = params.organizationId as string;
  const teamId = params.teamId as string;

  const [activeTab, setActiveTab] = useState("members");
  const [isAddMemberDialogOpen, setIsAddMemberDialogOpen] = useState(false);

  // Fetch team
  const { data: team, isLoading: isTeamLoading } = useTeam(teamId);

  // Fetch team members
  const { data: membersData, isLoading: isMembersLoading } = useTeamMembers(teamId);

  // Team member mutations
  const addTeamMemberMutation = useAddTeamMember();
  const updateTeamMemberMutation = useUpdateTeamMember();
  const removeTeamMemberMutation = useRemoveTeamMember();

  // Fetch organization members
  const { data: organizationMembersData } = useQuery({
    queryKey: ['organization', organizationId, 'members'],
    queryFn: async () => {
      const response = await organizationService.getMembers(organizationId);

      // Fetch additional user details for each member using backend API client
      const membersWithDetails = await Promise.all(
        response.map(async (member: any) => {
          try {
            // Use backend API client for consistent user detail fetching
            const userData = await backendApiClient.request(`/users/${member.userId}/profile`);

            return {
              ...member,
              name: userData.displayName || `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || userData.email,
              email: userData.email,
              avatarUrl: userData.avatar || userData.avatarUrl,
              firstName: userData.firstName,
              lastName: userData.lastName,
              status: userData.status,
              lastLoginAt: userData.lastLoginAt,
              roles: userData.roles || []
            };
          } catch (error) {
            console.error(`Failed to fetch details for user ${member.userId}`, error);
            // Fallback to basic member data if user fetch fails
            return {
              ...member,
              name: member.displayName || member.name || 'Unknown User',
              email: member.email || '',
              avatarUrl: member.avatarUrl || member.avatar,
              status: 'unknown'
            };
          }
        })
      );

      return membersWithDetails;
    },
    enabled: !!organizationId
  });

  // Handle add member
  const handleAddMember = (userId: string, role: TeamRole) => {
    addTeamMemberMutation.mutate(
      {
        teamId,
        data: {
          userId,
          role,
        },
      },
      {
        onSuccess: () => {
          setIsAddMemberDialogOpen(false);
        },
      }
    );
  };

  // Handle update member
  const handleUpdateMember = (userId: string, role: TeamRole) => {
    updateTeamMemberMutation.mutate({
      teamId,
      memberId: userId,
      data: {
        role,
      },
    });
  };

  // Handle remove member
  const handleRemoveMember = (userId: string) => {
    removeTeamMemberMutation.mutate({
      teamId,
      memberId: userId,
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href={`/organizations/${organizationId}/teams`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">
          {isTeamLoading ? <Skeleton className="h-9 w-40" /> : team?.name}
        </h1>
      </div>

      {isTeamLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      ) : team ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Team Details</CardTitle>
                <CardDescription>Basic information about the team</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium">Status</h3>
                  <Badge variant={(team as any).isActive ? "default" : "secondary"}>
                    {(team as any).isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
                {team.description && (
                  <div>
                    <h3 className="text-sm font-medium">Description</h3>
                    <p className="text-sm text-muted-foreground">{team.description}</p>
                  </div>
                )}
                <div>
                  <h3 className="text-sm font-medium">Created</h3>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(team.createdAt), "PPP")} ({formatDistanceToNow(new Date(team.createdAt))} ago)
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Last Updated</h3>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(team.updatedAt), "PPP")} ({formatDistanceToNow(new Date(team.updatedAt))} ago)
                  </p>
                </div>
                <div className="pt-4">
                  <Button asChild variant="outline" className="w-full">
                    <Link href={`/organizations/${organizationId}/teams/${teamId}/edit`}>
                      <Pencil className="mr-2 h-4 w-4" />
                      Edit Team
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Team Management</CardTitle>
                <CardDescription>Manage team members and projects</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="members" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-2 w-full">
                    <TabsTrigger value="members">
                      <Users className="mr-2 h-4 w-4" />
                      Members
                    </TabsTrigger>
                    <TabsTrigger value="projects">
                      <FolderKanban className="mr-2 h-4 w-4" />
                      Projects
                    </TabsTrigger>
                  </TabsList>
                  <div className="mt-6">
                    <TabsContent value="members">
                      <TeamMemberList
                        organizationId={organizationId}
                        teamId={teamId}
                        members={(membersData || []).map((member: any) => ({
                          ...member,
                          permissions: member.permissions || [],
                          addedAt: member.addedAt || member.joinedAt || new Date().toISOString(),
                          isActive: member.isActive !== undefined ? member.isActive : true
                        }))}
                        isLoading={isMembersLoading}
                        onAddMember={() => setIsAddMemberDialogOpen(true)}
                        onUpdateMember={handleUpdateMember}
                        onRemoveMember={handleRemoveMember}
                      />
                    </TabsContent>
                    <TabsContent value="projects">
                      <div className="text-center py-8">
                        <h3 className="text-lg font-medium">Projects Coming Soon</h3>
                        <p className="text-sm text-muted-foreground mt-2">
                          Project management for teams will be available soon.
                        </p>
                      </div>
                    </TabsContent>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Add Member Dialog */}
          <AddTeamMemberDialog
            isOpen={isAddMemberDialogOpen}
            onClose={() => setIsAddMemberDialogOpen(false)}
            onAddMember={handleAddMember}
            organizationMembers={organizationMembersData?.map((member: any) => ({
              id: member.userId,
              name: member.name || 'Unknown User',
              email: member.email || '',
              avatarUrl: member.avatarUrl
            })) || []}
            isSubmitting={addTeamMemberMutation.isPending}
          />
        </>
      ) : (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold">Team not found</h2>
          <p className="text-muted-foreground mt-2">
            The team you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button asChild className="mt-6">
            <Link href={`/organizations/${organizationId}/teams`}>
              Go back to teams
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
