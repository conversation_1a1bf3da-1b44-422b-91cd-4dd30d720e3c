/**
 * Embeddings Hooks
 * React hooks for AI embeddings using Cohere and Azure AI Search
 */

import { useCallback } from 'react'
import { useAIStore, useStartAIOperation, useAILoading, useAIError } from '@/stores/ai-store'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface EmbeddingRequest {
  text: string | string[]
  model?: 'cohere-embed' | 'auto'  // Removed OpenAI model, use Cohere from Azure AI Foundry
  organizationId: string
  projectId?: string
  metadata?: Record<string, any>
}

export interface EmbeddingResponse {
  id: string
  embeddings: number[][]
  dimensions: number
  model: string
  tokensUsed: number
  processingTime: number
  metadata?: Record<string, any>
  createdAt: string
}

export interface SimilaritySearchRequest {
  query: string
  organizationId: string
  projectId?: string
  filters?: {
    documentIds?: string[]
    tags?: string[]
    dateRange?: { start: string; end: string }
    contentType?: string[]
  }
  options?: {
    maxResults?: number
    minSimilarity?: number
    includeMetadata?: boolean
    includeContent?: boolean
    rerank?: boolean
  }
}

export interface SimilaritySearchResult {
  id: string
  documentId: string
  content: string
  similarity: number
  metadata: {
    title?: string
    author?: string
    createdAt: string
    tags?: string[]
    section?: string
    pageNumber?: number
  }
  embedding?: number[]
}

export interface VectorIndexRequest {
  documentId: string
  content: string
  metadata?: Record<string, any>
  organizationId: string
  projectId?: string
  chunkSize?: number
  overlapSize?: number
}

export interface VectorIndexResponse {
  id: string
  documentId: string
  chunks: Array<{
    id: string
    content: string
    embedding: number[]
    metadata: Record<string, any>
    position: { start: number; end: number }
  }>
  totalChunks: number
  processingTime: number
  model: string
}

export interface EmbeddingAnalytics {
  totalEmbeddings: number
  totalTokensUsed: number
  averageProcessingTime: number
  modelUsage: Record<string, number>
  dailyUsage: Array<{
    date: string
    count: number
    tokensUsed: number
  }>
  topQueries: Array<{
    query: string
    count: number
    avgSimilarity: number
  }>
}

/**
 * Hook to generate embeddings
 */
export function useGenerateEmbeddings() {
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  const mutate = useCallback(async (data: EmbeddingRequest) => {
    try {
      const result = await startAIOperation({
        type: 'EMBEDDING_GENERATION',
        parameters: {
          text: data.text,
          model: data.model || 'cohere-embed'
        },
        organizationId: data.organizationId,
        projectId: data.projectId
      })

      toast({
        title: 'Embeddings generated',
        description: 'Text embeddings have been generated successfully.',
      })

      return result.results as EmbeddingResponse
    } catch (error: any) {
      toast({
        title: 'Error generating embeddings',
        description: error.message || 'Failed to generate embeddings',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to perform similarity search
 */
export function useSimilaritySearch() {
  const { toast } = useToast()
  const loading = useAILoading()
  const error = useAIError()

  const mutate = useCallback(async (data: SimilaritySearchRequest) => {
    try {
      const results = await backendApiClient.request<SimilaritySearchResult[]>('/ai/embeddings/search', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'RAG_QUERY',
          ragRequest: {
            operation: 'SEARCH_KNOWLEDGE',
            searchData: {
              query: data.query,
              maxResults: data.options?.maxResults || 10,
              minSimilarity: data.options?.minSimilarity || 0.7,
              includeMetadata: data.options?.includeMetadata !== false,
              includeContent: data.options?.includeContent !== false,
              filters: data.filters
            }
          },
          organizationId: data.organizationId,
          projectId: data.projectId
        })
      })

      toast({
        title: 'Search completed',
        description: `Found ${results.length} similar documents.`,
      })

      return results
    } catch (error: any) {
      toast({
        title: 'Error in similarity search',
        description: 'There was a problem performing the similarity search. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to index document for vector search
 */
export function useIndexDocument() {
  const { toast } = useToast()
  const loading = useAILoading()
  const error = useAIError()

  const mutate = useCallback(async (data: VectorIndexRequest) => {
    try {
      const result = await backendApiClient.request<VectorIndexResponse>('/rag/query', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'RAG_QUERY',
          ragRequest: {
            operation: 'INDEX_DOCUMENT',
            indexData: {
              documentId: data.documentId,
              content: data.content,
              metadata: data.metadata,
              chunkSize: data.chunkSize || 1000,
              overlapSize: data.overlapSize || 200
            }
          },
          organizationId: data.organizationId,
          projectId: data.projectId
        })
      })

      toast({
        title: 'Document indexed',
        description: `Document has been indexed with ${result.totalChunks} chunks.`,
      })

      return result
    } catch (error: any) {
      toast({
        title: 'Error indexing document',
        description: 'There was a problem indexing the document. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to get embedding history
 */
export function useEmbeddingHistory(organizationId: string, params?: {
  projectId?: string
  model?: string
  page?: number
  pageSize?: number
  dateRange?: { start: string; end: string }
}) {
  const listOperations = useAIStore((state) => state.listOperations)
  const loading = useAILoading()
  const error = useAIError()

  const fetchHistory = useCallback(async () => {
    if (!organizationId) return null

    return await listOperations({
      organizationId,
      type: 'EMBEDDING_GENERATION',
      ...params
    })
  }, [organizationId, params, listOperations])

  return {
    data: null, // This would need to be managed in the store for caching
    isLoading: loading,
    error,
    refetch: fetchHistory
  }
}

/**
 * Hook to get vector index status
 */
export function useVectorIndexStatus(params?: {
  organizationId?: string
  projectId?: string
  documentId?: string
}) {
  const loading = useAILoading()
  const error = useAIError()

  const fetchStatus = useCallback(async () => {
    return await backendApiClient.request('/ai/embeddings/index/status', {
      params
    })
  }, [params])

  return {
    data: null, // This would need to be managed in the store for caching
    isLoading: loading,
    error,
    refetch: fetchStatus
  }
}

/**
 * Hook to get embedding analytics
 */
export function useEmbeddingAnalytics(organizationId: string, params?: {
  projectId?: string
  dateRange?: { start: string; end: string }
}) {
  const loading = useAILoading()
  const error = useAIError()

  const fetchAnalytics = useCallback(async () => {
    if (!organizationId) return null

    return await backendApiClient.request<EmbeddingAnalytics>('/ai/analytics', {
      params: {
        organizationId,
        operationType: 'EMBEDDING_GENERATION',
        ...params
      }
    })
  }, [organizationId, params])

  return {
    data: null, // This would need to be managed in the store for caching
    isLoading: loading,
    error,
    refetch: fetchAnalytics
  }
}

/**
 * Hook to batch index multiple documents
 */
export function useBatchIndexDocuments() {
  const { toast } = useToast()
  const loading = useAILoading()
  const error = useAIError()

  const mutate = useCallback(async ({
    documents,
    organizationId,
    projectId
  }: {
    documents: Array<{
      documentId: string
      content: string
      metadata?: Record<string, any>
    }>
    organizationId: string
    projectId?: string
  }) => {
    try {
      const result = await backendApiClient.request('/ai/embeddings/batch-index', {
        method: 'POST',
        body: JSON.stringify({
          documents,
          organizationId,
          projectId
        })
      })

      toast({
        title: 'Batch indexing started',
        description: `Indexing ${result.totalDocuments} documents in the background.`,
      })

      return result
    } catch (error: any) {
      toast({
        title: 'Error in batch indexing',
        description: 'There was a problem starting the batch indexing. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to find similar documents
 */
export function useFindSimilarDocuments() {
  const { toast } = useToast()
  const loading = useAILoading()
  const error = useAIError()

  const mutate = useCallback(async ({
    documentId,
    maxResults = 5,
    organizationId
  }: {
    documentId: string
    maxResults?: number
    organizationId: string
  }) => {
    try {
      const results = await backendApiClient.request<SimilaritySearchResult[]>('/rag/query', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'RAG_QUERY',
          ragRequest: {
            operation: 'SIMILARITY_SEARCH',
            searchData: {
              documentId,
              maxResults,
              threshold: 0.7
            }
          },
          organizationId
        })
      })

      toast({
        title: 'Similar documents found',
        description: `Found ${results.length} similar documents.`,
      })

      return results
    } catch (error: any) {
      toast({
        title: 'Error finding similar documents',
        description: 'There was a problem finding similar documents. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to delete embeddings
 */
export function useDeleteEmbeddings() {
  const { toast } = useToast()
  const loading = useAILoading()
  const error = useAIError()

  const mutate = useCallback(async ({
    documentId,
    organizationId
  }: {
    documentId: string
    organizationId: string
  }) => {
    try {
      await backendApiClient.request(`/ai/embeddings/documents/${documentId}`, {
        method: 'DELETE',
        params: { organizationId }
      })

      toast({
        title: 'Embeddings deleted',
        description: 'Document embeddings have been deleted successfully.',
      })

      return documentId
    } catch (error: any) {
      toast({
        title: 'Error deleting embeddings',
        description: 'There was a problem deleting the embeddings. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to get available embedding models
 */
export function useEmbeddingModels() {
  const loading = useAILoading()
  const error = useAIError()

  const fetchModels = useCallback(async () => {
    return await backendApiClient.request('/ai/embeddings/models')
  }, [])

  return {
    data: null, // This would need to be managed in the store for caching
    isLoading: loading,
    error,
    refetch: fetchModels
  }
}
