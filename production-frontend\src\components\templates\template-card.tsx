"use client";

import { useState } from "react";
import Link from "next/link";
import { formatDistanceToNow } from "date-fns";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileStack, 
  Copy, 
  MoreHorizontal, 
  Pencil, 
  Trash, 
  FileText, 
  Clock, 
  Calendar, 
  Tag,
  Share,
  Eye
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Template, Template<PERSON>tatus, TemplateType } from "@/services/template-service";
import { cn } from "@/lib/utils";

interface TemplateCardProps {
  template: Template;
  onDelete?: (id: string) => void;
  onDuplicate?: (id: string) => void;
  onShare?: (id: string) => void;
  className?: string;
}

export function TemplateCard({ 
  template, 
  onDelete, 
  onDuplicate, 
  onShare,
  className 
}: TemplateCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  // Format status badge
  const getStatusBadge = (status: TemplateStatus) => {
    switch (status) {
      case TemplateStatus.PUBLISHED:
        return { variant: "default" as const, label: "Published" };
      case TemplateStatus.DRAFT:
        return { variant: "outline" as const, label: "Draft" };
      case TemplateStatus.ARCHIVED:
        return { variant: "secondary" as const, label: "Archived" };
      default:
        return { variant: "outline" as const, label: status };
    }
  };
  
  // Format type badge
  const getTypeBadge = (type: TemplateType) => {
    switch (type) {
      case TemplateType.DOCUMENT:
        return { variant: "default" as const, label: "Document" };
      case TemplateType.FORM:
        return { variant: "secondary" as const, label: "Form" };
      case TemplateType.EMAIL:
        return { variant: "outline" as const, label: "Email" };
      case TemplateType.WORKFLOW:
        return { variant: "destructive" as const, label: "Workflow" };
      case TemplateType.PROJECT:
        return { variant: "success" as const, label: "Project" };
      default:
        return { variant: "outline" as const, label: type };
    }
  };
  
  const statusBadge = getStatusBadge(template.status);
  const typeBadge = getTypeBadge(template.type);
  
  // Handle delete
  const handleDelete = () => {
    setIsDeleteDialogOpen(false);
    if (onDelete) {
      onDelete(template.id);
    }
  };
  
  // Handle duplicate
  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate(template.id);
    }
  };
  
  // Handle share
  const handleShare = () => {
    if (onShare) {
      onShare(template.id);
    }
  };
  
  return (
    <>
      <Card className={cn("overflow-hidden hover:shadow-md transition-shadow", className)}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <CardTitle className="text-lg flex items-center gap-2">
              <FileStack className="h-5 w-5 text-primary" />
              {template.name}
            </CardTitle>
            <Badge variant={statusBadge.variant}>{statusBadge.label}</Badge>
          </div>
          <CardDescription className="line-clamp-2">
            {template.description || "No description provided"}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="pb-2">
          <div className="flex flex-wrap gap-1 mb-3">
            <Badge variant={typeBadge.variant} className="mr-1">{typeBadge.label}</Badge>
            {template.tags && template.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">{tag}</Badge>
            ))}
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span>Version {template.version}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Created {formatDistanceToNow(new Date(template.createdAt))} ago</span>
            </div>
            <div className="flex items-center gap-2 col-span-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>Updated {formatDistanceToNow(new Date(template.updatedAt))} ago</span>
            </div>
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-between">
          <Button asChild variant="default">
            <Link href={`/templates/${template.id}`}>
              <Eye className="mr-2 h-4 w-4" />
              View
            </Link>
          </Button>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="icon"
              onClick={handleShare}
            >
              <Share className="h-4 w-4" />
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href={`/templates/${template.id}/edit`}>
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDuplicate}>
                  <Copy className="mr-2 h-4 w-4" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardFooter>
      </Card>
      
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the template "{template.name}".
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
