"use client";

import React from "react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export interface LoadingStateProps {
  title?: string;
  description?: string;
  className?: string;
  variant?: "default" | "inline" | "skeleton" | "card" | "spinner";
  count?: number;
  height?: number | string;
  width?: number | string;
}

/**
 * A reusable component for displaying loading states
 */
export function LoadingState({
  title = "Loading",
  description = "Please wait while we load the data...",
  className,
  variant = "default",
  count = 3,
  height = "1rem",
  width = "100%",
}: LoadingStateProps) {
  // Inline variant
  if (variant === "inline") {
    return (
      <div className={cn("flex items-center gap-2 text-muted-foreground", className)}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span>{title}</span>
      </div>
    );
  }

  // Spinner variant
  if (variant === "spinner") {
    return (
      <div className={cn("flex flex-col items-center justify-center p-4", className)}>
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
        {title && <p className="text-sm font-medium">{title}</p>}
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
      </div>
    );
  }

  // Skeleton variant
  if (variant === "skeleton") {
    return (
      <div className={cn("space-y-2", className)}>
        {Array.from({ length: count }).map((_, i) => (
          <Skeleton 
            key={i} 
            className={cn("w-full", typeof height === 'string' ? height : `${height}px`)}
            style={{ 
              height: typeof height === 'string' ? height : `${height}px`,
              width: typeof width === 'string' ? width : `${width}px`
            }}
          />
        ))}
      </div>
    );
  }

  // Card variant
  if (variant === "card") {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {Array.from({ length: count }).map((_, i) => (
              <Skeleton key={i} className="h-4 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <div className={cn("flex flex-col items-center justify-center p-6", className)}>
      <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
      <h3 className="text-lg font-medium">{title}</h3>
      <p className="text-sm text-muted-foreground text-center mt-1">{description}</p>
    </div>
  );
}

/**
 * Skeleton loader for a card
 */
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("rounded-lg border bg-card text-card-foreground shadow-sm", className)}>
      <div className="p-6 space-y-4">
        <Skeleton className="h-8 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>
      <div className="p-6 pt-0 flex justify-end">
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  );
}

/**
 * Skeleton loader for a table
 */
export function TableSkeleton({ 
  rows = 5, 
  columns = 4,
  className 
}: { 
  rows?: number;
  columns?: number;
  className?: string;
}) {
  return (
    <div className={cn("w-full", className)}>
      {/* Header */}
      <div className="flex border-b pb-4">
        {Array.from({ length: columns }).map((_, i) => (
          <div key={i} className="flex-1 pr-4">
            <Skeleton className="h-6 w-full" />
          </div>
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex py-4 border-b">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="flex-1 pr-4">
              <Skeleton className="h-5 w-full" />
            </div>
          ))}
        </div>
      ))}
    </div>
  );
}
