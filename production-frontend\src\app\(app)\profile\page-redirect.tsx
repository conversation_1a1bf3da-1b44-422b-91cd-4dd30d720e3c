'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, User } from 'lucide-react';

export default function ProfileRedirectPage() {
  const router = useRouter();

  // Redirect to settings page with profile tab active
  useEffect(() => {
    const redirectTimer = setTimeout(() => {
      router.push('/settings?tab=profile');
    }, 1500);
    
    return () => clearTimeout(redirectTimer);
  }, [router]);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Profile</h1>
        <p className="text-muted-foreground">
          Profile settings have moved to the unified settings page
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profile Has Moved</CardTitle>
          <CardDescription>
            Profile settings are now part of the unified settings page
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center py-6">
            <User className="h-16 w-16 text-muted-foreground" />
          </div>
          
          <div className="space-y-4">
            <p className="text-center">You'll be redirected automatically in a moment</p>
            <Button 
              className="w-full"
              onClick={() => router.push('/settings?tab=profile')}
            >
              Go to Settings
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
