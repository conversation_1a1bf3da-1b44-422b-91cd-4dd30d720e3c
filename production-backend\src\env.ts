/**
 * Environment configuration for Azure Functions
 * Uses Azure Identity for authentication instead of connection strings
 */

import * as dotenv from 'dotenv';

// Load environment variables from .env file in development
if (process.env.NODE_ENV !== 'production') {
  dotenv.config();
}

// Azure resource names and endpoints (no secrets)
const requiredAzureResources = [
  'COSMOS_DB_ENDPOINT',
  'COSMOS_DB_DATABASE',
  'AZURE_SERVICE_BUS_NAMESPACE',
  'AZURE_REDIS_HOST'
];

// Services that still require connection strings/API keys
const requiredConnectionStringEnvVars = [
  'AZURE_STORAGE_CONNECTION_STRING',
  'POSTMARK_SERVER_TOKEN'
];

// Check for required Azure resource configurations
const missingAzureResources = requiredAzureResources.filter(envVar => !process.env[envVar]);
const missingConnectionStringVars = requiredConnectionStringEnvVars.filter(envVar => !process.env[envVar]);

if (missingAzureResources.length > 0) {
  console.warn(`Warning: Missing Azure resource configurations: ${missingAzureResources.join(', ')}`);
  console.warn('Azure Identity will be used for authentication to these services.');
}

if (missingConnectionStringVars.length > 0) {
  console.warn(`Warning: Missing connection string/API key variables: ${missingConnectionStringVars.join(', ')}`);
  console.warn('These services require connection strings or API keys and may not work properly.');
}

// Export environment configuration with Azure Identity support
export const config = {
  // Database - Using Azure Identity
  cosmosDb: {
    endpoint: process.env.COSMOS_DB_ENDPOINT || '',
    database: process.env.COSMOS_DB_DATABASE || 'hepz-db',
    // No key - will use Azure Identity
  },

  // Storage - Keep using connection string (Azure Identity not supported)
  storage: {
    connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING || ''
  },

  // AI Services - Using Azure Identity
  ai: {
    // Azure AI Foundry Models
    deepSeekR1: {
      endpoint: process.env.AI_DEEPSEEK_R1_ENDPOINT || 'https://DeepSeek-R1-pvcnl.eastus.models.ai.azure.com',
      deploymentName: process.env.AI_DEEPSEEK_R1_DEPLOYMENT_NAME || 'deepseek-r1-chat',
      enabled: process.env.AI_DEEPSEEK_R1_ENABLED === 'true',
      // No key - will use Azure Identity
    },
    llama: {
      endpoint: process.env.AI_LLAMA_ENDPOINT || 'https://Llama-3-3-70B-Instruct-pvcnl.eastus.models.ai.azure.com',
      deploymentName: process.env.AI_LLAMA_DEPLOYMENT_NAME || 'Llama-3-3-70B-Instruct',
      enabled: process.env.AI_LLAMA_ENABLED === 'true',
      // No key - will use Azure Identity
    },
    // Cohere Embeddings from Azure AI Foundry
    cohere: {
      endpoint: process.env.AI_COHERE_ENDPOINT || 'https://Cohere-embed-v3-multilingual-nxn.eastus.models.ai.azure.com',
      deploymentName: process.env.AI_COHERE_DEPLOYMENT_NAME || 'Cohere-embed-v3-multilingual',
      embeddingDimensions: parseInt(process.env.AI_COHERE_EMBEDDING_DIMENSIONS || '1024'),
      enabled: process.env.AI_COHERE_ENABLED === 'true',
      // No key - will use Azure Identity
    },
    // Legacy Azure OpenAI (for backward compatibility)
    azureOpenAI: {
      endpoint: process.env.AZURE_OPENAI_ENDPOINT || '',
      deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME || 'gpt-4',
      // No key - will use Azure Identity
    },
    documentIntelligence: {
      endpoint: process.env.AI_DOCUMENT_INTELLIGENCE_ENDPOINT || '',
      // No key - will use Azure Identity
    },
    search: {
      endpoint: process.env.AZURE_SEARCH_ENDPOINT || '',
      indexName: process.env.AZURE_SEARCH_INDEX_NAME || 'documents',
      vectorIndexName: process.env.AZURE_SEARCH_VECTOR_INDEX_NAME || 'embeddings',
      embeddingDimensions: parseInt(process.env.AZURE_SEARCH_EMBEDDING_DIMENSIONS || '1024'), // Cohere embed-v3-multilingual dimensions
      vectorEnabled: process.env.SEARCH_VECTOR_ENABLED === 'true',
      semanticEnabled: process.env.SEARCH_SEMANTIC_ENABLED === 'true',
      // No key - will use Azure Identity
    }
  },

  // Service Bus - Using Azure Identity
  serviceBus: {
    namespace: process.env.AZURE_SERVICE_BUS_NAMESPACE || 'hepzbackend',
    endpoint: process.env.AZURE_SERVICE_BUS_NAMESPACE
      ? `https://${process.env.AZURE_SERVICE_BUS_NAMESPACE}.servicebus.windows.net`
      : 'https://hepzbackend.servicebus.windows.net',
    // No connection string - will use Azure Identity
  },

  // SignalR - Using Azure Identity
  signalR: {
    endpoint: process.env.AZURE_SIGNALR_ENDPOINT || 'https://hepztech.service.signalr.net',
    hubName: process.env.SIGNALR_HUB_NAME || 'hepztech',
    // No connection string - will use Azure Identity
  },

  // Event Grid - Using Azure Identity
  eventGrid: {
    endpoint: process.env.EVENT_GRID_TOPIC_ENDPOINT || 'https://hepzeg.eastus-1.eventgrid.azure.net/api/events',
    topicName: process.env.EVENT_GRID_TOPIC_NAME || 'hepzeg',
    // No access key - will use Azure Identity
  },

  // Redis - Using Azure Identity (already implemented)
  redis: {
    host: process.env.AZURE_REDIS_HOST || 'hepzbackend.eastus.redis.azure.net',
    port: parseInt(process.env.AZURE_REDIS_PORT || '10000'),
    database: parseInt(process.env.REDIS_DATABASE || '0'),
    // No connection string - will use Azure Identity
  },

  // Non-Azure services (still use API keys)
  email: {
    postmarkServerToken: process.env.POSTMARK_SERVER_TOKEN || ''
  },

  lemonSqueezy: {
    apiKey: process.env.LEMONSQUEEZY_API_KEY || '',
    storeId: process.env.LEMONSQUEEZY_STORE_ID || '80076',
    webhookSecret: process.env.LEMONSQUEEZY_WEBHOOK_SECRET || ''
  },

  // Application
  app: {
    environment: process.env.NODE_ENV || 'development',
    version: process.env.VERSION || '1.0.0',
    logLevel: process.env.LOG_LEVEL || 'info'
  },

  // Azure Identity configuration
  azure: {
    // Managed Identity Client ID (optional - for user-assigned managed identity)
    managedIdentityClientId: process.env.AZURE_CLIENT_ID || '',
    // Tenant ID (optional - for service principal auth in development)
    tenantId: process.env.AZURE_TENANT_ID || '',
    // Subscription ID (for resource management)
    subscriptionId: process.env.AZURE_SUBSCRIPTION_ID || ''
  },

  // Azure PKI Services
  pki: {
    // Azure Key Vault (Primary PKI Service)
    keyVault: {
      url: process.env.AZURE_KEYVAULT_URL || '',
      // No access key - will use Azure Identity
    }
  }
};

export default config;
