/**
 * Admin Layout
 * Layout component for admin pages with navigation and access control
 */
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { useAuth } from '@/hooks';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  AlertCircle,
  BarChart3,
  Bell,
  ChevronDown,
  ClipboardList,
  FileText,
  Layers,
  LayoutDashboard,
  LogOut,
  Menu,
  Settings,
  Shield,
  Users,
  Zap,
} from 'lucide-react';

// Navigation items
const navigationItems = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: <LayoutDashboard className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Users',
    href: '/admin/users',
    icon: <Users className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Organizations',
    href: '/admin/organizations',
    icon: <Layers className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Documents',
    href: '/admin/documents',
    icon: <FileText className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Security',
    href: '/admin/security',
    icon: <Shield className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Event Monitoring',
    href: '/admin/events',
    icon: <Zap className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Analytics',
    href: '/admin/analytics',
    icon: <BarChart3 className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'System Logs',
    href: '/admin/logs',
    icon: <ClipboardList className="h-5 w-5" />,
    requiredRole: 'admin',
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: <Settings className="h-5 w-5" />,
    requiredRole: 'admin',
  },
];

// Admin layout props
interface AdminLayoutProps {
  children: React.ReactNode;
}

// Admin layout component
export function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { user, isLoading, logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if user has admin role
  const isAdmin = user?.roles?.some(role =>
    typeof role === 'string' ? role === 'admin' : role.name === 'admin'
  );

  // Handle unauthorized access
  if (!isLoading && !isAdmin) {
    // Show unauthorized toast
    toast({
      title: 'Unauthorized',
      description: 'You do not have permission to access this page.',
      variant: 'destructive',
    });

    // Redirect to home page
    router.push('/');
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-16 items-center justify-between py-4">
          <div className="flex items-center gap-4">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild className="lg:hidden">
                <Button variant="ghost" size="icon">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-64">
                <SheetHeader className="mb-4">
                  <SheetTitle>Admin Panel</SheetTitle>
                  <SheetDescription>
                    Manage your application
                  </SheetDescription>
                </SheetHeader>
                <nav className="flex flex-col gap-2">
                  {navigationItems.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium",
                        router.pathname === item.href
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted"
                      )}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.icon}
                      {item.title}
                    </Link>
                  ))}
                </nav>
              </SheetContent>
            </Sheet>
            <Link href="/admin" className="flex items-center gap-2 font-semibold">
              <Shield className="h-5 w-5" />
              <span className="hidden md:inline-block">Admin Panel</span>
            </Link>
          </div>

          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/admin/notifications">
                <Bell className="h-5 w-5" />
                <span className="sr-only">Notifications</span>
              </Link>
            </Button>

            {isLoading ? (
              <Skeleton className="h-8 w-8 rounded-full" />
            ) : (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2">
                    <div className="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
                      {user?.name?.charAt(0) || 'U'}
                    </div>
                    <span className="hidden md:inline-block">{user?.name || 'User'}</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuItem asChild>
                    <Link href="/profile">Profile</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings">Settings</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/">Back to App</Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => logout()}>
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </div>
      </header>

      {/* Main content */}
      <div className="flex-1 flex">
        {/* Sidebar (desktop) */}
        <aside className="hidden lg:block w-64 border-r bg-background">
          <nav className="flex flex-col gap-2 p-4">
            {navigationItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium",
                  router.pathname === item.href || router.pathname.startsWith(`${item.href}/`)
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                )}
              >
                {item.icon}
                {item.title}
              </Link>
            ))}
          </nav>
        </aside>

        {/* Content */}
        <main className="flex-1">
          {isLoading ? (
            <div className="container py-6 space-y-6">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-64 w-full" />
            </div>
          ) : !isAdmin ? (
            <div className="container py-6">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Unauthorized</AlertTitle>
                <AlertDescription>
                  You do not have permission to access this page.
                </AlertDescription>
              </Alert>
            </div>
          ) : (
            children
          )}
        </main>
      </div>
    </div>
  );
}
