/**
 * Auth Store Hook
 * Provides easy access to authentication store
 */

import { useAuthStore as useAuthStoreBase } from '../stores/auth-store'

// Re-export the main store hook
export const useAuthStore = useAuthStoreBase

// Selector hooks for specific auth data
export const useAuth = () => {
  const { user, isAuthenticated, loading, error } = useAuthStore()
  
  return {
    user,
    isAuthenticated,
    loading,
    error,
  }
}

export const useAuthUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.loading)
export const useAuthError = () => useAuthStore((state) => state.error)

// Action hooks
export const useLogin = () => useAuthStore((state) => state.login)
export const useLogout = () => useAuthStore((state) => state.logout)
export const useRefreshAuth = () => useAuthStore((state) => state.refreshAuth)
export const useUpdateProfile = () => useAuthStore((state) => state.updateProfile)
