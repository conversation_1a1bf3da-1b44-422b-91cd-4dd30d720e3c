/**
 * AI Operations Management Hooks
 * React hooks for managing AI operations, monitoring, and analytics
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface AIOperation {
  id: string
  operationType: AIOperationType
  status: AIOperationStatus
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  parameters: Record<string, any>
  organizationId: string
  projectId?: string
  documentId?: string
  modelId?: string
  batchId?: string
  results?: AIOperationResults
  progress: {
    percentage: number
    currentStep: string
    totalSteps: number
    completedSteps: number
    estimatedTimeRemaining?: number
  }
  metadata: {
    createdBy: string
    tenantId: string
    callbackUrl?: string
    tags?: string[]
  }
  estimatedCompletionTime?: string
  actualCompletionTime?: string
  createdAt: string
  updatedAt: string
  error?: string
}

export type AIOperationType = 
  | 'DOCUMENT_ANALYSIS'
  | 'FORM_PROCESSING'
  | 'SMART_FORM_PROCESSING'
  | 'BATCH_PROCESSING'
  | 'MODEL_TRAINING'
  | 'MODEL_DEPLOYMENT'
  | 'CONTENT_GENERATION'
  | 'CLASSIFICATION'
  | 'EXTRACTION'
  | 'ORCHESTRATION'
  | 'TEXT_GENERATION'
  | 'IMAGE_ANALYSIS'
  | 'CHAT_COMPLETION'
  | 'EMBEDDING_GENERATION'
  | 'RAG_QUERY'

export type AIOperationStatus = 
  | 'PENDING'
  | 'QUEUED'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'PAUSED'

export interface AIOperationResults {
  success: boolean
  data?: any
  metrics?: {
    processingTime: number
    tokensUsed?: number
    confidence?: number
    accuracy?: number
  }
  outputs?: Array<{
    type: string
    content: any
    metadata?: Record<string, any>
  }>
  errors?: string[]
  warnings?: string[]
}

export interface CreateAIOperationRequest {
  operationType: AIOperationType
  parameters: Record<string, any>
  organizationId: string
  projectId?: string
  documentId?: string
  priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  callbackUrl?: string
  tags?: string[]
}

export interface AIOperationAnalytics {
  totalOperations: number
  operationsByType: Record<AIOperationType, number>
  operationsByStatus: Record<AIOperationStatus, number>
  averageProcessingTime: number
  successRate: number
  totalTokensUsed: number
  totalCost: number
  dailyStats: Array<{
    date: string
    operations: number
    tokensUsed: number
    cost: number
    avgProcessingTime: number
  }>
  topModels: Array<{
    model: string
    usage: number
    avgConfidence: number
  }>
}

export interface BatchOperation {
  id: string
  name: string
  operationType: AIOperationType
  status: AIOperationStatus
  totalItems: number
  processedItems: number
  failedItems: number
  successRate: number
  organizationId: string
  createdAt: string
  completedAt?: string
  results?: {
    successful: any[]
    failed: any[]
    summary: Record<string, any>
  }
}

/**
 * Hook to get AI operations
 */
export function useAIOperations(params?: {
  organizationId?: string
  projectId?: string
  operationType?: AIOperationType
  status?: AIOperationStatus
  page?: number
  pageSize?: number
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: ['ai-operations', params],
    queryFn: async () => {
      return await backendApiClient.request<AIOperation[]>('/ai/operations/list', {
        params
      })
    },
  })
}

/**
 * Hook to get a specific AI operation
 */
export function useAIOperation(operationId: string) {
  return useQuery({
    queryKey: ['ai-operation', operationId],
    queryFn: async () => {
      return await backendApiClient.request<AIOperation>(`/ai/operations/${operationId}`)
    },
    enabled: !!operationId,
    refetchInterval: 2000, // Refetch every 2 seconds
  })
}

/**
 * Hook to create an AI operation
 */
export function useCreateAIOperation() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateAIOperationRequest) => {
      return await backendApiClient.request<AIOperation>('/ai/operations', {
        method: 'POST',
        body: JSON.stringify({
          operationType: data.operationType,
          parameters: data.parameters,
          organizationId: data.organizationId,
          projectId: data.projectId,
          documentId: data.documentId,
          priority: data.priority || 'NORMAL',
          callbackUrl: data.callbackUrl,
          metadata: {
            tags: data.tags
          }
        })
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['ai-operations'] })
      toast({
        title: 'AI operation started',
        description: `${operation.operationType} operation has been started. ID: ${operation.id}`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error starting AI operation',
        description: 'There was a problem starting the AI operation. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to cancel an AI operation
 */
export function useCancelAIOperation() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (operationId: string) => {
      return await backendApiClient.request(`/ai/operations/${operationId}/cancel`, {
        method: 'POST'
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['ai-operation', operation.id] })
      queryClient.invalidateQueries({ queryKey: ['ai-operations'] })
      toast({
        title: 'Operation cancelled',
        description: 'The AI operation has been cancelled successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error cancelling operation',
        description: 'There was a problem cancelling the AI operation. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to retry a failed AI operation
 */
export function useRetryAIOperation() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (operationId: string) => {
      return await backendApiClient.request(`/ai/operations/${operationId}/retry`, {
        method: 'POST'
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['ai-operation', operation.id] })
      queryClient.invalidateQueries({ queryKey: ['ai-operations'] })
      toast({
        title: 'Operation restarted',
        description: 'The AI operation has been restarted successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error retrying operation',
        description: 'There was a problem retrying the AI operation. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get AI operation analytics
 */
export function useAIOperationAnalytics(organizationId: string, params?: {
  projectId?: string
  dateRange?: { start: string; end: string }
  operationType?: AIOperationType
}) {
  return useQuery({
    queryKey: ['ai-operation-analytics', organizationId, params],
    queryFn: async () => {
      return await backendApiClient.request<AIOperationAnalytics>('/ai/operations/analytics', {
        params: { organizationId, ...params }
      })
    },
    enabled: !!organizationId,
  })
}

/**
 * Hook to get batch operations
 */
export function useBatchOperations(organizationId: string, params?: {
  operationType?: AIOperationType
  status?: AIOperationStatus
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['batch-operations', organizationId, params],
    queryFn: async () => {
      return await backendApiClient.request<BatchOperation[]>('/ai/batch/operations', {
        params: { organizationId, ...params }
      })
    },
    enabled: !!organizationId,
  })
}

/**
 * Hook to create a batch operation
 */
export function useCreateBatchOperation() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: {
      name: string
      operationType: AIOperationType
      items: any[]
      organizationId: string
      projectId?: string
      batchSize?: number
      priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
    }) => {
      return await backendApiClient.request<BatchOperation>('/ai/batch/process', {
        method: 'POST',
        body: JSON.stringify({
          name: data.name,
          operationType: data.operationType,
          items: data.items,
          organizationId: data.organizationId,
          projectId: data.projectId,
          batchSize: data.batchSize || 10,
          priority: data.priority || 'NORMAL'
        })
      })
    },
    onSuccess: (batch) => {
      queryClient.invalidateQueries({ queryKey: ['batch-operations'] })
      toast({
        title: 'Batch operation started',
        description: `Batch operation "${batch.name}" has been started with ${batch.totalItems} items.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error starting batch operation',
        description: 'There was a problem starting the batch operation. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get operation queue status
 */
export function useOperationQueueStatus(organizationId: string) {
  return useQuery({
    queryKey: ['operation-queue-status', organizationId],
    queryFn: async () => {
      return await backendApiClient.request('/ai/operations/queue/status', {
        params: { organizationId }
      })
    },
    enabled: !!organizationId,
    refetchInterval: 5000, // Refresh every 5 seconds
  })
}

/**
 * Hook to get AI model usage statistics
 */
export function useAIModelUsage(organizationId: string, params?: {
  model?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: ['ai-model-usage', organizationId, params],
    queryFn: async () => {
      return await backendApiClient.request('/ai/models/usage', {
        params: { organizationId, ...params }
      })
    },
    enabled: !!organizationId,
  })
}

/**
 * Hook to export operation results
 */
export function useExportOperationResults() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      operationIds, 
      format = 'json',
      organizationId 
    }: { 
      operationIds: string[]
      format?: 'json' | 'csv' | 'excel'
      organizationId: string 
    }) => {
      return await backendApiClient.request('/ai/operations/export', {
        method: 'POST',
        body: JSON.stringify({
          operationIds,
          format,
          organizationId
        })
      })
    },
    onSuccess: (result) => {
      if (result.downloadUrl) {
        window.open(result.downloadUrl, '_blank')
      }
      toast({
        title: 'Export started',
        description: 'Your operation results export is being prepared for download.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error exporting results',
        description: 'There was a problem exporting the operation results. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
