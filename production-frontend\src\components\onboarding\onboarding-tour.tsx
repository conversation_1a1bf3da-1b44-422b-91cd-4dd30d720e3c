'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { X, ChevronLeft, ChevronRight, Play, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TourStep {
  id: string
  title: string
  description: string
  target: string // CSS selector for the element to highlight
  position: 'top' | 'bottom' | 'left' | 'right'
  action?: {
    type: 'click' | 'hover' | 'input'
    element?: string
    value?: string
  }
  optional?: boolean
}

interface OnboardingTourProps {
  steps: TourStep[]
  onComplete: () => void
  onSkip: () => void
  className?: string
}

export function OnboardingTour({ steps, onComplete, onSkip, className }: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isActive, setIsActive] = useState(false)
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set())

  useEffect(() => {
    // Check if user has already completed onboarding
    const hasCompletedOnboarding = localStorage.getItem('onboarding_completed')
    if (!hasCompletedOnboarding) {
      setIsActive(true)
    }
  }, [])

  useEffect(() => {
    if (isActive && steps[currentStep]) {
      highlightElement(steps[currentStep].target)
    }
  }, [currentStep, isActive, steps])

  const highlightElement = (selector: string) => {
    // Remove previous highlights
    document.querySelectorAll('.onboarding-highlight').forEach(el => {
      el.classList.remove('onboarding-highlight')
    })

    // Add highlight to target element
    const element = document.querySelector(selector)
    if (element) {
      element.classList.add('onboarding-highlight')
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, steps[currentStep].id]))
      setCurrentStep(currentStep + 1)
    } else {
      completeTour()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const skipStep = () => {
    if (steps[currentStep].optional) {
      nextStep()
    }
  }

  const completeTour = () => {
    setIsActive(false)
    localStorage.setItem('onboarding_completed', 'true')
    
    // Remove all highlights
    document.querySelectorAll('.onboarding-highlight').forEach(el => {
      el.classList.remove('onboarding-highlight')
    })
    
    onComplete()
  }

  const skipTour = () => {
    setIsActive(false)
    localStorage.setItem('onboarding_completed', 'true')
    
    // Remove all highlights
    document.querySelectorAll('.onboarding-highlight').forEach(el => {
      el.classList.remove('onboarding-highlight')
    })
    
    onSkip()
  }

  const restartTour = () => {
    setCurrentStep(0)
    setCompletedSteps(new Set())
    setIsActive(true)
    localStorage.removeItem('onboarding_completed')
  }

  if (!isActive) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={restartTour}
        className={cn("fixed bottom-4 left-4 z-40", className)}
      >
        <Play className="h-4 w-4 mr-2" />
        Start Tour
      </Button>
    )
  }

  const step = steps[currentStep]
  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-50 pointer-events-none" />
      
      {/* Tour Card */}
      <Card className="fixed bottom-4 right-4 w-80 z-50 shadow-2xl">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="secondary">
                Step {currentStep + 1} of {steps.length}
              </Badge>
              {step.optional && (
                <Badge variant="outline" className="text-xs">
                  Optional
                </Badge>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={skipTour}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <Progress value={progress} className="mt-2" />
          
          <CardTitle className="text-lg">{step.title}</CardTitle>
          <CardDescription>{step.description}</CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Action hint */}
          {step.action && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                {step.action.type === 'click' && '👆 Click the highlighted element'}
                {step.action.type === 'hover' && '🖱️ Hover over the highlighted element'}
                {step.action.type === 'input' && '⌨️ Type in the highlighted field'}
              </p>
            </div>
          )}

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={prevStep}
              disabled={currentStep === 0}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Previous
            </Button>

            <div className="flex items-center gap-2">
              {step.optional && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={skipStep}
                >
                  Skip
                </Button>
              )}
              
              <Button
                size="sm"
                onClick={nextStep}
              >
                {currentStep === steps.length - 1 ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Complete
                  </>
                ) : (
                  <>
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Quick skip option */}
          <div className="text-center">
            <Button
              variant="link"
              size="sm"
              onClick={skipTour}
              className="text-xs text-muted-foreground"
            >
              Skip entire tour
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* CSS for highlighting */}
      <style jsx global>{`
        .onboarding-highlight {
          position: relative;
          z-index: 51;
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
          border-radius: 8px;
          transition: all 0.3s ease;
        }
        
        .onboarding-highlight::after {
          content: '';
          position: absolute;
          inset: -4px;
          border: 2px solid rgb(59, 130, 246);
          border-radius: 8px;
          animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }
      `}</style>
    </>
  )
}

// Predefined tour steps for common flows
export const dashboardTourSteps: TourStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to HEPZ!',
    description: 'Let\'s take a quick tour to get you started with document management and AI-powered collaboration.',
    target: 'main',
    position: 'bottom'
  },
  {
    id: 'global-search',
    title: 'Global Search',
    description: 'Use Cmd/Ctrl + K to quickly search across all your documents, projects, and content.',
    target: '[data-tour="global-search"]',
    position: 'bottom',
    action: { type: 'click' }
  },
  {
    id: 'ai-assistant',
    title: 'AI Assistant',
    description: 'Get help with document analysis, content generation, and answers to your questions.',
    target: '[data-tour="ai-chat"]',
    position: 'bottom',
    action: { type: 'click' }
  },
  {
    id: 'notifications',
    title: 'Smart Notifications',
    description: 'Stay updated with real-time notifications about collaborations and AI operations.',
    target: '[data-tour="notifications"]',
    position: 'bottom',
    optional: true
  },
  {
    id: 'floating-actions',
    title: 'Quick Actions',
    description: 'Use the floating action button for quick access to common tasks.',
    target: '[data-tour="fab"]',
    position: 'left',
    action: { type: 'click' }
  },
  {
    id: 'analytics',
    title: 'Analytics Dashboard',
    description: 'Monitor performance, AI usage, and search analytics in the Analytics tab.',
    target: '[data-value="analytics"]',
    position: 'top',
    optional: true
  }
]

// Hook for managing onboarding state
export function useOnboarding() {
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useState(true)

  useEffect(() => {
    const completed = localStorage.getItem('onboarding_completed')
    setHasCompletedOnboarding(!!completed)
  }, [])

  const markOnboardingComplete = () => {
    localStorage.setItem('onboarding_completed', 'true')
    setHasCompletedOnboarding(true)
  }

  const resetOnboarding = () => {
    localStorage.removeItem('onboarding_completed')
    setHasCompletedOnboarding(false)
  }

  return {
    hasCompletedOnboarding,
    markOnboardingComplete,
    resetOnboarding
  }
}
