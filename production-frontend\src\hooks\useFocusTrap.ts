import { useEffect, useRef, useCallback, useState } from 'react'

/**
 * Focus Trap Hook
 * Traps focus within a container for accessibility
 */

export interface UseFocusTrapOptions {
  enabled?: boolean
  autoFocus?: boolean
  restoreFocus?: boolean
  allowOutsideClick?: boolean
}

export interface UseFocusTrapResult {
  ref: React.RefObject<HTMLElement>
  activate: () => void
  deactivate: () => void
}

export function useFocusTrap(
  options: UseFocusTrapOptions = {}
): UseFocusTrapResult {
  const {
    enabled = true,
    autoFocus = true,
    restoreFocus = true,
    allowOutsideClick = false
  } = options

  const ref = useRef<HTMLElement>(null)
  const previousActiveElement = useRef<Element | null>(null)
  const isActive = useRef(false)

  const getFocusableElements = useCallback((container: HTMLElement): HTMLElement[] => {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      'area[href]',
      'summary',
      'iframe',
      'object',
      'embed',
      'audio[controls]',
      'video[controls]',
      '[contenteditable]:not([contenteditable="false"])',
      '[tabindex]:not([tabindex="-1"])'
    ].join(', ')

    return Array.from(container.querySelectorAll(focusableSelectors))
      .filter(element => {
        const style = window.getComputedStyle(element)
        return style.display !== 'none' && style.visibility !== 'hidden'
      }) as HTMLElement[]
  }, [])

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isActive.current || !ref.current || event.key !== 'Tab') return

    const focusableElements = getFocusableElements(ref.current)
    if (focusableElements.length === 0) return

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]
    const activeElement = document.activeElement as HTMLElement

    if (event.shiftKey) {
      // Shift + Tab
      if (activeElement === firstElement || !ref.current.contains(activeElement)) {
        event.preventDefault()
        lastElement.focus()
      }
    } else {
      // Tab
      if (activeElement === lastElement || !ref.current.contains(activeElement)) {
        event.preventDefault()
        firstElement.focus()
      }
    }
  }, [getFocusableElements])

  const handleClick = useCallback((event: MouseEvent) => {
    if (!isActive.current || !ref.current || allowOutsideClick) return

    if (!ref.current.contains(event.target as Node)) {
      event.preventDefault()
      event.stopPropagation()
      
      // Return focus to the first focusable element
      const focusableElements = getFocusableElements(ref.current)
      if (focusableElements.length > 0) {
        focusableElements[0].focus()
      }
    }
  }, [allowOutsideClick, getFocusableElements])

  const activate = useCallback(() => {
    if (!ref.current || isActive.current) return

    // Store the currently focused element
    previousActiveElement.current = document.activeElement

    isActive.current = true

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('click', handleClick, true)

    // Focus the first focusable element if autoFocus is enabled
    if (autoFocus) {
      const focusableElements = getFocusableElements(ref.current)
      if (focusableElements.length > 0) {
        focusableElements[0].focus()
      }
    }
  }, [handleKeyDown, handleClick, autoFocus, getFocusableElements])

  const deactivate = useCallback(() => {
    if (!isActive.current) return

    isActive.current = false

    // Remove event listeners
    document.removeEventListener('keydown', handleKeyDown)
    document.removeEventListener('click', handleClick, true)

    // Restore focus to the previously focused element
    if (restoreFocus && previousActiveElement.current) {
      (previousActiveElement.current as HTMLElement).focus()
    }
  }, [handleKeyDown, handleClick, restoreFocus])

  useEffect(() => {
    if (enabled) {
      activate()
    } else {
      deactivate()
    }

    return () => {
      deactivate()
    }
  }, [enabled, activate, deactivate])

  return {
    ref,
    activate,
    deactivate,
  }
}

/**
 * Focus management hook for modals and dialogs
 */
export interface UseModalFocusOptions extends UseFocusTrapOptions {
  isOpen?: boolean
  onClose?: () => void
}

export function useModalFocus(options: UseModalFocusOptions = {}) {
  const { isOpen = false, onClose, ...focusTrapOptions } = options
  
  const focusTrap = useFocusTrap({
    ...focusTrapOptions,
    enabled: isOpen,
  })

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose?.()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  return focusTrap
}

/**
 * Focus restoration hook
 */
export function useFocusRestore() {
  const previousActiveElement = useRef<Element | null>(null)

  const saveFocus = useCallback(() => {
    previousActiveElement.current = document.activeElement
  }, [])

  const restoreFocus = useCallback(() => {
    if (previousActiveElement.current) {
      (previousActiveElement.current as HTMLElement).focus()
      previousActiveElement.current = null
    }
  }, [])

  return {
    saveFocus,
    restoreFocus,
  }
}

/**
 * Focus visible hook for keyboard navigation
 */
export function useFocusVisible() {
  const [isFocusVisible, setIsFocusVisible] = useState(false)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    let hadKeyboardEvent = false

    const onKeyDown = () => {
      hadKeyboardEvent = true
    }

    const onMouseDown = () => {
      hadKeyboardEvent = false
    }

    const onFocus = () => {
      if (hadKeyboardEvent) {
        setIsFocusVisible(true)
      }
    }

    const onBlur = () => {
      setIsFocusVisible(false)
    }

    const element = ref.current
    if (!element) return

    document.addEventListener('keydown', onKeyDown, true)
    document.addEventListener('mousedown', onMouseDown, true)
    element.addEventListener('focus', onFocus)
    element.addEventListener('blur', onBlur)

    return () => {
      document.removeEventListener('keydown', onKeyDown, true)
      document.removeEventListener('mousedown', onMouseDown, true)
      element.removeEventListener('focus', onFocus)
      element.removeEventListener('blur', onBlur)
    }
  }, [])

  return {
    ref,
    isFocusVisible,
  }
}
