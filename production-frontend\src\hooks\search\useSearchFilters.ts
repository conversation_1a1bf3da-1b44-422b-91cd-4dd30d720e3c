/**
 * Search Filters Hook
 * Manages search filter state and operations
 */

import { useState, useCallback } from 'react'
import type { SearchFilters } from './useSearch'

export interface UseSearchFiltersResult {
  filters: SearchFilters
  
  setFilters: (filters: SearchFilters) => void
  updateFilter: (key: keyof SearchFilters, value: any) => void
  clearFilters: () => void
  resetFilter: (key: keyof SearchFilters) => void
  
  // Convenience methods
  setTypes: (types: string[]) => void
  setDateRange: (start: string, end: string) => void
  setTags: (tags: string[]) => void
  setStatus: (status: string[]) => void
  
  // Filter state checks
  hasActiveFilters: boolean
  getActiveFilterCount: () => number
}

const defaultFilters: SearchFilters = {
  types: [],
  dateRange: undefined,
  tags: [],
  status: [],
  projectId: undefined,
  organizationId: undefined,
  userId: undefined,
}

export function useSearchFilters(initialFilters?: SearchFilters): UseSearchFiltersResult {
  const [filters, setFiltersState] = useState<SearchFilters>({
    ...defaultFilters,
    ...initialFilters
  })

  const setFilters = useCallback((newFilters: SearchFilters) => {
    setFiltersState(newFilters)
  }, [])

  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
    setFiltersState(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFiltersState(defaultFilters)
  }, [])

  const resetFilter = useCallback((key: keyof SearchFilters) => {
    setFiltersState(prev => ({
      ...prev,
      [key]: defaultFilters[key]
    }))
  }, [])

  // Convenience methods
  const setTypes = useCallback((types: string[]) => {
    updateFilter('types', types)
  }, [updateFilter])

  const setDateRange = useCallback((start: string, end: string) => {
    updateFilter('dateRange', { start, end })
  }, [updateFilter])

  const setTags = useCallback((tags: string[]) => {
    updateFilter('tags', tags)
  }, [updateFilter])

  const setStatus = useCallback((status: string[]) => {
    updateFilter('status', status)
  }, [updateFilter])

  // Check if any filters are active
  const hasActiveFilters = Boolean(
    (filters.types && filters.types.length > 0) ||
    filters.dateRange ||
    (filters.tags && filters.tags.length > 0) ||
    (filters.status && filters.status.length > 0) ||
    filters.projectId ||
    filters.organizationId ||
    filters.userId
  )

  const getActiveFilterCount = useCallback((): number => {
    let count = 0
    
    if (filters.types && filters.types.length > 0) count++
    if (filters.dateRange) count++
    if (filters.tags && filters.tags.length > 0) count++
    if (filters.status && filters.status.length > 0) count++
    if (filters.projectId) count++
    if (filters.organizationId) count++
    if (filters.userId) count++
    
    return count
  }, [filters])

  return {
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    resetFilter,
    setTypes,
    setDateRange,
    setTags,
    setStatus,
    hasActiveFilters,
    getActiveFilterCount,
  }
}

export default useSearchFilters
