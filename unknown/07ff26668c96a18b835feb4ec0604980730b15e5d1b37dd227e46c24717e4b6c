/**
 * Server-side Azure AD B2C Authentication API
 * Handles authentication with client secret securely on the server
 */

import { NextRequest, NextResponse } from 'next/server'
import { ConfidentialClientApplication } from '@azure/msal-node'
import { createMSALConfig, B2C_POLICIES, B2C_SCOPES, getRedirectUri } from '@/lib/auth/msal-node-config'

export async function POST(request: NextRequest) {
  try {
    const { policy = B2C_POLICIES.SIGN_UP_SIGN_IN } = await request.json()

    // Create MSAL instance for the specific policy
    const msalConfig = createMSALConfig(policy)
    const msalInstance = new ConfidentialClientApplication(msalConfig)

    // Get authorization URL for the client-side redirect
    const authCodeUrlParameters = {
      scopes: B2C_SCOPES,
      redirectUri: getRedirectUri(),
      prompt: 'select_account',
      state: JSON.stringify({ policy: policy, timestamp: Date.now() }),
    }

    console.log('[Auth API] Creating auth URL for policy:', policy)
    const authUrl = await msalInstance.getAuthCodeUrl(authCodeUrlParameters)

    return NextResponse.json({
      success: true,
      authUrl: authUrl,
      policy: policy,
    })

  } catch (error) {
    console.error('[Auth API] Login failed:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const policy = searchParams.get('policy') || B2C_POLICIES.SIGN_UP_SIGN_IN
    const returnUrl = searchParams.get('returnUrl') || '/dashboard'

    // Create MSAL instance for the specific policy
    const msalConfig = createMSALConfig(policy)
    const msalInstance = new ConfidentialClientApplication(msalConfig)

    // Get authorization URL for the client-side redirect
    const authCodeUrlParameters = {
      scopes: B2C_SCOPES,
      redirectUri: getRedirectUri(),
      prompt: 'select_account',
      state: JSON.stringify({
        policy: policy,
        returnUrl: returnUrl,
        timestamp: Date.now()
      }),
    }

    console.log('[Auth API] Creating auth URL for policy:', policy)
    console.log('[Auth API] Redirect URI:', getRedirectUri())
    const authUrl = await msalInstance.getAuthCodeUrl(authCodeUrlParameters)
    console.log('[Auth API] Generated auth URL:', authUrl)

    // Redirect to Azure AD B2C
    return NextResponse.redirect(authUrl)

  } catch (error) {
    console.error('[Auth API] Login redirect failed:', error)

    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=auth_failed&details=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    )
  }
}
