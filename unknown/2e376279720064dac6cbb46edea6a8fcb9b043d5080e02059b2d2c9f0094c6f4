/**
 * Server-side Azure AD B2C Token Exchange API
 * Exchanges authorization code for tokens using client secret
 */

import { NextRequest, NextResponse } from 'next/server'
import { ConfidentialClientApplication } from '@azure/msal-node'
import { cookies } from 'next/headers'
import { createMSALConfig, B2C_POLICIES, B2C_SCOPES, getRedirectUri, getPostLogoutRedirectUri } from '@/lib/auth/msal-node-config'

// Helper function to extract data from state parameter
const extractDataFromState = (state: string | null): { policy: string; returnUrl: string } => {
  const defaults = {
    policy: B2C_POLICIES.SIGN_UP_SIGN_IN,
    returnUrl: '/dashboard'
  }

  if (!state) {
    console.warn('[Auth Callback] No state parameter found, using defaults')
    return defaults
  }

  try {
    const stateData = JSON.parse(state)
    const result = {
      policy: stateData.policy || defaults.policy,
      returnUrl: stateData.returnUrl || defaults.returnUrl
    }
    console.log('[Auth Callback] Extracted from state:', result)
    return result
  } catch (error) {
    console.warn('[Auth Callback] Could not parse state parameter:', error)
    return defaults
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')

    // Handle authentication errors
    if (error) {
      console.error('[Auth Callback] Authentication error:', error, errorDescription)
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=${encodeURIComponent(error)}&error_description=${encodeURIComponent(errorDescription || '')}`
      )
    }

    // Validate authorization code
    if (!code) {
      console.error('[Auth Callback] No authorization code received')
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_AZURE_AD_B2C_POST_LOGOUT_REDIRECT_URI}/auth/enhanced-login?error=no_code`
      )
    }

    // Extract policy and returnUrl from state parameter
    const { policy, returnUrl } = extractDataFromState(state)
    console.log('[Auth Callback] Using policy for token exchange:', policy)
    console.log('[Auth Callback] Return URL:', returnUrl)

    // Create MSAL instance with the correct policy for token exchange
    const msalConfig = createMSALConfig(policy)
    const msalInstance = new ConfidentialClientApplication(msalConfig)

    // Exchange authorization code for tokens
    const tokenRequest = {
      code: code,
      scopes: B2C_SCOPES,
      redirectUri: getRedirectUri(),
    }

    console.log('[Auth Callback] Exchanging code for tokens...')
    console.log('[Auth Callback] Token request:', { ...tokenRequest, code: 'REDACTED' })
    const response = await msalInstance.acquireTokenByCode(tokenRequest)

    if (!response) {
      throw new Error('No token response received')
    }

    console.log('[Auth Callback] Token exchange successful for user:', response.account?.username)

    // Create secure session data
    const sessionData = {
      accessToken: response.accessToken,
      idToken: response.idToken,
      account: {
        homeAccountId: response.account?.homeAccountId,
        environment: response.account?.environment,
        tenantId: response.account?.tenantId,
        username: response.account?.username,
        localAccountId: response.account?.localAccountId,
        name: response.account?.name,
      },
      idTokenClaims: response.idTokenClaims,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    }

    // Set secure HTTP-only cookies
    const cookieStore = await cookies()

    // Set access token cookie (HTTP-only for security)
    cookieStore.set('msal_access_token', response.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Set user info cookie (can be read by client)
    const userInfoData = JSON.stringify({
      username: response.account?.username,
      name: response.account?.name,
      localAccountId: response.account?.localAccountId,
      tenantId: response.account?.tenantId,
    })

    console.log('[Auth Callback] Setting user info cookie:', userInfoData)
    cookieStore.set('msal_user_info', userInfoData, {
      httpOnly: false, // Allow client-side access
      secure: false, // Set to false for localhost
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Set session cookie
    console.log('[Auth Callback] Response expiresOn:', response.expiresOn)
    console.log('[Auth Callback] Response scopes:', response.scopes)
    const expiresOn = response.expiresOn?.getTime() || (Date.now() + 3600000) // 1 hour from now
    console.log('[Auth Callback] Calculated expiresOn:', expiresOn)
    const sessionCookieData = JSON.stringify({
      isAuthenticated: true,
      expiresOn: expiresOn,
      scopes: response.scopes || [],
    })

    console.log('[Auth Callback] Setting session cookie:', sessionCookieData)
    cookieStore.set('msal_session', sessionCookieData, {
      httpOnly: false, // Allow client-side access
      secure: false, // Set to false for localhost
      sameSite: 'lax',
      maxAge: 3600, // 1 hour
      path: '/',
    })

    // Verify cookies were set
    console.log('[Auth Callback] Verifying cookies were set...')
    const verifyUserInfo = cookieStore.get('msal_user_info')
    const verifySession = cookieStore.get('msal_session')
    console.log('[Auth Callback] User info cookie verification:', !!verifyUserInfo)
    console.log('[Auth Callback] Session cookie verification:', !!verifySession)

    // Redirect directly to the intended destination
    console.log('[Auth Callback] Redirecting to:', returnUrl)
    return NextResponse.redirect(
      `${getPostLogoutRedirectUri()}${returnUrl}`
    )

  } catch (error) {
    console.error('[Auth Callback] Token exchange failed:', error)
    
    return NextResponse.redirect(
      `${getPostLogoutRedirectUri()}/auth/enhanced-login?error=token_exchange_failed&error_description=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { code, state } = await request.json()

    if (!code) {
      throw new Error('Authorization code is required')
    }

    // Extract policy from state parameter
    const { policy } = extractDataFromState(state)
    console.log('[Auth Callback API] Using policy for token exchange:', policy)

    // Create MSAL instance with the correct policy for token exchange
    const msalConfig = createMSALConfig(policy)
    const msalInstance = new ConfidentialClientApplication(msalConfig)

    // Exchange authorization code for tokens
    const tokenRequest = {
      code: code,
      scopes: B2C_SCOPES,
      redirectUri: getRedirectUri(),
    }

    const response = await msalInstance.acquireTokenByCode(tokenRequest)

    if (!response) {
      throw new Error('No token response received')
    }

    // Return token data (for API usage)
    return NextResponse.json({
      success: true,
      accessToken: response.accessToken,
      idToken: response.idToken,
      account: response.account,
      idTokenClaims: response.idTokenClaims,
      expiresOn: response.expiresOn?.getTime(),
      scopes: response.scopes,
    })

  } catch (error) {
    console.error('[Auth Callback API] Token exchange failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Token exchange failed',
      },
      { status: 500 }
    )
  }
}
