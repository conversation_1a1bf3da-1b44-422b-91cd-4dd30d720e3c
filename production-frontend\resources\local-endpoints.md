# Functions:

advanced-analytics-get: [GET,OPTIONS] http://localhost:7071/analytics/advanced

advanced-permission-check: [POST,OPTIONS] http://localhost:7071/permissions/check-advanced

advanced-role-create: [POST,OPTIONS] http://localhost:7071/management/advanced-roles

ai-document-analysis: [POST,OPTIONS] http://localhost:7071/documents/{id}/ai-analysis

ai-form-process: [POST,OPTIONS] http://localhost:7071/ai/forms/process

ai-intelligent-search: [GET,POST,OPTIONS] http://localhost:7071/search/intelligent

ai-model-create: [POST,OPTIONS] http://localhost:7071/ai/models

ai-model-deploy: [POST,OPTIONS] http://localhost:7071/ai/models/{modelId}/deploy

ai-model-train: [POST,OPTIONS] http://localhost:7071/ai/models/{modelId}/train

ai-operation-create: [POST,OPTIONS] http://localhost:7071/ai/operations

ai-operation-status: [GET,OPTIONS] http://localhost:7071/ai/operations/{operationId}

analytics-dashboard: [GET,OPTIONS] http://localhost:7071/analytics/dashboard

analytics-get: [GET,OPTIONS] http://localhost:7071/analytics

api-connection-create: [POST,OPTIONS] http://localhost:7071/integrations/api-connections

api-connection-test: [POST,OPTIONS] http://localhost:7071/integrations/api-connections/test

api-key-create: [POST,OPTIONS] http://localhost:7071/api-keys/create

api-key-revoke: [DELETE,OPTIONS] http://localhost:7071/api-keys/{apiKeyId}

api-key-validate: [GET,POST,OPTIONS] http://localhost:7071/api-keys/validate

api-keys: [GET,POST,OPTIONS] http://localhost:7071/api-keys

audit-log-create: [POST,OPTIONS] http://localhost:7071/audit/logs/create

audit-logs-export: [POST,OPTIONS] http://localhost:7071/audit/logs/export

audit-logs-get: [GET,OPTIONS] http://localhost:7071/audit/logs/get

audit-logs-list: [GET,OPTIONS] http://localhost:7071/audit/logs

audit-statistics: [GET,OPTIONS] http://localhost:7071/audit/statistics

auth-login: [POST,OPTIONS] http://localhost:7071/auth/login

auth-logout: [POST,OPTIONS] http://localhost:7071/auth/logout

auth-me: [GET,OPTIONS] http://localhost:7071/auth/me

auth-refresh: [POST,OPTIONS] http://localhost:7071/auth/refresh

auth-register: [POST,OPTIONS] http://localhost:7071/auth/register

automation-create: [POST,OPTIONS] http://localhost:7071/automations

automation-execute: [POST,OPTIONS] http://localhost:7071/automations/execute

backup-create: [POST,OPTIONS] http://localhost:7071/management/backups/create

backup-status: [GET,OPTIONS] http://localhost:7071/management/backups/{backupId}/status

batch-job-create: [POST,OPTIONS] http://localhost:7071/ai/batch-jobs

batch-job-status: [GET,OPTIONS] http://localhost:7071/ai/batch-jobs/{batchJobId}/status

bi-dashboard-get: [GET,OPTIONS] http://localhost:7071/analytics/bi/dashboard

bi-report-generate: [POST,OPTIONS] http://localhost:7071/analytics/bi/reports

cache-clear: [DELETE,OPTIONS] http://localhost:7071/management/cache/clear

cache-operation: [POST,OPTIONS] http://localhost:7071/management/cache/operations

cache-statistics: [GET,OPTIONS] http://localhost:7071/management/cache/statistics

channel-create: [POST,OPTIONS] http://localhost:7071/channels

classification-category-create: [POST,OPTIONS] http://localhost:7071/classification/categories

collaboration-session-create: [POST,OPTIONS] http://localhost:7071/collaboration/sessions

collaboration-session-join: [POST,OPTIONS] http://localhost:7071/collaboration/sessions/join

comment-create: [POST,OPTIONS] http://localhost:7071/comments

comment-reaction-add: [POST,OPTIONS] http://localhost:7071/comments/reactions

compliance-assessment-create: [POST,OPTIONS] http://localhost:7071/compliance/assessments

compliance-status-update: [PUT,OPTIONS] http://localhost:7071/compliance/status

comprehensive-document-analysis: [POST,OPTIONS] http://localhost:7071/documents/{id}/comprehensive-analysis

config-get: [GET,OPTIONS] http://localhost:7071/config

config-update: [PUT,POST,OPTIONS] http://localhost:7071/management/configuration

custom-report-create: [POST,OPTIONS] http://localhost:7071/reports/custom

custom-report-status: [GET,OPTIONS] http://localhost:7071/reports/custom/{reportId}/status

dashboard-create: [POST,OPTIONS] http://localhost:7071/dashboards

dashboard-get: [GET,OPTIONS] http://localhost:7071/dashboards/{dashboardId}

data-decrypt: [POST,OPTIONS] http://localhost:7071/security/decrypt

data-encrypt: [POST,OPTIONS] http://localhost:7071/security/encrypt

data-export-create: [POST,OPTIONS] http://localhost:7071/exports

data-export-status: [GET,OPTIONS] http://localhost:7071/exports/{exportId}/status

device-register: [POST,OPTIONS] http://localhost:7071/notifications/devices

document-approval-create: [POST,OPTIONS] http://localhost:7071/documents/approvals

document-archive: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/archive

document-classify: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/classify

document-comments: [GET,POST,OPTIONS] http://localhost:7071/documents/{documentId}/comments

document-complete-content: [POST,OPTIONS] http://localhost:7071/documents/{id}/complete-content

document-enhance: [POST,OPTIONS] http://localhost:7071/documents/{id}/enhance

document-list: [GET,OPTIONS] http://localhost:7071/documents

document-metadata-extract: [POST,OPTIONS] http://localhost:7071/documents/metadata/extract

document-metadata-update: [PUT,OPTIONS] http://localhost:7071/documents/{documentId}/metadata

document-processing: [POST,OPTIONS] http://localhost:7071/documents/process

document-restore: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/restore

document-retrieve: [GET,OPTIONS] http://localhost:7071/documents/{id}

document-review: [POST,OPTIONS] http://localhost:7071/documents/approvals/review

document-share: [POST,OPTIONS] http://localhost:7071/documents/{id}/share

document-share-create: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/share

document-share-get: [GET,OPTIONS] http://localhost:7071/documents/{documentId}/shares

document-sign: [POST,OPTIONS] http://localhost:7071/documents/sign

document-template-generate: [POST,OPTIONS] http://localhost:7071/document-templates/generate

document-transform: [POST,OPTIONS] http://localhost:7071/documents/transform

document-upload: [POST,OPTIONS] http://localhost:7071/documents/upload

document-upload-complete: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/upload/complete

document-version-create: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/versions

document-version-restore: [POST,OPTIONS] http://localhost:7071/documents/{documentId}/versions/{versionId}/restore

document-versions: [GET,POST,OPTIONS] http://localhost:7071/documents/{id}/versions

email-automation-send: [POST,OPTIONS] http://localhost:7071/emails/automation/send

email-automation-template-create: [POST,OPTIONS] http://localhost:7071/emails/automation/templates

email-list: [GET,OPTIONS] http://localhost:7071/emails

email-send: [POST,OPTIONS] http://localhost:7071/emails/send

email-template-create: [POST,OPTIONS] http://localhost:7071/emails/templates

enterprise-integration-create: [POST,OPTIONS] http://localhost:7071/integrations/enterprise

enterprise-integration-sync: [POST,OPTIONS] http://localhost:7071/integrations/enterprise/sync

event-grid-publish: [POST,OPTIONS] http://localhost:7071/eventgrid/publish

event-grid-webhook: [POST,OPTIONS] http://localhost:7071/eventgrid/webhook

feature-flag-create: [POST,OPTIONS] http://localhost:7071/management/feature-flags/create

feature-flag-evaluate: [GET,POST,OPTIONS] http://localhost:7071/feature-flags/evaluate

health: [GET,OPTIONS] http://localhost:7071/health

health-check-create: [POST,OPTIONS] http://localhost:7071/management/health-checks/create

health-system: [GET,OPTIONS] http://localhost:7071/system/health-status

integration-create: [POST,OPTIONS] http://localhost:7071/integrations

lemonsqueezy-webhooks: [GET,POST,OPTIONS] http://localhost:7071/lemonsqueezy-webhooks

log-create: [POST,OPTIONS] http://localhost:7071/logs

log-query: [GET,OPTIONS] http://localhost:7071/logs/query

log-statistics: [GET,OPTIONS] http://localhost:7071/logs/statistics

message-list: [GET,OPTIONS] http://localhost:7071/channels/{channelId}/messages

message-send: [POST,OPTIONS] http://localhost:7071/messages

metric-record: [POST,OPTIONS] http://localhost:7071/metrics

metrics-collect: [POST,OPTIONS] http://localhost:7071/metrics/collect

metrics-query: [GET,OPTIONS] http://localhost:7071/metrics/query

metrics-summary: [GET,OPTIONS] http://localhost:7071/metrics/summary

migration-create: [POST,OPTIONS] http://localhost:7071/management/data-migrations/create

migration-status: [GET,OPTIONS] http://localhost:7071/management/data-migrations/{migrationId}/status

mobile-device-register: [POST,OPTIONS] http://localhost:7071/mobile/devices/register

mobile-offline-data: [GET,OPTIONS] http://localhost:7071/mobile/offline-data

mobile-sync: [POST,OPTIONS] http://localhost:7071/mobile/sync

model-train: [POST,OPTIONS] http://localhost:7071/management/analytics/models/train

notification-analytics: [GET,OPTIONS] http://localhost:7071/notifications/analytics

notification-get: [GET,OPTIONS] http://localhost:7071/notifications/{notificationId}

[2025-06-04T05:35:31.402Z] {"timestamp":"2025-06-04T05:35:31.402Z","level":"INFO","message":"Redis pub/sub client initialized"}
notification-list: [GET,OPTIONS] http://localhost:7071/notifications

[2025-06-04T05:35:31.405Z] {"timestamp":"2025-06-04T05:35:31.402Z","level":"INFO","message":"Azure Managed Redis service initialized successfully","host":"hepzbackend.eastus.redis.azure.net","port":10000,"mode":"single","managedIdentityType":"system-assigned","connectionTime":6338}
notification-mark-read: [PATCH,OPTIONS] http://localhost:7071/notifications/mark-read

[2025-06-04T05:35:31.406Z] Γ£à Redis Enhanced Service initialized
notification-preferences-get: [GET,OPTIONS] http://localhost:7071/notifications/preferences

notification-preferences-reset: [POST,OPTIONS] http://localhost:7071/notifications/preferences/reset

notification-preferences-update: [PUT,OPTIONS] http://localhost:7071/notifications/preferences/update

notification-send: [POST,OPTIONS] http://localhost:7071/notifications/send

notification-tracking-track: [POST,OPTIONS] http://localhost:7071/notifications/track

organization-analytics: [GET,OPTIONS] http://localhost:7071/organizations/analytics

organization-billing-get: [GET,OPTIONS] http://localhost:7071/organizations/{organizationId}/billing

organization-billing-update: [PUT,OPTIONS] http://localhost:7071/organizations/{organizationId}/billing/subscription

organization-manage: [GET,PATCH,DELETE,OPTIONS] http://localhost:7071/organizations/{organizationId}

organization-members-invite: [POST,OPTIONS] http://localhost:7071/organizations/{organizationId}/members/invite

organization-settings-get: [GET,OPTIONS] http://localhost:7071/organizations/{organizationId}/settings

organization-settings-update: [PUT,OPTIONS] http://localhost:7071/organizations/settings

organization-teams-create: [POST,OPTIONS] http://localhost:7071/organizations/{organizationId}/teams

organizations: [GET,POST,OPTIONS] http://localhost:7071/organizations

performance-alert-rule-create: [POST,OPTIONS] http://localhost:7071/performance/alert-rules

performance-metrics: [GET,POST,OPTIONS] http://localhost:7071/performance/metrics

permission-batch-check: [POST,OPTIONS] http://localhost:7071/permissions/batch-check

permission-check: [GET,OPTIONS] http://localhost:7071/permissions/check

permission-grant: [POST,OPTIONS] http://localhost:7071/permissions/grant

prediction-generate: [POST,OPTIONS] http://localhost:7071/analytics/predictions

Production-sample: [GET,POST] http://localhost:7071/sample

project-analytics: [GET,OPTIONS] http://localhost:7071/projects/analytics

project-manage: [GET,PATCH,DELETE,OPTIONS] http://localhost:7071/projects/{projectId}

project-members-add: [POST,OPTIONS] http://localhost:7071/projects/members

project-members-get: [GET,OPTIONS] http://localhost:7071/projects/{projectId}/members

project-members-update: [PUT,OPTIONS] http://localhost:7071/projects/members/update

project-settings-get: [GET,OPTIONS] http://localhost:7071/projects/{projectId}/settings

project-settings-update: [PUT,OPTIONS] http://localhost:7071/projects/settings

projects: [GET,POST,OPTIONS] http://localhost:7071/projects

push-notification-register: [POST,OPTIONS] http://localhost:7071/notifications/push/register

push-notification-send: [POST,OPTIONS] http://localhost:7071/notifications/push/send

push-notification-stats: [GET,OPTIONS] http://localhost:7071/notifications/push/stats

push-notification-unregister: [POST,OPTIONS] http://localhost:7071/notifications/push/unregister

push-notifications-send: [POST,OPTIONS] http://localhost:7071/notifications/push

rag-query: [POST,OPTIONS] http://localhost:7071/rag/query

rag-query-history: [GET,OPTIONS] http://localhost:7071/rag/history

rate-limit-check: [GET,POST,OPTIONS] http://localhost:7071/rate-limit/check

rate-limit-create: [POST,OPTIONS] http://localhost:7071/management/rate-limits/create

search: [GET,OPTIONS] http://localhost:7071/search

search-advanced: [GET,POST,OPTIONS] http://localhost:7071/search/advanced

search-documents: [GET,OPTIONS] http://localhost:7071/search/documents

search-index-document: [POST,OPTIONS] http://localhost:7071/search/index

security-incident-create: [POST,OPTIONS] http://localhost:7071/security/incidents

security-threats-analyze: [GET,OPTIONS] http://localhost:7071/security/threats/analyze

signalr-broadcast: [POST,OPTIONS] http://localhost:7071/signalr/broadcast

signalr-groups: [POST,OPTIONS] http://localhost:7071/signalr/groups

signalr-negotiate: [POST,OPTIONS] http://localhost:7071/signalr/negotiate

storage-bulk-sync: [POST,OPTIONS] http://localhost:7071/storage/sync/bulk

storage-configure: [POST,OPTIONS] http://localhost:7071/storage/configure

storage-sync-document: [POST,OPTIONS] http://localhost:7071/storage/sync/document

subscription-create: [POST,OPTIONS] http://localhost:7071/subscriptions

subscription-get: [GET,OPTIONS] http://localhost:7071/subscriptions/{organizationId}

system-health: [GET,OPTIONS] http://localhost:7071/system/health

system-metrics: [GET,OPTIONS] http://localhost:7071/system/metrics

template-apply: [POST,OPTIONS] http://localhost:7071/templates/{templateId}/apply

template-create: [POST,OPTIONS] http://localhost:7071/templates

template-generate: [POST,OPTIONS] http://localhost:7071/templates/generate

templates: [GET,POST,OPTIONS] http://localhost:7071/templates/manage

tenant-create: [POST,OPTIONS] http://localhost:7071/tenants

tenant-get: [GET,OPTIONS] http://localhost:7071/tenants/{tenantId}

user-activity-analytics: [GET,OPTIONS] http://localhost:7071/users/activity/analytics

user-activity-track: [POST,OPTIONS] http://localhost:7071/users/activity/track

user-avatar-upload: [POST,OPTIONS] http://localhost:7071/users/avatar/upload

user-permissions: [GET,OPTIONS] http://localhost:7071/users/{userId?}/permissions

user-personalization-get: [GET,OPTIONS] http://localhost:7071/users/personalization

user-personalization-update: [PATCH,OPTIONS] http://localhost:7071/users/personalization/update

user-preferences-get: [GET,OPTIONS] http://localhost:7071/users/preferences

user-preferences-reset: [POST,OPTIONS] http://localhost:7071/users/preferences/reset

user-preferences-update: [PUT,OPTIONS] http://localhost:7071/users/preferences/update

user-profile: [GET,PATCH,OPTIONS] http://localhost:7071/users/profile

user-profile-get: [GET,OPTIONS] http://localhost:7071/users/{userId?}/profile

user-profile-preferences-update: [PATCH,OPTIONS] http://localhost:7071/users/profile/preferences

user-profile-update: [PUT,OPTIONS] http://localhost:7071/users/profile/update

user-tenants-get: [GET,OPTIONS] http://localhost:7071/users/{userId?}/tenants

user-tenants-invite: [POST,OPTIONS] http://localhost:7071/organizations/{organizationId}/invite

user-tenants-switch: [POST,OPTIONS] http://localhost:7071/users/tenants/switch

webhook-delivery: [POST,OPTIONS] http://localhost:7071/webhooks/deliver

webhook-test: [POST,OPTIONS] http://localhost:7071/webhooks/test

webhooks: [GET,POST,OPTIONS] http://localhost:7071/webhooks

workflow-action: [POST,OPTIONS] http://localhost:7071/workflows/{workflowId}/actions

workflow-bulk-action: [POST,OPTIONS] http://localhost:7071/workflows/bulk-actions

workflow-execution-complete-step: [POST,OPTIONS] http://localhost:7071/workflows/{id}/steps/{stepId}/complete

workflow-execution-start: [POST,OPTIONS] http://localhost:7071/workflows/executions

workflow-get: [GET,OPTIONS] http://localhost:7071/workflows/{id}

workflow-monitoring: [GET,OPTIONS] http://localhost:7071/workflows/analytics

workflow-schedule-create: [POST,OPTIONS] http://localhost:7071/workflows/schedules

workflow-schedule-status-update: [PUT,OPTIONS] http://localhost:7071/workflows/schedules/status

workflow-start: [POST,OPTIONS] http://localhost:7071/workflows/{id}/start

workflow-template-create: [POST,OPTIONS] http://localhost:7071/workflow-templates/create

workflow-templates: [GET,POST,OPTIONS] http://localhost:7071/workflow-templates

workflow-templates-get: [GET,OPTIONS] http://localhost:7071/workflow-templates/{id}

workflows: [GET,POST,OPTIONS] http://localhost:7071/workflows

aiOperations: serviceBusTrigger

analyticsAggregation: serviceBusTrigger

cache-warming-scheduler: timerTrigger

custom-events-trigger: eventGridTrigger

customEventGridTrigger: eventGridTrigger

dailyCleanupTimer: timerTrigger

deadLetterQueue: queueTrigger

documentBlobTrigger: blobTrigger

documentCollaboration: serviceBusTrigger

documentProcessing: serviceBusTrigger

documentProcessingQueue: queueTrigger

emailQueue: queueTrigger

exportBlobTrigger: blobTrigger

hourlyHealthCheckTimer: timerTrigger

notificationDelivery: serviceBusTrigger

notificationQueue: queueTrigger

scheduledEmails: serviceBusTrigger

storage-events-trigger: eventGridTrigger

storageEventGridTrigger: eventGridTrigger

systemMonitoring: serviceBusTrigger

templateBlobTrigger: blobTrigger

weeklyAnalyticsTimer: timerTrigger

workflowOrchestration: serviceBusTrigger
