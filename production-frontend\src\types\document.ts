/**
 * Document Management Types
 * Frontend document types that extend backend types
 */

import type { ID, Timestamp, URL, User } from './index'
import type {
  Document as BackendDocument,
  DocumentStatus as BackendDocumentStatus,
  DocumentType as BackendDocumentType,
  DocumentMetadata as BackendDocumentMetadata
} from './backend'



// Use backend Document as base and extend for frontend needs
export interface Document extends Omit<BackendDocument, 'sharing' | 'processing'> {
  // Frontend-specific computed properties
  mimeType?: string
  url?: URL
  thumbnailUrl?: URL
  versions?: DocumentVersion[]
  uploadedBy?: string
  uploadedAt?: Timestamp
  processedAt?: Timestamp
  lastModified?: Timestamp
  isPublic?: boolean
  permissions?: DocumentPermission[]
  sharing?: DocumentSharing
  processing?: DocumentProcessing
  analysis?: DocumentAnalysis

  // Additional properties for UI
  entities?: Array<{
    text: string
    type: string
    confidence: number
    page?: number
  }>
  content?: string
  contentUrl?: string
}

// Create enum-like objects for runtime usage
export const DocumentStatus = {
  PENDING: 'pending' as const,
  PROCESSING: 'processing' as const,
  PROCESSED: 'processed' as const,
  FAILED: 'failed' as const,
  COMPLETED: 'completed' as const,
  RUNNING: 'running' as const,
  ARCHIVED: 'archived' as const,
  UPLOADED: 'uploaded' as const,
  UPLOADING: 'uploading' as const,
  DELETED: 'deleted' as const
} as const

export const DocumentType = {
  GENERAL: 'general' as const,
  INVOICE: 'invoice' as const,
  CONTRACT: 'contract' as const,
  REPORT: 'report' as const,
  FORM: 'form' as const,
  PDF: 'pdf' as const,
  WORD: 'word' as const,
  EXCEL: 'excel' as const,
  POWERPOINT: 'powerpoint' as const,
  IMAGE: 'image' as const,
  TEXT: 'text' as const,
  CSV: 'csv' as const,
  JSON: 'json' as const,
  XML: 'xml' as const,
  OTHER: 'other' as const
} as const

// Export type aliases for compatibility
export type DocumentStatus = BackendDocumentStatus
export type DocumentType = BackendDocumentType

// Document version
export interface DocumentVersion {
  id: ID
  versionNumber: number
  version?: number // For backward compatibility
  url?: URL
  size?: number
  uploadedBy?: ID
  uploadedAt?: Timestamp
  createdAt: Timestamp
  changes?: string
  isActive?: boolean
  isLatestVersion?: boolean
  isCurrent?: boolean
  fileName?: string
  parentDocumentId?: string
}

// Document metadata - extends backend metadata
export interface DocumentMetadata extends BackendDocumentMetadata {
  // Frontend-specific metadata fields
  title?: string
  author?: string
  subject?: string
  keywords?: string[]
  createdDate?: Timestamp
  modifiedDate?: Timestamp
  pageCount?: number
  wordCount?: number
  language?: string
  encoding?: string
  customFields?: Record<string, any>

  // Additional computed fields for UI
  contentType?: string
  createdBy?: string
  updatedBy?: string
  entities?: Array<{
    text: string
    type: string
    confidence: number
    page?: number
  }>
}

// Document permissions
export interface DocumentPermission {
  userId: ID
  user: User
  permission: 'read' | 'write' | 'admin'
  grantedBy: ID
  grantedAt: Timestamp
  expiresAt?: Timestamp
}

// Document sharing
export interface DocumentSharing {
  isPublic: boolean
  publicUrl?: URL
  allowDownload: boolean
  allowPrint: boolean
  allowCopy: boolean
  passwordProtected: boolean
  expiresAt?: Timestamp
  shareLinks: DocumentShareLink[]
}

export interface DocumentShareLink {
  id: ID
  url: URL
  permissions: string[]
  createdBy: ID
  createdAt: Timestamp
  expiresAt?: Timestamp
  accessCount: number
  lastAccessed?: Timestamp
}

// Document processing
export interface DocumentProcessing {
  status: DocumentStatus
  progress: number
  startedAt?: Timestamp
  completedAt?: Timestamp
  error?: string
  steps: DocumentProcessingStep[]
  options: DocumentProcessingOptions
}

export interface DocumentProcessingStep {
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  startedAt?: Timestamp
  completedAt?: Timestamp
  error?: string
  result?: any
}

export interface DocumentProcessingOptions {
  extractText: boolean
  extractImages: boolean
  extractTables: boolean
  analyzeLayout: boolean
  detectLanguage: boolean
  generateThumbnail: boolean
  runOCR: boolean
  extractMetadata: boolean
  generateSummary: boolean
  analyzeContent: boolean
}

// Document analysis (AI/ML results)
export interface DocumentAnalysis {
  textContent?: string
  summary?: string
  keyPhrases?: string[]
  entities?: DocumentEntity[]
  sentiment?: DocumentSentiment
  language?: string
  confidence: number
  tables?: DocumentTable[]
  images?: DocumentImage[]
  layout?: DocumentLayout
  insights?: DocumentInsight[]
  generatedAt: Timestamp
}

export interface DocumentEntity {
  text: string
  type: string
  confidence: number
  offset: number
  length: number
}

export interface DocumentSentiment {
  overall: 'positive' | 'negative' | 'neutral'
  confidence: number
  scores: {
    positive: number
    negative: number
    neutral: number
  }
}

export interface DocumentTable {
  id: string
  rows: number
  columns: number
  data: string[][]
  confidence: number
  boundingBox?: BoundingBox
}

export interface DocumentImage {
  id: string
  url: URL
  description?: string
  confidence: number
  boundingBox?: BoundingBox
}

export interface DocumentLayout {
  pages: DocumentPage[]
  confidence: number
}

export interface DocumentPage {
  pageNumber: number
  width: number
  height: number
  angle: number
  unit: string
  lines: DocumentLine[]
  tables: DocumentTable[]
  images: DocumentImage[]
}

export interface DocumentLine {
  text: string
  boundingBox: BoundingBox
  words: DocumentWord[]
}

export interface DocumentWord {
  text: string
  confidence: number
  boundingBox: BoundingBox
}

export interface BoundingBox {
  x: number
  y: number
  width: number
  height: number
}

export interface DocumentInsight {
  type: string
  title: string
  description: string
  confidence: number
  data?: any
}

// Document operations
export interface DocumentUploadRequest {
  file: File
  name?: string
  description?: string
  projectId?: ID
  organizationId?: ID
  tags?: string[]
  metadata?: Record<string, any>
  processingOptions?: Partial<DocumentProcessingOptions>
}

export interface DocumentUpdateRequest {
  name?: string
  description?: string
  tags?: string[]
  metadata?: Record<string, any>
  projectId?: ID
}

export interface DocumentSearchQuery {
  query?: string
  type?: DocumentType[]
  status?: DocumentStatus[]
  projectId?: ID
  organizationId?: ID
  uploadedBy?: ID
  tags?: string[]
  dateRange?: {
    start: Timestamp
    end: Timestamp
  }
  hasAnalysis?: boolean
  isPublic?: boolean
}

export interface DocumentSearchResult {
  documents: Document[]
  total: number
  facets: {
    types: { type: DocumentType; count: number }[]
    statuses: { status: DocumentStatus; count: number }[]
    tags: { tag: string; count: number }[]
    projects: { projectId: ID; projectName: string; count: number }[]
  }
  suggestions: string[]
}

// Document comparison
export interface DocumentComparison {
  id: ID
  document1Id: ID
  document2Id: ID
  sourceVersionNumber?: number
  targetVersionNumber?: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  results?: DocumentComparisonResult
  differences: DocumentDifference[]
  createdAt: Timestamp
  completedAt?: Timestamp
}

export interface DocumentComparisonResult {
  similarity: number
  differences: DocumentDifference[]
  summary: string | {
    addedLines: number
    removedLines: number
    modifiedLines: number
    totalChanges: number
  }
  recommendations?: string[]
  insights?: string[]
}

export interface DocumentDifference {
  type: 'added' | 'removed' | 'modified' | 'changed'
  section: string
  oldValue?: string
  newValue?: string
  confidence: number

  // Additional properties for compatibility
  lineNumber?: number
  path?: string
  content?: string
  oldContent?: string
  newContent?: string
}

// Document workflow
export interface DocumentWorkflow {
  id: ID
  name: string
  description?: string
  steps: DocumentWorkflowStep[]
  triggers: DocumentWorkflowTrigger[]
  isActive: boolean
  organizationId: ID
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface DocumentWorkflowStep {
  id: string
  type: 'process' | 'analyze' | 'approve' | 'notify' | 'transform'
  name: string
  config: Record<string, any>
  order: number
}

export interface DocumentWorkflowTrigger {
  type: 'upload' | 'status_change' | 'schedule' | 'manual'
  config: Record<string, any>
}

export interface DocumentWorkflowExecution {
  id: ID
  workflowId: ID
  documentId: ID
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  currentStep: number
  steps: DocumentWorkflowStepExecution[]
  startedAt: Timestamp
  completedAt?: Timestamp
  error?: string
}

export interface DocumentWorkflowStepExecution {
  stepId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  startedAt?: Timestamp
  completedAt?: Timestamp
  input?: any
  output?: any
  error?: string
}

// Document templates
export interface DocumentTemplate {
  id: ID
  name: string
  description?: string
  type: DocumentType
  fields: DocumentTemplateField[]
  layout?: DocumentTemplateLayout
  isPublic: boolean
  organizationId: ID
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
  usageCount: number
}

export interface DocumentTemplateField {
  id: string
  name: string
  type: 'text' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect'
  required: boolean
  defaultValue?: any
  options?: string[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
  position?: {
    page: number
    x: number
    y: number
    width: number
    height: number
  }
}

export interface DocumentTemplateLayout {
  pages: number
  width: number
  height: number
  orientation: 'portrait' | 'landscape'
  margins: {
    top: number
    right: number
    bottom: number
    left: number
  }
}

// Document collaboration
export interface DocumentCollaboration {
  id: ID
  documentId: ID
  participants: DocumentParticipant[]
  comments: DocumentComment[]
  changes: DocumentChange[]
  isActive: boolean
  startedAt: Timestamp
  endedAt?: Timestamp
}

export interface DocumentParticipant {
  userId: ID
  user: User
  role: 'viewer' | 'editor' | 'reviewer'
  joinedAt: Timestamp
  lastSeen: Timestamp
  isOnline: boolean
  cursor?: {
    x: number
    y: number
    page: number
  }
}

export interface DocumentComment {
  id: ID
  content: string
  authorId: ID
  author: User
  documentId?: ID
  createdBy?: ID
  position?: {
    page: number
    x: number
    y: number
  }
  replies: DocumentComment[]
  resolved: boolean
  isResolved?: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface DocumentChange {
  id: ID
  type: 'text' | 'annotation' | 'highlight' | 'comment'
  authorId: ID
  author: User
  position: {
    page: number
    x: number
    y: number
    width?: number
    height?: number
  }
  content: any
  timestamp: Timestamp
}
