/**
 * Bulk Operations Dashboard
 * Comprehensive interface for managing bulk document operations
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Upload,
  FileText,
  Workflow,
  PenTool,
  Download,
  Play,
  Pause,
  Square,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  MoreHorizontal,
  Trash2,
  FolderOpen,
  Zap,
  XCircle
} from 'lucide-react';
import { useBulkOperations } from '@/hooks/bulk-operations/useBulkOperations';
// import { BulkOperationJob } from '@/services/bulk-operations-service';
import { formatDistanceToNow } from 'date-fns';

// Helper functions for icons and colors
function getOperationIcon(type: string) {
  switch (type.toLowerCase()) {
    case 'upload':
    case 'bulk_upload':
      return <Upload className="h-4 w-4" />;
    case 'delete':
    case 'bulk_delete':
      return <Trash2 className="h-4 w-4" />;
    case 'move':
    case 'bulk_move':
      return <FolderOpen className="h-4 w-4" />;
    case 'process':
    case 'bulk_process':
      return <Zap className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
}

function getStatusIcon(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    case 'failed':
      return <XCircle className="h-4 w-4 text-red-500" />;
    case 'running':
    case 'processing':
      return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
    case 'pending':
      return <Clock className="h-4 w-4 text-yellow-500" />;
    default:
      return <Clock className="h-4 w-4 text-gray-500" />;
  }
}

function getStatusColor(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'failed':
      return 'bg-red-100 text-red-800';
    case 'running':
    case 'processing':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

interface BulkOperationsDashboardProps {
  projectId: string;
  organizationId: string;
}

export function BulkOperationsDashboard({ projectId, organizationId }: BulkOperationsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const { data: jobs = [], isLoading, refetch } = useBulkOperations();

  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [refetch]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'processing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getOperationIcon = (type: string) => {
    switch (type) {
      case 'upload':
        return <Upload className="h-4 w-4" />;
      case 'processing':
        return <FileText className="h-4 w-4" />;
      case 'workflow':
        return <Workflow className="h-4 w-4" />;
      case 'signing':
        return <PenTool className="h-4 w-4" />;
      case 'export':
        return <Download className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const activeJobs = jobs.filter(job => ['pending', 'running'].includes(job.status));
  const completedJobs = jobs.filter(job => job.status === 'completed');
  const failedJobs = jobs.filter(job => job.status === 'failed');

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeJobs.length}</div>
            <p className="text-xs text-muted-foreground">
              Currently processing
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedJobs.length}</div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{failedJobs.length}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {jobs.reduce((sum, job) => sum + ((job.progress as any)?.totalItems || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Documents processed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="active">Active Jobs</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="new">New Operation</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Active Jobs */}
          {activeJobs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Active Operations</CardTitle>
                <CardDescription>
                  Currently running bulk operations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {activeJobs.map((job) => (
                  <JobCard key={job.id} job={job} />
                ))}
              </CardContent>
            </Card>
          )}

          {/* Recent Completed Jobs */}
          {completedJobs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Completions</CardTitle>
                <CardDescription>
                  Recently completed operations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {completedJobs.slice(0, 5).map((job) => (
                  <JobCard key={job.id} job={job} />
                ))}
              </CardContent>
            </Card>
          )}

          {/* Failed Jobs */}
          {failedJobs.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Failed Operations</CardTitle>
                <CardDescription>
                  Operations that require attention
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {failedJobs.map((job) => (
                  <JobCard key={job.id} job={job} />
                ))}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="active" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Operations</CardTitle>
              <CardDescription>
                Monitor and manage currently running operations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {activeJobs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No active operations
                </div>
              ) : (
                activeJobs.map((job) => (
                  <JobCard key={job.id} job={job} showActions />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operation History</CardTitle>
              <CardDescription>
                View all past bulk operations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {jobs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No operations found
                </div>
              ) : (
                jobs.map((job) => (
                  <JobCard key={job.id} job={job} />
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="new" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <OperationCard
              title="Bulk Upload"
              description="Upload multiple documents at once"
              icon={<Upload className="h-6 w-6" />}
              onClick={() => {/* Handle bulk upload */}}
            />
            <OperationCard
              title="Bulk Processing"
              description="Process multiple documents with AI"
              icon={<FileText className="h-6 w-6" />}
              onClick={() => {/* Handle bulk processing */}}
            />
            <OperationCard
              title="Workflow Automation"
              description="Start workflows for multiple documents"
              icon={<Workflow className="h-6 w-6" />}
              onClick={() => {/* Handle workflow automation */}}
            />
            <OperationCard
              title="Bulk Signing"
              description="Send multiple documents for signing"
              icon={<PenTool className="h-6 w-6" />}
              onClick={() => {/* Handle bulk signing */}}
            />
            <OperationCard
              title="Bulk Export"
              description="Export multiple documents"
              icon={<Download className="h-6 w-6" />}
              onClick={() => {/* Handle bulk export */}}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

interface JobCardProps {
  job: any; // BulkOperationJob;
  showActions?: boolean;
}

function JobCard({ job, showActions = false }: JobCardProps) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          {getOperationIcon(job.operationType)}
          {getStatusIcon(job.status)}
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <h4 className="font-medium capitalize">{job.operationType} Operation</h4>
            <Badge className={getStatusColor(job.status)}>
              {job.status}
            </Badge>
          </div>
          <div className="text-sm text-muted-foreground">
            {job.progress?.processedItems || 0} of {job.progress?.totalItems || 0} items processed
            {(job.progress?.failedItems || 0) > 0 && (
              <span className="text-red-500 ml-2">
                ({job.progress?.failedItems} failed)
              </span>
            )}
          </div>
          <div className="text-xs text-muted-foreground">
            Started {job.startedAt ? formatDistanceToNow(new Date(job.startedAt)) : 'Unknown'} ago
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-4">
        <div className="w-32">
          <Progress value={job.progress?.percentage || 0} className="h-2" />
          <div className="text-xs text-center mt-1">{job.progress?.percentage || 0}%</div>
        </div>
        {showActions && (
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}

interface OperationCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
}

function OperationCard({ title, description, icon, onClick }: OperationCardProps) {
  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onClick}>
      <CardHeader>
        <div className="flex items-center space-x-2">
          {icon}
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
    </Card>
  );
}
