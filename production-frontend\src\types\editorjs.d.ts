/**
 * Type declarations for EditorJS packages
 */

declare module '@editorjs/embed' {
  import { BlockTool } from '@editorjs/editorjs'
  
  interface EmbedConfig {
    services?: {
      [key: string]: {
        regex: RegExp
        embedUrl: string
        html: string
        height?: number
        width?: number
        id?: (groups: string[]) => string
      }
    }
  }
  
  export default class Embed implements BlockTool {
    constructor(config: { data?: any; config?: EmbedConfig; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): any
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
  }
}

declare module '@editorjs/link' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class Link implements InlineTool {
    constructor(config: { api?: any })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get shortcut(): string
    static get title(): string
  }
}

declare module '@editorjs/marker' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class Marker implements InlineTool {
    constructor(config: { api?: any })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get shortcut(): string
    static get title(): string
  }
}

declare module '@editorjs/checklist' {
  import { BlockTool } from '@editorjs/editorjs'
  
  interface ChecklistData {
    items: Array<{
      text: string
      checked: boolean
    }>
  }
  
  export default class Checklist implements BlockTool {
    constructor(config: { data?: ChecklistData; config?: any; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): ChecklistData
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
    static get enableLineBreaks(): boolean
    static get sanitize(): any
  }
}

declare module '@editorjs/inline-code' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class InlineCode implements InlineTool {
    constructor(config: { api?: any })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get shortcut(): string
    static get title(): string
  }
}

declare module '@editorjs/quote' {
  import { BlockTool } from '@editorjs/editorjs'
  
  interface QuoteData {
    text: string
    caption: string
    alignment: 'left' | 'center'
  }
  
  export default class Quote implements BlockTool {
    constructor(config: { data?: QuoteData; config?: any; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): QuoteData
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
    static get sanitize(): any
    static get conversionConfig(): any
  }
}

declare module '@editorjs/code' {
  import { BlockTool } from '@editorjs/editorjs'
  
  interface CodeData {
    code: string
  }
  
  export default class Code implements BlockTool {
    constructor(config: { data?: CodeData; config?: any; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): CodeData
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
    static get sanitize(): any
    static get enableLineBreaks(): boolean
  }
}

declare module '@editorjs/delimiter' {
  import { BlockTool } from '@editorjs/editorjs'
  
  export default class Delimiter implements BlockTool {
    constructor(config: { data?: any; config?: any; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): any
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
  }
}

declare module '@editorjs/raw' {
  import { BlockTool } from '@editorjs/editorjs'
  
  interface RawData {
    html: string
  }
  
  export default class Raw implements BlockTool {
    constructor(config: { data?: RawData; config?: any; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): RawData
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
  }
}

declare module '@editorjs/warning' {
  import { BlockTool } from '@editorjs/editorjs'
  
  interface WarningData {
    title: string
    message: string
  }
  
  export default class Warning implements BlockTool {
    constructor(config: { data?: WarningData; config?: any; api?: any; readOnly?: boolean })
    render(): HTMLElement
    save(): WarningData
    static get toolbox(): { icon: string; title: string }
    static get isReadOnlySupported(): boolean
    static get sanitize(): any
  }
}

declare module '@editorjs/underline' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class Underline implements InlineTool {
    constructor(config: { api?: any })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get shortcut(): string
    static get title(): string
  }
}

declare module '@editorjs/strikethrough' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class Strikethrough implements InlineTool {
    constructor(config: { api?: any })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get shortcut(): string
    static get title(): string
  }
}

declare module '@editorjs/text-color' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class TextColor implements InlineTool {
    constructor(config: { api?: any; config?: { colorCollections?: string[] } })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get title(): string
  }
}

declare module '@editorjs/background-color' {
  import { InlineTool } from '@editorjs/editorjs'
  
  export default class BackgroundColor implements InlineTool {
    constructor(config: { api?: any; config?: { colorCollections?: string[] } })
    render(): HTMLElement
    surround(range: Range): void
    checkState(): boolean
    static get isInline(): boolean
    static get sanitize(): any
    static get title(): string
  }
}
