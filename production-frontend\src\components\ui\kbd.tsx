import React from 'react';
import { cn } from '@/lib/utils';

export interface KbdProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
}

export function Kbd({ children, className, ...props }: KbdProps) {
  return (
    <kbd
      className={cn(
        'inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-medium text-muted-foreground bg-muted border border-border rounded shadow-sm',
        className
      )}
      {...props}
    >
      {children}
    </kbd>
  );
}
