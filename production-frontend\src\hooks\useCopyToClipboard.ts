import { useState, useCallback } from 'react'

/**
 * Copy to Clipboard Hook
 * Provides functionality to copy text to clipboard with feedback
 */

export interface UseCopyToClipboardResult {
  copiedText: string | null
  copy: (text: string) => Promise<boolean>
  reset: () => void
  isCopied: boolean
}

export function useCopyToClipboard(): UseCopyToClipboardResult {
  const [copiedText, setCopiedText] = useState<string | null>(null)

  const copy = useCallback(async (text: string): Promise<boolean> => {
    if (!navigator?.clipboard) {
      console.warn('Clipboard not supported')
      return false
    }

    try {
      await navigator.clipboard.writeText(text)
      setCopiedText(text)
      return true
    } catch (error) {
      console.warn('Copy failed', error)
      setCopiedText(null)
      return false
    }
  }, [])

  const reset = useCallback(() => {
    setCopiedText(null)
  }, [])

  return {
    copiedText,
    copy,
    reset,
    isCopied: copiedText !== null,
  }
}
