'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useProject } from '@/hooks/projects/useProject';
import { useWorkflow } from '@/hooks/workflows/useWorkflows';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { WorkflowBuilder } from '@/components/workflows/workflow-builder';

export default function EditWorkflowPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();

  // Ensure params exists and get IDs
  if (!params || !params.projectId || !params.workflowId) {
    return <div>Loading...</div>;
  }

  const projectId = Array.isArray(params.projectId) ? params.projectId[0] : params.projectId;
  const workflowId = Array.isArray(params.workflowId) ? params.workflowId[0] : params.workflowId;

  const { project: _project } = useProject({ projectId });
  const { data: workflow, isLoading, error } = useWorkflow(workflowId);

  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  const [triggerType, setTriggerType] = useState('manual');
  const [isActive, setIsActive] = useState(true);
  const [workflowSteps, setWorkflowSteps] = useState<any[]>([]);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load workflow details',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  // Initialize form with workflow data when loaded
  useEffect(() => {
    if (workflow) {
      setWorkflowName(workflow.name);
      setWorkflowDescription(workflow.description || '');
      setTriggerType((workflow as any).triggerType || 'manual');
      setIsActive((workflow as any).isActive || true);
      setWorkflowSteps(workflow.steps || []);
    }
  }, [workflow]);

  const handleSave = async () => {
    if (!workflowName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Workflow name is required',
        variant: 'destructive',
      });
      return;
    }

    if (workflowSteps.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Workflow must have at least one step',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);

    try {
      // This would be an API call in a real application
      // const response = await fetch(`/api/workflows/${workflowId}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     name: workflowName,
      //     description: workflowDescription,
      //     triggerType,
      //     isActive,
      //     steps: workflowSteps,
      //   })
      // });

      // if (!response.ok) throw new Error('Failed to update workflow');

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Success',
        description: 'Workflow updated successfully',
      });

      router.push(`/projects/${projectId}/workflows/${workflowId}`);
    } catch (error) {
      console.error('Error updating workflow:', error);
      toast({
        title: 'Error',
        description: 'Failed to update workflow. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading workflow details...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <Button
          variant="ghost"
          size="sm"
          className="mb-2"
          onClick={() => router.push(`/projects/${projectId}/workflows/${workflowId}`)}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Workflow
        </Button>
        <h1 className="text-3xl font-bold">Edit Workflow</h1>
        <p className="text-muted-foreground">Project: {_project?.name}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="space-y-6 md:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Workflow Name</Label>
                <Input
                  id="name"
                  value={workflowName}
                  onChange={(e) => setWorkflowName(e.target.value)}
                  placeholder="Enter workflow name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={workflowDescription}
                  onChange={(e) => setWorkflowDescription(e.target.value)}
                  placeholder="Enter workflow description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="triggerType">Trigger Type</Label>
                <Select
                  value={triggerType}
                  onValueChange={setTriggerType}
                >
                  <SelectTrigger id="triggerType">
                    <SelectValue placeholder="Select trigger type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="manual">Manual</SelectItem>
                    <SelectItem value="document_upload">Document Upload</SelectItem>
                    <SelectItem value="document_update">Document Update</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={isActive}
                  onCheckedChange={setIsActive}
                />
                <Label htmlFor="active">Active</Label>
              </div>

              <div className="pt-4 space-y-2">
                <Button
                  className="w-full"
                  onClick={handleSave}
                  disabled={isSaving || !workflowName.trim() || workflowSteps.length === 0}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </Button>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push(`/projects/${projectId}/workflows/${workflowId}`)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Builder</CardTitle>
            </CardHeader>
            <CardContent>
              <WorkflowBuilder />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
