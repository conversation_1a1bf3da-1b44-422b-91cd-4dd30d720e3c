/**
 * Backend Types - Exact Mirror of Azure Functions Backend
 * These types MUST match the backend interfaces exactly
 */

// ============================================================================
// AUTHENTICATION & USER TYPES
// ============================================================================

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  organizationName?: string
  acceptTerms: boolean
}

export interface StandardToken {
  accessToken: string
  refreshToken?: string
  expiresAt: number
  userId: string
  email: string
  roles: string[]
  organizationIds?: string[]
  tenantId?: string
  scope?: string
  tokenType?: string
}

export interface UserContext {
  id: string
  email: string
  name?: string // Display name for the user
  firstName?: string
  lastName?: string
  displayName?: string
  avatar?: string // Backend uses 'avatar' not 'avatarUrl'
  profilePictureUrl?: string // For MSAL compatibility
  tenantId?: string
  organizationId?: string
  organizationIds?: string[]
  roles: string[]
  systemRoles?: string[]
  permissions?: string[]
  status?: string
  lastLoginAt?: string
  isEmailVerified?: boolean
  locale?: string
  createdAt?: string
  updatedAt?: string
}

// User interface that extends UserContext for frontend compatibility
export interface User extends UserContext {
  name: string // Computed from firstName + lastName or displayName
  timezone?: string
  language?: string
  theme?: 'light' | 'dark' | 'system'
  notifications?: NotificationPreferences
  preferences?: UserPreferences
  lastLoginAt?: string
  isActive: boolean
  emailVerified: boolean
  phoneNumber?: string
  phoneVerified?: boolean
  twoFactorEnabled?: boolean
  organizations?: UserOrganization[]
}

export interface UserOrganization {
  organizationId: string
  role: string
  permissions: string[]
  joinedAt: string
}

export interface NotificationPreferences {
  email: {
    enabled: boolean
    frequency: 'immediate' | 'daily' | 'weekly'
    types: string[]
  }
  inApp: {
    enabled: boolean
    types: string[]
  }
  push?: {
    enabled: boolean
    types: string[]
  }
}

export interface UserPreferences {
  dashboard: DashboardPreferences
  documents: DocumentPreferences
  collaboration: CollaborationPreferences
}

export interface DashboardPreferences {
  layout: 'grid' | 'list'
  itemsPerPage: number
  defaultView: string
  showWelcome: boolean
}

export interface DocumentPreferences {
  defaultView: 'list' | 'grid' | 'table'
  autoSave: boolean
  showPreview: boolean
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

export interface CollaborationPreferences {
  showPresence: boolean
  enableNotifications: boolean
  autoJoinSessions: boolean
}

export interface AuthResult {
  success: boolean
  user?: UserContext
  standardToken?: StandardToken
  error?: string
  authMethod?: 'jwt' | 'standard-token'
  securityEvent?: SecurityEvent
  correlationId?: string
}

export interface SecurityEvent {
  id: string
  type: string
  userId?: string
  organizationId?: string
  details?: Record<string, any>
  timestamp: string
}

// ============================================================================
// DOCUMENT TYPES
// ============================================================================

export const DocumentStatus = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  PROCESSED: 'processed',
  FAILED: 'failed',
  ARCHIVED: 'archived',
  UPLOADED: 'uploaded',
  UPLOADING: 'uploading',
  COMPLETED: 'completed',
  RUNNING: 'running'
} as const

export type DocumentStatus = typeof DocumentStatus[keyof typeof DocumentStatus]

export const DocumentType = {
  PDF: 'pdf',
  WORD: 'word',
  EXCEL: 'excel',
  POWERPOINT: 'powerpoint',
  IMAGE: 'image',
  TEXT: 'text',
  OTHER: 'other'
} as const

export type DocumentType = typeof DocumentType[keyof typeof DocumentType]

// Project visibility enum - ALIGNED WITH BACKEND
export enum ProjectVisibility {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  PUBLIC = 'PUBLIC'
}

// Workflow status enum - ALIGNED WITH BACKEND
export const WorkflowStatus = {
  DRAFT: 'DRAFT',
  ACTIVE: 'ACTIVE',
  PAUSED: 'PAUSED',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  FAILED: 'FAILED'
} as const

export type WorkflowStatus = typeof WorkflowStatus[keyof typeof WorkflowStatus]

// Template status enum - ALIGNED WITH BACKEND
export enum TemplateStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

// Template type enum - ALIGNED WITH BACKEND
export enum TemplateType {
  DOCUMENT = 'DOCUMENT',
  FORM = 'FORM',
  WORKFLOW = 'WORKFLOW',
  EMAIL = 'EMAIL',
  PROJECT = 'PROJECT'
}

// ============================================================================
// ORGANIZATION TYPES - ALIGNED WITH BACKEND
// ============================================================================

export interface Organization {
  id: string
  name: string
  description?: string
  domain?: string
  logo?: string
  tier?: 'FREE' | 'PROFESSIONAL' | 'ENTERPRISE'
  storageUsed?: number
  projectIds?: string[]
  memberIds?: string[]
  settings: OrganizationSettings
  subscription?: OrganizationSubscription
  billing?: OrganizationBilling
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  tenantId: string
}

export interface OrganizationSettings {
  allowPublicProjects: boolean
  requireApproval: boolean
  enableSSO: boolean
  defaultRole: string
  maxMembers: number
  maxFileSize?: number
  features?: {
    aiAnalysis?: boolean
    [key: string]: any
  }
}

export interface OrganizationSubscription {
  plan: string
  status: 'active' | 'cancelled' | 'expired'
  features: string[]
  limits: Record<string, number>
  renewsAt?: string
  cancelledAt?: string
}

export interface OrganizationBilling {
  customerId: string
  paymentMethod?: string
  billingEmail: string
  address?: BillingAddress
}

export interface BillingAddress {
  line1: string
  line2?: string
  city: string
  state: string
  postalCode: string
  country: string
}

export interface OrganizationMember {
  id: string
  userId: string
  organizationId: string
  role: string
  permissions: string[]
  status: 'active' | 'invited' | 'suspended'
  invitedBy?: string
  invitedAt?: string
  joinedAt?: string
  user: User
}

// ============================================================================
// TEAM TYPES - ALIGNED WITH BACKEND
// ============================================================================

export interface Team {
  id: string
  name: string
  description?: string
  organizationId: string
  projectId?: string
  settings: TeamSettings
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  tenantId: string
}

export interface TeamSettings {
  isPrivate: boolean
  allowSelfJoin: boolean
  requireApproval: boolean
  maxMembers: number
}

export interface TeamMember {
  id: string
  userId: string
  teamId: string
  role: string
  permissions: string[]
  addedAt: string
  addedBy: string
  isActive: boolean
  user: User
}

export interface DocumentMetadata {
  extracted: {
    title?: string
    author?: string
    subject?: string
    keywords?: string[]
    language?: string
    pageCount?: number
    wordCount?: number
    characterCount?: number
    creationDate?: string
    modificationDate?: string
  }
  computed: {
    searchableText?: string
    indexedKeywords?: string[]
    similarityHash?: string
    qualityScore?: number
    readabilityScore?: number
    sentimentScore?: number
  }
  custom: Record<string, any>
}

export interface ProcessingInfo {
  autoProcess: boolean
  extractText: boolean
  generateThumbnail: boolean
  textExtracted: boolean
  thumbnailGenerated: boolean
  aiProcessed: boolean
  ocrCompleted: boolean
  classificationCompleted: boolean
  lastProcessedAt?: string
  processingResults?: any
  errors?: string[]
}

export interface SharingInfo {
  isShared: boolean
  sharedCount: number
  publicLinkEnabled: boolean
  shareLinks?: DocumentShareLink[]
  permissions?: DocumentPermission[]
}

export interface DocumentShareLink {
  id: string
  url: string
  permissions: string[]
  createdBy: string
  createdAt: string
  expiresAt?: string
}

export interface DocumentPermission {
  userId: string
  permission: string
  grantedBy: string
  grantedAt: string
}

export interface ApprovalInfo {
  required: boolean
  approvers: string[]
  status: 'pending' | 'approved' | 'rejected'
  approvedBy?: string[]
  rejectedBy?: string[]
  comments?: ApprovalComment[]
}

export interface ApprovalComment {
  id: string
  userId: string
  comment: string
  action: 'approve' | 'reject' | 'request_changes'
  createdAt: string
}

export interface CollaborationInfo {
  collaborators: string[]
  commentsCount: number
  isLocked: boolean
  lockedBy?: string
  lockedAt?: string
}

export interface Document {
  id: string
  name: string
  originalName: string
  description?: string
  contentType: string
  size: number
  status: DocumentStatus
  type: DocumentType
  organizationId: string
  projectId?: string
  category?: string
  tags: string[]
  blobName: string
  blobPath: string
  downloadUrl?: string
  thumbnailUrl?: string
  version: number
  isLatestVersion: boolean
  parentDocumentId?: string
  metadata: DocumentMetadata
  processing: ProcessingInfo
  sharing: SharingInfo
  approval?: ApprovalInfo
  collaboration: CollaborationInfo
  createdBy: string
  uploadedBy?: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  tenantId: string
}

// ============================================================================
// PROJECT TYPES - ALIGNED WITH BACKEND
// ============================================================================

export enum ProjectStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
  DRAFT = 'draft',
  COMPLETED = 'completed'
}

export interface Project {
  id: string
  name: string
  description?: string
  visibility: ProjectVisibility
  organizationId: string
  ownerId: string
  status: 'active' | 'archived' | 'deleted'
  settings: ProjectSettings
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  tenantId: string
}

export interface ProjectSettings {
  allowPublicAccess: boolean
  requireApproval: boolean
  enableCollaboration: boolean
  defaultPermissions: string[]
  autoProcessing?: boolean
}

// ============================================================================
// WORKFLOW TYPES - ALIGNED WITH BACKEND
// ============================================================================

export interface Workflow {
  id: string
  name: string
  description?: string
  status: WorkflowStatus
  organizationId: string
  projectId?: string
  steps: WorkflowStep[]
  triggers: WorkflowTrigger[]
  settings: WorkflowSettings
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  tenantId: string
}

export interface WorkflowStep {
  id: string
  workflowId: string
  name: string
  description?: string
  type: string
  order: number
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  config: Record<string, any>
  position: { x: number; y: number }
  assigneeType: 'user' | 'role' | 'group'
  assigneeId?: string
  dueDate?: string
  options?: Record<string, any>
}

export interface WorkflowTrigger {
  id: string
  workflowId: string
  type: string
  config: Record<string, any>
  enabled: boolean
}

export interface WorkflowSettings {
  autoStart: boolean
  allowParallelExecution: boolean
  maxExecutions: number
  timeoutMinutes: number
}

// ============================================================================
// TEMPLATE TYPES - ALIGNED WITH BACKEND
// ============================================================================

export interface Template {
  id: string
  name: string
  description?: string
  type: TemplateType
  status: TemplateStatus
  organizationId: string
  projectId?: string
  content: TemplateContent
  fields: TemplateField[]
  settings: TemplateSettings
  tags: string[]
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedBy: string
  updatedAt: string
  tenantId: string
  isDefault?: boolean
  version?: number
}

export interface TemplateContent {
  sections: TemplateSection[]
  styles: Record<string, any>
  layout: string
}

export interface TemplateSection {
  id: string
  name: string
  type: string
  order: number
  fields: TemplateField[]
  config: Record<string, any>
}

export interface TemplateField {
  id: string
  name: string
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'checkbox' | 'textarea' | 'file' | 'richtext' | 'email' | 'signature' | 'table'
  label: string
  description?: string
  required?: boolean
  options?: string[]
  validation?: Record<string, any>
  defaultValue?: any
  helpText?: string
  placeholder?: string
}

export interface TemplateSettings {
  allowPublicAccess: boolean
  requireApproval: boolean
  enableVersioning: boolean
  autoSave: boolean
}

// ============================================================================
// AI/ML TYPES
// ============================================================================

export interface AIOperation {
  id: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  organizationId: string
  projectId?: string
  parameters: Record<string, any>
  results?: Record<string, any>
  progress: number
  createdBy: string
  createdAt: string
  updatedAt: string
  completedAt?: string
  error?: string
}

export interface AIModel {
  id: string
  name: string
  type: string
  status: 'training' | 'ready' | 'deployed' | 'failed'
  organizationId: string
  configuration: Record<string, any>
  metrics?: Record<string, any>
  createdBy: string
  createdAt: string
  updatedAt: string
}

// ============================================================================
// COLLABORATION TYPES
// ============================================================================

export interface CollaborationSession {
  id: string
  documentId: string
  organizationId: string
  projectId?: string
  name: string
  description?: string
  status: 'active' | 'paused' | 'ended'
  participants: SessionParticipant[]
  maxParticipants: number
  settings: SessionSettings
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedAt: string
  endedAt?: string
  tenantId: string
}

export interface SessionParticipant {
  userId: string
  role: string
  permissions: string[]
  joinedAt: string
  lastActivity: string
  cursor?: CursorPosition
  isActive: boolean
}

export interface CursorPosition {
  x: number
  y: number
  page?: number
  element?: string
}

export interface SessionSettings {
  allowAnonymous: boolean
  requireApproval: boolean
  enableVoiceChat: boolean
  enableVideoChat: boolean
  enableScreenShare: boolean
  autoSave: boolean
  conflictResolution: string
}

// ============================================================================
// STORAGE & DATA TYPES
// ============================================================================

export interface StorageOperation {
  id: string
  operationType: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  organizationId: string
  projectId?: string
  parameters: OperationParameters
  progress: OperationProgress
  results?: OperationResults
  metadata: Record<string, any>
  createdBy: string
  createdAt: string
  updatedAt: string
  completedAt?: string
  tenantId: string
}

export interface OperationParameters {
  bulkConfig?: BulkOperationConfig
  migrationConfig?: MigrationConfig
  encryptionConfig?: EncryptionConfig
  searchConfig?: SearchConfig
  fileProcessingConfig?: FileProcessingConfig
  customParameters?: Record<string, any>
}

export interface OperationProgress {
  totalItems: number
  processedItems: number
  failedItems: number
  percentage: number
  currentItem?: string
  estimatedTimeRemaining?: number
}

export interface OperationResults {
  successCount: number
  failureCount: number
  warnings: string[]
  errors: string[]
  summary: Record<string, any>
}

export interface BulkOperationConfig {
  batchSize: number
  maxConcurrency: number
  retryAttempts: number
  validateBeforeProcess: boolean
}

export interface MigrationConfig {
  sourceType: string
  targetType: string
  preserveMetadata: boolean
  preservePermissions: boolean
}

export interface EncryptionConfig {
  algorithm: string
  keySize: number
  encryptMetadata: boolean
}

export interface SearchConfig {
  indexName: string
  fields: string[]
  filters: Record<string, any>
}

export interface FileProcessingConfig {
  extractText: boolean
  generateThumbnails: boolean
  runOCR: boolean
  detectLanguage: boolean
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean
  data: T
  message?: string
  timestamp: string
  requestId?: string
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
  requestId?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
  success: boolean
  timestamp: string
}
