import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { SearchResult } from '@/services/search-service';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  FileText, 
  FolderKanban, 
  Users, 
  Calendar, 
  Tag, 
  Clock, 
  ExternalLink,
  MessageSquare,
  ArrowUpRight
} from 'lucide-react';

interface SearchResultsProps {
  results: SearchResult[];
  isLoading?: boolean;
  onResultClick?: (result: SearchResult, position: number) => void;
  contextualResponse?: string;
  suggestedQueries?: string[];
  onSuggestedQueryClick?: (query: string) => void;
}

export function SearchResults({
  results,
  isLoading = false,
  onResultClick,
  contextualResponse,
  suggestedQueries,
  onSuggestedQueryClick
}: SearchResultsProps) {
  const router = useRouter();
  
  // Handle result click
  const handleResultClick = (result: SearchResult, position: number) => {
    if (onResultClick) {
      onResultClick(result, position);
    }
    
    // Navigate based on result type
    switch (result.type) {
      case 'document':
        router.push(`/documents/${result.id}`);
        break;
      case 'project':
        router.push(`/projects/${result.id}`);
        break;
      case 'organization':
        router.push(`/organizations/${result.id}`);
        break;
      case 'user':
        router.push(`/users/${result.id}`);
        break;
      default:
        // For other types, just log
        console.log('Clicked result:', result);
    }
  };
  
  // Get icon for result type
  const getResultIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'project':
        return <FolderKanban className="h-4 w-4" />;
      case 'organization':
        return <Users className="h-4 w-4" />;
      case 'user':
        return <Avatar className="h-6 w-6"><AvatarFallback>U</AvatarFallback></Avatar>;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };
  
  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-1/3" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3" />
            </CardContent>
            <CardFooter>
              <Skeleton className="h-4 w-1/4" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }
  
  // No results
  if (results.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">No results found</p>
        {suggestedQueries && suggestedQueries.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Try searching for:</p>
            <div className="flex flex-wrap gap-2 justify-center">
              {suggestedQueries.map((query, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => onSuggestedQueryClick && onSuggestedQueryClick(query)}
                >
                  {query}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Contextual Response */}
      {contextualResponse && (
        <Card className="bg-muted/50 border-muted">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">AI-Generated Response</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <p>{contextualResponse}</p>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Suggested Queries */}
      {suggestedQueries && suggestedQueries.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="text-sm font-medium text-muted-foreground mr-2 pt-1">
            Related:
          </span>
          {suggestedQueries.map((query, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => onSuggestedQueryClick && onSuggestedQueryClick(query)}
            >
              {query}
            </Button>
          ))}
        </div>
      )}
      
      {/* Results */}
      <div className="space-y-4">
        {results.map((result, index) => (
          <Card 
            key={result.id}
            className="overflow-hidden hover:border-primary/50 transition-colors cursor-pointer"
            onClick={() => handleResultClick(result, index)}
          >
            <CardHeader className="pb-2">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="flex items-center gap-1">
                    {getResultIcon(result.type)}
                    <span className="capitalize">{result.type}</span>
                  </Badge>
                  {result.metadata?.tags && result.metadata.tags.length > 0 && (
                    <div className="flex items-center gap-1">
                      <Tag className="h-3 w-3 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {result.metadata.tags.slice(0, 2).join(', ')}
                        {result.metadata.tags.length > 2 && '...'}
                      </span>
                    </div>
                  )}
                </div>
                <Badge variant="secondary">
                  {Math.round(result.relevanceScore * 100)}% match
                </Badge>
              </div>
              <CardTitle className="text-lg">{result.title}</CardTitle>
              {result.metadata?.description && (
                <CardDescription>{result.metadata.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {result.highlights && Object.keys(result.highlights).length > 0 && (
                <div className="text-sm text-muted-foreground">
                  {Object.entries(result.highlights).map(([field, fragments], i) => (
                    <div key={i} className="mb-1">
                      {(fragments as string[]).map((fragment: string, j: number) => (
                        <p 
                          key={j} 
                          className="mb-1"
                          dangerouslySetInnerHTML={{ __html: fragment }}
                        />
                      ))}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="text-xs text-muted-foreground flex items-center gap-4">
              {result.metadata?.createdAt && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(result.metadata.createdAt)}</span>
                </div>
              )}
              {result.metadata?.updatedAt && (
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>Updated {formatDate(result.metadata.updatedAt)}</span>
                </div>
              )}
              <div className="ml-auto">
                <ArrowUpRight className="h-3 w-3" />
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default SearchResults;
