/**
 * Compliance and Audit System
 * Comprehensive compliance tracking, audit trails, and regulatory reporting
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { aiServices } from '../shared/services/ai-services';
import { signalREnhanced } from '../shared/services/signalr';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Compliance interfaces
export interface CompliancePolicy {
  id: string;
  name: string;
  description: string;
  framework: ComplianceFramework;
  requirements: ComplianceRequirement[];
  controls: ComplianceControl[];
  applicableDocumentTypes: string[];
  applicableDepartments: string[];
  riskLevel: RiskLevel;
  status: PolicyStatus;
  version: string;
  effectiveDate: string;
  expirationDate?: string;
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ComplianceRequirement {
  id: string;
  code: string;
  title: string;
  description: string;
  mandatory: boolean;
  category: RequirementCategory;
  controls: string[];
  evidence: EvidenceRequirement[];
  frequency: AssessmentFrequency;
  lastAssessment?: string;
  nextAssessment: string;
  status: ComplianceStatus;
  riskRating: RiskLevel;
}

export interface ComplianceControl {
  id: string;
  code: string;
  title: string;
  description: string;
  type: ControlType;
  implementation: ControlImplementation;
  testing: ControlTesting;
  effectiveness: ControlEffectiveness;
  owner: string;
  lastReview: string;
  nextReview: string;
  status: ControlStatus;
}

export interface AuditTrail {
  id: string;
  entityType: string;
  entityId: string;
  action: AuditAction;
  userId: string;
  userName: string;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  sessionId: string;
  changes: AuditChange[];
  metadata: Record<string, any>;
  riskLevel: RiskLevel;
  complianceImpact: ComplianceImpact;
  organizationId: string;
}

export interface ComplianceAssessment {
  id: string;
  policyId: string;
  assessmentType: AssessmentType;
  scope: AssessmentScope;
  assessor: string;
  startDate: string;
  endDate?: string;
  status: AssessmentStatus;
  findings: ComplianceFinding[];
  recommendations: ComplianceRecommendation[];
  overallScore: number;
  riskRating: RiskLevel;
  reportUrl?: string;
  organizationId: string;
}

export interface ComplianceReport {
  id: string;
  title: string;
  framework: ComplianceFramework;
  reportType: ReportType;
  period: ReportPeriod;
  scope: ReportScope;
  generatedBy: string;
  generatedAt: string;
  status: ReportStatus;
  summary: ComplianceSummary;
  sections: ReportSection[];
  attachments: string[];
  organizationId: string;
}

// Enums
export enum ComplianceFramework {
  SOX = 'sox',
  GDPR = 'gdpr',
  HIPAA = 'hipaa',
  PCI_DSS = 'pci_dss',
  ISO_27001 = 'iso_27001',
  SOC2 = 'soc2',
  CCPA = 'ccpa',
  FERPA = 'ferpa',
  FISMA = 'fisma',
  NIST = 'nist',
  CUSTOM = 'custom'
}

export enum ComplianceStatus {
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant',
  PARTIALLY_COMPLIANT = 'partially_compliant',
  NOT_ASSESSED = 'not_assessed',
  IN_PROGRESS = 'in_progress',
  REMEDIATION_REQUIRED = 'remediation_required'
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum PolicyStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  ARCHIVED = 'archived'
}

export enum RequirementCategory {
  GOVERNANCE = 'governance',
  RISK_MANAGEMENT = 'risk_management',
  COMPLIANCE = 'compliance',
  SECURITY = 'security',
  PRIVACY = 'privacy',
  OPERATIONAL = 'operational'
}

export enum AssessmentFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  ANNUALLY = 'annually',
  ON_DEMAND = 'on_demand'
}

export enum ControlType {
  PREVENTIVE = 'preventive',
  DETECTIVE = 'detective',
  CORRECTIVE = 'corrective',
  COMPENSATING = 'compensating'
}

export enum ControlStatus {
  IMPLEMENTED = 'implemented',
  PARTIALLY_IMPLEMENTED = 'partially_implemented',
  NOT_IMPLEMENTED = 'not_implemented',
  UNDER_REVIEW = 'under_review'
}

export enum AuditAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  APPROVE = 'approve',
  REJECT = 'reject',
  SIGN = 'sign',
  DOWNLOAD = 'download',
  SHARE = 'share',
  EXPORT = 'export',
  LOGIN = 'login',
  LOGOUT = 'logout'
}

export enum AssessmentType {
  SELF_ASSESSMENT = 'self_assessment',
  INTERNAL_AUDIT = 'internal_audit',
  EXTERNAL_AUDIT = 'external_audit',
  AUTOMATED = 'automated',
  CONTINUOUS = 'continuous'
}

export enum AssessmentStatus {
  PLANNED = 'planned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue'
}

export enum ReportType {
  COMPLIANCE_DASHBOARD = 'compliance_dashboard',
  AUDIT_REPORT = 'audit_report',
  RISK_ASSESSMENT = 'risk_assessment',
  CONTROL_EFFECTIVENESS = 'control_effectiveness',
  VIOLATION_SUMMARY = 'violation_summary',
  REGULATORY_FILING = 'regulatory_filing'
}

// Supporting interfaces
export interface EvidenceRequirement {
  type: string;
  description: string;
  required: boolean;
  format: string[];
  retention: number;
}

export interface ControlImplementation {
  description: string;
  procedures: string[];
  tools: string[];
  responsible: string[];
  frequency: string;
}

export interface ControlTesting {
  method: string;
  frequency: string;
  lastTested: string;
  nextTest: string;
  results: TestResult[];
}

export interface ControlEffectiveness {
  rating: 'effective' | 'partially_effective' | 'ineffective';
  lastAssessment: string;
  issues: string[];
  improvements: string[];
}

export interface AuditChange {
  field: string;
  oldValue: any;
  newValue: any;
  changeType: 'create' | 'update' | 'delete';
}

export interface ComplianceImpact {
  frameworks: ComplianceFramework[];
  requirements: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
}

export interface AssessmentScope {
  departments: string[];
  systems: string[];
  processes: string[];
  timeframe: {
    start: string;
    end: string;
  };
}

export interface ComplianceFinding {
  id: string;
  requirementId: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'accepted';
  description: string;
  evidence: string[];
  remediation: string;
  dueDate: string;
  assignee: string;
}

export interface ComplianceRecommendation {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  description: string;
  implementation: string;
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
  timeline: string;
}

export interface TestResult {
  date: string;
  result: 'pass' | 'fail' | 'partial';
  findings: string[];
  tester: string;
}

export interface ReportPeriod {
  start: string;
  end: string;
  frequency: 'monthly' | 'quarterly' | 'annually';
}

export interface ReportScope {
  frameworks: ComplianceFramework[];
  departments: string[];
  systems: string[];
}

export interface ComplianceSummary {
  overallScore: number;
  totalRequirements: number;
  compliantRequirements: number;
  nonCompliantRequirements: number;
  riskDistribution: Record<RiskLevel, number>;
  trendAnalysis: TrendData[];
}

export interface ReportSection {
  id: string;
  title: string;
  content: string;
  charts: ChartData[];
  tables: TableData[];
  attachments: string[];
}

export interface TrendData {
  period: string;
  score: number;
  violations: number;
  improvements: number;
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'donut';
  title: string;
  data: any[];
  labels: string[];
}

export interface TableData {
  title: string;
  headers: string[];
  rows: any[][];
}

export interface ReportStatus {
  status: 'generating' | 'completed' | 'failed';
  progress: number;
  message?: string;
}

class ComplianceAuditSystem {
  private readonly CACHE_TTL = 3600; // 1 hour

  /**
   * Create compliance policy
   */
  async createCompliancePolicy(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    if (request.method === 'OPTIONS') {
      const preflightResponse = handlePreflight(request);
      if (preflightResponse) {
        return preflightResponse;
      }
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        name: Joi.string().min(1).max(255).required(),
        description: Joi.string().max(1000).required(),
        framework: Joi.string().valid(...Object.values(ComplianceFramework)).required(),
        requirements: Joi.array().items(Joi.object({
          code: Joi.string().required(),
          title: Joi.string().required(),
          description: Joi.string().required(),
          mandatory: Joi.boolean().default(true),
          category: Joi.string().valid(...Object.values(RequirementCategory)).required(),
          frequency: Joi.string().valid(...Object.values(AssessmentFrequency)).default(AssessmentFrequency.ANNUALLY),
          riskRating: Joi.string().valid(...Object.values(RiskLevel)).default(RiskLevel.MEDIUM)
        })).min(1).required(),
        applicableDocumentTypes: Joi.array().items(Joi.string()).default([]),
        applicableDepartments: Joi.array().items(Joi.string()).default([]),
        riskLevel: Joi.string().valid(...Object.values(RiskLevel)).default(RiskLevel.MEDIUM),
        effectiveDate: Joi.string().isoDate().required(),
        expirationDate: Joi.string().isoDate().optional()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkCompliancePermission(
        authResult.user?.organizationId || '',
        authResult.user?.id || '',
        'create_compliance_policy'
      );

      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Insufficient permissions to create compliance policies' }
        }, request);
      }

      const policyId = uuidv4();
      const now = new Date().toISOString();

      // Process requirements
      const requirements = value.requirements.map((req: any) => ({
        id: uuidv4(),
        code: req.code,
        title: req.title,
        description: req.description,
        mandatory: req.mandatory,
        category: req.category,
        controls: [],
        evidence: [],
        frequency: req.frequency,
        nextAssessment: this.calculateNextAssessment(req.frequency, value.effectiveDate),
        status: ComplianceStatus.NOT_ASSESSED,
        riskRating: req.riskRating
      }));

      const policy: CompliancePolicy = {
        id: policyId,
        name: value.name,
        description: value.description,
        framework: value.framework,
        requirements,
        controls: [],
        applicableDocumentTypes: value.applicableDocumentTypes,
        applicableDepartments: value.applicableDepartments,
        riskLevel: value.riskLevel,
        status: PolicyStatus.DRAFT,
        version: '1.0.0',
        effectiveDate: value.effectiveDate,
        expirationDate: value.expirationDate,
        organizationId: authResult.user?.organizationId || '',
        createdBy: authResult.user.id,
        createdAt: now,
        updatedAt: now
      };

      // Store policy
      await db.createItem('compliance-policies', policy);

      // Cache policy
      await redis.setex(`compliance-policy:${policyId}`, this.CACHE_TTL, JSON.stringify(policy));

      // Log audit event
      await this.logAuditEvent(authResult.user, AuditAction.CREATE, 'compliance-policy', policyId, {
        policyName: policy.name,
        framework: policy.framework,
        requirementCount: requirements.length
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Compliance.PolicyCreated',
        subject: `compliance/policies/${policyId}`,
        data: {
          policyId,
          name: policy.name,
          framework: policy.framework,
          organizationId: policy.organizationId,
          createdBy: authResult.user.id
        }
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: policy
      }, request);

    } catch (error) {
      logger.error('Error creating compliance policy', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Generate compliance report
   */
  async generateComplianceReport(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        title: Joi.string().min(1).max(255).required(),
        framework: Joi.string().valid(...Object.values(ComplianceFramework)).required(),
        reportType: Joi.string().valid(...Object.values(ReportType)).required(),
        period: Joi.object({
          start: Joi.string().isoDate().required(),
          end: Joi.string().isoDate().required(),
          frequency: Joi.string().valid('monthly', 'quarterly', 'annually').required()
        }).required(),
        scope: Joi.object({
          frameworks: Joi.array().items(Joi.string().valid(...Object.values(ComplianceFramework))).default([]),
          departments: Joi.array().items(Joi.string()).default([]),
          systems: Joi.array().items(Joi.string()).default([])
        }).default({})
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkCompliancePermission(
        authResult.user?.organizationId || '',
        authResult.user?.id || '',
        'generate_compliance_report'
      );

      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Insufficient permissions to generate compliance reports' }
        }, request);
      }

      const reportId = uuidv4();
      const now = new Date().toISOString();

      // Generate report data
      const summary = await this.generateComplianceSummary(
        authResult.user?.organizationId || '',
        value.framework,
        value.period
      );

      const sections = await this.generateReportSections(
        authResult.user?.organizationId || '',
        value.reportType,
        value.scope,
        value.period
      );

      const report: ComplianceReport = {
        id: reportId,
        title: value.title,
        framework: value.framework,
        reportType: value.reportType,
        period: value.period,
        scope: value.scope,
        generatedBy: authResult.user.id,
        generatedAt: now,
        status: {
          status: 'completed',
          progress: 100
        },
        summary,
        sections,
        attachments: [],
        organizationId: authResult.user?.organizationId || ''
      };

      // Store report
      await db.createItem('compliance-reports', report);

      // Log audit event
      await this.logAuditEvent(authResult.user, AuditAction.CREATE, 'compliance-report', reportId, {
        reportTitle: report.title,
        framework: report.framework,
        reportType: report.reportType
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          reportId,
          status: 'completed',
          summary,
          downloadUrl: `/api/compliance/reports/${reportId}/download`,
          message: 'Compliance report generated successfully'
        }
      }, request);

    } catch (error) {
      logger.error('Error generating compliance report', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Private helper methods
  private async checkCompliancePermission(organizationId: string, userId: string, permission: string): Promise<boolean> {
    try {
      const userRole = await db.queryItems('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.userId = @userId',
        [organizationId, userId]
      );

      if (userRole.length === 0) {
        return false;
      }

      const member = userRole[0] as any;
      const allowedRoles = ['admin', 'compliance_officer', 'audit_manager'];

      return allowedRoles.includes(member.role) || member.permissions?.includes(permission);
    } catch (error) {
      logger.error('Error checking compliance permission', { organizationId, userId, permission, error });
      return false;
    }
  }

  private calculateNextAssessment(frequency: AssessmentFrequency, effectiveDate: string): string {
    const date = new Date(effectiveDate);
    
    switch (frequency) {
      case AssessmentFrequency.DAILY:
        date.setDate(date.getDate() + 1);
        break;
      case AssessmentFrequency.WEEKLY:
        date.setDate(date.getDate() + 7);
        break;
      case AssessmentFrequency.MONTHLY:
        date.setMonth(date.getMonth() + 1);
        break;
      case AssessmentFrequency.QUARTERLY:
        date.setMonth(date.getMonth() + 3);
        break;
      case AssessmentFrequency.ANNUALLY:
        date.setFullYear(date.getFullYear() + 1);
        break;
      default:
        date.setFullYear(date.getFullYear() + 1);
    }
    
    return date.toISOString();
  }

  private async logAuditEvent(
    user: any,
    action: AuditAction,
    entityType: string,
    entityId: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const auditEntry: AuditTrail = {
        id: uuidv4(),
        entityType,
        entityId,
        action,
        userId: user.id,
        userName: user.displayName || user.email,
        timestamp: new Date().toISOString(),
        ipAddress: 'unknown', // Would be extracted from request
        userAgent: 'unknown', // Would be extracted from request
        sessionId: 'unknown', // Would be extracted from session
        changes: [],
        metadata,
        riskLevel: RiskLevel.LOW,
        complianceImpact: {
          frameworks: [],
          requirements: [],
          severity: 'low',
          description: `${action} action on ${entityType}`
        },
        organizationId: user.organizationId
      };

      await db.createItem('audit-trails', auditEntry);
    } catch (error) {
      logger.error('Failed to log audit event', { userId: user.id, action, entityType, entityId, error });
    }
  }

  private async generateComplianceSummary(
    _organizationId: string,
    _framework: ComplianceFramework,
    _period: ReportPeriod
  ): Promise<ComplianceSummary> {
    // Implementation for compliance summary generation
    return {
      overallScore: 85,
      totalRequirements: 50,
      compliantRequirements: 42,
      nonCompliantRequirements: 8,
      riskDistribution: {
        [RiskLevel.LOW]: 30,
        [RiskLevel.MEDIUM]: 15,
        [RiskLevel.HIGH]: 4,
        [RiskLevel.CRITICAL]: 1
      },
      trendAnalysis: []
    };
  }

  private async generateReportSections(
    _organizationId: string,
    _reportType: ReportType,
    _scope: ReportScope,
    _period: ReportPeriod
  ): Promise<ReportSection[]> {
    // Implementation for report sections generation
    return [
      {
        id: uuidv4(),
        title: 'Executive Summary',
        content: 'Overall compliance status and key findings...',
        charts: [],
        tables: [],
        attachments: []
      }
    ];
  }
}

// Create instance
const complianceSystem = new ComplianceAuditSystem();

// Register HTTP functions
app.http('compliance-policy-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'compliance/policies',
  handler: (request, context) => complianceSystem.createCompliancePolicy(request, context)
});

app.http('compliance-report-generate', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'compliance/reports/generate',
  handler: (request, context) => complianceSystem.generateComplianceReport(request, context)
});
