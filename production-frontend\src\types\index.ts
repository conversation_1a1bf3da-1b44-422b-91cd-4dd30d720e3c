/**
 * Main Type Definitions
 * Central export for all application types
 */

// Re-export all types from individual modules
export * from './user'
export * from './document'
export * from './project'
export * from './template'
export * from './workflow'
export * from './api'
export * from './tenant'
export * from './team'

// Selective exports from role to avoid conflicts with user module
export type {
  Role,
  RoleScope,
  Permission,
  RoleAssignment,
  CreateRoleRequest,
  UpdateRoleRequest,
  RoleHierarchy,
  PermissionAction,
  PermissionResource,
  SystemRole,
  TeamRole,
  RoleFilter as RoleSearchQuery
} from './role'

// Additional exports for missing types
export type {
  DocumentFilters,
  DocumentProcessingStatus,
  ProcessingOptions,
  ShareData,
  ProjectMemberData,
  OrganizationMember,
  DashboardPreferences,
  CreateProjectData,
  TemplateVariable,
  FieldType,
  FieldOption,
  TemplatePreviewResult
} from './store'

// Organization creation data
export interface CreateOrganizationData {
  name: string
  description?: string
  logo?: string
  settings?: Partial<OrganizationSettings>
}

// Export constants (avoiding duplicates)
export { FIELD_TYPES, DocumentStatus } from './store'

// Selective exports from store to avoid conflicts
export type {
  // Base store types
  BaseStore,
  StoreActions,
  StoreMiddleware,
  PersistOptions,
  DevtoolsOptions,
  StoreSelector,
  StoreSubscription,
  StoreErrorHandler,

  // Store interfaces (avoiding conflicts with other modules)
  AuthStore,
  DashboardStore,
  DocumentStore,
  ProjectStore,
  TemplateStore,
  WorkflowStore,
  NotificationStore,
  PreferencesStore,
  OrganizationStore,
  AppStore
} from './store'

// Common utility types
export type ID = string
export type Timestamp = string // ISO 8601 format
export type Email = string
export type URL = string

// Generic API response wrapper
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  timestamp: Timestamp
}

// Generic paginated response
export interface PaginatedResponse<T = any> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
  message?: string
  success: boolean
  timestamp: Timestamp
}

// Generic error response
export interface ErrorResponse {
  error: {
    code: string
    message: string
    details?: Record<string, any>
  }
  success: false
  timestamp: Timestamp
}

// Loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error'

// Generic async state
export interface AsyncState<T = any> {
  data: T | null
  loading: boolean
  error: string | null
  lastUpdated?: Timestamp
}

// File upload state
export interface FileUploadState {
  file: File | null
  progress: number
  status: 'idle' | 'uploading' | 'success' | 'error'
  error?: string
}

// Notification types
export interface Notification {
  id: ID
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Timestamp
  read: boolean
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
  variant?: 'primary' | 'secondary' | 'destructive'
}

// Theme types
export type Theme = 'light' | 'dark' | 'system'

// Language types
export interface Language {
  code: string
  name: string
}

// Note: Permission and Role types are now exported from './role' module above

// Organization types
export interface Organization {
  id: ID
  name: string
  description?: string
  logo?: URL
  settings: OrganizationSettings
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface OrganizationSettings {
  allowPublicProjects: boolean
  defaultProjectVisibility: 'private' | 'internal' | 'public'
  documentRetentionDays: number
  maxFileSize: number
  allowedFileTypes: string[]
}

// Tenant types (for multi-tenancy)
export interface Tenant {
  id: ID
  name: string
  domain?: string
  settings: TenantSettings
  subscription: TenantSubscription
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface TenantSettings {
  features: string[]
  limits: {
    users: number
    projects: number
    storage: number // in bytes
    apiCalls: number
  }
  branding: {
    logo?: URL
    primaryColor?: string
    secondaryColor?: string
  }
}

export interface TenantSubscription {
  plan: string
  status: 'active' | 'cancelled' | 'expired' | 'trial'
  expiresAt?: Timestamp
  features: string[]
}

// Analytics types
export interface AnalyticsData {
  metrics: Record<string, number>
  charts: ChartData[]
  period: {
    start: Timestamp
    end: Timestamp
  }
}

export interface ChartData {
  id: string
  type: 'line' | 'bar' | 'pie' | 'area'
  title: string
  data: ChartDataPoint[]
}

export interface ChartDataPoint {
  label: string
  value: number
  timestamp?: Timestamp
}

// Search types
export interface SearchQuery {
  query: string
  filters?: Record<string, any>
  sort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  pagination?: {
    page: number
    pageSize: number
  }
}

export interface SearchResult<T = any> {
  items: T[]
  total: number
  facets?: Record<string, SearchFacet[]>
  suggestions?: string[]
}

export interface SearchFacet {
  value: string
  count: number
}

// Activity types
export interface Activity {
  id: ID
  type: string
  actor: {
    id: ID
    name: string
    avatar?: URL
  }
  target: {
    type: string
    id: ID
    name: string
  }
  description: string
  metadata?: Record<string, any>
  timestamp: Timestamp
}

// Comment types
export interface Comment {
  id: ID
  content: string
  author: {
    id: ID
    name: string
    avatar?: URL
  }
  parentId?: ID
  replies?: Comment[]
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Tag types
export interface Tag {
  id: ID
  name: string
  color?: string
  description?: string
}

// Attachment types
export interface Attachment {
  id: ID
  name: string
  url: URL
  size: number
  mimeType: string
  uploadedBy: ID
  uploadedAt: Timestamp
}

// Collaboration types
export interface CollaborationSession {
  id: ID
  participants: Participant[]
  document?: ID
  project?: ID
  startedAt: Timestamp
  endedAt?: Timestamp
}

export interface Participant {
  userId: ID
  name: string
  avatar?: URL
  cursor?: {
    x: number
    y: number
  }
  selection?: {
    start: number
    end: number
  }
  lastSeen: Timestamp
}

// Webhook types
export interface Webhook {
  id: ID
  url: URL
  events: string[]
  secret: string
  active: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Integration types
export interface Integration {
  id: ID
  name: string
  type: string
  config: Record<string, any>
  enabled: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Audit log types
export interface AuditLog {
  id: ID
  action: string
  actor: {
    id: ID
    name: string
    type: 'user' | 'system'
  }
  target: {
    type: string
    id: ID
    name?: string
  }
  changes?: Record<string, {
    old: any
    new: any
  }>
  metadata?: Record<string, any>
  timestamp: Timestamp
  ipAddress?: string
  userAgent?: string
}

// Form types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file' | 'date'
  required?: boolean
  placeholder?: string
  options?: { label: string; value: any }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

export interface FormData {
  [key: string]: any
}

export interface FormErrors {
  [key: string]: string
}

// Component props types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'destructive' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
}

export interface InputProps extends BaseComponentProps {
  type?: string
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  error?: string
  disabled?: boolean
}

// Event types for real-time communication
export interface RealtimeEvent {
  type: string
  payload: any
  timestamp: Timestamp
  userId?: ID
}

// Feature flag types
export interface FeatureFlag {
  key: string
  enabled: boolean
  rolloutPercentage?: number
  conditions?: Record<string, any>
}

// Configuration types
export interface AppConfig {
  apiUrl: string
  wsUrl: string
  features: Record<string, boolean>
  limits: Record<string, number>
  integrations: Record<string, any>
}
