"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import dynamic from 'next/dynamic';

// Dynamically import PDF components to avoid SSR issues
const Document = dynamic(() => import('react-pdf').then(mod => ({ default: mod.Document })), {
  ssr: false,
  loading: () => <div className="flex justify-center p-8">Loading PDF...</div>
});

const Page = dynamic(() => import('react-pdf').then(mod => ({ default: mod.Page })), {
  ssr: false
});

// Dynamic import for pdfjs
let pdfjs: any = null;
if (typeof window !== 'undefined') {
  import('react-pdf').then(mod => {
    pdfjs = mod.pdfjs;
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  });
}
import { useResizeObserver } from "@/hooks/use-resize-observer";
import { Card, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorDisplay } from "@/components/ui/error-display";
import { cn } from "@/lib/utils";
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Search,
  Download,
  Printer,
  Maximize,
  Minimize,
  FileText,
  Image,
  X,
} from "lucide-react";
import { documentSearchService, DocumentSearchResult } from "@/services/document-search-service";

// PDF.js worker is set dynamically above

export interface AdvancedDocumentViewerProps {
  url: string;
  title?: string;
  className?: string;
  onAnnotationChange?: (annotations: any[]) => void;
  initialAnnotations?: any[];
  allowAnnotations?: boolean;
  allowDownload?: boolean;
  allowPrint?: boolean;
  allowFullScreen?: boolean;
  allowSearch?: boolean;
  allowZoom?: boolean;
  allowRotate?: boolean;
  allowPageNavigation?: boolean;
  initialPage?: number;
  initialZoom?: number;
  initialRotation?: number;
  onPageChange?: (page: number) => void;
  onDocumentLoad?: (document: any) => void;
}

export function AdvancedDocumentViewer({
  url,
  title,
  className,
  onAnnotationChange: _onAnnotationChange,
  initialAnnotations = [],
  allowAnnotations: _allowAnnotations = true,
  allowDownload = true,
  allowPrint = true,
  allowFullScreen = true,
  allowSearch = true,
  allowZoom = true,
  allowRotate = true,
  allowPageNavigation = true,
  initialPage = 1,
  initialZoom = 1,
  initialRotation = 0,
  onPageChange,
  onDocumentLoad,
}: AdvancedDocumentViewerProps) {
  // Document state
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(initialPage);
  const [zoom, setZoom] = useState(initialZoom);
  const [rotation, setRotation] = useState(initialRotation);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [searchResults, setSearchResults] = useState<DocumentSearchResult[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [isSearching, setIsSearching] = useState(false);
  const [pdfDocumentInstance, setPdfDocumentInstance] = useState<any>(null);
  const [_annotations, _setAnnotations] = useState(initialAnnotations);
  const [activeTab, setActiveTab] = useState<"document" | "thumbnails">("document");
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<Error | null>(null);

  // Refs
  const documentRef = useRef<HTMLDivElement>(null);
  const { ref: containerRef, width } = useResizeObserver<HTMLDivElement>();

  // Handle document load success
  const onDocumentLoadSuccess = useCallback((pdfDocument: any) => {
    setNumPages(pdfDocument.numPages);
    setIsLoading(false);
    setLoadError(null);
    setPdfDocumentInstance(pdfDocument);
    if (onDocumentLoad) {
      onDocumentLoad(pdfDocument);
    }
  }, [onDocumentLoad]);

  // Handle document load error
  const onDocumentLoadError = useCallback((error: Error) => {
    setIsLoading(false);

    // Create a more user-friendly error message based on the error
    let userFriendlyError: Error;

    if (error.message.includes('Failed to fetch')) {
      userFriendlyError = new Error(
        'Failed to fetch the document. Please check your internet connection and try again.'
      );
    } else if (error.message.includes('password')) {
      userFriendlyError = new Error(
        'This document is password protected. Please provide the correct password to view it.'
      );
    } else if (error.message.includes('corrupt') || error.message.includes('invalid')) {
      userFriendlyError = new Error(
        'The document appears to be corrupted or in an invalid format. Please try with a different file.'
      );
    } else {
      userFriendlyError = new Error(
        `Failed to load the document: ${error.message}`
      );
    }

    setLoadError(userFriendlyError);
    console.error("Error loading document:", error);
  }, []);

  // Handle page change
  const changePage = useCallback((offset: number) => {
    if (!numPages) return;

    const newPageNumber = pageNumber + offset;
    if (newPageNumber >= 1 && newPageNumber <= numPages) {
      setPageNumber(newPageNumber);
      if (onPageChange) {
        onPageChange(newPageNumber);
      }
    }
  }, [numPages, pageNumber, onPageChange]);

  // Handle zoom change
  const changeZoom = useCallback((amount: number) => {
    setZoom((prevZoom) => {
      const newZoom = Math.max(0.1, Math.min(5, prevZoom + amount));
      return newZoom;
    });
  }, []);

  // Handle rotation change
  const changeRotation = useCallback(() => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  }, []);

  // Handle full screen toggle
  const toggleFullScreen = useCallback(() => {
    if (!containerRef.current) return;

    if (!isFullScreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [isFullScreen]);

  // Listen for fullscreen change
  useEffect(() => {
    const handleFullScreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullScreenChange);
    return () => {
      document.removeEventListener("fullscreenchange", handleFullScreenChange);
    };
  }, []);

  // State for search errors
  const [searchError, setSearchError] = useState<Error | null>(null);

  // Handle search
  const handleSearch = useCallback(async () => {
    if (!searchText || !documentRef.current) return;

    setIsSearching(true);
    setSearchError(null); // Clear previous errors

    try {
      let results: DocumentSearchResult[] = [];

      if (!pdfDocumentInstance) {
        // Handle the error properly with user feedback
        throw new Error('PDF document instance not available for search. Please wait for the document to fully load before searching.');
      }

      // Use client-side search with PDF.js
      const searchResponse = await documentSearchService.searchDocuments({
        query: searchText,
        documentId: (document as any).id,
        filters: {}
      });
      results = searchResponse.results;

      setSearchResults(results);
      setCurrentSearchIndex(0);

      // Navigate to the first result
      if (results.length > 0) {
        setPageNumber(results[0].page || 1);
      } else {
        // No results found - this is not an error, but we should inform the user
        setSearchError(new Error(`No results found for "${searchText}"`));
      }
    } catch (error) {
      console.error('Error searching document:', error);
      setSearchError(error instanceof Error ? error : new Error('An unknown error occurred during search'));
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [searchText, pdfDocumentInstance]);

  // Handle next search result
  const goToNextSearchResult = useCallback(() => {
    if (searchResults.length === 0) return;

    const nextIndex = (currentSearchIndex + 1) % searchResults.length;
    setCurrentSearchIndex(nextIndex);
    setPageNumber(searchResults[nextIndex].page || 1);
  }, [searchResults, currentSearchIndex]);

  // Handle previous search result
  const goToPreviousSearchResult = useCallback(() => {
    if (searchResults.length === 0) return;

    const prevIndex = (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    setCurrentSearchIndex(prevIndex);
    setPageNumber(searchResults[prevIndex].page || 1);
  }, [searchResults, currentSearchIndex]);

  // Handle download
  const handleDownload = useCallback(() => {
    const link = document.createElement("a");
    link.href = url;
    link.download = title || "document.pdf";
    link.click();
  }, [url, title]);

  // Handle print
  const handlePrint = useCallback(() => {
    const printWindow = window.open(url);
    if (printWindow) {
      printWindow.addEventListener("load", () => {
        printWindow.print();
      });
    }
  }, [url]);

  // Handle annotation change - kept for future use
  // const handleAnnotationChange = useCallback((newAnnotations: any[]) => {
  //   setAnnotations(newAnnotations);
  //   if (onAnnotationChange) {
  //     onAnnotationChange(newAnnotations);
  //   }
  // }, [onAnnotationChange]);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchText("");
    setSearchResults([]);
    setCurrentSearchIndex(0);
    setSearchError(null); // Also clear any search errors
  }, []);

  return (
    <Card className={cn("overflow-hidden", className)} ref={containerRef as any}>
      <CardHeader className="p-4 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {title || "Document Viewer"}
          </CardTitle>
          <div className="flex items-center gap-2">
            {allowSearch && (
              <div className="flex flex-col">
                <div className="relative">
                  <Input
                    type="text"
                    placeholder="Search..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    className={cn(
                      "h-8 w-40 md:w-60",
                      searchError && "border-red-500 focus-visible:ring-red-500"
                    )}
                    onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                  />
                  {searchText && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-8 top-0 h-8 w-8"
                      onClick={clearSearch}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-8 w-8"
                    onClick={handleSearch}
                    disabled={!searchText || isSearching}
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </div>

                {/* Search error message */}
                {searchError && (
                  <div className="text-xs text-red-500 mt-1 max-w-xs">
                    {searchError.message}
                  </div>
                )}

                {/* Search results count and navigation */}
                {searchResults.length > 0 && !searchError && (
                  <div className="flex items-center justify-between mt-1">
                    <div className="text-xs text-muted-foreground">
                      {searchResults.length} {searchResults.length === 1 ? 'result' : 'results'} found
                      {searchResults.length > 1 && ` (${currentSearchIndex + 1}/${searchResults.length})`}
                    </div>

                    {/* Result navigation buttons */}
                    {searchResults.length > 1 && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={goToPreviousSearchResult}
                          title="Previous result"
                        >
                          <ChevronLeft className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={goToNextSearchResult}
                          title="Next result"
                        >
                          <ChevronRight className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
            {allowDownload && (
              <Button variant="outline" size="icon" onClick={handleDownload} className="h-8 w-8">
                <Download className="h-4 w-4" />
              </Button>
            )}
            {allowPrint && (
              <Button variant="outline" size="icon" onClick={handlePrint} className="h-8 w-8">
                <Printer className="h-4 w-4" />
              </Button>
            )}
            {allowFullScreen && (
              <Button variant="outline" size="icon" onClick={toggleFullScreen} className="h-8 w-8">
                {isFullScreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex flex-col h-full">
        <div className="border-b px-4 py-2">
          <TabsList>
            <TabsTrigger value="document" className="flex items-center gap-1">
              <FileText className="h-4 w-4" />
              Document
            </TabsTrigger>
            <TabsTrigger value="thumbnails" className="flex items-center gap-1">
              <Image className="h-4 w-4" />
              Thumbnails
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="document" className="flex-1 overflow-auto p-4">
          <div ref={documentRef} className="flex justify-center">
            {isLoading ? (
              <LoadingState title="Loading document..." variant="spinner" />
            ) : loadError ? (
              <ErrorDisplay
                title="Failed to load document"
                description={loadError?.message || "There was an error loading the document. Please try again."}
                error={loadError}
                variant="card"
                actions={
                  <div className="flex gap-2 mt-4">
                    <Button
                      variant="outline"
                      onClick={() => window.location.reload()}
                    >
                      Reload Page
                    </Button>
                    {allowDownload && (
                      <Button
                        variant="outline"
                        onClick={handleDownload}
                      >
                        Download Document
                      </Button>
                    )}
                  </div>
                }
              />
            ) : (
              <Document
                file={url}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                loading={<LoadingState variant="spinner" />}
                error={<ErrorDisplay variant="card" />}
              >
                <Page
                  pageNumber={pageNumber}
                  width={width ? width * 0.8 : undefined}
                  scale={zoom}
                  rotate={rotation}
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                />
              </Document>
            )}
          </div>
        </TabsContent>

        <TabsContent value="thumbnails" className="flex-1 overflow-auto p-4">
          {isLoading ? (
            <LoadingState title="Loading thumbnails..." variant="spinner" />
          ) : loadError ? (
            <ErrorDisplay
              title="Failed to load document"
              description={loadError?.message || "There was an error loading the document. Please try again."}
              error={loadError}
              variant="card"
              actions={
                <div className="flex gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    Reload Page
                  </Button>
                  {allowDownload && (
                    <Button
                      variant="outline"
                      onClick={handleDownload}
                    >
                      Download Document
                    </Button>
                  )}
                </div>
              }
            />
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {numPages && Array.from(new Array(numPages), (_, index) => (
                <div
                  key={`thumbnail-${index + 1}`}
                  className={cn(
                    "cursor-pointer border rounded overflow-hidden",
                    pageNumber === index + 1 && "ring-2 ring-primary"
                  )}
                  onClick={() => {
                    setPageNumber(index + 1);
                    setActiveTab("document");
                    if (onPageChange) {
                      onPageChange(index + 1);
                    }
                  }}
                >
                  <Document file={url} loading={<LoadingState variant="skeleton" />}>
                    <Page
                      pageNumber={index + 1}
                      width={150}
                      renderTextLayer={false}
                      renderAnnotationLayer={false}
                    />
                  </Document>
                  <div className="text-center py-1 text-xs bg-muted">Page {index + 1}</div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <CardFooter className="p-2 border-t flex justify-between items-center">
        <div className="flex items-center gap-2">
          {allowPageNavigation && numPages && (
            <>
              <Button
                variant="outline"
                size="icon"
                onClick={() => changePage(-1)}
                disabled={pageNumber <= 1}
                className="h-8 w-8"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Page {pageNumber} of {numPages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => changePage(1)}
                disabled={pageNumber >= (numPages || 0)}
                className="h-8 w-8"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>

        <div className="flex items-center gap-2">
          {allowZoom && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => changeZoom(-0.1)}
                disabled={zoom <= 0.1}
                className="h-8 w-8"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Slider
                value={[zoom * 100]}
                min={10}
                max={500}
                step={10}
                className="w-24"
                onValueChange={(value) => setZoom(value[0] / 100)}
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => changeZoom(0.1)}
                disabled={zoom >= 5}
                className="h-8 w-8"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
          )}

          {allowRotate && (
            <Button
              variant="outline"
              size="icon"
              onClick={changeRotation}
              className="h-8 w-8"
            >
              <RotateCw className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
