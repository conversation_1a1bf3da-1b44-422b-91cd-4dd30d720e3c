/**
 * API Types and Interfaces
 */

import type { ID, Timestamp } from './index'

// HTTP Methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'

// API Request configuration
export interface ApiRequestConfig {
  method: HttpMethod
  url: string
  data?: any
  params?: Record<string, any>
  headers?: Record<string, string>
  timeout?: number
  retries?: number
  cache?: boolean
  signal?: AbortSignal
  responseType?: 'json' | 'blob' | 'text' | 'arraybuffer'
}

// API Response wrapper
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  timestamp: Timestamp
  requestId?: string
}

// Paginated API response
export interface PaginatedApiResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }
}

// API Error response
export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  field?: string
  timestamp: Timestamp
  requestId?: string
  traceId?: string
}

export interface ApiErrorResponse {
  error: ApiError
  success: false
  timestamp: Timestamp
  requestId?: string
}

// Validation error
export interface ValidationError {
  field: string
  message: string
  code: string
  value?: any
}

export interface ValidationErrorResponse extends ApiErrorResponse {
  error: ApiError & {
    validationErrors: ValidationError[]
  }
}

// API client configuration
export interface ApiClientConfig {
  baseURL: string
  timeout: number
  retries: number
  retryDelay: number
  headers: Record<string, string>
  interceptors: {
    request: RequestInterceptor[]
    response: ResponseInterceptor[]
  }
}

// Interceptor types
export type RequestInterceptor = (config: ApiRequestConfig) => ApiRequestConfig | Promise<ApiRequestConfig>
export type ResponseInterceptor = (response: any) => any | Promise<any>

// Request/Response logging
export interface ApiLog {
  id: string
  method: HttpMethod
  url: string
  requestHeaders: Record<string, string>
  requestBody?: any
  responseStatus: number
  responseHeaders: Record<string, string>
  responseBody?: any
  duration: number
  timestamp: Timestamp
  error?: string
}

// Rate limiting
export interface RateLimit {
  limit: number
  remaining: number
  reset: number
  retryAfter?: number
}

// Cache configuration
export interface CacheConfig {
  ttl: number // Time to live in seconds
  key: string
  tags?: string[]
  invalidateOn?: string[]
}

// File upload types
export interface FileUploadRequest {
  file: File
  fileName?: string
  contentType?: string
  metadata?: Record<string, any>
  onProgress?: (progress: number) => void
}

export interface FileUploadResponse {
  id: ID
  fileName: string
  originalName: string
  size: number
  contentType: string
  url: string
  metadata?: Record<string, any>
  uploadedAt: Timestamp
}

export interface MultipartUploadRequest {
  files: File[]
  metadata?: Record<string, any>
  onProgress?: (progress: number) => void
  onFileProgress?: (fileIndex: number, progress: number) => void
}

export interface MultipartUploadResponse {
  files: FileUploadResponse[]
  totalSize: number
  uploadedAt: Timestamp
}

// Batch operations
export interface BatchRequest<T = any> {
  operations: BatchOperation<T>[]
  atomic?: boolean // All operations succeed or all fail
}

export interface BatchOperation<T = any> {
  id: string
  method: HttpMethod
  url: string
  data?: T
}

export interface BatchResponse<T = any> {
  results: BatchResult<T>[]
  success: boolean
  timestamp: Timestamp
}

export interface BatchResult<T = any> {
  id: string
  success: boolean
  data?: T
  error?: ApiError
}

// WebSocket types
export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnect: boolean
  reconnectInterval: number
  maxReconnectAttempts: number
  heartbeat: boolean
  heartbeatInterval: number
}

export interface WebSocketMessage {
  type: string
  payload: any
  timestamp: Timestamp
  id?: string
}

export interface WebSocketEvent {
  type: 'open' | 'close' | 'error' | 'message'
  data?: any
  error?: Error
  timestamp: Timestamp
}

// Server-Sent Events
export interface SSEConfig {
  url: string
  withCredentials: boolean
  headers?: Record<string, string>
  retry: boolean
  retryInterval: number
}

export interface SSEEvent {
  type: string
  data: any
  id?: string
  retry?: number
  timestamp: Timestamp
}

// GraphQL types (if used)
export interface GraphQLRequest {
  query: string
  variables?: Record<string, any>
  operationName?: string
}

export interface GraphQLResponse<T = any> {
  data?: T
  errors?: GraphQLError[]
  extensions?: Record<string, any>
}

export interface GraphQLError {
  message: string
  locations?: Array<{
    line: number
    column: number
  }>
  path?: Array<string | number>
  extensions?: Record<string, any>
}

// API endpoint definitions
export interface ApiEndpoint {
  path: string
  method: HttpMethod
  description?: string
  parameters?: ApiParameter[]
  requestBody?: ApiSchema
  responses: Record<string, ApiResponse>
  security?: string[]
  tags?: string[]
}

export interface ApiParameter {
  name: string
  in: 'path' | 'query' | 'header' | 'cookie'
  required: boolean
  schema: ApiSchema
  description?: string
}

export interface ApiSchema {
  type: string
  properties?: Record<string, ApiSchema>
  items?: ApiSchema
  required?: string[]
  example?: any
  description?: string
}

// API versioning
export interface ApiVersion {
  version: string
  deprecated: boolean
  deprecationDate?: Timestamp
  supportedUntil?: Timestamp
  changelog?: string
}

// API metrics
export interface ApiMetrics {
  endpoint: string
  method: HttpMethod
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  errorRate: number
  lastUpdated: Timestamp
}

// Health check
export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy'
  version: string
  timestamp: Timestamp
  checks: HealthCheckItem[]
}

export interface HealthCheckItem {
  name: string
  status: 'pass' | 'fail' | 'warn'
  duration?: number
  message?: string
  details?: Record<string, any>
}

// API documentation
export interface ApiDocumentation {
  title: string
  version: string
  description: string
  baseUrl: string
  endpoints: ApiEndpoint[]
  schemas: Record<string, ApiSchema>
  securitySchemes: Record<string, SecurityScheme>
}

export interface SecurityScheme {
  type: 'apiKey' | 'http' | 'oauth2' | 'openIdConnect'
  scheme?: string
  bearerFormat?: string
  flows?: OAuthFlows
  openIdConnectUrl?: string
}

export interface OAuthFlows {
  implicit?: OAuthFlow
  password?: OAuthFlow
  clientCredentials?: OAuthFlow
  authorizationCode?: OAuthFlow
}

export interface OAuthFlow {
  authorizationUrl?: string
  tokenUrl?: string
  refreshUrl?: string
  scopes: Record<string, string>
}

// Request context
export interface RequestContext {
  userId?: ID
  organizationId?: ID
  tenantId?: ID
  sessionId?: string
  requestId: string
  userAgent?: string
  ipAddress?: string
  timestamp: Timestamp
}

// API client hooks
export interface UseApiOptions<T = any> {
  enabled?: boolean
  retry?: number
  retryDelay?: number
  cache?: boolean
  cacheTime?: number
  staleTime?: number
  refetchOnWindowFocus?: boolean
  refetchOnReconnect?: boolean
  onSuccess?: (data: T) => void
  onError?: (error: ApiError) => void
}

export interface UseApiResult<T = any> {
  data: T | null
  loading: boolean
  error: ApiError | null
  refetch: () => Promise<void>
  mutate: (data: T) => void
}

export interface UseMutationOptions<TData = any, TVariables = any> {
  onSuccess?: (data: TData, variables: TVariables) => void
  onError?: (error: ApiError, variables: TVariables) => void
  onSettled?: (data: TData | null, error: ApiError | null, variables: TVariables) => void
}

export interface UseMutationResult<TData = any, TVariables = any> {
  mutate: (variables: TVariables) => Promise<TData>
  mutateAsync: (variables: TVariables) => Promise<TData>
  data: TData | null
  loading: boolean
  error: ApiError | null
  reset: () => void
}

// Query key factory
export type QueryKey = readonly unknown[]

export interface QueryKeyFactory {
  all: QueryKey
  lists: () => QueryKey
  list: (filters?: Record<string, any>) => QueryKey
  details: () => QueryKey
  detail: (id: ID) => QueryKey
}

// Infinite query
export interface InfiniteQueryOptions<T = any> extends UseApiOptions<T> {
  getNextPageParam?: (lastPage: T, allPages: T[]) => any
  getPreviousPageParam?: (firstPage: T, allPages: T[]) => any
}

export interface InfiniteQueryResult<T = any> {
  data: {
    pages: T[]
    pageParams: any[]
  } | null
  loading: boolean
  error: ApiError | null
  fetchNextPage: () => Promise<void>
  fetchPreviousPage: () => Promise<void>
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFetchingNextPage: boolean
  isFetchingPreviousPage: boolean
}
