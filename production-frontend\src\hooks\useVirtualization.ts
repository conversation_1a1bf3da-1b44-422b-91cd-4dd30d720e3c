import { useState, useEffect, useCallback, useMemo } from 'react'

/**
 * Virtualization Hook
 * Optimizes rendering of large lists by only rendering visible items
 */

export interface UseVirtualizationOptions {
  itemHeight: number
  containerHeight: number
  overscan?: number // Number of items to render outside visible area
  scrollElement?: HTMLElement | null
}

export interface VirtualItem {
  index: number
  start: number
  end: number
  size: number
}

export interface UseVirtualizationResult {
  virtualItems: VirtualItem[]
  totalSize: number
  scrollToIndex: (index: number) => void
  scrollToOffset: (offset: number) => void
  measureElement: (element: HTMLElement | null) => void
}

export function useVirtualization(
  itemCount: number,
  options: UseVirtualizationOptions
): UseVirtualizationResult {
  const { itemHeight, containerHeight, overscan = 5, scrollElement } = options
  
  const [scrollTop, setScrollTop] = useState(0)
  const [isScrolling, setIsScrolling] = useState(false)

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const start = Math.floor(scrollTop / itemHeight)
    const end = Math.min(
      itemCount - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    )
    
    return {
      start: Math.max(0, start - overscan),
      end: Math.min(itemCount - 1, end + overscan)
    }
  }, [scrollTop, itemHeight, containerHeight, itemCount, overscan])

  // Generate virtual items
  const virtualItems = useMemo(() => {
    const items: VirtualItem[] = []
    
    for (let i = visibleRange.start; i <= visibleRange.end; i++) {
      items.push({
        index: i,
        start: i * itemHeight,
        end: (i + 1) * itemHeight,
        size: itemHeight
      })
    }
    
    return items
  }, [visibleRange, itemHeight])

  const totalSize = itemCount * itemHeight

  // Handle scroll events
  useEffect(() => {
    const element = scrollElement || window
    let timeoutId: NodeJS.Timeout

    const handleScroll = () => {
      const scrollTop = scrollElement 
        ? scrollElement.scrollTop 
        : window.pageYOffset || document.documentElement.scrollTop

      setScrollTop(scrollTop)
      setIsScrolling(true)

      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        setIsScrolling(false)
      }, 150)
    }

    element.addEventListener('scroll', handleScroll, { passive: true })
    
    return () => {
      element.removeEventListener('scroll', handleScroll)
      clearTimeout(timeoutId)
    }
  }, [scrollElement])

  const scrollToIndex = useCallback((index: number) => {
    const offset = index * itemHeight
    const element = scrollElement || window

    if (scrollElement) {
      scrollElement.scrollTop = offset
    } else {
      window.scrollTo(0, offset)
    }
  }, [itemHeight, scrollElement])

  const scrollToOffset = useCallback((offset: number) => {
    const element = scrollElement || window

    if (scrollElement) {
      scrollElement.scrollTop = offset
    } else {
      window.scrollTo(0, offset)
    }
  }, [scrollElement])

  const measureElement = useCallback((element: HTMLElement | null) => {
    // Measure element height for dynamic sizing
    if (element) {
      const rect = element.getBoundingClientRect();
      const measuredHeight = rect.height;

      // Store measured height for this element
      // This could be enhanced with a height cache for better performance
      if (measuredHeight > 0) {
        // Update the item height if it's different from the default
        // For now, we'll use the measured height as a baseline
        console.debug('Measured element height:', measuredHeight);
      }
    }
  }, [itemHeight])

  return {
    virtualItems,
    totalSize,
    scrollToIndex,
    scrollToOffset,
    measureElement,
  }
}

/**
 * Grid virtualization hook for 2D virtualization
 */
export interface UseGridVirtualizationOptions {
  rowHeight: number
  columnWidth: number
  containerHeight: number
  containerWidth: number
  overscan?: number
}

export interface GridVirtualItem {
  rowIndex: number
  columnIndex: number
  x: number
  y: number
  width: number
  height: number
}

export function useGridVirtualization(
  rowCount: number,
  columnCount: number,
  options: UseGridVirtualizationOptions
) {
  const { 
    rowHeight, 
    columnWidth, 
    containerHeight, 
    containerWidth, 
    overscan = 5 
  } = options
  
  const [scrollTop, setScrollTop] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)

  // Calculate visible ranges
  const visibleRowRange = useMemo(() => {
    const start = Math.floor(scrollTop / rowHeight)
    const end = Math.min(
      rowCount - 1,
      Math.ceil((scrollTop + containerHeight) / rowHeight)
    )
    
    return {
      start: Math.max(0, start - overscan),
      end: Math.min(rowCount - 1, end + overscan)
    }
  }, [scrollTop, rowHeight, containerHeight, rowCount, overscan])

  const visibleColumnRange = useMemo(() => {
    const start = Math.floor(scrollLeft / columnWidth)
    const end = Math.min(
      columnCount - 1,
      Math.ceil((scrollLeft + containerWidth) / columnWidth)
    )
    
    return {
      start: Math.max(0, start - overscan),
      end: Math.min(columnCount - 1, end + overscan)
    }
  }, [scrollLeft, columnWidth, containerWidth, columnCount, overscan])

  // Generate virtual items
  const virtualItems = useMemo(() => {
    const items: GridVirtualItem[] = []
    
    for (let rowIndex = visibleRowRange.start; rowIndex <= visibleRowRange.end; rowIndex++) {
      for (let columnIndex = visibleColumnRange.start; columnIndex <= visibleColumnRange.end; columnIndex++) {
        items.push({
          rowIndex,
          columnIndex,
          x: columnIndex * columnWidth,
          y: rowIndex * rowHeight,
          width: columnWidth,
          height: rowHeight
        })
      }
    }
    
    return items
  }, [visibleRowRange, visibleColumnRange, rowHeight, columnWidth])

  const totalHeight = rowCount * rowHeight
  const totalWidth = columnCount * columnWidth

  return {
    virtualItems,
    totalHeight,
    totalWidth,
    visibleRowRange,
    visibleColumnRange,
  }
}
