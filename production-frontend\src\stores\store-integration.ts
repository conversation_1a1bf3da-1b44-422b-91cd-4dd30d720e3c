/**
 * Store Integration Manager
 * Manages cross-store communication and synchronization
 */

import { 
  useAuthStore,
  useDocumentStore,
  useProjectStore,
  useWorkflowStore,
  useOrganizationStore,
  useCollaborationStore,
  useAIStore,
  useNotificationStore,
  useTenantStore,
  useTemplateStore,
  useDashboardStore,
  usePreferencesStore
} from './index'

// Store registry for centralized management
export const storeRegistry = {
  auth: useAuthStore,
  document: useDocumentStore,
  project: useProjectStore,
  workflow: useWorkflowStore,
  organization: useOrganizationStore,
  collaboration: useCollaborationStore,
  ai: useAIStore,
  notification: useNotificationStore,
  tenant: useTenantStore,
  template: useTemplateStore,
  dashboard: useDashboardStore,
  preferences: usePreferencesStore,
} as const

// Store integration manager
export class StoreIntegrationManager {
  private static instance: StoreIntegrationManager
  private subscriptions: Array<() => void> = []
  private isInitialized = false

  static getInstance(): StoreIntegrationManager {
    if (!StoreIntegrationManager.instance) {
      StoreIntegrationManager.instance = new StoreIntegrationManager()
    }
    return StoreIntegrationManager.instance
  }

  // Initialize store integrations
  initialize() {
    if (this.isInitialized) return

    this.setupAuthIntegration()
    this.setupOrganizationIntegration()
    this.setupCollaborationIntegration()
    this.setupNotificationIntegration()
    this.setupDocumentIntegration()
    this.setupProjectIntegration()
    this.setupWorkflowIntegration()
    this.setupAIIntegration()

    this.isInitialized = true
  }

  // Cleanup all subscriptions
  cleanup() {
    this.subscriptions.forEach(unsubscribe => unsubscribe())
    this.subscriptions = []
    this.isInitialized = false
  }

  // Setup authentication store integration
  private setupAuthIntegration() {
    // Track previous authentication state
    let previousIsAuthenticated = useAuthStore.getState().isAuthenticated

    // When user logs in/out, sync other stores
    const unsubscribe = useAuthStore.subscribe((state) => {
      const currentIsAuthenticated = state.isAuthenticated

      if (currentIsAuthenticated && !previousIsAuthenticated) {
        // User logged in - initialize user-specific stores
        this.initializeUserStores()
      } else if (!currentIsAuthenticated && previousIsAuthenticated) {
        // User logged out - cleanup stores
        this.cleanupUserStores()
      }

      previousIsAuthenticated = currentIsAuthenticated
    })
    this.subscriptions.push(unsubscribe)

    // Sync organization context
    let previousOrganizationId = useAuthStore.getState().user?.organizationId

    const orgUnsubscribe = useAuthStore.subscribe((state) => {
      const currentOrganizationId = state.user?.organizationId

      if (currentOrganizationId && currentOrganizationId !== previousOrganizationId) {
        useOrganizationStore.getState().setCurrentOrganization(currentOrganizationId)
      }

      previousOrganizationId = currentOrganizationId
    })
    this.subscriptions.push(orgUnsubscribe)
  }

  // Setup organization store integration
  private setupOrganizationIntegration() {
    // Track previous organization
    let previousOrganization = useOrganizationStore.getState().currentOrganization

    // When organization changes, update related stores
    const unsubscribe = useOrganizationStore.subscribe((state) => {
      const currentOrganization = state.currentOrganization

      if (currentOrganization && currentOrganization !== previousOrganization) {
        // Update document store filters
        useDocumentStore.getState().setFilters({ organizationId: currentOrganization.id })

        // Update project store filters
        useProjectStore.getState().fetchProjects()

        // Update AI store context
        useAIStore.getState().listModels(currentOrganization.id)
      }

      previousOrganization = currentOrganization
    })
    this.subscriptions.push(unsubscribe)
  }

  // Setup collaboration store integration
  private setupCollaborationIntegration() {
    // Track previous collaboration session
    let previousSession = useCollaborationStore.getState().currentSession

    // When collaboration session changes, update document locks
    const unsubscribe = useCollaborationStore.subscribe((state) => {
      const currentSession = state.currentSession

      if (currentSession && currentSession !== previousSession) {
        // Update document store with collaboration context
        useDocumentStore.getState().setCollaborationContext(currentSession)
      }

      previousSession = currentSession
    })
    this.subscriptions.push(unsubscribe)
  }

  // Setup notification store integration
  private setupNotificationIntegration() {
    // Listen for errors from other stores and create notifications
    Object.entries(storeRegistry).forEach(([storeName, store]) => {
      if (storeName === 'notification') return

      // Track previous error state
      let previousError = (store.getState() as any).error

      const unsubscribe = store.subscribe((state: any) => {
        const currentError = state.error

        if (currentError && currentError !== previousError) {
          useNotificationStore.getState().addNotification({
            type: 'error',
            title: `${storeName.charAt(0).toUpperCase()}${storeName.slice(1)} Error`,
            message: currentError,
          })
        }

        previousError = currentError
      })
      this.subscriptions.push(unsubscribe)
    })
  }

  // Setup document store integration
  private setupDocumentIntegration() {
    // Track previous documents
    let previousDocuments = useDocumentStore.getState().documents

    // When document is processed, update AI store
    const unsubscribe = useDocumentStore.subscribe((state) => {
      const currentDocuments = state.documents

      const newlyProcessed = currentDocuments.filter((doc: any) =>
        doc.status === 'processed' &&
        !previousDocuments?.find((prev: any) => prev.id === doc.id && prev.status === 'processed')
      )

      newlyProcessed.forEach((doc: any) => {
        useNotificationStore.getState().addNotification({
          type: 'success',
          title: 'Document Processed',
          message: `${doc.name} has been successfully processed.`,
        })
      })

      previousDocuments = currentDocuments
    })
    this.subscriptions.push(unsubscribe)
  }

  // Setup project store integration
  private setupProjectIntegration() {
    // Track previous selected project
    let previousSelectedProject = useProjectStore.getState().selectedProject

    // When project changes, update related stores
    const unsubscribe = useProjectStore.subscribe((state) => {
      const selectedProject = state.selectedProject

      if (selectedProject && selectedProject !== previousSelectedProject) {
        // Update document store to show project documents
        useDocumentStore.getState().setFilters({ projectId: selectedProject.id })

        // Update workflow store to show project workflows
        useWorkflowStore.getState().fetchWorkflows()

        // Update template store to show project templates
        useTemplateStore.getState().fetchTemplates({})
      }

      previousSelectedProject = selectedProject
    })
    this.subscriptions.push(unsubscribe)
  }

  // Setup workflow store integration
  private setupWorkflowIntegration() {
    // Track previous executions
    let previousExecutions = useWorkflowStore.getState().executions || {}

    // When workflow execution completes, update related stores
    const unsubscribe = useWorkflowStore.subscribe((state) => {
      const currentExecutions = state.executions || {}

      Object.entries(currentExecutions).forEach(([workflowId, workflowExecutions]: [string, any]) => {
        const previousWorkflowExecutions = previousExecutions[workflowId] || []

        const newlyCompleted = (workflowExecutions as any[]).filter((exec: any) =>
          exec.status === 'completed' &&
          !previousWorkflowExecutions.find((prev: any) => prev.id === exec.id && prev.status === 'completed')
        )

        newlyCompleted.forEach((exec: any) => {
          useNotificationStore.getState().addNotification({
            type: 'success',
            title: 'Workflow Completed',
            message: `Workflow execution ${exec.id} has completed successfully.`,
          })
        })
      })

      previousExecutions = currentExecutions
    })
    this.subscriptions.push(unsubscribe)
  }

  // Setup AI store integration
  private setupAIIntegration() {
    // Track previous operations
    let previousOperations = useAIStore.getState().operations || []

    // When AI operation completes, update related stores
    const unsubscribe = useAIStore.subscribe((state) => {
      const currentOperations = state.operations || []

      const newlyCompleted = currentOperations.filter((op: any) =>
        op.status === 'completed' &&
        !previousOperations?.find((prev: any) => prev.id === op.id && prev.status === 'completed')
      )

      newlyCompleted.forEach((op: any) => {
        useNotificationStore.getState().addNotification({
          type: 'success',
          title: 'AI Operation Completed',
          message: `${op.type} operation has completed successfully.`,
        })

        // If it's a document analysis, update document store
        if (op.type === 'DOCUMENT_ANALYSIS' && op.documentId) {
          useDocumentStore.getState().invalidateDocument(op.documentId)
        }
      })

      previousOperations = currentOperations
    })
    this.subscriptions.push(unsubscribe)
  }

  // Initialize user-specific stores
  private async initializeUserStores() {
    const user = useAuthStore.getState().user
    if (!user) return

    try {
      // Initialize organization store
      if (user.organizationId) {
        await useOrganizationStore.getState().fetchOrganizations()
        useOrganizationStore.getState().setCurrentOrganization(user.organizationId)
      }

      // Initialize preferences store
      // await usePreferencesStore.getState().loadPreferences()

      // Initialize dashboard store
      try {
        await useDashboardStore.getState().fetchMetrics()
      } catch (error) {
        console.warn('Failed to initialize dashboard metrics:', error)
      }

      // Collaboration connections are now managed by collaborative components only
      // No global auto-connection to prevent unnecessary SignalR connections

    } catch (error) {
      console.error('Failed to initialize user stores:', error)
    }
  }

  // Cleanup user-specific data
  private cleanupUserStores() {
    try {
      // Reset all stores except auth
      Object.entries(storeRegistry).forEach(([storeName, store]) => {
        if (storeName !== 'auth') {
          const state = store.getState() as any
          if (typeof state.reset === 'function') {
            state.reset()
          }
        }
      })

      // Disconnect collaboration
      useCollaborationStore.getState().disconnect()

    } catch (error) {
      console.error('Failed to cleanup user stores:', error)
    }
  }

  // Get all store states for debugging
  getAllStoreStates() {
    const states: Record<string, any> = {}
    Object.entries(storeRegistry).forEach(([name, store]) => {
      states[name] = store.getState()
    })
    return states
  }

  // Reset all stores
  resetAllStores() {
    Object.entries(storeRegistry).forEach(([name, store]) => {
      const state = store.getState() as any
      if (typeof state.reset === 'function') {
        state.reset()
      }
    })
  }

  // Check store health
  checkStoreHealth() {
    const health: Record<string, boolean> = {}
    Object.entries(storeRegistry).forEach(([name, store]) => {
      try {
        const state = store.getState() as any
        health[name] = state._hydrated === true
      } catch (error) {
        health[name] = false
      }
    })
    return health
  }
}

// Store hydration helper
export const waitForHydration = async (stores: Array<{ getState: () => any }>) => {
  const checkHydration = () => {
    return stores.every(store => {
      try {
        const state = store.getState()
        return state._hydrated === true
      } catch {
        return false
      }
    })
  }

  if (checkHydration()) {
    return Promise.resolve()
  }

  return new Promise<void>((resolve) => {
    const interval = setInterval(() => {
      if (checkHydration()) {
        clearInterval(interval)
        resolve()
      }
    }, 50)
  })
}

// Export singleton instance
export const storeIntegration = StoreIntegrationManager.getInstance()

// Hook for using store integration
export const useStoreIntegration = () => {
  return {
    initialize: () => storeIntegration.initialize(),
    cleanup: () => storeIntegration.cleanup(),
    getAllStates: () => storeIntegration.getAllStoreStates(),
    resetAll: () => storeIntegration.resetAllStores(),
    checkHealth: () => storeIntegration.checkStoreHealth(),
  }
}
