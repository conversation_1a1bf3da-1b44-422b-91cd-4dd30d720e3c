/**
 * Cohere Embedding Service
 * Uses Cohere embed-v3-multilingual model from Azure AI Foundry
 * Provides high-quality multilingual embeddings with Azure Identity authentication
 */

import { logger } from '../utils/logger';
import { azureIdentityService } from './azure-identity';
import { config } from '../../env';

export interface CohereEmbeddingRequest {
  texts: string[];
  inputType?: 'search_document' | 'search_query' | 'classification' | 'clustering';
  embeddingTypes?: ('float' | 'int8' | 'uint8' | 'binary' | 'ubinary')[];
  truncate?: 'NONE' | 'START' | 'END';
}

export interface CohereEmbeddingResponse {
  embeddings: number[][];
  texts: string[];
  meta?: {
    api_version?: {
      version: string;
    };
    billed_units?: {
      input_tokens: number;
    };
  };
}

export interface EmbeddingResult {
  embedding: number[];
  dimensions: number;
  model: string;
  tokensUsed: number;
  inputType?: string;
}

export class CohereEmbeddingService {
  private initialized: boolean = false;
  private config: {
    endpoint: string;
    deploymentName: string;
    embeddingDimensions: number;
    enabled: boolean;
  };

  constructor() {
    this.config = {
      endpoint: config.ai.cohere.endpoint,
      deploymentName: config.ai.cohere.deploymentName,
      embeddingDimensions: config.ai.cohere.embeddingDimensions,
      enabled: config.ai.cohere.enabled && !!config.ai.cohere.endpoint
    };
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    if (!this.config.enabled) {
      logger.warn('Cohere embedding service is disabled - Azure AI Foundry endpoint not configured');
      return;
    }

    try {
      // Initialize Azure Identity service if not already done
      if (!azureIdentityService.isReady()) {
        await azureIdentityService.initialize();
      }

      // Test the connection
      await this.testConnection();

      this.initialized = true;
      logger.info('Cohere Embedding Service initialized successfully with Azure AI Foundry', {
        endpoint: this.config.endpoint,
        deploymentName: this.config.deploymentName,
        embeddingDimensions: this.config.embeddingDimensions
      });
    } catch (error) {
      logger.error('Failed to initialize Cohere Embedding Service with Azure AI Foundry', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Test Cohere embedding connection
   */
  private async testConnection(): Promise<void> {
    try {
      // Test with a simple embedding request
      const testResult = await this.generateEmbedding('Test connection', 'search_query');
      
      if (!testResult.embedding || testResult.embedding.length === 0) {
        throw new Error('Invalid response from Cohere embedding service');
      }

      logger.info('Cohere embedding connection test successful', {
        dimensions: testResult.dimensions,
        model: testResult.model
      });
    } catch (error) {
      logger.error('Cohere embedding connection test failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw new Error(`Cohere embedding connection test failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(
    text: string,
    inputType: 'search_document' | 'search_query' | 'classification' | 'clustering' = 'search_document'
  ): Promise<EmbeddingResult> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.config.enabled) {
      throw new Error('Cohere embedding service is not enabled');
    }

    try {
      const result = await this.generateEmbeddings([text], inputType);
      return {
        embedding: result.embeddings[0],
        dimensions: result.embeddings[0].length,
        model: this.config.deploymentName,
        tokensUsed: result.meta?.billed_units?.input_tokens || 0,
        inputType
      };
    } catch (error) {
      logger.error('Cohere embedding generation failed', {
        error: error instanceof Error ? error.message : String(error),
        textLength: text.length,
        inputType
      });
      throw error;
    }
  }

  /**
   * Generate embeddings for multiple texts (batch processing)
   */
  async generateEmbeddings(
    texts: string[],
    inputType: 'search_document' | 'search_query' | 'classification' | 'clustering' = 'search_document'
  ): Promise<CohereEmbeddingResponse> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.config.enabled) {
      throw new Error('Cohere embedding service is not enabled');
    }

    try {
      logger.debug('Generating Cohere embeddings', {
        textCount: texts.length,
        inputType,
        endpoint: this.config.endpoint
      });

      // Get access token for Azure AI Foundry
      const token = await azureIdentityService.getToken('https://ml.azure.com/.default');

      const requestBody: CohereEmbeddingRequest = {
        texts,
        inputType,
        embeddingTypes: ['float'],
        truncate: 'END'
      };

      // Use the Azure AI model inference endpoint
      const url = `${this.config.endpoint}/embeddings`;
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Cohere embedding API error: ${response.status} ${errorText}`);
      }

      const result: CohereEmbeddingResponse = await response.json();

      if (!result.embeddings || result.embeddings.length === 0) {
        throw new Error('No embeddings received from Cohere API');
      }

      logger.debug('Cohere embeddings generated successfully', {
        textCount: texts.length,
        embeddingCount: result.embeddings.length,
        dimensions: result.embeddings[0]?.length || 0,
        tokensUsed: result.meta?.billed_units?.input_tokens || 0
      });

      return result;
    } catch (error) {
      logger.error('Cohere batch embedding generation failed', {
        error: error instanceof Error ? error.message : String(error),
        textCount: texts.length,
        inputType
      });
      throw error;
    }
  }

  /**
   * Generate embeddings optimized for document indexing
   */
  async generateDocumentEmbeddings(texts: string[]): Promise<CohereEmbeddingResponse> {
    return this.generateEmbeddings(texts, 'search_document');
  }

  /**
   * Generate embeddings optimized for search queries
   */
  async generateQueryEmbeddings(texts: string[]): Promise<CohereEmbeddingResponse> {
    return this.generateEmbeddings(texts, 'search_query');
  }

  /**
   * Generate embeddings optimized for classification
   */
  async generateClassificationEmbeddings(texts: string[]): Promise<CohereEmbeddingResponse> {
    return this.generateEmbeddings(texts, 'classification');
  }

  /**
   * Generate embeddings optimized for clustering
   */
  async generateClusteringEmbeddings(texts: string[]): Promise<CohereEmbeddingResponse> {
    return this.generateEmbeddings(texts, 'clustering');
  }

  /**
   * Get service configuration
   */
  getConfig() {
    return {
      endpoint: this.config.endpoint,
      deploymentName: this.config.deploymentName,
      embeddingDimensions: this.config.embeddingDimensions,
      enabled: this.config.enabled,
      initialized: this.initialized
    };
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.initialized && this.config.enabled;
  }
}

// Export singleton instance
export const cohereEmbeddingService = new CohereEmbeddingService();
