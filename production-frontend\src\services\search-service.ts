/**
 * Enhanced Search Service
 * Handles search operations across the application with caching, analytics, and AI-powered features
 */

import { memoryCache } from '../lib/cache'
import { performanceMonitor } from '../lib/performance'
import { debounce } from '../lib/utils'

export interface SearchResult {
  id: string
  type: 'document' | 'project' | 'organization' | 'user' | 'template' | 'workflow'
  title: string
  description?: string
  content?: string
  url: string
  score: number
  relevanceScore: number
  highlights: Record<string, string[]>
  metadata: {
    createdAt: string
    updatedAt: string
    description?: string
    author?: {
      id: string
      name: string
      avatarUrl?: string
    }
    tags?: string[]
    category?: string
    organizationId?: string
    projectId?: string
  }
}

export interface SearchQuery {
  query: string
  type?: SearchResult['type'] | SearchResult['type'][]
  organizationId?: string
  projectId?: string
  tags?: string[]
  dateRange?: {
    from: string
    to: string
  }
  sortBy?: 'relevance' | 'date' | 'title'
  sortOrder?: 'asc' | 'desc'
  page?: number
  pageSize?: number
  // Additional fields for RAG integration
  limit?: number
  filters?: Record<string, any>
}

export interface SearchResponse {
  results: SearchResult[]
  total: number
  page?: number
  pageSize?: number
  totalPages?: number
  facets?: {
    types: Array<{ type: string; count: number }>
    tags: Array<{ tag: string; count: number }>
    authors: Array<{ author: string; count: number }>
  }
  suggestions?: string[]
  searchTime?: number
  // Additional fields for RAG integration
  query?: string
  took?: number
}

export interface SearchFilters {
  types: string[]
  tags: string[]
  authors: string[]
  dateRange?: {
    from: string
    to: string
  }
}

export interface SearchSuggestion {
  id: string
  text: string
  type: 'query' | 'filter' | 'entity'
  score: number
  metadata?: Record<string, any>
}

export interface PopularSearchQuery {
  query: string
  count: number
  lastUsed: string
}

class SearchService {
  private baseUrl: string
  private searchHistory: string[] = []
  private searchAnalytics: Array<{
    query: string
    resultCount: number
    timestamp: number
    took: number
  }> = []
  private maxHistorySize = 50
  private debouncedSearch: Function

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:7071/'
    this.loadSearchHistory()
    this.loadSearchAnalytics()

    // Debounced search for real-time suggestions
    this.debouncedSearch = debounce(this.performSearch.bind(this), 300)
  }

  /**
   * Perform a search query with caching and analytics
   */
  async search(query: SearchQuery): Promise<SearchResponse> {
    if (!query.query.trim()) {
      return {
        results: [],
        total: 0,
        facets: { types: [], tags: [], authors: [] },
        suggestions: [],
        searchTime: 0
      }
    }

    const cacheKey = `search_${JSON.stringify(query)}`

    // Check cache first
    const cached = memoryCache.get(cacheKey)
    if (cached) {
      return cached
    }

    try {
      const startTime = performance.now()

      // Add to search history
      this.addToHistory(query.query)

      // Use RAG query endpoint for search functionality
      const response = await fetch(`${this.baseUrl}/rag/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          operationType: 'RAG_QUERY',
          ragRequest: {
            operation: 'QUERY',
            queryData: {
              query: query.query,
              maxResults: query.limit || 10,
              includeMetadata: true,
              filters: query.filters
            }
          },
          organizationId: query.organizationId
        })
      })

      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`)
      }

      const data = await response.json()
      const duration = performance.now() - startTime

      // Record performance metrics
      performanceMonitor.recordMetric('Search_Request', duration)

      // Record analytics
      this.recordSearchAnalytics({
        query: query.query,
        resultCount: data.total || 0,
        timestamp: Date.now(),
        took: duration
      })

      // Transform RAG response to SearchResponse format
      const searchResponse = {
        results: data.results || [],
        total: data.total || 0,
        page: query.page || 1,
        pageSize: query.pageSize || 10,
        totalPages: Math.ceil((data.total || 0) / (query.pageSize || 10)),
        facets: data.facets || {
          types: [],
          tags: [],
          authors: []
        },
        suggestions: data.suggestions || [],
        searchTime: duration,
        query: query.query,
        took: duration
      }

      // Cache results for 5 minutes
      memoryCache.set(cacheKey, searchResponse, 5 * 60 * 1000)

      return searchResponse
    } catch (error) {
      console.error('Search error:', error)

      // Record failed search analytics
      this.recordSearchAnalytics({
        query: query.query,
        resultCount: 0,
        timestamp: Date.now(),
        took: 0
      })

      // Return empty results for production
      return {
        results: [],
        total: 0,
        page: query.page || 1,
        pageSize: query.pageSize || 10,
        totalPages: 0,
        facets: { types: [], tags: [], authors: [] },
        suggestions: [],
        searchTime: 0,
        query: query.query,
        took: 0
      }
    }
  }

  /**
   * Get search suggestions
   */
  async getSuggestions(query: string): Promise<string[]> {
    if (!query.trim()) {
      return []
    }

    try {
      const response = await fetch(`${this.baseUrl}/search/suggestions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: query.trim(),
          limit: 5
        })
      })

      if (!response.ok) {
        throw new Error(`Suggestions failed: ${response.statusText}`)
      }

      const data = await response.json()
      return data.suggestions || []
    } catch (error) {
      console.error('Suggestions error:', error)

      // Return search history as fallback suggestions
      const historyMatches = this.searchHistory
        .filter(h => h.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 5)

      return historyMatches
    }
  }

  /**
   * Get popular searches
   */
  async getPopularSearches(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/search/popular`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Popular searches failed: ${response.statusText}`)
      }

      const data = await response.json()
      return data.popularSearches || []
    } catch (error) {
      console.error('Popular searches error:', error)

      // Return local popular queries as fallback
      return this.getPopularQueries(5).map(item => item.query)
    }
  }

  /**
   * Save search query for analytics
   */
  async saveSearchQuery(query: string, resultCount: number): Promise<void> {
    try {
      await fetch(`${this.baseUrl}/search/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          resultCount,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      console.error('Save search query error:', error)
    }
  }



  /**
   * Enhanced search method with debouncing
   */
  private async performSearch(query: SearchQuery): Promise<SearchResponse> {
    return this.search(query)
  }

  /**
   * Get search history
   */
  getSearchHistory(): string[] {
    return [...this.searchHistory]
  }

  /**
   * Clear search history
   */
  clearSearchHistory(): void {
    this.searchHistory = []
    this.saveSearchHistory()
  }

  /**
   * Get search analytics
   */
  getSearchAnalytics() {
    return [...this.searchAnalytics]
  }

  /**
   * Get popular queries from analytics
   */
  getPopularQueries(limit = 10): Array<{ query: string; count: number }> {
    const queryCount = new Map<string, number>()

    this.searchAnalytics.forEach(analytics => {
      const count = queryCount.get(analytics.query) || 0
      queryCount.set(analytics.query, count + 1)
    })

    return Array.from(queryCount.entries())
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
  }

  /**
   * Record result click for analytics
   */
  recordResultClick(resultId: string, query: string): void {
    // This could be enhanced to track click-through rates
    console.log(`Result clicked: ${resultId} for query: ${query}`)
  }

  /**
   * Add query to search history
   */
  private addToHistory(query: string): void {
    const trimmedQuery = query.trim()
    if (!trimmedQuery) return

    // Remove if already exists
    const index = this.searchHistory.indexOf(trimmedQuery)
    if (index > -1) {
      this.searchHistory.splice(index, 1)
    }

    // Add to beginning
    this.searchHistory.unshift(trimmedQuery)

    // Limit size
    if (this.searchHistory.length > this.maxHistorySize) {
      this.searchHistory = this.searchHistory.slice(0, this.maxHistorySize)
    }

    this.saveSearchHistory()
  }

  /**
   * Record search analytics
   */
  private recordSearchAnalytics(analytics: {
    query: string
    resultCount: number
    timestamp: number
    took: number
  }): void {
    this.searchAnalytics.unshift(analytics)

    // Limit size
    if (this.searchAnalytics.length > 100) {
      this.searchAnalytics = this.searchAnalytics.slice(0, 100)
    }

    this.saveSearchAnalytics()
  }

  /**
   * Load search history from localStorage
   */
  private loadSearchHistory(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem('search_history')
      if (stored) {
        this.searchHistory = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load search history:', error)
    }
  }

  /**
   * Save search history to localStorage
   */
  private saveSearchHistory(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem('search_history', JSON.stringify(this.searchHistory))
    } catch (error) {
      console.warn('Failed to save search history:', error)
    }
  }

  /**
   * Load search analytics from localStorage
   */
  private loadSearchAnalytics(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem('search_analytics')
      if (stored) {
        this.searchAnalytics = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load search analytics:', error)
    }
  }

  /**
   * Save search analytics to localStorage
   */
  private saveSearchAnalytics(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem('search_analytics', JSON.stringify(this.searchAnalytics))
    } catch (error) {
      console.warn('Failed to save search analytics:', error)
    }
  }
}

export const searchService = new SearchService()
export default searchService
