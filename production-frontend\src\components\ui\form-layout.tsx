"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface FormLayoutProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

export function FormLayout({ children, className, ...props }: FormLayoutProps) {
  return (
    <div className={cn("space-y-6", className)} {...props}>
      {children}
    </div>
  )
}

export interface FormSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  title?: string
  description?: string
  className?: string
}

export function FormSection({ children, title, description, className, ...props }: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)} {...props}>
      {(title || description) && (
        <div className="space-y-1">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

export interface FormGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

export function FormGroup({ children, className, ...props }: FormGroupProps) {
  return (
    <div className={cn("space-y-2", className)} {...props}>
      {children}
    </div>
  )
}

export interface FormFieldProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  label?: string
  description?: string
  error?: string
  className?: string
}

export function FormField({ children, label, description, error, className, ...props }: FormFieldProps) {
  const id = React.useId()

  return (
    <div className={cn("space-y-2", className)} {...props}>
      {label && (
        <label htmlFor={id} className="text-sm font-medium">
          {label}
        </label>
      )}
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {React.isValidElement(children) && React.cloneElement(children, { ...(children.props as any), id })}
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}

export interface FormActionsProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  className?: string
}

export function FormActions({ children, className, ...props }: FormActionsProps) {
  return (
    <div className={cn("flex items-center justify-end space-x-2", className)} {...props}>
      {children}
    </div>
  )
}
