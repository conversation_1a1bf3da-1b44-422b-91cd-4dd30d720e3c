'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useProjectDocuments } from '@/hooks/documents';
import { DocumentCard } from '@/components/documents/document-card';
import { EmptyState } from '@/components/empty-state';
import { FileText, Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useState } from 'react';

import { useToast } from '@/components/ui/use-toast';

interface ProjectDocumentsProps {
  projectId: string;
}

export function ProjectDocuments({ projectId }: ProjectDocumentsProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const {
    documents,
    isLoading,
    error,
    downloadDocument
  } = useProjectDocuments(projectId);

  // Filter documents by search query
  const filteredDocuments = searchQuery
    ? documents.filter((doc: any) =>
        doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.fileName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.tags?.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : documents;

  const handleDownloadDocument = async (documentId: string) => {
    try {
      await downloadDocument.mutateAsync({
        documentId,
        version: undefined
      });

      toast({
        title: 'Download started',
        description: 'Your document is being downloaded',
      });
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'There was an error downloading the document',
        variant: 'destructive',
      });
    }
  };

  if (error) {
    return (
      <EmptyState
        icon={<FileText className="h-10 w-10 text-muted-foreground" />}
        title="Error loading documents"
        description="There was a problem loading the documents for this project."
        action={
          <Button onClick={() => router.refresh()}>
            Try Again
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search documents..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          onClick={() => router.push(`/projects/${projectId}/documents/upload`)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Upload
        </Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-40 bg-muted rounded-md animate-pulse" />
          ))}
        </div>
      ) : filteredDocuments.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredDocuments.map((document) => (
            <div
              key={document.id}
              onClick={() => router.push(`/projects/${projectId}/documents/${document.id}`)}
              className="cursor-pointer"
            >
              <DocumentCard
                document={document}
                onDownload={(doc) => {
                  handleDownloadDocument(doc.id);
                }}
              />
            </div>
          ))}
        </div>
      ) : (
        <EmptyState
          icon={<FileText className="h-10 w-10 text-muted-foreground" />}
          title={searchQuery ? "No matching documents" : "No documents yet"}
          description={
            searchQuery
              ? "Try a different search term or clear the search"
              : "Upload your first document to get started"
          }
          action={
            <Button
              onClick={() => router.push(`/projects/${projectId}/documents/upload`)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Upload Document
            </Button>
          }
        />
      )}
    </div>
  );
}
