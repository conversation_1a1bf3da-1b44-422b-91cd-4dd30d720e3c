/**
 * Enhanced Cache Manager with Cache-Aside Pattern
 * Provides intelligent caching strategies for enterprise applications
 */

import { redis } from '../services/redis';
import { logger } from './logger';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  namespace?: string;
  compression?: boolean;
  serialization?: 'json' | 'msgpack' | 'string';
  tags?: string[]; // For cache invalidation by tags
}

export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalOperations: number;
  averageResponseTime: number;
}

export class EnhancedCacheManager {
  private static instance: EnhancedCacheManager;
  private stats: Map<string, CacheStats> = new Map();
  private defaultTTL = 3600; // 1 hour
  private defaultNamespace = 'app';

  static getInstance(): EnhancedCacheManager {
    if (!EnhancedCacheManager.instance) {
      EnhancedCacheManager.instance = new EnhancedCacheManager();
    }
    return EnhancedCacheManager.instance;
  }

  /**
   * Cache-aside pattern: Get from cache or execute function and cache result
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const startTime = Date.now();
    const fullKey = this.buildKey(key, options.namespace);

    try {
      // Try to get from cache first
      const cached = await this.get<T>(key, options);
      if (cached !== null) {
        this.recordHit(key, Date.now() - startTime);
        return cached;
      }

      // Cache miss - execute function
      const result = await fetchFunction();
      
      // Cache the result
      await this.set(key, result, options);
      
      this.recordMiss(key, Date.now() - startTime);
      return result;

    } catch (error) {
      logger.error('Cache-aside operation failed', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // Fallback to direct function execution
      return await fetchFunction();
    }
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    const fullKey = this.buildKey(key, options.namespace);

    try {
      const value = await redis.get(fullKey);
      if (value === null) return null;

      return this.deserialize<T>(value, options.serialization);
    } catch (error) {
      logger.error('Cache get failed', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const fullKey = this.buildKey(key, options.namespace);
    const ttl = options.ttl || this.defaultTTL;

    try {
      const serialized = this.serialize(value, options.serialization);
      
      if (ttl > 0) {
        await redis.setex(fullKey, ttl, serialized);
      } else {
        await redis.set(fullKey, serialized);
      }

      // Handle tags for invalidation
      if (options.tags && options.tags.length > 0) {
        await this.addToTags(fullKey, options.tags);
      }

    } catch (error) {
      logger.error('Cache set failed', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Delete from cache
   */
  async delete(key: string, namespace?: string): Promise<void> {
    const fullKey = this.buildKey(key, namespace);

    try {
      await redis.del(fullKey);
    } catch (error) {
      logger.error('Cache delete failed', {
        key: fullKey,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<number> {
    let totalDeleted = 0;

    try {
      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        const keys = await redis.smembers(tagKey);
        
        if (keys.length > 0) {
          await redis.del(...keys);
          await redis.del(tagKey);
          totalDeleted += keys.length;
        }
      }

      logger.info('Cache invalidated by tags', { tags, keysDeleted: totalDeleted });
    } catch (error) {
      logger.error('Cache tag invalidation failed', {
        tags,
        error: error instanceof Error ? error.message : String(error)
      });
    }

    return totalDeleted;
  }

  /**
   * Warm cache with multiple values
   */
  async warmCache<T>(
    entries: Array<{ key: string; value: T; options?: CacheOptions }>
  ): Promise<void> {
    try {
      const pipeline = await redis.pipeline();

      for (const entry of entries) {
        const fullKey = this.buildKey(entry.key, entry.options?.namespace);
        const serialized = this.serialize(entry.value, entry.options?.serialization);
        const ttl = entry.options?.ttl || this.defaultTTL;

        if (ttl > 0) {
          pipeline.setex(fullKey, ttl, serialized);
        } else {
          pipeline.set(fullKey, serialized);
        }
      }

      await pipeline.exec();
      logger.info('Cache warmed', { entriesCount: entries.length });

    } catch (error) {
      logger.error('Cache warming failed', {
        entriesCount: entries.length,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get cache statistics
   */
  getStats(key?: string): CacheStats | Map<string, CacheStats> {
    if (key) {
      return this.stats.get(key) || {
        hits: 0,
        misses: 0,
        hitRate: 0,
        totalOperations: 0,
        averageResponseTime: 0
      };
    }
    return this.stats;
  }

  /**
   * Clear all cache statistics
   */
  clearStats(): void {
    this.stats.clear();
  }

  /**
   * Flush entire cache (use with caution)
   */
  async flushAll(): Promise<void> {
    try {
      await redis.flushall();
      logger.warn('Cache flushed completely');
    } catch (error) {
      logger.error('Cache flush failed', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  // Private helper methods
  private buildKey(key: string, namespace?: string): string {
    const ns = namespace || this.defaultNamespace;
    return `${ns}:${key}`;
  }

  private serialize<T>(value: T, format?: string): string {
    switch (format) {
      case 'string':
        return String(value);
      case 'json':
      default:
        return JSON.stringify(value);
    }
  }

  private deserialize<T>(value: string, format?: string): T {
    switch (format) {
      case 'string':
        return value as unknown as T;
      case 'json':
      default:
        return JSON.parse(value);
    }
  }

  private async addToTags(key: string, tags: string[]): Promise<void> {
    try {
      const pipeline = await redis.pipeline();

      for (const tag of tags) {
        const tagKey = `tag:${tag}`;
        pipeline.sadd(tagKey, key);
        pipeline.expire(tagKey, this.defaultTTL);
      }

      await pipeline.exec();
    } catch (error) {
      logger.error('Failed to add cache tags', {
        key,
        tags,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private recordHit(key: string, responseTime: number): void {
    const stats = this.stats.get(key) || {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalOperations: 0,
      averageResponseTime: 0
    };

    stats.hits++;
    stats.totalOperations++;
    stats.hitRate = stats.hits / stats.totalOperations;
    stats.averageResponseTime = (stats.averageResponseTime + responseTime) / 2;

    this.stats.set(key, stats);
  }

  private recordMiss(key: string, responseTime: number): void {
    const stats = this.stats.get(key) || {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalOperations: 0,
      averageResponseTime: 0
    };

    stats.misses++;
    stats.totalOperations++;
    stats.hitRate = stats.hits / stats.totalOperations;
    stats.averageResponseTime = (stats.averageResponseTime + responseTime) / 2;

    this.stats.set(key, stats);
  }
}

export const enhancedCacheManager = EnhancedCacheManager.getInstance();
