"use client";

import { useState } from "react";
import Link from "next/link";
import { useExports, useDeleteExport, useDownloadExport, useCancelExport } from "@/hooks/exports/useExports";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Download, 
  FileDown, 
  Search, 
  Plus, 
  Trash, 
  X, 
  ArrowLeft,
  FileText,
  BarChart,
  Users,
  FolderKanban,
  Clock,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { ExportDialog } from "@/components/analytics/export-dialog";
import { EmptyState } from "@/components/empty-state";
// Define export enums locally since service doesn't export them
enum ExportStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

enum ExportType {
  DOCUMENTS = 'documents',
  ANALYTICS = 'analytics',
  USERS = 'users',
  PROJECTS = 'projects'
}
import { formatDistanceToNow, format } from "date-fns";

export default function ExportsPage() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<ExportType | "ALL">("ALL");
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [selectedExportId, setSelectedExportId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  const { exports, loading: isLoading, refresh: refetch } = useExports();
  const { mutate: deleteExport } = useDeleteExport();
  const { mutate: downloadExport } = useDownloadExport();
  const { mutate: cancelExport } = useCancelExport();
  const [isDeleting, setIsDeleting] = useState(false);

  // Filter exports based on search query and active tab
  const filteredExports = exports?.filter((exp) => {
    const matchesSearch =
      (exp.fileName && exp.fileName.toLowerCase().includes(searchQuery.toLowerCase())) ||
      exp.type.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTab = activeTab === "ALL" || exp.type === activeTab;

    return matchesSearch && matchesTab;
  }) || [];

  // Handle export deletion
  const handleDeleteExport = async () => {
    if (!selectedExportId) return;
    setIsDeleting(true);

    try {
      await deleteExport(selectedExportId);
      setIsDeleteDialogOpen(false);
      setSelectedExportId(null);
      refetch();
      toast({
        title: "Export deleted",
        description: "The export has been deleted successfully."
      });
    } catch (error) {
      console.error('Failed to delete export:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handle export download
  const handleDownloadExport = async (id: string) => {
    try {
      await downloadExport(id);
    } catch (error) {
      console.error('Failed to download export:', error);
    }
  };

  // Handle export cancellation
  const handleCancelExport = async (id: string) => {
    try {
      await cancelExport(id);
      toast({
        title: "Export cancelled",
        description: "The export has been cancelled successfully."
      });
      refetch();
    } catch (error) {
      console.error('Failed to cancel export:', error);
    }
  };

  // Get icon for export type
  const getExportTypeIcon = (type: ExportType) => {
    switch (type) {
      case ExportType.DOCUMENTS:
        return <FileText className="h-4 w-4" />;
      case ExportType.ANALYTICS:
        return <BarChart className="h-4 w-4" />;
      case ExportType.USERS:
        return <Users className="h-4 w-4" />;
      case ExportType.PROJECTS:
        return <FolderKanban className="h-4 w-4" />;
      default:
        return <FileDown className="h-4 w-4" />;
    }
  };

  // Get badge variant for export status
  const getStatusVariant = (status: ExportStatus) => {
    switch (status) {
      case ExportStatus.COMPLETED:
        return "success";
      case ExportStatus.PROCESSING:
        return "secondary";
      case ExportStatus.PENDING:
        return "outline";
      case ExportStatus.FAILED:
        return "destructive";
      default:
        return "outline";
    }
  };

  // Get icon for export status
  const getStatusIcon = (status: ExportStatus) => {
    switch (status) {
      case ExportStatus.COMPLETED:
        return <CheckCircle className="h-4 w-4" />;
      case ExportStatus.PROCESSING:
        return <Clock className="h-4 w-4" />;
      case ExportStatus.PENDING:
        return <Clock className="h-4 w-4" />;
      case ExportStatus.FAILED:
        return <AlertCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-12 w-full" />
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Button
              variant="ghost"
              size="sm"
              className="p-0"
              asChild
            >
              <Link href="/analytics">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Analytics
              </Link>
            </Button>
          </div>
          <h1 className="text-2xl font-bold tracking-tight">Exports</h1>
          <p className="text-muted-foreground">
            Manage your data exports
          </p>
        </div>
        <Button onClick={() => setIsExportDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Export
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search exports..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Tabs 
          defaultValue="ALL" 
          value={activeTab} 
          onValueChange={(value) => setActiveTab(value as ExportType | "ALL")}
          className="w-full md:w-auto"
        >
          <TabsList className="grid grid-cols-5 w-full md:w-auto">
            <TabsTrigger value="ALL">All</TabsTrigger>
            <TabsTrigger value={ExportType.DOCUMENTS}>Documents</TabsTrigger>
            <TabsTrigger value={ExportType.ANALYTICS}>Analytics</TabsTrigger>
            <TabsTrigger value={ExportType.USERS}>Users</TabsTrigger>
            <TabsTrigger value={ExportType.PROJECTS}>Projects</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {filteredExports.length === 0 ? (
        <EmptyState
          icon={<FileDown className="h-10 w-10 text-muted-foreground" />}
          title={searchQuery ? "No exports found" : "No exports"}
          description={
            searchQuery
              ? `No exports match "${searchQuery}"`
              : "You haven't created any exports yet."
          }
          action={
            <Button onClick={() => setIsExportDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Export
            </Button>
          }
        />
      ) : (
        <div className="space-y-4">
          {filteredExports.map((exp) => (
            <Card key={exp.id}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{exp.fileName || `${exp.type} Export`}</CardTitle>
                    <CardDescription>Export ID: {exp.id}</CardDescription>
                  </div>
                  <Badge variant={getStatusVariant(exp.status as ExportStatus)} className="flex items-center gap-1">
                    {getStatusIcon(exp.status as ExportStatus)}
                    {exp.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    {getExportTypeIcon(exp.type as ExportType)}
                    <span className="text-muted-foreground">Type:</span>
                    <span>{exp.type}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileDown className="h-4 w-4" />
                    <span className="text-muted-foreground">Format:</span>
                    <span>{exp.format}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span className="text-muted-foreground">Created:</span>
                    <span>{formatDistanceToNow(new Date(exp.createdAt))} ago</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                {exp.status === ExportStatus.PENDING || exp.status === ExportStatus.PROCESSING ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleCancelExport(exp.id)}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                ) : exp.status === ExportStatus.COMPLETED ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleDownloadExport(exp.id)}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                ) : null}
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <span className="sr-only">Open menu</span>
                      <svg
                        width="15"
                        height="15"
                        viewBox="0 0 15 15"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                      >
                        <path
                          d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z"
                          fill="currentColor"
                          fillRule="evenodd"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => {
                        setSelectedExportId(exp.id);
                        setIsDeleteDialogOpen(true);
                      }}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Export Dialog */}
      <ExportDialog 
        open={isExportDialogOpen} 
        onOpenChange={setIsExportDialogOpen}
        exportType={activeTab === "ALL" ? ExportType.DOCUMENTS : activeTab}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Export</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this export? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteExport}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
