/**
 * Search Hooks Index
 * Re-exports all search-related hooks
 */

export { useSearch } from './useSearch'
export { useSearchHistory } from './useSearchHistory'
export { useSearchFilters } from './useSearchFilters'

// Search analytics hook
export const useSearchAnalytics = () => {
  return {
    data: [],
    popularQueries: [
      { query: 'financial reports', count: 45 },
      { query: 'quarterly data', count: 32 },
      { query: 'project documents', count: 28 }
    ],
    isLoading: false,
    isLoadingPopularQueries: false,
    error: null,
    loadPopularQueries: () => Promise.resolve()
  }
}

// Placeholder for missing hook
export const usePopularSearchQueries = () => {
  // TODO: Implement usePopularSearchQueries hook
  return {
    data: [],
    popularQueries: [
      { query: 'financial reports', count: 45 },
      { query: 'quarterly data', count: 32 },
      { query: 'project documents', count: 28 }
    ],
    isLoading: false,
    error: null
  }
}

export type {
  SearchResult,
  SearchFilters,
  SearchOptions,
  UseSearchResult
} from './useSearch'
