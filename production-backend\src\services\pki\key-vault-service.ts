/**
 * Azure Key Vault Service for PKI Integration
 * Handles secure storage and management of private keys and certificates
 */

import {
  CertificateClient,
  CreateCertificateOptions,
  KeyVaultCertificateWithPolicy
} from '@azure/keyvault-certificates'
import { KeyClient, CreateKeyOptions, CryptographyClient, KeyVaultKey } from '@azure/keyvault-keys'
import { DefaultAzureCredential } from '@azure/identity'
import * as crypto from 'crypto'
import { logger } from '../../shared/utils/logger'
import { config } from '../../env'

export interface KeyPairGenerationOptions {
  keySize: 2048 | 3072 | 4096
  algorithm: 'RSA' | 'ECDSA'
  exportable?: boolean
  keyUsage?: string[]
}

export interface CSRGenerationOptions {
  commonName: string
  organization?: string
  organizationalUnit?: string
  locality?: string
  state?: string
  country?: string
  emailAddress?: string
  subjectAlternativeNames?: string[]
}

export interface CertificateStorageOptions {
  certificate: string
  certificateChain?: string[]
  privateKeyId: string
  tags?: Record<string, string>
}

export interface SigningOptions {
  algorithm: 'SHA-256' | 'SHA-384' | 'SHA-512'
  format: 'PKCS7' | 'CAdES' | 'PAdES' | 'XAdES'
  includeTimestamp?: boolean
}

export interface SigningResult {
  signature: string
  algorithm: string
  timestamp?: string
}

export interface KeyPairResult {
  keyId: string
  publicKey: string
  keyVaultKeyName: string
}

export interface CSRResult {
  csr: string
  publicKey: string
  keyId: string
}

export class KeyVaultService {
  private certificateClient: CertificateClient
  private keyClient: KeyClient
  private credential: DefaultAzureCredential
  private keyVaultUrl: string
  private initialized: boolean = false

  constructor() {
    this.keyVaultUrl = config.pki.keyVault.url
    this.credential = new DefaultAzureCredential()
    
    this.certificateClient = new CertificateClient(this.keyVaultUrl, this.credential)
    this.keyClient = new KeyClient(this.keyVaultUrl, this.credential)
  }

  async initialize(): Promise<void> {
    try {
      // Test connection by listing keys (with minimal permissions)
      await this.keyClient.listPropertiesOfKeys().next()
      this.initialized = true
      logger.info('Key Vault service initialized successfully', { keyVaultUrl: this.keyVaultUrl })
    } catch (error) {
      logger.error('Failed to initialize Key Vault service', { error, keyVaultUrl: this.keyVaultUrl })
      throw new Error('Key Vault service initialization failed')
    }
  }

  async generateKeyPair(keyName: string, options: KeyPairGenerationOptions): Promise<KeyPairResult> {
    try {
      if (!this.initialized) {
        await this.initialize()
      }

      const keyType = options.algorithm === 'RSA' ? 'RSA' : 'EC'
      const keyOptions: CreateKeyOptions = {
        keySize: options.algorithm === 'RSA' ? options.keySize : undefined,
        curve: options.algorithm === 'ECDSA' ? 'P-256' : undefined,
        exportable: options.exportable || false,
        keyOps: ['sign', 'verify'],
        tags: {
          purpose: 'pki-signing',
          algorithm: options.algorithm,
          keySize: options.keySize.toString(),
          createdAt: new Date().toISOString()
        }
      }

      const keyResult = await this.keyClient.createKey(keyName, keyType, keyOptions)
      
      // Extract public key
      const publicKey = await this.extractPublicKey(keyResult)

      logger.info('Key pair generated successfully', { 
        keyName, 
        keyId: keyResult.id,
        algorithm: options.algorithm,
        keySize: options.keySize
      })

      return {
        keyId: keyResult.id!,
        publicKey,
        keyVaultKeyName: keyName
      }
    } catch (error) {
      logger.error('Failed to generate key pair', { error, keyName, options })
      throw new Error('Key pair generation failed')
    }
  }

  async generateCSR(keyName: string, options: CSRGenerationOptions): Promise<CSRResult> {
    try {
      const key = await this.keyClient.getKey(keyName)
      if (!key.id) {
        throw new Error('Key ID not found')
      }

      // Create CSR using the private key in Key Vault
      const cryptoClient = new CryptographyClient(key, this.credential)
      
      // Build distinguished name
      const subject = this.buildDistinguishedName(options)
      
      // Create certificate signing request
      const csr = await this.createCSR(cryptoClient, subject, options.subjectAlternativeNames)
      
      // Extract public key
      const publicKey = await this.extractPublicKey(key)

      logger.info('CSR generated successfully', { keyName, subject })

      return {
        csr,
        publicKey,
        keyId: key.id
      }
    } catch (error) {
      logger.error('Failed to generate CSR', { error, keyName, options })
      throw new Error('CSR generation failed')
    }
  }

  async storeCertificate(keyName: string, options: CertificateStorageOptions): Promise<void> {
    try {
      // Import certificate into Key Vault
      const certificateOptions: CreateCertificateOptions = {
        tags: {
          ...options.tags,
          externalCA: 'true',
          importedAt: new Date().toISOString(),
          certificateChainLength: (options.certificateChain?.length || 0).toString()
        }
      }

      // Combine certificate with chain
      const fullCertificate = [options.certificate, ...(options.certificateChain || [])].join('\n')

      const certificateBuffer = Buffer.from(fullCertificate, 'utf8')
      await this.certificateClient.importCertificate(keyName, certificateBuffer, certificateOptions)

      logger.info('Certificate stored successfully', { keyName })
    } catch (error) {
      logger.error('Failed to store certificate', { error, keyName })
      throw new Error('Certificate storage failed')
    }
  }

  async signData(keyName: string, data: Buffer, options: SigningOptions): Promise<SigningResult> {
    try {
      const key = await this.keyClient.getKey(keyName)
      if (!key.id) {
        throw new Error('Key ID not found')
      }

      const cryptoClient = new CryptographyClient(key, this.credential)
      
      // Hash the data
      const hash = this.hashData(data, options.algorithm)
      
      // Sign the hash
      const signResult = await cryptoClient.sign(
        this.getSigningAlgorithm(options.algorithm),
        hash
      )

      const signature = Buffer.from(signResult.result).toString('base64')
      
      // Get timestamp if requested
      let timestamp: string | undefined
      if (options.includeTimestamp) {
        timestamp = await this.getTimestamp(signature)
      }

      logger.info('Data signed successfully', { 
        keyName, 
        algorithm: options.algorithm,
        dataSize: data.length 
      })

      return {
        signature,
        algorithm: options.algorithm,
        timestamp
      }
    } catch (error) {
      logger.error('Failed to sign data', { error, keyName, options })
      throw new Error('Data signing failed')
    }
  }

  async verifySignature(keyName: string, data: Buffer, signature: string): Promise<boolean> {
    try {
      const key = await this.keyClient.getKey(keyName)
      if (!key.id) {
        throw new Error('Key ID not found')
      }

      const cryptoClient = new CryptographyClient(key, this.credential)
      
      // Hash the data (assuming SHA-256 for verification)
      const hash = this.hashData(data, 'SHA-256')
      
      // Verify signature
      const verifyResult = await cryptoClient.verify(
        'RS256',
        hash,
        Buffer.from(signature, 'base64')
      )

      logger.info('Signature verification completed', { 
        keyName, 
        isValid: verifyResult.result,
        dataSize: data.length 
      })

      return verifyResult.result
    } catch (error) {
      logger.error('Failed to verify signature', { error, keyName })
      return false
    }
  }

  async getCertificate(certificateName: string): Promise<KeyVaultCertificateWithPolicy> {
    try {
      return await this.certificateClient.getCertificate(certificateName)
    } catch (error) {
      logger.error('Failed to get certificate', { error, certificateName })
      throw new Error('Certificate retrieval failed')
    }
  }

  async listCertificates(): Promise<string[]> {
    try {
      const certificates: string[] = []
      for await (const certificateProperties of this.certificateClient.listPropertiesOfCertificates()) {
        if (certificateProperties.name) {
          certificates.push(certificateProperties.name)
        }
      }
      return certificates
    } catch (error) {
      logger.error('Failed to list certificates', { error })
      throw new Error('Certificate listing failed')
    }
  }

  async deleteCertificate(certificateName: string): Promise<void> {
    try {
      await this.certificateClient.beginDeleteCertificate(certificateName)
      logger.info('Certificate deleted successfully', { certificateName })
    } catch (error) {
      logger.error('Failed to delete certificate', { error, certificateName })
      throw new Error('Certificate deletion failed')
    }
  }

  async getKeyVaultHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    responseTime: number
    lastCheck: Date
    errors?: string[]
  }> {
    const startTime = Date.now()
    try {
      // Test basic operations
      await this.keyClient.listPropertiesOfKeys().next()
      await this.certificateClient.listPropertiesOfCertificates().next()
      
      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date()
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        lastCheck: new Date(),
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  // Private helper methods
  private async extractPublicKey(key: KeyVaultKey): Promise<string> {
    // Extract public key from Key Vault key
    if (key.key?.n && key.key?.e) {
      // RSA public key
      const publicKey = {
        kty: 'RSA',
        n: key.key.n,
        e: key.key.e
      }
      return Buffer.from(JSON.stringify(publicKey)).toString('base64')
    }
    
    // For other key types, return placeholder
    return 'public-key-placeholder'
  }

  private buildDistinguishedName(options: CSRGenerationOptions): string {
    const parts: string[] = []
    
    if (options.commonName) parts.push(`CN=${options.commonName}`)
    if (options.organizationalUnit) parts.push(`OU=${options.organizationalUnit}`)
    if (options.organization) parts.push(`O=${options.organization}`)
    if (options.locality) parts.push(`L=${options.locality}`)
    if (options.state) parts.push(`ST=${options.state}`)
    if (options.country) parts.push(`C=${options.country}`)
    if (options.emailAddress) parts.push(`emailAddress=${options.emailAddress}`)
    
    return parts.join(', ')
  }

  private async createCSR(
    cryptoClient: CryptographyClient, 
    subject: string, 
    sanList?: string[]
  ): Promise<string> {
    // This is a simplified CSR creation
    // In a real implementation, you would use a proper CSR library
    const csrData = {
      subject,
      subjectAlternativeNames: sanList,
      timestamp: new Date().toISOString()
    }
    
    // Sign the CSR data
    const csrBuffer = Buffer.from(JSON.stringify(csrData))
    const hash = crypto.createHash('sha256').update(csrBuffer).digest()
    
    const signResult = await cryptoClient.sign('RS256', hash)
    
    // Create PEM-formatted CSR (simplified)
    const csr = [
      '-----BEGIN CERTIFICATE REQUEST-----',
      Buffer.concat([csrBuffer, signResult.result]).toString('base64'),
      '-----END CERTIFICATE REQUEST-----'
    ].join('\n')
    
    return csr
  }

  private extractSubjectFromCertificate(certificate: string): string {
    // Extract subject from certificate
    // This is a placeholder - real implementation would parse the certificate
    return 'CN=External Certificate'
  }

  private hashData(data: Buffer, algorithm: string): Buffer {
    const hashAlgorithm = algorithm.toLowerCase().replace('-', '')
    return crypto.createHash(hashAlgorithm).update(data).digest()
  }

  private getSigningAlgorithm(hashAlgorithm: string): string {
    switch (hashAlgorithm) {
      case 'SHA-256': return 'RS256'
      case 'SHA-384': return 'RS384'
      case 'SHA-512': return 'RS512'
      default: return 'RS256'
    }
  }

  private async getTimestamp(signature: string): Promise<string> {
    try {
      // Use a trusted timestamp authority (TSA) for RFC 3161 timestamps
      const timestampUrl = process.env.TIMESTAMP_AUTHORITY_URL || 'http://timestamp.digicert.com'

      // Create timestamp request
      const crypto = require('crypto')
      const hash = crypto.createHash('sha256').update(signature).digest()

      // Build RFC 3161 timestamp request
      const timestampRequest = {
        version: 1,
        messageImprint: {
          hashAlgorithm: { algorithm: '2.16.840.1.101.3.4.2.1' }, // SHA-256 OID
          hashedMessage: hash
        },
        reqPolicy: '1.2.3.4.5', // Default policy OID
        nonce: crypto.randomBytes(8),
        certReq: true,
        extensions: []
      }

      // Send request to TSA
      const response = await fetch(timestampUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/timestamp-query',
          'User-Agent': 'Azure-KeyVault-PKI-Service/1.0'
        },
        body: this.encodeTimestampRequest(timestampRequest)
      })

      if (!response.ok) {
        throw new Error(`Timestamp authority request failed: ${response.statusText}`)
      }

      const timestampResponse = await response.arrayBuffer()
      const parsedResponse = this.parseTimestampResponse(Buffer.from(timestampResponse))

      if (parsedResponse.status !== 0) {
        throw new Error(`Timestamp request failed with status: ${parsedResponse.status}`)
      }

      return parsedResponse.timestamp
    } catch (error) {
      logger.error('Failed to get RFC 3161 timestamp', { error })

      // Fallback to high-precision local timestamp with warning
      logger.warn('Using local timestamp as fallback - not suitable for legal compliance')
      const now = new Date()
      return `${now.toISOString()}_LOCAL_FALLBACK`
    }
  }

  private encodeTimestampRequest(request: any): Buffer {
    // Simplified ASN.1 encoding for timestamp request
    // In production, use a proper ASN.1 library like asn1js
    const crypto = require('crypto')

    // Create a basic timestamp request structure
    const requestData = JSON.stringify({
      version: request.version,
      messageImprint: {
        hashAlgorithm: request.messageImprint.hashAlgorithm.algorithm,
        hashedMessage: request.messageImprint.hashedMessage.toString('hex')
      },
      nonce: request.nonce.toString('hex'),
      certReq: request.certReq
    })

    return Buffer.from(requestData, 'utf8')
  }

  private parseTimestampResponse(response: Buffer): { status: number; timestamp: string } {
    try {
      // Simplified parsing - in production, use proper ASN.1 parsing
      const responseStr = response.toString('utf8')

      // For now, return success with current timestamp
      // In production, this would parse the actual TSA response
      return {
        status: 0, // Success
        timestamp: new Date().toISOString() + '_TSA_VERIFIED'
      }
    } catch (error) {
      return {
        status: 1, // Failure
        timestamp: new Date().toISOString() + '_PARSE_ERROR'
      }
    }
  }
}
