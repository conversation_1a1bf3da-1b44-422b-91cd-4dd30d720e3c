/**
 * RAG (Retrieval-Augmented Generation) Hooks
 * React hooks for RAG operations using Zustand AI store
 */

import { useCallback } from 'react'
import { useToast } from '@/hooks/use-toast'
import {
  useAIStore,
  useAIOperations,
  useAILoading,
  useAIError,
  useStartAIOperation
} from '@/stores/ai-store'

export interface RAGQuery {
  query: string
  organizationId: string
  projectId?: string
  filters?: {
    documentIds?: string[]
    tags?: string[]
    dateRange?: { start: string; end: string }
    contentType?: string[]
    authors?: string[]
  }
  options?: {
    maxResults?: number
    minRelevance?: number
    includeReasoning?: boolean
    useAdvancedAI?: boolean
    temperature?: number
    maxTokens?: number
    contextWindow?: number
  }
}

export interface RAGResult {
  id: string
  query: string
  answer: string
  reasoning?: string
  sources: Array<{
    documentId: string
    documentName: string
    content: string
    relevanceScore: number
    pageNumber?: number
    section?: string
    metadata?: Record<string, any>
  }>
  confidence: number
  tokensUsed: number
  processingTime: number
  model: string
  createdAt: string
  organizationId: string
  projectId?: string
}

export interface RAGSession {
  id: string
  title: string
  queries: RAGResult[]
  organizationId: string
  projectId?: string
  userId: string
  settings: {
    maxResults: number
    minRelevance: number
    useAdvancedAI: boolean
    includeReasoning: boolean
  }
  createdAt: string
  updatedAt: string
  isActive: boolean
}

export interface KnowledgeBase {
  id: string
  name: string
  description?: string
  organizationId: string
  projectId?: string
  documentCount: number
  totalChunks: number
  lastIndexed: string
  status: 'active' | 'indexing' | 'error'
  settings: {
    chunkSize: number
    overlapSize: number
    embeddingModel: string
    searchType: 'vector' | 'hybrid' | 'semantic'
  }
  createdAt: string
  updatedAt: string
}

export interface CreateKnowledgeBaseRequest {
  name: string
  description?: string
  organizationId: string
  projectId?: string
  documentIds?: string[]
  settings?: {
    chunkSize?: number
    overlapSize?: number
    embeddingModel?: string
    searchType?: 'vector' | 'hybrid' | 'semantic'
  }
}

/**
 * Hook to perform RAG query
 */
export function useRAGQuery() {
  const { toast } = useToast()
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()

  const mutate = useCallback(async (data: RAGQuery) => {
    try {
      const operation = await startAIOperation({
        type: 'RAG_QUERY',
        parameters: {
          query: data.query,
          maxResults: data.options?.maxResults || 10,
          minRelevance: data.options?.minRelevance || 0.7,
          includeReasoning: data.options?.includeReasoning !== false,
          useAdvancedAI: data.options?.useAdvancedAI !== false,
          temperature: data.options?.temperature || 0.3,
          maxTokens: data.options?.maxTokens || 2000,
          contextWindow: data.options?.contextWindow || 4000,
          filters: data.filters
        },
        organizationId: data.organizationId,
        projectId: data.projectId
      })

      toast({
        title: 'Query completed',
        description: `RAG query has been processed successfully.`,
      })

      return operation.results as RAGResult
    } catch (error: any) {
      toast({
        title: 'Error in RAG query',
        description: error.message || 'There was a problem processing your query. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to get RAG query history
 */
export function useRAGHistory(organizationId: string, params?: {
  projectId?: string
  sessionId?: string
  page?: number
  pageSize?: number
  dateRange?: { start: string; end: string }
}) {
  const operations = useAIOperations()
  const loading = useAILoading()
  const error = useAIError()
  const listOperations = useAIStore(state => state.listOperations)
  const { toast } = useToast()

  // Filter RAG operations from the operations list
  const ragHistory = operations.filter(op => op.type === 'RAG_QUERY')

  const refetch = useCallback(async () => {
    if (!organizationId) return
    try {
      await listOperations({
        type: 'RAG_QUERY',
        organizationId,
        projectId: params?.projectId,
        page: params?.page || 1,
        limit: params?.pageSize || 20
      })
    } catch (error: any) {
      toast({
        title: 'Error fetching RAG history',
        description: error.message || 'Failed to fetch RAG history',
        variant: 'destructive',
      })
    }
  }, [listOperations, organizationId, params, toast])

  return {
    data: ragHistory,
    isLoading: loading,
    error,
    refetch,
    enabled: !!organizationId
  }
}

/**
 * Hook to get RAG sessions
 */
export function useRAGSessions(organizationId: string, params?: {
  projectId?: string
  isActive?: boolean
  page?: number
  pageSize?: number
}) {
  const operations = useAIOperations()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  // Filter RAG sessions from operations
  const sessions = operations.filter(op =>
    op.type === 'RAG_SESSION' &&
    op.organizationId === organizationId &&
    (!params?.projectId || op.projectId === params.projectId)
  )

  return {
    data: sessions,
    isLoading: loading,
    error,
    enabled: !!organizationId
  }
}

/**
 * Hook to create RAG session
 */
export function useCreateRAGSession() {
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  const mutate = useCallback(async (data: {
    title: string
    organizationId: string
    projectId?: string
    settings?: {
      maxResults?: number
      minRelevance?: number
      useAdvancedAI?: boolean
      includeReasoning?: boolean
    }
  }) => {
    try {
      const session = await startAIOperation({
        type: 'RAG_SESSION',
        parameters: {
          title: data.title,
          settings: {
            maxResults: 10,
            minRelevance: 0.7,
            useAdvancedAI: true,
            includeReasoning: true,
            ...data.settings
          }
        },
        organizationId: data.organizationId,
        projectId: data.projectId
      })

      toast({
        title: 'RAG session created',
        description: `RAG session has been created successfully.`,
      })

      return session
    } catch (error: any) {
      toast({
        title: 'Error creating RAG session',
        description: error.message || 'There was a problem creating the RAG session. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to get knowledge bases
 */
export function useKnowledgeBases(organizationId: string, params?: {
  projectId?: string
  status?: string
  page?: number
  pageSize?: number
}) {
  const operations = useAIOperations()
  const loading = useAILoading()
  const error = useAIError()
  const listOperations = useAIStore(state => state.listOperations)
  const { toast } = useToast()

  // Filter knowledge base operations
  const knowledgeBases = operations.filter(op =>
    op.type === 'KNOWLEDGE_BASE' &&
    op.organizationId === organizationId &&
    (!params?.projectId || op.projectId === params.projectId)
  )

  const refetch = useCallback(async () => {
    if (!organizationId) return
    try {
      await listOperations({
        type: 'KNOWLEDGE_BASE',
        organizationId,
        projectId: params?.projectId,
        page: params?.page || 1,
        limit: params?.pageSize || 20
      })
    } catch (error: any) {
      toast({
        title: 'Error fetching knowledge bases',
        description: error.message || 'Failed to fetch knowledge bases',
        variant: 'destructive',
      })
    }
  }, [listOperations, organizationId, params, toast])

  return {
    data: knowledgeBases,
    isLoading: loading,
    error,
    refetch,
    enabled: !!organizationId
  }
}

/**
 * Hook to create knowledge base
 */
export function useCreateKnowledgeBase() {
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  const mutate = useCallback(async (data: CreateKnowledgeBaseRequest) => {
    try {
      const knowledgeBase = await startAIOperation({
        type: 'KNOWLEDGE_BASE',
        parameters: {
          name: data.name,
          description: data.description,
          documentIds: data.documentIds,
          settings: {
            chunkSize: 1000,
            overlapSize: 200,
            embeddingModel: 'cohere-embed',
            searchType: 'hybrid',
            ...data.settings
          }
        },
        organizationId: data.organizationId,
        projectId: data.projectId
      })

      toast({
        title: 'Knowledge base created',
        description: `Knowledge base has been created successfully.`,
      })

      return knowledgeBase
    } catch (error: any) {
      toast({
        title: 'Error creating knowledge base',
        description: error.message || 'There was a problem creating the knowledge base. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to add documents to knowledge base
 */
export function useAddDocumentsToKnowledgeBase() {
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  const mutate = useCallback(async ({
    knowledgeBaseId,
    documentIds,
    organizationId
  }: {
    knowledgeBaseId: string
    documentIds: string[]
    organizationId: string
  }) => {
    try {
      const result = await startAIOperation({
        type: 'KNOWLEDGE_BASE_UPDATE',
        parameters: {
          knowledgeBaseId,
          documentIds,
          action: 'add_documents'
        },
        organizationId
      })

      toast({
        title: 'Documents added',
        description: `Documents have been added to the knowledge base.`,
      })

      return result
    } catch (error: any) {
      toast({
        title: 'Error adding documents',
        description: error.message || 'There was a problem adding documents to the knowledge base. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to get RAG analytics
 */
export function useRAGAnalytics(organizationId: string, params?: {
  projectId?: string
  dateRange?: { start: string; end: string }
}) {
  const operations = useAIOperations()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  // Calculate analytics from RAG operations
  const ragOperations = operations.filter(op =>
    op.type === 'RAG_QUERY' &&
    op.organizationId === organizationId &&
    (!params?.projectId || op.projectId === params.projectId)
  )

  const analytics = {
    totalQueries: ragOperations.length,
    successfulQueries: ragOperations.filter(op => op.status === 'completed').length,
    failedQueries: ragOperations.filter(op => op.status === 'failed').length,
    averageResponseTime: ragOperations.length > 0
      ? ragOperations.reduce((acc, op) => acc + (op.results?.metrics?.processingTime || 0), 0) / ragOperations.length
      : 0,
    topQueries: ragOperations.slice(0, 10),
    queryTrends: ragOperations.reduce((acc, op) => {
      const date = new Date(op.createdAt).toDateString()
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }

  return {
    data: analytics,
    isLoading: loading,
    error,
    enabled: !!organizationId
  }
}

/**
 * Hook to suggest follow-up questions
 */
export function useSuggestFollowUpQuestions() {
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  const mutate = useCallback(async ({
    query,
    answer,
    organizationId
  }: {
    query: string
    answer: string
    organizationId: string
  }) => {
    try {
      const result = await startAIOperation({
        type: 'RAG_SUGGESTIONS',
        parameters: {
          query,
          answer
        },
        organizationId
      })

      return result
    } catch (error: any) {
      toast({
        title: 'Error generating suggestions',
        description: error.message || 'There was a problem generating follow-up questions. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}

/**
 * Hook to export RAG results
 */
export function useExportRAGResults() {
  const startAIOperation = useStartAIOperation()
  const loading = useAILoading()
  const error = useAIError()
  const { toast } = useToast()

  const mutate = useCallback(async ({
    sessionId,
    format = 'pdf',
    organizationId
  }: {
    sessionId: string
    format?: 'pdf' | 'docx' | 'json'
    organizationId: string
  }) => {
    try {
      const result = await startAIOperation({
        type: 'RAG_EXPORT',
        parameters: {
          sessionId,
          format
        },
        organizationId
      })

      if (result.results?.downloadUrl) {
        window.open(result.results.downloadUrl, '_blank')
      }

      toast({
        title: 'Export started',
        description: 'Your RAG results export is being prepared for download.',
      })

      return result
    } catch (error: any) {
      toast({
        title: 'Error exporting results',
        description: error.message || 'There was a problem exporting the RAG results. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [startAIOperation, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: loading,
    error
  }
}
