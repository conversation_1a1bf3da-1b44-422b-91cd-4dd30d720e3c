"use client";

import React, { forwardRef, useId, useState } from "react";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { Input, InputProps } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, CheckCircle, Info } from "lucide-react";

export interface AccessibleFormFieldProps {
  /** Field label */
  label: string;
  /** Field description/help text */
  description?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
  /** Whether the field is required */
  required?: boolean;
  /** Whether the field is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Children (form control) */
  children: React.ReactNode;
}

/**
 * Accessible form field wrapper with proper labeling and error handling
 */
export function AccessibleFormField({
  label,
  description,
  error,
  success,
  required = false,
  disabled = false,
  className,
  children,
}: AccessibleFormFieldProps) {
  const fieldId = useId();
  const descriptionId = description ? `${fieldId}-description` : undefined;
  const errorId = error ? `${fieldId}-error` : undefined;
  const successId = success ? `${fieldId}-success` : undefined;

  // Build aria-describedby
  const ariaDescribedBy = [descriptionId, errorId, successId]
    .filter(Boolean)
    .join(" ") || undefined;

  return (
    <div className={cn("space-y-2", className)}>
      <Label
        htmlFor={fieldId}
        className={cn(
          "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          required && "after:content-['*'] after:ml-0.5 after:text-destructive",
          error && "text-destructive"
        )}
      >
        {label}
      </Label>

      {description && (
        <p
          id={descriptionId}
          className="text-sm text-muted-foreground"
        >
          {description}
        </p>
      )}

      {/* Clone children to add accessibility props */}
      {React.cloneElement(children as React.ReactElement, {
        id: fieldId,
        "aria-describedby": ariaDescribedBy,
        "aria-invalid": error ? "true" : "false",
        "aria-required": required,
        disabled,
      })}

      {error && (
        <div
          id={errorId}
          className="flex items-center gap-2 text-sm text-destructive"
          role="alert"
          aria-live="polite"
        >
          <AlertCircle className="h-4 w-4" aria-hidden="true" />
          <span>{error}</span>
        </div>
      )}

      {success && !error && (
        <div
          id={successId}
          className="flex items-center gap-2 text-sm text-green-600"
          role="status"
          aria-live="polite"
        >
          <CheckCircle className="h-4 w-4" aria-hidden="true" />
          <span>{success}</span>
        </div>
      )}
    </div>
  );
}

export interface AccessibleInputProps extends InputProps {
  /** Input label */
  label: string;
  /** Input description/help text */
  description?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
  /** Whether the input is required */
  required?: boolean;
}

/**
 * Accessible input component with built-in labeling and validation
 */
export const AccessibleInput = forwardRef<HTMLInputElement, AccessibleInputProps>(
  ({ label, description, error, success, required, className, ...props }, ref) => {
    return (
      <AccessibleFormField
        label={label}
        description={description}
        error={error}
        success={success}
        required={required}
        disabled={props.disabled}
        className={className}
      >
        <Input
          ref={ref}
          className={cn(
            error && "border-destructive focus-visible:ring-destructive",
            success && !error && "border-green-500 focus-visible:ring-green-500"
          )}
          {...props}
        />
      </AccessibleFormField>
    );
  }
);

AccessibleInput.displayName = "AccessibleInput";

export interface AccessibleTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /** Textarea label */
  label: string;
  /** Textarea description/help text */
  description?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
  /** Whether the textarea is required */
  required?: boolean;
}

/**
 * Accessible textarea component with built-in labeling and validation
 */
export const AccessibleTextarea = forwardRef<HTMLTextAreaElement, AccessibleTextareaProps>(
  ({ label, description, error, success, required, className, ...props }, ref) => {
    return (
      <AccessibleFormField
        label={label}
        description={description}
        error={error}
        success={success}
        required={required}
        disabled={props.disabled}
        className={className}
      >
        <Textarea
          ref={ref}
          className={cn(
            error && "border-destructive focus-visible:ring-destructive",
            success && !error && "border-green-500 focus-visible:ring-green-500"
          )}
          {...props}
        />
      </AccessibleFormField>
    );
  }
);

AccessibleTextarea.displayName = "AccessibleTextarea";

export interface AccessibleSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  /** Select label */
  label: string;
  /** Select description/help text */
  description?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
  /** Whether the select is required */
  required?: boolean;
  /** Select options */
  options: { value: string; label: string; disabled?: boolean }[];
  /** Placeholder option */
  placeholder?: string;
}

/**
 * Accessible select component with built-in labeling and validation
 */
export const AccessibleSelect = forwardRef<HTMLSelectElement, AccessibleSelectProps>(
  ({ label, description, error, success, required, options, placeholder, className, ...props }, ref) => {
    return (
      <AccessibleFormField
        label={label}
        description={description}
        error={error}
        success={success}
        required={required}
        disabled={props.disabled}
        className={className}
      >
        <select
          ref={ref}
          className={cn(
            "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
            "file:border-0 file:bg-transparent file:text-sm file:font-medium",
            "placeholder:text-muted-foreground",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            "disabled:cursor-not-allowed disabled:opacity-50",
            error && "border-destructive focus-visible:ring-destructive",
            success && !error && "border-green-500 focus-visible:ring-green-500",
            className
          )}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>
      </AccessibleFormField>
    );
  }
);

AccessibleSelect.displayName = "AccessibleSelect";

export interface AccessibleCheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  /** Checkbox label */
  label: string;
  /** Checkbox description/help text */
  description?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
}

/**
 * Accessible checkbox component with built-in labeling
 */
export const AccessibleCheckbox = forwardRef<HTMLInputElement, AccessibleCheckboxProps>(
  ({ label, description, error, success, className, ...props }, ref) => {
    const fieldId = useId();
    const descriptionId = description ? `${fieldId}-description` : undefined;
    const errorId = error ? `${fieldId}-error` : undefined;
    const successId = success ? `${fieldId}-success` : undefined;

    const ariaDescribedBy = [descriptionId, errorId, successId]
      .filter(Boolean)
      .join(" ") || undefined;

    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-start space-x-2">
          <input
            ref={ref}
            type="checkbox"
            id={fieldId}
            className={cn(
              "h-4 w-4 rounded border border-input bg-background",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
              "disabled:cursor-not-allowed disabled:opacity-50",
              error && "border-destructive focus-visible:ring-destructive"
            )}
            aria-describedby={ariaDescribedBy}
            aria-invalid={error ? "true" : "false"}
            {...props}
          />
          <div className="space-y-1">
            <Label
              htmlFor={fieldId}
              className={cn(
                "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                error && "text-destructive"
              )}
            >
              {label}
            </Label>
            {description && (
              <p
                id={descriptionId}
                className="text-sm text-muted-foreground"
              >
                {description}
              </p>
            )}
          </div>
        </div>

        {error && (
          <div
            id={errorId}
            className="flex items-center gap-2 text-sm text-destructive"
            role="alert"
            aria-live="polite"
          >
            <AlertCircle className="h-4 w-4" aria-hidden="true" />
            <span>{error}</span>
          </div>
        )}

        {success && !error && (
          <div
            id={successId}
            className="flex items-center gap-2 text-sm text-green-600"
            role="status"
            aria-live="polite"
          >
            <CheckCircle className="h-4 w-4" aria-hidden="true" />
            <span>{success}</span>
          </div>
        )}
      </div>
    );
  }
);

AccessibleCheckbox.displayName = "AccessibleCheckbox";
