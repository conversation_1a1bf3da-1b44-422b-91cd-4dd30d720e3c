import React, { useRef, useState, useEffect } from 'react';
import {
  Trash2,
  Save,
  Undo,
  Upload,
  Brush,
  Palette
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface SignatureCanvasProps {
  onSave: (signatureData: string) => void;
  onCancel: () => void;
  initialSignature?: string;
  width?: number;
  height?: number;
}

/**
 * Signature Canvas Component
 * Allows users to draw their signature with a mouse or touch input
 * Supports uploading an image, changing pen color, and undoing strokes
 */
const SignatureCanvas: React.FC<SignatureCanvasProps> = ({
  onSave,
  onCancel,
  initialSignature,
  width = 500,
  height = 200
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [context, setContext] = useState<CanvasRenderingContext2D | null>(null);
  const [strokes, setStrokes] = useState<Array<{ points: Array<{x: number, y: number}>, color: string, width: number }>>([]);
  const [currentStroke, setCurrentStroke] = useState<{x: number, y: number}[]>([]);
  const [penColor, setPenColor] = useState('#000000');
  const [penWidth, setPenWidth] = useState(2);
  const [isEmpty, setIsEmpty] = useState(true);

  // Initialize canvas context
  useEffect(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      if (ctx) {
        ctx.lineJoin = 'round';
        ctx.lineCap = 'round';
        ctx.lineWidth = penWidth;
        ctx.strokeStyle = penColor;
        setContext(ctx);

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Load initial signature if provided
        if (initialSignature) {
          const img = new Image();
          img.onload = () => {
            ctx.drawImage(img, 0, 0);
            setIsEmpty(false);
          };
          img.src = initialSignature;
        }
      }
    }
  }, [initialSignature]);

  // Redraw canvas when strokes change
  useEffect(() => {
    if (context && canvasRef.current) {
      // Clear canvas
      context.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);

      // Redraw all strokes
      strokes.forEach(stroke => {
        if (stroke.points.length > 0) {
          context.beginPath();
          context.strokeStyle = stroke.color;
          context.lineWidth = stroke.width;
          context.moveTo(stroke.points[0].x, stroke.points[0].y);

          for (let i = 1; i < stroke.points.length; i++) {
            context.lineTo(stroke.points[i].x, stroke.points[i].y);
          }

          context.stroke();
        }
      });

      // Update isEmpty state
      setIsEmpty(strokes.length === 0);
    }
  }, [strokes, context]);

  // Start drawing
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!context) return;

    setIsDrawing(true);
    setCurrentStroke([]);

    const point = getCoordinates(e);
    if (point) {
      setCurrentStroke([point]);
      context.beginPath();
      context.moveTo(point.x, point.y);
      context.strokeStyle = penColor;
      context.lineWidth = penWidth;
    }
  };

  // Continue drawing
  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !context) return;

    const point = getCoordinates(e);
    if (point) {
      setCurrentStroke(prev => [...prev, point]);
      context.lineTo(point.x, point.y);
      context.stroke();
    }
  };

  // Stop drawing
  const stopDrawing = () => {
    if (!isDrawing || !context) return;

    context.closePath();
    setIsDrawing(false);

    // Add current stroke to strokes array
    if (currentStroke.length > 0) {
      setStrokes(prev => [...prev, {
        points: currentStroke,
        color: penColor,
        width: penWidth
      }]);
    }
  };

  // Get coordinates from mouse or touch event
  const getCoordinates = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return null;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();

    let clientX, clientY;

    // Handle both mouse and touch events
    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0].clientX;
      clientY = e.touches[0].clientY;
    } else {
      // Mouse event
      clientX = e.clientX;
      clientY = e.clientY;
    }

    return {
      x: clientX - rect.left,
      y: clientY - rect.top
    };
  };

  // Clear the canvas
  const clearCanvas = () => {
    if (context && canvasRef.current) {
      context.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      setStrokes([]);
      setIsEmpty(true);
    }
  };

  // Undo last stroke
  const undoLastStroke = () => {
    setStrokes(prev => prev.slice(0, -1));
  };

  // Save signature
  const saveSignature = () => {
    if (canvasRef.current) {
      const dataUrl = canvasRef.current.toDataURL('image/png');
      onSave(dataUrl);
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && context && canvasRef.current) {
      const reader = new FileReader();

      reader.onload = (event) => {
        const img = new Image();
        img.onload = () => {
          // Clear canvas
          context.clearRect(0, 0, canvasRef.current!.width, canvasRef.current!.height);

          // Calculate dimensions to fit image within canvas
          const canvas = canvasRef.current!;
          const hRatio = canvas.width / img.width;
          const vRatio = canvas.height / img.height;
          const ratio = Math.min(hRatio, vRatio);
          const centerX = (canvas.width - img.width * ratio) / 2;
          const centerY = (canvas.height - img.height * ratio) / 2;

          // Draw image
          context.drawImage(
            img,
            0, 0, img.width, img.height,
            centerX, centerY, img.width * ratio, img.height * ratio
          );

          // Convert to strokes (simplified representation)
          setStrokes([{
            points: [
              { x: centerX, y: centerY },
              { x: centerX + img.width * ratio, y: centerY + img.height * ratio }
            ],
            color: penColor,
            width: penWidth
          }]);

          setIsEmpty(false);
        };
        img.src = event.target?.result as string;
      };

      reader.readAsDataURL(file);
    }
  };

  return (
    <TooltipProvider>
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Draw Your Signature</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="relative border rounded-lg bg-white">
            <canvas
              ref={canvasRef}
              width={width}
              height={height}
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              onTouchStart={startDrawing}
              onTouchMove={draw}
              onTouchEnd={stopDrawing}
              className="touch-none cursor-crosshair"
            />

            {isEmpty && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <p className="text-muted-foreground text-sm">
                  Sign here or upload an image
                </p>
              </div>
            )}
          </div>

          <div className="flex justify-center gap-2 mb-4">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Cycle through colors
                    const colors = ['#000000', '#0000FF', '#FF0000', '#008000'];
                    const currentIndex = colors.indexOf(penColor);
                    const nextIndex = (currentIndex + 1) % colors.length;
                    setPenColor(colors[nextIndex]);
                  }}
                  className="flex items-center gap-2"
                >
                  <Palette className="h-4 w-4" style={{ color: penColor }} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Change pen color</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Cycle through widths
                    const widths = [1, 2, 3, 4];
                    const currentIndex = widths.indexOf(penWidth);
                    const nextIndex = (currentIndex + 1) % widths.length;
                    setPenWidth(widths[nextIndex]);
                  }}
                  className="flex items-center gap-2"
                >
                  <Brush className="h-4 w-4" style={{ width: 14 + penWidth * 2 }} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Change pen width</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={undoLastStroke}
                  disabled={strokes.length === 0}
                  className="flex items-center gap-2"
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Undo last stroke</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearCanvas}
                  disabled={isEmpty}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Clear signature</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Upload signature image</TooltipContent>
            </Tooltip>
          </div>

          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileUpload}
          />

          <div className="flex justify-center gap-4">
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button
              onClick={saveSignature}
              disabled={isEmpty}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Save Signature
            </Button>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
};

export default SignatureCanvas;
