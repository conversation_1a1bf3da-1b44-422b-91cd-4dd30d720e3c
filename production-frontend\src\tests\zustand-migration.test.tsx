/**
 * Zustand Migration Test
 * Tests to verify that React Query has been successfully replaced with Zustand
 */

import { renderHook, act } from '@testing-library/react'
import { useTemplateStore, useTemplates, useFetchTemplates } from '../stores/template-store'
import { useProjectStore, useProjects, useFetchProjects } from '../stores/project-store'
import { useAIStore, useAIOperations, useStartAIOperation } from '../stores/ai-store'
import { useDocumentStore } from '../stores/document-store'

// Mock the backend API client
jest.mock('../services/backend-api-client', () => ({
  backendApiClient: {
    request: jest.fn(),
    getProjects: jest.fn(),
    createProject: jest.fn(),
    updateProject: jest.fn(),
    deleteProject: jest.fn(),
    getProject: jest.fn(),
    startAIOperation: jest.fn(),
    getAIOperation: jest.fn(),
    listAIOperations: jest.fn(),
  }
}))

describe('Zustand Migration Tests', () => {
  beforeEach(() => {
    // Clear all stores before each test
    useTemplateStore.getState().reset()
    useProjectStore.setState({
      projects: [],
      selectedProject: null,
      members: {},
      loading: false,
      error: null,
      lastUpdated: undefined,
    })
    useAIStore.getState().reset()
    useDocumentStore.setState({
      documents: [],
      selectedDocument: null,
      loading: false,
      uploading: false,
      error: null,
      lastUpdated: null,
    })
  })

  describe('Template Store', () => {
    it('should initialize with empty state', () => {
      const { result } = renderHook(() => useTemplates())
      expect(result.current).toEqual([])
    })

    it('should provide fetch templates action', () => {
      const { result } = renderHook(() => useFetchTemplates())
      expect(typeof result.current).toBe('function')
    })

    it('should handle template state updates', () => {
      const { result } = renderHook(() => useTemplateStore())
      
      act(() => {
        result.current.setFilters({ categoryId: 'test' })
      })

      expect(result.current.filters.categoryId).toBe('test')
    })
  })

  describe('Project Store', () => {
    it('should initialize with empty projects', () => {
      const { result } = renderHook(() => useProjects())
      expect(result.current).toEqual([])
    })

    it('should provide fetch projects action', () => {
      const { result } = renderHook(() => useFetchProjects())
      expect(typeof result.current).toBe('function')
    })

    it('should handle project selection', () => {
      const { result } = renderHook(() => useProjectStore())
      
      const mockProject = {
        id: '1',
        name: 'Test Project',
        status: 'active' as any,
        organizationId: 'org1',
        visibility: 'private' as any,
        ownerId: 'user-1',
        settings: {},
        metadata: {},
        description: 'Test project',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 'user-1',
        updatedBy: 'user-1',
        tenantId: 'tenant-1'
      }

      act(() => {
        result.current.projects = [mockProject as any]
        result.current.selectedProject = mockProject as any
      })

      expect(result.current.selectedProject?.name).toBe('Test Project')
    })
  })

  describe('AI Store', () => {
    it('should initialize with empty operations', () => {
      const { result } = renderHook(() => useAIOperations())
      expect(result.current).toEqual([])
    })

    it('should provide start AI operation action', () => {
      const { result } = renderHook(() => useStartAIOperation())
      expect(typeof result.current).toBe('function')
    })

    it('should handle operation state updates', () => {
      const { result } = renderHook(() => useAIStore())
      
      const mockOperation = {
        id: '1',
        type: 'RAG_QUERY' as const,
        status: 'pending' as const,
        organizationId: 'org1',
        parameters: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        progress: { completed: 0, total: 100 },
        createdBy: 'user-1'
      }

      act(() => {
        result.current.operations = [mockOperation as any]
        result.current.currentOperation = mockOperation as any
      })

      expect(result.current.currentOperation?.type).toBe('RAG_QUERY')
    })
  })

  describe('Document Store', () => {
    it('should initialize with empty documents', () => {
      const { result } = renderHook(() => useDocumentStore())
      expect(result.current.documents).toEqual([])
    })

    it('should handle document state updates', () => {
      const { result } = renderHook(() => useDocumentStore())
      
      const mockDocument = {
        id: '1',
        name: 'Test Document',
        type: 'pdf',
        size: 1024,
        organizationId: 'org1'
      }

      act(() => {
        result.current.documents = [mockDocument as any]
        result.current.selectedDocument = mockDocument as any
      })

      expect(result.current.selectedDocument?.name).toBe('Test Document')
    })
  })

  describe('Integration Tests', () => {
    it('should not have any React Query dependencies', () => {
      // This test ensures that React Query is completely removed
      expect(() => {
        require('@tanstack/react-query')
      }).toThrow()
    })

    it('should have all Zustand stores properly configured', () => {
      // Verify all stores are accessible
      expect(useTemplateStore).toBeDefined()
      expect(useProjectStore).toBeDefined()
      expect(useAIStore).toBeDefined()
      expect(useDocumentStore).toBeDefined()
    })

    it('should provide consistent API across all stores', () => {
      // All stores should have loading, error, and reset functionality
      const templateStore = useTemplateStore.getState()
      const projectStore = useProjectStore.getState()
      const aiStore = useAIStore.getState()
      const documentStore = useDocumentStore.getState()

      expect(typeof templateStore.loading).toBe('boolean')
      expect(typeof projectStore.loading).toBe('boolean')
      expect(typeof aiStore.loading).toBe('boolean')
      expect(typeof documentStore.loading).toBe('boolean')

      expect(typeof templateStore.reset).toBe('function')
      expect(typeof projectStore.fetchProjects).toBe('function')
      expect(typeof aiStore.reset).toBe('function')
      expect(typeof documentStore.fetchDocuments).toBe('function')
    })
  })
})
