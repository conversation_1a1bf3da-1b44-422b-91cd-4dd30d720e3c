/**
 * Team Management Types
 */

import type { ID, Timestamp, User } from './index'

// Core team interface
export interface Team {
  id: ID
  name: string
  description?: string
  organizationId: ID
  projectId?: ID
  members: TeamMember[]
  settings: TeamSettings
  permissions: TeamPermission[]
  createdBy: ID
  creator: User
  createdAt: Timestamp
  updatedAt: Timestamp
  isActive: boolean

  // Additional properties for compatibility
  memberIds?: string[]
  projectIds?: string[]
}

// Team member
export interface TeamMember {
  id: ID
  teamId: ID
  userId: ID
  user: User
  role: TeamRole
  permissions: string[]
  joinedAt: Timestamp
  addedAt: Timestamp
  isActive: boolean
}

// Team roles
export type TeamRole = 
  | 'owner'
  | 'admin'
  | 'member'
  | 'viewer'

// Team settings
export interface TeamSettings {
  isPublic: boolean
  allowMemberInvites: boolean
  requireApprovalForJoin: boolean
  maxMembers?: number
  defaultRole: TeamRole
}

// Team permissions
export interface TeamPermission {
  userId: ID
  user: User
  permission: 'view' | 'edit' | 'admin'
  grantedBy: ID
  grantedAt: Timestamp
  expiresAt?: Timestamp
}

// Team operations
export interface CreateTeamDto {
  name: string
  description?: string
  organizationId: ID
  projectId?: ID
  settings?: Partial<TeamSettings>
  initialMembers?: {
    userId: ID
    role: TeamRole
  }[]
}

export interface UpdateTeamDto {
  name?: string
  description?: string
  settings?: Partial<TeamSettings>
  isActive?: boolean
}

export interface AddTeamMemberDto {
  userId: ID
  role: TeamRole
  permissions?: string[]
}

export interface UpdateTeamMemberDto {
  role?: TeamRole
  permissions?: string[]
  isActive?: boolean
}

// Team analytics
export interface TeamAnalytics {
  memberCount: number
  activeMembers: number
  recentActivity: TeamActivity[]
  performanceMetrics: TeamMetrics
}

export interface TeamActivity {
  id: ID
  type: 'member_added' | 'member_removed' | 'role_changed' | 'settings_updated'
  description: string
  userId: ID
  user: User
  timestamp: Timestamp
  metadata?: Record<string, any>
}

export interface TeamMetrics {
  tasksCompleted: number
  projectsActive: number
  collaborationScore: number
  averageResponseTime: number
}

// Team search and filters
export interface TeamSearchQuery {
  query?: string
  organizationId?: ID
  projectId?: ID
  isActive?: boolean
  role?: TeamRole
}

export interface TeamSearchResult {
  teams: Team[]
  total: number
  facets: {
    organizations: { organizationId: ID; organizationName: string; count: number }[]
    projects: { projectId: ID; projectName: string; count: number }[]
    roles: { role: TeamRole; count: number }[]
  }
}
