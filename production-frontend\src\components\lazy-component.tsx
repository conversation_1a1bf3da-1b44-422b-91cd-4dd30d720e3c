"use client";

import React, { Suspense, lazy, ComponentType } from "react";
import { LoadingState, LoadingStateProps } from "@/components/ui/loading-state";
import { ErrorBoundary } from "@/components/error-boundary";

export interface LazyComponentProps {
  importFn: () => Promise<{ default: ComponentType<any> }>;
  props?: any;
  fallback?: React.ReactNode;
  loadingProps?: LoadingStateProps;
  errorBoundary?: boolean;
}

/**
 * A component that lazily loads another component
 */
export function LazyComponent({
  importFn,
  props = {},
  fallback,
  loadingProps,
  errorBoundary = true,
}: LazyComponentProps) {
  // Create lazy component
  const LazyComponentImpl = lazy(importFn);

  // Default loading fallback
  const defaultFallback = <LoadingState variant="skeleton" {...loadingProps} />;

  // Render component with suspense
  const content = (
    <Suspense fallback={fallback || defaultFallback}>
      <LazyComponentImpl {...props} />
    </Suspense>
  );

  // Wrap with error boundary if needed
  if (errorBoundary) {
    return <ErrorBoundary>{content}</ErrorBoundary>;
  }

  return content;
}

/**
 * HOC to create a lazy-loaded component
 * @param importFn Function that imports the component
 * @param options Options for the lazy component
 * @returns Lazy-loaded component
 */
export function createLazyComponent<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: Omit<LazyComponentProps, "importFn" | "props"> = {}
): React.FC<P> {
  const LazyComponentWrapper: React.FC<P> = (props) => {
    return (
      <LazyComponent
        importFn={importFn}
        props={props}
        fallback={options.fallback}
        loadingProps={options.loadingProps}
        errorBoundary={options.errorBoundary}
      />
    );
  };

  return LazyComponentWrapper;
}

// Example usage:
// const LazyRichTextEditor = createLazyComponent(() => import("@/components/rich-text-editor"));
// <LazyRichTextEditor value={content} onChange={handleChange} />
