/**
 * Local Storage Hook
 * Provides type-safe local storage with SSR support
 */

import { useState, useEffect, useCallback } from 'react'
import { storage } from '../lib/utils'

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    // Return initial value during SSR
    if (typeof window === 'undefined') {
      return initialValue
    }

    try {
      return storage.get(key, initialValue)
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // Allow value to be a function so we have the same API as useState
        const valueToStore = value instanceof Function ? value(storedValue) : value
        
        // Save state
        setStoredValue(valueToStore)
        
        // Save to local storage
        storage.set(key, valueToStore)
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error)
      }
    },
    [key, storedValue]
  )

  // Remove value from localStorage
  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue)
      storage.remove(key)
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  }, [key, initialValue])

  // Listen for changes to this key from other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue))
        } catch (error) {
          console.warn(`Error parsing localStorage value for key "${key}":`, error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [key])

  return [storedValue, setValue, removeValue]
}

// Hook for session storage
export function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue
    }

    try {
      const item = sessionStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading sessionStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value
        setStoredValue(valueToStore)
        
        if (typeof window !== 'undefined') {
          sessionStorage.setItem(key, JSON.stringify(valueToStore))
        }
      } catch (error) {
        console.warn(`Error setting sessionStorage key "${key}":`, error)
      }
    },
    [key, storedValue]
  )

  const removeValue = useCallback(() => {
    try {
      setStoredValue(initialValue)
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem(key)
      }
    } catch (error) {
      console.warn(`Error removing sessionStorage key "${key}":`, error)
    }
  }, [key, initialValue])

  return [storedValue, setValue, removeValue]
}

// Hook for storing objects with automatic serialization
export function useStoredObject<T extends Record<string, any>>(
  key: string,
  initialValue: T
): [T, (updates: Partial<T>) => void, () => void] {
  const [value, setValue, removeValue] = useLocalStorage(key, initialValue)

  const updateValue = useCallback(
    (updates: Partial<T>) => {
      setValue(prev => ({ ...prev, ...updates }))
    },
    [setValue]
  )

  return [value, updateValue, removeValue]
}

// Hook for storing arrays with helper methods
export function useStoredArray<T>(
  key: string,
  initialValue: T[] = []
): [
  T[],
  {
    add: (item: T) => void
    remove: (index: number) => void
    removeBy: (predicate: (item: T) => boolean) => void
    update: (index: number, item: T) => void
    clear: () => void
    set: (items: T[]) => void
  }
] {
  const [array, setArray, removeArray] = useLocalStorage(key, initialValue)

  const add = useCallback(
    (item: T) => {
      setArray(prev => [...prev, item])
    },
    [setArray]
  )

  const remove = useCallback(
    (index: number) => {
      setArray(prev => prev.filter((_, i) => i !== index))
    },
    [setArray]
  )

  const removeBy = useCallback(
    (predicate: (item: T) => boolean) => {
      setArray(prev => prev.filter(item => !predicate(item)))
    },
    [setArray]
  )

  const update = useCallback(
    (index: number, item: T) => {
      setArray(prev => prev.map((existingItem, i) => i === index ? item : existingItem))
    },
    [setArray]
  )

  const clear = useCallback(() => {
    setArray([])
  }, [setArray])

  const set = useCallback(
    (items: T[]) => {
      setArray(items)
    },
    [setArray]
  )

  return [
    array,
    {
      add,
      remove,
      removeBy,
      update,
      clear,
      set,
    }
  ]
}

// Hook for boolean flags with toggle functionality
export function useStoredFlag(
  key: string,
  initialValue: boolean = false
): [boolean, () => void, (value: boolean) => void] {
  const [flag, setFlag] = useLocalStorage(key, initialValue)

  const toggle = useCallback(() => {
    setFlag(prev => !prev)
  }, [setFlag])

  const set = useCallback(
    (value: boolean) => {
      setFlag(value)
    },
    [setFlag]
  )

  return [flag, toggle, set]
}

// Hook for storing user preferences
export function useUserPreferences<T extends Record<string, any>>(
  userId: string,
  defaultPreferences: T
): [T, (updates: Partial<T>) => void, () => void] {
  const key = `user_preferences_${userId}`
  return useStoredObject(key, defaultPreferences)
}

// Hook for recent items (with max limit)
export function useRecentItems<T>(
  key: string,
  maxItems: number = 10
): [T[], (item: T) => void, () => void] {
  const [items, setItems] = useLocalStorage<T[]>(key, [])

  const addItem = useCallback(
    (item: T) => {
      setItems(prev => {
        // Remove existing item if it exists
        const filtered = prev.filter(existing => 
          JSON.stringify(existing) !== JSON.stringify(item)
        )
        
        // Add to beginning and limit to maxItems
        return [item, ...filtered].slice(0, maxItems)
      })
    },
    [setItems, maxItems]
  )

  const clearItems = useCallback(() => {
    setItems([])
  }, [setItems])

  return [items, addItem, clearItems]
}
