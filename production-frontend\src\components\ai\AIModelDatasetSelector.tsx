'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Search, Database, Plus, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useDatasets } from '@/hooks/ai/useDatasets';

// Using Dataset interface from dataset-service

interface AIModelDatasetSelectorProps {
  selectedDatasets: string[];
  onSelectDatasets: (datasetIds: string[]) => void;
  className?: string;
}

/**
 * Component for selecting datasets to use with AI models
 */
export function AIModelDatasetSelector({
  selectedDatasets,
  onSelectDatasets,
  className,
}: AIModelDatasetSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');

  // Use the datasets hook
  const datasetsQuery = useDatasets();
  const datasets = datasetsQuery.data || [];
  const isLoading = datasetsQuery.isLoading;

  // Filter datasets based on search query
  const filteredDatasets = datasets.filter((dataset: any) =>
    dataset.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    dataset.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Toggle dataset selection
  const toggleDataset = (datasetId: string) => {
    if (selectedDatasets.includes(datasetId)) {
      onSelectDatasets(selectedDatasets.filter((id) => id !== datasetId));
    } else {
      onSelectDatasets([...selectedDatasets, datasetId]);
    }
  };

  return (
    <div className={className}>
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Database className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-medium">Training Datasets</h3>
        </div>

        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search datasets..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {selectedDatasets.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {selectedDatasets.map((datasetId) => {
              const dataset = datasets.find((d: any) => d.id === datasetId);
              return (
                <Badge key={datasetId} variant="secondary" className="flex items-center gap-1">
                  {dataset?.parameters?.name || dataset?.id || datasetId}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 p-0 ml-1"
                    onClick={() => toggleDataset(datasetId)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              );
            })}
          </div>
        )}

        <ScrollArea className="h-[300px] border rounded-md">
          {isLoading ? (
            <div className="p-4 space-y-4">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <Skeleton className="h-4 w-4 mt-1" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                </div>
              ))}
            </div>
          ) : filteredDatasets.length > 0 ? (
            <div className="p-4 space-y-4">
              {filteredDatasets.map((dataset: any) => (
                <div key={dataset.id} className="flex items-start space-x-3">
                  <Checkbox
                    id={`dataset-${dataset.id}`}
                    checked={selectedDatasets.includes(dataset.id)}
                    onCheckedChange={() => toggleDataset(dataset.id)}
                  />
                  <div className="space-y-1">
                    <Label
                      htmlFor={`dataset-${dataset.id}`}
                      className="text-sm font-medium cursor-pointer"
                    >
                      {dataset.name}
                    </Label>
                    <p className="text-xs text-muted-foreground">{dataset.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>{dataset.documentCount} documents</span>
                      <span>Updated: {dataset.lastUpdated}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <Database className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <h3 className="text-sm font-medium">No datasets found</h3>
              <p className="text-xs text-muted-foreground mt-1">
                Try adjusting your search or create a new dataset.
              </p>
              <Button variant="outline" size="sm" className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Create Dataset
              </Button>
            </div>
          )}
        </ScrollArea>
      </div>
    </div>
  );
}
