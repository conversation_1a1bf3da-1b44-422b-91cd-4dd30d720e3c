'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Brain, MessageCircle, FileText, TrendingUp, Sparkles, Clock, Users } from 'lucide-react'
import { aiService } from '@/services/ai-service'

interface AIInsight {
  id: string
  type: 'trend' | 'recommendation' | 'alert' | 'summary'
  title: string
  description: string
  confidence: number
  timestamp: number
  actionable: boolean
}

export function AIInsightsWidget() {
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [chatSessions, setChatSessions] = useState(0)
  const [activeOperations, setActiveOperations] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadAIData()
    
    // Refresh every 2 minutes
    const interval = setInterval(loadAIData, 120000)
    return () => clearInterval(interval)
  }, [])

  const loadAIData = async () => {
    setIsLoading(true)
    
    try {
      // Get chat sessions
      const sessions = aiService.getAllChatSessions()
      setChatSessions(sessions.length)

      // Get active operations
      const operations = aiService.getActiveOperations()
      setActiveOperations(operations.length)

      // Generate AI insights from real system data and analytics
      try {
        const response = await fetch('/api/ai/insights', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const data = await response.json();
          setInsights(data.insights || []);
        } else {
          // Fallback to generating insights from available data
          const fallbackInsights = await generateFallbackInsights(sessions, activeOperations);
          setInsights(fallbackInsights);
        }
      } catch (error) {
        console.error('Failed to fetch AI insights:', error);

        // Generate fallback insights from local data
        const fallbackInsights = await generateFallbackInsights(sessions, activeOperations);
        setInsights(fallbackInsights);
      }
    } catch (error) {
      console.error('Failed to load AI data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Generate fallback insights from available local data
  const generateFallbackInsights = async (sessions: any[], operations: number): Promise<AIInsight[]> => {
    const insights: AIInsight[] = [];
    const now = Date.now();

    // Session-based insights
    if (sessions.length > 0) {
      const avgMessages = sessions.reduce((sum, s) => sum + s.messages.length, 0) / sessions.length;

      insights.push({
        id: 'session-summary',
        type: 'summary',
        title: 'AI Chat Activity',
        description: `${sessions.length} chat sessions with an average of ${avgMessages.toFixed(1)} messages per session.`,
        confidence: 1.0,
        timestamp: now - 3600000,
        actionable: false
      });

      if (sessions.length > 10) {
        insights.push({
          id: 'usage-trend',
          type: 'trend',
          title: 'High AI Usage',
          description: 'You\'re actively using AI features. Consider exploring advanced automation options.',
          confidence: 0.85,
          timestamp: now - 7200000,
          actionable: true
        });
      }
    }

    // Operations-based insights
    if (operations > 5) {
      insights.push({
        id: 'operations-alert',
        type: 'alert',
        title: 'High Processing Load',
        description: `${operations} AI operations are currently active. Monitor performance for optimal results.`,
        confidence: 0.75,
        timestamp: now - 1800000,
        actionable: true
      });
    }

    // General recommendations
    if (insights.length === 0) {
      insights.push({
        id: 'getting-started',
        type: 'recommendation',
        title: 'Explore AI Features',
        description: 'Start using AI chat and document analysis to get personalized insights and recommendations.',
        confidence: 0.9,
        timestamp: now - 86400000,
        actionable: true
      });
    }

    return insights;
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <TrendingUp className="h-4 w-4" />
      case 'recommendation':
        return <Sparkles className="h-4 w-4" />
      case 'alert':
        return <Clock className="h-4 w-4" />
      case 'summary':
        return <FileText className="h-4 w-4" />
      default:
        return <Brain className="h-4 w-4" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'trend':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'recommendation':
        return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'alert':
        return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'summary':
        return 'text-green-600 bg-green-50 border-green-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Just now'
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600'
    if (confidence >= 0.7) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Brain className="h-4 w-4" />
          AI Insights
        </CardTitle>
        <CardDescription>
          Intelligent recommendations and analytics from your AI usage
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="insights" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="insights" className="space-y-4">
            {/* AI Insights */}
            <div className="space-y-3">
              {insights.map((insight) => (
                <div
                  key={insight.id}
                  className={`p-3 border rounded-lg ${getInsightColor(insight.type)}`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getInsightIcon(insight.type)}
                      <span className="font-medium text-sm">{insight.title}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={getConfidenceColor(insight.confidence)}>
                        {Math.round(insight.confidence * 100)}%
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(insight.timestamp)}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-sm mb-3">{insight.description}</p>
                  
                  {insight.actionable && (
                    <Button variant="outline" size="sm" className="h-7 text-xs">
                      Take Action
                    </Button>
                  )}
                </div>
              ))}

              {insights.length === 0 && !isLoading && (
                <div className="text-center py-8 text-muted-foreground">
                  <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No AI insights available yet</p>
                  <p className="text-sm">Start using AI features to see personalized recommendations</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            {/* AI Activity Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{chatSessions}</div>
                <div className="text-sm text-muted-foreground flex items-center justify-center gap-1">
                  <MessageCircle className="h-3 w-3" />
                  Chat Sessions
                </div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">{activeOperations}</div>
                <div className="text-sm text-muted-foreground flex items-center justify-center gap-1">
                  <Brain className="h-3 w-3" />
                  Active Operations
                </div>
              </div>
            </div>

            {/* Recent AI Activity */}
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Recent Activity
              </h4>
              
              <div className="space-y-2">
                {chatSessions > 0 ? (
                  aiService.getAllChatSessions().slice(0, 3).map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <MessageCircle className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium">{session.title}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {session.messages.length} messages
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatTimeAgo(session.updatedAt)}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No recent AI activity
                  </p>
                )}
              </div>
            </div>

            {/* AI Usage Tips */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                AI Usage Tips
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Ask specific questions for better AI responses</li>
                <li>• Use document context for more accurate analysis</li>
                <li>• Try batch processing for multiple documents</li>
                <li>• Provide feedback to improve AI recommendations</li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
