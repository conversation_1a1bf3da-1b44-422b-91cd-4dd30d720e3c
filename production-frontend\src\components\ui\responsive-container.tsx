"use client";

import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

export interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  breakpoints?: {
    sm?: React.ReactNode;
    md?: React.ReactNode;
    lg?: React.ReactNode;
    xl?: React.ReactNode;
  };
  defaultContent?: React.ReactNode;
  className?: string;
}

/**
 * A component that renders different content based on screen size
 */
export function ResponsiveContainer({
  children,
  breakpoints,
  defaultContent,
  className,
  ...props
}: ResponsiveContainerProps) {
  const [screenSize, setScreenSize] = useState<"sm" | "md" | "lg" | "xl" | null>(null);

  useEffect(() => {
    // Function to update screen size
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 640) {
        setScreenSize("sm");
      } else if (width < 768) {
        setScreenSize("md");
      } else if (width < 1024) {
        setScreenSize("lg");
      } else {
        setScreenSize("xl");
      }
    };

    // Initial update
    updateScreenSize();

    // Add event listener
    window.addEventListener("resize", updateScreenSize);

    // Cleanup
    return () => {
      window.removeEventListener("resize", updateScreenSize);
    };
  }, []);

  // If no screen size is detected yet (during SSR), render default content or children
  if (!screenSize) {
    return (
      <div className={cn(className)} {...props}>
        {defaultContent || children}
      </div>
    );
  }

  // Render content based on screen size
  if (breakpoints) {
    if (screenSize === "sm" && breakpoints.sm) {
      return (
        <div className={cn(className)} {...props}>
          {breakpoints.sm}
        </div>
      );
    }

    if (screenSize === "md" && breakpoints.md) {
      return (
        <div className={cn(className)} {...props}>
          {breakpoints.md}
        </div>
      );
    }

    if (screenSize === "lg" && breakpoints.lg) {
      return (
        <div className={cn(className)} {...props}>
          {breakpoints.lg}
        </div>
      );
    }

    if (screenSize === "xl" && breakpoints.xl) {
      return (
        <div className={cn(className)} {...props}>
          {breakpoints.xl}
        </div>
      );
    }
  }

  // Fallback to default children
  return (
    <div className={cn(className)} {...props}>
      {children}
    </div>
  );
}

/**
 * A component that only renders on mobile devices
 */
export function MobileOnly({
  children,
  className,
  ...props
}: Omit<ResponsiveContainerProps, "breakpoints" | "defaultContent">) {
  return (
    <div className={cn("md:hidden", className)} {...props}>
      {children}
    </div>
  );
}

/**
 * A component that only renders on tablet and larger devices
 */
export function TabletAndAbove({
  children,
  className,
  ...props
}: Omit<ResponsiveContainerProps, "breakpoints" | "defaultContent">) {
  return (
    <div className={cn("hidden md:block", className)} {...props}>
      {children}
    </div>
  );
}

/**
 * A component that only renders on desktop devices
 */
export function DesktopOnly({
  children,
  className,
  ...props
}: Omit<ResponsiveContainerProps, "breakpoints" | "defaultContent">) {
  return (
    <div className={cn("hidden lg:block", className)} {...props}>
      {children}
    </div>
  );
}
