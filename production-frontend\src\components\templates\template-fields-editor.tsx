"use client";

import { useEffect } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from "@dnd-kit/sortable";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import {
  Plus,
  Layers,
  AlertCircle
} from "lucide-react";
import { Template } from "@/services/template-service";
import {
  TemplateSection,
  TemplateField,
  FieldType
} from "@/services/template-fields-service";
import { useTemplateFields } from "@/hooks/templates/useTemplateFields";
import { SortableTemplateSection } from "./sortable-template-section";
import { cn } from "@/lib/utils";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface TemplateFieldsEditorProps {
  template?: Template;
  onChange: (sections: TemplateSection[]) => void;
  className?: string;
}

export function TemplateFieldsEditor({
  template,
  onChange,
  className
}: TemplateFieldsEditorProps) {
  // Use the template fields hook
  const {
    sections,
    setSections,
    isLoadingSections,
    sectionsError,
    addSection,
    updateSection,
    deleteSection,
    addField,
    updateField,
    deleteField,
    createField
  } = useTemplateFields({ templateId: template?.id || '' });

  // Set up DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Update parent component when sections change
  useEffect(() => {
    onChange(sections);
  }, [sections, onChange]);

  // Handle section drag end
  const handleSectionDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setSections((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        const reorderedItems = arrayMove(items, oldIndex, newIndex);

        // Update order property
        return reorderedItems.map((item, index) => ({
          ...item,
          order: index
        }));
      });
    }
  };

  // Handle field drag end
  const handleFieldDragEnd = (sectionId: string, event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      const section = sections.find(s => s.id === sectionId);
      if (!section) return;

      const oldIndex = section.fields.findIndex((field: any) => field.id === active.id);
      const newIndex = section.fields.findIndex((field: any) => field.id === over.id);

      const reorderedFields = arrayMove(section.fields, oldIndex, newIndex);

      // Update order property
      const updatedFields = reorderedFields.map((field, index) => ({
        ...(field as any),
        order: index
      }));

      const updatedSection = {
        ...section,
        fields: updatedFields
      };

      updateSection({ sectionId, section: updatedSection });
    }
  };

  // Handle adding a new section
  const handleAddSection = () => {
    addSection(`Section ${sections.length + 1}`, "");
  };

  // Handle adding a field to a section
  const handleAddField = (sectionId: string, type: FieldType) => {
    createField(sectionId, type);
  };

  if (isLoadingSections) {
    return (
      <div className="flex items-center justify-center p-8">
        <Spinner className="mr-2" />
        <span>Loading template sections...</span>
      </div>
    );
  }

  if (sectionsError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load template sections. Please try again.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Template Sections and Fields</h3>
        <Button
          type="button"
          onClick={handleAddSection}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Section
        </Button>
      </div>

      {sections.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg bg-muted/50">
          <Layers className="h-8 w-8 text-muted-foreground mb-2" />
          <p className="text-muted-foreground text-center">
            No sections added yet. Use the "Add Section" button to create sections for your template.
          </p>
        </div>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleSectionDragEnd}
        >
          <SortableContext
            items={sections.map(section => section.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {sections.map((section) => (
                <SortableTemplateSection
                  key={section.id}
                  section={section}
                  onUpdate={(updatedSection) => updateSection({ sectionId: updatedSection.id, section: updatedSection })}
                  onRemove={(sectionId) => deleteSection(sectionId)}
                  onAddField={handleAddField}
                  onRemoveField={(sectionId, fieldId) => deleteField({ sectionId, fieldId })}
                  onUpdateField={(_sectionId, field) => updateField(field.id, field as any)}
                  onFieldDragEnd={(event) => handleFieldDragEnd(section.id, event)}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  );
}
