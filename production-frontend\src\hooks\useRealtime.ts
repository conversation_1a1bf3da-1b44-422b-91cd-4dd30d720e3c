import { useEffect, useCallback, useRef, useState } from 'react'
import { useAuth } from './useAuthStore'

/**
 * Realtime Hook
 * Manages WebSocket connections and real-time updates
 */

export interface RealtimeOptions {
  autoConnect?: boolean
  reconnectAttempts?: number
  reconnectDelay?: number
  heartbeatInterval?: number
}

export interface RealtimeMessage {
  type: string
  payload: any
  timestamp: string
  id?: string
}

export interface UseRealtimeResult {
  connected: boolean
  connecting: boolean
  error: string | null
  connect: () => void
  disconnect: () => void
  send: (message: RealtimeMessage) => void
  subscribe: (channel: string, callback: (message: RealtimeMessage) => void) => () => void
  unsubscribe: (channel: string) => void
}

export function useRealtime(options: RealtimeOptions = {}): UseRealtimeResult {
  const {
    autoConnect = true,
    reconnectAttempts = 5,
    reconnectDelay = 1000,
    heartbeatInterval = 30000
  } = options

  const { user, isAuthenticated } = useAuth()
  const [connected, setConnected] = useState(false)
  const [connecting, setConnecting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectCountRef = useRef(0)
  const heartbeatIntervalRef = useRef<NodeJS.Timeout>()
  const subscriptionsRef = useRef<Map<string, Set<(message: RealtimeMessage) => void>>>(new Map())

  const getWebSocketUrl = useCallback(() => {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const host = process.env.NEXT_PUBLIC_WS_HOST || window.location.host
    return `${protocol}//${host}/ws`
  }, [])

  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify({ type: 'ping' }))
      }
    }, heartbeatInterval)
  }, [heartbeatInterval])

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
      heartbeatIntervalRef.current = undefined
    }
  }, [])

  const connect = useCallback(() => {
    if (!isAuthenticated || !user) {
      setError('User must be authenticated to connect')
      return
    }

    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return
    }

    setConnecting(true)
    setError(null)

    try {
      const ws = new WebSocket(getWebSocketUrl())
      wsRef.current = ws

      ws.onopen = () => {
        setConnected(true)
        setConnecting(false)
        setError(null)
        reconnectCountRef.current = 0
        startHeartbeat()

        // Send authentication
        ws.send(JSON.stringify({
          type: 'auth',
          payload: { userId: user.id }
        }))
      }

      ws.onmessage = (event) => {
        try {
          const message: RealtimeMessage = JSON.parse(event.data)
          
          // Handle system messages
          if (message.type === 'pong') {
            return
          }

          // Broadcast to subscribers
          const channel = message.type
          const subscribers = subscriptionsRef.current.get(channel)
          if (subscribers) {
            subscribers.forEach(callback => callback(message))
          }
        } catch (err) {
          console.error('Failed to parse WebSocket message:', err)
        }
      }

      ws.onclose = () => {
        setConnected(false)
        setConnecting(false)
        stopHeartbeat()

        // Attempt reconnection
        if (reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++
          setTimeout(() => {
            connect()
          }, reconnectDelay * reconnectCountRef.current)
        } else {
          setError('Failed to reconnect after maximum attempts')
        }
      }

      ws.onerror = (event) => {
        setError('WebSocket connection error')
        setConnecting(false)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to connect')
      setConnecting(false)
    }
  }, [isAuthenticated, user, getWebSocketUrl, startHeartbeat, stopHeartbeat, reconnectAttempts, reconnectDelay])

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
    setConnected(false)
    setConnecting(false)
    stopHeartbeat()
  }, [stopHeartbeat])

  const send = useCallback((message: RealtimeMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }, [])

  const subscribe = useCallback((channel: string, callback: (message: RealtimeMessage) => void) => {
    if (!subscriptionsRef.current.has(channel)) {
      subscriptionsRef.current.set(channel, new Set())
    }
    subscriptionsRef.current.get(channel)!.add(callback)

    // Send subscription message
    send({
      type: 'subscribe',
      payload: { channel },
      timestamp: new Date().toISOString()
    })

    // Return unsubscribe function
    return () => {
      const subscribers = subscriptionsRef.current.get(channel)
      if (subscribers) {
        subscribers.delete(callback)
        if (subscribers.size === 0) {
          subscriptionsRef.current.delete(channel)
          send({
            type: 'unsubscribe',
            payload: { channel },
            timestamp: new Date().toISOString()
          })
        }
      }
    }
  }, [send])

  const unsubscribe = useCallback((channel: string) => {
    subscriptionsRef.current.delete(channel)
    send({
      type: 'unsubscribe',
      payload: { channel },
      timestamp: new Date().toISOString()
    })
  }, [send])

  // Auto-connect on mount if enabled
  useEffect(() => {
    if (autoConnect && isAuthenticated) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect, isAuthenticated, connect, disconnect])

  return {
    connected,
    connecting,
    error,
    connect,
    disconnect,
    send,
    subscribe,
    unsubscribe,
  }
}

/**
 * Hook for subscribing to specific channels
 */
export function useRealtimeSubscription(
  channel: string,
  callback: (message: RealtimeMessage) => void,
  enabled = true
) {
  const { subscribe } = useRealtime()

  useEffect(() => {
    if (!enabled) return

    const unsubscribe = subscribe(channel, callback)
    return unsubscribe
  }, [channel, callback, enabled, subscribe])
}
