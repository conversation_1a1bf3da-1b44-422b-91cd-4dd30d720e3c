/**
 * Organization Members Hook
 * Manages organization member operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { organizationService } from '@/services/organization-service'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'
import type { OrganizationMember } from '@/types/organization'

export interface UseOrganizationMembersOptions {
  organizationId: ID
  autoLoad?: boolean
  includeInactive?: boolean
}

export interface UseOrganizationMembersResult {
  // State
  members: OrganizationMember[]
  loading: boolean
  error: string | null
  
  // Member operations
  loadMembers: () => Promise<void>
  addMember: (userId: ID, role: string, permissions?: string[]) => Promise<void>
  updateMember: (userId: ID, updates: Partial<OrganizationMember>) => Promise<void>
  removeMember: (userId: ID) => Promise<void>
  
  // Role management
  updateMemberRole: (userId: ID, role: string) => Promise<void>
  updateMemberPermissions: (userId: ID, permissions: string[]) => Promise<void>
  
  // Status management
  activateMember: (userId: ID) => Promise<void>
  deactivateMember: (userId: ID) => Promise<void>
  
  // Bulk operations
  bulkUpdateRoles: (updates: Array<{ userId: ID; role: string }>) => Promise<void>
  bulkRemoveMembers: (userIds: ID[]) => Promise<void>
  
  // Search and filtering
  searchMembers: (query: string) => OrganizationMember[]
  filterMembersByRole: (role: string) => OrganizationMember[]
  filterMembersByStatus: (status: string) => OrganizationMember[]
  
  // Statistics
  getMemberStats: () => {
    total: number
    active: number
    inactive: number
    byRole: Record<string, number>
  }
  
  // Refresh
  refresh: () => Promise<void>
}

export function useOrganizationMembers(options: UseOrganizationMembersOptions): UseOrganizationMembersResult {
  const { organizationId, autoLoad = true, includeInactive = false } = options
  const { toast } = useToast()
  
  const [members, setMembers] = useState<OrganizationMember[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load members
  const loadMembers = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await organizationService.getMembers(organizationId)
      setMembers((response as OrganizationMember[]) || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load organization members'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [organizationId, includeInactive, toast])

  // Add member
  const addMember = useCallback(async (userId: ID, role: string, _permissions?: string[]) => {
    try {
      await organizationService.addMember(organizationId, {
        userId,
        role
      })
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Member added',
        description: 'Member has been added to the organization.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to add member'
      
      toast({
        type: 'error',
        title: 'Add failed',
        description: errorMessage,
      })
    }
  }, [organizationId, loadMembers, toast])

  // Update member (wrapper for interface compatibility)
  const updateMember = useCallback(async (userId: ID, updates: Partial<OrganizationMember>) => {
    try {
      // Only role updates are supported by the backend
      if (updates.role) {
        await organizationService.updateMemberRole(organizationId, userId, updates.role)
        await loadMembers()

        toast({
          type: 'success',
          title: 'Member updated',
          description: 'Member has been updated successfully.',
        })
      } else {
        toast({
          type: 'info',
          title: 'Not implemented',
          description: 'Only role updates are currently supported.',
        })
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update member'

      toast({
        type: 'error',
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [organizationId, loadMembers, toast])

  // Remove member
  const removeMember = useCallback(async (userId: ID) => {
    try {
      await organizationService.removeMember(organizationId, userId)
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Member removed',
        description: 'Member has been removed from the organization.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to remove member'
      
      toast({
        type: 'error',
        title: 'Remove failed',
        description: errorMessage,
      })
    }
  }, [organizationId, loadMembers, toast])

  // Update member role
  const updateMemberRole = useCallback(async (userId: ID, role: string) => {
    await updateMember(userId, { role })
  }, [updateMember])

  // Update member permissions (placeholder - not supported by backend)
  const updateMemberPermissions = useCallback(async (_userId: ID, _permissions: string[]) => {
    // TODO: Implement permissions update when backend supports it
    toast({
      type: 'info',
      title: 'Not implemented',
      description: 'Permission updates are not yet supported.',
    })
  }, [toast])

  // Activate member (placeholder - not supported by backend)
  const activateMember = useCallback(async (_userId: ID) => {
    // TODO: Implement member activation when backend supports it
    toast({
      type: 'info',
      title: 'Not implemented',
      description: 'Member activation is not yet supported.',
    })
  }, [toast])

  // Deactivate member (placeholder - not supported by backend)
  const deactivateMember = useCallback(async (_userId: ID) => {
    // TODO: Implement member deactivation when backend supports it
    toast({
      type: 'info',
      title: 'Not implemented',
      description: 'Member deactivation is not yet supported.',
    })
  }, [toast])

  // Bulk update roles
  const bulkUpdateRoles = useCallback(async (updates: Array<{ userId: ID; role: string }>) => {
    try {
      await Promise.all(
        updates.map(update =>
          organizationService.updateMemberRole(organizationId, update.userId, update.role)
        )
      )
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Roles updated',
        description: `${updates.length} member roles have been updated.`,
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update roles'
      
      toast({
        type: 'error',
        title: 'Bulk update failed',
        description: errorMessage,
      })
    }
  }, [organizationId, loadMembers, toast])

  // Bulk remove members
  const bulkRemoveMembers = useCallback(async (userIds: ID[]) => {
    try {
      await Promise.all(
        userIds.map(userId => 
          organizationService.removeMember(organizationId, userId)
        )
      )
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Members removed',
        description: `${userIds.length} members have been removed.`,
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to remove members'
      
      toast({
        type: 'error',
        title: 'Bulk remove failed',
        description: errorMessage,
      })
    }
  }, [organizationId, loadMembers, toast])

  // Search members
  const searchMembers = useCallback((query: string): OrganizationMember[] => {
    if (!query.trim()) return members
    
    const lowercaseQuery = query.toLowerCase()
    return members.filter(member => 
      member.user.name.toLowerCase().includes(lowercaseQuery) ||
      member.user.email.toLowerCase().includes(lowercaseQuery) ||
      member.role.toLowerCase().includes(lowercaseQuery)
    )
  }, [members])

  // Filter by role
  const filterMembersByRole = useCallback((role: string): OrganizationMember[] => {
    return members.filter(member => member.role === role)
  }, [members])

  // Filter by status (placeholder - status not supported by backend)
  const filterMembersByStatus = useCallback((_status: string): OrganizationMember[] => {
    // TODO: Implement status filtering when backend supports member status
    return members
  }, [members])

  // Get member statistics
  const getMemberStats = useCallback(() => {
    const stats = {
      total: members.length,
      active: 0,
      inactive: 0,
      pending: 0,
      byRole: {} as Record<string, number>
    }

    members.forEach(member => {
      // Count by status - check actual member status
      const memberStatus = member.status || 'active';

      switch (memberStatus.toLowerCase()) {
        case 'active':
        case 'accepted':
          stats.active++;
          break;
        case 'pending':
        case 'invited':
          stats.pending++;
          break;
        case 'inactive':
        case 'suspended':
          stats.inactive++;
          break;
        default:
          stats.active++; // Default to active for unknown statuses
      }

      // Count by role
      if (stats.byRole[member.role]) {
        stats.byRole[member.role]++
      } else {
        stats.byRole[member.role] = 1
      }
    })

    return stats
  }, [members])

  // Load members on mount if autoLoad is enabled
  useEffect(() => {
    if (autoLoad) {
      loadMembers()
    }
  }, [autoLoad, loadMembers])

  return {
    // State
    members,
    loading,
    error,
    
    // Member operations
    loadMembers,
    addMember,
    updateMember,
    removeMember,
    
    // Role management
    updateMemberRole,
    updateMemberPermissions,
    
    // Status management
    activateMember,
    deactivateMember,
    
    // Bulk operations
    bulkUpdateRoles,
    bulkRemoveMembers,
    
    // Search and filtering
    searchMembers,
    filterMembersByRole,
    filterMembersByStatus,
    
    // Statistics
    getMemberStats,
    
    // Refresh
    refresh: loadMembers,
  }
}

/**
 * Hook for member activity tracking
 */
export function useMemberActivity(memberId?: string) {
  const [activities, setActivities] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadActivities = useCallback(async () => {
    if (!memberId) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/organizations/members/${memberId}/activities`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load member activities: ${response.statusText}`);
      }

      const result = await response.json();
      setActivities(result.activities || []);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load member activities'
      setError(errorMessage)
      console.error('Error loading member activities:', err)
    } finally {
      setIsLoading(false)
    }
  }, [memberId])

  useEffect(() => {
    loadActivities()
  }, [loadActivities])

  return {
    activities,
    isLoading,
    error,
    refresh: loadActivities,
  }
}
