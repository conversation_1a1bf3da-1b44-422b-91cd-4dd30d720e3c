/**
 * Workflow Service
 * Handles all workflow-related API calls using backend API client
 */

import { backendApiClient, type PaginatedResponse } from './backend-api-client'
import type { Workflow, WorkflowStatus } from '../types/backend'

class WorkflowService {
  /**
   * Get all workflows with optional filters
   */
  async getWorkflows(params?: {
    organizationId?: string
    projectId?: string
    status?: WorkflowStatus[]
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<Workflow>> {
    return await backendApiClient.getWorkflows(params)
  }

  /**
   * Get workflow by ID
   */
  async getWorkflow(workflowId: string): Promise<Workflow> {
    return await backendApiClient.getWorkflow(workflowId)
  }

  /**
   * Create new workflow
   */
  async createWorkflow(workflowData: {
    name: string
    description?: string
    organizationId: string
    projectId?: string
    definition: any
    triggers?: any[]
    isActive?: boolean
  }): Promise<Workflow> {
    return await backendApiClient.createWorkflow(workflowData)
  }

  /**
   * Update workflow
   */
  async updateWorkflow(workflowId: string, updateData: Partial<Workflow>): Promise<Workflow> {
    return await backendApiClient.updateWorkflow(workflowId, updateData)
  }

  /**
   * Delete workflow
   */
  async deleteWorkflow(workflowId: string): Promise<void> {
    return await backendApiClient.deleteWorkflow(workflowId)
  }

  /**
   * Execute workflow
   */
  async executeWorkflow(workflowId: string, input?: any): Promise<{
    executionId: string
    status: string
  }> {
    return await backendApiClient.executeWorkflow(workflowId, input)
  }

  /**
   * Get workflow executions
   */
  async getWorkflowExecutions(workflowId: string, params?: {
    page?: number
    pageSize?: number
    status?: string[]
  }): Promise<PaginatedResponse<{
    id: string
    workflowId: string
    status: string
    startedAt: string
    completedAt?: string
    input: any
    output?: any
    error?: string
  }>> {
    return await backendApiClient.getWorkflowExecutions(workflowId, params)
  }

  /**
   * Get workflow execution by ID
   */
  async getWorkflowExecution(executionId: string): Promise<{
    id: string
    workflowId: string
    status: string
    startedAt: string
    completedAt?: string
    input: any
    output?: any
    error?: string
    steps: Array<{
      id: string
      name: string
      status: string
      startedAt: string
      completedAt?: string
      input: any
      output?: any
      error?: string
    }>
  }> {
    return await backendApiClient.getWorkflowExecution(executionId)
  }

  /**
   * Cancel workflow execution
   */
  async cancelWorkflowExecution(executionId: string): Promise<void> {
    return await backendApiClient.cancelWorkflowExecution(executionId)
  }

  /**
   * Retry workflow execution
   */
  async retryWorkflowExecution(executionId: string): Promise<{
    executionId: string
    status: string
  }> {
    return await backendApiClient.retryWorkflowExecution(executionId)
  }

  /**
   * Get workflow analytics
   */
  async getWorkflowAnalytics(workflowId?: string): Promise<{
    totalExecutions: number
    successfulExecutions: number
    failedExecutions: number
    averageExecutionTime: number
    executionStats: Array<{
      date: string
      executions: number
      successes: number
      failures: number
    }>
  }> {
    if (workflowId) {
      return await backendApiClient.getWorkflowAnalytics(workflowId)
    } else {
      return await backendApiClient.request('/workflows/analytics')
    }
  }

  /**
   * Search workflows
   */
  async searchWorkflows(query: string, filters?: {
    organizationId?: string
    projectId?: string
    status?: WorkflowStatus[]
    tags?: string[]
  }): Promise<PaginatedResponse<Workflow>> {
    return await backendApiClient.searchWorkflows(query, filters)
  }

  /**
   * Clone workflow
   */
  async cloneWorkflow(workflowId: string, name: string): Promise<Workflow> {
    return await backendApiClient.cloneWorkflow(workflowId, name)
  }

  /**
   * Export workflow
   */
  async exportWorkflow(workflowId: string, format: 'json' | 'yaml'): Promise<{
    downloadUrl: string
    expiresAt: string
  }> {
    return await backendApiClient.exportWorkflow(workflowId, format)
  }

  /**
   * Import workflow
   */
  async importWorkflow(file: File, organizationId: string, projectId?: string): Promise<Workflow> {
    return await backendApiClient.importWorkflow(file, organizationId, projectId)
  }

  /**
   * Validate workflow definition
   */
  async validateWorkflow(definition: any): Promise<{
    isValid: boolean
    errors: Array<{
      path: string
      message: string
    }>
    warnings: Array<{
      path: string
      message: string
    }>
  }> {
    return await backendApiClient.validateWorkflow(definition)
  }

  /**
   * Test workflow
   */
  async testWorkflow(workflowId: string, testInput?: any): Promise<{
    success: boolean
    output?: any
    error?: string
    executionTime: number
    steps: Array<{
      name: string
      success: boolean
      output?: any
      error?: string
      executionTime: number
    }>
  }> {
    return await backendApiClient.testWorkflow(workflowId, testInput)
  }

  /**
   * Get workflow templates
   */
  async getWorkflowTemplates(params?: {
    category?: string
    page?: number
    pageSize?: number
    search?: string
  }): Promise<PaginatedResponse<{
    id: string
    name: string
    description: string
    category: string
    definition: any
    isPublic: boolean
    createdBy: string
    createdAt: string
  }>> {
    return await backendApiClient.getWorkflowTemplates(params)
  }

  /**
   * Create workflow from template
   */
  async createWorkflowFromTemplate(templateId: string, workflowData: {
    name: string
    description?: string
    organizationId: string
    projectId?: string
  }): Promise<Workflow> {
    return await backendApiClient.createWorkflowFromTemplate(templateId, workflowData)
  }

  /**
   * Save workflow as template
   */
  async saveWorkflowAsTemplate(workflowId: string, templateData: {
    name: string
    description?: string
    category: string
    isPublic?: boolean
  }): Promise<{
    id: string
    name: string
    description: string
    category: string
  }> {
    return await backendApiClient.saveWorkflowAsTemplate(workflowId, templateData)
  }

  /**
   * Get pending approvals
   */
  async getPendingApprovals(): Promise<Array<{
    id: string
    workflowId: string
    executionId: string
    stepId: string
    title: string
    description: string
    requestedBy: string
    requestedAt: string
    data: any
  }>> {
    return await backendApiClient.request('/workflows/approvals/pending')
  }

  /**
   * Duplicate workflow
   */
  async duplicateWorkflow(workflowId: string): Promise<Workflow> {
    return await backendApiClient.request(`/workflows/${workflowId}/duplicate`, {
      method: 'POST'
    })
  }

  /**
   * Pause workflow execution
   */
  async pauseWorkflowExecution(executionId: string): Promise<void> {
    await backendApiClient.request(`/workflows/executions/${executionId}/pause`, {
      method: 'POST'
    })
  }

  /**
   * Resume workflow execution
   */
  async resumeWorkflowExecution(executionId: string): Promise<void> {
    await backendApiClient.request(`/workflows/executions/${executionId}/resume`, {
      method: 'POST'
    })
  }

  /**
   * Approve workflow step
   */
  async approveWorkflowStep(approvalId: string, data?: any): Promise<void> {
    await backendApiClient.request(`/workflows/approvals/${approvalId}/approve`, {
      method: 'POST',
      body: JSON.stringify({ data })
    })
  }

  /**
   * Reject workflow step
   */
  async rejectWorkflowStep(approvalId: string, reason?: string): Promise<void> {
    await backendApiClient.request(`/workflows/approvals/${approvalId}/reject`, {
      method: 'POST',
      body: JSON.stringify({ reason })
    })
  }

  /**
   * Bulk execute workflows
   */
  async bulkExecuteWorkflows(requests: Array<{
    workflowId: string
    input?: any
  }>): Promise<Array<{
    workflowId: string
    executionId: string
    status: string
  }>> {
    return await backendApiClient.request('/workflows/bulk-execute', {
      method: 'POST',
      body: JSON.stringify({ requests })
    })
  }

  /**
   * Bulk cancel executions
   */
  async bulkCancelExecutions(executionIds: string[]): Promise<void> {
    await backendApiClient.request('/workflows/executions/bulk-cancel', {
      method: 'POST',
      body: JSON.stringify({ executionIds })
    })
  }

  /**
   * Get execution logs
   */
  async getExecutionLogs(executionId: string): Promise<Array<{
    id: string
    timestamp: string
    level: 'info' | 'warn' | 'error'
    message: string
    data?: any
  }>> {
    return await backendApiClient.request(`/workflows/executions/${executionId}/logs`)
  }

  /**
   * Get execution metrics
   */
  async getExecutionMetrics(executionId: string): Promise<{
    duration: number
    stepsCompleted: number
    totalSteps: number
    memoryUsage: number
    cpuUsage: number
  }> {
    return await backendApiClient.request(`/workflows/executions/${executionId}/metrics`)
  }
}

// Export singleton instance
export const workflowService = new WorkflowService()

// Re-export types for convenience
export { WorkflowStatus } from '../types/backend'
