"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const panelVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm",
  {
    variants: {
      variant: {
        default: "",
        outline: "bg-background shadow-none",
        ghost: "border-none shadow-none bg-transparent",
        elevated: "border-none shadow-md",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        xl: "p-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface PanelProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof panelVariants> {
  asChild?: boolean
  header?: React.ReactNode
  footer?: React.ReactNode
  headerClassName?: string
  bodyClassName?: string
  footerClassName?: string
  collapsible?: boolean
  defaultCollapsed?: boolean
}

const Panel = React.forwardRef<HTMLDivElement, PanelProps>(
  ({ 
    className, 
    variant, 
    size, 
    header, 
    footer, 
    headerClassName, 
    bodyClassName, 
    footerClassName, 
    collapsible = false,
    defaultCollapsed = false,
    children, 
    ...props 
  }, ref) => {
    const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed)

    const toggleCollapse = () => {
      if (collapsible) {
        setIsCollapsed(!isCollapsed)
      }
    }

    return (
      <div
        ref={ref}
        className={cn(panelVariants({ variant, size, className }))}
        {...props}
      >
        {header && (
          <div 
            className={cn(
              "flex items-center justify-between border-b pb-4", 
              collapsible && "cursor-pointer",
              headerClassName
            )}
            onClick={toggleCollapse}
          >
            {header}
            {collapsible && (
              <button className="text-muted-foreground hover:text-foreground">
                {isCollapsed ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <polyline points="6 9 12 15 18 9"></polyline>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                    <polyline points="18 15 12 9 6 15"></polyline>
                  </svg>
                )}
              </button>
            )}
          </div>
        )}
        
        {!isCollapsed && (
          <div className={cn("pt-4", header && "pt-4", bodyClassName)}>
            {children}
          </div>
        )}
        
        {footer && !isCollapsed && (
          <div className={cn("border-t pt-4 mt-4", footerClassName)}>
            {footer}
          </div>
        )}
      </div>
    )
  }
)
Panel.displayName = "Panel"

export { Panel, panelVariants }
