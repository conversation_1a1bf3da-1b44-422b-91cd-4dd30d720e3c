/**
 * Template Store - Zustand Store for Template Management
 * Manages document templates, form templates, and workflow templates
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '@/services/backend-api-client'
import type { Template, TemplateCategory, TemplateSearchQuery, CreateTemplateRequest, UpdateTemplateRequest } from '@/types/template'
import type { ID } from '@/types'

interface TemplateState {
  // Core state
  templates: Template[]
  selectedTemplate: Template | null
  categories: TemplateCategory[]

  // Featured and popular templates
  featuredTemplates: Template[]
  popularTemplates: Template[]
  publicTemplates: Template[]

  // Template instances and analytics
  templateInstances: Record<ID, any[]>
  templateAnalytics: Record<ID, any>

  // UI state
  loading: boolean
  isLoading: boolean
  error: string | null

  // Filters and pagination
  filters: TemplateSearchQuery
  searchQuery: string

  // Cache management
  cache: {
    templates: Record<ID, Template>
    lastFetch: Record<string, number>
    ttl: number
  }

  // Cache
  lastUpdated: string | null
  _hydrated: boolean
}

interface TemplateActions {
  // Template CRUD
  fetchTemplates: (filters?: TemplateSearchQuery) => Promise<void>
  fetchTemplate: (templateId: ID) => Promise<Template>
  createTemplate: (data: CreateTemplateRequest) => Promise<Template>
  updateTemplate: (templateId: ID, data: UpdateTemplateRequest) => Promise<void>
  deleteTemplate: (templateId: ID) => Promise<void>

  // Template operations
  selectTemplate: (templateId: ID) => Promise<void>
  cloneTemplate: (templateId: ID, name: string) => Promise<Template>
  shareTemplate: (templateId: ID, shareData: any) => Promise<void>

  // Featured and popular templates
  fetchFeaturedTemplates: () => Promise<void>
  fetchPopularTemplates: (limit?: number) => Promise<void>
  fetchPublicTemplates: () => Promise<void>

  // Template instances
  fetchTemplateInstances: (templateId: ID) => Promise<any[]>
  createTemplateInstance: (templateId: ID, data: any) => Promise<any>

  // Template analytics
  fetchTemplateAnalytics: (templateId: ID) => Promise<any>

  // Template validation
  validateTemplateData: (templateId: ID, data: any) => Promise<boolean>

  // Template export/import
  exportTemplate: (templateId: ID, format?: string) => Promise<void>
  previewTemplate: (templateId: ID, data?: any) => Promise<any>

  // Template publishing
  toggleTemplatePublish: (templateId: ID, isPublic: boolean) => Promise<void>
  publishTemplate: (templateId: ID) => Promise<void>
  archiveTemplate: (templateId: ID) => Promise<void>

  // Categories
  fetchCategories: () => Promise<void>

  // Search
  searchTemplates: (query: string, filters?: TemplateSearchQuery) => Promise<void>

  // Filters
  setFilters: (filters: Partial<TemplateSearchQuery>) => void
  setSearchQuery: (query: string) => void
  clearFilters: () => void

  // Computed getters
  getTemplatesByCategory: (categoryId: ID) => Template[]
  getPublicTemplates: () => Template[]
  getFavoriteTemplates: () => Template[]
  getFeaturedTemplates: () => Template[]
  getPopularTemplates: () => Template[]

  // Utility
  clearError: () => void
  reset: () => void
}

export type TemplateStore = TemplateState & TemplateActions

const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

const initialState: TemplateState = {
  templates: [],
  selectedTemplate: null,
  categories: [],
  featuredTemplates: [],
  popularTemplates: [],
  publicTemplates: [],
  templateInstances: {},
  templateAnalytics: {},
  loading: false,
  isLoading: false,
  error: null,
  filters: {},
  searchQuery: '',
  cache: {
    templates: {},
    lastFetch: {},
    ttl: CACHE_TTL,
  },
  lastUpdated: null,
  _hydrated: false,
}

export const useTemplateStore = create<TemplateStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Template CRUD
      fetchTemplates: async (filters?: TemplateSearchQuery) => {
        set({ loading: true, isLoading: true, error: null })
        
        try {
          const response = await backendApiClient.request('/templates/list', {
            method: 'GET',
            params: { ...get().filters, ...filters }
          })
          
          set({
            templates: response.templates || [],
            lastUpdated: new Date().toISOString(),
            loading: false,
            isLoading: false
          })
        } catch (error: any) {
          console.error('Fetch templates error:', error)
          set({
            error: error.message || 'Failed to fetch templates',
            loading: false,
            isLoading: false
          })
        }
      },

      createTemplate: async (data: CreateTemplateRequest) => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request('/templates', {
            method: 'POST',
            body: JSON.stringify(data)
          })
          
          const newTemplate = response.template
          set(state => ({
            templates: [newTemplate, ...state.templates],
            selectedTemplate: newTemplate,
            loading: false
          }))
          
          return newTemplate
        } catch (error: any) {
          console.error('Create template error:', error)
          set({
            error: error.message || 'Failed to create template',
            loading: false
          })
          throw error
        }
      },

      updateTemplate: async (templateId: ID, data: UpdateTemplateRequest) => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/update`, {
            method: 'PUT',
            body: JSON.stringify(data)
          })
          
          const updatedTemplate = response.template
          set(state => ({
            templates: state.templates.map(t => 
              t.id === templateId ? updatedTemplate : t
            ),
            selectedTemplate: state.selectedTemplate?.id === templateId 
              ? updatedTemplate 
              : state.selectedTemplate,
            loading: false
          }))
        } catch (error: any) {
          console.error('Update template error:', error)
          set({
            error: error.message || 'Failed to update template',
            loading: false
          })
          throw error
        }
      },

      deleteTemplate: async (templateId: ID) => {
        set({ loading: true, error: null })
        
        try {
          await backendApiClient.request(`/templates/${templateId}/delete`, {
            method: 'DELETE'
          })
          
          set(state => ({
            templates: state.templates.filter(t => t.id !== templateId),
            selectedTemplate: state.selectedTemplate?.id === templateId 
              ? null 
              : state.selectedTemplate,
            loading: false
          }))
        } catch (error: any) {
          console.error('Delete template error:', error)
          set({
            error: error.message || 'Failed to delete template',
            loading: false
          })
          throw error
        }
      },

      fetchTemplate: async (templateId: ID) => {
        const { cache } = get()

        // Check cache first
        if (cache.templates[templateId]) {
          const cachedTemplate = cache.templates[templateId]
          set({ selectedTemplate: cachedTemplate })
          return cachedTemplate
        }

        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request(`/templates/${templateId}`)
          const template = response.template

          // Update cache
          const updatedCache = { ...cache }
          updatedCache.templates[templateId] = template

          set({
            selectedTemplate: template,
            cache: updatedCache,
            loading: false
          })

          return template
        } catch (error: any) {
          console.error('Fetch template error:', error)
          set({
            error: error.message || 'Failed to fetch template',
            loading: false
          })
          throw error
        }
      },

      selectTemplate: async (templateId: ID) => {
        await get().fetchTemplate(templateId)
      },

      cloneTemplate: async (templateId: ID, name: string) => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/clone`, {
            method: 'POST',
            body: JSON.stringify({ name })
          })
          
          const clonedTemplate = response.template
          set(state => ({
            templates: [clonedTemplate, ...state.templates],
            loading: false
          }))
          
          return clonedTemplate
        } catch (error: any) {
          console.error('Clone template error:', error)
          set({
            error: error.message || 'Failed to clone template',
            loading: false
          })
          throw error
        }
      },

      shareTemplate: async (templateId: ID, shareData: any) => {
        set({ loading: true, error: null })
        
        try {
          await backendApiClient.request(`/templates/${templateId}/share`, {
            method: 'POST',
            body: JSON.stringify(shareData)
          })
          
          set({ loading: false })
        } catch (error: any) {
          console.error('Share template error:', error)
          set({
            error: error.message || 'Failed to share template',
            loading: false
          })
          throw error
        }
      },

      fetchFeaturedTemplates: async () => {
        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request('/templates/featured')
          set({
            featuredTemplates: response.templates || [],
            loading: false
          })
        } catch (error: any) {
          console.error('Fetch featured templates error:', error)
          set({
            error: error.message || 'Failed to fetch featured templates',
            loading: false
          })
        }
      },

      fetchPopularTemplates: async (limit?: number) => {
        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request('/templates/popular', {
            params: { limit }
          })
          set({
            popularTemplates: response.templates || [],
            loading: false
          })
        } catch (error: any) {
          console.error('Fetch popular templates error:', error)
          set({
            error: error.message || 'Failed to fetch popular templates',
            loading: false
          })
        }
      },

      fetchPublicTemplates: async () => {
        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request('/templates/public')
          set({
            publicTemplates: response.templates || [],
            loading: false
          })
        } catch (error: any) {
          console.error('Fetch public templates error:', error)
          set({
            error: error.message || 'Failed to fetch public templates',
            loading: false
          })
        }
      },

      fetchTemplateInstances: async (templateId: ID) => {
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/instances`)
          const instances = response.instances || []

          set(state => ({
            templateInstances: {
              ...state.templateInstances,
              [templateId]: instances
            }
          }))

          return instances
        } catch (error: any) {
          console.error('Fetch template instances error:', error)
          set({ error: error.message || 'Failed to fetch template instances' })
          throw error
        }
      },

      createTemplateInstance: async (templateId: ID, data: any) => {
        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request(`/templates/${templateId}/instances`, {
            method: 'POST',
            body: JSON.stringify(data)
          })

          const instance = response.instance

          // Update instances cache
          set(state => {
            const currentInstances = state.templateInstances[templateId] || []
            return {
              templateInstances: {
                ...state.templateInstances,
                [templateId]: [instance, ...currentInstances]
              },
              loading: false
            }
          })

          return instance
        } catch (error: any) {
          console.error('Create template instance error:', error)
          set({
            error: error.message || 'Failed to create template instance',
            loading: false
          })
          throw error
        }
      },

      fetchTemplateAnalytics: async (templateId: ID) => {
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/analytics`)
          const analytics = response.analytics

          set(state => ({
            templateAnalytics: {
              ...state.templateAnalytics,
              [templateId]: analytics
            }
          }))

          return analytics
        } catch (error: any) {
          console.error('Fetch template analytics error:', error)
          set({ error: error.message || 'Failed to fetch template analytics' })
          throw error
        }
      },

      validateTemplateData: async (templateId: ID, data: any) => {
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/validate`, {
            method: 'POST',
            body: JSON.stringify(data)
          })

          return response.isValid || false
        } catch (error: any) {
          console.error('Validate template data error:', error)
          set({ error: error.message || 'Failed to validate template data' })
          return false
        }
      },

      exportTemplate: async (templateId: ID, format = 'json') => {
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/export`, {
            params: { format }
          })

          // Trigger download
          const blob = new Blob([JSON.stringify(response.data)], { type: 'application/json' })
          const url = URL.createObjectURL(blob)
          const a = document.createElement('a')
          a.href = url
          a.download = `template-${templateId}.${format}`
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          URL.revokeObjectURL(url)
        } catch (error: any) {
          console.error('Export template error:', error)
          set({ error: error.message || 'Failed to export template' })
          throw error
        }
      },

      previewTemplate: async (templateId: ID, data?: any) => {
        try {
          const response = await backendApiClient.request(`/templates/${templateId}/preview`, {
            method: 'POST',
            body: JSON.stringify({ data })
          })

          return response.preview
        } catch (error: any) {
          console.error('Preview template error:', error)
          set({ error: error.message || 'Failed to preview template' })
          throw error
        }
      },

      toggleTemplatePublish: async (templateId: ID, isPublic: boolean) => {
        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request(`/templates/${templateId}/publish`, {
            method: 'PUT',
            body: JSON.stringify({ isPublic })
          })

          const updatedTemplate = response.template

          set(state => ({
            templates: state.templates.map(t =>
              t.id === templateId ? updatedTemplate : t
            ),
            selectedTemplate: state.selectedTemplate?.id === templateId
              ? updatedTemplate
              : state.selectedTemplate,
            loading: false
          }))
        } catch (error: any) {
          console.error('Toggle template publish error:', error)
          set({
            error: error.message || 'Failed to update template visibility',
            loading: false
          })
          throw error
        }
      },

      publishTemplate: async (templateId: ID) => {
        set({ loading: true, isLoading: true, error: null })

        try {
          const response = await backendApiClient.request(`/templates/${templateId}/publish`, {
            method: 'POST'
          })

          const updatedTemplate = response.template

          set(state => ({
            templates: state.templates.map(t =>
              t.id === templateId ? updatedTemplate : t
            ),
            selectedTemplate: state.selectedTemplate?.id === templateId
              ? updatedTemplate
              : state.selectedTemplate,
            loading: false,
            isLoading: false
          }))
        } catch (error: any) {
          console.error('Publish template error:', error)
          set({
            error: error.message || 'Failed to publish template',
            loading: false,
            isLoading: false
          })
          throw error
        }
      },

      archiveTemplate: async (templateId: ID) => {
        set({ loading: true, isLoading: true, error: null })

        try {
          const response = await backendApiClient.request(`/templates/${templateId}/archive`, {
            method: 'POST'
          })

          const updatedTemplate = response.template

          set(state => ({
            templates: state.templates.map(t =>
              t.id === templateId ? updatedTemplate : t
            ),
            selectedTemplate: state.selectedTemplate?.id === templateId
              ? updatedTemplate
              : state.selectedTemplate,
            loading: false,
            isLoading: false
          }))
        } catch (error: any) {
          console.error('Archive template error:', error)
          set({
            error: error.message || 'Failed to archive template',
            loading: false,
            isLoading: false
          })
          throw error
        }
      },

      searchTemplates: async (query: string, filters?: TemplateSearchQuery) => {
        set({ loading: true, error: null, searchQuery: query })

        try {
          const response = await backendApiClient.request('/templates/search', {
            method: 'POST',
            body: JSON.stringify({ query, filters })
          })

          set({
            templates: response.templates || [],
            loading: false
          })
        } catch (error: any) {
          console.error('Search templates error:', error)
          set({
            error: error.message || 'Failed to search templates',
            loading: false
          })
        }
      },

      fetchCategories: async () => {
        try {
          const response = await backendApiClient.request('/templates/categories')
          set({ categories: response.categories || [] })
        } catch (error: any) {
          console.error('Fetch categories error:', error)
          set({ error: error.message || 'Failed to fetch categories' })
        }
      },

      // Filters
      setFilters: (filters: Partial<TemplateSearchQuery>) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },

      clearFilters: () => {
        set({ filters: {}, searchQuery: '' })
      },

      // Computed getters
      getTemplatesByCategory: (categoryId: ID) => {
        return get().templates.filter(t => t.category?.id === categoryId)
      },

      getPublicTemplates: () => {
        return get().publicTemplates.length > 0 ? get().publicTemplates : get().templates.filter(t => t.isPublic)
      },

      getFavoriteTemplates: () => {
        return get().templates.filter(t => t.tags?.includes('favorite'))
      },

      getFeaturedTemplates: () => {
        return get().featuredTemplates
      },

      getPopularTemplates: () => {
        return get().popularTemplates
      },

      // Utility
      clearError: () => {
        set({ error: null })
      },

      reset: () => {
        set(initialState)
      },
    }),
    {
      name: 'template-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        selectedTemplate: state.selectedTemplate,
        filters: state.filters,
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
        }
      },
    }
  )
)

// Selector hooks for better performance
export const useTemplates = () => useTemplateStore(state => state.templates)
export const useSelectedTemplate = () => useTemplateStore(state => state.selectedTemplate)
export const useTemplateCategories = () => useTemplateStore(state => state.categories)
export const useTemplateLoading = () => useTemplateStore(state => state.loading)
export const useTemplateError = () => useTemplateStore(state => state.error)

// Additional selector hooks for new functionality
export const useFeaturedTemplates = () => useTemplateStore(state => state.featuredTemplates)
export const usePopularTemplates = () => useTemplateStore(state => state.popularTemplates)
export const usePublicTemplates = () => useTemplateStore(state => state.publicTemplates)
export const useTemplateInstances = (templateId: ID) => useTemplateStore(state => state.templateInstances[templateId] || [])
export const useTemplateAnalytics = (templateId: ID) => useTemplateStore(state => state.templateAnalytics[templateId])

// Action hooks
export const useFetchTemplates = () => useTemplateStore(state => state.fetchTemplates)
export const useFetchTemplate = () => useTemplateStore(state => state.fetchTemplate)
export const useCreateTemplate = () => useTemplateStore(state => state.createTemplate)
export const useUpdateTemplate = () => useTemplateStore(state => state.updateTemplate)
export const useDeleteTemplate = () => useTemplateStore(state => state.deleteTemplate)
export const useCloneTemplate = () => useTemplateStore(state => state.cloneTemplate)
export const useShareTemplate = () => useTemplateStore(state => state.shareTemplate)
export const useFetchFeaturedTemplates = () => useTemplateStore(state => state.fetchFeaturedTemplates)
export const useFetchPopularTemplates = () => useTemplateStore(state => state.fetchPopularTemplates)
export const useFetchPublicTemplates = () => useTemplateStore(state => state.fetchPublicTemplates)
export const useFetchTemplateInstances = () => useTemplateStore(state => state.fetchTemplateInstances)
export const useCreateTemplateInstance = () => useTemplateStore(state => state.createTemplateInstance)
export const useFetchTemplateAnalytics = () => useTemplateStore(state => state.fetchTemplateAnalytics)
export const useValidateTemplateData = () => useTemplateStore(state => state.validateTemplateData)
export const useExportTemplate = () => useTemplateStore(state => state.exportTemplate)
export const usePreviewTemplate = () => useTemplateStore(state => state.previewTemplate)
export const useToggleTemplatePublish = () => useTemplateStore(state => state.toggleTemplatePublish)
export const usePublishTemplate = () => useTemplateStore(state => state.publishTemplate)
export const useArchiveTemplate = () => useTemplateStore(state => state.archiveTemplate)
export const useSearchTemplates = () => useTemplateStore(state => state.searchTemplates)
