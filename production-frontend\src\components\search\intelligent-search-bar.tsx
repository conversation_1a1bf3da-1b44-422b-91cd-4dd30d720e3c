"use client";

import { useState, useEffect, useRef } from "react";
import { useRAGQuery } from "@/hooks/ai/useRAG";
import { useToast } from "@/hooks/use-toast";
import { useCurrentOrganization } from "@/stores/organization-store";

// Real intelligent search implementation using Zustand RAG
const useIntelligentSearch = () => {
  const { toast } = useToast();
  const currentOrganization = useCurrentOrganization();
  const ragQuery = useRAGQuery();

  const search = async (query: string, options?: any) => {
    if (!currentOrganization?.id) {
      throw new Error('No organization selected');
    }

    try {
      const response = await ragQuery.mutateAsync({
        query,
        organizationId: currentOrganization.id,
        projectId: options?.projectId,
        filters: options?.filters,
        options: {
          maxResults: options?.maxResults || 10,
          includeReasoning: true,
          useAdvancedAI: true,
          temperature: 0.3
        }
      });

      return {
        results: response?.sources?.map((source: any) => ({
          id: source.documentId || source.id,
          title: source.documentName || source.title,
          content: source.content,
          type: 'document',
          relevanceScore: source.relevanceScore || source.relevance,
          highlights: { content: [source.content] }
        })) || [],
        contextualResponse: response?.answer || '',
        suggestedQueries: [] // TODO: Add suggested queries from backend
      };
    } catch (error: any) {
      toast({
        title: 'Search Failed',
        description: error.message || 'Failed to perform search',
        variant: 'destructive',
      });
      throw error;
    }
  };

  return {
    search,
    isSearching: ragQuery.isLoading,
    error: ragQuery.error,
    results: {
      results: [] as Array<{
        id: string;
        title: string;
        content: string;
        type: string;
        relevanceScore: number;
        highlights: Record<string, string[]>;
      }>,
      contextualResponse: '',
      suggestedQueries: [] as string[]
    }
  };
};
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Loader2, FileText, MessageSquare, X, Sparkles } from "lucide-react";

interface IntelligentSearchBarProps {
  initialQuery?: string;
  placeholder?: string;
  organizationId?: string;
  projectId?: string;
  onResultSelect?: (result: any) => void;
  className?: string;
}

export function IntelligentSearchBar({
  initialQuery = "",
  placeholder = "Search documents, forms, and knowledge...",
  organizationId,
  projectId,
  onResultSelect,
  className = "",
}: IntelligentSearchBarProps) {
  const [query, setQuery] = useState(initialQuery);
  const [showResults, setShowResults] = useState(false);
  const [activeTab, setActiveTab] = useState("results");
  const searchBarRef = useRef<HTMLDivElement>(null);
  
  const {
    search,
    isSearching,
    error,
    results,
  } = useIntelligentSearch();

  // Handle search
  const handleSearch = async () => {
    if (!query.trim()) return;
    
    try {
      await search(query, {
        type: "HYBRID",
        includeContext: true,
        organizationId,
        projectId,
      });
      
      setShowResults(true);
    } catch (error) {
      console.error("Search failed", error);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle result selection
  const handleResultSelect = (result: any) => {
    if (onResultSelect) {
      onResultSelect(result);
    }
    setShowResults(false);
  };

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchBarRef.current && !searchBarRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Perform initial search if initialQuery is provided
  useEffect(() => {
    if (initialQuery) {
      handleSearch();
    }
  }, []);

  return (
    <div ref={searchBarRef} className={`relative ${className}`}>
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder={placeholder}
            className="pr-10"
          />
          {query && (
            <button
              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
              onClick={() => setQuery("")}
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        <Button onClick={handleSearch} disabled={!query.trim() || isSearching}>
          {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
          <span className="ml-2 hidden sm:inline">Search</span>
        </Button>
      </div>

      {showResults && (
        <Card className="absolute z-50 mt-2 w-full shadow-lg">
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <div className="flex items-center justify-between border-b px-4 py-2">
                <TabsList className="grid w-[200px] grid-cols-2">
                  <TabsTrigger value="results">Results</TabsTrigger>
                  <TabsTrigger value="insights">Insights</TabsTrigger>
                </TabsList>
                <Button variant="ghost" size="icon" onClick={() => setShowResults(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <TabsContent value="results" className="p-0">
                {isSearching ? (
                  <div className="p-4 space-y-4">
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-4 w-4 rounded-full" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                    <Skeleton className="h-[100px] w-full" />
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-4 w-4 rounded-full" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                    <Skeleton className="h-[100px] w-full" />
                  </div>
                ) : error ? (
                  <div className="p-4 text-center text-destructive">
                    <p>Error performing search. Please try again.</p>
                  </div>
                ) : results && results.results.length > 0 ? (
                  <ScrollArea className="h-[400px]">
                    <div className="p-4 space-y-4">
                      {results.results.map((result) => (
                        <div
                          key={result.id}
                          className="border rounded-md p-3 hover:bg-muted/50 cursor-pointer transition-colors"
                          onClick={() => handleResultSelect(result)}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center">
                              {result.type === "document" ? (
                                <FileText className="h-4 w-4 mr-2 text-blue-500" />
                              ) : (
                                <MessageSquare className="h-4 w-4 mr-2 text-green-500" />
                              )}
                              <h4 className="font-medium text-sm">{result.title}</h4>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {Math.round(result.relevanceScore * 100)}%
                            </Badge>
                          </div>
                          {result.content && (
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {result.content}
                            </p>
                          )}
                          {result.highlights && Object.keys(result.highlights).length > 0 && (
                            <div className="mt-2">
                              {Object.entries(result.highlights).map(([field, highlights], i) => (
                                <div key={i} className="text-xs text-muted-foreground mt-1">
                                  <span className="font-medium">{field}:</span>{" "}
                                  <span dangerouslySetInnerHTML={{ __html: highlights[0] }} />
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="p-4 text-center text-muted-foreground">
                    <p>No results found. Try a different search term.</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="insights" className="p-0">
                {isSearching ? (
                  <div className="p-4">
                    <Skeleton className="h-[200px] w-full" />
                  </div>
                ) : results && results.contextualResponse ? (
                  <div className="p-4">
                    <div className="flex items-center mb-3">
                      <Sparkles className="h-4 w-4 mr-2 text-purple-500" />
                      <h4 className="font-medium text-sm">AI-Generated Insights</h4>
                    </div>
                    <div className="text-sm space-y-2">
                      <p>{results.contextualResponse}</p>
                    </div>
                    
                    {results.suggestedQueries && results.suggestedQueries.length > 0 && (
                      <div className="mt-4">
                        <h5 className="text-xs font-medium mb-2">Suggested Searches:</h5>
                        <div className="flex flex-wrap gap-2">
                          {results.suggestedQueries.map((suggestion, i) => (
                            <Badge
                              key={i}
                              variant="outline"
                              className="cursor-pointer hover:bg-muted"
                              onClick={() => {
                                setQuery(suggestion);
                                handleSearch();
                              }}
                            >
                              {suggestion}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="p-4 text-center text-muted-foreground">
                    <p>No insights available. Try a search first.</p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
