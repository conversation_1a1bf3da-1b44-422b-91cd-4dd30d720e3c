/**
 * CORS middleware for Azure Functions
 * Handles Cross-Origin Resource Sharing headers and preflight requests
 */

import { HttpRequest, HttpResponseInit } from '@azure/functions';

export interface CorsOptions {
  allowedOrigins?: string[];
  allowedMethods?: string[];
  allowedHeaders?: string[];
  allowCredentials?: boolean;
  maxAge?: number;
}

const defaultCorsOptions: CorsOptions = {
  allowedOrigins: [
    'http://localhost:3000',
    'https://hepz.tech',
    'http://127.0.0.1:3000',
    'https://app.hepz.tech',
    'https://portal.azure.com',
    'https://hepz.tech',
    'https://app.hepz.tech',
    'https://www.hepz.tech'
  ],
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD', 'CONNECT'],
  allowedHeaders: [
    // Standard CORS headers
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Accept-Language',
    'Accept-Encoding',
    'Origin',
    'Referer',
    'User-Agent',
    'Host',

    // CORS preflight headers
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',

    // Custom application headers
    'X-Tenant-ID',
    'X-User-ID',
    'X-Organization-ID',
    'X-Project-ID',
    'X-Document-ID',
    'X-Session-ID',
    'X-Correlation-ID',
    'x-correlation-id',
    'X-Request-Time',
    'x-request-time',
    'X-Request-ID',
    'x-request-id',
    'X-Client-Version',
    'X-API-Version',

    // Azure Functions headers
    'X-Azure-Functions-Key',
    'x-azure-functions-key',
    'X-Functions-Key',
    'x-functions-key',
    'X-Functions-ClientId',
    'x-functions-clientid',

    // SignalR headers
    'x-signalr-user-agent',
    'X-SignalR-User-Agent',
    'X-SignalR-ConnectionId',
    'x-signalr-connectionid',
    'X-SignalR-ConnectionToken',
    'x-signalr-connectiontoken',

    // WebSocket headers
    'Connection',
    'Upgrade',
    'Sec-WebSocket-Key',
    'Sec-WebSocket-Version',
    'Sec-WebSocket-Protocol',
    'Sec-WebSocket-Extensions',
    'Sec-WebSocket-Accept',

    // Caching headers
    'Cache-Control',
    'Pragma',
    'Expires',
    'If-Modified-Since',
    'If-None-Match',
    'If-Match',
    'ETag',
    'Last-Modified',

    // Network headers
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Forwarded-Host',
    'X-Real-IP',
    'X-Original-URL',

    // Content headers
    'Content-Length',
    'Content-Range',
    'Content-Disposition',
    'Content-Encoding',
    'Transfer-Encoding',

    // Authentication headers
    'WWW-Authenticate',
    'Proxy-Authorization',
    'Proxy-Authenticate',

    // Azure AD B2C headers
    'X-MS-Request-ID',
    'x-ms-request-id',
    'X-MS-Client-Request-ID',
    'x-ms-client-request-id',

    // Event Grid headers
    'aeg-event-type',
    'aeg-subscription-name',
    'aeg-delivery-count',
    'aeg-data-version',

    // Service Bus headers
    'BrokerProperties',
    'x-ms-retrypolicy-type',
    'x-ms-retrypolicy-delay',

    // File upload headers
    'X-File-Name',
    'X-File-Size',
    'X-File-Type',
    'X-Upload-Content-Length',
    'X-Upload-Content-Type'
  ],
  allowCredentials: true,
  maxAge: 86400 // 24 hours
};

/**
 * Get allowed origins from environment and defaults
 */
function getAllowedOrigins(): string[] {
  const envOrigins = process.env.CORS_ALLOWED_ORIGINS?.split(',').map(o => o.trim()) || [];
  const defaultOrigins = [
    // Development origins
    'http://localhost:3000',
    'https://localhost:3000',
    'http://127.0.0.1:3000',
    'https://127.0.0.1:3000',
    'http://localhost:7071',
    'https://localhost:7071',

    // Production origins
    'https://hepz.tech',
    'https://app.hepz.tech',
    'https://www.hepz.tech',

    // Azure services
    'https://portal.azure.com',
    'https://hepzlogic.azurewebsites.net',
    'https://hepzeg.eastus-1.eventgrid.azure.net',
    'https://hepztech.service.signalr.net',

    // Azure AD B2C
    'https://login.microsoftonline.com',
    'https://hepzdocs.b2clogin.com',
    'https://graph.microsoft.com',

    // WebSocket origins (for SignalR)
    'wss://hepzlogic.azurewebsites.net',
    'ws://localhost:7071',
    'wss://localhost:7071'
  ];

  return [...new Set([...envOrigins, ...defaultOrigins])];
}

/**
 * Add CORS headers to response
 */
export function addCorsHeaders(
  response: HttpResponseInit,
  request: HttpRequest,
  options: CorsOptions = {}
): HttpResponseInit {
  const corsOptions = { ...defaultCorsOptions, ...options };
  const origin = request.headers.get('origin') || '';

  // Get allowed origins from environment and defaults
  const allowedOrigins = getAllowedOrigins();

  // Determine allowed origin
  let allowedOrigin = 'null';

  if (origin && allowedOrigins.includes(origin)) {
    allowedOrigin = origin;
  } else if (corsOptions.allowedOrigins?.includes('*')) {
    allowedOrigin = origin || '*';
  } else if (corsOptions.allowedOrigins?.includes(origin)) {
    allowedOrigin = origin;
  } else if (allowedOrigins.length > 0) {
    // For development, be more permissive with localhost
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      allowedOrigin = origin;
    }
  }

  const corsHeaders = {
    'Access-Control-Allow-Origin': allowedOrigin,
    'Access-Control-Allow-Methods': corsOptions.allowedMethods?.join(', ') || '',
    'Access-Control-Allow-Headers': corsOptions.allowedHeaders?.join(', ') || '',
    'Access-Control-Max-Age': corsOptions.maxAge?.toString() || '86400',
    'Access-Control-Expose-Headers': 'X-Correlation-ID, X-Request-Time, X-Total-Count',
    ...(corsOptions.allowCredentials && { 'Access-Control-Allow-Credentials': 'true' }),
    'Vary': 'Origin'
  };

  return {
    ...response,
    headers: {
      ...response.headers,
      ...corsHeaders
    }
  };
}

/**
 * Handle preflight OPTIONS request
 */
export function handlePreflight(
  request: HttpRequest,
  options: CorsOptions = {}
): HttpResponseInit | null {
  if (request.method === 'OPTIONS') {
    const response = addCorsHeaders({
      status: 200,
      headers: {
        'Content-Length': '0',
        'Cache-Control': 'max-age=86400'
      }
    }, request, options);

    // Log preflight requests for debugging
    const origin = request.headers.get('origin');
    const requestedMethod = request.headers.get('access-control-request-method');
    const requestedHeaders = request.headers.get('access-control-request-headers');

    console.log('CORS Preflight:', {
      origin,
      requestedMethod,
      requestedHeaders,
      url: request.url
    });

    return response;
  }
  return null;
}

/**
 * CORS middleware wrapper for Azure Functions
 */
export function withCors(
  handler: (request: HttpRequest, context: any) => Promise<HttpResponseInit>,
  options: CorsOptions = {}
) {
  return async (request: HttpRequest, context: any): Promise<HttpResponseInit> => {
    // Handle preflight request
    const preflightResponse = handlePreflight(request, options);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      // Execute the handler
      const response = await handler(request, context);

      // Add CORS headers to response
      return addCorsHeaders(response, request, options);
    } catch (error) {
      // Ensure CORS headers are added even for error responses
      const errorResponse = {
        status: 500,
        jsonBody: { error: 'Internal server error' }
      };

      return addCorsHeaders(errorResponse, request, options);
    }
  };
}

/**
 * Production-ready CORS options for all endpoints
 */
export const productionCorsOptions: CorsOptions = {
  allowedOrigins: getAllowedOrigins(),
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'X-Tenant-ID',
    'X-User-ID',
    'X-Correlation-ID',
    'x-correlation-id',
    'X-Request-Time',
    'x-request-time',
    'x-signalr-user-agent',
    'X-SignalR-User-Agent',
    'X-Azure-Functions-Key',
    'x-azure-functions-key',
    'X-Functions-Key',
    'x-functions-key',
    'Cache-Control',
    'Pragma',
    'Expires',
    'If-Modified-Since',
    'If-None-Match',
    'X-Forwarded-For',
    'X-Real-IP',
    'User-Agent',
    'Referer',
    'Connection',
    'Upgrade',
    'Sec-WebSocket-Key',
    'Sec-WebSocket-Version',
    'Sec-WebSocket-Protocol',
    'Sec-WebSocket-Extensions'
  ],
  allowCredentials: true,
  maxAge: 86400
};
