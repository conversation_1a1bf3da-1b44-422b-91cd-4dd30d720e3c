"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const listVariants = cva(
  "w-full",
  {
    variants: {
      variant: {
        default: "space-y-1",
        separated: "divide-y",
        bordered: "border rounded-md overflow-hidden divide-y",
        card: "border rounded-md overflow-hidden divide-y bg-card",
      },
      size: {
        default: "",
        sm: "text-sm",
        lg: "text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ListProps
  extends React.HTMLAttributes<HTMLUListElement>,
    VariantProps<typeof listVariants> {
  items?: React.ReactNode[]
  renderItem?: (item: any, index: number) => React.ReactNode
  data?: any[]
  isLoading?: boolean
  isError?: boolean
  loadingComponent?: React.ReactNode
  errorComponent?: React.ReactNode
  emptyComponent?: React.ReactNode
}

const List = React.forwardRef<HTMLUListElement, ListProps>(
  ({ 
    className, 
    variant, 
    size, 
    items, 
    renderItem, 
    data, 
    isLoading, 
    isError, 
    loadingComponent, 
    errorComponent, 
    emptyComponent, 
    children, 
    ...props 
  }, ref) => {
    // Determine what to render
    const content = React.useMemo(() => {
      if (isLoading) {
        return loadingComponent || (
          <li className="p-4 text-center text-muted-foreground">Loading...</li>
        )
      }
      
      if (isError) {
        return errorComponent || (
          <li className="p-4 text-center text-destructive">Error loading data</li>
        )
      }
      
      if (items && items.length > 0) {
        return items
      }
      
      if (data && data.length > 0 && renderItem) {
        return data.map((item, index) => (
          <React.Fragment key={index}>
            {renderItem(item, index)}
          </React.Fragment>
        ))
      }
      
      if (children) {
        return children
      }
      
      return emptyComponent || (
        <li className="p-4 text-center text-muted-foreground">No items</li>
      )
    }, [isLoading, isError, items, data, renderItem, children, loadingComponent, errorComponent, emptyComponent])
    
    return (
      <ul
        ref={ref}
        className={cn(listVariants({ variant, size, className }))}
        {...props}
      >
        {content}
      </ul>
    )
  }
)
List.displayName = "List"

export interface ListItemProps extends React.HTMLAttributes<HTMLLIElement> {
  active?: boolean
  disabled?: boolean
  selected?: boolean
  startContent?: React.ReactNode
  endContent?: React.ReactNode
}

const ListItem = React.forwardRef<HTMLLIElement, ListItemProps>(
  ({ 
    className, 
    active, 
    disabled, 
    selected, 
    startContent, 
    endContent, 
    children, 
    ...props 
  }, ref) => {
    return (
      <li
        ref={ref}
        className={cn(
          "relative flex items-center px-4 py-2",
          active && "bg-accent text-accent-foreground",
          selected && "bg-primary/10 text-primary",
          disabled && "opacity-50 cursor-not-allowed",
          !disabled && "cursor-pointer hover:bg-accent/50",
          className
        )}
        {...props}
      >
        {startContent && (
          <div className="mr-3 flex-shrink-0">
            {startContent}
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          {children}
        </div>
        
        {endContent && (
          <div className="ml-3 flex-shrink-0">
            {endContent}
          </div>
        )}
      </li>
    )
  }
)
ListItem.displayName = "ListItem"

export { List, ListItem, listVariants }
