/**
 * Teams Hooks
 * React hooks for team management
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface Team {
  id: string
  name: string
  description?: string
  organizationId: string
  members: TeamMember[]
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface TeamMember {
  id: string
  teamId: string
  userId: string
  user: {
    id: string
    firstName: string
    lastName: string
    email: string
    avatar?: string
  }
  role: string
  joinedAt: string
  addedBy: string
}

export interface CreateTeamRequest {
  name: string
  description?: string
  organizationId: string
  memberIds?: string[]
}

export interface UpdateTeamRequest {
  name?: string
  description?: string
}

export interface AddTeamMemberRequest {
  userId: string
  role?: string
}

export interface UpdateTeamMemberRequest {
  role: string
}

/**
 * Hook to get all teams for an organization
 */
export function useTeams(organizationId: string) {
  return useQuery({
    queryKey: ['teams', organizationId],
    queryFn: async () => {
      return await backendApiClient.request<Team[]>(`/organizations/${organizationId}/teams`)
    },
    enabled: !!organizationId,
  })
}

/**
 * Hook to get a specific team
 */
export function useTeam(teamId: string) {
  return useQuery({
    queryKey: ['team', teamId],
    queryFn: async () => {
      return await backendApiClient.request<Team>(`/teams/${teamId}`)
    },
    enabled: !!teamId,
  })
}

/**
 * Hook to get team members
 */
export function useTeamMembers(teamId: string) {
  return useQuery({
    queryKey: ['team', teamId, 'members'],
    queryFn: async () => {
      return await backendApiClient.request<TeamMember[]>(`/teams/${teamId}/members/list`)
    },
    enabled: !!teamId,
  })
}

/**
 * Hook to create a new team
 */
export function useCreateTeam() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateTeamRequest) => {
      return await backendApiClient.request<Team>('/teams', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (team) => {
      queryClient.invalidateQueries({ queryKey: ['teams', team.organizationId] })
      toast({
        title: 'Team created',
        description: `Team "${team.name}" has been created successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error creating team',
        description: 'There was a problem creating the team. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update a team
 */
export function useUpdateTeam() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ teamId, data }: { teamId: string; data: UpdateTeamRequest }) => {
      return await backendApiClient.request<Team>(`/teams/${teamId}`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (team) => {
      queryClient.invalidateQueries({ queryKey: ['team', team.id] })
      queryClient.invalidateQueries({ queryKey: ['teams', team.organizationId] })
      toast({
        title: 'Team updated',
        description: `Team "${team.name}" has been updated successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error updating team',
        description: 'There was a problem updating the team. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a team
 */
export function useDeleteTeam() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (teamId: string) => {
      await backendApiClient.request(`/teams/${teamId}`, {
        method: 'DELETE'
      })
      return teamId
    },
    onSuccess: (teamId) => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
      queryClient.removeQueries({ queryKey: ['team', teamId] })
      toast({
        title: 'Team deleted',
        description: 'The team has been deleted successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error deleting team',
        description: 'There was a problem deleting the team. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to add a member to a team
 */
export function useAddTeamMember() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ teamId, data }: { teamId: string; data: AddTeamMemberRequest }) => {
      return await backendApiClient.request<TeamMember>(`/teams/${teamId}/members`, {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (member) => {
      queryClient.invalidateQueries({ queryKey: ['team', member.teamId] })
      queryClient.invalidateQueries({ queryKey: ['team', member.teamId, 'members'] })
      toast({
        title: 'Member added',
        description: 'The member has been added to the team successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error adding member',
        description: 'There was a problem adding the member to the team. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update a team member
 */
export function useUpdateTeamMember() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      teamId, 
      memberId, 
      data 
    }: { 
      teamId: string
      memberId: string
      data: UpdateTeamMemberRequest 
    }) => {
      return await backendApiClient.request<TeamMember>(`/teams/${teamId}/members/${memberId}/update`, {
        method: 'PUT',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (member) => {
      queryClient.invalidateQueries({ queryKey: ['team', member.teamId] })
      queryClient.invalidateQueries({ queryKey: ['team', member.teamId, 'members'] })
      toast({
        title: 'Member updated',
        description: 'The team member has been updated successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error updating member',
        description: 'There was a problem updating the team member. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to remove a member from a team
 */
export function useRemoveTeamMember() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ teamId, memberId }: { teamId: string; memberId: string }) => {
      await backendApiClient.request(`/teams/${teamId}/members/${memberId}`, {
        method: 'DELETE'
      })
      return { teamId, memberId }
    },
    onSuccess: ({ teamId }) => {
      queryClient.invalidateQueries({ queryKey: ['team', teamId] })
      queryClient.invalidateQueries({ queryKey: ['team', teamId, 'members'] })
      toast({
        title: 'Member removed',
        description: 'The member has been removed from the team successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error removing member',
        description: 'There was a problem removing the member from the team. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
