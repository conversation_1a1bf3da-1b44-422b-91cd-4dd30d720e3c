/**
 * DocuSign Hooks
 * React hooks for DocuSign integration
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'

export interface DocuSignEnvelope {
  id: string
  documentId: string
  envelopeId: string
  status: 'created' | 'sent' | 'delivered' | 'signed' | 'completed' | 'declined' | 'voided' | 'expired'
  subject: string
  message?: string
  recipients: DocuSignRecipient[]
  documents: DocuSignDocument[]
  createdAt: string
  sentAt?: string
  completedAt?: string
  voidedAt?: string
  organizationId: string
  createdBy: string
}

export interface DocuSignRecipient {
  id: string
  name: string
  email: string
  role: 'signer' | 'cc' | 'certifiedDelivery' | 'inPersonSigner'
  routingOrder: number
  status: 'created' | 'sent' | 'delivered' | 'signed' | 'declined'
  signedAt?: string
  deliveredAt?: string
  tabs?: DocuSignTab[]
}

export interface DocuSignDocument {
  id: string
  name: string
  documentId: string
  order: number
  pages: number
}

export interface DocuSignTab {
  id: string
  type: 'signHere' | 'dateSign' | 'fullName' | 'company' | 'title' | 'text' | 'checkbox'
  label?: string
  value?: string
  required: boolean
  position: {
    pageNumber: number
    x: number
    y: number
    width?: number
    height?: number
  }
}

export interface CreateEnvelopeRequest {
  documentId: string
  subject: string
  message?: string
  recipients: {
    name: string
    email: string
    role: 'signer' | 'cc' | 'certifiedDelivery'
    routingOrder: number
    tabs?: Omit<DocuSignTab, 'id'>[]
  }[]
  organizationId: string
  sendNow?: boolean
}

export interface SendEnvelopeRequest {
  envelopeId: string
  message?: string
}

/**
 * Hook to get DocuSign envelopes
 */
export function useDocuSignEnvelopes(params?: {
  documentId?: string
  organizationId?: string
  status?: string
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['docusign-envelopes', params],
    queryFn: async () => {
      return await backendApiClient.request<DocuSignEnvelope[]>('/integrations/docusign/envelopes', {
        params
      })
    },
  })
}

/**
 * Hook to get a specific DocuSign envelope
 */
export function useDocuSignEnvelope(envelopeId: string) {
  return useQuery({
    queryKey: ['docusign-envelope', envelopeId],
    queryFn: async () => {
      return await backendApiClient.request<DocuSignEnvelope>(`/integrations/docusign/envelopes/${envelopeId}`)
    },
    enabled: !!envelopeId,
  })
}

/**
 * Hook to create a DocuSign envelope
 */
export function useCreateDocuSignEnvelope() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateEnvelopeRequest) => {
      return await backendApiClient.request<DocuSignEnvelope>('/integrations/docusign/envelopes', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (envelope) => {
      queryClient.invalidateQueries({ queryKey: ['docusign-envelopes'] })
      queryClient.invalidateQueries({ queryKey: ['document', envelope.documentId] })
      toast({
        title: 'Envelope created',
        description: `DocuSign envelope "${envelope.subject}" has been created successfully.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error creating envelope',
        description: 'There was a problem creating the DocuSign envelope. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to send a DocuSign envelope
 */
export function useSendDocuSignEnvelope() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: SendEnvelopeRequest) => {
      return await backendApiClient.request<DocuSignEnvelope>(`/integrations/docusign/envelopes/${data.envelopeId}/send`, {
        method: 'POST',
        body: JSON.stringify({ message: data.message })
      })
    },
    onSuccess: (envelope) => {
      queryClient.invalidateQueries({ queryKey: ['docusign-envelope', envelope.id] })
      queryClient.invalidateQueries({ queryKey: ['docusign-envelopes'] })
      toast({
        title: 'Envelope sent',
        description: `DocuSign envelope "${envelope.subject}" has been sent to recipients.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error sending envelope',
        description: 'There was a problem sending the DocuSign envelope. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to void a DocuSign envelope
 */
export function useVoidDocuSignEnvelope() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ envelopeId, reason }: { envelopeId: string; reason: string }) => {
      return await backendApiClient.request<DocuSignEnvelope>(`/integrations/docusign/envelopes/${envelopeId}/void`, {
        method: 'POST',
        body: JSON.stringify({ reason })
      })
    },
    onSuccess: (envelope) => {
      queryClient.invalidateQueries({ queryKey: ['docusign-envelope', envelope.id] })
      queryClient.invalidateQueries({ queryKey: ['docusign-envelopes'] })
      toast({
        title: 'Envelope voided',
        description: `DocuSign envelope "${envelope.subject}" has been voided.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error voiding envelope',
        description: 'There was a problem voiding the DocuSign envelope. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get envelope status
 */
export function useDocuSignEnvelopeStatus(envelopeId: string) {
  return useQuery({
    queryKey: ['docusign-envelope-status', envelopeId],
    queryFn: async () => {
      return await backendApiClient.request(`/integrations/docusign/envelopes/${envelopeId}/status`)
    },
    enabled: !!envelopeId,
    refetchInterval: (query) => {
      // Refetch every 30 seconds if envelope is not completed
      const data = query.state.data
      return data?.status && !['completed', 'declined', 'voided'].includes(data.status) ? 30000 : false
    },
  })
}

/**
 * Hook to get signing URL
 */
export function useGetSigningUrl() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      envelopeId, 
      recipientId, 
      returnUrl 
    }: { 
      envelopeId: string
      recipientId: string
      returnUrl: string 
    }) => {
      return await backendApiClient.request(`/integrations/docusign/envelopes/${envelopeId}/signing-url`, {
        method: 'POST',
        body: JSON.stringify({ recipientId, returnUrl })
      })
    },
    onError: (error) => {
      toast({
        title: 'Error getting signing URL',
        description: 'There was a problem getting the signing URL. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to download completed envelope
 */
export function useDownloadDocuSignEnvelope() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (envelopeId: string) => {
      return await backendApiClient.request(`/integrations/docusign/envelopes/${envelopeId}/download`, {
        method: 'GET'
      })
    },
    onSuccess: (result) => {
      if (result.downloadUrl) {
        window.open(result.downloadUrl, '_blank')
      }
    },
    onError: (error) => {
      toast({
        title: 'Error downloading envelope',
        description: 'There was a problem downloading the envelope. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get DocuSign account info
 */
export function useDocuSignAccountInfo() {
  return useQuery({
    queryKey: ['docusign-account'],
    queryFn: async () => {
      return await backendApiClient.request('/integrations/docusign/account')
    },
  })
}

/**
 * Hook to check DocuSign connection status
 */
export function useDocuSignConnectionStatus() {
  return useQuery({
    queryKey: ['docusign-connection'],
    queryFn: async () => {
      return await backendApiClient.request('/integrations/docusign/connection/status')
    },
  })
}

/**
 * Hook to connect to DocuSign
 */
export function useConnectDocuSign() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (authCode: string) => {
      return await backendApiClient.request('/integrations/docusign/connect', {
        method: 'POST',
        body: JSON.stringify({ authCode })
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['docusign-connection'] })
      queryClient.invalidateQueries({ queryKey: ['docusign-account'] })
      toast({
        title: 'DocuSign connected',
        description: 'Your DocuSign account has been connected successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error connecting DocuSign',
        description: 'There was a problem connecting to DocuSign. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Main DocuSign hook that combines all functionality
 */
export function useDocuSign() {
  const envelopes = useDocuSignEnvelopes()
  const connectionStatus = useDocuSignConnectionStatus()
  const accountInfo = useDocuSignAccountInfo()
  const createEnvelope = useCreateDocuSignEnvelope()
  const sendEnvelope = useSendDocuSignEnvelope()
  const voidEnvelope = useVoidDocuSignEnvelope()
  const getSigningUrl = useGetSigningUrl()
  const downloadEnvelope = useDownloadDocuSignEnvelope()
  const connectDocuSign = useConnectDocuSign()

  return {
    envelopes: envelopes.data || [],
    isLoading: envelopes.isLoading,
    error: envelopes.error,
    connectionStatus: connectionStatus.data,
    accountInfo: accountInfo.data,
    createEnvelope: createEnvelope.mutate,
    sendEnvelope: sendEnvelope.mutate,
    sendForSigning: sendEnvelope.mutate,
    voidEnvelope: (envelopeId: string) => voidEnvelope.mutate({ envelopeId, reason: 'Voided by user' }),
    getSigningUrl: getSigningUrl.mutate,
    getEnvelopeStatus: (envelopeId: string) => {
      // This would typically refetch the envelope data
      envelopes.refetch()
    },
    downloadEnvelope: downloadEnvelope.mutate,
    downloadSignedDocument: downloadEnvelope.mutate,
    connectDocuSign: connectDocuSign.mutate,
    sendReminder: async (envelopeId: string) => {
      try {
        const response = await fetch(`/api/docusign/envelopes/${envelopeId}/reminder`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to send reminder: ${response.statusText}`);
        }

        console.log('Reminder sent successfully for envelope:', envelopeId);

        // Optionally refresh envelopes to get updated status
        envelopes.refetch();
      } catch (error) {
        console.error('Failed to send reminder:', error);
        throw error;
      }
    },
    fetchEnvelopes: () => envelopes.refetch(),
    isCreating: createEnvelope.isPending,
    isSending: sendEnvelope.isPending,
    isVoiding: voidEnvelope.isPending,
    isConnecting: connectDocuSign.isPending,
  }
}
