/**
 * Lazy Components Hook - Comprehensive Component Lazy Loading
 * Provides hooks for lazy loading heavy components only when needed
 * Optimizes bundle size and improves initial page load performance
 */

import { useState, useEffect, useCallback } from 'react'
import { useAuth } from '@/hooks/useAuth'

interface LazyComponentState {
  isLoaded: boolean
  isLoading: boolean
  error: Error | null
  component: React.ComponentType<any> | null
}

/**
 * Hook for lazy loading document viewer components
 * Only loads when document viewing is needed
 */
export function useLazyDocumentComponents(enabled: boolean = false) {
  const [advancedViewer, setAdvancedViewer] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const [editorJS, setEditorJS] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const [pdfEditor, setPdfEditor] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const loadAdvancedViewer = useCallback(async () => {
    if (advancedViewer.isLoaded || advancedViewer.isLoading) return

    setAdvancedViewer(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/documents/advanced-document-viewer')
      setAdvancedViewer({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.AdvancedDocumentViewer
      })
    } catch (error) {
      setAdvancedViewer({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [advancedViewer.isLoaded, advancedViewer.isLoading])

  const loadEditorJS = useCallback(async () => {
    if (editorJS.isLoaded || editorJS.isLoading) return

    setEditorJS(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/documents/editorjs-rich-text-editor')
      setEditorJS({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.EditorJSRichTextEditor
      })
    } catch (error) {
      setEditorJS({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [editorJS.isLoaded, editorJS.isLoading])

  const loadPdfEditor = useCallback(async () => {
    if (pdfEditor.isLoaded || pdfEditor.isLoading) return

    setPdfEditor(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/documents/interactive-pdf-editor')
      setPdfEditor({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.InteractivePDFEditor
      })
    } catch (error) {
      setPdfEditor({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [pdfEditor.isLoaded, pdfEditor.isLoading])

  useEffect(() => {
    if (enabled) {
      // Preload commonly used document components
      loadAdvancedViewer()
    }
  }, [enabled, loadAdvancedViewer])

  return {
    advancedViewer,
    editorJS,
    pdfEditor,
    loadAdvancedViewer,
    loadEditorJS,
    loadPdfEditor,
    isAnyLoading: advancedViewer.isLoading || editorJS.isLoading || pdfEditor.isLoading
  }
}

/**
 * Hook for lazy loading AI components
 * Only loads when AI features are used
 */
export function useLazyAIComponents(enabled: boolean = false) {
  const { user } = useAuth()
  const [aiAssistant, setAiAssistant] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const [modelConfig, setModelConfig] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const loadAIAssistant = useCallback(async () => {
    if (aiAssistant.isLoaded || aiAssistant.isLoading) return

    setAiAssistant(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/documents/ai-document-assistant')
      setAiAssistant({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.AIDocumentAssistant
      })
    } catch (error) {
      setAiAssistant({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [aiAssistant.isLoaded, aiAssistant.isLoading])

  const loadModelConfig = useCallback(async () => {
    if (modelConfig.isLoaded || modelConfig.isLoading) return

    setModelConfig(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/ai/AIModelConfigEditor')
      setModelConfig({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.AIModelConfigEditor
      })
    } catch (error) {
      setModelConfig({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [modelConfig.isLoaded, modelConfig.isLoading])

  useEffect(() => {
    if (enabled && user) {
      const userRoles = user.roles?.map(role => 
        typeof role === 'string' ? role : role.name || role.id
      ) || []
      
      if (userRoles.includes('admin') || userRoles.includes('user')) {
        loadAIAssistant()
      }
    }
  }, [enabled, user, loadAIAssistant])

  return {
    aiAssistant,
    modelConfig,
    loadAIAssistant,
    loadModelConfig,
    isAnyLoading: aiAssistant.isLoading || modelConfig.isLoading
  }
}

/**
 * Hook for lazy loading analytics components
 * Only loads when analytics are viewed
 */
export function useLazyAnalyticsComponents(enabled: boolean = false) {
  const { user } = useAuth()
  const [dashboard, setDashboard] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const [searchAnalytics, setSearchAnalytics] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const loadDashboard = useCallback(async () => {
    if (dashboard.isLoaded || dashboard.isLoading) return

    setDashboard(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/analytics/comprehensive-analytics-dashboard')
      setDashboard({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.ComprehensiveAnalyticsDashboard
      })
    } catch (error) {
      setDashboard({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [dashboard.isLoaded, dashboard.isLoading])

  const loadSearchAnalytics = useCallback(async () => {
    if (searchAnalytics.isLoaded || searchAnalytics.isLoading) return

    setSearchAnalytics(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/analytics/SearchAnalyticsPanel')
      setSearchAnalytics({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.SearchAnalyticsPanel
      })
    } catch (error) {
      setSearchAnalytics({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [searchAnalytics.isLoaded, searchAnalytics.isLoading])

  useEffect(() => {
    if (enabled && user) {
      const userRoles = user.roles?.map(role => 
        typeof role === 'string' ? role : role.name || role.id
      ) || []
      
      if (userRoles.includes('admin') || userRoles.includes('analyst')) {
        loadDashboard()
      }
    }
  }, [enabled, user, loadDashboard])

  return {
    dashboard,
    searchAnalytics,
    loadDashboard,
    loadSearchAnalytics,
    isAnyLoading: dashboard.isLoading || searchAnalytics.isLoading
  }
}

/**
 * Hook for lazy loading admin components
 * Only loads for admin users
 */
export function useLazyAdminComponents(enabled: boolean = false) {
  const { user } = useAuth()
  const [systemMonitoring, setSystemMonitoring] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const [eventGridMonitor, setEventGridMonitor] = useState<LazyComponentState>({
    isLoaded: false,
    isLoading: false,
    error: null,
    component: null
  })

  const loadSystemMonitoring = useCallback(async () => {
    if (systemMonitoring.isLoaded || systemMonitoring.isLoading) return

    setSystemMonitoring(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/admin/system-monitoring-dashboard')
      setSystemMonitoring({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.SystemMonitoringDashboard
      })
    } catch (error) {
      setSystemMonitoring({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [systemMonitoring.isLoaded, systemMonitoring.isLoading])

  const loadEventGridMonitor = useCallback(async () => {
    if (eventGridMonitor.isLoaded || eventGridMonitor.isLoading) return

    setEventGridMonitor(prev => ({ ...prev, isLoading: true }))
    try {
      const module = await import('@/components/admin/EventGridMonitor')
      setEventGridMonitor({
        isLoaded: true,
        isLoading: false,
        error: null,
        component: module.default
      })
    } catch (error) {
      setEventGridMonitor({
        isLoaded: false,
        isLoading: false,
        error: error instanceof Error ? error : new Error(String(error)),
        component: null
      })
    }
  }, [eventGridMonitor.isLoaded, eventGridMonitor.isLoading])

  useEffect(() => {
    if (enabled && user) {
      const userRoles = user.roles?.map(role => 
        typeof role === 'string' ? role : role.name || role.id
      ) || []
      
      if (userRoles.includes('admin')) {
        loadSystemMonitoring()
        loadEventGridMonitor()
      }
    }
  }, [enabled, user, loadSystemMonitoring, loadEventGridMonitor])

  return {
    systemMonitoring,
    eventGridMonitor,
    loadSystemMonitoring,
    loadEventGridMonitor,
    isAnyLoading: systemMonitoring.isLoading || eventGridMonitor.isLoading,
    hasAccess: user?.roles?.some(role => 
      (typeof role === 'string' ? role : role.name || role.id) === 'admin'
    ) || false
  }
}

/**
 * Master hook for all lazy components
 */
export function useLazyComponentManager() {
  const [activeComponents, setActiveComponents] = useState<Set<string>>(new Set())
  
  const activateComponent = useCallback((componentName: string) => {
    setActiveComponents(prev => new Set(prev).add(componentName))
  }, [])

  const deactivateComponent = useCallback((componentName: string) => {
    setActiveComponents(prev => {
      const newSet = new Set(prev)
      newSet.delete(componentName)
      return newSet
    })
  }, [])

  const isComponentActive = useCallback((componentName: string) => {
    return activeComponents.has(componentName)
  }, [activeComponents])

  return {
    activeComponents: Array.from(activeComponents),
    activateComponent,
    deactivateComponent,
    isComponentActive,
    componentCount: activeComponents.size
  }
}
