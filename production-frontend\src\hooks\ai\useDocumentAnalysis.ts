/**
 * Document Analysis Hooks
 * React hooks for AI-powered document analysis
 */

import { useCallback } from 'react'
import { useAIStore, useStartAIOperation, useAILoading, useAIError } from '@/stores/ai-store'
import { useToast } from '@/hooks/use-toast'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import backendApiClient from '@/services/backend-api-client'
export interface DocumentAnalysisResult {
  id: string
  documentId: string
  analysisType: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  results: {
    summary?: string
    entities?: Array<{
      type: string
      value: string
      confidence: number
      position: { start: number; end: number }
    }>
    sentiment?: {
      score: number
      label: 'positive' | 'negative' | 'neutral'
      confidence: number
    }
    topics?: Array<{
      name: string
      confidence: number
      keywords: string[]
    }>
    classification?: {
      category: string
      confidence: number
      subcategories: Array<{
        name: string
        confidence: number
      }>
    }
    keyPhrases?: Array<{
      phrase: string
      confidence: number
    }>
    language?: {
      code: string
      name: string
      confidence: number
    }
  }
  metadata: Record<string, any>
  createdAt: string
  completedAt?: string
  error?: string
}

export interface AnalyzeDocumentRequest {
  documentId: string
  analysisTypes: string[]
  options?: {
    extractEntities?: boolean
    analyzeSentiment?: boolean
    extractTopics?: boolean
    classifyDocument?: boolean
    extractKeyPhrases?: boolean
    detectLanguage?: boolean
    generateSummary?: boolean
    customPrompt?: string
  }
}

/**
 * Hook to analyze a document
 */
export function useAnalyzeDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: AnalyzeDocumentRequest) => {
      return await backendApiClient.request<DocumentAnalysisResult>('/ai/documents/analyze', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['document-analysis', result.documentId] })
      toast({
        title: 'Analysis started',
        description: 'Document analysis has been started. You will be notified when it\'s complete.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error starting analysis',
        description: 'There was a problem starting the document analysis. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get document analysis results
 */
export function useDocumentAnalysis(documentId: string) {
  return useQuery({
    queryKey: ['document-analysis', documentId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentAnalysisResult[]>(`/ai/documents/${documentId}/analysis`)
    },
    enabled: !!documentId,
  })
}

/**
 * Hook to get a specific analysis result
 */
export function useAnalysisResult(analysisId: string) {
  return useQuery({
    queryKey: ['analysis-result', analysisId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentAnalysisResult>(`/ai/analysis/${analysisId}`)
    },
    enabled: !!analysisId,
  })
}

/**
 * Hook to get analysis history
 */
export function useAnalysisHistory(params?: {
  documentId?: string
  analysisType?: string
  status?: string
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['analysis-history', params],
    queryFn: async () => {
      return await backendApiClient.request('/ai/analysis/history', {
        params
      })
    },
  })
}

/**
 * Hook to retry failed analysis
 */
export function useRetryAnalysis() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (analysisId: string) => {
      return await backendApiClient.request(`/ai/analysis/${analysisId}/retry`, {
        method: 'POST'
      })
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['analysis-result', result.id] })
      queryClient.invalidateQueries({ queryKey: ['document-analysis', result.documentId] })
      toast({
        title: 'Analysis restarted',
        description: 'The analysis has been restarted successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error retrying analysis',
        description: 'There was a problem retrying the analysis. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to cancel running analysis
 */
export function useCancelAnalysis() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (analysisId: string) => {
      return await backendApiClient.request(`/ai/analysis/${analysisId}/cancel`, {
        method: 'POST'
      })
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['analysis-result', result.id] })
      queryClient.invalidateQueries({ queryKey: ['document-analysis', result.documentId] })
      toast({
        title: 'Analysis cancelled',
        description: 'The analysis has been cancelled successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error cancelling analysis',
        description: 'There was a problem cancelling the analysis. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get available analysis types
 */
export function useAnalysisTypes() {
  return useQuery({
    queryKey: ['analysis-types'],
    queryFn: async () => {
      return await backendApiClient.request('/ai/analysis/types')
    },
  })
}

/**
 * Hook to get analysis templates
 */
export function useAnalysisTemplates() {
  return useQuery({
    queryKey: ['analysis-templates'],
    queryFn: async () => {
      return await backendApiClient.request('/ai/analysis/templates')
    },
  })
}

/**
 * Hook to create custom analysis template
 */
export function useCreateAnalysisTemplate() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: {
      name: string
      description?: string
      analysisTypes: string[]
      options: Record<string, any>
      prompt?: string
    }) => {
      return await backendApiClient.request('/ai/analysis/templates', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (template) => {
      queryClient.invalidateQueries({ queryKey: ['analysis-templates'] })
      toast({
        title: 'Template created',
        description: `Analysis template "${template.name}" has been created successfully.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error creating template',
        description: 'There was a problem creating the analysis template. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to batch analyze multiple documents
 */
export function useBatchAnalyze() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: {
      documentIds: string[]
      analysisTypes: string[]
      options?: Record<string, any>
    }) => {
      return await backendApiClient.request('/ai/documents/batch-analyze', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (result) => {
      // Invalidate analysis queries for all documents
      result.documentIds?.forEach((documentId: string) => {
        queryClient.invalidateQueries({ queryKey: ['document-analysis', documentId] })
      })
      toast({
        title: 'Batch analysis started',
        description: `Analysis has been started for ${result.documentIds?.length || 0} documents.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error starting batch analysis',
        description: 'There was a problem starting the batch analysis. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
