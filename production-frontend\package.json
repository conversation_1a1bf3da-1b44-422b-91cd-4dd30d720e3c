{"name": "hepz-frontend-production", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "npx tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "analyze": "cross-env ANALYZE=true next build", "lighthouse": "lhci autorun"}, "dependencies": {"@azure/msal-browser": "^4.13.1", "@azure/msal-node": "^3.6.0", "@azure/msal-react": "^3.0.12", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@editorjs/checklist": "^1.6.0", "@editorjs/code": "^2.9.3", "@editorjs/editorjs": "^2.30.8", "@editorjs/embed": "^2.7.6", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.2", "@editorjs/inline-code": "^1.5.1", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.8", "@editorjs/marker": "^1.4.0", "@editorjs/paragraph": "^2.11.7", "@editorjs/quote": "^2.7.6", "@editorjs/table": "^2.4.5", "@editorjs/underline": "^1.2.1", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^4.1.3", "@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@microsoft/signalr": "^8.0.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.80.7", "@tanstack/react-virtual": "^3.0.0", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@tinymce/tinymce-react": "^6.1.0", "@types/crypto-js": "^4.2.2", "@types/dompurify": "^3.0.5", "@types/marked": "^5.0.2", "@types/react-signature-canvas": "^1.0.7", "axios": "^1.5.1", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^1.1.1", "crypto-js": "^4.2.0", "d3-scale": "^4.0.2", "date-fns": "^2.30.0", "diff": "^8.0.2", "dompurify": "^3.2.4", "framer-motion": "^12.7.3", "immer": "^10.1.1", "jose": "^6.0.11", "jotai": "^2.6.5", "lodash.debounce": "^4.0.8", "lucide-react": "^0.483.0", "marked": "^15.0.7", "next": "15.3.3", "next-themes": "^0.4.6", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-force-graph": "^1.47.6", "react-force-graph-2d": "^1.27.1", "react-heatmap-grid": "^0.9.1", "react-hook-form": "^7.54.2", "react-intersection-observer": "^9.8.1", "react-pdf": "^7.5.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^7.4.0", "react-signature-canvas": "^1.1.0-alpha.2", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^9.1.3", "react-wordcloud": "^1.2.7", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.24.2", "zustand": "^4.5.7"}, "devDependencies": {"@lhci/cli": "^0.14.0", "@next/bundle-analyzer": "^15.3.3", "@playwright/test": "^1.48.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^29.5.14", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.6.0", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "autoprefixer": "^10.4.15", "cross-env": "^7.0.3", "eslint": "^8.51.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.29", "prettier": "^3.0.3", "tailwindcss": "^3.3.3", "typescript": "^5.8.3"}}