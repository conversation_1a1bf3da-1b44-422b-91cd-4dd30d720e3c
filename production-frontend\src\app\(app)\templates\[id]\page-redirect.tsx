'use client';

import { useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { useTemplate } from '@/hooks/templates';

export default function TemplateRedirectPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const templateId = params?.id as string;

  // Get projectId from query params or from template data
  const projectIdFromQuery = searchParams?.get('projectId');

  // Use the template hook to get template data
  const { data: template } = useTemplate(templateId);

  useEffect(() => {
    // If we have a projectId from query params, redirect immediately
    if (projectIdFromQuery) {
      router.replace(`/projects/${projectIdFromQuery}/templates/${templateId}`);
      return;
    }

    // If template is loaded and has a projectId, redirect to the new URL
    if (template && template.projectId) {
      router.replace(`/projects/${template.projectId}/templates/${templateId}`);
    }
  }, [template, templateId, projectIdFromQuery, router]);

  // Show loading state while redirecting
  return (
    <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
      <p>Redirecting to template...</p>
    </div>
  );
}
