/**
 * Timer-based Azure Functions for scheduled tasks
 * Handles periodic maintenance, cleanup, monitoring, and batch processing
 */

import { InvocationContext, Timer, app } from '@azure/functions';
import { BlobServiceClient } from '@azure/storage-blob';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';

/**
 * Daily cleanup timer - runs every day at 2 AM UTC
 * Cleans up expired documents, old logs, and temporary files
 */
async function dailyCleanupTimer(myTimer: Timer, context: InvocationContext): Promise<void> {
  logger.info('Daily cleanup timer triggered', {
    timestamp: new Date().toISOString(),
    isPastDue: myTimer.isPastDue
  });

  try {
    const cleanupResults = {
      expiredDocuments: 0,
      oldLogs: 0,
      tempFiles: 0,
      orphanedBlobs: 0
    };

    // 1. Clean up expired documents (older than 365 days)
    const expiredDate = new Date();
    expiredDate.setDate(expiredDate.getDate() - 365);

    const expiredDocs = await db.queryItems<any>('documents',
      'SELECT * FROM c WHERE c.createdAt < @expiredDate AND c.status != @deletedStatus',
      [expiredDate.toISOString(), 'deleted']
    );

    for (const doc of expiredDocs) {
      await db.updateItem('documents', {
        ...doc,
        status: 'expired',
        expiredAt: new Date().toISOString()
      });
      cleanupResults.expiredDocuments++;
    }

    // 2. Clean up old audit logs (older than 90 days)
    const oldLogDate = new Date();
    oldLogDate.setDate(oldLogDate.getDate() - 90);

    const oldLogs = await db.queryItems<any>('audit-logs',
      'SELECT * FROM c WHERE c.timestamp < @oldDate',
      [oldLogDate.toISOString()]
    );

    for (const log of oldLogs) {
      // Use log.id as partition key for audit logs
      await db.deleteItem('audit-logs', log.id, log.id);
      cleanupResults.oldLogs++;
    }

    // 3. Clean up temporary files in blob storage
    const blobServiceClient = new BlobServiceClient(
      process.env.AZURE_STORAGE_CONNECTION_STRING || ""
    );

    const tempContainer = blobServiceClient.getContainerClient('temp');
    const tempBlobs = tempContainer.listBlobsFlat();

    for await (const blob of tempBlobs) {
      const blobClient = tempContainer.getBlobClient(blob.name);
      const properties = await blobClient.getProperties();

      // Delete temp files older than 24 hours
      if (properties.lastModified) {
        const ageInHours = (Date.now() - properties.lastModified.getTime()) / (1000 * 60 * 60);
        if (ageInHours > 24) {
          await blobClient.delete();
          cleanupResults.tempFiles++;
        }
      }
    }

    // 4. Find and clean orphaned blobs (blobs without corresponding database records)
    const documentsContainer = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || 'documents'
    );

    const documentBlobs = documentsContainer.listBlobsFlat();
    for await (const blob of documentBlobs) {
      const docs = await db.queryItems<any>('documents',
        'SELECT * FROM c WHERE c.blobName = @blobName',
        [blob.name]
      );

      if (docs.length === 0) {
        // Orphaned blob - delete it
        const blobClient = documentsContainer.getBlobClient(blob.name);
        await blobClient.delete();
        cleanupResults.orphanedBlobs++;
      }
    }

    logger.info('Daily cleanup completed', cleanupResults);

    // Publish cleanup event
    await publishEvent(
      EventType.SYSTEM_HEALTH_CHECK,
      'system/daily-cleanup',
      {
        ...cleanupResults,
        timestamp: new Date().toISOString(),
        status: 'completed'
      }
    );

  } catch (error) {
    logger.error('Daily cleanup timer failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    await publishEvent(
      EventType.SYSTEM_HEALTH_CHECK,
      'system/daily-cleanup',
      {
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      }
    );
  }
}

/**
 * Hourly health check timer - runs every hour
 * Monitors system health and performance metrics
 */
async function hourlyHealthCheckTimer(myTimer: Timer, _context: InvocationContext): Promise<void> {
  logger.info('Hourly health check timer triggered', {
    timestamp: new Date().toISOString(),
    isPastDue: myTimer.isPastDue
  });

  try {
    const healthMetrics = {
      timestamp: new Date().toISOString(),
      database: { status: 'unknown', responseTime: 0 },
      storage: { status: 'unknown', responseTime: 0 },
      redis: { status: 'unknown', responseTime: 0 },
      documentCount: 0,
      activeWorkflows: 0,
      queueDepth: 0
    };

    // Check database health
    const dbStart = Date.now();
    try {
      await db.queryItems<any>('documents',
        'SELECT VALUE COUNT(1) FROM c',
        []
      );
      healthMetrics.database.status = 'healthy';
      healthMetrics.database.responseTime = Date.now() - dbStart;
    } catch (error) {
      healthMetrics.database.status = 'unhealthy';
      healthMetrics.database.responseTime = Date.now() - dbStart;
    }

    // Check storage health
    const storageStart = Date.now();
    try {
      const blobServiceClient = new BlobServiceClient(
        process.env.AZURE_STORAGE_CONNECTION_STRING || ""
      );
      const containerClient = blobServiceClient.getContainerClient(
        process.env.DOCUMENT_CONTAINER || 'documents'
      );
      await containerClient.exists();
      healthMetrics.storage.status = 'healthy';
      healthMetrics.storage.responseTime = Date.now() - storageStart;
    } catch (error) {
      healthMetrics.storage.status = 'unhealthy';
      healthMetrics.storage.responseTime = Date.now() - storageStart;
    }

    // Get document count
    try {
      const docCountResult = await db.queryItems<any>('documents',
        'SELECT VALUE COUNT(1) FROM c WHERE c.status != @deletedStatus',
        ['deleted']
      );
      healthMetrics.documentCount = (docCountResult[0] as number) || 0;
    } catch (error) {
      logger.error('Failed to get document count', { error });
    }

    // Get active workflows count
    try {
      const workflowCountResult = await db.queryItems<any>('workflows',
        'SELECT VALUE COUNT(1) FROM c WHERE c.status = @activeStatus',
        ['active']
      );
      healthMetrics.activeWorkflows = (workflowCountResult[0] as number) || 0;
    } catch (error) {
      logger.error('Failed to get active workflows count', { error });
    }

    logger.info('Health check completed', healthMetrics);

    // Store health metrics
    await db.createItem('health-metrics', {
      id: `health-${Date.now()}`,
      ...healthMetrics,
      type: 'hourly-check'
    });

    // Publish health check event
    await publishEvent(
      EventType.SYSTEM_HEALTH_CHECK,
      'system/hourly-health',
      healthMetrics
    );

    // Check for alerts
    if (healthMetrics.database.status === 'unhealthy' ||
        healthMetrics.storage.status === 'unhealthy') {
      await publishEvent(
        EventType.PERFORMANCE_ALERT,
        'system/health-alert',
        {
          alertType: 'service_unhealthy',
          severity: 'high',
          metrics: healthMetrics,
          timestamp: new Date().toISOString()
        }
      );
    }

  } catch (error) {
    logger.error('Hourly health check timer failed', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Weekly analytics generation timer - runs every Sunday at 3 AM UTC
 * Generates weekly analytics reports and summaries
 */
async function weeklyAnalyticsTimer(myTimer: Timer, _context: InvocationContext): Promise<void> {
  logger.info('Weekly analytics timer triggered', {
    timestamp: new Date().toISOString(),
    isPastDue: myTimer.isPastDue
  });

  try {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    const analytics = {
      period: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      },
      documents: {
        uploaded: 0,
        processed: 0,
        shared: 0,
        totalSize: 0
      },
      workflows: {
        started: 0,
        completed: 0,
        failed: 0
      },
      users: {
        active: 0,
        newRegistrations: 0
      },
      performance: {
        avgProcessingTime: 0,
        errorRate: 0
      }
    };

    // Get document analytics
    const documentStats = await db.queryItems<any>('documents', `
        SELECT
          COUNT(1) as total,
          SUM(c.size) as totalSize
        FROM c
        WHERE c.createdAt >= @startDate AND c.createdAt < @endDate
      `,
      [startDate.toISOString(), endDate.toISOString()]
    );

    if (documentStats.length > 0) {
      const stats = documentStats[0] as any;
      analytics.documents.uploaded = stats.total || 0;
      analytics.documents.totalSize = stats.totalSize || 0;
    }

    // Get workflow analytics
    const workflowStats = await db.queryItems<any>('workflows', `
        SELECT
          c.status,
          COUNT(1) as count
        FROM c
        WHERE c.createdAt >= @startDate AND c.createdAt < @endDate
        GROUP BY c.status
      `,
      [startDate.toISOString(), endDate.toISOString()]
    );

    for (const stat of workflowStats) {
      const workflowStat = stat as any;
      switch (workflowStat.status) {
        case 'active':
        case 'started':
          analytics.workflows.started += workflowStat.count;
          break;
        case 'completed':
          analytics.workflows.completed += workflowStat.count;
          break;
        case 'failed':
          analytics.workflows.failed += workflowStat.count;
          break;
      }
    }

    logger.info('Weekly analytics generated', analytics);

    // Store analytics
    await db.createItem('analytics', {
      id: `weekly-${startDate.getTime()}`,
      type: 'weekly',
      ...analytics,
      generatedAt: new Date().toISOString()
    });

    // Publish analytics event
    await publishEvent(
      EventType.ANALYTICS_GENERATED,
      'analytics/weekly-report',
      analytics
    );

  } catch (error) {
    logger.error('Weekly analytics timer failed', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Register timer functions
app.timer('dailyCleanupTimer', {
  schedule: '0 0 2 * * *', // Every day at 2 AM UTC
  handler: dailyCleanupTimer
});

app.timer('hourlyHealthCheckTimer', {
  schedule: '0 0 * * * *', // Every hour
  handler: hourlyHealthCheckTimer
});

app.timer('weeklyAnalyticsTimer', {
  schedule: '0 0 3 * * 0', // Every Sunday at 3 AM UTC
  handler: weeklyAnalyticsTimer
});
