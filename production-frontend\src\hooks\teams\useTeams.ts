/**
 * Teams Hook
 * Manages team operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'

export interface Team {
  id: ID
  name: string
  description?: string
  organizationId: ID
  projectId?: ID
  members: TeamMember[]
  settings: TeamSettings
  status: 'active' | 'inactive' | 'archived'
  createdAt: string
  updatedAt: string
  createdBy: ID
}

export interface TeamMember {
  id: ID
  teamId: ID
  userId: ID
  user: {
    id: ID
    name: string
    email: string
    avatar?: string
  }
  role: string
  permissions: string[]
  joinedAt: string
  status: 'active' | 'inactive' | 'pending'
}

export interface TeamSettings {
  isPublic: boolean
  allowSelfJoin: boolean
  requireApproval: boolean
  maxMembers?: number
}

export interface UseTeamsOptions {
  organizationId?: ID
  projectId?: ID
  autoLoad?: boolean
}

export interface UseTeamsResult {
  teams: Team[]
  loading: boolean
  error: string | null
  
  loadTeams: () => Promise<void>
  createTeam: (data: Partial<Team>) => Promise<Team>
  updateTeam: (teamId: ID, updates: Partial<Team>) => Promise<void>
  deleteTeam: (teamId: ID) => Promise<void>
  
  refresh: () => Promise<void>
}

export function useTeams(options: UseTeamsOptions = {}): UseTeamsResult {
  const { organizationId, projectId, autoLoad = true } = options
  const { toast } = useToast()
  
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadTeams = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (organizationId) params.append('organizationId', organizationId)
      if (projectId) params.append('projectId', projectId)

      const response = await backendApiClient.request(`/teams?${params}`)
      setTeams(response || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load teams'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [organizationId, projectId, toast])

  const createTeam = useCallback(async (data: Partial<Team>): Promise<Team> => {
    try {
      const response = await backendApiClient.request<Team>('/teams', {
        method: 'POST',
        body: JSON.stringify(data)
      })
      await loadTeams()
      
      toast({
        type: 'success',
        title: 'Team created',
        description: `Team "${data.name}" has been created successfully.`,
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create team'
      
      toast({
        type: 'error',
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadTeams, toast])

  const updateTeam = useCallback(async (teamId: ID, updates: Partial<Team>) => {
    try {
      await backendApiClient.request(`/teams/${teamId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      await loadTeams()
      
      toast({
        type: 'success',
        title: 'Team updated',
        description: 'Team has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update team'
      
      toast({
        type: 'error',
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [loadTeams, toast])

  const deleteTeam = useCallback(async (teamId: ID) => {
    try {
      await backendApiClient.request(`/teams/${teamId}`, {
        method: 'DELETE'
      })
      await loadTeams()
      
      toast({
        type: 'success',
        title: 'Team deleted',
        description: 'Team has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete team'
      
      toast({
        type: 'error',
        title: 'Deletion failed',
        description: errorMessage,
      })
    }
  }, [loadTeams, toast])

  useEffect(() => {
    if (autoLoad) {
      loadTeams()
    }
  }, [autoLoad, loadTeams])

  return {
    teams,
    loading,
    error,
    loadTeams,
    createTeam,
    updateTeam,
    deleteTeam,
    refresh: loadTeams,
  }
}
