/**
 * AI Document Generator Type Declarations
 */

export interface SmartContentSuggestion {
  id: string
  type: 'text' | 'image' | 'table' | 'chart' | 'form'
  content: string
  confidence: number
  position?: {
    page: number
    x: number
    y: number
  }
  metadata: {
    source: string
    category: string
    tags: string[]
    relevanceScore: number
  }
  alternatives?: Array<{
    content: string
    confidence: number
  }>
}

export interface DocumentGenerationOptions {
  template?: string
  style?: 'formal' | 'casual' | 'technical' | 'legal'
  language?: string
  includeImages?: boolean
  includeTables?: boolean
  includeCharts?: boolean
  maxPages?: number
  targetAudience?: string
  purpose?: string
}

export interface GeneratedDocument {
  id: string
  content: string
  metadata: {
    title: string
    author: string
    createdAt: string
    wordCount: number
    pageCount: number
    language: string
  }
  sections: Array<{
    id: string
    title: string
    content: string
    type: 'introduction' | 'body' | 'conclusion' | 'appendix'
    order: number
  }>
  suggestions: SmartContentSuggestion[]
  quality: {
    readabilityScore: number
    grammarScore: number
    coherenceScore: number
    completenessScore: number
  }
}

export class AIDocumentGenerator {
  constructor(options?: {
    apiKey?: string
    model?: string
    temperature?: number
    maxTokens?: number
  })

  generateDocument(prompt: string, options?: DocumentGenerationOptions): Promise<GeneratedDocument>
  generateSuggestions(context: string, type?: string): Promise<SmartContentSuggestion[]>
  improveContent(content: string, improvements?: string[]): Promise<string>
  summarizeDocument(content: string, maxLength?: number): Promise<string>
  translateDocument(content: string, targetLanguage: string): Promise<string>
  
  // Content analysis
  analyzeReadability(content: string): Promise<{
    score: number
    level: string
    suggestions: string[]
  }>
  
  extractKeyPoints(content: string): Promise<string[]>
  generateOutline(topic: string): Promise<Array<{
    title: string
    subtopics: string[]
    estimatedLength: number
  }>>
  
  // Event handlers
  onGenerationProgress(callback: (progress: number) => void): void
  onGenerationComplete(callback: (document: GeneratedDocument) => void): void
  onError(callback: (error: Error) => void): void
}
