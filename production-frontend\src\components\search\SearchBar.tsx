import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSearch } from '@/hooks/search';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Search as SearchIcon, X } from 'lucide-react';
import SearchSuggestions from './SearchSuggestions';

interface SearchBarProps {
  placeholder?: string;
  className?: string;
  value?: string;
  onChange?: (query: string) => void;
  onSearch?: (query: string) => void;
  autoFocus?: boolean;
  showSuggestions?: boolean;
  variant?: 'default' | 'minimal' | 'command';
  size?: 'default' | 'sm' | 'lg';
}

export function SearchBar({
  placeholder = 'Search...',
  className = '',
  value,
  onChange,
  onSearch,
  autoFocus = false,
  showSuggestions = true,
  variant = 'default',
  size = 'default'
}: SearchBarProps) {
  const router = useRouter();
  const { search: handleSearch } = useSearch();
  const [internalQuery, setInternalQuery] = useState('');

  // Use controlled value if provided, otherwise use internal state
  const query = value !== undefined ? value : internalQuery;
  const setQuery = value !== undefined ? (onChange || (() => {})) : setInternalQuery;
  const [isFocused, setIsFocused] = useState(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input on mount if autoFocus is true
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Handle search
  const executeSearch = () => {
    if (!query.trim()) return;

    if (onSearch) {
      onSearch(query);
    } else {
      handleSearch({ query });
      router.push(`/search?q=${encodeURIComponent(query)}`);
    }

    setIsPopoverOpen(false);
  };

  // Handle key down
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      executeSearch();
    } else if (e.key === 'Escape') {
      setIsPopoverOpen(false);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (selectedQuery: string) => {
    setQuery(selectedQuery);
    executeSearch();
  };

  // Handle clear
  const handleClear = () => {
    setQuery('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Determine input size class
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'h-8 text-sm';
      case 'lg':
        return 'h-12 text-lg';
      default:
        return 'h-10';
    }
  };

  // Render based on variant
  if (variant === 'command') {
    return (
      <Button
        variant="outline"
        className={`justify-between w-full ${className}`}
        onClick={() => setIsPopoverOpen(true)}
      >
        <div className="flex items-center">
          <SearchIcon className="mr-2 h-4 w-4" />
          <span className="text-muted-foreground">{placeholder}</span>
        </div>
        <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <Popover open={isPopoverOpen && showSuggestions} onOpenChange={setIsPopoverOpen}>
        <PopoverTrigger asChild>
          <div className="relative flex w-full items-center">
            <SearchIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              ref={inputRef}
              type="text"
              placeholder={placeholder}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => {
                setIsFocused(true);
                if (showSuggestions) setIsPopoverOpen(true);
              }}
              onBlur={() => setIsFocused(false)}
              className={`pl-10 pr-10 ${getSizeClass()} ${variant === 'minimal' ? 'border-0 bg-transparent focus-visible:bg-background focus-visible:ring-0 focus-visible:ring-offset-0' : ''}`}
            />
            {query && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-1/2 h-8 w-8 -translate-y-1/2"
                onClick={handleClear}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-[var(--radix-popover-trigger-width)] p-0"
          align="start"
          sideOffset={4}
        >
          <SearchSuggestions
            onSearch={handleSuggestionSelect}
            onClose={() => setIsPopoverOpen(false)}
            autoFocus={false}
            placeholder={placeholder}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

export default SearchBar;
