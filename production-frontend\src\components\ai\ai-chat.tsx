'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Send, Bot, User, Loader2, <PERSON><PERSON><PERSON>, FileText, <PERSON><PERSON>, ThumbsUp, ThumbsDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { aiService } from '@/services/ai-service'
import { useToast } from '@/hooks/use-toast'
import type { ChatMessage, ChatSession } from '@/services/ai-service'

interface AIChatProps {
  sessionId?: string
  context?: {
    documentIds?: string[]
    projectId?: string
    organizationId?: string
  }
  className?: string
}

export function AIChat({ sessionId, context, className }: AIChatProps) {
  const [session, setSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const { toast } = useToast()

  // Initialize or load session
  useEffect(() => {
    const initializeSession = async () => {
      try {
        if (sessionId) {
          const existingSession = aiService.getChatSession(sessionId)
          if (existingSession) {
            setSession(existingSession)
            setMessages(existingSession.messages)
          }
        } else {
          const newSession = await aiService.createChatSession(
            'New Chat',
            context ? {
              documentIds: context.documentIds || [],
              projectId: context.projectId,
              organizationId: context.organizationId
            } : undefined
          )
          setSession(newSession)
          setMessages([])
        }
      } catch (error) {
        console.error('Failed to initialize chat session:', error)
        toast({
          title: 'Error',
          description: 'Failed to initialize chat session',
          variant: 'destructive'
        })
      }
    }

    initializeSession()
  }, [sessionId, context, toast])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = async () => {
    if (!input.trim() || !session || isLoading) return

    const userMessage = input.trim()
    setInput('')
    setIsLoading(true)
    setIsTyping(true)

    try {
      // Add user message immediately
      const newUserMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        role: 'user',
        content: userMessage,
        timestamp: Date.now()
      }
      setMessages(prev => [...prev, newUserMessage])

      // Send to AI service
      const assistantMessage = await aiService.sendChatMessage(
        session.id,
        userMessage,
        context?.documentIds?.[0] ? { documentId: context.documentIds[0] } : undefined
      )

      // Update messages with actual response
      setMessages(prev => {
        const filtered = prev.filter(m => m.id !== newUserMessage.id)
        return [...filtered, newUserMessage, assistantMessage]
      })

      // Update session
      const updatedSession = aiService.getChatSession(session.id)
      if (updatedSession) {
        setSession(updatedSession)
      }

    } catch (error) {
      console.error('Failed to send message:', error)
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
      setIsTyping(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: 'Copied',
        description: 'Message copied to clipboard'
      })
    } catch (error) {
      console.error('Failed to copy:', error)
    }
  }

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>')
  }

  const getMessageIcon = (role: ChatMessage['role']) => {
    switch (role) {
      case 'user':
        return <User className="h-4 w-4" />
      case 'assistant':
        return <Bot className="h-4 w-4" />
      case 'system':
        return <Sparkles className="h-4 w-4" />
      default:
        return <Bot className="h-4 w-4" />
    }
  }

  const getMessageBgColor = (role: ChatMessage['role']) => {
    switch (role) {
      case 'user':
        return 'bg-primary text-primary-foreground'
      case 'assistant':
        return 'bg-muted'
      case 'system':
        return 'bg-accent'
      default:
        return 'bg-muted'
    }
  }

  if (!session) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Initializing chat...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5" />
          AI Assistant
          {context?.documentIds && context.documentIds.length > 0 && (
            <Badge variant="secondary" className="ml-auto">
              <FileText className="h-3 w-3 mr-1" />
              {context.documentIds.length} doc{context.documentIds.length > 1 ? 's' : ''}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="h-96 px-4">
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="text-center text-muted-foreground py-8">
                <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Start a conversation with the AI assistant</p>
                <p className="text-sm mt-2">
                  Ask questions about your documents, get summaries, or request analysis
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                {message.role !== 'user' && (
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      {getMessageIcon(message.role)}
                    </div>
                  </div>
                )}

                <div
                  className={`max-w-[80%] rounded-lg px-3 py-2 ${getMessageBgColor(
                    message.role
                  )}`}
                >
                  <div
                    className="text-sm"
                    dangerouslySetInnerHTML={{
                      __html: formatMessage(message.content)
                    }}
                  />
                  
                  <div className="flex items-center justify-between mt-2 text-xs opacity-70">
                    <span>
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </span>
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={() => copyToClipboard(message.content)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      
                      {message.role === 'assistant' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                          >
                            <ThumbsUp className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0"
                          >
                            <ThumbsDown className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {message.metadata?.confidence && (
                    <div className="mt-2">
                      <Badge variant="outline" className="text-xs">
                        Confidence: {Math.round(message.metadata.confidence * 100)}%
                      </Badge>
                    </div>
                  )}
                </div>

                {message.role === 'user' && (
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                      {getMessageIcon(message.role)}
                    </div>
                  </div>
                )}
              </div>
            ))}

            {isTyping && (
              <div className="flex gap-3 justify-start">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <Bot className="h-4 w-4" />
                  </div>
                </div>
                <div className="bg-muted rounded-lg px-3 py-2">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce delay-100" />
                    <div className="w-2 h-2 bg-current rounded-full animate-bounce delay-200" />
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        <Separator />

        <div className="p-4">
          <div className="flex gap-2">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your documents..."
              className="min-h-[60px] resize-none"
              disabled={isLoading}
            />
            <Button
              onClick={handleSendMessage}
              disabled={!input.trim() || isLoading}
              size="sm"
              className="px-3"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
            <span>Press Enter to send, Shift+Enter for new line</span>
            {context?.documentIds && (
              <span>{context.documentIds.length} document(s) in context</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
