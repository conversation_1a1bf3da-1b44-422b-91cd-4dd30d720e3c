# HEPZ Frontend

This is the frontend application for the HEPZ Enterprise Document Processing Platform.

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm 9.x or higher

### Installation

```bash
# Install dependencies
npm install
```

### Development

```bash
# Start development server
npm run dev
```

### Production

```bash
# Build for production
npm run build

# Start production server
npm start
```

## Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── app/                # Next.js App Router
│   │   ├── (auth)/         # Authentication routes
│   │   ├── (app)/          # Main application routes
│   │   └── layout.tsx      # Root layout
│   ├── components/         # React components
│   │   ├── ui/             # UI components
│   │   ├── documents/      # Document-related components
│   │   └── ...             # Other components
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utility functions
│   ├── services/           # API services
│   └── types/              # TypeScript type definitions
```

## Component Standards

All components should follow the standards defined in `src/docs/component-standards.md`.

## New Components

### Error Handling

Use the `ErrorDisplay` component for displaying errors:

```tsx
import { ErrorDisplay } from "@/components/ui/error-display";

// Basic usage
<ErrorDisplay 
  title="An error occurred" 
  description="Failed to load data" 
  error={error} 
  onRetry={handleRetry} 
/>

// Inline variant
<ErrorDisplay 
  description="Failed to save" 
  variant="inline" 
/>
```

### Loading States

Use the `LoadingState` component for displaying loading states:

```tsx
import { LoadingState } from "@/components/ui/loading-state";

// Basic usage
<LoadingState />

// Skeleton variant
<LoadingState variant="skeleton" count={3} />

// Inline variant
<LoadingState variant="inline" title="Loading..." />
```

### Data Display

Use the `DataDisplay` component for handling data fetching states:

```tsx
import { DataDisplay } from "@/components/ui/data-display";

// Basic usage
<DataDisplay
  data={data}
  isLoading={isLoading}
  isError={isError}
  error={error}
>
  {(data) => (
    <div>
      {data.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  )}
</DataDisplay>
```

### Responsive Design

Use the responsive container components for handling different screen sizes:

```tsx
import { 
  ResponsiveContainer, 
  MobileOnly, 
  TabletAndAbove, 
  DesktopOnly 
} from "@/components/ui/responsive-container";

// Show different content based on screen size
<ResponsiveContainer
  breakpoints={{
    sm: <MobileView />,
    md: <TabletView />,
    lg: <DesktopView />,
  }}
>
  <DefaultView />
</ResponsiveContainer>

// Only show on mobile
<MobileOnly>
  <MobileContent />
</MobileOnly>

// Only show on tablet and above
<TabletAndAbove>
  <DesktopContent />
</TabletAndAbove>
```

### Virtual Lists

Use the `VirtualList` component for efficiently rendering large lists:

```tsx
import { VirtualList } from "@/components/ui/virtual-list";

// Basic usage
<VirtualList
  items={items}
  renderItem={(item, index) => (
    <div key={item.id}>{item.name}</div>
  )}
  height={400}
  itemHeight={50}
/>
```

### Lazy Loading

Use the `LazyComponent` or `createLazyComponent` for lazy loading components:

```tsx
import { LazyComponent, createLazyComponent } from "@/components/lazy-component";

// Direct usage
<LazyComponent
  importFn={() => import("@/components/heavy-component")}
  props={{ data: someData }}
/>

// Create a reusable lazy component
const LazyHeavyComponent = createLazyComponent(
  () => import("@/components/heavy-component")
);

// Use it like a normal component
<LazyHeavyComponent data={someData} />
```

### Real-Time Collaboration

Use the `EnhancedRealTimeCollaboration` component for collaborative editing:

```tsx
import { EnhancedRealTimeCollaboration } from "@/components/documents/enhanced-real-time-collaboration";

// Basic usage
<EnhancedRealTimeCollaboration
  documentId="doc-123"
  documentName="My Document"
  initialContent="Initial content"
  currentUser={{
    id: "user-123",
    name: "John Doe",
    avatarUrl: "/avatars/john.jpg"
  }}
  onSave={handleSave}
/>
```

### Document Viewer

Use the `AdvancedDocumentViewer` component for viewing documents:

```tsx
import { AdvancedDocumentViewer } from "@/components/documents/advanced-document-viewer";

// Basic usage
<AdvancedDocumentViewer
  url="/documents/sample.pdf"
  title="Sample Document"
  allowAnnotations={true}
  allowDownload={true}
  onPageChange={handlePageChange}
/>
```

### Version Comparison

Use the `VersionComparison` component for comparing document versions:

```tsx
import { VersionComparison } from "@/components/documents/version-comparison";

// Basic usage
<VersionComparison
  documentId="doc-123"
  versions={versions}
  onFetchVersionContent={fetchVersionContent}
  onRestoreVersion={handleRestore}
/>
```

## Client-Side Caching

Use the `useCachedResource` hook for client-side caching with React Query:

```tsx
import { useCachedResource } from "@/hooks/use-cached-resources";

// Basic usage
const {
  data,
  isLoading,
  isError,
  error,
  refetch,
  mutate,
  isUpdating
} = useCachedResource({
  queryKey: ["documents", documentId],
  queryFn: () => documentService.getDocument(documentId),
  mutationFn: (data) => documentService.updateDocument(documentId, data),
  optimisticUpdate: (oldData, newData) => ({ ...oldData, ...newData }),
});
```

## Error Boundaries

Use the `ErrorBoundary` component to catch rendering errors:

```tsx
import { ErrorBoundary } from "@/components/error-boundary";

// Basic usage
<ErrorBoundary>
  <ComponentThatMightError />
</ErrorBoundary>

// With custom fallback
<ErrorBoundary
  fallback={<CustomErrorComponent />}
>
  <ComponentThatMightError />
</ErrorBoundary>
```

## Mobile Navigation

Use the `EnhancedMobileNav` component for mobile navigation:

```tsx
import { EnhancedMobileNav } from "@/components/enhanced-mobile-nav";

// Basic usage
<EnhancedMobileNav
  user={user}
  onLogout={handleLogout}
  notifications={{ count: 3, hasUnread: true }}
/>
```
