import React, { useState } from 'react';
import { useSearchAnalytics } from '@/hooks/search';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/components/ui/charts';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TrendingUp, Search, Clock, Filter } from 'lucide-react';

interface SearchAnalyticsPanelProps {
  className?: string;
}

export function SearchAnalyticsPanel({ className }: SearchAnalyticsPanelProps) {
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month' | 'all'>('week');

  // Get analytics data
  const {
    analytics,
    isLoading: isLoadingAnalytics,
    error
  } = useSearchAnalytics();

  const popularQueries = analytics.popularQueries;

  // Handle timeframe change
  const handleTimeframeChange = (value: string) => {
    const newTimeframe = value as 'day' | 'week' | 'month' | 'all';
    setTimeframe(newTimeframe);

    // Update analytics options
    const now = new Date();
    let startDate: string | undefined;

    switch (newTimeframe) {
      case 'day':
        const yesterday = new Date(now);
        yesterday.setDate(yesterday.getDate() - 1);
        startDate = yesterday.toISOString();
        break;
      case 'week':
        const lastWeek = new Date(now);
        lastWeek.setDate(lastWeek.getDate() - 7);
        startDate = lastWeek.toISOString();
        break;
      case 'month':
        const lastMonth = new Date(now);
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        startDate = lastMonth.toISOString();
        break;
      case 'all':
      default:
        startDate = undefined;
        break;
    }

    // Placeholder for setting options
    console.log('Setting options:', {
      startDate,
      endDate: now.toISOString(),
      limit: 100
    });
  };

  // Prepare chart data
  const prepareSearchVolumeData = () => {
    if (!analytics || analytics.searchTrends.length === 0) return [];

    // Use the searchTrends data directly
    return analytics.searchTrends.map((trend: { date: string; count: number }) => ({
      date: new Date(trend.date).toLocaleDateString(),
      count: trend.count
    }));
  };

  const prepareSearchTypeData = () => {
    if (!analytics) return [];

    // Extract search type data from analytics if available
    if ((analytics as any).searchByType) {
      return Object.entries((analytics as any).searchByType).map(([type, count]) => ({
        type,
        count: typeof count === 'number' ? count : 0
      }));
    }

    // Fallback: analyze popular queries to infer search types
    if (analytics.popularQueries && analytics.popularQueries.length > 0) {
      const typeMap: Record<string, number> = {};

      analytics.popularQueries.forEach(query => {
        // Simple heuristic to categorize search queries
        const queryLower = query.query.toLowerCase();
        let type = 'general';

        if (queryLower.includes('document') || queryLower.includes('file') || queryLower.includes('pdf')) {
          type = 'document';
        } else if (queryLower.includes('project') || queryLower.includes('workspace')) {
          type = 'project';
        } else if (queryLower.includes('template') || queryLower.includes('form')) {
          type = 'template';
        } else if (queryLower.includes('workflow') || queryLower.includes('process')) {
          type = 'workflow';
        } else if (queryLower.includes('user') || queryLower.includes('member')) {
          type = 'user';
        }

        typeMap[type] = (typeMap[type] || 0) + query.count;
      });

      return Object.entries(typeMap).map(([type, count]) => ({ type, count }));
    }

    return [];
  };

  const calculateAverageResultsClicked = () => {
    if (!analytics || analytics.searchTrends.length === 0) return 0;

    // Use the averageResultsPerSearch from analytics
    return analytics.averageResultsPerSearch;
  };

  // Loading state
  if (isLoadingAnalytics) {
    return (
      <Card className={`col-span-2 ${className}`}>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-72" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  // No data state
  if (!analytics || analytics.searchTrends.length === 0) {
    return (
      <Card className={`col-span-2 ${className}`}>
        <CardHeader>
          <CardTitle>Search Analytics</CardTitle>
          <CardDescription>No search data available</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px]">
          <p className="text-muted-foreground">No search activity recorded yet</p>
        </CardContent>
      </Card>
    );
  }

  // Prepare data
  const searchVolumeData = prepareSearchVolumeData();
  const searchTypeData = prepareSearchTypeData();
  const averageResultsClicked = calculateAverageResultsClicked();

  return (
    <Card className={`col-span-2 ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Search Analytics</CardTitle>
          <CardDescription>
            Search activity and performance metrics
          </CardDescription>
        </div>
        <Select value={timeframe} onValueChange={handleTimeframeChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Timeframe" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Last 24h</SelectItem>
            <SelectItem value="week">Last Week</SelectItem>
            <SelectItem value="month">Last Month</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="popular">Popular Searches</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Search className="h-4 w-4 mr-2 text-muted-foreground" />
                    Total Searches
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-2xl font-bold">{analytics.totalSearches}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                    Avg. Results Clicked
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-2xl font-bold">{averageResultsClicked.toFixed(1)}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Filter className="h-4 w-4 mr-2 text-muted-foreground" />
                    Search Types
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="flex flex-wrap gap-2">
                    {searchTypeData.map(({ type }) => (
                      <Badge key={type} variant="secondary">
                        {type}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="h-[300px]">
              <LineChart
                data={searchVolumeData}
                index="date"
                categories={["count"]}
                colors={["blue"]}
                valueFormatter={(value) => `${value} searches`}
                showLegend={false}
                showGridLines={false}
                showAnimation={true}
                title="Search Volume Over Time"
              />
            </div>
          </TabsContent>

          <TabsContent value="trends">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="h-[300px]">
                <BarChart
                  data={searchTypeData}
                  index="type"
                  categories={["count"]}
                  colors={["blue"]}
                  valueFormatter={(value) => `${value} searches`}
                  showLegend={false}
                  showGridLines={false}
                  showAnimation={true}
                  title="Search Types"
                />
              </div>

              <div className="h-[300px]">
                <PieChart
                  data={searchTypeData}
                  index="type"
                  category="count"
                  valueFormatter={(value) => `${value} searches`}
                  showAnimation={true}
                  title="Search Type Distribution"
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="popular">
            <div className="space-y-4">
              <h3 className="text-sm font-medium flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-muted-foreground" />
                Top Search Queries
              </h3>

              {isLoadingAnalytics ? (
                <div className="space-y-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-8 w-full" />
                  ))}
                </div>
              ) : popularQueries.length > 0 ? (
                <div className="space-y-2">
                  {popularQueries.map((query: { query: string; count: number }, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted/50">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{index + 1}</Badge>
                        <span className="font-medium">{query.query}</span>
                      </div>
                      <Badge variant="secondary">{query.count} searches</Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No popular searches recorded yet</p>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

export default SearchAnalyticsPanel;
