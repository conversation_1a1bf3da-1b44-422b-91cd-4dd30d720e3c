'use client';

import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Play, 
  Pause,
  ArrowRight,
  User,
  Calendar
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  name: string;
  description?: string;
  status: 'pending' | 'active' | 'completed' | 'failed' | 'skipped';
  assignedTo?: string;
  dueDate?: string;
  completedAt?: string;
  order: number;
}

interface WorkflowDiagramProps {
  steps: WorkflowStep[];
  onStepClick?: (step: WorkflowStep) => void;
  onStepAction?: (stepId: string, action: 'start' | 'complete' | 'skip') => void;
  readonly?: boolean;
}

export function WorkflowDiagram({ 
  steps, 
  onStepClick, 
  onStepAction, 
  readonly = false 
}: WorkflowDiagramProps) {
  const sortedSteps = useMemo(() => 
    [...steps].sort((a, b) => a.order - b.order), 
    [steps]
  );

  const getStepIcon = useCallback((status: WorkflowStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'active':
        return <Play className="h-5 w-5 text-blue-600" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'skipped':
        return <Pause className="h-5 w-5 text-gray-400" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  }, []);

  const getStepBadgeVariant = useCallback((status: WorkflowStep['status']) => {
    switch (status) {
      case 'completed':
        return 'default' as const;
      case 'active':
        return 'secondary' as const;
      case 'failed':
        return 'destructive' as const;
      case 'skipped':
        return 'outline' as const;
      default:
        return 'outline' as const;
    }
  }, []);

  const canPerformAction = useCallback((step: WorkflowStep, action: string) => {
    if (readonly) return false;
    
    switch (action) {
      case 'start':
        return step.status === 'pending';
      case 'complete':
        return step.status === 'active';
      case 'skip':
        return step.status === 'pending' || step.status === 'active';
      default:
        return false;
    }
  }, [readonly]);

  const formatDate = useCallback((dateString?: string) => {
    if (!dateString) return null;
    return new Date(dateString).toLocaleDateString();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex flex-col space-y-4">
        {sortedSteps.map((step, index) => (
          <div key={step.id} className="relative">
            {/* Connection Line */}
            {index < sortedSteps.length - 1 && (
              <div className="absolute left-6 top-16 w-0.5 h-8 bg-border z-0" />
            )}
            
            <Card 
              className={`relative z-10 transition-all duration-200 ${
                step.status === 'active' ? 'ring-2 ring-primary' : ''
              } ${onStepClick ? 'cursor-pointer hover:shadow-md' : ''}`}
              onClick={() => onStepClick?.(step)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {getStepIcon(step.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg">{step.name}</CardTitle>
                      {step.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {step.description}
                        </p>
                      )}
                    </div>
                  </div>
                  <Badge variant={getStepBadgeVariant(step.status)}>
                    {step.status}
                  </Badge>
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    {step.assignedTo && (
                      <div className="flex items-center space-x-1">
                        <User className="h-4 w-4" />
                        <span>{step.assignedTo}</span>
                      </div>
                    )}
                    
                    {step.dueDate && (
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Due: {formatDate(step.dueDate)}</span>
                      </div>
                    )}
                    
                    {step.completedAt && (
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-4 w-4" />
                        <span>Completed: {formatDate(step.completedAt)}</span>
                      </div>
                    )}
                  </div>
                  
                  {!readonly && onStepAction && (
                    <div className="flex space-x-2">
                      {canPerformAction(step, 'start') && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStepAction(step.id, 'start');
                          }}
                        >
                          Start
                        </Button>
                      )}
                      
                      {canPerformAction(step, 'complete') && (
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStepAction(step.id, 'complete');
                          }}
                        >
                          Complete
                        </Button>
                      )}
                      
                      {canPerformAction(step, 'skip') && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation();
                            onStepAction(step.id, 'skip');
                          }}
                        >
                          Skip
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Arrow between steps */}
            {index < sortedSteps.length - 1 && (
              <div className="flex justify-center py-2">
                <ArrowRight className="h-4 w-4 text-muted-foreground" />
              </div>
            )}
          </div>
        ))}
      </div>
      
      {sortedSteps.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No workflow steps defined</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
