/**
 * Document Processing Hooks
 * React hooks for document processing operations
 */

import { useCallback } from 'react'
import { useDocumentStore } from '@/stores/document-store'
import { useToast } from '@/hooks/use-toast'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import backendApiClient from '@/services/backend-api-client'

export interface DocumentProcessingJob {
  id: string
  documentId: string
  processingType: 'OCR' | 'ANALYSIS' | 'EXTRACTION' | 'CLASSIFICATION' | 'ENHANCEMENT' | 'CONVERSION'
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  progress: {
    percentage: number
    currentStep: string
    totalSteps: number
    completedSteps: number
    estimatedTimeRemaining?: number
  }
  options: {
    extractText?: boolean
    extractTables?: boolean
    extractKeyValuePairs?: boolean
    extractEntities?: boolean
    analyzeSentiment?: boolean
    classifyDocument?: boolean
    enhanceQuality?: boolean
    generateThumbnails?: boolean
    convertFormat?: string
    customOptions?: Record<string, any>
  }
  results?: {
    extractedText?: string
    tables?: Array<{
      headers: string[]
      rows: string[][]
      confidence: number
    }>
    keyValuePairs?: Array<{
      key: string
      value: string
      confidence: number
    }>
    entities?: Array<{
      type: string
      value: string
      confidence: number
      position: { start: number; end: number }
    }>
    sentiment?: {
      score: number
      label: 'positive' | 'negative' | 'neutral'
      confidence: number
    }
    classification?: {
      category: string
      confidence: number
      subcategories: Array<{
        name: string
        confidence: number
      }>
    }
    enhancedDocument?: {
      url: string
      format: string
      size: number
    }
    convertedDocument?: {
      url: string
      format: string
      size: number
    }
    thumbnails?: Array<{
      url: string
      size: string
      page?: number
    }>
  }
  organizationId: string
  projectId?: string
  createdAt: string
  startedAt?: string
  completedAt?: string
  error?: string
  metadata: Record<string, any>
}

export interface ProcessDocumentRequest {
  documentId: string
  processingType: 'OCR' | 'ANALYSIS' | 'EXTRACTION' | 'CLASSIFICATION' | 'ENHANCEMENT' | 'CONVERSION'
  options?: {
    extractText?: boolean
    extractTables?: boolean
    extractKeyValuePairs?: boolean
    extractEntities?: boolean
    analyzeSentiment?: boolean
    classifyDocument?: boolean
    enhanceQuality?: boolean
    generateThumbnails?: boolean
    convertFormat?: string
    priority?: 'low' | 'normal' | 'high'
    customOptions?: Record<string, any>
  }
  organizationId: string
  projectId?: string
}

export interface BatchProcessRequest {
  documentIds: string[]
  processingType: 'OCR' | 'ANALYSIS' | 'EXTRACTION' | 'CLASSIFICATION' | 'ENHANCEMENT' | 'CONVERSION'
  options?: {
    extractText?: boolean
    extractTables?: boolean
    extractKeyValuePairs?: boolean
    extractEntities?: boolean
    analyzeSentiment?: boolean
    classifyDocument?: boolean
    enhanceQuality?: boolean
    generateThumbnails?: boolean
    convertFormat?: string
    batchSize?: number
    maxConcurrency?: number
    priority?: 'low' | 'normal' | 'high'
    customOptions?: Record<string, any>
  }
  organizationId: string
  projectId?: string
}

/**
 * Hook to get document processing jobs
 */
export function useDocumentProcessingJobs(params?: {
  documentId?: string
  organizationId?: string
  projectId?: string
  processingType?: string
  status?: string
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['document-processing-jobs', params],
    queryFn: async () => {
      return await backendApiClient.request<DocumentProcessingJob[]>('/documents/processing/jobs', {
        params
      })
    },
  })
}

/**
 * Hook to get a specific processing job
 */
export function useDocumentProcessingJob(jobId: string) {
  return useQuery({
    queryKey: ['document-processing-job', jobId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentProcessingJob>(`/documents/processing/jobs/${jobId}`)
    },
    enabled: !!jobId,
    refetchInterval: 2000, // Refetch every 2 seconds
  })
}

/**
 * Hook to process a document
 */
export function useProcessDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ProcessDocumentRequest) => {
      return await backendApiClient.request<DocumentProcessingJob>('/documents/processing/start', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (job) => {
      queryClient.invalidateQueries({ queryKey: ['document-processing-jobs'] })
      queryClient.invalidateQueries({ queryKey: ['document', job.documentId] })
      toast({
        title: 'Processing started',
        description: `Document processing has been started. Job ID: ${job.id}`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error starting processing',
        description: 'There was a problem starting the document processing. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to batch process multiple documents
 */
export function useBatchProcessDocuments() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: BatchProcessRequest) => {
      return await backendApiClient.request<DocumentProcessingJob[]>('/documents/processing/batch', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (jobs) => {
      queryClient.invalidateQueries({ queryKey: ['document-processing-jobs'] })
      // Invalidate document queries for all processed documents
      jobs.forEach(job => {
        queryClient.invalidateQueries({ queryKey: ['document', job.documentId] })
      })
      toast({
        title: 'Batch processing started',
        description: `Processing ${jobs.length} documents. Check the processing dashboard for progress.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error starting batch processing',
        description: 'There was a problem starting the batch processing. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to cancel a processing job
 */
export function useCancelProcessingJob() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (jobId: string) => {
      return await backendApiClient.request(`/documents/processing/jobs/${jobId}/cancel`, {
        method: 'POST'
      })
    },
    onSuccess: (job) => {
      queryClient.invalidateQueries({ queryKey: ['document-processing-job', job.id] })
      queryClient.invalidateQueries({ queryKey: ['document-processing-jobs'] })
      toast({
        title: 'Processing cancelled',
        description: 'The document processing job has been cancelled successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error cancelling processing',
        description: 'There was a problem cancelling the processing job. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to retry a failed processing job
 */
export function useRetryProcessingJob() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (jobId: string) => {
      return await backendApiClient.request(`/documents/processing/jobs/${jobId}/retry`, {
        method: 'POST'
      })
    },
    onSuccess: (job) => {
      queryClient.invalidateQueries({ queryKey: ['document-processing-job', job.id] })
      queryClient.invalidateQueries({ queryKey: ['document-processing-jobs'] })
      toast({
        title: 'Processing restarted',
        description: 'The document processing job has been restarted successfully.',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error retrying processing',
        description: 'There was a problem retrying the processing job. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get processing statistics
 */
export function useProcessingStats(params?: {
  organizationId?: string
  projectId?: string
  dateRange?: { start: string; end: string }
}) {
  return useQuery({
    queryKey: ['processing-stats', params],
    queryFn: async () => {
      return await backendApiClient.request('/documents/processing/stats', {
        params
      })
    },
  })
}

/**
 * Hook to get available processing types
 */
export function useProcessingTypes() {
  return useQuery({
    queryKey: ['processing-types'],
    queryFn: async () => {
      return await backendApiClient.request('/documents/processing/types')
    },
  })
}

/**
 * Hook to get processing templates
 */
export function useProcessingTemplates() {
  return useQuery({
    queryKey: ['processing-templates'],
    queryFn: async () => {
      return await backendApiClient.request('/documents/processing/templates')
    },
  })
}

/**
 * Hook to create a processing template
 */
export function useCreateProcessingTemplate() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: {
      name: string
      description?: string
      processingType: string
      options: Record<string, any>
      organizationId: string
    }) => {
      return await backendApiClient.request('/documents/processing/templates', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (template) => {
      queryClient.invalidateQueries({ queryKey: ['processing-templates'] })
      toast({
        title: 'Template created',
        description: `Processing template "${template.name}" has been created successfully.`,
      })
    },
    onError: (error) => {
      toast({
        title: 'Error creating template',
        description: 'There was a problem creating the processing template. Please try again.',
        variant: 'destructive',
      })
    },
  })
}
