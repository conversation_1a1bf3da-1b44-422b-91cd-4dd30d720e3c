'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Plus, Search, Brain, Upload, MessageCircle, Zap } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FloatingAction {
  icon: React.ReactNode
  label: string
  onClick: () => void
  shortcut?: string
}

interface FloatingActionButtonProps {
  actions: FloatingAction[]
  className?: string
}

export function FloatingActionButton({ actions, className }: FloatingActionButtonProps) {
  const [isOpen, setIsOpen] = useState(false)

  const toggleOpen = () => setIsOpen(!isOpen)

  return (
    <TooltipProvider>
      <div className={cn("fixed bottom-6 right-6 z-50", className)}>
        {/* Action Buttons */}
        <div className={cn(
          "flex flex-col-reverse gap-3 mb-3 transition-all duration-300 ease-in-out",
          isOpen ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4 pointer-events-none"
        )}>
          {actions.map((action, index) => (
            <Tooltip key={index}>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
                  onClick={() => {
                    action.onClick()
                    setIsOpen(false)
                  }}
                >
                  {action.icon}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left" className="flex items-center gap-2">
                <span>{action.label}</span>
                {action.shortcut && (
                  <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                    {action.shortcut}
                  </kbd>
                )}
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Main FAB */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="lg"
              className={cn(
                "h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300",
                isOpen && "rotate-45"
              )}
              onClick={toggleOpen}
            >
              <Plus className="h-6 w-6" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left">
            {isOpen ? 'Close menu' : 'Quick actions'}
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  )
}

// Predefined action sets for common use cases
export const defaultActions: FloatingAction[] = [
  {
    icon: <Search className="h-4 w-4" />,
    label: 'Global Search',
    onClick: () => {
      // This will be overridden by the parent component
      console.log('Search action')
    },
    shortcut: '⌘K'
  },
  {
    icon: <Brain className="h-4 w-4" />,
    label: 'AI Assistant',
    onClick: () => {
      // This will be overridden by the parent component
      console.log('AI action')
    },
    shortcut: '⌘J'
  },
  {
    icon: <Upload className="h-4 w-4" />,
    label: 'Upload Document',
    onClick: () => {
      // This will be overridden by the parent component
      console.log('Upload action')
    }
  },
  {
    icon: <MessageCircle className="h-4 w-4" />,
    label: 'Start Collaboration',
    onClick: () => {
      // This will be overridden by the parent component
      console.log('Collaboration action')
    }
  }
]
