/**
 * PKI Digital Signatures Hook
 * React hook for managing PKI-enhanced digital signatures
 */

import { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

import {
  pkiSignatureService,
  type EnhancedDigitalSignature,
  type CreatePKISignatureRequest,
  type CreatePKISignatureResponse,
  type SignatureVerificationResult,
  type CertificateInfo
} from '@/services/pki-signature-service';
import { useAuthStore } from '@/stores/auth-store';

export interface UsePKISignaturesOptions {
  documentId?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface UsePKISignaturesReturn {
  // State
  signatures: EnhancedDigitalSignature[];
  isLoading: boolean;
  error: string | null;

  // Actions - Azure Key Vault PKI (Primary)
  createSignature: (request: CreatePKISignatureRequest) => Promise<CreatePKISignatureResponse>;
  createPKISignature: (request: CreatePKISignatureRequest) => Promise<CreatePKISignatureResponse>;
  verifySignature: (signatureId: string) => Promise<SignatureVerificationResult>;
  getSignatureDetails: (signatureId: string) => Promise<EnhancedDigitalSignature>;
  downloadCertificate: (signatureId: string) => Promise<void>;
  refreshSignatures: () => Promise<void>;

  // Utilities
  getComplianceInfo: (complianceLevel: string) => any;
  formatValidationResults: (validationResults: string[]) => any[];
  clearCache: (signatureId?: string) => void;
}

export function usePKISignatures(options: UsePKISignaturesOptions = {}): UsePKISignaturesReturn {
  const { documentId, autoRefresh = false, refreshInterval = 30000 } = options;
  const { user } = useAuthStore();
  const [error, setError] = useState<string | null>(null);
  const [signatures, setSignatures] = useState<EnhancedDigitalSignature[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch signatures
  const refreshSignatures = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const data = await pkiSignatureService.listUserSignatures(documentId);
      setSignatures(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch signatures');
    } finally {
      setIsLoading(false);
    }
  }, [user, documentId]);

  // Auto-refresh effect
  useEffect(() => {
    refreshSignatures();

    if (autoRefresh) {
      const interval = setInterval(refreshSignatures, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [refreshSignatures, autoRefresh, refreshInterval]);

  // Create PKI signature
  const createSignature = useCallback(async (request: CreatePKISignatureRequest) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await pkiSignatureService.createSignature(request);

      // Refresh signatures list
      await refreshSignatures();

      toast.success('Digital Signature Created', {
        description: `Secure digital signature created with ${data.complianceLevel} compliance level using Azure Key Vault PKI`
      });

      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create digital signature';
      setError(errorMessage);

      toast.error('Signature Creation Failed', {
        description: errorMessage
      });

      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [refreshSignatures]);

  // Verify signature
  const verifySignature = useCallback(async (signatureId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await pkiSignatureService.verifyPKISignature(signatureId);

      toast.success('Signature Verified', {
        description: data.isValid
          ? 'Signature is valid and trusted'
          : 'Signature verification failed'
      });

      return data;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to verify signature';
      setError(errorMessage);

      toast.error('Verification Failed', {
        description: errorMessage
      });

      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get signature details
  const getSignatureDetails = useCallback(async (signatureId: string): Promise<EnhancedDigitalSignature> => {
    try {
      setError(null);
      const details = await pkiSignatureService.getSignatureDetails(signatureId);
      return details;
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to get signature details';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // Download certificate
  const downloadCertificate = useCallback(async (signatureId: string): Promise<void> => {
    try {
      setError(null);
      const blob = await pkiSignatureService.downloadCertificate(signatureId);
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `certificate-${signatureId}.crt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success('Certificate Downloaded', {
        description: 'Certificate file has been downloaded'
      });
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to download certificate';
      setError(errorMessage);
      
      toast.error('Download Failed', {
        description: errorMessage
      });
    }
  }, []);

  // Utility functions
  const getComplianceInfo = useCallback((complianceLevel: string) => {
    return pkiSignatureService.getComplianceInfo(complianceLevel);
  }, []);

  const formatValidationResults = useCallback((validationResults: string[]) => {
    return pkiSignatureService.formatValidationResults(validationResults);
  }, []);

  const clearCache = useCallback((signatureId?: string) => {
    pkiSignatureService.clearCache(signatureId);
    // Refresh signatures after clearing cache
    refreshSignatures();
  }, [refreshSignatures]);

  // Clear error when user changes
  useEffect(() => {
    setError(null);
  }, [user?.id]);

  return {
    // State
    signatures,
    isLoading,
    error,

    // Actions - Azure Key Vault PKI (Primary)
    createSignature,
    createPKISignature: createSignature, // Alias for compatibility
    verifySignature,
    getSignatureDetails,
    downloadCertificate,
    refreshSignatures,

    // Utilities
    getComplianceInfo,
    formatValidationResults,
    clearCache
  };
}

/**
 * Hook for managing certificate information
 */
export function useCertificateInfo(certificateId?: string) {
  const [error, setError] = useState<string | null>(null);
  const [certificateInfo, setCertificateInfo] = useState<CertificateInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const fetchCertificateInfo = useCallback(async () => {
    if (!certificateId) return;

    setIsLoading(true);
    setError(null);

    try {
      const info = await pkiSignatureService.getCertificateInfo(certificateId);
      setCertificateInfo(info);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch certificate info');
    } finally {
      setIsLoading(false);
    }
  }, [certificateId]);

  const checkStatus = useCallback(async () => {
    if (!certificateId) return null;

    try {
      setError(null);
      return await pkiSignatureService.checkCertificateStatus(certificateId);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to check certificate status';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [certificateId]);

  useEffect(() => {
    fetchCertificateInfo();
  }, [fetchCertificateInfo]);

  return {
    certificateInfo,
    isLoading,
    error,
    checkStatus,
    refresh: fetchCertificateInfo
  };
}

/**
 * Hook for signature verification
 */
export function useSignatureVerification(signatureId?: string) {
  const [error, setError] = useState<string | null>(null);
  const [verificationResult, setVerificationResult] = useState<SignatureVerificationResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const reverify = useCallback(async () => {
    if (!signatureId) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await pkiSignatureService.verifyPKISignature(signatureId);
      setVerificationResult(result);
    } catch (err: any) {
      setError(err.message || 'Failed to verify signature');
    } finally {
      setIsLoading(false);
    }
  }, [signatureId]);

  useEffect(() => {
    reverify();
  }, [reverify]);

  return {
    verificationResult,
    isLoading,
    error,
    reverify
  };
}

export default usePKISignatures;
