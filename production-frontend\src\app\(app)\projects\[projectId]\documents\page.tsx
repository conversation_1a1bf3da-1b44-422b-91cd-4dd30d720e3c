'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { DocumentList } from '@/components/documents/document-list';
import { useProjects } from '@/hooks/projects/useProjects';
import { useDocuments } from '@/hooks/documents/useDocuments';

export default function ProjectDocumentsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;

  const { projects, loading: projectLoading } = useProjects({ organizationId: undefined });
  const { documents: documentsResponse, loading: documentsLoading } = useDocuments({ projectId });

  const project = projects?.find((p: any) => p.id === projectId);
  const documents = documentsResponse || [];
  const projectError = null;
  const documentsError = null;

  useEffect(() => {
    if (projectError || documentsError) {
      toast({
        title: 'Error',
        description: 'Failed to load project documents',
        variant: 'destructive',
      });
    }
  }, [projectError, documentsError, toast]);

  const isLoading = projectLoading || documentsLoading;

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading documents...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{project?.name} Documents</h1>
          <p className="text-muted-foreground">Manage documents for this project</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/projects/${projectId}`)}
          >
            Back to Project
          </Button>
          <Button
            onClick={() => router.push(`/projects/${projectId}/documents/upload`)}
          >
            Upload Document
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <DocumentList
            projectId={projectId}
            onDocumentClick={(documentId: string) => router.push(`/projects/${projectId}/documents/${documentId}`)}
          />
        </CardContent>
      </Card>
    </div>
  );
}
