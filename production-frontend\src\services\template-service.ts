/**
 * Template Service
 * Handles all template-related API calls using backend API client
 */

import { backendApiClient, type PaginatedResponse } from './backend-api-client'
import type { Template, TemplateType, TemplateStatus } from '../types/backend'

export interface TemplateSearchQuery {
  query?: string
  search?: string
  category?: string
  isPublic?: boolean
  organizationId?: string
  projectId?: string
  tags?: string[]
  page?: number
  pageSize?: number
}

export interface CreateTemplateRequest {
  name: string
  description?: string
  categoryId: string
  type: TemplateType
  status: TemplateStatus
  category?: string
  content?: any
  fields?: any[]
  isPublic?: boolean
  isDefault?: boolean
  tags?: string[]
  organizationId: string
  projectId?: string
  sections?: any[]
}

export interface UpdateTemplateRequest {
  name?: string
  description?: string
  category?: string
  content?: any
  fields?: any[]
  isPublic?: boolean
  tags?: string[]
}

export interface TemplateInstance {
  id: string
  templateId: string
  name: string
  data: Record<string, any>
  status: 'draft' | 'submitted' | 'approved' | 'rejected'
  createdAt: string
  updatedAt: string
  createdBy: string
}

export interface TemplateCategory {
  id: string
  name: string
  description?: string
  templateCount: number
}

export interface TemplateAnalytics {
  totalViews: number
  totalInstances: number
  averageRating: number
  popularFields: string[]
  usageOverTime: Array<{
    date: string
    views: number
    instances: number
  }>
}

export interface TemplatePreviewResult {
  html: string
  content?: string
  pdf?: string
  variables?: Record<string, any>
  errors?: string[]
}

class TemplateService {
  /**
   * Get all templates with optional filters
   */
  async getTemplates(params?: TemplateSearchQuery): Promise<PaginatedResponse<Template>> {
    return await backendApiClient.request('/templates/list', {
      method: 'GET',
      params
    })
  }

  /**
   * Get template by ID
   */
  async getTemplate(templateId: string): Promise<Template> {
    return await backendApiClient.request(`/templates/${templateId}`)
  }

  /**
   * Create new template
   */
  async createTemplate(templateData: CreateTemplateRequest): Promise<Template> {
    return await backendApiClient.request('/templates/create', {
      method: 'POST',
      body: JSON.stringify(templateData)
    })
  }

  /**
   * Update template
   */
  async updateTemplate(templateId: string, updateData: UpdateTemplateRequest): Promise<Template> {
    return await backendApiClient.request(`/templates/${templateId}/update`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    })
  }

  /**
   * Delete template
   */
  async deleteTemplate(templateId: string): Promise<void> {
    await backendApiClient.request(`/templates/${templateId}/delete`, {
      method: 'DELETE'
    })
  }

  /**
   * Clone template
   */
  async cloneTemplate(templateId: string, name: string): Promise<Template> {
    return await backendApiClient.request(`/templates/${templateId}/clone`, {
      method: 'POST',
      body: JSON.stringify({ name })
    })
  }

  /**
   * Publish template
   */
  async publishTemplate(templateId: string): Promise<Template> {
    return await backendApiClient.request(`/templates/${templateId}/publish`, {
      method: 'POST'
    })
  }

  /**
   * Unpublish template
   */
  async unpublishTemplate(templateId: string): Promise<Template> {
    return await backendApiClient.request(`/templates/${templateId}/unpublish`, {
      method: 'POST'
    })
  }

  /**
   * Get template categories
   */
  async getCategories(): Promise<TemplateCategory[]> {
    return await backendApiClient.request('/templates/categories')
  }

  /**
   * Create template category
   */
  async createCategory(categoryData: Omit<TemplateCategory, 'id' | 'templateCount'>): Promise<TemplateCategory> {
    return await backendApiClient.request('/templates/categories', {
      method: 'POST',
      body: JSON.stringify(categoryData)
    })
  }

  /**
   * Search templates
   */
  async searchTemplates(query: TemplateSearchQuery): Promise<PaginatedResponse<Template>> {
    return await backendApiClient.request('/templates/search', {
      method: 'POST',
      body: JSON.stringify(query)
    })
  }

  /**
   * Get public templates
   */
  async getPublicTemplates(): Promise<Template[]> {
    return await backendApiClient.request('/templates/public')
  }

  /**
   * Get featured templates
   */
  async getFeaturedTemplates(): Promise<Template[]> {
    return await backendApiClient.request('/templates/featured')
  }

  /**
   * Get popular templates
   */
  async getPopularTemplates(limit?: number): Promise<Template[]> {
    return await backendApiClient.request('/templates/popular', {
      params: { limit }
    })
  }

  /**
   * Create template instance
   */
  async createInstance(
    templateId: string,
    data: Record<string, any>,
    name?: string
  ): Promise<TemplateInstance> {
    return await backendApiClient.request(`/templates/${templateId}/instances`, {
      method: 'POST',
      body: JSON.stringify({ data, name })
    })
  }

  /**
   * Get template instances
   */
  async getInstances(templateId: string): Promise<TemplateInstance[]> {
    return await backendApiClient.request(`/templates/${templateId}/instances`)
  }

  /**
   * Get template instance
   */
  async getInstance(templateId: string, instanceId: string): Promise<TemplateInstance> {
    return await backendApiClient.request(`/templates/${templateId}/instances/${instanceId}`)
  }

  /**
   * Update template instance
   */
  async updateInstance(
    templateId: string,
    instanceId: string,
    data: Record<string, any>
  ): Promise<TemplateInstance> {
    return await backendApiClient.request(`/templates/${templateId}/instances/${instanceId}`, {
      method: 'PUT',
      body: JSON.stringify({ data })
    })
  }

  /**
   * Delete template instance
   */
  async deleteInstance(templateId: string, instanceId: string): Promise<void> {
    await backendApiClient.request(`/templates/${templateId}/instances/${instanceId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Get template analytics
   */
  async getAnalytics(templateId: string): Promise<TemplateAnalytics> {
    return await backendApiClient.request(`/templates/${templateId}/analytics`)
  }

  /**
   * Preview template
   */
  async previewTemplate(templateId: string, data?: Record<string, any>): Promise<TemplatePreviewResult> {
    return await backendApiClient.request(`/templates/${templateId}/preview`, {
      method: 'POST',
      body: JSON.stringify({ data })
    })
  }

  /**
   * Export template
   */
  async exportTemplate(
    templateId: string,
    format: 'json' | 'pdf' | 'html'
  ): Promise<{ downloadUrl: string }> {
    return await backendApiClient.request(`/templates/${templateId}/export`, {
      method: 'POST',
      body: JSON.stringify({ format })
    })
  }

  /**
   * Validate template data
   */
  async validateData(templateId: string, data: Record<string, any>): Promise<{
    isValid: boolean
    errors: { field: string; message: string }[]
  }> {
    return await backendApiClient.request(`/templates/${templateId}/validate`, {
      method: 'POST',
      body: JSON.stringify({ data })
    })
  }

  /**
   * Bulk delete templates
   */
  async bulkDelete(templateIds: string[]): Promise<{ success: string[]; failed: string[] }> {
    return await backendApiClient.request('/templates/bulk/delete', {
      method: 'POST',
      body: JSON.stringify({ templateIds })
    })
  }

  /**
   * Bulk publish templates
   */
  async bulkPublish(templateIds: string[]): Promise<{ success: string[]; failed: string[] }> {
    return await backendApiClient.request('/templates/bulk/publish', {
      method: 'POST',
      body: JSON.stringify({ templateIds })
    })
  }
}

// Export singleton instance
export const templateService = new TemplateService()

// Export additional types and interfaces for convenience
export type { Template } from '@/types/backend'
export { TemplateStatus, TemplateType } from '@/types/backend'

export type CreateTemplateParams = CreateTemplateRequest

export type UpdateTemplateParams = UpdateTemplateRequest
