import { useEffect, useRef } from 'react'

/**
 * Interval Hook
 * Manages setInterval with automatic cleanup
 */

export function useInterval(
  callback: () => void,
  delay: number | null,
  options: {
    immediate?: boolean
    enabled?: boolean
  } = {}
): void {
  const { immediate = false, enabled = true } = options
  const savedCallback = useRef<() => void>()

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  // Set up the interval
  useEffect(() => {
    if (!enabled || delay === null) return

    const tick = () => {
      savedCallback.current?.()
    }

    // Execute immediately if requested
    if (immediate) {
      tick()
    }

    const id = setInterval(tick, delay)
    return () => clearInterval(id)
  }, [delay, immediate, enabled])
}

/**
 * Advanced interval hook with controls
 */
export function useIntervalControls(
  callback: () => void,
  delay: number | null
) {
  const savedCallback = useRef<() => void>()
  const intervalRef = useRef<NodeJS.Timeout>()
  const isRunningRef = useRef(false)

  // Remember the latest callback
  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  const start = () => {
    if (delay === null || isRunningRef.current) return

    const tick = () => {
      savedCallback.current?.()
    }

    intervalRef.current = setInterval(tick, delay)
    isRunningRef.current = true
  }

  const stop = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = undefined
    }
    isRunningRef.current = false
  }

  const toggle = () => {
    if (isRunningRef.current) {
      stop()
    } else {
      start()
    }
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stop()
    }
  }, [])

  return {
    start,
    stop,
    toggle,
    isRunning: isRunningRef.current,
  }
}
