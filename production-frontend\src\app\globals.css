@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 224 71.4% 4.1%;

    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;

    --primary: 142 76% 36%;
    --primary-foreground: 144 70% 98%;

    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 142 76% 36%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 144 40% 6%;
    --foreground: 144 70% 98%;

    --card: 144 40% 8%;
    --card-foreground: 144 70% 98%;

    --popover: 144 40% 8%;
    --popover-foreground: 144 70% 98%;

    --primary: 142 76% 36%;
    --primary-foreground: 144 70% 98%;

    --secondary: 144 30% 14%;
    --secondary-foreground: 144 70% 98%;

    --muted: 144 30% 14%;
    --muted-foreground: 144 10% 80%;

    --accent: 144 30% 14%;
    --accent-foreground: 144 70% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 144 70% 98%;

    --border: 144 30% 14%;
    --input: 144 30% 14%;
    --ring: 142 76% 36%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* App theme - used for the main application */
  .app-theme {
    --background: 255, 255, 255;
    --foreground: 10, 10, 10;

    --card: 255, 255, 255;
    --card-foreground: 10, 10, 10;

    --popover: 255, 255, 255;
    --popover-foreground: 10, 10, 10;

    --primary: 47, 112, 193;
    --primary-foreground: 250, 250, 250;

    --secondary: 240, 240, 240;
    --secondary-foreground: 10, 10, 10;

    --muted: 240, 240, 240;
    --muted-foreground: 115, 115, 115;

    --accent: 240, 240, 240;
    --accent-foreground: 10, 10, 10;

    --destructive: 239, 68, 68;
    --destructive-foreground: 250, 250, 250;

    --border: 226, 232, 240;
    --input: 226, 232, 240;
    --ring: 224, 242, 254;
  }

  .app-theme.dark {
    --background: 10, 10, 10;
    --foreground: 250, 250, 250;

    --card: 30, 30, 30;
    --card-foreground: 250, 250, 250;

    --popover: 30, 30, 30;
    --popover-foreground: 250, 250, 250;

    --primary: 47, 112, 193;
    --primary-foreground: 250, 250, 250;

    --secondary: 30, 30, 30;
    --secondary-foreground: 250, 250, 250;

    --muted: 30, 30, 30;
    --muted-foreground: 150, 150, 150;

    --accent: 30, 30, 30;
    --accent-foreground: 250, 250, 250;

    --destructive: 239, 68, 68;
    --destructive-foreground: 250, 250, 250;

    --border: 50, 50, 50;
    --input: 50, 50, 50;
    --ring: 30, 64, 175;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors;
  }

  .btn-secondary {
    @apply px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-md p-6;
  }
}

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-custom::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-custom::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full;
  }

  .scrollbar-custom::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/40;
  }
}
