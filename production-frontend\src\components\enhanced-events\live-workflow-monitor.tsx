'use client'

/**
 * Live Workflow Monitor Component
 * Real-time workflow execution monitoring with Event Grid and Service Bus
 */

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Users,
  ArrowRight,
  Zap
} from 'lucide-react'
import { useWorkflowExecutionUpdates, useEventPublisher } from '@/hooks/useEnhancedEvents'
import { EnhancedEventType, ServiceBusQueue } from '@/services/enhanced-event-integration'

interface LiveWorkflowMonitorProps {
  workflowId: string
  executionId: string
  workflowName: string
}

export function LiveWorkflowMonitor({ workflowId, executionId, workflowName }: LiveWorkflowMonitorProps) {
  // Real-time workflow execution updates
  const { execution, currentStep, progress } = useWorkflowExecutionUpdates(executionId)
  
  // Event publisher for workflow actions
  const { publishEvent, sendMessage } = useEventPublisher()

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'running':
        return <Play className="h-5 w-5 text-blue-500" />
      case 'paused':
        return <Pause className="h-5 w-5 text-yellow-500" />
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'pending':
        return <Clock className="h-5 w-5 text-gray-500" />
      default:
        return <AlertTriangle className="h-5 w-5 text-orange-500" />
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'running':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'pending':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-orange-100 text-orange-800 border-orange-200'
    }
  }

  const handleApprove = async () => {
    try {
      await sendMessage(ServiceBusQueue.WORKFLOW_ORCHESTRATION, {
        action: 'approve',
        workflowId,
        executionId,
        stepId: currentStep,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to approve workflow step:', error)
    }
  }

  const handleReject = async () => {
    try {
      await sendMessage(ServiceBusQueue.WORKFLOW_ORCHESTRATION, {
        action: 'reject',
        workflowId,
        executionId,
        stepId: currentStep,
        reason: 'Rejected by user',
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to reject workflow step:', error)
    }
  }

  const handlePause = async () => {
    try {
      await publishEvent(EnhancedEventType.WORKFLOW_STEP_COMPLETED, {
        workflowId,
        executionId,
        stepId: currentStep,
        status: 'paused',
        progress,
        timestamp: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to pause workflow:', error)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon(execution?.status)}
            <span>{workflowName}</span>
          </div>
          <Badge className={getStatusColor(execution?.status)}>
            {execution?.status || 'Unknown'}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Execution Info */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Execution ID:</span>
            <p className="text-muted-foreground font-mono text-xs">{executionId}</p>
          </div>
          <div>
            <span className="font-medium">Current Step:</span>
            <p className="text-muted-foreground">{currentStep || 'Initializing...'}</p>
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Progress:</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Workflow Steps */}
        {execution?.steps && (
          <div className="space-y-2">
            <span className="text-sm font-medium">Workflow Steps:</span>
            <div className="space-y-1">
              {execution.steps.map((step: any, index: number) => (
                <div key={step.id} className="flex items-center gap-2 p-2 rounded-md bg-gray-50">
                  <div className="flex items-center gap-2 flex-1">
                    {step.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                    {step.status === 'running' && <Play className="h-4 w-4 text-blue-500" />}
                    {step.status === 'pending' && <Clock className="h-4 w-4 text-gray-400" />}
                    {step.status === 'failed' && <XCircle className="h-4 w-4 text-red-500" />}
                    
                    <span className="text-sm">{step.name}</span>
                  </div>
                  
                  {index < execution.steps.length - 1 && (
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {execution?.status === 'running' && (
          <div className="flex gap-2 pt-2 border-t">
            <Button size="sm" onClick={handleApprove} className="flex-1">
              <CheckCircle className="h-4 w-4 mr-1" />
              Approve
            </Button>
            <Button size="sm" variant="destructive" onClick={handleReject} className="flex-1">
              <XCircle className="h-4 w-4 mr-1" />
              Reject
            </Button>
            <Button size="sm" variant="outline" onClick={handlePause}>
              <Pause className="h-4 w-4 mr-1" />
              Pause
            </Button>
          </div>
        )}

        {/* Execution Metadata */}
        {execution && (
          <div className="text-xs text-muted-foreground border-t pt-2 space-y-1">
            {execution.startedAt && (
              <div>Started: {new Date(execution.startedAt).toLocaleString()}</div>
            )}
            {execution.completedAt && (
              <div>Completed: {new Date(execution.completedAt).toLocaleString()}</div>
            )}
            <div>Last updated: {new Date().toLocaleTimeString()}</div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Live Workflow Dashboard
export function LiveWorkflowDashboard() {
  const [workflows] = React.useState([
    { 
      workflowId: 'wf-1', 
      executionId: 'exec-1', 
      name: 'Document Approval Workflow' 
    },
    { 
      workflowId: 'wf-2', 
      executionId: 'exec-2', 
      name: 'Contract Review Process' 
    },
    { 
      workflowId: 'wf-3', 
      executionId: 'exec-3', 
      name: 'Invoice Processing Pipeline' 
    }
  ])

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Zap className="h-6 w-6 text-blue-500" />
        <h2 className="text-2xl font-bold">Live Workflow Monitor</h2>
      </div>
      <p className="text-muted-foreground">
        Real-time workflow execution monitoring with Event Grid and Service Bus integration
      </p>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {workflows.map((workflow) => (
          <LiveWorkflowMonitor
            key={workflow.executionId}
            workflowId={workflow.workflowId}
            executionId={workflow.executionId}
            workflowName={workflow.name}
          />
        ))}
      </div>
    </div>
  )
}
