/**
 * Projects Hook
 * Manages project operations and state using Zustand store
 */

import { useCallback } from 'react'
import { useToast } from '@/hooks/use-toast'
import {
  useProjectStore,
  useProjectLoading,
  useProjectError,
  useFetchProjects,
  useCreateProject as useCreateProjectStore
} from '@/stores/project-store'
import type { Project, ProjectVisibility, PaginatedResponse } from '@/types/backend'

export interface UseProjectsOptions {
  organizationId?: string
  visibility?: ProjectVisibility[]
  status?: string[]
  page?: number
  pageSize?: number
  search?: string
  enabled?: boolean
}

export interface CreateProjectData {
  name: string
  description?: string
  visibility?: 'organization' | 'private' | 'public'
  organizationId: string
  settings?: any
}

export interface UseProjectsResult {
  // State
  projects: Project[]
  loading: boolean // For backward compatibility
  isLoading: boolean
  error: Error | null
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }

  // Operations
  createProject: (data: CreateProjectData) => Promise<Project>
  isCreating: boolean // For backward compatibility
  refetch: () => Promise<void>
}

export function useProjects(options: UseProjectsOptions = {}): UseProjectsResult {
  const {
    organizationId,
    visibility,
    status,
    page = 1,
    pageSize = 20,
    search,
    enabled = true
  } = options
  const { toast } = useToast()

  // Get data from Zustand store
  const projectsData = useProjectStore((state) => state.projects)
  const isLoading = useProjectLoading()
  const error = useProjectError()
  const fetchProjects = useFetchProjects()

  // Filter projects based on options
  const filteredProjects = (projectsData || []).filter((project: any) => {
    if (organizationId && project.organizationId !== organizationId) return false
    if (visibility && !visibility.includes(project.visibility as ProjectVisibility)) return false
    if (status && !status.includes(project.status)) return false
    if (search && !project.name.toLowerCase().includes(search.toLowerCase())) return false
    return true
  })

  // Simulate pagination
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedProjects = filteredProjects.slice(startIndex, endIndex)

  const projectsResponse: PaginatedResponse<Project> = {
    data: paginatedProjects,
    pagination: {
      page,
      pageSize,
      total: filteredProjects.length,
      totalPages: Math.ceil(filteredProjects.length / pageSize),
      hasNext: endIndex < filteredProjects.length,
      hasPrevious: page > 1
    },
    success: true,
    timestamp: new Date().toISOString()
  }

  const refetch = useCallback(async () => {
    if (!enabled) return
    try {
      await fetchProjects()
    } catch (error: any) {
      toast({
        title: 'Error fetching projects',
        description: error.message || 'Failed to fetch projects',
        variant: 'destructive',
      })
    }
  }, [fetchProjects, enabled, toast])

  // Project mutations using Zustand store
  const createProject = useCreateProjectStore()

  const createMutation = {
    mutateAsync: useCallback(async (data: CreateProjectData) => {
      try {
        const newProject = await createProject(data)
        toast({
          title: 'Project created',
          description: `Project "${newProject.name}" has been created successfully.`,
        })
        return newProject
      } catch (error: any) {
        toast({
          title: 'Creation failed',
          description: error.message || 'Failed to create project',
          variant: 'destructive',
        })
        throw error
      }
    }, [createProject, toast]),
    isPending: isLoading,
    error
  }

  const loading = isLoading

  return {
    // State
    projects: projectsResponse?.data || [],
    loading, // For backward compatibility
    isLoading: loading,
    error: error ? new Error(error) : null,
    pagination: projectsResponse?.pagination,

    // Operations
    createProject: createMutation.mutateAsync,
    isCreating: createMutation.isPending, // For backward compatibility
    refetch
  }
}
