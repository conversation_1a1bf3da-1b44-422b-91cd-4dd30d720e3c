import React, { useState } from 'react';
import { Upload, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import SignatureCanvas from './SignatureCanvas';
import { useAuth } from '../../hooks/useAuth';
import { backendApiClient } from '../../services/backend-api-client';

interface SignatureUploadProps {
  onSignatureUploaded?: (signatureId: string) => void;
}

/**
 * Signature Upload Component
 * Allows users to upload or draw a signature and save it to their profile
 */
const SignatureUpload: React.FC<SignatureUploadProps> = ({
  onSignatureUploaded
}) => {
  const { user } = useAuth();
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [signaturePreview, setSignaturePreview] = useState<string | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [userSignatures, setUserSignatures] = useState<Array<{id: string, name: string, url: string}>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load user's existing signatures
  React.useEffect(() => {
    if (user) {
      loadUserSignatures();
    }
  }, [user]);

  const loadUserSignatures = async () => {
    try {
      setIsLoading(true);
      const response = await backendApiClient.request('/signatures');
      setUserSignatures(response.data.items || []);
    } catch (err) {
      console.error('Failed to load signatures:', err);
      setError('Failed to load your existing signatures');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset states
    setError(null);
    setSuccess(null);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please upload an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size should be less than 5MB');
      return;
    }

    // Show preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setSignaturePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!signaturePreview) return;

    try {
      setIsUploading(true);
      setError(null);

      // Convert base64 to blob
      const response = await fetch(signaturePreview);
      const blob = await response.blob();

      // Create form data
      const formData = new FormData();
      formData.append('signature', blob, 'signature.png');
      formData.append('name', 'My Signature');
      formData.append('description', 'Uploaded signature');

      // Upload to server
      // Convert to base64 for API
      const uploadResponse = await backendApiClient.request('/signatures/upload', {
        method: 'POST',
        body: JSON.stringify({
          signature: signaturePreview,
          name: 'My Signature',
          description: 'Uploaded signature'
        }),
      });

      // Handle success
      setSuccess('Signature uploaded successfully');
      setSignaturePreview(null);

      // Reload signatures
      await loadUserSignatures();

      // Notify parent component
      if (onSignatureUploaded) {
        onSignatureUploaded(uploadResponse.data.id);
      }
    } catch (err) {
      console.error('Upload failed:', err);
      setError('Failed to upload signature. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrawSignature = () => {
    setIsDrawing(true);
  };

  const handleSaveDrawnSignature = async (signatureData: string) => {
    setSignaturePreview(signatureData);
    setIsDrawing(false);
  };

  const handleCancelDrawing = () => {
    setIsDrawing(false);
  };

  const handleDeleteSignature = async (signatureId: string) => {
    try {
      setIsLoading(true);
      await backendApiClient.request(`/signatures/${signatureId}`, {
        method: 'DELETE'
      });

      // Reload signatures
      await loadUserSignatures();

      setSuccess('Signature deleted successfully');
    } catch (err) {
      console.error('Delete failed:', err);
      setError('Failed to delete signature');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          {success}
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Upload or Create Signature</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">

          {signaturePreview ? (
            <div className="text-center space-y-4">
              <p className="text-sm font-medium">Preview:</p>
              <img
                src={signaturePreview}
                alt="Signature Preview"
                className="max-w-full max-h-48 border rounded bg-white mx-auto"
              />

              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={() => setSignaturePreview(null)}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear
                </Button>

                <Button
                  onClick={handleUpload}
                  disabled={isUploading}
                  className="flex items-center gap-2"
                >
                  {isUploading ? (
                    <Spinner className="h-4 w-4" />
                  ) : (
                    <Upload className="h-4 w-4" />
                  )}
                  {isUploading ? 'Uploading...' : 'Upload Signature'}
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex gap-2 justify-center">
              <Button variant="outline" asChild>
                <label>
                  Upload Image
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                </label>
              </Button>

              <Button onClick={handleDrawSignature}>
                Draw Signature
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Existing signatures */}
      <Card>
        <CardHeader>
          <CardTitle>Your Signatures</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center p-8">
              <Spinner className="h-6 w-6" />
            </div>
          ) : userSignatures.length > 0 ? (
            <div className="space-y-4">
              {userSignatures.map(signature => (
                <div
                  key={signature.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <img
                    src={signature.url}
                    alt={signature.name}
                    className="h-16 max-w-48 object-contain bg-white rounded p-2"
                  />

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteSignature(signature.id)}
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-8">
              You don't have any saved signatures yet
            </p>
          )}
        </CardContent>
      </Card>

      {/* Signature drawing dialog */}
      <Dialog open={isDrawing}>
        <DialogHeader>
          <DialogTitle>Draw Your Signature</DialogTitle>
        </DialogHeader>
        <DialogContent>
          <SignatureCanvas
            onSave={handleSaveDrawnSignature}
            onCancel={handleCancelDrawing}
            width={500}
            height={200}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SignatureUpload;
