/**
 * Template Rendering Service
 * Handles template rendering and variable processing
 */

import { backendApiClient } from './backend-api-client'
import type { ID } from '../types'

export interface TemplateVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array'
  value?: any
  required?: boolean
  description?: string
  defaultValue?: any
  category?: string
}

export interface RenderTemplateRequest {
  templateId: ID
  variables: Record<string, any>
  format?: 'html' | 'pdf' | 'docx'
  options?: {
    includeStyles?: boolean
    pageSize?: 'A4' | 'Letter' | 'Legal'
    orientation?: 'portrait' | 'landscape'
    margins?: {
      top: number
      right: number
      bottom: number
      left: number
    }
  }
}

export interface RenderTemplateResult {
  content: string
  format: string
  size: number
  downloadUrl?: string
  previewUrl?: string
  variables: TemplateVariable[]
  metadata: {
    renderedAt: string
    templateVersion: number
    processingTime: number
  }
}

export interface TemplatePreviewRequest {
  templateId: ID
  variables?: Record<string, any>
}

export interface TemplatePreviewResult {
  html: string
  variables: TemplateVariable[]
  errors: string[]
  warnings: string[]
}

export interface ValidateTemplateRequest {
  templateId: ID
  variables: Record<string, any>
}

export interface ValidateTemplateResult {
  valid: boolean
  errors: string[]
  warnings: string[]
  missingVariables: string[]
  unusedVariables: string[]
}

class TemplateRenderingService {
  /**
   * Render a template with provided variables
   */
  async renderTemplate(request: RenderTemplateRequest): Promise<RenderTemplateResult> {
    return await backendApiClient.request('/templates/render', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Preview a template without full rendering
   */
  async previewTemplate(request: TemplatePreviewRequest): Promise<TemplatePreviewResult> {
    return await backendApiClient.request('/templates/preview', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Validate template variables
   */
  async validateTemplate(request: ValidateTemplateRequest): Promise<ValidateTemplateResult> {
    return await backendApiClient.request('/templates/validate', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Get template variables for a specific template
   */
  async getTemplateVariables(templateId?: ID): Promise<TemplateVariable[]> {
    if (templateId) {
      return await backendApiClient.request(`/templates/${templateId}/variables`)
    }

    // Return default/common template variables when no templateId is provided
    return [
      {
        name: 'user.name',
        type: 'string',
        description: 'Current user name',
        category: 'user',
        required: false,
        defaultValue: ''
      },
      {
        name: 'user.email',
        type: 'string',
        description: 'Current user email',
        category: 'user',
        required: false,
        defaultValue: ''
      },
      {
        name: 'organization.name',
        type: 'string',
        description: 'Organization name',
        category: 'organization',
        required: false,
        defaultValue: ''
      },
      {
        name: 'project.name',
        type: 'string',
        description: 'Project name',
        category: 'project',
        required: false,
        defaultValue: ''
      },
      {
        name: 'document.title',
        type: 'string',
        description: 'Document title',
        category: 'document',
        required: false,
        defaultValue: ''
      },
      {
        name: 'current.date',
        type: 'date',
        description: 'Current date',
        category: 'system',
        required: false,
        defaultValue: new Date().toISOString()
      }
    ]
  }

  /**
   * Extract variables from template content
   */
  async extractVariables(content: string): Promise<TemplateVariable[]> {
    return await backendApiClient.request('/templates/extract-variables', {
      method: 'POST',
      body: JSON.stringify({ content })
    })
  }

  /**
   * Render template to PDF
   */
  async renderToPdf(request: Omit<RenderTemplateRequest, 'format'>): Promise<Blob> {
    try {
      const response = await backendApiClient.request('/templates/render/pdf', {
        method: 'POST',
        body: JSON.stringify({ ...request, format: 'pdf' }),
        responseType: 'blob'
      })

      if (response instanceof Blob) {
        return response
      }

      return new Blob([response], { type: 'application/pdf' })
    } catch (error) {
      console.error('PDF rendering failed:', error)
      throw new Error(`Failed to render template to PDF: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Render template to DOCX
   */
  async renderToDocx(request: Omit<RenderTemplateRequest, 'format'>): Promise<Blob> {
    try {
      const response = await backendApiClient.request('/templates/render/docx', {
        method: 'POST',
        body: JSON.stringify({ ...request, format: 'docx' }),
        responseType: 'blob'
      })

      if (response instanceof Blob) {
        return response
      }

      return new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' })
    } catch (error) {
      console.error('DOCX rendering failed:', error)
      throw new Error(`Failed to render template to DOCX: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Batch render multiple templates
   */
  async batchRender(requests: RenderTemplateRequest[]): Promise<RenderTemplateResult[]> {
    return await backendApiClient.request('/templates/batch-render', {
      method: 'POST',
      body: JSON.stringify({ requests })
    })
  }

  /**
   * Get rendering history
   */
  async getRenderingHistory(templateId: ID, limit = 50): Promise<RenderTemplateResult[]> {
    return await backendApiClient.request(`/templates/${templateId}/render-history`, {
      method: 'GET',
      params: { limit: limit.toString() }
    })
  }

  /**
   * Cancel rendering job
   */
  async cancelRendering(jobId: ID): Promise<void> {
    await backendApiClient.request(`/templates/render/${jobId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Get rendering status
   */
  async getRenderingStatus(jobId: ID): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
    progress: number
    result?: RenderTemplateResult
    error?: string
  }> {
    return await backendApiClient.request(`/templates/render/${jobId}/status`)
  }

  /**
   * Render preview of template content
   */
  renderPreview(htmlContent: string): string {
    // Simple preview rendering - in production this might involve more complex processing
    return htmlContent
  }
}

export const templateRenderingService = new TemplateRenderingService()
export default templateRenderingService
