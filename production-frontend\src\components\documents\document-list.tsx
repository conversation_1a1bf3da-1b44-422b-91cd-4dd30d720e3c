"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
  FileText,
  Download,
  Share2,
  MoreHorizontal,
  Search,
  Grid,
  List,
  Calendar,
  User,
  Edit,
  RefreshCw,
  Trash2,
  Eye,
  Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Document, DocumentStatus } from '@/types/document';
import { cn } from '@/lib/utils';
import {
  useDocuments,
  useDocumentLoading,
  useDocumentError,
  useFetchDocuments,
  useSelectDocument,
  useDeleteDocument,
  useProcessingStatus
} from '@/stores/document-store';

interface DocumentListProps {
  projectId?: string;
  onDocumentClick?: (documentId: string) => void;
  onDownload?: (document: Document) => void;
  onShare?: (document: Document) => void;
  onEdit?: (document: Document) => void;
  viewMode?: 'grid' | 'list';
  showSearch?: boolean;
  showFilters?: boolean;
  showActions?: boolean;
  autoRefresh?: boolean;
}

export function DocumentList({
  projectId,
  onDocumentClick,
  onDownload,
  onShare,
  onEdit,
  viewMode = 'grid',
  showSearch = true,
  showFilters = true,
  showActions = true,
  autoRefresh = true
}: DocumentListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<DocumentStatus | 'all'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [currentViewMode, setCurrentViewMode] = useState(viewMode);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Store hooks
  const documents = useDocuments();
  const loading = useDocumentLoading();
  const error = useDocumentError();
  const fetchDocuments = useFetchDocuments();
  const selectDocument = useSelectDocument();
  const deleteDocument = useDeleteDocument();
  const processingStatus = useProcessingStatus();
  const { toast } = useToast();

  // Fetch documents on mount and when projectId changes
  useEffect(() => {
    const filters = projectId ? { projectId } : undefined;
    fetchDocuments(filters);
  }, [fetchDocuments, projectId]);

  // Auto-refresh documents
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      const filters = projectId ? { projectId } : undefined;
      fetchDocuments(filters);
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [fetchDocuments, projectId, autoRefresh]);

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      const filters = projectId ? { projectId } : undefined;
      await fetchDocuments(filters);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle document actions
  const handleDocumentClick = (documentId: string) => {
    if (onDocumentClick) {
      onDocumentClick(documentId);
    } else {
      const document = documents.find(doc => doc.id === documentId);
      if (document) {
        selectDocument(document);
      }
    }
  };

  const handleDelete = async (document: Document, event: React.MouseEvent) => {
    event.stopPropagation();

    if (confirm(`Are you sure you want to delete "${document.name}"?`)) {
      try {
        await deleteDocument(document.id);
        toast({
          title: "Document deleted",
          description: `"${document.name}" has been deleted successfully.`,
        });
      } catch (error) {
        toast({
          title: "Delete failed",
          description: "Failed to delete the document. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  // Filter and sort documents
  const filteredDocuments = documents
    .filter(doc => {
      const matchesSearch = doc.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           doc.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' || doc.status === statusFilter;
      const matchesProject = !projectId || doc.projectId === projectId;
      return matchesSearch && matchesStatus && matchesProject;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'size':
          return (b.size || 0) - (a.size || 0);
        case 'date':
        default:
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
      }
    });

  const getStatusColor = (status: DocumentStatus) => {
    switch (status) {
      case DocumentStatus.PROCESSED:
        return 'bg-green-100 text-green-800';
      case DocumentStatus.PROCESSING:
        return 'bg-blue-100 text-blue-800';
      case DocumentStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case DocumentStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const renderGridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {filteredDocuments.map((document) => {
        const documentProcessingStatus = processingStatus[document.id];
        const isProcessing = documentProcessingStatus?.status === 'running' || document.status === 'running';

        return (
          <Card
            key={document.id}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => handleDocumentClick(document.id)}
          >
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="relative">
                    <FileText className="h-8 w-8 text-blue-500" />
                    {isProcessing && (
                      <Loader2 className="h-3 w-3 animate-spin absolute -top-1 -right-1 text-blue-500" />
                    )}
                  </div>
                  <Badge className={cn('text-xs', getStatusColor(document.status))}>
                    {document.status}
                  </Badge>
                </div>

                <div>
                  <h3 className="font-medium text-sm truncate" title={document.name}>
                    {document.name}
                  </h3>
                  {document.description && (
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {document.description}
                    </p>
                  )}
                </div>

                <div className="space-y-1 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDistanceToNow(new Date(document.updatedAt), { addSuffix: true })}
                  </div>
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {document.uploadedBy || 'Unknown'}
                  </div>
                  <div>{formatFileSize(document.size)}</div>
                  {documentProcessingStatus && (
                    <div className="text-xs">
                      Progress: {documentProcessingStatus.progress}%
                    </div>
                  )}
                </div>

                {showActions && (
                  <div className="flex items-center justify-between pt-2">
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDocumentClick(document.id);
                        }}
                        className="h-6 w-6 p-0"
                        title="View document"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit?.(document as any);
                        }}
                        className="h-6 w-6 p-0"
                        title="Edit document"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDownload?.(document as any);
                        }}
                        className="h-6 w-6 p-0"
                        title="Download document"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onShare?.(document as any);
                        }}
                        className="h-6 w-6 p-0"
                        title="Share document"
                      >
                        <Share2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => handleDelete(document as any, e)}
                      className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      title="Delete document"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );

  const renderListView = () => (
    <div className="space-y-2">
      {filteredDocuments.map((document) => (
        <Card
          key={document.id}
          className="cursor-pointer hover:shadow-sm transition-shadow"
          onClick={() => onDocumentClick?.(document.id)}
        >
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <FileText className="h-6 w-6 text-blue-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm truncate">{document.name}</h3>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                    <span>{formatDistanceToNow(new Date(document.createdAt), { addSuffix: true })}</span>
                    <span>{document.createdBy || 'Unknown'}</span>
                    <span>{formatFileSize(document.size)}</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Badge className={cn('text-xs', getStatusColor(document.status))}>
                  {document.status}
                </Badge>

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit?.(document as any);
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDownload?.(document as any);
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onShare?.(document as any);
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Loading state for initial load
  if (loading && documents.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-1/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      {(showSearch || showFilters) && (
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex items-center gap-2 flex-1">
            {showSearch && (
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            )}

            {showFilters && (
              <>
                <Select value={statusFilter} onValueChange={(value: any) => setStatusFilter(value)}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="processed">Processed</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="size">Size</SelectItem>
                  </SelectContent>
                </Select>
              </>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            </Button>
            <Button
              variant={currentViewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrentViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={currentViewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setCurrentViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <div className="text-destructive">⚠️</div>
              <p className="text-sm text-destructive">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                className="ml-auto"
              >
                Retry
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Documents */}
      {filteredDocuments.length > 0 ? (
        currentViewMode === 'grid' ? renderGridView() : renderListView()
      ) : (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No documents found</h3>
          <p className="text-muted-foreground">
            {searchQuery || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Upload your first document to get started'
            }
          </p>
        </div>
      )}
    </div>
  );
}
