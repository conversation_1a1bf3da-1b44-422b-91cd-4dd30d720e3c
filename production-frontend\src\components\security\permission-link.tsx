"use client";

import { ReactNode } from "react";
import Link, { LinkProps } from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { PermissionAction, hasPermission, hasAllPermissions, hasAnyPermission } from "@/lib/permissions";
import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface PermissionLinkProps extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> {
  /**
   * Next.js Link props
   */
  href: LinkProps["href"];

  /**
   * Permission required to enable the link
   */
  permission?: PermissionAction;

  /**
   * Multiple permissions required (all must be present)
   */
  permissions?: PermissionAction[];

  /**
   * Multiple permissions where any one is sufficient
   */
  anyPermission?: PermissionAction[];

  /**
   * Content to render inside the link
   */
  children: ReactNode;

  /**
   * Whether to hide the link completely if user doesn't have permission
   */
  hideIfNoPermission?: boolean;

  /**
   * Tooltip text to show when link is disabled due to permissions
   */
  permissionTooltip?: string;

  /**
   * Organization ID for organization-specific permissions
   */
  organizationId?: string;

  /**
   * Project ID for project-specific permissions
   */
  projectId?: string;
}

/**
 * Link that is enabled/disabled based on user permissions
 */
export function PermissionLink({
  permission,
  permissions,
  anyPermission,
  children,
  hideIfNoPermission = false,
  permissionTooltip = "You don't have permission to access this page",
  organizationId,
  projectId,
  className,
  href,
  ...linkProps
}: PermissionLinkProps) {
  const { user } = useAuth();

  // Check if user has the required permission(s)
  const hasRequiredPermission = permission
    ? hasPermission(user, permission)
    : permissions
    ? hasAllPermissions(user, permissions)
    : anyPermission
    ? hasAnyPermission(user, anyPermission)
    : true;

  // If user doesn't have permission and we should hide the link
  if (!hasRequiredPermission && hideIfNoPermission) {
    return null;
  }

  // If user doesn't have permission, disable the link and add tooltip
  if (!hasRequiredPermission) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span
              className={cn(
                "cursor-not-allowed opacity-50",
                className
              )}
              {...linkProps}
            >
              {children}
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{permissionTooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // User has permission, render normal link
  return (
    <Link href={href} className={className} {...linkProps}>
      {children}
    </Link>
  );
}
