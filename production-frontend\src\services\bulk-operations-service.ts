/**
 * Bulk Operations Service
 * Service for managing bulk operations and batch processing
 */

import { backendApiClient } from './backend-api-client'

export interface BulkOperation {
  id: string
  operationType: 'BULK_UPLOAD' | 'BULK_DELETE' | 'BULK_MOVE' | 'BULK_COPY' | 'DATA_MIGRATION'
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  organizationId: string
  parameters: {
    bulkConfig?: BulkOperationConfig
    migrationConfig?: any
    encryptionConfig?: any
    searchConfig?: any
    fileProcessingConfig?: any
    customParameters?: Record<string, any>
  }
  progress: {
    totalItems: number
    processedItems: number
    failedItems: number
    percentage: number
    currentItem?: string
    estimatedTimeRemaining?: number
  }
  results?: {
    successCount: number
    failureCount: number
    warnings: string[]
    errors: string[]
    summary: Record<string, any>
  }
  createdAt: string
  startedAt?: string
  completedAt?: string
  error?: string
}

export interface BulkOperationConfig {
  batchSize: number
  maxConcurrency: number
  retryAttempts: number
  validateBeforeProcess: boolean
  parallelProcessing?: boolean
  generateThumbnails?: boolean
  extractText?: boolean
  autoProcess?: boolean
}

export interface FileUploadInfo {
  name: string
  size: number
  contentType: string
  content: string // Base64 encoded
  metadata?: Record<string, any>
}

export interface BulkUploadRequest {
  files: FileUploadInfo[]
  organizationId: string
  projectId?: string
  config: BulkOperationConfig
}

export interface BulkDeleteRequest {
  documentIds: string[]
  organizationId: string
  config?: {
    validateBeforeDelete: boolean
    createBackup: boolean
    batchSize: number
  }
}

export interface BulkMoveRequest {
  documentIds: string[]
  targetProjectId: string
  organizationId: string
  config?: {
    preserveMetadata: boolean
    updateReferences: boolean
    batchSize: number
  }
}

export interface BulkProcessRequest {
  documentIds: string[]
  processingType: 'OCR' | 'ANALYSIS' | 'EXTRACTION' | 'CLASSIFICATION'
  organizationId: string
  config?: {
    analysisOptions?: Record<string, any>
    extractionOptions?: Record<string, any>
    batchSize: number
    maxConcurrency: number
  }
}

export interface BulkOperationStats {
  totalOperations: number
  operationsByType: Record<string, number>
  operationsByStatus: Record<string, number>
  averageProcessingTime: number
  successRate: number
  totalItemsProcessed: number
  dailyStats: Array<{
    date: string
    operations: number
    itemsProcessed: number
    successRate: number
  }>
}

class BulkOperationsService {
  /**
   * Get all bulk operations
   */
  async getBulkOperations(params?: {
    organizationId?: string
    operationType?: string
    status?: string
    page?: number
    pageSize?: number
  }): Promise<BulkOperation[]> {
    return await backendApiClient.request<BulkOperation[]>('/storage/operations', {
      params
    })
  }

  /**
   * Get a specific bulk operation
   */
  async getBulkOperation(operationId: string): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>(`/storage/operations/${operationId}`)
  }

  /**
   * Perform bulk upload
   */
  async bulkUpload(data: BulkUploadRequest): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>('/storage/bulk/upload', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Perform bulk delete
   */
  async bulkDelete(data: BulkDeleteRequest): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>('/storage/bulk/delete', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Perform bulk move
   */
  async bulkMove(data: BulkMoveRequest): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>('/storage/bulk/move', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Perform bulk processing
   */
  async bulkProcess(data: BulkProcessRequest): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>('/storage/bulk/process', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Cancel a bulk operation
   */
  async cancelBulkOperation(operationId: string): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>(`/storage/operations/${operationId}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Retry a failed bulk operation
   */
  async retryBulkOperation(operationId: string): Promise<BulkOperation> {
    return await backendApiClient.request<BulkOperation>(`/storage/operations/${operationId}/retry`, {
      method: 'POST'
    })
  }

  /**
   * Get bulk operation statistics
   */
  async getBulkOperationStats(organizationId: string): Promise<BulkOperationStats> {
    return await backendApiClient.request<BulkOperationStats>('/storage/operations/stats', {
      params: { organizationId }
    })
  }

  /**
   * Get operation progress
   */
  async getOperationProgress(operationId: string): Promise<{
    percentage: number
    currentItem: string
    estimatedTimeRemaining: number
    throughput: number
    errorRate: number
  }> {
    return await backendApiClient.request(`/storage/operations/${operationId}/progress`)
  }

  /**
   * Get operation logs
   */
  async getOperationLogs(operationId: string, params?: {
    level?: 'info' | 'warning' | 'error'
    page?: number
    pageSize?: number
  }): Promise<Array<{
    timestamp: string
    level: string
    message: string
    details?: Record<string, any>
  }>> {
    return await backendApiClient.request(`/storage/operations/${operationId}/logs`, {
      params
    })
  }

  /**
   * Export operation results
   */
  async exportOperationResults(operationId: string, format: 'csv' | 'excel' | 'json'): Promise<{
    downloadUrl: string
    expiresAt: string
  }> {
    return await backendApiClient.request(`/storage/operations/${operationId}/export`, {
      method: 'POST',
      body: JSON.stringify({ format })
    })
  }

  /**
   * Validate bulk operation before execution
   */
  async validateBulkOperation(data: {
    operationType: string
    parameters: Record<string, any>
    organizationId: string
  }): Promise<{
    isValid: boolean
    warnings: string[]
    errors: string[]
    estimatedTime: number
    estimatedCost: number
  }> {
    return await backendApiClient.request('/storage/operations/validate', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Get operation templates
   */
  async getOperationTemplates(organizationId: string): Promise<Array<{
    id: string
    name: string
    description: string
    operationType: string
    defaultConfig: Record<string, any>
    isPublic: boolean
  }>> {
    return await backendApiClient.request('/storage/operations/templates', {
      params: { organizationId }
    })
  }

  /**
   * Create operation template
   */
  async createOperationTemplate(data: {
    name: string
    description: string
    operationType: string
    config: Record<string, any>
    organizationId: string
    isPublic?: boolean
  }): Promise<{
    id: string
    name: string
    description: string
    operationType: string
    config: Record<string, any>
  }> {
    return await backendApiClient.request('/storage/operations/templates', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Schedule bulk operation
   */
  async scheduleBulkOperation(data: {
    operationType: string
    parameters: Record<string, any>
    organizationId: string
    scheduledAt: string
    recurrence?: {
      type: 'daily' | 'weekly' | 'monthly'
      interval: number
      endDate?: string
    }
  }): Promise<{
    scheduleId: string
    nextRun: string
    status: 'active' | 'paused'
  }> {
    return await backendApiClient.request('/storage/operations/schedule', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Get scheduled operations
   */
  async getScheduledOperations(organizationId: string): Promise<Array<{
    scheduleId: string
    operationType: string
    nextRun: string
    lastRun?: string
    status: 'active' | 'paused'
    recurrence?: {
      type: string
      interval: number
    }
  }>> {
    return await backendApiClient.request('/storage/operations/scheduled', {
      params: { organizationId }
    })
  }

  /**
   * Pause/resume scheduled operation
   */
  async toggleScheduledOperation(scheduleId: string, action: 'pause' | 'resume'): Promise<{
    scheduleId: string
    status: 'active' | 'paused'
    nextRun?: string
  }> {
    return await backendApiClient.request(`/storage/operations/scheduled/${scheduleId}/${action}`, {
      method: 'POST'
    })
  }
}

export const bulkOperationsService = new BulkOperationsService()
