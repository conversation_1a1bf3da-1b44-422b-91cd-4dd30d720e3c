/**
 * AI Personalization Service
 * Handles personalized AI experiences, content recommendations, and user behavior tracking
 */

import { backendApiClient } from './backend-api-client'
import { memoryCache } from '../lib/cache'
import { performanceMonitor } from '../lib/performance'

export interface PersonalizationRecommendation {
  id: string
  userId: string
  type: 'content' | 'workflow' | 'collaboration' | 'ai_prompt' | 'document'
  title: string
  description: string
  confidence: number
  reasoning: string
  actionData: Record<string, any>
  priority: number
  expiresAt: string
  createdAt: string
}

export interface UserInteraction {
  interactionType: string
  context: Record<string, any>
  outcome: 'positive' | 'negative' | 'neutral'
  sessionId?: string
}

export interface BehaviorAnalysis {
  summary: string
  patterns: {
    mostActiveHours: Record<string, number>
    topActivities: Record<string, number>
    documentTypes: Record<string, number>
    collaborationFrequency: number
  }
  recommendations: Array<{
    type: string
    title: string
    description: string
    confidence: number
  }>
  timeRange: string
  analyzedAt: string
}

export interface PersonalizedPrompt {
  text: string
  category: string
  confidence: number
  context?: string
}

class PersonalizationService {
  private readonly CACHE_TTL = 1800 // 30 minutes
  private sessionId: string

  constructor() {
    this.sessionId = this.generateSessionId()
  }

  /**
   * Get personalized recommendations for the current user
   */
  async getRecommendations(
    type?: string,
    limit: number = 10
  ): Promise<PersonalizationRecommendation[]> {
    const cacheKey = `personalization:recommendations:${type || 'all'}:${limit}`
    
    try {
      // Check cache first
      const cached = memoryCache.get(cacheKey)
      if (cached) {
        return cached
      }

      const startTime = performance.now()

      const params: any = {}
      if (type) params.type = type
      params.limit = limit.toString()

      const recommendations = await backendApiClient.request<PersonalizationRecommendation[]>('/ai/personalization/recommendations', {
        method: 'GET',
        params
      })

      performanceMonitor.recordMetric(
        'getPersonalizationRecommendations',
        performance.now() - startTime
      )

      // Cache the results
      memoryCache.set(cacheKey, recommendations, this.CACHE_TTL)

      return recommendations
    } catch (error) {
      console.error('Error getting personalized recommendations:', error)
      return this.getFallbackRecommendations(type, limit)
    }
  }

  /**
   * Record user interaction for personalization learning
   */
  async recordInteraction(interaction: UserInteraction): Promise<void> {
    try {
      const interactionData = {
        ...interaction,
        sessionId: this.sessionId,
        timestamp: Date.now()
      }

      // Record immediately for real-time learning
      await backendApiClient.request<void>('/ai/personalization/interaction', {
        method: 'POST',
        body: JSON.stringify(interactionData)
      })

      // Also store locally for offline capability
      this.storeInteractionLocally(interactionData)

    } catch (error) {
      console.error('Error recording user interaction:', error)
      // Store locally if backend fails
      this.storeInteractionLocally(interaction)
    }
  }

  /**
   * Get personalized AI prompts based on context
   */
  async getPersonalizedPrompts(
    context?: string,
    documentId?: string
  ): Promise<PersonalizedPrompt[]> {
    const cacheKey = `personalization:prompts:${context || 'general'}:${documentId || 'none'}`
    
    try {
      // Check cache first
      const cached = memoryCache.get(cacheKey)
      if (cached) {
        return cached
      }

      const params: any = {}
      if (context) params.context = context
      if (documentId) params.documentId = documentId

      const response = await backendApiClient.request<{ prompts: string[] }>('/ai/personalization/prompts', {
        method: 'GET',
        params
      })

      const prompts = response.prompts.map((prompt: string, index: number) => ({
        text: prompt,
        category: context || 'general',
        confidence: 0.8 - (index * 0.1), // Decrease confidence for lower-ranked prompts
        context: context
      }))

      // Cache the results
      memoryCache.set(cacheKey, prompts, this.CACHE_TTL)

      return prompts
    } catch (error) {
      console.error('Error getting personalized prompts:', error)
      return this.getFallbackPrompts(context)
    }
  }

  /**
   * Get user behavior analysis
   */
  async getBehaviorAnalysis(timeRange: string = '30d'): Promise<BehaviorAnalysis | null> {
    const cacheKey = `personalization:behavior:${timeRange}`
    
    try {
      // Check cache first
      const cached = memoryCache.get(cacheKey)
      if (cached) {
        return cached
      }

      const analysis = await backendApiClient.request<BehaviorAnalysis>('/ai/personalization/behavior', {
        method: 'GET',
        params: { timeRange }
      })

      // Cache the results
      memoryCache.set(cacheKey, analysis, this.CACHE_TTL)

      return analysis
    } catch (error) {
      console.error('Error getting behavior analysis:', error)
      return null
    }
  }

  /**
   * Track feature usage for personalization
   */
  async trackFeatureUsage(feature: string, context: Record<string, any> = {}): Promise<void> {
    await this.recordInteraction({
      interactionType: 'feature_usage',
      context: {
        feature,
        ...context,
        timestamp: Date.now()
      },
      outcome: 'positive'
    })
  }

  /**
   * Track document interaction
   */
  async trackDocumentInteraction(
    documentId: string,
    action: string,
    outcome: 'positive' | 'negative' | 'neutral' = 'positive'
  ): Promise<void> {
    await this.recordInteraction({
      interactionType: 'document_interaction',
      context: {
        documentId,
        action,
        timestamp: Date.now()
      },
      outcome
    })
  }

  /**
   * Track AI interaction feedback
   */
  async trackAIFeedback(
    operationId: string,
    feedback: 'helpful' | 'not_helpful' | 'incorrect',
    details?: string
  ): Promise<void> {
    await this.recordInteraction({
      interactionType: 'ai_feedback',
      context: {
        operationId,
        feedback,
        details,
        timestamp: Date.now()
      },
      outcome: feedback === 'helpful' ? 'positive' : 'negative'
    })
  }

  /**
   * Get smart suggestions based on current context
   */
  async getSmartSuggestions(context: {
    currentPage?: string
    documentType?: string
    projectId?: string
    recentActions?: string[]
  }): Promise<PersonalizationRecommendation[]> {
    try {
      const recommendations = await this.getRecommendations('workflow', 5)
      
      // Filter and rank based on context
      return recommendations
        .filter(rec => this.isRelevantToContext(rec, context))
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 3)
    } catch (error) {
      console.error('Error getting smart suggestions:', error)
      return []
    }
  }

  /**
   * Clear personalization cache
   */
  clearCache(): void {
    // Clear all personalization-related cache entries
    const keys = ['personalization:recommendations', 'personalization:prompts', 'personalization:behavior']
    keys.forEach(key => {
      try {
        memoryCache.delete(key)
      } catch (error) {
        console.warn('Failed to clear cache key:', key)
      }
    })
  }

  /**
   * Get new session ID
   */
  refreshSession(): void {
    this.sessionId = this.generateSessionId()
  }

  // Private helper methods
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private storeInteractionLocally(interaction: any): void {
    try {
      const stored = localStorage.getItem('personalization_interactions') || '[]'
      const interactions = JSON.parse(stored)
      interactions.push(interaction)
      
      // Keep only last 100 interactions
      const recent = interactions.slice(-100)
      localStorage.setItem('personalization_interactions', JSON.stringify(recent))
    } catch (error) {
      console.warn('Failed to store interaction locally:', error)
    }
  }

  private getFallbackRecommendations(type?: string, limit: number = 10): PersonalizationRecommendation[] {
    const fallbacks: PersonalizationRecommendation[] = [
      {
        id: 'fallback_1',
        userId: 'current',
        type: 'ai_prompt',
        title: 'Try Document Analysis',
        description: 'Analyze your documents for insights and key information',
        confidence: 0.8,
        reasoning: 'Popular feature for new users',
        actionData: { feature: 'document_analysis' },
        priority: 1,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString()
      },
      {
        id: 'fallback_2',
        userId: 'current',
        type: 'workflow',
        title: 'Set Up Collaboration',
        description: 'Invite team members to collaborate on your projects',
        confidence: 0.7,
        reasoning: 'Improves productivity',
        actionData: { feature: 'collaboration' },
        priority: 2,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString()
      }
    ]

    return fallbacks
      .filter(rec => !type || rec.type === type)
      .slice(0, limit)
  }

  private getFallbackPrompts(context?: string): PersonalizedPrompt[] {
    const basePrompts = [
      'Summarize the key points in this document',
      'Extract actionable items from this content',
      'Identify potential improvements or optimizations',
      'Analyze the main themes and patterns',
      'Generate a brief overview of this information'
    ]

    return basePrompts.map((prompt, index) => ({
      text: prompt,
      category: context || 'general',
      confidence: 0.7 - (index * 0.1),
      context
    }))
  }

  private isRelevantToContext(
    recommendation: PersonalizationRecommendation,
    context: Record<string, any>
  ): boolean {
    // Simple relevance scoring based on context
    if (context.currentPage && recommendation.actionData.page === context.currentPage) {
      return true
    }
    
    if (context.documentType && recommendation.actionData.documentType === context.documentType) {
      return true
    }
    
    if (context.recentActions && recommendation.actionData.feature) {
      return context.recentActions.includes(recommendation.actionData.feature)
    }
    
    return recommendation.confidence > 0.6 // Default threshold
  }
}

// Export singleton instance
export const personalizationService = new PersonalizationService()
