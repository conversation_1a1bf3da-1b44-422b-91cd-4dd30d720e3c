"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, FolderKanban, Calendar, MoreHorizontal, Settings, Trash } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import { Team } from "@/types/team";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface TeamCardProps {
  team: Team;
  onDelete?: (teamId: string) => void;
}

export function TeamCard({ team, onDelete }: TeamCardProps) {
  const statusVariant = team.isActive ? "default" : "secondary";
  const statusLabel = team.isActive ? "Active" : "Inactive";

  const handleDelete = () => {
    if (onDele<PERSON>) {
      onD<PERSON><PERSON>(team.id);
    }
  };

  return (
    <Card className="w-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg">{team.name}</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={statusVariant}>{statusLabel}</Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href={`/organizations/${team.organizationId}/teams/${team.id}`}>
                    <Users className="mr-2 h-4 w-4" />
                    View Team
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/organizations/${team.organizationId}/teams/${team.id}/edit`}>
                    <Settings className="mr-2 h-4 w-4" />
                    Edit Team
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                  <Trash className="mr-2 h-4 w-4" />
                  Delete Team
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {team.description && (
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{team.description}</p>
        )}
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center gap-2 text-sm">
            <Users className="text-muted-foreground h-4 w-4" />
            <span>{team.memberIds?.length || 0} Members</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <FolderKanban className="text-muted-foreground h-4 w-4" />
            <span>{team.projectIds?.length || 0} Projects</span>
          </div>
          <div className="flex items-center gap-2 text-sm col-span-2">
            <Calendar className="text-muted-foreground h-4 w-4" />
            <span>Created {formatDistanceToNow(new Date(team.createdAt))} ago</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        <Button asChild variant="default" className="flex-1">
          <Link href={`/organizations/${team.organizationId}/teams/${team.id}`}>
            View
          </Link>
        </Button>
        <Button asChild variant="outline" className="flex-1">
          <Link href={`/organizations/${team.organizationId}/teams/${team.id}/members`}>
            Manage Members
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
