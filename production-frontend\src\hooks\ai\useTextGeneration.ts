/**
 * Text Generation Hooks
 * React hooks for AI-powered text generation using DeepSeek R1 and Llama
 */

import { useQuery, useMutation } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface TextGenerationRequest {
  prompt: string
  systemPrompt?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  context?: string[]
  contentType?: 'document' | 'email' | 'report' | 'summary' | 'analysis'
  outputFormat?: 'text' | 'markdown' | 'html' | 'json'
  useAdvancedAI?: boolean
  includeQualityMetrics?: boolean
  generateSuggestions?: boolean
  validateContent?: boolean
  organizationId: string
  projectId?: string
}

export interface TextGenerationResponse {
  id: string
  content: string
  reasoning?: string
  confidence: number
  tokensUsed: number
  model: string
  processingTime: number
  qualityMetrics?: {
    readability: number
    coherence: number
    relevance: number
    completeness: number
  }
  suggestions?: string[]
  metadata: Record<string, any>
  createdAt: string
}

export interface ContentGenerationOptions {
  useDeepSeekR1?: boolean // For reasoning-heavy content
  useLlama?: boolean // For creative content
  includeReasoning?: boolean
  maxRetries?: number
  priority?: 'low' | 'normal' | 'high'
}

export interface ReasoningRequest {
  prompt: string
  context?: string[]
  systemPrompt?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  organizationId: string
  projectId?: string
}

export interface ContentOptimizationRequest {
  content: string
  targetAudience?: string
  tone?: 'formal' | 'casual' | 'professional' | 'friendly'
  purpose?: 'inform' | 'persuade' | 'entertain' | 'instruct'
  maxLength?: number
  organizationId: string
}

/**
 * Hook to generate text content using Llama
 */
export function useGenerateContent() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: TextGenerationRequest) => {
      return await backendApiClient.request<TextGenerationResponse>('/ai/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt: data.prompt,
            contentType: data.contentType || 'document',
            outputFormat: data.outputFormat || 'text',
            options: {
              useAdvancedAI: data.useAdvancedAI,
              includeQualityMetrics: data.includeQualityMetrics,
              generateSuggestions: data.generateSuggestions,
              validateContent: data.validateContent,
              maxTokens: data.maxTokens,
              temperature: data.temperature
            }
          },
          organizationId: data.organizationId,
          projectId: data.projectId
        })
      })
    },
    onSuccess: (_result) => {
      toast({
        title: 'Content generated',
        description: 'AI content has been generated successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error generating content',
        description: 'There was a problem generating the content. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to perform AI reasoning using DeepSeek R1
 */
export function useAIReasoning() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ReasoningRequest) => {
      return await backendApiClient.request<TextGenerationResponse>('/ai/process', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt: data.prompt,
            contentType: 'analysis',
            outputFormat: 'text',
            options: {
              useAdvancedAI: true, // Use DeepSeek R1 for reasoning
              includeQualityMetrics: true,
              maxTokens: data.maxTokens || 4000,
              temperature: data.temperature || 0.7
            }
          },
          organizationId: data.organizationId,
          projectId: data.projectId
        })
      })
    },
    onSuccess: (_result) => {
      toast({
        title: 'Reasoning completed',
        description: 'AI reasoning analysis has been completed successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error in reasoning',
        description: 'There was a problem with the AI reasoning. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to optimize existing content
 */
export function useOptimizeContent() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: ContentOptimizationRequest) => {
      const prompt = `Optimize the following content for ${data.targetAudience || 'general audience'} with a ${data.tone || 'professional'} tone for the purpose of ${data.purpose || 'inform'}:

${data.content}

Please provide an optimized version that is clear, engaging, and appropriate for the target audience.`

      return await backendApiClient.request<TextGenerationResponse>('/ai/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt,
            contentType: 'document',
            outputFormat: 'text',
            options: {
              useAdvancedAI: true,
              includeQualityMetrics: true,
              generateSuggestions: true,
              validateContent: true,
              maxTokens: data.maxLength || 2000,
              temperature: 0.7
            }
          },
          organizationId: data.organizationId
        })
      })
    },
    onSuccess: (_result) => {
      toast({
        title: 'Content optimized',
        description: 'Your content has been optimized successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error optimizing content',
        description: 'There was a problem optimizing the content. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to generate multiple content variations
 */
export function useGenerateContentVariations() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      prompt, 
      variations = 3, 
      organizationId 
    }: { 
      prompt: string
      variations?: number
      organizationId: string 
    }) => {
      const requests = Array.from({ length: variations }, (_, i) => 
        backendApiClient.request<TextGenerationResponse>('/ai/content/generate', {
          method: 'POST',
          body: JSON.stringify({
            operationType: 'CONTENT_GENERATION',
            contentRequest: {
              prompt,
              contentType: 'document',
              outputFormat: 'text',
              options: {
                useAdvancedAI: false, // Use Llama for creative variations
                includeQualityMetrics: true,
                maxTokens: 1000,
                temperature: 0.8 + (i * 0.1) // Vary temperature for different results
              }
            },
            organizationId
          })
        })
      )

      return await Promise.all(requests)
    },
    onSuccess: (results) => {
      toast({
        title: 'Variations generated',
        description: `${results.length} content variations have been generated successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error generating variations',
        description: 'There was a problem generating content variations. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to summarize text content
 */
export function useSummarizeText() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      text, 
      maxLength = 200, 
      style = 'bullet-points',
      organizationId 
    }: { 
      text: string
      maxLength?: number
      style?: 'paragraph' | 'bullet-points' | 'key-points'
      organizationId: string 
    }) => {
      const prompt = `Summarize the following text in ${style} format, keeping it under ${maxLength} words:

${text}`

      return await backendApiClient.request<TextGenerationResponse>('/ai/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt,
            contentType: 'summary',
            outputFormat: 'text',
            options: {
              useAdvancedAI: true, // Use DeepSeek R1 for accurate summarization
              includeQualityMetrics: true,
              maxTokens: Math.min(maxLength * 2, 1000),
              temperature: 0.3
            }
          },
          organizationId
        })
      })
    },
    onSuccess: (_result) => {
      toast({
        title: 'Text summarized',
        description: 'The text has been summarized successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error summarizing text',
        description: 'There was a problem summarizing the text. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get text generation history
 */
export function useTextGenerationHistory(organizationId: string, params?: {
  projectId?: string
  contentType?: string
  page?: number
  pageSize?: number
}) {
  return useQuery({
    queryKey: ['text-generation-history', organizationId, params],
    queryFn: async () => {
      return await backendApiClient.request('/ai/content/history', {
        params: { organizationId, ...params }
      })
    },
    enabled: !!organizationId,
  })
}

/**
 * Hook to get content generation templates
 */
export function useContentTemplates(organizationId: string) {
  return useQuery({
    queryKey: ['content-templates', organizationId],
    queryFn: async () => {
      return await backendApiClient.request('/ai/content/templates', {
        params: { organizationId }
      })
    },
    enabled: !!organizationId,
  })
}
