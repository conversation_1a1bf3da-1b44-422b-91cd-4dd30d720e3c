"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { History, Download, Eye, ArrowLeft, ArrowRight, RotateCcw } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { useDocumentVersionsWithActions } from "@/hooks/documents/useDocumentVersions";

interface DocumentVersionsProps {
  documentId: string;
}

export function DocumentVersions({ documentId }: DocumentVersionsProps) {
  // Use the document versions hook
  const {
    versions,
    isLoading,
    selectedVersion,
    isRestoreDialogOpen,
    setIsRestoreDialogOpen,
    formatFileSize,
    handleViewVersion,
    handleDownloadVersion,
    handleRestoreVersion,
    confirmRestore,
    isRestoring
  } = useDocumentVersionsWithActions(documentId);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Version History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((_, i) => (
              <div key={i} className="flex items-center gap-4 p-4 border rounded-md">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-full" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Version History
        </CardTitle>
      </CardHeader>
      <CardContent>
        {versions.length === 0 ? (
          <div className="text-center py-8">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No version history available</p>
          </div>
        ) : (
          <div className="space-y-4">
            {versions.map((version) => (
              <div
                key={version.id}
                className={`flex flex-col md:flex-row md:items-center gap-4 p-4 border rounded-md ${
                  (version as any).isCurrent ? "bg-primary/5 border-primary/20" : ""
                }`}
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium">Version {version.version}</h3>
                    {(version as any).isCurrent && (
                      <Badge variant="outline" className="text-primary border-primary">Current</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {format(new Date(version.createdAt), "PPP 'at' p")} by {
                      typeof version.createdBy === 'string'
                        ? version.createdBy
                        : (version.createdBy as any)?.name || 'Unknown'
                    }
                  </p>
                  <p className="text-sm">
                    {typeof version.changes === 'string'
                      ? version.changes
                      : version.changes?.summary || 'No changes recorded'
                    }
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Size: {formatFileSize(version.size)}
                  </p>
                </div>
                <div className="flex gap-2 self-end md:self-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewVersion(version)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDownloadVersion(version)}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                  {!(version as any).isCurrent && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRestoreVersion(version)}
                    >
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Restore
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Version comparison navigation */}
        {versions.length > 1 && (
          <div className="flex justify-center mt-6">
            <div className="flex items-center gap-2 border rounded-md p-2">
              <Button variant="ghost" size="sm" disabled>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              <div className="px-2 text-sm">
                Compare versions
              </div>
              <Button variant="ghost" size="sm" disabled>
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        )}

        {/* Restore Confirmation Dialog */}
        <Dialog open={isRestoreDialogOpen} onOpenChange={setIsRestoreDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Restore Version</DialogTitle>
              <DialogDescription>
                Are you sure you want to restore the document to version {selectedVersion?.version}?
                This will create a new version based on the selected version.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsRestoreDialogOpen(false)}
                disabled={isRestoring}
              >
                Cancel
              </Button>
              <Button
                onClick={confirmRestore}
                disabled={isRestoring}
              >
                {isRestoring ? "Restoring..." : "Restore"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
