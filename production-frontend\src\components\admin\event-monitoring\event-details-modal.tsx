/**
 * Event Details Modal
 * Displays detailed information about an event
 */
import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { adminService } from '@/services';
import { JsonView } from '@/components/common/json-view';

// Event status badge component
const EventStatusBadge = ({ status }: { status: string }) => {
  const variant =
    status === 'success' ? 'success' :
    status === 'failed' ? 'destructive' :
    status === 'processing' ? 'default' :
    status === 'retrying' ? 'warning' : 'secondary';

  return (
    <Badge variant={variant}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

// Event details modal props
interface EventDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  eventId: string | null;
  isDeadLetterQueueItem?: boolean;
  onRetry?: (eventId: string) => Promise<boolean>;
}

// Event details modal component
export function EventDetailsModal({
  isOpen,
  onClose,
  eventId,
  isDeadLetterQueueItem = false,
  onRetry
}: EventDetailsModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [activeTab, setActiveTab] = useState('details');
  const [event, setEvent] = useState<any | null>(null);

  // Load event details when modal opens
  useEffect(() => {
    if (isOpen && eventId) {
      loadEventDetails();
    } else {
      setEvent(null);
    }
  }, [isOpen, eventId]);

  // Load event details
  const loadEventDetails = async () => {
    if (!eventId) return;

    try {
      setIsLoading(true);

      if (isDeadLetterQueueItem) {
        const data = await adminService.getDeadLetterQueueItemDetails(eventId);
        setEvent(data);
      } else {
        const data = await adminService.getEventDetails(eventId);
        setEvent(data);
      }
    } catch (error) {
      toast({
        title: 'Error loading event details',
        description: 'Failed to load event details. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Retry event
  const handleRetry = async () => {
    if (!eventId || !onRetry) return;

    try {
      setIsRetrying(true);
      await onRetry(eventId);
      toast({
        title: 'Event retried',
        description: 'The event has been successfully queued for retry.',
        variant: 'default'
      });
    } catch (error) {
      toast({
        title: 'Error retrying event',
        description: 'Failed to retry the event. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsRetrying(false);
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return format(new Date(timestamp), 'MMM d, yyyy HH:mm:ss.SSS');
    } catch (error) {
      return timestamp;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {isLoading ? (
              <Skeleton className="h-8 w-64" />
            ) : (
              <>
                {isDeadLetterQueueItem ? 'Dead Letter Queue Item' : 'Event'} Details
                {event && (
                  <span className="ml-2">
                    <EventStatusBadge status={event.status || 'failed'} />
                  </span>
                )}
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {isLoading ? (
              <Skeleton className="h-4 w-full mt-2" />
            ) : event ? (
              <>
                {event.eventType} - {event.id}
              </>
            ) : (
              'Loading event details...'
            )}
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4 py-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        ) : event ? (
          <>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <TabsList className="grid grid-cols-3">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="data">Data</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
              </TabsList>

              <ScrollArea className="flex-1 mt-4">
                <TabsContent value="details" className="space-y-4 p-1">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Event Type</h4>
                      <p>{event.eventType}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Status</h4>
                      <p><EventStatusBadge status={event.status || 'failed'} /></p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Event ID</h4>
                      <p className="text-sm font-mono">{event.id}</p>
                    </div>
                    {isDeadLetterQueueItem && event.originalEventId && (
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Original Event ID</h4>
                        <p className="text-sm font-mono">{event.originalEventId}</p>
                      </div>
                    )}
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Subject</h4>
                      <p>{event.subject}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Data Version</h4>
                      <p>{event.dataVersion}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Schema Version</h4>
                      <p>{event.schemaVersion}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-muted-foreground">Timestamp</h4>
                      <p>{formatTimestamp(event.eventTime)}</p>
                    </div>
                    {isDeadLetterQueueItem && event.failureTimestamp && (
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Failure Timestamp</h4>
                        <p>{formatTimestamp(event.failureTimestamp)}</p>
                      </div>
                    )}
                    {isDeadLetterQueueItem && event.processingAttempts && (
                      <div>
                        <h4 className="text-sm font-medium text-muted-foreground">Processing Attempts</h4>
                        <p>{event.processingAttempts}</p>
                      </div>
                    )}
                    {isDeadLetterQueueItem && event.failureReason && (
                      <div className="col-span-2">
                        <h4 className="text-sm font-medium text-muted-foreground">Failure Reason</h4>
                        <p className="text-sm font-mono whitespace-pre-wrap">{event.failureReason}</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="data" className="p-1">
                  <JsonView data={event.data} />
                </TabsContent>

                <TabsContent value="metadata" className="p-1">
                  <JsonView data={event.metadata} />
                </TabsContent>
              </ScrollArea>
            </Tabs>

            <DialogFooter className="mt-4 gap-2">
              {isDeadLetterQueueItem && onRetry && (
                <Button
                  onClick={handleRetry}
                  disabled={isRetrying}
                  className="mr-auto"
                >
                  {isRetrying ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4 mr-2" />
                  )}
                  Retry Event
                </Button>
              )}
              <Button
                variant="outline"
                onClick={loadEventDetails}
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh
              </Button>
              <Button variant="secondary" onClick={onClose}>
                Close
              </Button>
            </DialogFooter>
          </>
        ) : (
          <div className="py-8 text-center text-muted-foreground">
            No event details available
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
