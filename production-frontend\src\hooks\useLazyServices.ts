/**
 * Lazy Service Hooks - Production-Ready Service Loading
 * Provides React hooks for lazy loading services only when needed
 * Optimizes performance by preventing unnecessary service initialization
 */

import { useEffect, useState, useCallback } from 'react'
import { useConditionalService } from '@/services/lazy-service-manager'
import { hasServiceAccess } from '@/services/service-registry'
import { useAuth } from '@/hooks/useAuth'

/**
 * Hook for lazy loading collaboration services
 * Only loads when collaborative features are explicitly needed
 */
export function useCollaborationServices(enabled: boolean = false) {
  const { service: collaboration, isLoading, error } = useConditionalService(
    'collaboration',
    enabled
  )

  return {
    collaboration,
    isLoading,
    error,
    isAvailable: !!collaboration
  }
}

/**
 * Hook for lazy loading AI services
 * Only loads when AI features are used
 */
export function useAIServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('AI_FEATURES', userRoles) : false
  
  const { service: aiService, isLoading: aiLoading, error: aiError } = useConditionalService(
    'aiService',
    enabled && hasAccess
  )
  
  const { service: documentAnalysis, isLoading: analysisLoading, error: analysisError } = useConditionalService(
    'documentAnalysis',
    enabled && hasAccess && !!aiService
  )

  return {
    aiService,
    documentAnalysis,
    isLoading: aiLoading || analysisLoading,
    error: aiError || analysisError,
    hasAccess,
    isAvailable: !!aiService
  }
}

/**
 * Hook for lazy loading infrastructure monitoring services
 * Only for admin users and monitoring components
 */
export function useInfrastructureServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('INFRASTRUCTURE', userRoles) : false
  
  const { service: eventGrid, isLoading: eventGridLoading } = useConditionalService(
    'eventGrid',
    enabled && hasAccess
  )
  
  const { service: serviceBus, isLoading: serviceBusLoading } = useConditionalService(
    'serviceBus',
    enabled && hasAccess
  )
  
  const { service: monitoring, isLoading: monitoringLoading } = useConditionalService(
    'monitoring',
    enabled && hasAccess
  )

  return {
    eventGrid,
    serviceBus,
    monitoring,
    isLoading: eventGridLoading || serviceBusLoading || monitoringLoading,
    hasAccess,
    isAvailable: !!(eventGrid && serviceBus && monitoring)
  }
}

/**
 * Hook for lazy loading document processing services
 * Only loads when document processing is needed
 */
export function useDocumentProcessingServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('DOCUMENT_PROCESSING', userRoles) : false

  const { service: documentProcessor, isLoading: processorLoading } = useConditionalService(
    'documentProcessor',
    enabled && hasAccess
  )

  const { service: ocrService, isLoading: ocrLoading } = useConditionalService(
    'ocrService',
    enabled && hasAccess
  )

  return {
    documentProcessor,
    ocrService,
    isLoading: processorLoading || ocrLoading,
    hasAccess,
    isAvailable: !!(documentProcessor && ocrService)
  }
}

/**
 * Hook for lazy loading notification services
 * Loads based on user preferences and permissions
 */
export function useNotificationServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('NOTIFICATIONS', userRoles) : false

  const { service: notifications, isLoading: notificationsLoading } = useConditionalService(
    'notifications',
    enabled && hasAccess
  )

  const { service: pushNotifications, isLoading: pushLoading } = useConditionalService(
    'pushNotifications',
    enabled && hasAccess && !!notifications
  )

  return {
    notifications,
    pushNotifications,
    isLoading: notificationsLoading || pushLoading,
    hasAccess,
    isAvailable: !!notifications
  }
}

/**
 * Hook for lazy loading search services
 * Only loads when search functionality is used
 */
export function useSearchServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('SEARCH', userRoles) : false

  const { service: search, isLoading: searchLoading } = useConditionalService(
    'search',
    enabled && hasAccess
  )

  const { service: advancedSearch, isLoading: advancedLoading } = useConditionalService(
    'advancedSearch',
    enabled && hasAccess && !!search
  )

  return {
    search,
    advancedSearch,
    isLoading: searchLoading || advancedLoading,
    hasAccess,
    isAvailable: !!search
  }
}

/**
 * Hook for lazy loading analytics services
 * Only for users with analytics permissions
 */
export function useAnalyticsServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('ANALYTICS', userRoles) : false

  const { service: analytics, isLoading: analyticsLoading } = useConditionalService(
    'analytics',
    enabled && hasAccess
  )

  const { service: reporting, isLoading: reportingLoading } = useConditionalService(
    'reporting',
    enabled && hasAccess && !!analytics
  )

  return {
    analytics,
    reporting,
    isLoading: analyticsLoading || reportingLoading,
    hasAccess,
    isAvailable: !!analytics
  }
}

/**
 * Hook for lazy loading workflow services
 * Only loads when workflow features are used
 */
export function useWorkflowServices(enabled: boolean = false) {
  const { user } = useAuth()
  const userRoles = user?.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []
  const hasAccess = user ? hasServiceAccess('WORKFLOW', userRoles) : false

  const { service: workflow, isLoading: workflowLoading } = useConditionalService(
    'workflow',
    enabled && hasAccess
  )

  const { service: automation, isLoading: automationLoading } = useConditionalService(
    'automation',
    enabled && hasAccess && !!workflow
  )

  return {
    workflow,
    automation,
    isLoading: workflowLoading || automationLoading,
    hasAccess,
    isAvailable: !!workflow
  }
}

/**
 * Hook for conditional service loading based on feature flags
 */
export function useFeatureServices(features: {
  collaboration?: boolean
  ai?: boolean
  infrastructure?: boolean
  documentProcessing?: boolean
  notifications?: boolean
  search?: boolean
  analytics?: boolean
  workflow?: boolean
}) {
  const collaboration = useCollaborationServices(features.collaboration)
  const ai = useAIServices(features.ai)
  const infrastructure = useInfrastructureServices(features.infrastructure)
  const documentProcessing = useDocumentProcessingServices(features.documentProcessing)
  const notifications = useNotificationServices(features.notifications)
  const search = useSearchServices(features.search)
  const analytics = useAnalyticsServices(features.analytics)
  const workflow = useWorkflowServices(features.workflow)

  const isLoading = [
    collaboration.isLoading,
    ai.isLoading,
    infrastructure.isLoading,
    documentProcessing.isLoading,
    notifications.isLoading,
    search.isLoading,
    analytics.isLoading,
    workflow.isLoading
  ].some(Boolean)

  return {
    collaboration,
    ai,
    infrastructure,
    documentProcessing,
    notifications,
    search,
    analytics,
    workflow,
    isLoading
  }
}

/**
 * Hook for smart service preloading based on user behavior
 */
export function useSmartServicePreloading() {
  const { user } = useAuth()
  const [preloadedServices, setPreloadedServices] = useState<Set<string>>(new Set())

  const preloadService = useCallback(async (serviceName: string) => {
    if (preloadedServices.has(serviceName)) return

    try {
      // Preload service in the background
      const { lazyServiceManager } = await import('@/services/lazy-service-manager')
      await lazyServiceManager.getService(serviceName)
      
      setPreloadedServices(prev => new Set(prev).add(serviceName))
      console.info(`Service '${serviceName}' preloaded successfully`)
    } catch (error) {
      console.warn(`Failed to preload service '${serviceName}':`, error)
    }
  }, [preloadedServices])

  // Smart preloading based on user roles and common usage patterns
  useEffect(() => {
    if (!user) return

    const userRoles = user.roles?.map(role => typeof role === 'string' ? role : role.name || role.id) || []

    // Preload commonly used services based on user role
    if (userRoles.includes('admin')) {
      // Admins likely need infrastructure monitoring
      setTimeout(() => preloadService('monitoring'), 2000)
    }

    if (userRoles.includes('editor') || userRoles.includes('user')) {
      // Editors/users likely need document processing
      setTimeout(() => preloadService('documentProcessor'), 1000)
    }

    // Preload notifications for all authenticated users
    setTimeout(() => preloadService('notifications'), 500)

  }, [user, preloadService])

  return {
    preloadService,
    preloadedServices: Array.from(preloadedServices)
  }
}

/**
 * Hook for service health monitoring
 */
export function useServiceHealth() {
  const [serviceHealth, setServiceHealth] = useState<Record<string, boolean>>({})

  useEffect(() => {
    const checkServiceHealth = async () => {
      try {
        const { lazyServiceManager } = await import('@/services/lazy-service-manager')
        const activeServices = lazyServiceManager.getActiveServices()
        
        const health: Record<string, boolean> = {}
        for (const serviceName of activeServices) {
          const status = lazyServiceManager.getServiceStatus(serviceName)
          health[serviceName] = status.initialized
        }
        
        setServiceHealth(health)
      } catch (error) {
        console.error('Failed to check service health:', error)
      }
    }

    // Check health every 30 seconds
    const interval = setInterval(checkServiceHealth, 30000)
    checkServiceHealth() // Initial check

    return () => clearInterval(interval)
  }, [])

  return serviceHealth
}
