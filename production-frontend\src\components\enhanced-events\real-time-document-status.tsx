'use client'

/**
 * Real-time Document Status Component
 * Demonstrates enhanced Event Grid and Service Bus integration for document processing
 */

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Clock, AlertCircle, FileText, Users, MessageSquare } from 'lucide-react'
import { useDocumentStatusUpdates, useCollaborationUpdates } from '@/hooks/useEnhancedEvents'

interface RealTimeDocumentStatusProps {
  documentId: string
  documentName: string
}

export function RealTimeDocumentStatus({ documentId, documentName }: RealTimeDocumentStatusProps) {
  // Real-time document status updates via Event Grid
  const { status, progress, error } = useDocumentStatusUpdates(documentId)
  
  // Real-time collaboration updates via Service Bus
  const { participants, cursors, edits } = useCollaborationUpdates(documentId)

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'completed':
      case 'processed':
      case 'signed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'processing':
      case 'analyzing':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />
      case 'failed':
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
      case 'processed':
      case 'signed':
        return 'bg-green-100 text-green-800'
      case 'processing':
      case 'analyzing':
        return 'bg-blue-100 text-blue-800'
      case 'failed':
      case 'error':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          {getStatusIcon(status)}
          {documentName}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Document Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Status:</span>
          <Badge className={getStatusColor(status)}>
            {status || 'Unknown'}
          </Badge>
        </div>

        {/* Progress Bar (for processing operations) */}
        {(status === 'processing' || status === 'analyzing') && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Progress:</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-sm font-medium text-red-800">Error</span>
            </div>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        )}

        {/* Real-time Collaboration Info */}
        {participants.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Users className="h-4 w-4" />
              Active Collaborators ({participants.length})
            </div>
            
            <div className="flex flex-wrap gap-2">
              {participants.map((participant) => (
                <Badge key={participant.userId} variant="outline" className="text-xs">
                  {participant.userName}
                </Badge>
              ))}
            </div>

            {/* Live Cursors Indicator */}
            {Object.keys(cursors).length > 0 && (
              <div className="text-xs text-muted-foreground">
                {Object.keys(cursors).length} active cursor{Object.keys(cursors).length !== 1 ? 's' : ''}
              </div>
            )}
          </div>
        )}

        {/* Recent Edits */}
        {edits.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium">
              <MessageSquare className="h-4 w-4" />
              Recent Activity
            </div>
            
            <div className="space-y-1 max-h-20 overflow-y-auto">
              {edits.slice(-3).map((edit, index) => (
                <div key={index} className="text-xs text-muted-foreground p-2 bg-gray-50 rounded">
                  {edit.type}: {edit.description || 'Document modified'}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status Timestamps */}
        <div className="text-xs text-muted-foreground border-t pt-2">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  )
}

// Live Document Processing Dashboard
export function LiveDocumentProcessingDashboard() {
  const [documents] = React.useState([
    { id: 'doc-1', name: 'Contract_2024.pdf' },
    { id: 'doc-2', name: 'Invoice_March.pdf' },
    { id: 'doc-3', name: 'Report_Q1.docx' }
  ])

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Live Document Processing</h2>
      <p className="text-muted-foreground">
        Real-time updates powered by Event Grid and Service Bus integration
      </p>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {documents.map((doc) => (
          <RealTimeDocumentStatus
            key={doc.id}
            documentId={doc.id}
            documentName={doc.name}
          />
        ))}
      </div>
    </div>
  )
}
