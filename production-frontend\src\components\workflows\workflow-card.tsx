"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  GitBranch,
  Calendar,
  Clock,
  User,
  FileText,
  PlayCircle,
  PauseCircle
} from "lucide-react";
import { format } from "date-fns";
import { Workflow, WorkflowStatus, WorkflowStepStatus } from "@/types/workflow";
import { cn } from "@/lib/utils";

interface WorkflowCardProps {
  workflow: Workflow;
  className?: string;
  showActions?: boolean;
  onStart?: (workflow: Workflow) => void;
  onCancel?: (workflow: Workflow) => void;
}

export function WorkflowCard({
  workflow,
  className,
  showActions = true,
  onStart,
  onCancel
}: WorkflowCardProps) {
  // Get current step
  const currentStep = workflow.steps?.find(step => step.id === workflow.currentStepId);

  // Format status badge
  const getStatusBadge = (status: WorkflowStatus) => {
    switch (status) {
      case WorkflowStatus.DRAFT:
        return { variant: "outline" as const, label: "Draft" };
      case WorkflowStatus.ACTIVE:
        return { variant: "secondary" as const, label: "Active" };
      case WorkflowStatus.COMPLETED:
        return { variant: "success" as const, label: "Completed" };
      case WorkflowStatus.CANCELED:
        return { variant: "destructive" as const, label: "Canceled" };
      default:
        return { variant: "outline" as const, label: status };
    }
  };

  const statusBadge = getStatusBadge(workflow.status);

  // Calculate progress
  const totalSteps = workflow.steps?.length || 0;
  const completedSteps = workflow.steps?.filter(
    step => {
      // Check for completed status
      if (step.status === WorkflowStepStatus.COMPLETED) return true;

      // Handle "APPROVED" status which might come from the API but isn't in our enum
      const status = String(step.status).toUpperCase();
      return status === "APPROVED";
    }
  )?.length || 0;
  const progress = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

  // Handle start workflow
  const handleStart = () => {
    if (onStart) {
      onStart(workflow);
    }
  };

  // Handle cancel workflow
  const handleCancel = () => {
    if (onCancel) {
      onCancel(workflow);
    }
  };

  return (
    <Card className={cn("w-full hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg flex items-center gap-2">
            <GitBranch className="h-5 w-5 text-primary" />
            {workflow.name}
          </CardTitle>
          <Badge variant={statusBadge.variant}>{statusBadge.label}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {workflow.description && (
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{workflow.description}</p>
        )}

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 text-sm">
            <FileText size={14} className="text-muted-foreground" />
            <span>Document: {workflow.documentId ? "Attached" : "None"}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <User size={14} className="text-muted-foreground" />
            <span>Initiated by: {workflow.createdBy}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Clock size={14} className="text-muted-foreground" />
            <span>Progress: {progress}%</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Calendar size={14} className="text-muted-foreground" />
            <span>Created: {format(new Date(workflow.createdAt), 'MMM d, yyyy')}</span>
          </div>
        </div>

        {/* Current step */}
        {currentStep && (
          <div className="bg-muted/50 p-3 rounded-md">
            <div className="flex justify-between items-center mb-2">
              <h4 className="text-sm font-medium">Current Step: {currentStep.name}</h4>
              <Badge variant="outline" className="text-xs">
                {currentStep.status}
              </Badge>
            </div>
            {currentStep.description && (
              <p className="text-xs text-muted-foreground line-clamp-2">{currentStep.description}</p>
            )}
          </div>
        )}

        {/* Progress bar */}
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className={cn(
              "h-2 rounded-full",
              workflow.status === WorkflowStatus.COMPLETED ? "bg-success" :
              workflow.status === WorkflowStatus.CANCELED ? "bg-destructive" :
              "bg-primary"
            )}
            style={{ width: `${progress}%` }}
          />
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="flex gap-2">
          <Button asChild variant="default" className="flex-1">
            <Link href={`/workflows/${workflow.id}`}>
              <GitBranch className="mr-1 h-4 w-4" />
              View Details
            </Link>
          </Button>

          {workflow.status === WorkflowStatus.DRAFT && (
            <Button variant="outline" className="flex-1" onClick={handleStart}>
              <PlayCircle className="mr-1 h-4 w-4" />
              Start Workflow
            </Button>
          )}

          {workflow.status === WorkflowStatus.ACTIVE && (
            <Button variant="outline" className="flex-1" onClick={handleCancel}>
              <PauseCircle className="mr-1 h-4 w-4" />
              Cancel
            </Button>
          )}
        </CardFooter>
      )}
    </Card>
  );
}
