"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { Diff, ArrowLeft, SplitSquareVertical, AlignJustify, RefreshCw } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { optimizedDocumentService as documentService } from "@/services/optimized-document-service";
import { DocumentComparison, DocumentVersion } from "@/types/document";

interface DocumentComparisonProps {
  documentId: string;
  projectId: string;
  organizationId: string;
  sourceVersionId: string;
  targetVersionId: string;
  onBack?: () => void;
}

export function DocumentComparisonViewer({
  documentId,
  projectId,
  organizationId,
  sourceVersionId,
  targetVersionId,
  onBack
}: DocumentComparisonProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [comparison, setComparison] = useState<DocumentComparison | null>(null);
  const [sourceVersion, setSourceVersion] = useState<DocumentVersion | null>(null);
  const [targetVersion, setTargetVersion] = useState<DocumentVersion | null>(null);
  const [viewMode, setViewMode] = useState<"side-by-side" | "inline">("side-by-side");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch comparison data on mount
  useEffect(() => {
    fetchComparisonData();
  }, [documentId, sourceVersionId, targetVersionId, projectId, organizationId]);

  const fetchComparisonData = async () => {
    setIsLoading(true);
    try {
      // Fetch comparison data
      const comparisonData = await documentService.compareDocumentVersions(
        documentId,
        sourceVersionId,
        targetVersionId,
        projectId,
        organizationId
      );
      // Transform the service response to match the expected interface
      const transformedComparison = {
        ...comparisonData,
        differences: comparisonData.results?.differences?.map((diff: any) => ({
          ...diff,
          confidence: diff.confidence || 1.0
        })) || [],
        results: {
          ...comparisonData.results,
          differences: comparisonData.results?.differences?.map((diff: any) => ({
            ...diff,
            confidence: diff.confidence || 1.0
          })) || []
        }
      };
      setComparison(transformedComparison);

      // Fetch version details
      const versionsResponse = await documentService.getDocumentVersions(
        documentId,
        projectId,
        organizationId
      );

      const source = versionsResponse.versions.find(v => v.id === sourceVersionId) || null;
      const target = versionsResponse.versions.find(v => v.id === targetVersionId) || null;
      
      setSourceVersion(source);
      setTargetVersion(target);
    } catch (error) {
      console.error("Error fetching comparison data:", error);
      toast({
        title: "Error",
        description: "Failed to load document comparison. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchComparisonData();
    setIsRefreshing(false);
  };

  // Get difference type color
  const getDifferenceColor = (type: string) => {
    switch (type) {
      case 'added':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800';
      case 'removed':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800';
      case 'changed':
        return 'bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-300 border-amber-200 dark:border-amber-800';
      default:
        return '';
    }
  };

  // Render side-by-side comparison
  const renderSideBySide = () => {
    if (!comparison || !comparison.differences.length) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No differences found between these versions.</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="font-medium mb-2">Version {comparison.sourceVersionNumber}</h3>
          <div className="border rounded-md p-4 space-y-4">
            {comparison.differences.map((diff, index) => (
              <div key={index} className="space-y-2">
                {diff.lineNumber && (
                  <p className="text-xs text-muted-foreground">Line {diff.lineNumber}</p>
                )}
                {diff.type === 'removed' || diff.type === 'changed' ? (
                  <div className={`p-2 border rounded-md ${getDifferenceColor('removed')}`}>
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {diff.oldContent || diff.content}
                    </pre>
                  </div>
                ) : (
                  <div className="p-2 border rounded-md bg-gray-100 dark:bg-gray-800">
                    <pre className="whitespace-pre-wrap text-sm font-mono text-muted-foreground">
                      (No content)
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
        <div>
          <h3 className="font-medium mb-2">Version {comparison.targetVersionNumber}</h3>
          <div className="border rounded-md p-4 space-y-4">
            {comparison.differences.map((diff, index) => (
              <div key={index} className="space-y-2">
                {diff.lineNumber && (
                  <p className="text-xs text-muted-foreground">Line {diff.lineNumber}</p>
                )}
                {diff.type === 'added' || diff.type === 'changed' ? (
                  <div className={`p-2 border rounded-md ${getDifferenceColor('added')}`}>
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {diff.newContent || diff.content}
                    </pre>
                  </div>
                ) : (
                  <div className="p-2 border rounded-md bg-gray-100 dark:bg-gray-800">
                    <pre className="whitespace-pre-wrap text-sm font-mono text-muted-foreground">
                      (No content)
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Render inline comparison
  const renderInline = () => {
    if (!comparison || !comparison.differences.length) {
      return (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No differences found between these versions.</p>
        </div>
      );
    }

    return (
      <div className="border rounded-md p-4 space-y-4">
        {comparison.differences.map((diff, index) => (
          <div key={index} className="space-y-2">
            {diff.lineNumber && (
              <p className="text-xs text-muted-foreground">Line {diff.lineNumber}</p>
            )}
            <div className={`p-2 border rounded-md ${getDifferenceColor(diff.type)}`}>
              <div className="flex items-center gap-2 mb-1">
                <Badge variant="outline" className={getDifferenceColor(diff.type)}>
                  {diff.type === 'added' ? 'Added' : diff.type === 'removed' ? 'Removed' : 'Changed'}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {diff.path}
                </span>
              </div>
              {diff.type === 'changed' && (
                <>
                  <div className="mb-2">
                    <p className="text-xs font-medium mb-1">Previous:</p>
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {diff.oldContent}
                    </pre>
                  </div>
                  <div>
                    <p className="text-xs font-medium mb-1">Current:</p>
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {diff.newContent}
                    </pre>
                  </div>
                </>
              )}
              {diff.type !== 'changed' && (
                <pre className="whitespace-pre-wrap text-sm font-mono">
                  {diff.content}
                </pre>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Diff className="h-5 w-5" />
            Document Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-64 w-full" />
              <Skeleton className="h-64 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-start">
          <CardTitle className="flex items-center gap-2">
            <Diff className="h-5 w-5" />
            Document Comparison
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            {onBack && (
              <Button
                variant="outline"
                size="sm"
                onClick={onBack}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Versions
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col md:flex-row justify-between md:items-center gap-4 p-4 border rounded-md bg-muted/50">
            <div>
              <p className="text-sm font-medium">
                Comparing Version {sourceVersion?.versionNumber || comparison?.sourceVersionNumber} with Version {targetVersion?.versionNumber || comparison?.targetVersionNumber}
              </p>
              <p className="text-xs text-muted-foreground">
                {sourceVersion && format(new Date(sourceVersion.createdAt), "PPP")} → {targetVersion && format(new Date(targetVersion.createdAt), "PPP")}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "side-by-side" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("side-by-side")}
              >
                <SplitSquareVertical className="mr-2 h-4 w-4" />
                Side by Side
              </Button>
              <Button
                variant={viewMode === "inline" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("inline")}
              >
                <AlignJustify className="mr-2 h-4 w-4" />
                Inline
              </Button>
            </div>
          </div>

          {comparison && comparison.differences.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No differences found between these versions.</p>
            </div>
          ) : (
            <div className="mt-4">
              {viewMode === "side-by-side" ? renderSideBySide() : renderInline()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
