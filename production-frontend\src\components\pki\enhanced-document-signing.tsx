/**
 * Enhanced Document Signing Component
 * Supports both PKI digital signatures and visual signature placement
 */

import React, { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, 
  PenTool, 
  Shield, 
  Eye, 
  Download, 
  Upload,
  CheckCircle,
  AlertTriangle,
  Info,
  X
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Document {
  id: string
  name: string
  type: string
  size: number
  url: string
}

interface SignaturePosition {
  page: number
  x: number
  y: number
  width: number
  height: number
}

interface PKIProvider {
  id: string
  name: string
  type: 'cloud' | 'enterprise' | 'self-signed'
  complianceLevel: 'basic' | 'advanced' | 'qualified'
}

interface Certificate {
  id: string
  commonName: string
  issuer: string
  validFrom: Date
  validTo: Date
  status: 'active' | 'expired' | 'revoked'
  complianceLevel: 'basic' | 'advanced' | 'qualified'
}

interface EnhancedDocumentSigningProps {
  document: Document
  onSigningComplete?: (result: any) => void
  onCancel?: () => void
}

export function EnhancedDocumentSigning({ 
  document, 
  onSigningComplete, 
  onCancel 
}: EnhancedDocumentSigningProps) {
  const [signatureType, setSignatureType] = useState<'visual' | 'pki' | 'both'>('visual')
  const [selectedProvider, setSelectedProvider] = useState<string>('')
  const [selectedCertificate, setSelectedCertificate] = useState<string>('')
  const [signatureImage, setSignatureImage] = useState<File | null>(null)
  const [signaturePosition, setSignaturePosition] = useState<SignaturePosition | null>(null)
  const [isPositioning, setIsPositioning] = useState(false)
  const [isSigning, setIsSigning] = useState(false)
  const [signingProgress, setSigningProgress] = useState(0)
  const [showPreview, setShowPreview] = useState(false)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  // Helper function to convert file to base64
  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result.split(',')[1]) // Remove data:image/... prefix
      }
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  // Load PKI data from API
  const [providers, setProviders] = React.useState<PKIProvider[]>([])
  const [certificates, setCertificates] = React.useState<Certificate[]>([])
  const [isLoadingPKI, setIsLoadingPKI] = React.useState(true)

  React.useEffect(() => {
    const loadPKIData = async () => {
      try {
        const [providersResponse, certificatesResponse] = await Promise.all([
          fetch('/api/pki/providers'),
          fetch('/api/pki/certificates')
        ])

        if (providersResponse.ok) {
          const providersData = await providersResponse.json()
          setProviders(providersData.providers || [])
        }

        if (certificatesResponse.ok) {
          const certificatesData = await certificatesResponse.json()
          setCertificates(certificatesData.certificates || [])
        }
      } catch (error) {
        console.error('Failed to load PKI data:', error)
        toast({
          title: 'Failed to load PKI data',
          description: 'Unable to load PKI providers and certificates.',
          variant: 'destructive'
        })
      } finally {
        setIsLoadingPKI(false)
      }
    }

    loadPKIData()
  }, [])

  const handleSignatureImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.type.startsWith('image/')) {
        setSignatureImage(file)
        toast({
          title: 'Signature image uploaded',
          description: 'Your signature image has been uploaded successfully.'
        })
      } else {
        toast({
          title: 'Invalid file type',
          description: 'Please upload an image file (PNG, JPG, etc.).',
          variant: 'destructive'
        })
      }
    }
  }

  const handlePositionSignature = () => {
    setIsPositioning(true)
    // This would open a document viewer for positioning
    toast({
      title: 'Position your signature',
      description: 'Click on the document where you want to place your signature.'
    })
  }

  const handleSignDocument = async () => {
    try {
      setIsSigning(true)
      setSigningProgress(0)

      // Validate requirements
      if (signatureType === 'visual' || signatureType === 'both') {
        if (!signatureImage) {
          throw new Error('Signature image is required for visual signatures')
        }
        if (!signaturePosition) {
          throw new Error('Signature position is required for visual signatures')
        }
      }

      if (signatureType === 'pki' || signatureType === 'both') {
        if (!selectedProvider) {
          throw new Error('PKI provider is required for digital signatures')
        }
        if (!selectedCertificate) {
          throw new Error('Certificate is required for digital signatures')
        }
      }

      // Prepare signing request
      const signingRequest = {
        documentId: document.id,
        signatureType,
        provider: selectedProvider,
        certificateId: selectedCertificate,
        signaturePosition: signaturePosition,
        signatureImage: signatureImage ? await convertFileToBase64(signatureImage) : null,
        reason: 'Document signing',
        location: 'Digital Platform',
        contactInfo: '<EMAIL>'
      }

      // Update progress
      setSigningProgress(20)

      // Call the signing API
      const response = await fetch('/api/pki/sign-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(signingRequest)
      })

      setSigningProgress(60)

      if (!response.ok) {
        throw new Error(`Signing failed: ${response.statusText}`)
      }

      const result = await response.json()
      setSigningProgress(100)

      // Validate the response
      if (!result.signedDocumentId || !result.signatureId) {
        throw new Error('Invalid signing response: missing required fields')
      }

      toast({
        title: 'Document signed successfully',
        description: `Document has been signed with ${signatureType} signature.`
      })

      onSigningComplete?.(result)
    } catch (error) {
      toast({
        title: 'Signing failed',
        description: error instanceof Error ? error.message : 'An error occurred while signing the document.',
        variant: 'destructive'
      })
    } finally {
      setIsSigning(false)
      setSigningProgress(0)
    }
  }

  const getComplianceBadge = (level: string) => {
    const variants = {
      basic: 'secondary',
      advanced: 'default',
      qualified: 'default'
    } as const

    const colors = {
      basic: 'text-gray-600',
      advanced: 'text-blue-600',
      qualified: 'text-green-600'
    }

    return (
      <Badge variant={variants[level as keyof typeof variants]} className={colors[level as keyof typeof colors]}>
        {level.charAt(0).toUpperCase() + level.slice(1)}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Sign Document</h2>
          <p className="text-muted-foreground">
            Sign "{document.name}" with digital and/or visual signatures
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
            <Eye className="h-4 w-4 mr-2" />
            {showPreview ? 'Hide' : 'Show'} Preview
          </Button>
          {onCancel && (
            <Button variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Signing Configuration */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PenTool className="h-5 w-5" />
                Signature Type
              </CardTitle>
              <CardDescription>
                Choose how you want to sign this document
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant={signatureType === 'visual' ? 'default' : 'outline'}
                  onClick={() => setSignatureType('visual')}
                  className="flex flex-col items-center p-4 h-auto"
                >
                  <PenTool className="h-6 w-6 mb-2" />
                  <span className="text-sm">Visual Only</span>
                </Button>
                <Button
                  variant={signatureType === 'pki' ? 'default' : 'outline'}
                  onClick={() => setSignatureType('pki')}
                  className="flex flex-col items-center p-4 h-auto"
                >
                  <Shield className="h-6 w-6 mb-2" />
                  <span className="text-sm">PKI Only</span>
                </Button>
                <Button
                  variant={signatureType === 'both' ? 'default' : 'outline'}
                  onClick={() => setSignatureType('both')}
                  className="flex flex-col items-center p-4 h-auto"
                >
                  <div className="flex items-center gap-1 mb-2">
                    <PenTool className="h-5 w-5" />
                    <Shield className="h-5 w-5" />
                  </div>
                  <span className="text-sm">Both</span>
                </Button>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  {signatureType === 'visual' && 'Visual signatures provide document appearance but limited legal validity.'}
                  {signatureType === 'pki' && 'PKI signatures provide strong legal validity and non-repudiation.'}
                  {signatureType === 'both' && 'Combined signatures provide both visual appearance and legal validity.'}
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Visual Signature Configuration */}
          {(signatureType === 'visual' || signatureType === 'both') && (
            <Card>
              <CardHeader>
                <CardTitle>Visual Signature</CardTitle>
                <CardDescription>
                  Upload and position your signature image
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Signature Image</Label>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      Upload Image
                    </Button>
                    {signatureImage && (
                      <span className="text-sm text-muted-foreground">
                        {signatureImage.name}
                      </span>
                    )}
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleSignatureImageUpload}
                    className="hidden"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Position</Label>
                  <Button
                    variant="outline"
                    onClick={handlePositionSignature}
                    disabled={!signatureImage}
                    className="w-full"
                  >
                    {signaturePosition ? 'Reposition Signature' : 'Position Signature'}
                  </Button>
                  {signaturePosition && (
                    <p className="text-sm text-muted-foreground">
                      Page {signaturePosition.page}, Position ({signaturePosition.x}, {signaturePosition.y})
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* PKI Signature Configuration */}
          {(signatureType === 'pki' || signatureType === 'both') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  PKI Digital Signature
                </CardTitle>
                <CardDescription>
                  Select PKI provider and certificate
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>PKI Provider</Label>
                  <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select PKI provider" />
                    </SelectTrigger>
                    <SelectContent>
                      {providers.map((provider) => (
                        <SelectItem key={provider.id} value={provider.id}>
                          <div className="flex items-center justify-between w-full">
                            <span>{provider.name}</span>
                            {getComplianceBadge(provider.complianceLevel)}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Certificate</Label>
                  <Select 
                    value={selectedCertificate} 
                    onValueChange={setSelectedCertificate}
                    disabled={!selectedProvider}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select certificate" />
                    </SelectTrigger>
                    <SelectContent>
                      {certificates
                        .filter(cert => !selectedProvider || cert.issuer.includes(providers.find(p => p.id === selectedProvider)?.name || ''))
                        .map((cert) => (
                        <SelectItem key={cert.id} value={cert.id}>
                          <div className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{cert.commonName}</span>
                              {getComplianceBadge(cert.complianceLevel)}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Valid until {cert.validTo.toLocaleDateString()}
                            </p>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {selectedCertificate && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Certificate is valid and ready for signing
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Document Preview */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Preview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="aspect-[3/4] bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Document preview would appear here</p>
                  <p className="text-sm text-gray-400 mt-1">{document.name}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Signing Progress */}
          {isSigning && (
            <Card>
              <CardHeader>
                <CardTitle>Signing in Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <Progress value={signingProgress} className="mb-2" />
                <p className="text-sm text-muted-foreground">
                  {signingProgress < 100 ? 'Processing signature...' : 'Signature complete!'}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Sign Button */}
          <Button
            onClick={handleSignDocument}
            disabled={isSigning || (
              (signatureType === 'visual' || signatureType === 'both') && (!signatureImage || !signaturePosition)
            ) || (
              (signatureType === 'pki' || signatureType === 'both') && (!selectedProvider || !selectedCertificate)
            )}
            className="w-full"
            size="lg"
          >
            {isSigning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Signing Document...
              </>
            ) : (
              <>
                <PenTool className="h-4 w-4 mr-2" />
                Sign Document
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  )
}
