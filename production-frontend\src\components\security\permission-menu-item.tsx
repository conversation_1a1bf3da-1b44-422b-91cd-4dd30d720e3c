"use client";

import { ReactNode } from "react";
import { useAuth } from "@/hooks/useAuth";
import { PermissionAction, hasPermission, hasAllPermissions, hasAnyPermission } from "@/lib/permissions";
import {
  DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import { ComponentPropsWithoutRef } from "react";

// Define the correct type for DropdownMenuItem props
type DropdownMenuItemProps = ComponentPropsWithoutRef<typeof DropdownMenuItem> & {
  inset?: boolean;
};

interface PermissionMenuItemProps extends DropdownMenuItemProps {
  /**
   * Permission required to show the menu item
   */
  permission?: PermissionAction;

  /**
   * Multiple permissions required (all must be present)
   */
  permissions?: PermissionAction[];

  /**
   * Multiple permissions where any one is sufficient
   */
  anyPermission?: PermissionAction[];

  /**
   * Content to render inside the menu item
   */
  children: ReactNode;

  /**
   * Whether to hide the menu item completely if user doesn't have permission
   */
  hideIfNoPermission?: boolean;

  /**
   * Organization ID for organization-specific permissions
   */
  organizationId?: string;

  /**
   * Project ID for project-specific permissions
   */
  projectId?: string;
}

/**
 * Dropdown menu item that is shown/hidden based on user permissions
 */
export function PermissionMenuItem({
  permission,
  permissions,
  anyPermission,
  children,
  hideIfNoPermission = true, // Default to hiding for menu items
  organizationId,
  projectId,
  ...menuItemProps
}: PermissionMenuItemProps) {
  const { user } = useAuth();

  // Check if user has the required permission(s)
  const hasRequiredPermission = permission
    ? hasPermission(user, permission)
    : permissions
    ? hasAllPermissions(user, permissions)
    : anyPermission
    ? hasAnyPermission(user, anyPermission)
    : true;

  // If user doesn't have permission and we should hide the menu item
  if (!hasRequiredPermission && hideIfNoPermission) {
    return null;
  }

  // If user doesn't have permission, disable the menu item
  if (!hasRequiredPermission) {
    return (
      <DropdownMenuItem
        {...menuItemProps}
        disabled
      >
        {children}
      </DropdownMenuItem>
    );
  }

  // User has permission, render normal menu item
  return (
    <DropdownMenuItem {...menuItemProps}>
      {children}
    </DropdownMenuItem>
  );
}
