/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  poweredByHeader: false,
  compress: true,

  // Move serverComponentsExternalPackages to top level as serverExternalPackages
  serverExternalPackages: ['@azure/msal-browser'],

  // Enable experimental features for Next.js 15
  experimental: {
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },

  // Move turbo config to top level as turbopack (Turbopack is now stable)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '7071',
      },
    ],
  },
  // Add security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: `
              default-src 'self';
              script-src 'self' 'unsafe-inline' 'unsafe-eval' https://login.microsoftonline.com https://*.b2clogin.com;
              style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
              img-src 'self' data: https: blob:;
              font-src 'self' https://fonts.gstatic.com;
              connect-src 'self' https://hepzlogic.azurewebsites.net http://localhost:7071 https://login.microsoftonline.com https://*.b2clogin.com https://graph.microsoft.com https://hepztech.service.signalr.net wss: ws:;
              frame-src 'self' https://login.microsoftonline.com https://*.b2clogin.com;
              object-src 'none';
              base-uri 'self';
              form-action 'self' https://login.microsoftonline.com https://*.b2clogin.com;
              frame-ancestors 'none';
              block-all-mixed-content;
              upgrade-insecure-requests;
            `.replace(/\s+/g, ' ').trim()
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          }
        ]
      }
    ];
  },
  webpack: (config, { isServer }) => {
    // Add support for PDFs
    config.module.rules.push({
      test: /\.(pdf)$/i,
      type: 'asset/resource',
    });

    // Fix for PDF.js canvas dependency
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
      fs: false,
      net: false,
      tls: false,
      path: false,
      stream: false,
      util: false,
      crypto: false,
      os: false,
      url: false,
      assert: false,
      http: false,
      https: false,
      zlib: false,
      buffer: false,
    };

    // Ignore canvas module completely
    config.externals = config.externals || [];
    if (!isServer) {
      config.externals.push('canvas');
    }

    // Handle PDF.js worker
    config.resolve.alias = {
      ...config.resolve.alias,
      'pdfjs-dist/build/pdf.worker.js': 'pdfjs-dist/build/pdf.worker.min.js',
    };

    return config;
  },
}

module.exports = nextConfig
