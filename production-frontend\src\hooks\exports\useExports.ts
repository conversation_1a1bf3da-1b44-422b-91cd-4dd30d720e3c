/**
 * Exports Hook
 * Manages data export operations
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'

export interface ExportJob {
  id: ID
  type: 'documents' | 'projects' | 'templates' | 'workflows' | 'analytics' | 'users' | 'organizations'
  format: 'csv' | 'json' | 'xlsx' | 'pdf' | 'zip'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  fileName?: string
  fileSize?: number
  downloadUrl?: string
  createdAt: string
  completedAt?: string
  expiresAt?: string
  error?: string
  parameters: Record<string, any>
  createdBy: ID
}

export interface ExportRequest {
  type: ExportJob['type']
  format: ExportJob['format']
  parameters?: Record<string, any>
  filters?: Record<string, any>
  includeMetadata?: boolean
  compression?: boolean
  password?: string
}

export interface ExportTemplate {
  id: ID
  name: string
  description: string
  type: ExportJob['type']
  format: ExportJob['format']
  parameters: Record<string, any>
  isPublic: boolean
  usageCount: number
  createdBy: ID
  createdAt: string
}

export interface UseExportsResult {
  // State
  exports: ExportJob[]
  templates: ExportTemplate[]
  loading: boolean
  error: string | null
  
  // Export operations
  createExport: (request: ExportRequest) => Promise<ExportJob>
  cancelExport: (exportId: ID) => Promise<void>
  downloadExport: (exportId: ID) => Promise<void>
  deleteExport: (exportId: ID) => Promise<void>
  retryExport: (exportId: ID) => Promise<ExportJob>
  
  // Template operations
  createTemplate: (template: Omit<ExportTemplate, 'id' | 'createdAt' | 'usageCount'>) => Promise<ExportTemplate>
  updateTemplate: (templateId: ID, updates: Partial<ExportTemplate>) => Promise<void>
  deleteTemplate: (templateId: ID) => Promise<void>
  useTemplate: (templateId: ID, parameters?: Record<string, any>) => Promise<ExportJob>
  
  // Bulk operations
  bulkCancel: (exportIds: ID[]) => Promise<{ success: ID[]; failed: ID[] }>
  bulkDelete: (exportIds: ID[]) => Promise<{ success: ID[]; failed: ID[] }>
  bulkDownload: (exportIds: ID[]) => Promise<void>
  
  // Utilities
  getExportTypes: () => Array<{ value: string; label: string; formats: string[] }>
  getExportFormats: (type: string) => Array<{ value: string; label: string; description: string }>
  validateExportRequest: (request: ExportRequest) => { valid: boolean; errors: string[] }
  
  // Refresh
  refresh: () => Promise<void>
  refreshTemplates: () => Promise<void>
}

export function useExports(): UseExportsResult {
  const { toast } = useToast()
  
  const [exports, setExports] = useState<ExportJob[]>([])
  const [templates, setTemplates] = useState<ExportTemplate[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load exports
  const loadExports = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request<ExportJob[]>('/exports')
      setExports(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load exports'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Load templates
  const loadTemplates = useCallback(async () => {
    try {
      const response = await backendApiClient.request<ExportTemplate[]>('/exports/templates')
      setTemplates(response)
    } catch (err: any) {
      console.error('Failed to load export templates:', err)
    }
  }, [])

  // Create export
  const createExport = useCallback(async (request: ExportRequest): Promise<ExportJob> => {
    try {
      const response = await backendApiClient.request<ExportJob>('/exports', {
        method: 'POST',
        body: JSON.stringify(request)
      })
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Export started',
        description: 'Your export has been queued for processing.',
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create export'
      
      toast({
        type: 'error',
        title: 'Export failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadExports, toast])

  // Cancel export
  const cancelExport = useCallback(async (exportId: ID) => {
    try {
      await backendApiClient.request(`/exports/${exportId}/cancel`, {
        method: 'POST'
      })
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Export cancelled',
        description: 'Export has been cancelled successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to cancel export'
      
      toast({
        type: 'error',
        title: 'Cancel failed',
        description: errorMessage,
      })
    }
  }, [loadExports, toast])

  // Download export
  const downloadExport = useCallback(async (exportId: ID) => {
    try {
      const response = await backendApiClient.request(`/exports/${exportId}/download`, {
        method: 'GET'
      })
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      
      // Get filename from headers or use default
      const contentDisposition = response.headers['content-disposition']
      const filename = contentDisposition
        ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
        : `export-${exportId}.zip`
      
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      toast({
        type: 'success',
        title: 'Download started',
        description: 'Your export file is being downloaded.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to download export'
      
      toast({
        type: 'error',
        title: 'Download failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Delete export
  const deleteExport = useCallback(async (exportId: ID) => {
    try {
      await backendApiClient.request(`/exports/${exportId}`, {
        method: 'DELETE'
      })
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Export deleted',
        description: 'Export has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete export'
      
      toast({
        type: 'error',
        title: 'Delete failed',
        description: errorMessage,
      })
    }
  }, [loadExports, toast])

  // Retry export
  const retryExport = useCallback(async (exportId: ID): Promise<ExportJob> => {
    try {
      const response = await backendApiClient.request<ExportJob>(`/exports/${exportId}/retry`, {
        method: 'POST'
      })
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Export retried',
        description: 'Export has been queued for retry.',
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to retry export'
      
      toast({
        type: 'error',
        title: 'Retry failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadExports, toast])

  // Template operations
  const createTemplate = useCallback(async (template: Omit<ExportTemplate, 'id' | 'createdAt' | 'usageCount'>): Promise<ExportTemplate> => {
    try {
      const response = await backendApiClient.request<ExportTemplate>('/exports/templates', {
        method: 'POST',
        body: JSON.stringify(template)
      })
      await loadTemplates()
      
      toast({
        type: 'success',
        title: 'Template created',
        description: 'Export template has been created successfully.',
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create template'
      
      toast({
        type: 'error',
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadTemplates, toast])

  const updateTemplate = useCallback(async (templateId: ID, updates: Partial<ExportTemplate>) => {
    try {
      await backendApiClient.request(`/exports/templates/${templateId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      await loadTemplates()
      
      toast({
        type: 'success',
        title: 'Template updated',
        description: 'Export template has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update template'
      
      toast({
        type: 'error',
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [loadTemplates, toast])

  const deleteTemplate = useCallback(async (templateId: ID) => {
    try {
      await backendApiClient.request(`/exports/templates/${templateId}`, {
        method: 'DELETE'
      })
      await loadTemplates()
      
      toast({
        type: 'success',
        title: 'Template deleted',
        description: 'Export template has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete template'
      
      toast({
        type: 'error',
        title: 'Delete failed',
        description: errorMessage,
      })
    }
  }, [loadTemplates, toast])

  const useTemplate = useCallback(async (templateId: ID, parameters?: Record<string, any>): Promise<ExportJob> => {
    try {
      const response = await backendApiClient.request<ExportJob>(`/exports/templates/${templateId}/use`, {
        method: 'POST',
        body: JSON.stringify({ parameters })
      })
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Export started from template',
        description: 'Your export has been queued using the template.',
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to use template'
      
      toast({
        type: 'error',
        title: 'Template use failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadExports, toast])

  // Bulk operations
  const bulkCancel = useCallback(async (exportIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const response = await backendApiClient.request<{ success: ID[]; failed: ID[] }>('/exports/bulk/cancel', {
        method: 'POST',
        body: JSON.stringify({ exportIds })
      })
      
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Bulk cancellation completed',
        description: `${response.success.length} exports cancelled successfully.`,
      })

      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to cancel exports'
      
      toast({
        type: 'error',
        title: 'Bulk cancellation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadExports, toast])

  const bulkDelete = useCallback(async (exportIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const response = await backendApiClient.request<{ success: ID[]; failed: ID[] }>('/exports/bulk/delete', {
        method: 'POST',
        body: JSON.stringify({ exportIds })
      })
      
      await loadExports()
      
      toast({
        type: 'success',
        title: 'Bulk deletion completed',
        description: `${response.success.length} exports deleted successfully.`,
      })

      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete exports'
      
      toast({
        type: 'error',
        title: 'Bulk deletion failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadExports, toast])

  const bulkDownload = useCallback(async (exportIds: ID[]) => {
    try {
      const response = await backendApiClient.request('/exports/bulk/download', {
        method: 'POST',
        body: JSON.stringify({ exportIds })
      })

      // Create download link for zip file
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `bulk-exports-${Date.now()}.zip`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      toast({
        type: 'success',
        title: 'Bulk download started',
        description: 'Your exports are being downloaded as a zip file.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to download exports'
      
      toast({
        type: 'error',
        title: 'Bulk download failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Utilities
  const getExportTypes = useCallback(() => [
    { value: 'documents', label: 'Documents', formats: ['pdf', 'zip', 'json'] },
    { value: 'projects', label: 'Projects', formats: ['json', 'csv', 'xlsx'] },
    { value: 'templates', label: 'Templates', formats: ['json', 'zip'] },
    { value: 'workflows', label: 'Workflows', formats: ['json', 'csv'] },
    { value: 'analytics', label: 'Analytics', formats: ['csv', 'xlsx', 'json'] },
    { value: 'users', label: 'Users', formats: ['csv', 'xlsx', 'json'] },
    { value: 'organizations', label: 'Organizations', formats: ['csv', 'xlsx', 'json'] },
  ], [])

  const getExportFormats = useCallback((type: string) => {
    const formats = {
      csv: { value: 'csv', label: 'CSV', description: 'Comma-separated values file' },
      json: { value: 'json', label: 'JSON', description: 'JavaScript Object Notation file' },
      xlsx: { value: 'xlsx', label: 'Excel', description: 'Microsoft Excel spreadsheet' },
      pdf: { value: 'pdf', label: 'PDF', description: 'Portable Document Format' },
      zip: { value: 'zip', label: 'ZIP', description: 'Compressed archive file' },
    }
    
    const typeConfig = getExportTypes().find(t => t.value === type)
    return typeConfig ? typeConfig.formats.map(f => formats[f as keyof typeof formats]) : []
  }, [getExportTypes])

  const validateExportRequest = useCallback((request: ExportRequest): { valid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (!request.type) {
      errors.push('Export type is required')
    }
    
    if (!request.format) {
      errors.push('Export format is required')
    }
    
    const typeConfig = getExportTypes().find(t => t.value === request.type)
    if (typeConfig && !typeConfig.formats.includes(request.format)) {
      errors.push(`Format ${request.format} is not supported for type ${request.type}`)
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }, [getExportTypes])

  // Load data on mount
  useEffect(() => {
    loadExports()
    loadTemplates()
  }, [loadExports, loadTemplates])

  return {
    // State
    exports,
    templates,
    loading,
    error,

    // Export operations
    createExport,
    cancelExport,
    downloadExport,
    deleteExport,
    retryExport,

    // Template operations
    createTemplate,
    updateTemplate,
    deleteTemplate,
    useTemplate,

    // Bulk operations
    bulkCancel,
    bulkDelete,
    bulkDownload,

    // Utilities
    getExportTypes,
    getExportFormats,
    validateExportRequest,

    // Refresh
    refresh: loadExports,
    refreshTemplates: loadTemplates,
  }
}

// Individual hook for creating exports
export function useCreateExport() {
  const { toast } = useToast()

  return {
    mutate: async (data: any, options?: any) => {
      try {
        // Placeholder implementation
        toast({
          title: 'Export created',
          description: 'Export has been created successfully.',
        })
        if (options?.onSuccess) {
          options.onSuccess(data)
        }
      } catch (error) {
        toast({
          title: 'Error creating export',
          description: 'Failed to create export. Please try again.',
          variant: 'destructive',
        })
      }
    },
    isPending: false,
    error: null
  }
}

// Individual hook exports for specific operations
export function useDeleteExport() {
  const { toast } = useToast()

  return {
    mutate: async (exportId: string) => {
      try {
        // Placeholder implementation
        console.log('Deleting export:', exportId)
        toast({
          title: 'Export deleted',
          description: 'Export has been deleted successfully.',
        })
      } catch (error) {
        toast({
          title: 'Error deleting export',
          description: 'Failed to delete export. Please try again.',
          variant: 'destructive',
        })
      }
    },
    isPending: false,
    error: null
  }
}

export function useDownloadExport() {
  const { toast } = useToast()

  return {
    mutate: async (exportId: string) => {
      try {
        // Placeholder implementation
        console.log('Downloading export:', exportId)
        toast({
          title: 'Export downloaded',
          description: 'Export download has been started.',
        })
      } catch (error) {
        toast({
          title: 'Error downloading export',
          description: 'Failed to download export. Please try again.',
          variant: 'destructive',
        })
      }
    },
    isPending: false,
    error: null
  }
}

export function useCancelExport() {
  const { toast } = useToast()

  return {
    mutate: async (exportId: string) => {
      try {
        // Placeholder implementation
        console.log('Cancelling export:', exportId)
        toast({
          title: 'Export cancelled',
          description: 'Export has been cancelled successfully.',
        })
      } catch (error) {
        toast({
          title: 'Error cancelling export',
          description: 'Failed to cancel export. Please try again.',
          variant: 'destructive',
        })
      }
    },
    isPending: false,
    error: null
  }
}



export default useExports
