"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  // Redirect to dashboard if authenticated, otherwise to login
  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        router.push("/dashboard");
      } else {
        router.push("/auth/enhanced-login");
      }
    }
  }, [isAuthenticated, isLoading, router]);

  return (
    <div className="flex min-h-screen flex-col">
      {/* This is just a fallback while redirecting */}
      <div className="flex flex-1 items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold">hepz</h1>
          <p className="mt-4 text-lg text-muted-foreground">
            Enterprise Document Processing Platform
          </p>
          <div className="mt-8 flex justify-center gap-4">
            <Button asChild>
              <Link href="/auth/enhanced-login">Login</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/auth/enhanced-login?tab=signup">Register</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
