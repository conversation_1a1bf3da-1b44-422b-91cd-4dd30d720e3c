/**
 * Admin Dashboard Page
 * Central hub for all administrative functions
 */

'use client'

import React from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Activity,
  BarChart3,
  Database,
  FileText,
  GitBranch,
  Monitor,
  Settings,
  Shield,
  Smartphone,
  Users,
  Zap
} from 'lucide-react'

interface AdminSection {
  title: string
  description: string
  icon: React.ReactNode
  href: string
  badge?: string
  color: string
}

const adminSections: AdminSection[] = [
  {
    title: 'System Monitoring',
    description: 'Monitor system health, performance, and alerts',
    icon: <Monitor className="h-6 w-6" />,
    href: '/admin/system-monitoring',
    badge: 'Live',
    color: 'bg-blue-500'
  },
  {
    title: 'Analytics Dashboard',
    description: 'Comprehensive analytics and insights across all systems',
    icon: <BarChart3 className="h-6 w-6" />,
    href: '/admin/analytics',
    badge: 'Enhanced',
    color: 'bg-green-500'
  },
  {
    title: 'Mobile Device Management',
    description: 'Manage mobile devices, notifications, and sync',
    icon: <Smartphone className="h-6 w-6" />,
    href: '/admin/mobile-devices',
    color: 'bg-purple-500'
  },
  {
    title: 'Event Monitoring',
    description: 'Monitor events and manage dead-letter queues',
    icon: <Activity className="h-6 w-6" />,
    href: '/admin/event-monitoring',
    color: 'bg-orange-500'
  },
  {
    title: 'Security & Compliance',
    description: 'Security monitoring, audit logs, and compliance',
    icon: <Shield className="h-6 w-6" />,
    href: '/admin/security',
    badge: 'Critical',
    color: 'bg-red-500'
  },
  {
    title: 'User Management',
    description: 'Manage users, roles, and permissions',
    icon: <Users className="h-6 w-6" />,
    href: '/admin/users',
    color: 'bg-indigo-500'
  },
  {
    title: 'Workflow Management',
    description: 'Monitor and manage automated workflows',
    icon: <GitBranch className="h-6 w-6" />,
    href: '/admin/workflows',
    color: 'bg-teal-500'
  },
  {
    title: 'Document Processing',
    description: 'Monitor document processing and AI operations',
    icon: <FileText className="h-6 w-6" />,
    href: '/admin/documents',
    color: 'bg-yellow-500'
  },
  {
    title: 'Infrastructure',
    description: 'Cache management, performance tuning, and optimization',
    icon: <Database className="h-6 w-6" />,
    href: '/admin/infrastructure',
    color: 'bg-gray-500'
  },
  {
    title: 'System Configuration',
    description: 'Feature flags, system settings, and configuration',
    icon: <Settings className="h-6 w-6" />,
    href: '/admin/configuration',
    color: 'bg-pink-500'
  }
]

export default function AdminDashboard() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive administrative tools and system monitoring
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <Zap className="h-3 w-3 mr-1" />
            All Systems Operational
          </Badge>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Healthy</div>
            <p className="text-xs text-muted-foreground">All services operational</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">+12% from last hour</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Processing Queue</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23</div>
            <p className="text-xs text-muted-foreground">Documents in queue</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">145ms</div>
            <p className="text-xs text-muted-foreground">Average response time</p>
          </CardContent>
        </Card>
      </div>

      {/* Admin Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adminSections.map((section, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={`p-2 rounded-lg ${section.color} text-white`}>
                  {section.icon}
                </div>
                {section.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {section.badge}
                  </Badge>
                )}
              </div>
              <CardTitle className="group-hover:text-primary transition-colors">
                {section.title}
              </CardTitle>
              <CardDescription>{section.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={section.href}>
                <Button className="w-full" variant="outline">
                  Access {section.title}
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent System Activity</CardTitle>
          <CardDescription>Latest events and system changes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="p-2 bg-green-100 rounded-full">
                <Shield className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium">Security scan completed</p>
                <p className="text-sm text-muted-foreground">No vulnerabilities detected</p>
              </div>
              <div className="text-sm text-muted-foreground">2 minutes ago</div>
            </div>

            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="p-2 bg-blue-100 rounded-full">
                <Database className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium">Database backup completed</p>
                <p className="text-sm text-muted-foreground">Backup size: 2.3 GB</p>
              </div>
              <div className="text-sm text-muted-foreground">15 minutes ago</div>
            </div>

            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="p-2 bg-orange-100 rounded-full">
                <Activity className="h-4 w-4 text-orange-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium">High CPU usage detected</p>
                <p className="text-sm text-muted-foreground">CPU usage: 85% - Auto-scaling triggered</p>
              </div>
              <div className="text-sm text-muted-foreground">1 hour ago</div>
            </div>

            <div className="flex items-center gap-4 p-3 border rounded-lg">
              <div className="p-2 bg-purple-100 rounded-full">
                <Smartphone className="h-4 w-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium">Mobile device registered</p>
                <p className="text-sm text-muted-foreground">iPhone 15 Pro - John Doe</p>
              </div>
              <div className="text-sm text-muted-foreground">2 hours ago</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Services Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">API Gateway</span>
              <Badge variant="default" className="bg-green-100 text-green-800">Online</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Database</span>
              <Badge variant="default" className="bg-green-100 text-green-800">Online</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Redis Cache</span>
              <Badge variant="default" className="bg-green-100 text-green-800">Online</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Service Bus</span>
              <Badge variant="default" className="bg-green-100 text-green-800">Online</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm">CPU Usage</span>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Memory Usage</span>
              <span className="text-sm font-medium">62%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Disk Usage</span>
              <span className="text-sm font-medium">38%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Network I/O</span>
              <span className="text-sm font-medium">1.2 GB/s</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Shield className="h-4 w-4 mr-2" />
              Run Security Scan
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Database className="h-4 w-4 mr-2" />
              Create Backup
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Activity className="h-4 w-4 mr-2" />
              Clear Cache
            </Button>
            <Button variant="outline" size="sm" className="w-full justify-start">
              <Monitor className="h-4 w-4 mr-2" />
              System Health Check
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
