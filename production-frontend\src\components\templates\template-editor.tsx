"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { Editor } from "@tinymce/tinymce-react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from "@/components/ui/spinner";
import { Badge } from "@/components/ui/badge";
import {
  Code,
  Eye,
  FileText,
  Calendar,
  User,
  Building,
  FolderKanban,
  AlertCircle,
  Save,
  Clock,
  CheckCircle,
  RefreshCw
} from "lucide-react";
import { cn } from "@/lib/utils";
import { templateRenderingService, TemplateVariable } from "@/services/template-rendering-service";
import { templateService } from "@/services/template-service";
import { useToast } from "@/components/ui/use-toast";
import { useDebounce } from "@/hooks/useDebounce";

interface TemplateEditorProps {
  templateId?: string;
  value: string;
  onChange: (value: string) => void;
  className?: string;
  height?: number;
  autoSave?: boolean;
  autoSaveInterval?: number; // in milliseconds
  onSave?: (content: string) => Promise<void>;
  readOnly?: boolean;
}

export function TemplateEditor({
  templateId,
  value,
  onChange,
  className,
  height = 500,
  autoSave = true,
  autoSaveInterval = 5000, // 5 seconds
  onSave,
  readOnly = false
}: TemplateEditorProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("visual");
  const [htmlValue, setHtmlValue] = useState(value);
  const [previewValue, setPreviewValue] = useState("");
  const [isRendering, setIsRendering] = useState(false);
  const [renderError, setRenderError] = useState<string | null>(null);
  const [variables, setVariables] = useState<TemplateVariable[]>([]);
  const [isLoadingVariables, setIsLoadingVariables] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const editorRef = useRef<any>(null); // Using any for TinyMCE editor reference

  // Debounced value for auto-save
  const debouncedValue = useDebounce(htmlValue, autoSaveInterval);

  // Update HTML value when value changes
  useEffect(() => {
    setHtmlValue(value);
    setHasUnsavedChanges(false);
  }, [value]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !debouncedValue || debouncedValue === value || !hasUnsavedChanges) {
      return;
    }

    const performAutoSave = async () => {
      setIsSaving(true);
      try {
        if (onSave) {
          await onSave(debouncedValue);
        } else if (templateId) {
          // Use template service for auto-save
          await templateService.updateTemplate(templateId, {
            // Assuming the template has a content field
            // This would need to be adjusted based on actual template structure
          });
        }

        setLastSaved(new Date());
        setHasUnsavedChanges(false);

        toast({
          title: "Auto-saved",
          description: "Template has been automatically saved.",
          duration: 2000,
        });
      } catch (error) {
        console.error("Auto-save failed:", error);
        toast({
          title: "Auto-save failed",
          description: "Failed to auto-save template. Please save manually.",
          variant: "destructive",
        });
      } finally {
        setIsSaving(false);
      }
    };

    performAutoSave();
  }, [debouncedValue, autoSave, onSave, templateId, value, hasUnsavedChanges, toast]);

  // Manual save function
  const handleManualSave = useCallback(async () => {
    if (!hasUnsavedChanges) return;

    setIsSaving(true);
    try {
      if (onSave) {
        await onSave(htmlValue);
      } else if (templateId) {
        await templateService.updateTemplate(templateId, {
          // Template update data
        });
      }

      setLastSaved(new Date());
      setHasUnsavedChanges(false);

      toast({
        title: "Saved",
        description: "Template has been saved successfully.",
      });
    } catch (error) {
      console.error("Save failed:", error);
      toast({
        title: "Save failed",
        description: "Failed to save template. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [htmlValue, onSave, templateId, hasUnsavedChanges, toast]);

  // Update preview value
  useEffect(() => {
    const renderTemplate = async () => {
      if (!htmlValue) {
        setPreviewValue("");
        return;
      }

      setIsRendering(true);
      setRenderError(null);

      try {
        // Use the template rendering service to render the template
        const renderedContent = templateRenderingService.renderPreview(htmlValue);
        setPreviewValue(renderedContent);
      } catch (error) {
        console.error("Error rendering template:", error);
        setRenderError("Failed to render template. Please check your template syntax.");
        toast({
          title: "Render Error",
          description: "Failed to render template. Please check your template syntax.",
          variant: "destructive",
        });
      } finally {
        setIsRendering(false);
      }
    };

    renderTemplate();
  }, [htmlValue, toast]);

  // Load template variables
  useEffect(() => {
    const loadVariables = async () => {
      setIsLoadingVariables(true);
      try {
        const variables = await templateRenderingService.getTemplateVariables();
        setVariables(variables);
      } catch (error) {
        console.error("Error loading template variables:", error);
        toast({
          title: "Error",
          description: "Failed to load template variables.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingVariables(false);
      }
    };

    loadVariables();
  }, [toast]);

  // Handle HTML editor change
  const handleHtmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setHtmlValue(newValue);
    setHasUnsavedChanges(true);
    onChange(newValue);
  };

  // Handle visual editor change
  const handleVisualEditorChange = (newValue: string) => {
    setHtmlValue(newValue);
    setHasUnsavedChanges(true);
    onChange(newValue);
  };

  // Insert variable
  const insertVariable = (variable: string) => {
    if (activeTab === "visual" && editorRef.current) {
      editorRef.current.insertContent(`{{${variable}}}`);
    } else if (activeTab === "html") {
      const textarea = document.getElementById("html-editor") as HTMLTextAreaElement;
      if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const newValue =
          htmlValue.substring(0, start) +
          `{{${variable}}}` +
          htmlValue.substring(end);

        setHtmlValue(newValue);
        onChange(newValue);

        // Set cursor position after the inserted variable
        setTimeout(() => {
          textarea.focus();
          textarea.selectionStart = start + variable.length + 4; // +4 for {{ and }}
          textarea.selectionEnd = start + variable.length + 4;
        }, 0);
      }
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-4">
            <TabsList>
              <TabsTrigger value="visual" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Visual Editor
              </TabsTrigger>
              <TabsTrigger value="html" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                HTML
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </TabsTrigger>
            </TabsList>

            {/* Save Status Indicator */}
            <div className="flex items-center gap-2">
              {isSaving ? (
                <Badge variant="secondary" className="flex items-center gap-1">
                  <RefreshCw className="h-3 w-3 animate-spin" />
                  Saving...
                </Badge>
              ) : hasUnsavedChanges ? (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Unsaved changes
                </Badge>
              ) : lastSaved ? (
                <Badge variant="default" className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  Saved {lastSaved.toLocaleTimeString()}
                </Badge>
              ) : null}

              {/* Manual Save Button */}
              {!readOnly && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleManualSave}
                  disabled={isSaving || !hasUnsavedChanges}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
              )}
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("user.name")}
            >
              <User className="mr-1 h-4 w-4" />
              User
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("organization.name")}
            >
              <Building className="mr-1 h-4 w-4" />
              Organization
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("project.name")}
            >
              <FolderKanban className="mr-1 h-4 w-4" />
              Project
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => insertVariable("date")}
            >
              <Calendar className="mr-1 h-4 w-4" />
              Date
            </Button>
          </div>
        </div>

        <TabsContent value="visual" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <Editor
                apiKey="your-tinymce-api-key" // Replace with your TinyMCE API key
                onInit={(_: any, editor: any) => editorRef.current = editor}
                initialValue={value}
                onEditorChange={handleVisualEditorChange}
                init={{
                  height,
                  menubar: true,
                  plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount'
                  ],
                  toolbar: 'undo redo | blocks | ' +
                    'bold italic forecolor | alignleft aligncenter ' +
                    'alignright alignjustify | bullist numlist outdent indent | ' +
                    'removeformat | help',
                  content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="html" className="mt-0">
          <Card>
            <CardContent className="p-0">
              <textarea
                id="html-editor"
                className="w-full h-[500px] p-4 font-mono text-sm resize-none border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                value={htmlValue}
                onChange={handleHtmlChange}
                spellCheck={false}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview" className="mt-0">
          <Card>
            <CardContent className="p-4">
              {isRendering ? (
                <div className="flex items-center justify-center py-8">
                  <Spinner className="mr-2" />
                  <span>Rendering template...</span>
                </div>
              ) : renderError ? (
                <div className="flex items-start gap-2 p-4 border border-destructive/50 bg-destructive/10 rounded-md">
                  <AlertCircle className="h-5 w-5 text-destructive shrink-0 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-destructive">Error rendering template</h4>
                    <p className="text-sm text-muted-foreground">{renderError}</p>
                  </div>
                </div>
              ) : (
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: previewValue }}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="bg-muted p-4 rounded-md">
        <h3 className="text-sm font-medium mb-2">Available Variables</h3>

        {isLoadingVariables ? (
          <div className="flex items-center justify-center py-4">
            <Spinner className="mr-2" />
            <span>Loading variables...</span>
          </div>
        ) : variables.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Group variables by category */}
            {Object.entries(
              variables.reduce<Record<string, TemplateVariable[]>>((acc, variable) => {
                const category = variable.category || 'general';
                if (!acc[category]) {
                  acc[category] = [];
                }
                acc[category].push(variable);
                return acc;
              }, {})
            ).map(([category, categoryVariables]) => (
              <div key={category}>
                <h4 className="text-xs font-medium flex items-center gap-1 mb-2">
                  {category === 'User' && <User className="h-3 w-3" />}
                  {category === 'Organization' && <Building className="h-3 w-3" />}
                  {category === 'Project' && <FolderKanban className="h-3 w-3" />}
                  {category === 'Date' && <Calendar className="h-3 w-3" />}
                  {category} Variables
                </h4>
                <div className="space-y-1">
                  {categoryVariables.map((variable) => (
                    <div
                      key={variable.name}
                      className="text-xs bg-background p-1.5 rounded cursor-pointer hover:bg-accent group"
                      onClick={() => insertVariable(variable.name)}
                      title={variable.description}
                    >
                      <div className="flex justify-between items-center">
                        <code>{`{{${variable.name}}}`}</code>
                        {variable.required && (
                          <span className="text-[10px] text-muted-foreground">Required</span>
                        )}
                      </div>
                      <p className="text-[10px] text-muted-foreground mt-1 hidden group-hover:block">
                        {variable.description}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <div>
              <h4 className="text-xs font-medium flex items-center gap-1 mb-1">
                <User className="h-3 w-3" />
                User Variables
              </h4>
              <div className="space-y-1">
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("user.name")}
                >
                  <code>{"{{user.name}}"}</code>
                </div>
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("user.email")}
                >
                  <code>{"{{user.email}}"}</code>
                </div>
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("user.role")}
                >
                  <code>{"{{user.role}}"}</code>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium flex items-center gap-1 mb-1">
                <Building className="h-3 w-3" />
                Organization Variables
              </h4>
              <div className="space-y-1">
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("organization.name")}
                >
                  <code>{"{{organization.name}}"}</code>
                </div>
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("organization.address")}
                >
                  <code>{"{{organization.address}}"}</code>
                </div>
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("organization.phone")}
                >
                  <code>{"{{organization.phone}}"}</code>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium flex items-center gap-1 mb-1">
                <Calendar className="h-3 w-3" />
                Date Variables
              </h4>
              <div className="space-y-1">
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("date")}
                >
                  <code>{"{{date}}"}</code>
                </div>
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("date.short")}
                >
                  <code>{"{{date.short}}"}</code>
                </div>
                <div
                  className="text-xs bg-background p-1 rounded cursor-pointer hover:bg-accent"
                  onClick={() => insertVariable("date.long")}
                >
                  <code>{"{{date.long}}"}</code>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
