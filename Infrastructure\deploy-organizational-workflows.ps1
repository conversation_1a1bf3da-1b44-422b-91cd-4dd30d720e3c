#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Deploy Azure resources for Enhanced Organizational Workflows

.DESCRIPTION
    This script deploys the required Azure resources for the organizational workflow system
    including storage, cognitive services, search, SignalR, Service Bus, and monitoring.

.PARAMETER Environment
    The environment to deploy to (dev, staging, prod)

.PARAMETER ResourceGroupName
    The name of the resource group to deploy to

.PARAMETER Location
    The Azure region to deploy to

.PARAMETER BaseName
    The base name for all resources

.EXAMPLE
    .\deploy-organizational-workflows.ps1 -Environment dev -ResourceGroupName rg-orgworkflow-dev -Location eastus -BaseName orgworkflow
#>

param(
    [Parameter(Mandatory = $true)]
    [ValidateSet('dev', 'staging', 'prod')]
    [string]$Environment,
    
    [Parameter(Mandatory = $true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory = $true)]
    [string]$Location,
    
    [Parameter(Mandatory = $true)]
    [string]$BaseName
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = 'White'
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to check if Azure CLI is installed and user is logged in
function Test-AzureCLI {
    try {
        $null = az account show 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Not logged in to Azure CLI"
        }
        Write-ColorOutput "✓ Azure CLI is installed and user is logged in" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "✗ Azure CLI is not installed or user is not logged in" "Red"
        Write-ColorOutput "Please install Azure CLI and run 'az login'" "Yellow"
        return $false
    }
}

# Function to create resource group if it doesn't exist
function New-ResourceGroupIfNotExists {
    param(
        [string]$Name,
        [string]$Location
    )
    
    Write-ColorOutput "Checking if resource group '$Name' exists..." "Cyan"
    
    $rgExists = az group exists --name $Name --output tsv
    
    if ($rgExists -eq 'false') {
        Write-ColorOutput "Creating resource group '$Name' in '$Location'..." "Yellow"
        az group create --name $Name --location $Location --output none
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ Resource group '$Name' created successfully" "Green"
        }
        else {
            throw "Failed to create resource group '$Name'"
        }
    }
    else {
        Write-ColorOutput "✓ Resource group '$Name' already exists" "Green"
    }
}

# Function to deploy Bicep template
function Deploy-BicepTemplate {
    param(
        [string]$ResourceGroupName,
        [string]$TemplateFile,
        [hashtable]$Parameters
    )
    
    Write-ColorOutput "Deploying Bicep template..." "Cyan"
    
    # Convert parameters to Azure CLI format
    $paramString = ""
    foreach ($key in $Parameters.Keys) {
        $paramString += "$key='$($Parameters[$key])' "
    }
    
    $deploymentName = "orgworkflow-deployment-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    
    Write-ColorOutput "Deployment name: $deploymentName" "Gray"
    Write-ColorOutput "Parameters: $paramString" "Gray"
    
    # Execute deployment
    $deploymentResult = az deployment group create `
        --resource-group $ResourceGroupName `
        --template-file $TemplateFile `
        --parameters $paramString `
        --name $deploymentName `
        --output json
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✓ Bicep template deployed successfully" "Green"
        return $deploymentResult | ConvertFrom-Json
    }
    else {
        throw "Failed to deploy Bicep template"
    }
}

# Function to update application settings
function Update-AppSettings {
    param(
        [object]$DeploymentOutputs
    )
    
    Write-ColorOutput "Updating application settings..." "Cyan"
    
    # Create app settings object
    $appSettings = @{
        "AZURE_STORAGE_ACCOUNT_NAME" = $DeploymentOutputs.storageAccountName.value
        "AZURE_STORAGE_ACCOUNT_KEY" = $DeploymentOutputs.storageAccountKey.value
        "AZURE_KEY_VAULT_NAME" = $DeploymentOutputs.keyVaultName.value
        "AZURE_COGNITIVE_SERVICES_ENDPOINT" = $DeploymentOutputs.cognitiveServicesEndpoint.value
        "AZURE_COGNITIVE_SERVICES_KEY" = $DeploymentOutputs.cognitiveServicesKey.value
        "AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT" = $DeploymentOutputs.documentIntelligenceEndpoint.value
        "AZURE_DOCUMENT_INTELLIGENCE_KEY" = $DeploymentOutputs.documentIntelligenceKey.value
        "AZURE_SEARCH_SERVICE_NAME" = $DeploymentOutputs.searchServiceName.value
        "AZURE_SEARCH_SERVICE_KEY" = $DeploymentOutputs.searchServiceKey.value
        "AZURE_SIGNALR_CONNECTION_STRING" = $DeploymentOutputs.signalRConnectionString.value
        "AZURE_SERVICE_BUS_CONNECTION_STRING" = $DeploymentOutputs.serviceBusConnectionString.value
        "AZURE_EVENT_GRID_TOPIC_ENDPOINT" = $DeploymentOutputs.eventGridTopicEndpoint.value
        "AZURE_EVENT_GRID_TOPIC_KEY" = $DeploymentOutputs.eventGridTopicKey.value
        "APPLICATIONINSIGHTS_INSTRUMENTATION_KEY" = $DeploymentOutputs.applicationInsightsInstrumentationKey.value
        "APPLICATIONINSIGHTS_CONNECTION_STRING" = $DeploymentOutputs.applicationInsightsConnectionString.value
    }
    
    # Save to local.settings.json for local development
    $localSettingsPath = "production-backend/local.settings.json"
    
    if (Test-Path $localSettingsPath) {
        Write-ColorOutput "Updating existing local.settings.json..." "Yellow"
        $localSettings = Get-Content $localSettingsPath | ConvertFrom-Json
    }
    else {
        Write-ColorOutput "Creating new local.settings.json..." "Yellow"
        $localSettings = @{
            IsEncrypted = $false
            Values = @{}
        }
    }
    
    # Update values
    foreach ($key in $appSettings.Keys) {
        $localSettings.Values.$key = $appSettings[$key]
    }
    
    # Save updated settings
    $localSettings | ConvertTo-Json -Depth 10 | Set-Content $localSettingsPath
    Write-ColorOutput "✓ local.settings.json updated" "Green"
    
    # Save to environment file for frontend
    $envPath = "production-frontend/.env.local"
    Write-ColorOutput "Creating frontend environment file..." "Yellow"
    
    $envContent = @"
# Azure Organizational Workflows Configuration
NEXT_PUBLIC_AZURE_STORAGE_ACCOUNT_NAME=$($DeploymentOutputs.storageAccountName.value)
NEXT_PUBLIC_AZURE_SIGNALR_CONNECTION_STRING=$($DeploymentOutputs.signalRConnectionString.value)
NEXT_PUBLIC_AZURE_SEARCH_SERVICE_NAME=$($DeploymentOutputs.searchServiceName.value)
NEXT_PUBLIC_APPLICATIONINSIGHTS_CONNECTION_STRING=$($DeploymentOutputs.applicationInsightsConnectionString.value)

# Backend API Configuration
AZURE_STORAGE_ACCOUNT_KEY=$($DeploymentOutputs.storageAccountKey.value)
AZURE_COGNITIVE_SERVICES_ENDPOINT=$($DeploymentOutputs.cognitiveServicesEndpoint.value)
AZURE_COGNITIVE_SERVICES_KEY=$($DeploymentOutputs.cognitiveServicesKey.value)
AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT=$($DeploymentOutputs.documentIntelligenceEndpoint.value)
AZURE_DOCUMENT_INTELLIGENCE_KEY=$($DeploymentOutputs.documentIntelligenceKey.value)
AZURE_SEARCH_SERVICE_KEY=$($DeploymentOutputs.searchServiceKey.value)
AZURE_SERVICE_BUS_CONNECTION_STRING=$($DeploymentOutputs.serviceBusConnectionString.value)
AZURE_EVENT_GRID_TOPIC_ENDPOINT=$($DeploymentOutputs.eventGridTopicEndpoint.value)
AZURE_EVENT_GRID_TOPIC_KEY=$($DeploymentOutputs.eventGridTopicKey.value)
"@
    
    $envContent | Set-Content $envPath
    Write-ColorOutput "✓ Frontend environment file created" "Green"
}

# Function to create search indexes
function Initialize-SearchIndexes {
    param(
        [string]$SearchServiceName,
        [string]$SearchServiceKey
    )
    
    Write-ColorOutput "Initializing search indexes..." "Cyan"
    
    # Document index schema
    $documentIndexSchema = @{
        name = "documents-index"
        fields = @(
            @{ name = "id"; type = "Edm.String"; key = $true; searchable = $false }
            @{ name = "fileName"; type = "Edm.String"; searchable = $true; filterable = $true }
            @{ name = "content"; type = "Edm.String"; searchable = $true }
            @{ name = "documentType"; type = "Edm.String"; filterable = $true; facetable = $true }
            @{ name = "department"; type = "Edm.String"; filterable = $true; facetable = $true }
            @{ name = "tags"; type = "Collection(Edm.String)"; searchable = $true; filterable = $true }
            @{ name = "createdAt"; type = "Edm.DateTimeOffset"; filterable = $true; sortable = $true }
            @{ name = "modifiedAt"; type = "Edm.DateTimeOffset"; filterable = $true; sortable = $true }
            @{ name = "organizationId"; type = "Edm.String"; filterable = $true }
            @{ name = "complianceFlags"; type = "Collection(Edm.String)"; filterable = $true }
        )
    } | ConvertTo-Json -Depth 10
    
    # Workflow index schema
    $workflowIndexSchema = @{
        name = "workflows-index"
        fields = @(
            @{ name = "id"; type = "Edm.String"; key = $true; searchable = $false }
            @{ name = "name"; type = "Edm.String"; searchable = $true; filterable = $true }
            @{ name = "description"; type = "Edm.String"; searchable = $true }
            @{ name = "category"; type = "Edm.String"; filterable = $true; facetable = $true }
            @{ name = "department"; type = "Edm.String"; filterable = $true; facetable = $true }
            @{ name = "status"; type = "Edm.String"; filterable = $true; facetable = $true }
            @{ name = "documentTypes"; type = "Collection(Edm.String)"; filterable = $true }
            @{ name = "complianceFramework"; type = "Collection(Edm.String)"; filterable = $true }
            @{ name = "organizationId"; type = "Edm.String"; filterable = $true }
            @{ name = "createdAt"; type = "Edm.DateTimeOffset"; filterable = $true; sortable = $true }
        )
    } | ConvertTo-Json -Depth 10
    
    # Create indexes using REST API
    $searchEndpoint = "https://$SearchServiceName.search.windows.net"
    $headers = @{
        "Content-Type" = "application/json"
        "api-key" = $SearchServiceKey
    }
    
    try {
        # Create documents index
        Invoke-RestMethod -Uri "$searchEndpoint/indexes?api-version=2023-11-01" -Method POST -Body $documentIndexSchema -Headers $headers
        Write-ColorOutput "✓ Documents search index created" "Green"
    }
    catch {
        Write-ColorOutput "⚠ Documents index may already exist or failed to create: $($_.Exception.Message)" "Yellow"
    }
    
    try {
        # Create workflows index
        Invoke-RestMethod -Uri "$searchEndpoint/indexes?api-version=2023-11-01" -Method POST -Body $workflowIndexSchema -Headers $headers
        Write-ColorOutput "✓ Workflows search index created" "Green"
    }
    catch {
        Write-ColorOutput "⚠ Workflows index may already exist or failed to create: $($_.Exception.Message)" "Yellow"
    }
}

# Main execution
try {
    Write-ColorOutput "🚀 Starting Azure Organizational Workflows Deployment" "Magenta"
    Write-ColorOutput "Environment: $Environment" "Gray"
    Write-ColorOutput "Resource Group: $ResourceGroupName" "Gray"
    Write-ColorOutput "Location: $Location" "Gray"
    Write-ColorOutput "Base Name: $BaseName" "Gray"
    Write-ColorOutput ""
    
    # Check prerequisites
    if (-not (Test-AzureCLI)) {
        exit 1
    }
    
    # Create resource group
    New-ResourceGroupIfNotExists -Name $ResourceGroupName -Location $Location
    
    # Deploy Bicep template
    $templateFile = "azure-resources-organizational-workflows.bicep"
    $parameters = @{
        environment = $Environment
        location = $Location
        baseName = $BaseName
    }
    
    $deploymentResult = Deploy-BicepTemplate -ResourceGroupName $ResourceGroupName -TemplateFile $templateFile -Parameters $parameters
    
    # Update application settings
    Update-AppSettings -DeploymentOutputs $deploymentResult.properties.outputs
    
    # Initialize search indexes
    $searchServiceName = $deploymentResult.properties.outputs.searchServiceName.value
    $searchServiceKey = $deploymentResult.properties.outputs.searchServiceKey.value
    Initialize-SearchIndexes -SearchServiceName $searchServiceName -SearchServiceKey $searchServiceKey
    
    Write-ColorOutput ""
    Write-ColorOutput "🎉 Deployment completed successfully!" "Green"
    Write-ColorOutput ""
    Write-ColorOutput "📋 Deployment Summary:" "Cyan"
    Write-ColorOutput "  • Storage Account: $($deploymentResult.properties.outputs.storageAccountName.value)" "White"
    Write-ColorOutput "  • Key Vault: $($deploymentResult.properties.outputs.keyVaultName.value)" "White"
    Write-ColorOutput "  • Search Service: $($deploymentResult.properties.outputs.searchServiceName.value)" "White"
    Write-ColorOutput "  • SignalR Service: Created" "White"
    Write-ColorOutput "  • Service Bus: Created" "White"
    Write-ColorOutput "  • Event Grid: Created" "White"
    Write-ColorOutput "  • Application Insights: Created" "White"
    Write-ColorOutput ""
    Write-ColorOutput "📁 Configuration files updated:" "Cyan"
    Write-ColorOutput "  • production-backend/local.settings.json" "White"
    Write-ColorOutput "  • production-frontend/.env.local" "White"
    Write-ColorOutput ""
    Write-ColorOutput "🔧 Next steps:" "Yellow"
    Write-ColorOutput "  1. Deploy the Azure Functions to the Function App" "White"
    Write-ColorOutput "  2. Deploy the frontend application" "White"
    Write-ColorOutput "  3. Configure authentication and authorization" "White"
    Write-ColorOutput "  4. Test the organizational workflow functionality" "White"
}
catch {
    Write-ColorOutput ""
    Write-ColorOutput "❌ Deployment failed: $($_.Exception.Message)" "Red"
    Write-ColorOutput "Stack trace: $($_.ScriptStackTrace)" "Red"
    exit 1
}
