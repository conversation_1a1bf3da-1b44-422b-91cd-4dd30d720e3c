/**
 * PDF Form Engine Type Declarations
 */

export interface PDFFormField {
  id: string
  name: string
  type: PDFFieldType
  value?: any
  required?: boolean
  readonly?: boolean
  readOnly?: boolean
  placeholder?: string
  options?: string[]
  validation?: {
    pattern?: string
    min?: number
    max?: number
    message?: string
  }
  position: {
    page: number
    x: number
    y: number
    width: number
    height: number
  }
  appearance?: {
    fontSize?: number
    fontFamily?: string
    color?: string
    backgroundColor?: string
    borderColor?: string
    borderWidth?: number
  }
  aiSuggestion?: {
    suggestedValue: string
    confidence: number
  }
}

export enum PDFFieldType {
  TEXT = 'text',
  NUMBER = 'number',
  EMAIL = 'email',
  DATE = 'date',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SELECT = 'select',
  TEXTAREA = 'textarea',
  SIGNATURE = 'signature',
  IMAGE = 'image'
}

export interface DocumentLayout {
  pages: Array<{
    pageNumber: number
    width: number
    height: number
    fields: PDFFormField[]
    elements: Array<{
      type: 'text' | 'image' | 'shape'
      content: string
      position: {
        x: number
        y: number
        width: number
        height: number
      }
    }>
    textBlocks: Array<{
      content: string
      position: { x: number; y: number; width: number; height: number }
    }>
    images: Array<{
      src: string
      position: { x: number; y: number; width: number; height: number }
    }>
    tables: Array<{
      rows: number
      columns: number
      position: { x: number; y: number; width: number; height: number }
    }>
  }>
  metadata: {
    title?: string
    author?: string
    subject?: string
    keywords?: string[]
    createdAt: string
    modifiedAt: string
  }
}

export class PDFFormEngine {
  constructor(options?: {
    enableValidation?: boolean
    enableAutoSave?: boolean
    theme?: 'light' | 'dark'
  })

  loadDocument(documentUrl: string): Promise<DocumentLayout>
  getFormFields(): PDFFormField[]
  updateField(fieldId: string, value: any): void
  validateForm(): { isValid: boolean; errors: string[] }
  exportFormData(): Record<string, any>
  importFormData(data: Record<string, any>): void
  saveDocument(): Promise<Blob>

  // Missing methods that components expect
  detectFormFields(documentId: string): Promise<PDFFormField[]>
  extractDocumentLayout(documentId: string): Promise<DocumentLayout>
  fillFormFields(documentId: string, fields: PDFFormField[], options?: {
    useAISuggestions?: boolean
    validateFields?: boolean
    preserveLayout?: boolean
    generateMissingFields?: boolean
  }): Promise<{
    fields: PDFFormField[]
    completionPercentage: number
    validationErrors: Array<{
      fieldId: string
      message: string
      severity: 'error' | 'warning'
    }>
  }>
  validateFormCompletion(documentId: string, fields: PDFFormField[]): Promise<{
    isValid: boolean
    errors: Array<{
      fieldId: string
      message: string
      severity: 'error' | 'warning'
    }>
  }>

  // Event handlers
  onFieldChange(callback: (field: PDFFormField, value: any) => void): void
  onValidationError(callback: (errors: string[]) => void): void
  onFormComplete(callback: (data: Record<string, any>) => void): void
}
