/**
 * Digital Signature and Approval System
 * Comprehensive digital signature management with multi-level approvals and compliance tracking
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
// import { storageService } from '../shared/services/storage';
import { signalREnhanced } from '../shared/services/signalr';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Digital signature interfaces
export interface DigitalSignature {
  id: string;
  documentId: string;
  signerId: string;
  signerName: string;
  signerEmail: string;
  signatureType: SignatureType;
  signatureData: SignatureData;
  position: SignaturePosition;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  certificateInfo?: CertificateInfo;
  biometricData?: BiometricData;
  complianceLevel: ComplianceLevel;
  status: SignatureStatus;
  validationResults: ValidationResult[];
  organizationId: string;
  workflowExecutionId?: string;
  approvalLevelId?: string;
}

export interface ApprovalRequest {
  id: string;
  documentId: string;
  workflowExecutionId: string;
  approvalLevelId: string;
  requesterId: string;
  requesterName: string;
  approvers: ApprovalAssignment[];
  dueDate: string;
  priority: ApprovalPriority;
  message?: string;
  attachments: string[];
  status: ApprovalStatus;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  escalatedAt?: string;
  organizationId: string;
}

export interface ApprovalAssignment {
  id: string;
  approverId: string;
  approverName: string;
  approverEmail: string;
  role: string;
  status: ApprovalStatus;
  assignedAt: string;
  respondedAt?: string;
  response?: ApprovalResponse;
  delegatedTo?: string;
  escalationLevel: number;
}

export interface ApprovalResponse {
  action: ApprovalAction;
  comment?: string;
  conditions?: string[];
  attachments: string[];
  signatureRequired: boolean;
  digitalSignature?: DigitalSignature;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
}

export interface SignatureWorkflow {
  id: string;
  name: string;
  description: string;
  documentTypes: string[];
  signatureRequirements: SignatureRequirement[];
  approvalFlow: ApprovalFlow;
  complianceSettings: SignatureComplianceSettings;
  organizationId: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Enums
export enum SignatureType {
  ELECTRONIC = 'electronic',
  DIGITAL = 'digital',
  BIOMETRIC = 'biometric',
  CERTIFICATE_BASED = 'certificate_based',
  MULTI_FACTOR = 'multi_factor'
}

export enum SignatureStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  REVOKED = 'revoked'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELEGATED = 'delegated',
  ESCALATED = 'escalated',
  EXPIRED = 'expired'
}

export enum ApprovalAction {
  APPROVE = 'approve',
  REJECT = 'reject',
  REQUEST_CHANGES = 'request_changes',
  DELEGATE = 'delegate',
  ESCALATE = 'escalate'
}

export enum ApprovalPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

export enum ComplianceLevel {
  BASIC = 'basic',
  STANDARD = 'standard',
  ENHANCED = 'enhanced',
  REGULATORY = 'regulatory',
  LEGAL = 'legal'
}

// Supporting interfaces
export interface SignatureData {
  imageData?: string;
  vectorData?: string;
  biometricHash?: string;
  certificateData?: string;
  encryptedData?: string;
  metadata: Record<string, any>;
  // Additional properties for PKI integration
  signerName?: string;
  signerEmail?: string;
  position?: SignaturePosition;
  provider?: string;
}

export interface SignaturePosition {
  page: number;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface CertificateInfo {
  issuer: string;
  subject: string;
  serialNumber: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
  algorithm: string;
  keyUsage: string[];
}

export interface BiometricData {
  type: 'fingerprint' | 'voice' | 'facial' | 'iris';
  hash: string;
  confidence: number;
  template: string;
  deviceInfo: Record<string, any>;
}

export interface ValidationResult {
  type: 'integrity' | 'authenticity' | 'non_repudiation' | 'compliance';
  status: 'valid' | 'invalid' | 'warning';
  message: string;
  details: Record<string, any>;
  timestamp: string;
}

export interface SignatureRequirement {
  level: number;
  signerRoles: string[];
  signerUsers: string[];
  signatureType: SignatureType;
  complianceLevel: ComplianceLevel;
  required: boolean;
  conditions: SignatureCondition[];
}

export interface SignatureCondition {
  field: string;
  operator: string;
  value: any;
  description: string;
}

export interface ApprovalFlow {
  levels: ApprovalLevel[];
  parallelApproval: boolean;
  escalationEnabled: boolean;
  delegationEnabled: boolean;
  skipConditions: SkipCondition[];
}

export interface ApprovalLevel {
  level: number;
  name: string;
  approverRoles: string[];
  approverUsers: string[];
  requiredApprovals: number;
  timeoutHours: number;
  escalationChain: string[];
  signatureRequired: boolean;
}

export interface SkipCondition {
  field: string;
  operator: string;
  value: any;
  reason: string;
}

export interface SignatureComplianceSettings {
  frameworks: string[];
  auditRequired: boolean;
  timestampRequired: boolean;
  certificateValidation: boolean;
  biometricRequired: boolean;
  ipLogging: boolean;
  deviceFingerprinting: boolean;
}

class DigitalSignatureApprovalSystem {
  private readonly CACHE_TTL = 3600; // 1 hour

  /**
   * Create digital signature request
   */
  async createSignatureRequest(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    if (request.method === 'OPTIONS') {
      const preflightResponse = handlePreflight(request);
      if (preflightResponse) {
        return preflightResponse;
      }
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        documentId: Joi.string().uuid().required(),
        workflowExecutionId: Joi.string().uuid().optional(),
        signatureType: Joi.string().valid(...Object.values(SignatureType)).default(SignatureType.ELECTRONIC),
        position: Joi.object({
          page: Joi.number().min(1).required(),
          x: Joi.number().min(0).required(),
          y: Joi.number().min(0).required(),
          width: Joi.number().min(1).required(),
          height: Joi.number().min(1).required(),
          rotation: Joi.number().min(0).max(360).optional()
        }).required(),
        complianceLevel: Joi.string().valid(...Object.values(ComplianceLevel)).default(ComplianceLevel.STANDARD),
        message: Joi.string().max(1000).optional(),
        dueDate: Joi.string().isoDate().optional()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Get document
      const document = await db.readItem('documents', value.documentId, value.documentId);
      if (!document) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Document not found' }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkSignaturePermission(
        document,
        authResult.user,
        value.signatureType
      );

      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Insufficient permissions for digital signature' }
        }, request);
      }

      // Create signature request
      const signatureId = uuidv4();
      const signature: DigitalSignature = {
        id: signatureId,
        documentId: value.documentId,
        signerId: authResult.user.id,
        signerName: authResult.user.displayName || authResult.user.email,
        signerEmail: authResult.user.email,
        signatureType: value.signatureType,
        signatureData: {
          metadata: {
            requestedAt: new Date().toISOString(),
            message: value.message
          }
        },
        position: value.position,
        timestamp: new Date().toISOString(),
        ipAddress: this.getClientIP(request),
        userAgent: request.headers.get('user-agent') || 'unknown',
        complianceLevel: value.complianceLevel,
        status: SignatureStatus.PENDING,
        validationResults: [],
        organizationId: authResult.user?.organizationId || '',
        workflowExecutionId: value.workflowExecutionId
      };

      // Store signature request
      await db.createItem('digital-signatures', signature);

      // Cache signature
      await redis.setex(`signature:${signatureId}`, this.CACHE_TTL, JSON.stringify(signature));

      // Send notification
      await signalREnhanced.sendToUser(authResult.user?.id || '', {
        target: 'signature_request_created',
        arguments: [{
          signatureId,
          documentId: value.documentId,
          dueDate: value.dueDate
        }]
      });

      // Log audit event
      await this.logSignatureEvent(authResult.user.id, 'signature_request_created', {
        signatureId,
        documentId: value.documentId,
        signatureType: value.signatureType,
        complianceLevel: value.complianceLevel
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          signatureId,
          status: 'pending',
          message: 'Digital signature request created successfully'
        }
      }, request);

    } catch (error) {
      logger.error('Error creating signature request', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Apply digital signature to document
   */
  async applyDigitalSignature(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const signatureId = request.params?.signatureId;
      if (!signatureId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Signature ID is required' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        signatureData: Joi.object({
          imageData: Joi.string().optional(),
          vectorData: Joi.string().optional(),
          biometricHash: Joi.string().optional(),
          certificateData: Joi.string().optional(),
          metadata: Joi.object().default({})
        }).required(),
        pin: Joi.string().optional(),
        biometricData: Joi.object().optional(),
        certificateInfo: Joi.object().optional()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Get signature request
      const signature = await this.getDigitalSignature(signatureId);
      if (!signature) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Signature request not found' }
        }, request);
      }

      // Verify signer
      if (signature.signerId !== authResult.user.id) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Unauthorized to sign this document' }
        }, request);
      }

      // Validate signature data
      const validationResults = await this.validateSignatureData(
        value.signatureData,
        signature.signatureType,
        signature.complianceLevel
      );

      // Apply signature to document
      const signedDocumentUrl = await this.applySignatureToDocument(
        signature.documentId,
        signature.position,
        value.signatureData
      );

      // Update signature record
      const updatedSignature = {
        ...signature,
        signatureData: value.signatureData,
        biometricData: value.biometricData,
        certificateInfo: value.certificateInfo,
        status: SignatureStatus.COMPLETED,
        validationResults,
        timestamp: new Date().toISOString()
      };

      await db.upsertItem('digital-signatures', updatedSignature);

      // Update document with signature
      await this.updateDocumentWithSignature(signature.documentId, updatedSignature, signedDocumentUrl);

      // Send notifications
      await this.notifySignatureCompletion(updatedSignature);

      // Log audit event
      await this.logSignatureEvent(authResult.user.id, 'signature_applied', {
        signatureId,
        documentId: signature.documentId,
        validationResults: validationResults.map(v => v.status)
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          signatureId,
          status: 'completed',
          validationResults,
          signedDocumentUrl,
          message: 'Digital signature applied successfully'
        }
      }, request);

    } catch (error) {
      logger.error('Error applying digital signature', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Private helper methods
  private async checkSignaturePermission(document: any, user: any, signatureType: SignatureType): Promise<boolean> {
    try {
      // Check if user has permission to sign documents
      const userRole = await db.queryItems('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.userId = @userId',
        [document.organizationId, user.id]
      );

      if (userRole.length === 0) {
        return false;
      }

      const member = userRole[0];
      const allowedRoles = ['admin', 'document_signer', 'approver'];
      
      const memberData = member as any;

      // Check for enhanced signature types
      if (signatureType === SignatureType.CERTIFICATE_BASED || signatureType === SignatureType.BIOMETRIC) {
        return memberData.role === 'admin' || memberData.permissions?.includes('enhanced_signature');
      }

      return allowedRoles.includes(memberData.role) || memberData.permissions?.includes('document_signature');
    } catch (error) {
      logger.error('Error checking signature permission', { documentId: document.id, userId: user.id, error });
      return false;
    }
  }

  private getClientIP(request: HttpRequest): string {
    // Extract client IP from request headers
    return request.headers.get('x-forwarded-for') || 
           request.headers.get('x-real-ip') || 
           'unknown';
  }

  private async getDigitalSignature(signatureId: string): Promise<DigitalSignature | null> {
    try {
      // Check cache first
      const cached = await redis.get(`signature:${signatureId}`);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get from database
      const signature = await db.readItem('digital-signatures', signatureId, signatureId);
      if (signature) {
        await redis.setex(`signature:${signatureId}`, this.CACHE_TTL, JSON.stringify(signature));
      }

      return signature as DigitalSignature;
    } catch (error) {
      logger.error('Error getting digital signature', { signatureId, error });
      return null;
    }
  }

  private async validateSignatureData(
    signatureData: SignatureData,
    signatureType: SignatureType,
    complianceLevel: ComplianceLevel
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];

    // Basic validation
    if (!signatureData.imageData && !signatureData.vectorData && !signatureData.biometricHash) {
      results.push({
        type: 'integrity',
        status: 'invalid',
        message: 'No signature data provided',
        details: {},
        timestamp: new Date().toISOString()
      });
      return results;
    }

    // Integrity validation
    results.push({
      type: 'integrity',
      status: 'valid',
      message: 'Signature data integrity verified',
      details: { dataSize: JSON.stringify(signatureData).length },
      timestamp: new Date().toISOString()
    });

    // Compliance validation based on level
    if (complianceLevel === ComplianceLevel.REGULATORY || complianceLevel === ComplianceLevel.LEGAL) {
      if (signatureType === SignatureType.ELECTRONIC) {
        results.push({
          type: 'compliance',
          status: 'warning',
          message: 'Electronic signature may not meet regulatory requirements',
          details: { complianceLevel, signatureType },
          timestamp: new Date().toISOString()
        });
      } else {
        results.push({
          type: 'compliance',
          status: 'valid',
          message: 'Signature meets compliance requirements',
          details: { complianceLevel, signatureType },
          timestamp: new Date().toISOString()
        });
      }
    }

    return results;
  }

  private async applySignatureToDocument(
    documentId: string,
    _position: SignaturePosition,
    _signatureData: SignatureData
  ): Promise<string> {
    try {
      // Get the original document from storage
      const document = await db.readItem('documents', documentId, documentId);
      if (!document || !document.fileUrl) {
        throw new Error('Document not found or has no file URL');
      }

      // Download the original document
      const documentResponse = await fetch(document.fileUrl);
      if (!documentResponse.ok) {
        throw new Error(`Failed to download document: ${documentResponse.statusText}`);
      }

      const documentBuffer = await documentResponse.arrayBuffer();

      // Apply signature using PKI document signing service
      const { DocumentSigningService } = await import('../services/pki/document-signing-service');
      // Create a simple PKI provider registry for this context
      class SimplePKIProviderRegistry {
        async getProvider(providerId: string) {
          // Return a mock provider for now
          return {
            signData: async () => ({ signature: 'mock-signature', signingCertificate: 'mock-cert' }),
            getCertificate: async () => ({ commonName: 'Mock Certificate' })
          };
        }
      }

      const pkiRegistry = new SimplePKIProviderRegistry();
      const documentSigningService = new DocumentSigningService(pkiRegistry as any);
      await documentSigningService.initialize();

      const signingRequest = {
        documentId,
        documentBuffer: Buffer.from(documentBuffer),
        documentFormat: 'pdf' as const,
        signatureType: 'both' as const,
        pkiOptions: {
          providerId: _signatureData.provider || 'azure-keyvault',
          certificateId: _signatureData.certificateData || 'default',
          signatureFormat: 'PAdES' as const,
          timestampingRequired: true,
          includeSigningCertificate: true,
          includeCertificateChain: true,
          complianceLevel: 'advanced' as const
        },
        visualOptions: {
          signatureImage: Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64'), // 1x1 transparent PNG
          position: {
            page: 1,
            x: _signatureData.position?.x || 100,
            y: _signatureData.position?.y || 100,
            width: _signatureData.position?.width || 200,
            height: _signatureData.position?.height || 50,
            coordinateSystem: 'bottom-left' as const
          },
          appearance: {
            showSignatureImage: true,
            showSignerName: true,
            showSigningTime: true,
            showReason: true,
            showLocation: false,
            showCertificateInfo: false
          },
          reason: 'Document approval signature',
          location: 'Digital Platform',
          contactInfo: _signatureData.signerEmail || ''
        },
        userId: _signatureData.signerName || 'unknown',
        organizationId: 'default'
      };

      const signingResult = await documentSigningService.signDocument(signingRequest);
      const signedBuffer = signingResult.signedDocumentBuffer;

      // Generate signed document path
      const timestamp = Date.now();
      const signedDocumentPath = `signed-documents/${documentId}-signed-${timestamp}.pdf`;

      // Upload signed document to storage
      const uploadResponse = await fetch('/api/storage/upload', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/pdf',
          'X-File-Path': signedDocumentPath
        },
        body: signedBuffer
      });

      if (!uploadResponse.ok) {
        throw new Error(`Failed to upload signed document: ${uploadResponse.statusText}`);
      }

      const uploadResult = await uploadResponse.json();
      const signedDocumentUrl = uploadResult.url || `${process.env.STORAGE_BASE_URL}/${signedDocumentPath}`;

      logger.info('Document signature applied successfully', {
        documentId,
        signedDocumentUrl,
        signatureSize: signedBuffer.length
      });

      return signedDocumentUrl;
    } catch (error) {
      logger.error('Error applying signature to document', { documentId, error });
      throw error;
    }
  }

  private async updateDocumentWithSignature(
    documentId: string,
    signature: DigitalSignature,
    signedDocumentUrl: string
  ): Promise<void> {
    try {
      const document = await db.readItem('documents', documentId, documentId);
      if (document) {
        const updatedDocument = {
          ...document,
          signedDocumentUrl,
          signatures: [...(document.signatures || []), signature.id],
          status: 'signed',
          signedAt: new Date().toISOString(),
          signedBy: signature.signerId
        };

        await db.upsertItem('documents', updatedDocument);
      }
    } catch (error) {
      logger.error('Error updating document with signature', { documentId, signatureId: signature.id, error });
    }
  }

  private async notifySignatureCompletion(signature: DigitalSignature): Promise<void> {
    try {
      // Notify workflow participants if part of a workflow
      if (signature.workflowExecutionId) {
        await signalREnhanced.sendToGroup(`workflow:${signature.workflowExecutionId}`, {
          target: 'document_signed',
          arguments: [{
            documentId: signature.documentId,
            signerId: signature.signerId,
            signerName: signature.signerName,
            timestamp: signature.timestamp
          }]
        });
      }

      // Notify document owner
      const document = await db.readItem('documents', signature.documentId, signature.documentId);
      if (document && document.createdBy !== signature.signerId) {
        await signalREnhanced.sendToUser(document.createdBy, {
          target: 'document_signature_completed',
          arguments: [{
            documentId: signature.documentId,
            signerName: signature.signerName,
            timestamp: signature.timestamp
          }]
        });
      }

    } catch (error) {
      logger.error('Error notifying signature completion', { signatureId: signature.id, error });
    }
  }

  private async logSignatureEvent(userId: string, action: string, details: Record<string, any>): Promise<void> {
    try {
      const auditEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        userId,
        action,
        details,
        ipAddress: 'unknown',
        userAgent: 'unknown',
        result: 'success'
      };

      await db.createItem('signature-audit-logs', auditEntry);
    } catch (error) {
      logger.error('Failed to log signature event', { userId, action, error });
    }
  }
}

// Create instance
const signatureSystem = new DigitalSignatureApprovalSystem();

// Register HTTP functions
app.http('signature-request-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'signatures/request',
  handler: (request, context) => signatureSystem.createSignatureRequest(request, context)
});

app.http('signature-apply', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'signatures/{signatureId}/apply',
  handler: (request, context) => signatureSystem.applyDigitalSignature(request, context)
});
