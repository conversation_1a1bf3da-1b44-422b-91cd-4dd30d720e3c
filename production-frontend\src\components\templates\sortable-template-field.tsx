"use client";

import { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Trash2,
  GripVertical,
  ChevronDown,
  ChevronUp,
  Type,
  Hash,
  Calendar,
  CheckSquare,
  List,
  FileText,
  File,
  Pen,
  Table,
  Plus
} from "lucide-react";
// import { TemplateField } from "@/types/backend"; // Unused
import { FieldType } from "@/types/store";

import { FIELD_TYPES } from "@/types/store";
import { cn } from "@/lib/utils";

interface SortableTemplateFieldProps {
  field: any; // Using any to handle type conflicts between service and backend types
  onUpdate: (field: any) => void;
  onRemove: () => void;
}

export function SortableTemplateField({
  field,
  onUpdate,
  onRemove
}: SortableTemplateFieldProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: field.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.5 : 1,
  };

  // Update field property
  const updateField = (property: string, value: any) => {
    onUpdate({
      ...field,
      [property]: value
    });
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Get field type icon
  const getFieldTypeIcon = (type: FieldType) => {
    switch (type) {
      case FIELD_TYPES.TEXT:
        return <Type className="h-4 w-4" />;
      case FIELD_TYPES.NUMBER:
        return <Hash className="h-4 w-4" />;
      case FIELD_TYPES.DATE:
        return <Calendar className="h-4 w-4" />;
      case FIELD_TYPES.CHECKBOX:
        return <CheckSquare className="h-4 w-4" />;
      case FIELD_TYPES.SELECT:
        return <List className="h-4 w-4" />;
      case FIELD_TYPES.MULTI_SELECT:
        return <List className="h-4 w-4" />;
      case 'richtext':
        return <FileText className="h-4 w-4" />;
      case FIELD_TYPES.FILE:
        return <File className="h-4 w-4" />;
      case FIELD_TYPES.SIGNATURE:
        return <Pen className="h-4 w-4" />;
      case FIELD_TYPES.TABLE:
        return <Table className="h-4 w-4" />;
      default:
        return <Type className="h-4 w-4" />;
    }
  };

  // Get field type label
  const getFieldTypeLabel = (type: FieldType) => {
    switch (type) {
      case FIELD_TYPES.TEXT:
        return "Text";
      case FIELD_TYPES.NUMBER:
        return "Number";
      case FIELD_TYPES.DATE:
        return "Date";
      case FIELD_TYPES.CHECKBOX:
        return "Checkbox";
      case FIELD_TYPES.SELECT:
        return "Select";
      case FIELD_TYPES.MULTI_SELECT:
        return "Multi Select";
      case 'richtext':
        return "Rich Text";
      case FIELD_TYPES.FILE:
        return "File";
      case FIELD_TYPES.SIGNATURE:
        return "Signature";
      case FIELD_TYPES.TABLE:
        return "Table";
      default:
        return type;
    }
  };

  return (
    <div ref={setNodeRef} style={style} className="touch-none">
      <Card className={cn(
        "w-full transition-shadow border",
        isDragging ? "shadow-lg" : "",
        isExpanded ? "border-primary/50" : "border-muted"
      )}>
        <CardContent className="p-3">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 cursor-grab"
                {...attributes}
                {...listeners}
              >
                <GripVertical className="h-4 w-4" />
              </Button>

              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  {getFieldTypeIcon(field.type)}
                  <span className="text-sm font-medium">{field.label}</span>
                </div>
                <Badge variant="outline" className="text-xs">
                  {getFieldTypeLabel(field.type)}
                </Badge>
                {field.required && (
                  <Badge variant="default" className="text-xs">Required</Badge>
                )}
              </div>
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onRemove}
              >
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={toggleExpanded}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {isExpanded && (
            <div className="mt-4 space-y-4 pl-10">
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor={`field-label-${field.id}`}>Label</Label>
                  <Input
                    id={`field-label-${field.id}`}
                    value={field.label}
                    onChange={(e) => updateField("label", e.target.value)}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor={`field-name-${field.id}`}>Name</Label>
                  <Input
                    id={`field-name-${field.id}`}
                    value={field.name}
                    onChange={(e) => updateField("name", e.target.value)}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor={`field-type-${field.id}`}>Type</Label>
                  <Select
                    value={field.type}
                    onValueChange={(value) => updateField("type", value)}
                  >
                    <SelectTrigger id={`field-type-${field.id}`}>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={FIELD_TYPES.TEXT}>Text</SelectItem>
                      <SelectItem value={FIELD_TYPES.NUMBER}>Number</SelectItem>
                      <SelectItem value={FIELD_TYPES.DATE}>Date</SelectItem>
                      <SelectItem value={FIELD_TYPES.CHECKBOX}>Checkbox</SelectItem>
                      <SelectItem value={FIELD_TYPES.SELECT}>Select</SelectItem>
                      <SelectItem value={FIELD_TYPES.MULTI_SELECT}>Multi Select</SelectItem>
                      <SelectItem value={FIELD_TYPES.RICH_TEXT}>Rich Text</SelectItem>
                      <SelectItem value={FIELD_TYPES.FILE}>File</SelectItem>
                      <SelectItem value={FIELD_TYPES.SIGNATURE}>Signature</SelectItem>
                      <SelectItem value={FIELD_TYPES.TABLE}>Table</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor={`field-placeholder-${field.id}`}>Placeholder</Label>
                  <Input
                    id={`field-placeholder-${field.id}`}
                    value={field.placeholder || ""}
                    onChange={(e) => updateField("placeholder", e.target.value)}
                  />
                </div>
              </div>

              <div className="grid gap-2">
                <Label htmlFor={`field-help-${field.id}`}>Help Text</Label>
                <Textarea
                  id={`field-help-${field.id}`}
                  value={field.helpText || ""}
                  onChange={(e) => updateField("helpText", e.target.value)}
                  rows={2}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id={`field-required-${field.id}`}
                  checked={field.required}
                  onCheckedChange={(checked) => updateField("required", checked)}
                />
                <Label htmlFor={`field-required-${field.id}`}>Required field</Label>
              </div>

              {(field.type === FIELD_TYPES.SELECT || field.type === FIELD_TYPES.MULTI_SELECT) && (
                <div className="grid gap-2">
                  <Label>Options</Label>
                  <div className="space-y-2">
                    {(field.options || []).map((option: any, index: number) => (
                      <div key={index} className="flex gap-2">
                        <Input
                          value={String(option)}
                          onChange={(e) => {
                            const newOptions = [...(field.options || [])];
                            newOptions[index] = e.target.value;
                            updateField("options", newOptions);
                          }}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => {
                            const newOptions = [...(field.options || [])];
                            newOptions.splice(index, 1);
                            updateField("options", newOptions);
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => {
                        const newOptions = [...(field.options || []), "New Option"];
                        updateField("options", newOptions);
                      }}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Option
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
