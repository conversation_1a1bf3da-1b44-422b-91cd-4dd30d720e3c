'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useMemberActivity } from '@/hooks/organizations/useOrganizationMembers';
import { formatDistanceToNow } from 'date-fns';

interface MemberActivityLogProps {
  organizationId: string;
  memberId: string;
}

export function MemberActivityLog({ organizationId, memberId }: MemberActivityLogProps) {
  const { activities, isLoading, error } = useMemberActivity(memberId);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Log</CardTitle>
          <CardDescription>Recent member activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-start space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-3/4" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Log</CardTitle>
          <CardDescription>Recent member activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center p-4 text-destructive">
            Error loading activity log
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Log</CardTitle>
        <CardDescription>Recent member activity</CardDescription>
      </CardHeader>
      <CardContent>
        {activities && activities.length > 0 ? (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-4 border-b pb-4 last:border-0">
                <div className="flex-1">
                  <p className="text-sm font-medium">{activity.type || 'Activity'}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                  {activity.details && (
                    <div className="mt-2 text-xs text-muted-foreground">
                      {Object.entries(activity.details as Record<string, any>).map(([key, value]) => (
                        <div key={key}>
                          <span className="font-medium">{key}: </span>
                          <span>{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-4 text-muted-foreground">
            No activity recorded for this member
          </div>
        )}
      </CardContent>
    </Card>
  );
}
