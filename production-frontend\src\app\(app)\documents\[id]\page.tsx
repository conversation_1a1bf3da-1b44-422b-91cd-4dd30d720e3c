"use client";

import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { DocumentStatus } from "@/types/document";
import { documentService } from "@/services/document-service";
import { formatDistanceToNow } from "date-fns";
import { DocumentAnalysis } from '@/components/documents';
import { DocumentMetadata } from '@/components/documents/document-metadata';
import { DocumentProcessing } from '@/components/documents/document-processing';
import { DocumentSharing } from '@/components/documents/document-sharing';
import { DocumentSigningModern } from '@/components/documents/document-signing-modern';
import { EditorJSRichTextEditor } from '@/components/documents/editorjs-rich-text-editor';
import { DocumentVersions } from '@/components/documents/document-versions';
import { DocumentViewer } from '@/components/documents/document-viewer';
import { ProductionDocumentComments } from '@/components/documents/production-document-comments';
import { DialogFooter, DialogHeader } from '@/components/ui/dialog';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useDocuments, useDocument } from '@/hooks/documents';
import { DialogDescription, DialogTitle, Dialog, DialogContent } from '@radix-ui/react-dialog';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuItem, DropdownMenuSeparator } from '@radix-ui/react-dropdown-menu';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@radix-ui/react-tabs';
import { ArrowLeft, Download, MoreHorizontal, Share, MessageSquare, Trash, Tag, Plus, Pencil, FileText, Cpu, BarChart, GitCompare, PenTool, History } from 'lucide-react';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';

export default function DocumentDetailsPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { toast } = useToast();
  const { user } = useAuth();

  // Ensure params and searchParams are not null
  if (!params || !searchParams) {
    return <div>Loading...</div>;
  }

  const documentId = params.id as string;
  const projectId = searchParams.get("projectId") || "project-123"; // Fallback for demo
  const organizationId = searchParams.get("organizationId") || "org-456"; // Fallback for demo

  const [activeTab, setActiveTab] = useState("preview");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [tagInput, setTagInput] = useState("");
  const [isEditingTags, setIsEditingTags] = useState(false);
  const [content, setContent] = useState<string>("");

  // Get document data using the correct hook signature
  const { data: document, isLoading, refetch } = useDocument(documentId);

  // Real API implementations for document operations
  const [isUpdatingTags, setIsUpdatingTags] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const updateTags = async (tags: string[]) => {
    setIsUpdatingTags(true);
    try {
      const response = await fetch(`/documents/${documentId}/tags`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tags })
      });

      if (!response.ok) {
        throw new Error(`Failed to update tags: ${response.statusText}`);
      }

      const result = await response.json();

      // Update local document state
      if (result.success && result.data) {
        // The document should be refetched by the query
        refetch();
      }

      toast({
        title: "Tags updated",
        description: "Document tags have been updated successfully.",
      });
    } catch (error) {
      console.error('Error updating tags:', error);
      toast({
        title: "Error updating tags",
        description: "Failed to update document tags. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingTags(false);
    }
  };

  const deleteDocument = async () => {
    setIsDeleting(true);
    try {
      // Note: Document delete endpoint doesn't exist in backend
      // Using placeholder until backend endpoint is implemented
      const response = { ok: false } as any;

      if (!response.ok) {
        throw new Error(`Failed to delete document: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error deleting document",
        description: "Failed to delete document. Please try again.",
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsDeleting(false);
    }
  };

  const downloadDocument = async () => {
    setIsDownloading(true);
    try {
      const response = await fetch(`/documents/${documentId}/download`, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error(`Failed to download document: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.downloadUrl) {
        // Use the download URL provided by the backend
        const link = window.document.createElement('a');
        link.href = result.downloadUrl;
        link.download = result.filename || document?.name || 'document';
        window.document.body.appendChild(link);
        link.click();
        window.document.body.removeChild(link);
      } else {
        throw new Error('Download URL not provided');
      }

      toast({
        title: "Download started",
        description: "Document download has started.",
      });
    } catch (error) {
      console.error('Error downloading document:', error);
      toast({
        title: "Error downloading document",
        description: "Failed to download document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDownloading(false);
    }
  };

  // Fetch document content
  useEffect(() => {
    if (document && document.id) {
      fetchDocumentContent();
    }
  }, [document]);

  const fetchDocumentContent = async () => {
    try {
      // Fetch document content
      const contentBlob = await documentService.getContent(documentId);

      // Convert blob to text
      const contentText = await contentBlob.text();
      setContent(contentText);
    } catch (error) {
      console.error("Error fetching document content:", error);
      toast({
        title: "Error",
        description: "Failed to load document content. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle tag management
  const handleAddTag = () => {
    if (tagInput.trim() && document?.tags && !document.tags.includes(tagInput.trim())) {
      const newTags = [...document.tags, tagInput.trim()];
      updateTags(newTags);
      setTagInput("");
    }
  };

  const handleRemoveTag = (tag: string) => {
    if (document?.tags) {
      const newTags = document.tags.filter((t: string) => t !== tag);
      updateTags(newTags);
    }
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle document deletion
  const handleDeleteDocument = () => {
    try {
      deleteDocument();
      toast({
        title: "Document deleted",
        description: "The document has been deleted successfully.",
      });
      router.push(`/documents?projectId=${projectId}&organizationId=${organizationId}`);
    } catch (error) {
      // Error is handled by the hook
    } finally {
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle document download
  const handleDownloadDocument = () => {
    try {
      downloadDocument();
    } catch (error) {
      // Error is handled by the hook
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        <Skeleton className="h-[calc(100vh-300px)] w-full" />
      </div>
    );
  }

  if (!document) {
    return (
      <div className="flex flex-col items-center justify-center h-[calc(100vh-200px)]">
        <h2 className="text-2xl font-bold">Document not found</h2>
        <p className="text-muted-foreground mt-2">
          The document you're looking for doesn't exist or you don't have permission to view it.
        </p>
        <Button asChild className="mt-6">
          <Link href="/documents">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Documents
          </Link>
        </Button>
      </div>
    );
  }

  const statusVariant =
    document.status === 'processed' ? "success" :
    document.status === 'processing' ? "secondary" :
    document.status === 'pending' ? "outline" :
    "destructive";

  const statusLabel =
    document.status === 'processed' ? "Processed" :
    document.status === 'processing' ? "Processing" :
    document.status === 'pending' ? "Pending" :
    "Failed";

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <Button
              variant="ghost"
              size="sm"
              className="p-0"
              asChild
            >
              <Link href={`/documents?projectId=${projectId}&organizationId=${organizationId}`}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Documents
              </Link>
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold tracking-tight">{document.name}</h1>
            <Badge variant={statusVariant}>{statusLabel}</Badge>
          </div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
            <span>
              Updated {formatDistanceToNow(new Date(document.updatedAt))} ago
            </span>
            <span>•</span>
            <span>
              Version {document.version}
            </span>
            <span>•</span>
            <span>
              {document.type}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadDocument}
            disabled={isDownloading}
          >
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => setActiveTab("sharing")}>
                <Share className="mr-2 h-4 w-4" />
                Share
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveTab("versions")}>
                <History className="mr-2 h-4 w-4" />
                View History
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setActiveTab("comments")}>
                <MessageSquare className="mr-2 h-4 w-4" />
                Comments
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setIsDeleteDialogOpen(true)} className="text-destructive">
                <Trash className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 items-center">
        <div className="flex items-center gap-2">
          <Tag size={16} className="text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Tags:</span>
        </div>

        {isEditingTags ? (
          <div className="flex flex-1 items-center gap-2">
            <div className="flex flex-wrap gap-1">
              {document.tags?.map((tag: string) => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <button
                    type="button"
                    onClick={() => handleRemoveTag(tag)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <Trash size={12} />
                  </button>
                </Badge>
              ))}
            </div>
            <div className="flex gap-2 ml-2">
              <Input
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyDown={handleTagInputKeyDown}
                placeholder="Add tag..."
                className="h-8 w-32"
              />
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={handleAddTag}
                disabled={isUpdatingTags}
              >
                <Plus size={16} />
              </Button>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={() => setIsEditingTags(false)}
                disabled={isUpdatingTags}
              >
                Done
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div className="flex flex-wrap gap-1">
              {document.tags && document.tags.length > 0 ? (
                document.tags.map((tag: string) => (
                  <Badge key={tag} variant="secondary">{tag}</Badge>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No tags</span>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditingTags(true)}
              className="ml-2"
            >
              <Pencil size={14} className="mr-1" />
              Edit
            </Button>
          </>
        )}
      </div>

      <Tabs defaultValue="preview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-10 w-full">
          <TabsTrigger value="preview">Preview</TabsTrigger>
          <TabsTrigger value="editor">
            <FileText className="h-4 w-4 mr-2" />
            Editor
          </TabsTrigger>
          <TabsTrigger value="processing">
            <Cpu className="h-4 w-4 mr-2" />
            Processing
          </TabsTrigger>
          <TabsTrigger value="analysis">
            <BarChart className="h-4 w-4 mr-2" />
            Analysis
          </TabsTrigger>
          <TabsTrigger value="compare">
            <GitCompare className="h-4 w-4 mr-2" />
            Compare
          </TabsTrigger>
          <TabsTrigger value="sign">
            <PenTool className="h-4 w-4 mr-2" />
            Sign
          </TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="versions">Versions</TabsTrigger>
          <TabsTrigger value="sharing">Sharing</TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="mt-6">
          <DocumentViewer
            document={document as any}
            isLoading={isLoading}
            onDownload={handleDownloadDocument}
          />
        </TabsContent>

        <TabsContent value="editor" className="mt-6">
          {user && content && (
            <EditorJSRichTextEditor
              documentId={document.id}
              projectId={projectId as string}
              organizationId={organizationId as string}
              documentName={document.name}
              initialContent={content}
              currentUser={{
                id: user.id,
                name: user.displayName || user.firstName || user.lastName || user.email || "Anonymous",
                avatarUrl: user.avatarUrl
              }}
            />
          )}
        </TabsContent>

        <TabsContent value="processing" className="mt-6">
          {user && (
            <DocumentProcessing
              documentId={document.id}
              documentName={document.name}
              projectId={projectId as string}
              organizationId={organizationId as string}
              onProcessingComplete={() => {
                // Refresh document data after processing
                toast({
                  title: "Processing Complete",
                  description: "Document processing has completed successfully.",
                });
              }}
            />
          )}
        </TabsContent>

        <TabsContent value="metadata" className="mt-6">
          <DocumentMetadata document={document as any} />
        </TabsContent>

        <TabsContent value="comments" className="mt-6">
          {user && (
            <ProductionDocumentComments
              documentId={document.id}
              projectId={projectId as string}
              organizationId={organizationId as string}
              currentUser={{
                id: user.id,
                name: user.displayName || user.firstName || user.lastName || user.email || "Anonymous",
                avatarUrl: user.avatarUrl,
                email: user.email
              }}
            />
          )}
        </TabsContent>

        <TabsContent value="versions" className="mt-6">
          <DocumentVersions
            documentId={document.id}
          />
        </TabsContent>

        <TabsContent value="analysis" className="mt-6">
          {user && (
            <DocumentAnalysis
              documentIds={[document.id]}
              documentNames={[document.name]}
              organizationId={organizationId as string}
              projectId={projectId as string}
              onAnalysisComplete={() => {
                toast({
                  title: "Analysis Complete",
                  description: "Document analysis has completed successfully.",
                });
              }}
            />
          )}
        </TabsContent>

        <TabsContent value="compare" className="mt-6">
          {user && (
            <div className="flex flex-col space-y-4">
              <div className="bg-muted p-4 rounded-md">
                <h3 className="text-lg font-medium mb-2">Document Version Comparison</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Compare different versions of this document to see what has changed over time.
                </p>
                <Button
                  onClick={() => {
                    // Navigate to versions tab
                    setActiveTab("versions");
                    toast({
                      title: "Document Comparison",
                      description: "Please select versions to compare in the Versions tab.",
                    });
                  }}
                >
                  <GitCompare className="mr-2 h-4 w-4" />
                  Go to Version History
                </Button>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="sign" className="mt-6">
          {user && (
            <DocumentSigningModern
              documentId={document.id}
              documentName={document.name}
              projectId={projectId as string}
              organizationId={organizationId as string}
              onSigningComplete={() => {
                toast({
                  title: "Document Signed",
                  description: "Document has been signed successfully.",
                });
                // Refresh document data
                window.location.reload();
              }}
              onCancel={() => {
                setActiveTab("preview");
              }}
            />
          )}
        </TabsContent>

        <TabsContent value="sharing" className="mt-6">
          <DocumentSharing documentId={document.id} />
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteDocument}
              disabled={isDeleting}
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
