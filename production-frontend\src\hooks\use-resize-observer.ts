/**
 * Resize Observer Hook
 * Provides a React hook for observing element size changes
 */

import { useEffect, useRef, useCallback, useState } from 'react'

export interface ResizeObserverEntry {
  target: Element
  contentRect: DOMRectReadOnly
  borderBoxSize?: ReadonlyArray<ResizeObserverSize>
  contentBoxSize?: ReadonlyArray<ResizeObserverSize>
  devicePixelContentBoxSize?: ReadonlyArray<ResizeObserverSize>
}

export interface UseResizeObserverOptions {
  box?: 'content-box' | 'border-box' | 'device-pixel-content-box'
  debounceMs?: number
  disabled?: boolean
}

export interface UseResizeObserverResult {
  ref: React.RefObject<HTMLElement>
  width: number | undefined
  height: number | undefined
  entry: ResizeObserverEntry | undefined
}

/**
 * Hook that observes the size of an element using ResizeObserver
 */
export function useResizeObserver<T extends HTMLElement = HTMLElement>(
  options: UseResizeObserverOptions = {}
): UseResizeObserverResult {
  const { box = 'content-box', debounceMs = 0, disabled = false } = options
  
  const ref = useRef<T>(null)
  const [entry, setEntry] = useState<ResizeObserverEntry | undefined>()
  const [width, setWidth] = useState<number | undefined>()
  const [height, setHeight] = useState<number | undefined>()
  
  const debounceTimeoutRef = useRef<NodeJS.Timeout>()

  const updateSize = useCallback((entry: ResizeObserverEntry) => {
    const { contentRect, borderBoxSize, contentBoxSize, devicePixelContentBoxSize } = entry
    
    let newWidth: number
    let newHeight: number

    if (box === 'border-box' && borderBoxSize) {
      const borderBox = Array.isArray(borderBoxSize) ? borderBoxSize[0] : borderBoxSize
      newWidth = borderBox.inlineSize
      newHeight = borderBox.blockSize
    } else if (box === 'device-pixel-content-box' && devicePixelContentBoxSize) {
      const devicePixelBox = Array.isArray(devicePixelContentBoxSize) 
        ? devicePixelContentBoxSize[0] 
        : devicePixelContentBoxSize
      newWidth = devicePixelBox.inlineSize
      newHeight = devicePixelBox.blockSize
    } else if (box === 'content-box' && contentBoxSize) {
      const contentBox = Array.isArray(contentBoxSize) ? contentBoxSize[0] : contentBoxSize
      newWidth = contentBox.inlineSize
      newHeight = contentBox.blockSize
    } else {
      // Fallback to contentRect
      newWidth = contentRect.width
      newHeight = contentRect.height
    }

    setEntry(entry)
    setWidth(newWidth)
    setHeight(newHeight)
  }, [box])

  const debouncedUpdateSize = useCallback((entry: ResizeObserverEntry) => {
    if (debounceMs > 0) {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      debounceTimeoutRef.current = setTimeout(() => {
        updateSize(entry)
      }, debounceMs)
    } else {
      updateSize(entry)
    }
  }, [updateSize, debounceMs])

  useEffect(() => {
    const element = ref.current
    if (!element || disabled) return

    // Check if ResizeObserver is supported
    if (typeof ResizeObserver === 'undefined') {
      console.warn('ResizeObserver is not supported in this browser')
      return
    }

    const resizeObserver = new ResizeObserver((entries) => {
      if (entries.length > 0) {
        debouncedUpdateSize(entries[0] as ResizeObserverEntry)
      }
    })

    resizeObserver.observe(element, { box })

    return () => {
      resizeObserver.disconnect()
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [debouncedUpdateSize, box, disabled])

  return { ref, width, height, entry }
}

/**
 * Hook that observes multiple elements
 */
export function useMultipleResizeObserver<T extends HTMLElement = HTMLElement>(
  options: UseResizeObserverOptions = {}
) {
  const { box = 'content-box', debounceMs = 0, disabled = false } = options
  
  const [entries, setEntries] = useState<Map<Element, ResizeObserverEntry>>(new Map())
  const observerRef = useRef<ResizeObserver>()
  const elementsRef = useRef<Set<T>>(new Set())
  const debounceTimeoutRef = useRef<NodeJS.Timeout>()

  const updateEntries = useCallback((newEntries: ResizeObserverEntry[]) => {
    setEntries(prev => {
      const updated = new Map(prev)
      newEntries.forEach(entry => {
        updated.set(entry.target, entry)
      })
      return updated
    })
  }, [])

  const debouncedUpdateEntries = useCallback((newEntries: ResizeObserverEntry[]) => {
    if (debounceMs > 0) {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
      debounceTimeoutRef.current = setTimeout(() => {
        updateEntries(newEntries)
      }, debounceMs)
    } else {
      updateEntries(newEntries)
    }
  }, [updateEntries, debounceMs])

  const observe = useCallback((element: T) => {
    if (disabled || !element) return

    if (!observerRef.current) {
      if (typeof ResizeObserver === 'undefined') {
        console.warn('ResizeObserver is not supported in this browser')
        return
      }

      observerRef.current = new ResizeObserver((entries) => {
        debouncedUpdateEntries(entries as ResizeObserverEntry[])
      })
    }

    elementsRef.current.add(element)
    observerRef.current.observe(element, { box })
  }, [debouncedUpdateEntries, box, disabled])

  const unobserve = useCallback((element: T) => {
    if (observerRef.current && element) {
      observerRef.current.unobserve(element)
      elementsRef.current.delete(element)
      setEntries(prev => {
        const updated = new Map(prev)
        updated.delete(element)
        return updated
      })
    }
  }, [])

  const disconnect = useCallback(() => {
    if (observerRef.current) {
      observerRef.current.disconnect()
      elementsRef.current.clear()
      setEntries(new Map())
    }
  }, [])

  useEffect(() => {
    return () => {
      disconnect()
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current)
      }
    }
  }, [disconnect])

  return {
    entries,
    observe,
    unobserve,
    disconnect,
  }
}

/**
 * Hook that provides element dimensions with ResizeObserver
 */
export function useElementSize<T extends HTMLElement = HTMLElement>(
  options: UseResizeObserverOptions = {}
) {
  const { ref, width, height } = useResizeObserver<T>(options)
  
  return {
    ref,
    size: { width, height },
    width,
    height,
  }
}

/**
 * Hook that tracks if an element is being resized
 */
export function useIsResizing<T extends HTMLElement = HTMLElement>(
  options: UseResizeObserverOptions & { resizeEndDelay?: number } = {}
) {
  const { resizeEndDelay = 100, ...resizeOptions } = options
  const [isResizing, setIsResizing] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout>()
  
  const { ref, width, height } = useResizeObserver<T>({
    ...resizeOptions,
    debounceMs: 0, // We handle debouncing ourselves
  })

  useEffect(() => {
    if (width !== undefined || height !== undefined) {
      setIsResizing(true)
      
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      
      timeoutRef.current = setTimeout(() => {
        setIsResizing(false)
      }, resizeEndDelay)
    }
  }, [width, height, resizeEndDelay])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return {
    ref,
    isResizing,
    width,
    height,
  }
}

export default useResizeObserver
