import { useState, useCallback, useMemo } from 'react'

/**
 * Selection Hook
 * Manages selection state for lists and tables
 */

export interface UseSelectionOptions<T> {
  multiple?: boolean
  getItemId?: (item: T) => string | number
}

export interface UseSelectionResult<T> {
  selectedItems: T[]
  selectedIds: Set<string | number>
  isSelected: (item: T) => boolean
  isAllSelected: boolean
  isPartiallySelected: boolean
  selectItem: (item: T) => void
  deselectItem: (item: T) => void
  toggleItem: (item: T) => void
  selectAll: (items: T[]) => void
  deselectAll: () => void
  toggleAll: (items: T[]) => void
  setSelection: (items: T[]) => void
  clearSelection: () => void
}

export function useSelection<T>(
  options: UseSelectionOptions<T> = {}
): UseSelectionResult<T> {
  const { multiple = true, getItemId = (item: any) => item.id } = options
  
  const [selectedItems, setSelectedItems] = useState<T[]>([])

  const selectedIds = useMemo(() => {
    return new Set(selectedItems.map(getItemId))
  }, [selectedItems, getItemId])

  const isSelected = useCallback((item: T) => {
    return selectedIds.has(getItemId(item))
  }, [selectedIds, getItemId])

  const selectItem = useCallback((item: T) => {
    setSelectedItems(prev => {
      if (multiple) {
        if (prev.some(selected => getItemId(selected) === getItemId(item))) {
          return prev
        }
        return [...prev, item]
      } else {
        return [item]
      }
    })
  }, [multiple, getItemId])

  const deselectItem = useCallback((item: T) => {
    setSelectedItems(prev => 
      prev.filter(selected => getItemId(selected) !== getItemId(item))
    )
  }, [getItemId])

  const toggleItem = useCallback((item: T) => {
    if (isSelected(item)) {
      deselectItem(item)
    } else {
      selectItem(item)
    }
  }, [isSelected, selectItem, deselectItem])

  const selectAll = useCallback((items: T[]) => {
    if (multiple) {
      setSelectedItems(items)
    } else if (items.length > 0) {
      setSelectedItems([items[0]])
    }
  }, [multiple])

  const deselectAll = useCallback(() => {
    setSelectedItems([])
  }, [])

  const toggleAll = useCallback((items: T[]) => {
    const allSelected = items.every(item => isSelected(item))
    if (allSelected) {
      deselectAll()
    } else {
      selectAll(items)
    }
  }, [isSelected, selectAll, deselectAll])

  const setSelection = useCallback((items: T[]) => {
    setSelectedItems(items)
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedItems([])
  }, [])

  const isAllSelected = useCallback((items: T[]) => {
    return items.length > 0 && items.every(item => isSelected(item))
  }, [isSelected])

  const isPartiallySelected = useCallback((items: T[]) => {
    const selectedCount = items.filter(item => isSelected(item)).length
    return selectedCount > 0 && selectedCount < items.length
  }, [isSelected])

  return {
    selectedItems,
    selectedIds,
    isSelected,
    isAllSelected: false, // Will be computed by caller with items
    isPartiallySelected: false, // Will be computed by caller with items
    selectItem,
    deselectItem,
    toggleItem,
    selectAll,
    deselectAll,
    toggleAll,
    setSelection,
    clearSelection,
  }
}

/**
 * Selection hook with items context
 */
export function useSelectionWithItems<T>(
  items: T[],
  options: UseSelectionOptions<T> = {}
): UseSelectionResult<T> & {
  isAllSelected: boolean
  isPartiallySelected: boolean
} {
  const selection = useSelection<T>(options)
  
  const isAllSelected = useMemo(() => {
    return items.length > 0 && items.every(item => selection.isSelected(item))
  }, [items, selection.isSelected])

  const isPartiallySelected = useMemo(() => {
    const selectedCount = items.filter(item => selection.isSelected(item)).length
    return selectedCount > 0 && selectedCount < items.length
  }, [items, selection.isSelected])

  return {
    ...selection,
    isAllSelected,
    isPartiallySelected,
  }
}
