/**
 * Workflow Automation Hook
 * Manages workflow automation operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { workflowService } from '@/services/workflow-service'
import { useToast } from '@/hooks/use-toast'
import type { ID, Workflow } from '@/types'

export interface WorkflowTemplate {
  id: ID
  name: string
  description: string
  category: string
  steps: any[]
  triggers: any[]
  isPublic: boolean
  usageCount: number
  rating: number
  createdBy: ID
  createdAt: string
}

export interface WorkflowExecution {
  id: ID
  workflowId: ID
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled'
  startedAt: string
  completedAt?: string
  input?: any
  output?: any
  error?: string
  progress: number
  currentStep?: string
}

export interface WorkflowAnalytics {
  totalExecutions: number
  successRate: number
  averageExecutionTime: number
  mostUsedTemplates: Array<{
    templateId: ID
    name: string
    usageCount: number
  }>
  performanceMetrics: {
    bottlenecks: Array<{
      stepName: string
      averageTime: number
      failureRate: number
    }>
    trends: Array<{
      date: string
      executions: number
      successRate: number
    }>
  }
}

export interface PendingApproval {
  id: ID
  workflowId: ID
  executionId: ID
  stepName: string
  requestedBy: ID
  requestedAt: string
  data: any
  priority: 'low' | 'medium' | 'high'
}

export interface UseWorkflowAutomationResult {
  // Templates
  templates: WorkflowTemplate[]
  loadingTemplates: boolean
  
  // Executions
  executions: WorkflowExecution[]
  loadingExecutions: boolean
  
  // Analytics
  analytics: WorkflowAnalytics | null
  loadingAnalytics: boolean
  
  // Approvals
  pendingApprovals: PendingApproval[]
  loadingApprovals: boolean
  
  // Error state
  error: string | null
  
  // Template operations
  createFromTemplate: (templateId: ID, input?: any) => Promise<WorkflowExecution>
  duplicateWorkflow: (workflowId: ID) => Promise<Workflow>
  
  // Execution operations
  executeWorkflow: (workflowId: ID, input?: any) => Promise<WorkflowExecution>
  pauseExecution: (executionId: ID) => Promise<void>
  resumeExecution: (executionId: ID) => Promise<void>
  cancelExecution: (executionId: ID) => Promise<void>
  retryExecution: (executionId: ID) => Promise<WorkflowExecution>
  
  // Approval operations
  approveStep: (approvalId: ID, data?: any) => Promise<void>
  rejectStep: (approvalId: ID, reason: string) => Promise<void>
  
  // Bulk operations
  bulkExecute: (requests: Array<{ workflowId: ID; input?: any }>) => Promise<WorkflowExecution[]>
  bulkCancel: (executionIds: ID[]) => Promise<void>
  
  // Monitoring
  getExecutionLogs: (executionId: ID) => Promise<any[]>
  getExecutionMetrics: (executionId: ID) => Promise<any>
  
  // Refresh operations
  refreshTemplates: () => Promise<void>
  refreshExecutions: () => Promise<void>
  refreshAnalytics: () => Promise<void>
  refreshApprovals: () => Promise<void>
  refreshAll: () => Promise<void>
}

export function useWorkflowAutomation(): UseWorkflowAutomationResult {
  const { toast } = useToast()
  
  // State
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([])
  const [loadingTemplates, setLoadingTemplates] = useState(false)
  
  const [executions, setExecutions] = useState<WorkflowExecution[]>([])
  const [loadingExecutions, setLoadingExecutions] = useState(false)
  
  const [analytics, setAnalytics] = useState<WorkflowAnalytics | null>(null)
  const [loadingAnalytics, setLoadingAnalytics] = useState(false)
  
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([])
  const [loadingApprovals, setLoadingApprovals] = useState(false)
  
  const [error, setError] = useState<string | null>(null)

  // Load templates
  const refreshTemplates = useCallback(async () => {
    setLoadingTemplates(true)
    setError(null)

    try {
      const response = await workflowService.getWorkflowTemplates()
      setTemplates(response.data?.map(template => ({
        ...template,
        steps: [],
        triggers: [],
        usageCount: 0,
        rating: 0
      })) || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow templates'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoadingTemplates(false)
    }
  }, [toast])

  // Load executions
  const refreshExecutions = useCallback(async () => {
    setLoadingExecutions(true)
    setError(null)

    try {
      const response = await workflowService.getWorkflowExecutions('all') // Pass required workflowId
      setExecutions(response.data as any || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow executions'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoadingExecutions(false)
    }
  }, [toast])

  // Load analytics
  const refreshAnalytics = useCallback(async () => {
    setLoadingAnalytics(true)
    setError(null)

    try {
      const response = await workflowService.getWorkflowAnalytics()
      setAnalytics(response as any)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow analytics'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoadingAnalytics(false)
    }
  }, [toast])

  // Load pending approvals
  const refreshApprovals = useCallback(async () => {
    setLoadingApprovals(true)
    setError(null)

    try {
      const response = await workflowService.getPendingApprovals()
      setPendingApprovals(response as any || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load pending approvals'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoadingApprovals(false)
    }
  }, [toast])

  // Template operations
  const createFromTemplate = useCallback(async (templateId: ID, input?: any): Promise<WorkflowExecution> => {
    try {
      const execution = await workflowService.createWorkflowFromTemplate(templateId, input)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Workflow created',
        description: 'Workflow has been created from template and started.',
      })
      
      return execution as any
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create workflow from template'
      
      toast({
        type: 'error',
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [refreshExecutions, toast])

  const duplicateWorkflow = useCallback(async (workflowId: ID): Promise<Workflow> => {
    try {
      const workflow = await workflowService.duplicateWorkflow(workflowId)
      await refreshTemplates()
      
      toast({
        type: 'success',
        title: 'Workflow duplicated',
        description: 'Workflow has been duplicated successfully.',
      })
      
      return workflow as any
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to duplicate workflow'
      
      toast({
        type: 'error',
        title: 'Duplication failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [refreshTemplates, toast])

  // Execution operations
  const executeWorkflow = useCallback(async (workflowId: ID, input?: any): Promise<WorkflowExecution> => {
    try {
      const execution = await workflowService.executeWorkflow(workflowId, input)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Workflow started',
        description: 'Workflow execution has been started.',
      })
      
      return execution as any
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to execute workflow'
      
      toast({
        type: 'error',
        title: 'Execution failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [refreshExecutions, toast])

  const pauseExecution = useCallback(async (executionId: ID) => {
    try {
      await workflowService.pauseWorkflowExecution(executionId)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Workflow paused',
        description: 'Workflow execution has been paused.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to pause workflow'
      
      toast({
        type: 'error',
        title: 'Pause failed',
        description: errorMessage,
      })
    }
  }, [refreshExecutions, toast])

  const resumeExecution = useCallback(async (executionId: ID) => {
    try {
      await workflowService.resumeWorkflowExecution(executionId)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Workflow resumed',
        description: 'Workflow execution has been resumed.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to resume workflow'
      
      toast({
        type: 'error',
        title: 'Resume failed',
        description: errorMessage,
      })
    }
  }, [refreshExecutions, toast])

  const cancelExecution = useCallback(async (executionId: ID) => {
    try {
      await workflowService.cancelWorkflowExecution(executionId)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Workflow cancelled',
        description: 'Workflow execution has been cancelled.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to cancel workflow'
      
      toast({
        type: 'error',
        title: 'Cancel failed',
        description: errorMessage,
      })
    }
  }, [refreshExecutions, toast])

  const retryExecution = useCallback(async (executionId: ID): Promise<WorkflowExecution> => {
    try {
      const execution = await workflowService.retryWorkflowExecution(executionId)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Workflow retried',
        description: 'Workflow execution has been retried.',
      })
      
      return execution as any
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to retry workflow'
      
      toast({
        type: 'error',
        title: 'Retry failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [refreshExecutions, toast])

  // Approval operations
  const approveStep = useCallback(async (approvalId: ID, data?: any) => {
    try {
      await workflowService.approveWorkflowStep(approvalId, data)
      await Promise.all([refreshExecutions(), refreshApprovals()])
      
      toast({
        type: 'success',
        title: 'Step approved',
        description: 'Workflow step has been approved.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to approve step'
      
      toast({
        type: 'error',
        title: 'Approval failed',
        description: errorMessage,
      })
    }
  }, [refreshExecutions, refreshApprovals, toast])

  const rejectStep = useCallback(async (approvalId: ID, reason: string) => {
    try {
      await workflowService.rejectWorkflowStep(approvalId, reason)
      await Promise.all([refreshExecutions(), refreshApprovals()])
      
      toast({
        type: 'success',
        title: 'Step rejected',
        description: 'Workflow step has been rejected.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to reject step'
      
      toast({
        type: 'error',
        title: 'Rejection failed',
        description: errorMessage,
      })
    }
  }, [refreshExecutions, refreshApprovals, toast])

  // Bulk operations
  const bulkExecute = useCallback(async (requests: Array<{ workflowId: ID; input?: any }>): Promise<WorkflowExecution[]> => {
    try {
      const executions = await workflowService.bulkExecuteWorkflows(requests)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Bulk execution started',
        description: `${requests.length} workflows have been started.`,
      })
      
      return executions as any
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to execute workflows'
      
      toast({
        type: 'error',
        title: 'Bulk execution failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [refreshExecutions, toast])

  const bulkCancel = useCallback(async (executionIds: ID[]) => {
    try {
      await workflowService.bulkCancelExecutions(executionIds)
      await refreshExecutions()
      
      toast({
        type: 'success',
        title: 'Bulk cancellation completed',
        description: `${executionIds.length} executions have been cancelled.`,
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to cancel executions'
      
      toast({
        type: 'error',
        title: 'Bulk cancellation failed',
        description: errorMessage,
      })
    }
  }, [refreshExecutions, toast])

  // Monitoring
  const getExecutionLogs = useCallback(async (executionId: ID) => {
    try {
      return await workflowService.getExecutionLogs(executionId)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to get execution logs'
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  const getExecutionMetrics = useCallback(async (executionId: ID) => {
    try {
      return await workflowService.getExecutionMetrics(executionId)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to get execution metrics'
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Refresh all
  const refreshAll = useCallback(async () => {
    await Promise.all([
      refreshTemplates(),
      refreshExecutions(),
      refreshAnalytics(),
      refreshApprovals()
    ])
  }, [refreshTemplates, refreshExecutions, refreshAnalytics, refreshApprovals])

  // Load data on mount
  useEffect(() => {
    refreshAll()
  }, [refreshAll])

  return {
    // Templates
    templates,
    loadingTemplates,
    
    // Executions
    executions,
    loadingExecutions,
    
    // Analytics
    analytics,
    loadingAnalytics,
    
    // Approvals
    pendingApprovals,
    loadingApprovals,
    
    // Error state
    error,
    
    // Template operations
    createFromTemplate,
    duplicateWorkflow,
    
    // Execution operations
    executeWorkflow,
    pauseExecution,
    resumeExecution,
    cancelExecution,
    retryExecution,
    
    // Approval operations
    approveStep,
    rejectStep,
    
    // Bulk operations
    bulkExecute,
    bulkCancel,
    
    // Monitoring
    getExecutionLogs,
    getExecutionMetrics,
    
    // Refresh operations
    refreshTemplates,
    refreshExecutions,
    refreshAnalytics,
    refreshApprovals,
    refreshAll,
  }
}
