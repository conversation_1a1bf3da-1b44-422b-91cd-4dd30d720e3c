PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az functionapp show --name hepzlogic --resource-group docucontext --query "identity.principalId" --output tsv
3a280df5-b5ff-4c6c-911b-44f96b13eaf1
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "EventGrid Data Sender" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:55:41.500871+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/253f40ee-1ff2-4064-98af-c5a1f2e2c2d5",
  "name": "253f40ee-1ff2-4064-98af-c5a1f2e2c2d5",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/d5a91429-5739-47e2-a06b-3470a27159e7",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:55:42.068462+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "DocumentDB Account Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:56:08.990412+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/223a73cf-b071-4a54-9b9a-6b46fed933f9",
  "name": "223a73cf-b071-4a54-9b9a-6b46fed933f9",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/5bd9cd88-fe45-4216-938b-f97437e15450",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:56:09.466424+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Cosmos DB Built-in Data Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
Role 'Cosmos DB Built-in Data Contributor' doesn't exist.
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role definition list --name "*Cosmos*" --query "[].{Name:roleName, Id:name}" --output table

PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role definition list --query "[?contains(roleName, 'Cosmos')].{Name:roleName, Id:name}" --output table
Name                           Id
-----------------------------  ------------------------------------
CosmosBackupOperator           db7b14f2-5adf-42da-9f96-f2ee17bab5cb
Cosmos DB Account Reader Role  fbdf93bf-df7d-467e-a4d2-9458aa1360c8
Cosmos DB Operator             230815da-be43-4aae-9cb4-875f7bd000aa
CosmosRestoreOperator          5432c526-bc82-444a-b7ba-57c5b0b5b34f
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Cosmos DB Operator" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:57:03.263440+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/8b7486d1-648f-4bba-85e6-18f7829fed75",
  "name": "8b7486d1-648f-4bba-85e6-18f7829fed75",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/230815da-be43-4aae-9cb4-875f7bd000aa",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:57:03.552442+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Azure Service Bus Data Owner" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:57:18.094514+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/d8bb7de3-7753-4a41-9958-ac98b1871169",
  "name": "d8bb7de3-7753-4a41-9958-ac98b1871169",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/090c5cfd-751d-490a-894a-3ce6f1109419",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:57:18.672517+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "SignalR App Server" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:57:49.357666+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/0a5946b3-292b-4a6d-bed1-04194583686d",
  "name": "0a5946b3-292b-4a6d-bed1-04194583686d",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/420fcaa2-552c-430f-98ca-3264be4806c7",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:57:49.943920+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Cognitive Services User" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:58:04.105099+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/5587679d-3307-4b2d-8c0f-179bc0c52912",
  "name": "5587679d-3307-4b2d-8c0f-179bc0c52912",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/a97b65f3-24c7-4388-baec-2e87135dc908",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:58:04.369050+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Search Index Data Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:58:16.825509+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/f1611d59-4ba8-43c3-94a0-5c76902f9edc",
  "name": "f1611d59-4ba8-43c3-94a0-5c76902f9edc",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/8ebe5a00-799e-43f5-93ac-243d3dce84a7",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:58:17.104515+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Redis Cache Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T10:58:31.097621+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/3e5ef107-3d35-4de5-877f-b639d78ec677",
  "name": "3e5ef107-3d35-4de5-877f-b639d78ec677",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/e0f68234-74aa-48ed-b826-c38b57376e17",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T10:58:31.379622+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend>
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment list --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext" --query "[].{Role:roleDefinitionName, Scope:scope}" --output table
Role                            Scope
------------------------------  ------------------------------------------------------------------------------
Contributor                     /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext
EventGrid Data Sender           /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
DocumentDB Account Contributor  /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Cosmos DB Operator              /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Azure Service Bus Data Owner    /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
SignalR App Server              /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Cognitive Services User         /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Search Index Data Contributor   /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Redis Cache Contributor         /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend>
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az functionapp restart --name hepzlogic --resource-group docucontext
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "EventGrid Data Sender" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:07:38.941009+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events/providers/Microsoft.Authorization/roleAssignments/a2b95ef0-076c-4725-ab4f-f87cfb92bbf0",
  "name": "a2b95ef0-076c-4725-ab4f-f87cfb92bbf0",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/d5a91429-5739-47e2-a06b-3470a27159e7",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:07:39.243019+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "DocumentDB Account Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:07:55.782311+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/providers/Microsoft.Authorization/roleAssignments/463cc7b1-496f-4ed3-a767-5b902e06fd24",
  "name": "463cc7b1-496f-4ed3-a767-5b902e06fd24",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/5bd9cd88-fe45-4216-938b-f97437e15450",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:07:56.147318+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az cosmosdb sql role assignment create --account-name hepz --resource-group docucontext --scope "/" --principal-id 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role-definition-id ********-0000-0000-0000-********0002
{
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleAssignments/332b7a93-9ef3-463e-b878-5d726317cac0",
  "name": "332b7a93-9ef3-463e-b878-5d726317cac0",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleDefinitions/********-0000-0000-0000-********0002",        
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz",
  "type": "Microsoft.DocumentDB/databaseAccounts/sqlRoleAssignments"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az eventgrid topic show --name hepz-events --resource-group docucontext
{
  "dataResidencyBoundary": "WithinGeopair",
  "disableLocalAuth": false,
  "endpoint": "https://hepz-events.eastus-1.eventgrid.azure.net/api/events",
  "extendedLocation": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events",
  "identity": {
    "principalId": null,
    "tenantId": null,
    "type": "None",
    "userAssignedIdentities": null
  },
  "inboundIpRules": null,
  "inputSchema": "EventGridSchema",
  "inputSchemaMapping": null,
  "kind": "Azure",
  "location": "eastus",
  "metricResourceId": "cb90d361-c4c7-46a6-b440-9b764a961d2a",
  "name": "hepz-events",
  "privateEndpointConnections": null,
  "provisioningState": "Succeeded",
  "publicNetworkAccess": "Enabled",
  "resourceGroup": "docucontext",
  "sku": {
    "name": "Basic"
  },
  "systemData": null,
  "tags": null,
  "type": "Microsoft.EventGrid/topics"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "EventGrid Data Sender" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "createdOn": "2025-06-15T11:07:39.243019+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events/providers/Microsoft.Authorization/roleAssignments/a2b95ef0-076c-4725-ab4f-f87cfb92bbf0",
  "name": "a2b95ef0-076c-4725-ab4f-f87cfb92bbf0",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalName": "0489bbd7-4e97-4c84-be0d-a8b0e64b54de",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/d5a91429-5739-47e2-a06b-3470a27159e7",
  "roleDefinitionName": "EventGrid Data Sender",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:07:39.243019+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Storage Blob Data Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:09:20.135150+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/781fa248-a808-4839-8923-01810c04d0db",
  "name": "781fa248-a808-4839-8923-01810c04d0db",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/ba92f5b4-2d11-453d-a403-e96b0029c9fe",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:09:20.524156+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Storage Queue Data Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:09:50.249460+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/f072816e-7dca-4e3c-9cfd-53d57d1ac702",
  "name": "f072816e-7dca-4e3c-9cfd-53d57d1ac702",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/974c5e8b-45b9-4653-ba55-5f855dd0fb88",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:09:50.470464+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Storage Table Data Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:10:01.545804+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/69cecc64-c435-47ad-913a-8c87ee774df5",
  "name": "69cecc64-c435-47ad-913a-8c87ee774df5",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/0a9a7e1f-b9d0-4cc4-a60d-0319b160aaa3",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:10:02.174813+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Key Vault Secrets User" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:10:15.054196+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/4e636cda-a540-4244-ba59-4a0f8bd89371",
  "name": "4e636cda-a540-4244-ba59-4a0f8bd89371",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/4633458b-17de-408a-b874-0445c86b69e6",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:10:15.383203+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment create --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --role "Application Insights Component Contributor" --scope "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext"
{
  "condition": null,
  "conditionVersion": null,
  "createdBy": null,
  "createdOn": "2025-06-15T11:10:28.319052+00:00",
  "delegatedManagedIdentityResourceId": null,
  "description": null,
  "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.Authorization/roleAssignments/b86f2b3b-4290-47c0-bc5e-83b014297c75",
  "name": "b86f2b3b-4290-47c0-bc5e-83b014297c75",
  "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
  "principalType": "ServicePrincipal",
  "resourceGroup": "docucontext",
  "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/providers/Microsoft.Authorization/roleDefinitions/ae349356-3a1b-4a5e-921d-050484c6347e",
  "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext",
  "type": "Microsoft.Authorization/roleAssignments",
  "updatedBy": "a0d65b1f-d804-4e3d-ad98-63624861babf",
  "updatedOn": "2025-06-15T11:10:28.615057+00:00"
}
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment list --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --query "[].{Role:roleDefinitionName, Scope:scope}" --output table

PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az role assignment list --assignee 3a280df5-b5ff-4c6c-911b-44f96b13eaf1 --all --query "[?contains(scope, 'docucontext')].{Role:roleDefinitionName, Scope:scope}" --output table
Role                                        Scope
------------------------------------------  -----------------------------------------------------------------------------------------------------------------------------------
EventGrid Data Sender                       /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
DocumentDB Account Contributor              /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Cosmos DB Operator                          /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Azure Service Bus Data Owner                /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
SignalR App Server                          /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Cognitive Services User                     /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Search Index Data Contributor               /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Redis Cache Contributor                     /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
EventGrid Data Sender                       /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.EventGrid/topics/hepz-events
DocumentDB Account Contributor              /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz
Storage Blob Data Contributor               /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Storage Queue Data Contributor              /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Storage Table Data Contributor              /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Key Vault Secrets User                      /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
Application Insights Component Contributor  /subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az cosmosdb sql role assignment list --account-name hepz --resource-group docucontext
[
  {
    "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleAssignments/332b7a93-9ef3-463e-b878-5d726317cac0",
    "name": "332b7a93-9ef3-463e-b878-5d726317cac0",
    "principalId": "3a280df5-b5ff-4c6c-911b-44f96b13eaf1",
    "resourceGroup": "docucontext",
    "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleDefinitions/********-0000-0000-0000-********0002",      
    "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz",
    "type": "Microsoft.DocumentDB/databaseAccounts/sqlRoleAssignments"
  },
  {
    "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleAssignments/1424b6bc-9e2f-4aad-be04-cc9f9fab19f2",
    "name": "1424b6bc-9e2f-4aad-be04-cc9f9fab19f2",
    "principalId": "a0d65b1f-d804-4e3d-ad98-63624861babf",
    "resourceGroup": "docucontext",
    "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleDefinitions/********-0000-0000-0000-********0002",      
    "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz",
    "type": "Microsoft.DocumentDB/databaseAccounts/sqlRoleAssignments"
  },
  {
    "id": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleAssignments/55c8b534-4114-4261-bbed-44194bc8806e",
    "name": "55c8b534-4114-4261-bbed-44194bc8806e",
    "principalId": "dc649386-a325-4705-aa67-983f57631e46",
    "resourceGroup": "docucontext",
    "roleDefinitionId": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz/sqlRoleDefinitions/********-0000-0000-0000-********0002",      
    "scope": "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/docucontext/providers/Microsoft.DocumentDB/databaseAccounts/hepz",
    "type": "Microsoft.DocumentDB/databaseAccounts/sqlRoleAssignments"
  }
]
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> az functionapp restart --name hepzlogic --resource-group docucontext
PS C:\Users\<USER>\Downloads\FullStack-production\production-frontend> 