"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useProjects } from "@/hooks/projects";
import { useOrganizations } from "@/hooks/organizations";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON>bsContent } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import {
  FolderKanban,
  Plus,
  Search,
  Brain,
  Zap,
  Users,
  Workflow,
  FileText,
  Settings,
  Square,
  RefreshCw,
  Upload,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Loader2,
  MoreHorizontal
} from "lucide-react";
import { ProjectCard } from "@/components/projects/project-card";
import { EmptyState } from "@/components/empty-state";
import { ProjectVisibility } from "@/types/project";
import {
  useAIOperations,
  useAIOperationsSummary,
  useStartAIOperation,
  useCancelAIOperation,
  useRetryAIOperation
} from "@/stores";
import { useToast } from "@/components/ui/use-toast";

export default function ProjectsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<ProjectVisibility | "ALL">("ALL");
  const [mainTab, setMainTab] = useState("projects");
  const [bulkFiles, setBulkFiles] = useState<File[]>([]);

  const { currentOrganization } = useOrganizations();
  const { toast } = useToast();

  const {
    projects,
    isLoading,
    refetch
  } = useProjects({
    organizationId: currentOrganization?.id,
  });

  // AI Operations hooks
  const operations = useAIOperations();
  const operationsSummary = useAIOperationsSummary();
  const startAIOperation = useStartAIOperation();
  const cancelAIOperation = useCancelAIOperation();
  const retryAIOperation = useRetryAIOperation();

  // Refetch projects when organization changes
  useEffect(() => {
    if (currentOrganization?.id) {
      refetch();
    }
  }, [currentOrganization?.id, refetch]);

  // Filter projects based on search query and active tab
  const filteredProjects = projects.filter((project) => {
    const matchesSearch = project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (project.description && project.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesTab = activeTab === "ALL" || project.visibility === activeTab;

    return matchesSearch && matchesTab;
  });

  // AI Operations functions
  const handleBulkProcessing = async () => {
    if (bulkFiles.length === 0 || !currentOrganization?.id) return;

    try {
      await startAIOperation({
        type: 'BATCH_PROCESSING',
        parameters: {
          files: bulkFiles.map(f => f.name),
          operationType: 'DOCUMENT_ANALYSIS',
          batchSize: 5,
          parallelProcessing: true
        },
        organizationId: currentOrganization.id,
        priority: 'NORMAL'
      });

      setBulkFiles([]);
      toast({
        title: 'Batch processing started',
        description: `Processing ${bulkFiles.length} files.`,
      });
    } catch (error: any) {
      toast({
        title: 'Failed to start batch processing',
        description: error.message,
        variant: 'destructive'
      });
    }
  };

  const getOperationStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Projects</h1>
          <p className="text-muted-foreground">
            {currentOrganization
              ? `Manage projects, AI operations, and workflows for ${currentOrganization.name}`
              : "Select an organization to view its projects"}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {/* Quick stats */}
          {currentOrganization && (
            <div className="flex items-center gap-4 text-sm">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{operationsSummary.completed}</div>
                <div className="text-xs text-muted-foreground">Completed</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{operationsSummary.processing}</div>
                <div className="text-xs text-muted-foreground">Processing</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-yellow-600">{operationsSummary.pending}</div>
                <div className="text-xs text-muted-foreground">Pending</div>
              </div>
            </div>
          )}
          <Button
            asChild
            disabled={!currentOrganization}
          >
            <Link href={currentOrganization ? `/projects/create?organizationId=${currentOrganization.id}` : "#"}>
              <Plus className="mr-2 h-4 w-4" />
              Create Project
            </Link>
          </Button>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={mainTab} onValueChange={setMainTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="projects">
            <FolderKanban className="h-4 w-4 mr-2" />
            Projects
          </TabsTrigger>
          <TabsTrigger value="ai-operations">
            <Brain className="h-4 w-4 mr-2" />
            AI Operations
          </TabsTrigger>
          <TabsTrigger value="batch-processing">
            <Zap className="h-4 w-4 mr-2" />
            Batch Processing
          </TabsTrigger>
          <TabsTrigger value="workflows">
            <Workflow className="h-4 w-4 mr-2" />
            Workflows
          </TabsTrigger>
          <TabsTrigger value="team">
            <Users className="h-4 w-4 mr-2" />
            Team
          </TabsTrigger>
        </TabsList>

        {/* Projects Tab */}
        <TabsContent value="projects" className="space-y-4">
          {!currentOrganization ? (
            <EmptyState
              icon={<FolderKanban className="h-10 w-10 text-muted-foreground" />}
              title="No organization selected"
              description="Select an organization from the dropdown to view its projects"
              action={
                <Button asChild>
                  <Link href="/organizations">
                    View Organizations
                  </Link>
                </Button>
              }
            />
          ) : (
            <>
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search projects..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <Tabs
                  defaultValue="ALL"
                  value={activeTab}
                  onValueChange={(value) => setActiveTab(value as ProjectVisibility | "ALL")}
                  className="w-full md:w-auto"
                >
                  <TabsList className="grid grid-cols-4 w-full md:w-auto">
                    <TabsTrigger value="ALL">All</TabsTrigger>
                    <TabsTrigger value={ProjectVisibility.PRIVATE}>Private</TabsTrigger>
                    <TabsTrigger value={ProjectVisibility.ORGANIZATION}>Organization</TabsTrigger>
                    <TabsTrigger value={ProjectVisibility.PUBLIC}>Public</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-48 w-full" />
                  ))}
                </div>
              ) : filteredProjects.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredProjects.map((project) => (
                    <ProjectCard
                      key={project.id}
                      project={project}
                    />
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={<FolderKanban className="h-10 w-10 text-muted-foreground" />}
                  title={searchQuery ? "No projects found" : "No projects"}
                  description={
                    searchQuery
                      ? `No projects match "${searchQuery}"`
                      : `You don't have any projects in ${currentOrganization.name} yet. Create your first project to get started.`
                  }
                  action={
                    <Button asChild>
                      <Link href={`/projects/create?organizationId=${currentOrganization.id}`}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Project
                      </Link>
                    </Button>
                  }
                />
              )}
            </>
          )}
        </TabsContent>

        {/* AI Operations Tab */}
        <TabsContent value="ai-operations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Brain className="h-5 w-5 mr-2" />
                AI Operations
              </CardTitle>
              <CardDescription>
                Monitor and manage AI operations across all projects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {operations.map((operation) => (
                  <div key={operation.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getOperationStatusIcon(operation.status)}
                        <div>
                          <div className="font-medium">{operation.type}</div>
                          <div className="text-sm text-muted-foreground">
                            Started {new Date(operation.createdAt).toLocaleString()}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{operation.status}</Badge>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            if (operation.status === 'running') {
                              try {
                                await cancelAIOperation(operation.id);
                                toast({
                                  title: 'Operation cancelled',
                                  description: 'The operation has been cancelled successfully.',
                                });
                              } catch (error: any) {
                                toast({
                                  title: 'Failed to cancel operation',
                                  description: error.message,
                                  variant: 'destructive'
                                });
                              }
                            } else if (operation.status === 'failed') {
                              try {
                                await retryAIOperation(operation.id);
                                toast({
                                  title: 'Operation retried',
                                  description: 'A new operation has been started.',
                                });
                              } catch (error: any) {
                                toast({
                                  title: 'Failed to retry operation',
                                  description: error.message,
                                  variant: 'destructive'
                                });
                              }
                            }
                          }}
                        >
                          {operation.status === 'running' ? (
                            <>
                              <Square className="h-3 w-3 mr-1" />
                              Cancel
                            </>
                          ) : operation.status === 'failed' ? (
                            <>
                              <RefreshCw className="h-3 w-3 mr-1" />
                              Retry
                            </>
                          ) : (
                            <>
                              <MoreHorizontal className="h-3 w-3" />
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    {typeof operation.progress === 'number' && operation.progress > 0 && (
                      <div className="mt-3">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span>Progress</span>
                          <span>{operation.progress}%</span>
                        </div>
                        <Progress value={operation.progress} className="h-2" />
                      </div>
                    )}
                  </div>
                ))}

                {operations.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No AI operations yet</p>
                    <p className="text-sm">Operations will appear here when you start processing documents</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Batch Processing Tab */}
        <TabsContent value="batch-processing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="h-5 w-5 mr-2" />
                Batch Processing
              </CardTitle>
              <CardDescription>
                Process multiple documents simultaneously with AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Upload Documents</label>
                <div className="mt-2 border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    Drag and drop files here, or click to select
                  </p>
                  <Button variant="outline" className="mt-4">
                    Select Files
                  </Button>
                </div>
              </div>

              {bulkFiles.length > 0 && (
                <div>
                  <div className="text-sm font-medium mb-2">
                    Selected Files ({bulkFiles.length})
                  </div>
                  <div className="space-y-2">
                    {bulkFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">{file.name}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setBulkFiles(prev => prev.filter((_, i) => i !== index))}
                        >
                          <XCircle className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button
                onClick={handleBulkProcessing}
                disabled={bulkFiles.length === 0}
                className="w-full"
              >
                <Zap className="h-4 w-4 mr-2" />
                Start Batch Processing
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Workflow className="h-5 w-5 mr-2" />
                Workflows
              </CardTitle>
              <CardDescription>
                Automate document processing with custom workflows
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Workflow className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">Workflow Management</p>
                <p className="text-sm mb-4">Create and manage automated workflows for document processing</p>
                <div className="space-y-2">
                  <Button className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Workflow
                  </Button>
                  <Button variant="outline" className="w-full">
                    <FileText className="h-4 w-4 mr-2" />
                    Browse Templates
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Team Tab */}
        <TabsContent value="team" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Team Management
              </CardTitle>
              <CardDescription>
                Manage team members and permissions across projects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">Team Collaboration</p>
                <p className="text-sm mb-4">Invite team members and manage project access</p>
                <div className="space-y-2">
                  <Button className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Invite Members
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Permissions
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
