"use client";

import { ReactNode } from "react";
import { <PERSON>ton, ButtonProps } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { PermissionAction, hasPermission, hasAllPermissions, hasAnyPermission } from "@/lib/permissions";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface PermissionButtonProps extends ButtonProps {
  /**
   * Permission required to enable the button
   */
  permission?: PermissionAction;

  /**
   * Multiple permissions required (all must be present)
   */
  permissions?: PermissionAction[];

  /**
   * Multiple permissions where any one is sufficient
   */
  anyPermission?: PermissionAction[];

  /**
   * Content to render inside the button
   */
  children: ReactNode;

  /**
   * Whether to hide the button completely if user doesn't have permission
   */
  hideIfNoPermission?: boolean;

  /**
   * Tooltip text to show when button is disabled due to permissions
   */
  permissionTooltip?: string;

  /**
   * Organization ID for organization-specific permissions
   */
  organizationId?: string;

  /**
   * Project ID for project-specific permissions
   */
  projectId?: string;
}

/**
 * Button that is enabled/disabled based on user permissions
 */
export function PermissionButton({
  permission,
  permissions,
  anyPermission,
  children,
  hideIfNoPermission = false,
  permissionTooltip = "You don't have permission to perform this action",
  organizationId,
  projectId,
  ...buttonProps
}: PermissionButtonProps) {
  const { user } = useAuth();

  // Check if user has the required permission(s)
  const hasRequiredPermission = permission
    ? hasPermission(user, permission)
    : permissions
    ? hasAllPermissions(user, permissions)
    : anyPermission
    ? hasAnyPermission(user, anyPermission)
    : true;

  // If user doesn't have permission and we should hide the button
  if (!hasRequiredPermission && hideIfNoPermission) {
    return null;
  }

  // If user doesn't have permission, disable the button and add tooltip
  if (!hasRequiredPermission) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button {...buttonProps} disabled>
              {children}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{permissionTooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // User has permission, render normal button
  return <Button {...buttonProps}>{children}</Button>;
}
