/**
 * Template Sharing Hook
 * Manages template sharing operations and permissions
 */

import { useState, useCallback, useEffect } from 'react'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'

export interface ShareEntity {
  id: ID
  name: string
  email?: string
  type: 'user' | 'organization' | 'team'
  avatar?: string
}

export interface SharingPermission {
  id: ID
  entityId: ID
  entityType: 'user' | 'organization' | 'team'
  permission: 'view' | 'edit' | 'admin'
  grantedBy: ID
  grantedAt: string
  expiresAt?: string
}

export interface UseTemplateSharingOptions {
  templateId: ID
}

export interface UseTemplateSharingResult {
  // State
  sharedEntities: SharingPermission[]
  availableEntities: ShareEntity[]
  loading: boolean
  error: string | null
  
  // Search
  searchQuery: string
  setSearchQuery: (query: string) => void
  filteredEntities: ShareEntity[]
  
  // Sharing operations
  shareWithEntity: (entityId: ID, permission: string) => Promise<void>
  updatePermission: (sharingId: ID, permission: string) => Promise<void>
  removeSharing: (sharingId: ID) => Promise<void>
  
  // Bulk operations
  shareWithMultiple: (entities: { entityId: ID; permission: string }[]) => Promise<void>
  
  // Public sharing
  makePublic: () => Promise<void>
  makePrivate: () => Promise<void>
  isPublic: boolean
  
  // Link sharing
  generateShareLink: (permission: string, expiresAt?: string) => Promise<string>
  revokeShareLink: (linkId: ID) => Promise<void>
  shareLinks: Array<{
    id: ID
    url: string
    permission: string
    createdAt: string
    expiresAt?: string
    usageCount: number
  }>
  
  // Refresh data
  refresh: () => Promise<void>
}

export function useTemplateSharing(options: UseTemplateSharingOptions): UseTemplateSharingResult {
  const { templateId } = options
  const { toast } = useToast()
  
  const [sharedEntities, setSharedEntities] = useState<SharingPermission[]>([])
  const [availableEntities, setAvailableEntities] = useState<ShareEntity[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isPublic, setIsPublic] = useState(false)
  const [shareLinks, setShareLinks] = useState<any[]>([])

  // Filter entities based on search query
  const filteredEntities = availableEntities.filter(entity => {
    const query = searchQuery.toLowerCase()
    return (
      entity.name.toLowerCase().includes(query) ||
      entity.email?.toLowerCase().includes(query)
    )
  })

  // Load sharing data
  const loadSharingData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const [sharingResponse, entitiesResponse] = await Promise.all([
        fetch(`/templates/${templateId}/sharing`),
        fetch(`/templates/sharing/entities`)
      ])

      if (!sharingResponse.ok || !entitiesResponse.ok) {
        throw new Error('Failed to load sharing data')
      }

      const [sharing, entities] = await Promise.all([
        sharingResponse.json(),
        entitiesResponse.json()
      ])

      setSharedEntities(sharing.data || [])
      setAvailableEntities(entities.data || [])
      setIsPublic(sharing.isPublic || false)
      setShareLinks([])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load sharing data'
      setError(errorMessage)
      
      toast({
        title: 'Loading failed',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [templateId, toast])

  // Share with entity
  const shareWithEntity = useCallback(async (_entityId: ID, _permission: string) => {
    try {
      const response = await fetch(`/templates/${templateId}/share`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userIds: [_entityId],
          permissions: [_permission]
        })
      })

      if (!response.ok) {
        throw new Error('Failed to share template')
      }

      await loadSharingData()

      toast({
        title: 'Shared successfully',
        description: 'Template has been shared with the selected entity.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to share template'

      toast({
        title: 'Sharing failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, loadSharingData, toast])

  // Update permission
  const updatePermission = useCallback(async (_sharingId: ID, _permission: string) => {
    try {
      // TODO: Implement template sharing update endpoint
      // await templateService.updateTemplateSharing(templateId, sharingId, { permission })
      await loadSharingData()

      toast({
        title: 'Permission updated',
        description: 'Sharing permission has been updated.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update permission'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, loadSharingData, toast])

  // Remove sharing
  const removeSharing = useCallback(async (_sharingId: ID) => {
    try {
      // TODO: Implement template sharing removal endpoint
      // await templateService.removeTemplateSharing(templateId, sharingId)
      await loadSharingData()

      toast({
        title: 'Sharing removed',
        description: 'Template sharing has been removed.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to remove sharing'

      toast({
        title: 'Remove failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, loadSharingData, toast])

  // Bulk sharing
  const shareWithMultiple = useCallback(async (entities: { entityId: ID; permission: string }[]) => {
    try {
      // TODO: Implement bulk template sharing endpoint
      // await Promise.all(
      //   entities.map(entity =>
      //     templateService.shareTemplate(templateId, {
      //       entityId: entity.entityId,
      //       entityType: 'user',
      //       permission: entity.permission
      //     })
      //   )
      // )

      await loadSharingData()

      toast({
        title: 'Bulk sharing completed',
        description: `Template shared with ${entities.length} entities.`,
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to share with multiple entities'

      toast({
        title: 'Bulk sharing failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, loadSharingData, toast])

  // Public sharing
  const makePublic = useCallback(async () => {
    try {
      // TODO: Implement template visibility update endpoint
      // await templateService.updateTemplateVisibility(templateId, { isPublic: true })
      setIsPublic(true)

      toast({
        title: 'Template made public',
        description: 'Template is now publicly accessible.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to make template public'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, toast])

  const makePrivate = useCallback(async () => {
    try {
      // TODO: Implement template visibility update endpoint
      // await templateService.updateTemplateVisibility(templateId, { isPublic: false })
      setIsPublic(false)

      toast({
        title: 'Template made private',
        description: 'Template is now private.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to make template private'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, toast])

  // Link sharing
  const generateShareLink = useCallback(async (_permission: string, _expiresAt?: string): Promise<string> => {
    try {
      // TODO: Implement template share link generation endpoint
      // const link = await templateService.generateTemplateShareLink(templateId, {
      //   permission,
      //   expiresAt
      // })

      await loadSharingData()

      toast({
        title: 'Share link generated',
        description: 'Share link has been created.',
      })

      return 'https://example.com/share/template-link' // Placeholder URL
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to generate share link'

      toast({
        title: 'Link generation failed',
        description: errorMessage,
        variant: 'destructive',
      })

      throw err
    }
  }, [templateId, loadSharingData, toast])

  const revokeShareLink = useCallback(async (_linkId: ID) => {
    try {
      // TODO: Implement template share link revocation endpoint
      // await templateService.revokeTemplateShareLink(templateId, linkId)
      await loadSharingData()

      toast({
        title: 'Share link revoked',
        description: 'Share link has been revoked.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to revoke share link'

      toast({
        title: 'Revoke failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [templateId, loadSharingData, toast])

  // Load data on mount
  useEffect(() => {
    loadSharingData()
  }, [loadSharingData])

  return {
    // State
    sharedEntities,
    availableEntities,
    loading,
    error,
    
    // Search
    searchQuery,
    setSearchQuery,
    filteredEntities,
    
    // Sharing operations
    shareWithEntity,
    updatePermission,
    removeSharing,
    
    // Bulk operations
    shareWithMultiple,
    
    // Public sharing
    makePublic,
    makePrivate,
    isPublic,
    
    // Link sharing
    generateShareLink,
    revokeShareLink,
    shareLinks,
    
    // Refresh
    refresh: loadSharingData,
  }
}
