/**
 * Search Hook
 * Manages search operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'

export interface SearchResult {
  id: ID
  type: 'document' | 'project' | 'template' | 'workflow' | 'user' | 'organization'
  title: string
  description?: string
  content?: string
  url: string
  score: number
  highlights: string[]
  metadata: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface SearchFilters {
  types?: string[]
  dateRange?: {
    start: string
    end: string
  }
  tags?: string[]
  status?: string[]
  projectId?: ID
  organizationId?: ID
  userId?: ID
}

export interface SearchOptions {
  query: string
  filters?: SearchFilters
  limit?: number
  offset?: number
  sortBy?: 'relevance' | 'date' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface UseSearchResult {
  results: SearchResult[]
  loading: boolean
  error: string | null
  total: number
  hasMore: boolean

  // Missing properties expected by components
  searchResults: any
  isSearching: boolean
  handleSearch: (options: any) => Promise<void>
  handleResultClick: (id: string, position: number) => void

  search: (options: SearchOptions) => Promise<void>
  loadMore: () => Promise<void>
  clearResults: () => void

  // Quick search methods
  searchDocuments: (query: string, filters?: SearchFilters) => Promise<SearchResult[]>
  searchProjects: (query: string, filters?: SearchFilters) => Promise<SearchResult[]>
  searchTemplates: (query: string, filters?: SearchFilters) => Promise<SearchResult[]>
  searchUsers: (query: string, filters?: SearchFilters) => Promise<SearchResult[]>
}

export function useSearch(): UseSearchResult {
  const { toast } = useToast()
  
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [total, setTotal] = useState(0)
  const [currentOptions, setCurrentOptions] = useState<SearchOptions | null>(null)
  const [searchQuery, setSearchQuery] = useState<string>('')

  const search = useCallback(async (options: SearchOptions) => {
    setLoading(true)
    setError(null)
    setCurrentOptions(options)
    setSearchQuery(options.query)

    try {
      // Search using backend API client
      const response = await backendApiClient.request('/search', {
        method: 'POST',
        body: JSON.stringify(options)
      })

      setResults(response.results || [])
      setTotal(response.total || 0)
    } catch (err: any) {
      const errorMessage = err.message || 'Search failed'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Search failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  const loadMore = useCallback(async () => {
    if (!currentOptions || loading) return

    setLoading(true)

    try {
      const nextOptions = {
        ...currentOptions,
        offset: (currentOptions.offset || 0) + (currentOptions.limit || 20)
      }

      // Load more results using backend API client
      const response = await backendApiClient.request('/search', {
        method: 'POST',
        body: JSON.stringify(nextOptions)
      })
      
      setResults(prev => [...prev, ...(response.results || [])])
      setCurrentOptions(nextOptions)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load more results'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Load more failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [currentOptions, loading, toast])

  const clearResults = useCallback(() => {
    setResults([])
    setTotal(0)
    setCurrentOptions(null)
    setError(null)
  }, [])

  // Quick search methods
  const searchDocuments = useCallback(async (query: string, filters?: SearchFilters): Promise<SearchResult[]> => {
    try {
      const options: SearchOptions = {
        query,
        filters: {
          ...filters,
          types: ['document']
        }
      }
      
      // Search documents using backend API
      const response = await backendApiClient.request('/search/documents', {
        method: 'POST',
        body: JSON.stringify(options)
      })
      return response.results || []
    } catch (err: any) {
      toast({
        type: 'error',
        title: 'Document search failed',
        description: err.message || 'Failed to search documents',
      })
      throw err
    }
  }, [toast])

  const searchProjects = useCallback(async (query: string, filters?: SearchFilters): Promise<SearchResult[]> => {
    try {
      const options: SearchOptions = {
        query,
        filters: {
          ...filters,
          types: ['project']
        }
      }
      
      // Search projects using backend API
      const response = await backendApiClient.request('/search/projects', {
        method: 'POST',
        body: JSON.stringify(options)
      })
      return response.results || []
    } catch (err: any) {
      toast({
        type: 'error',
        title: 'Project search failed',
        description: err.message || 'Failed to search projects',
      })
      throw err
    }
  }, [toast])

  const searchTemplates = useCallback(async (query: string, filters?: SearchFilters): Promise<SearchResult[]> => {
    try {
      const options: SearchOptions = {
        query,
        filters: {
          ...filters,
          types: ['template']
        }
      }
      
      // Search templates using backend API
      const response = await backendApiClient.request('/search/templates', {
        method: 'POST',
        body: JSON.stringify(options)
      })
      return response.results || []
    } catch (err: any) {
      toast({
        type: 'error',
        title: 'Template search failed',
        description: err.message || 'Failed to search templates',
      })
      throw err
    }
  }, [toast])

  const searchUsers = useCallback(async (query: string, filters?: SearchFilters): Promise<SearchResult[]> => {
    try {
      const options: SearchOptions = {
        query,
        filters: {
          ...filters,
          types: ['user']
        }
      }
      
      // Search users using backend API
      const response = await backendApiClient.request('/search/users', {
        method: 'POST',
        body: JSON.stringify(options)
      })
      return response.results || []
    } catch (err: any) {
      toast({
        type: 'error',
        title: 'User search failed',
        description: err.message || 'Failed to search users',
      })
      throw err
    }
  }, [toast])

  const hasMore = results.length < total

  // Handle result click
  const handleResultClick = useCallback(async (id: string, position: number) => {
    try {
      // Track result click for analytics
      await backendApiClient.request('/analytics/search/click', {
        method: 'POST',
        body: JSON.stringify({
          resultId: id,
          position,
          query: searchQuery,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      console.warn('Failed to track search result click:', error)
    }
  }, [searchQuery])

  // Handle search with different interface
  const handleSearch = useCallback(async (options: any) => {
    await search({
      query: options.query || '',
      filters: options.filters
    })
  }, [search])

  return {
    results,
    loading,
    error,
    total,
    hasMore,

    // Missing properties expected by components
    searchResults: { results, total },
    isSearching: loading,
    handleSearch,
    handleResultClick,

    search,
    loadMore,
    clearResults,
    searchDocuments,
    searchProjects,
    searchTemplates,
    searchUsers,
  }
}

export default useSearch
