"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, Users, FolderKanban, Calendar, Database, Shield } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Organization } from "@/types/organization";
import { cn } from "@/lib/utils";

interface OrganizationCardProps {
  organization: Organization;
  className?: string;
  showActions?: boolean;
}

export function OrganizationCard({
  organization,
  className,
  showActions = true
}: OrganizationCardProps) {
  // Determine badge variant based on tier
  const tierVariant =
    organization.tier === 'ENTERPRISE' ? "default" :
    organization.tier === 'PROFESSIONAL' ? "secondary" :
    "outline";

  // Determine badge label based on tier
  const tierLabel =
    organization.tier === 'ENTERPRISE' ? "Enterprise" :
    organization.tier === 'PROFESSIONAL' ? "Professional" :
    "Free";

  // Calculate storage usage percentage if available
  const storageUsed = organization.storageUsed || 0;
  const storageLimit = organization.settings?.maxFileSize || 0;
  const storagePercentage = storageLimit > 0 ? Math.min(100, (storageUsed / storageLimit) * 100) : 0;

  // Format storage values
  const formatStorage = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  return (
    <Card className={cn("w-full hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg flex items-center gap-2">
            <Building2 className="h-5 w-5 text-primary" />
            {organization.name}
          </CardTitle>
          <Badge variant={tierVariant}>{tierLabel}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        {organization.description && (
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{organization.description}</p>
        )}

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 text-sm">
            <FolderKanban size={14} className="text-muted-foreground" />
            <span>{organization.projectIds?.length || 0} Projects</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Users size={14} className="text-muted-foreground" />
            <span>{organization.memberIds?.length || 0} Members</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Database size={14} className="text-muted-foreground" />
            <span>{formatStorage(storageUsed)} used</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Shield size={14} className="text-muted-foreground" />
            <span>{
              typeof organization.settings?.features === 'object' &&
              !Array.isArray(organization.settings?.features) &&
              organization.settings?.features?.aiAnalysis ? 'AI Enabled' : 'Basic'
            }</span>
          </div>
        </div>

        {/* Storage usage bar */}
        {storageLimit > 0 && (
          <div className="w-full bg-muted rounded-full h-2 mb-4">
            <div
              className={cn(
                "h-2 rounded-full",
                storagePercentage > 90 ? "bg-destructive" :
                storagePercentage > 70 ? "bg-warning" :
                "bg-primary"
              )}
              style={{ width: `${storagePercentage}%` }}
            />
          </div>
        )}

        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Calendar size={12} />
          <span>Created {formatDistanceToNow(new Date(organization.createdAt))} ago</span>
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="flex gap-2">
          <Button asChild variant="default" className="flex-1">
            <Link href={`/organizations/${organization.id}`}>
              View Details
            </Link>
          </Button>
          <Button asChild variant="outline" className="flex-1">
            <Link href={`/projects/create?organizationId=${organization.id}`}>
              New Project
            </Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
