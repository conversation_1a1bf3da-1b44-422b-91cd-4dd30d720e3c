"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorDisplay } from "@/components/ui/error-display";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
// import { documentDiffService } from "@/services/document-diff-service";
import {
  ArrowLeft,
  ArrowRight,
  Clock,
  FileText,
  GitBranch,
  GitCompare,
  RotateCc<PERSON>,
  User,
} from "lucide-react";

export interface DocumentVersion {
  id: string;
  versionNumber: number;
  createdAt: string;
  createdBy: {
    id: string;
    name: string;
    avatarUrl?: string;
  };
  changes?: {
    added: number;
    removed: number;
    modified: number;
  };
  comment?: string;
}

export interface VersionComparisonProps {
  documentId: string;
  versions: DocumentVersion[];
  isLoading?: boolean;
  error?: Error | null;
  onFetchVersionContent?: (versionId: string) => Promise<string>;
  onRestoreVersion?: (versionId: string) => Promise<void>;
  className?: string;
}

export function VersionComparison({
  documentId,
  versions,
  isLoading = false,
  error = null,
  onFetchVersionContent,
  onRestoreVersion,
  className,
}: VersionComparisonProps) {
  const [leftVersionId, setLeftVersionId] = useState<string>("");
  const [rightVersionId, setRightVersionId] = useState<string>("");
  const [leftContent, setLeftContent] = useState<string>("");
  const [rightContent, setRightContent] = useState<string>("");
  const [isLoadingContent, setIsLoadingContent] = useState(false);
  const [contentError, setContentError] = useState<Error | null>(null);
  const [diffView, setDiffView] = useState<"split" | "unified">("split");
  const [isRestoring, setIsRestoring] = useState(false);

  // Set initial versions when data is loaded
  useEffect(() => {
    if (versions.length >= 2) {
      setRightVersionId(versions[0].id); // Latest version
      setLeftVersionId(versions[1].id); // Previous version
    } else if (versions.length === 1) {
      setRightVersionId(versions[0].id);
      setLeftVersionId("");
    }
  }, [versions]);

  // Fetch version content when versions change
  useEffect(() => {
    if (!onFetchVersionContent) return;

    const fetchContent = async () => {
      setIsLoadingContent(true);
      setContentError(null);

      try {
        // Fetch left version content if selected
        if (leftVersionId) {
          const content = await onFetchVersionContent(leftVersionId);
          setLeftContent(content);
        } else {
          setLeftContent("");
        }

        // Fetch right version content if selected
        if (rightVersionId) {
          const content = await onFetchVersionContent(rightVersionId);
          setRightContent(content);
        } else {
          setRightContent("");
        }
      } catch (err) {
        console.error("Error fetching version content:", err);
        setContentError(err instanceof Error ? err : new Error("Failed to fetch version content"));
      } finally {
        setIsLoadingContent(false);
      }
    };

    fetchContent();
  }, [leftVersionId, rightVersionId, onFetchVersionContent]);

  // Handle version restore
  const handleRestoreVersion = useCallback(async (versionId: string) => {
    if (!onRestoreVersion) return;

    try {
      setIsRestoring(true);
      await onRestoreVersion(versionId);
    } catch (err) {
      console.error("Error restoring version:", err);
    } finally {
      setIsRestoring(false);
    }
  }, [onRestoreVersion]);

  // Get version by ID
  const getVersionById = useCallback((versionId: string) => {
    return versions.find((v) => v.id === versionId);
  }, [versions]);

  // Render diff between two versions
  const renderDiff = useCallback(() => {
    if (isLoadingContent) {
      return <LoadingState title="Loading version content..." variant="spinner" />;
    }

    if (contentError) {
      return (
        <ErrorDisplay
          title="Failed to load version content"
          error={contentError}
          variant="card"
        />
      );
    }

    if (!leftContent && !rightContent) {
      return (
        <div className="text-center p-6">
          <p className="text-muted-foreground">Select versions to compare</p>
        </div>
      );
    }

    // Calculate real diff using document diff service
    const [diffResult, setDiffResult] = React.useState<any>(null);
    const [isDiffLoading, setIsDiffLoading] = React.useState(false);

    React.useEffect(() => {
      const calculateDiff = async () => {
        if (!leftContent || !rightContent) return;

        setIsDiffLoading(true);
        try {
          const { documentDiffService } = await import('../../services/document-diff-service');
          const result = await documentDiffService.compareContent(leftContent, rightContent);
          setDiffResult(result);
        } catch (error) {
          console.error('Failed to calculate diff:', error);
          // Fallback to simple line-by-line comparison
          setDiffResult({
            unified: [
              { added: false, removed: false, value: leftContent },
              { added: true, removed: false, value: rightContent }
            ],
            split: {
              left: [{ added: false, removed: true, value: leftContent }],
              right: [{ added: true, removed: false, value: rightContent }]
            },
            statistics: {
              addedLines: 1,
              removedLines: 1,
              modifiedLines: 0,
              totalChanges: 2,
              similarity: 0
            }
          });
        } finally {
          setIsDiffLoading(false);
        }
      };

      calculateDiff();
    }, [leftContent, rightContent]);

    if (isDiffLoading) {
      return (
        <div className="border rounded-md p-4">
          <div className="flex items-center justify-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span className="text-sm text-muted-foreground">Calculating differences...</span>
          </div>
        </div>
      );
    }

    if (!diffResult) {
      return (
        <div className="border rounded-md p-4">
          <div className="text-center text-muted-foreground">
            Unable to calculate differences
          </div>
        </div>
      );
    }

    if (diffView === "unified") {
      // Unified diff view
      return (
        <div className="border rounded-md overflow-hidden">
          <div className="bg-muted p-2 text-sm font-medium">Unified Diff</div>
          <div className="p-4 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-[500px]">
            {diffResult.unified.map((part: any, i: number) => {
              if (part.added) {
                return <div key={i} className="bg-green-50 text-green-700">+ {part.value}</div>;
              } else if (part.removed) {
                return <div key={i} className="bg-red-50 text-red-700">- {part.value}</div>;
              } else {
                return <div key={i} className="text-muted-foreground">{part.value}</div>;
              }
            })}
          </div>
        </div>
      );
    } else {
      // Split diff view
      return (
        <div className="grid grid-cols-2 gap-4">
          <div className="border rounded-md overflow-hidden">
            <div className="bg-muted p-2 text-sm font-medium">
              Version {getVersionById(leftVersionId)?.versionNumber || ""}
            </div>
            <div className="p-4 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-[500px]">
              {diffResult.split.left.map((part: any, i: number) => (
                <div
                  key={i}
                  className={cn(
                    part.removed && "bg-red-50 text-red-700"
                  )}
                >
                  {part.value}
                </div>
              ))}
            </div>
          </div>
          <div className="border rounded-md overflow-hidden">
            <div className="bg-muted p-2 text-sm font-medium">
              Version {getVersionById(rightVersionId)?.versionNumber || ""}
            </div>
            <div className="p-4 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-[500px]">
              {diffResult.split.right.map((part: any, i: number) => (
                <div
                  key={i}
                  className={cn(
                    part.added && "bg-green-50 text-green-700"
                  )}
                >
                  {part.value}
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    }
  }, [
    isLoadingContent,
    contentError,
    leftContent,
    rightContent,
    diffView,
    leftVersionId,
    rightVersionId,
    getVersionById,
  ]);

  // Render version info
  const renderVersionInfo = useCallback((versionId: string) => {
    const version = getVersionById(versionId);

    if (!version) {
      return <div className="text-muted-foreground">No version selected</div>;
    }

    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-xs">
            v{version.versionNumber}
          </Badge>
          <span className="text-sm text-muted-foreground flex items-center gap-1">
            <Clock className="h-3 w-3" />
            {format(new Date(version.createdAt), "MMM d, yyyy h:mm a")}
          </span>
        </div>
        <div className="text-sm flex items-center gap-1">
          <User className="h-3 w-3" />
          {version.createdBy.name}
        </div>
        {version.comment && (
          <div className="text-sm border-l-2 pl-2 mt-2 italic">
            {version.comment}
          </div>
        )}
        {version.changes && (
          <div className="flex gap-2 text-xs">
            <Badge variant="outline" className="bg-green-50">
              +{version.changes.added}
            </Badge>
            <Badge variant="outline" className="bg-red-50">
              -{version.changes.removed}
            </Badge>
            <Badge variant="outline" className="bg-blue-50">
              ~{version.changes.modified}
            </Badge>
          </div>
        )}
      </div>
    );
  }, [getVersionById]);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GitBranch className="h-5 w-5" />
          Document Version History
        </CardTitle>
        <CardDescription>
          Compare and restore previous versions of this document
        </CardDescription>
      </CardHeader>

      {isLoading ? (
        <CardContent>
          <LoadingState title="Loading version history..." variant="skeleton" count={5} />
        </CardContent>
      ) : error ? (
        <CardContent>
          <ErrorDisplay
            title="Failed to load version history"
            error={error}
            variant="card"
          />
        </CardContent>
      ) : versions.length === 0 ? (
        <CardContent>
          <div className="text-center p-6">
            <p className="text-muted-foreground">No version history available</p>
          </div>
        </CardContent>
      ) : (
        <>
          <CardContent className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-1 block">Left Version</label>
                <Select value={leftVersionId} onValueChange={setLeftVersionId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select version" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.map((version) => (
                      <SelectItem key={version.id} value={version.id}>
                        Version {version.versionNumber} ({format(new Date(version.createdAt), "MMM d, yyyy")})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="mt-2">
                  {renderVersionInfo(leftVersionId)}
                </div>
              </div>

              <div className="flex items-center justify-center">
                <GitCompare className="h-6 w-6 text-muted-foreground" />
              </div>

              <div className="flex-1">
                <label className="text-sm font-medium mb-1 block">Right Version</label>
                <Select value={rightVersionId} onValueChange={setRightVersionId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select version" />
                  </SelectTrigger>
                  <SelectContent>
                    {versions.map((version) => (
                      <SelectItem key={version.id} value={version.id}>
                        Version {version.versionNumber} ({format(new Date(version.createdAt), "MMM d, yyyy")})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="mt-2">
                  {renderVersionInfo(rightVersionId)}
                </div>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium">Differences</h3>
                <Tabs value={diffView} onValueChange={(value) => setDiffView(value as any)}>
                  <TabsList className="h-8">
                    <TabsTrigger value="split" className="text-xs px-2 py-1">
                      Split View
                    </TabsTrigger>
                    <TabsTrigger value="unified" className="text-xs px-2 py-1">
                      Unified View
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>

              {renderDiff()}
            </div>
          </CardContent>

          <CardFooter className="flex justify-between border-t p-4">
            <div className="text-sm text-muted-foreground">
              Showing {versions.length} versions
            </div>

            {onRestoreVersion && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRestoreVersion(leftVersionId)}
                  disabled={!leftVersionId || isRestoring}
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Restore Left
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleRestoreVersion(rightVersionId)}
                  disabled={!rightVersionId || isRestoring}
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Restore Right
                </Button>
              </div>
            )}
          </CardFooter>
        </>
      )}
    </Card>
  );
}
