'use client';

import React, { useState } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Button } from '@/components/ui/button';
import { Copy, Check, ChevronDown, ChevronRight } from 'lucide-react';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';

interface JsonViewProps {
  data: any;
  title?: string;
  expandLevel?: number;
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

/**
 * JsonView component for displaying JSON data with syntax highlighting
 */
export function JsonView({
  data,
  title,
  expandLevel = 1,
  className,
  collapsible = true,
  defaultCollapsed = false,
}: JsonViewProps) {
  const [copied, setCopied] = useState(false);
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Format the JSON data
  const formattedJson = JSON.stringify(data, null, 2);

  // Copy the JSON data to the clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(formattedJson);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Toggle the collapsed state
  const toggleCollapsed = () => {
    if (collapsible) {
      setCollapsed(!collapsed);
    }
  };

  return (
    <div className={cn('rounded-md border', className)}>
      {title && (
        <div className="flex items-center justify-between px-4 py-2 border-b bg-muted/50">
          <div className="flex items-center gap-2">
            {collapsible && (
              <button
                onClick={toggleCollapsed}
                className="p-1 rounded-md hover:bg-muted"
                aria-label={collapsed ? 'Expand' : 'Collapse'}
              >
                {collapsed ? (
                  <ChevronRight className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </button>
            )}
            <h3 className="text-sm font-medium">{title}</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2"
            onClick={copyToClipboard}
            aria-label="Copy JSON"
          >
            {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
          </Button>
        </div>
      )}
      {!collapsed && (
        <div className="overflow-auto max-h-[500px]">
          <SyntaxHighlighter
            language="json"
            style={isDark ? vscDarkPlus : vs}
            customStyle={{
              margin: 0,
              padding: '1rem',
              borderRadius: title ? '0 0 0.375rem 0.375rem' : '0.375rem',
              fontSize: '0.875rem',
            }}
          >
            {formattedJson}
          </SyntaxHighlighter>
        </div>
      )}
    </div>
  );
}
