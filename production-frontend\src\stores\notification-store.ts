/**
 * Notification Store
 * Manages notifications and alerts with Zustand
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: string
  read: boolean
  persistent?: boolean
  actions?: Array<{
    label: string
    action: () => void
    variant?: 'default' | 'destructive'
  }>
  metadata?: Record<string, any>
}

export interface NotificationState {
  // Notification data
  notifications: Notification[]
  unreadCount: number

  // UI state
  isOpen: boolean
  loading: boolean
  error: string | null

  // Settings
  settings: {
    enableSound: boolean
    enableDesktop: boolean
    enableEmail: boolean
    maxNotifications: number
    autoMarkAsRead: boolean
    autoMarkAsReadDelay: number
  }

  // Internal state
  _hydrated: boolean
}

export interface NotificationActions {
  // Notification management
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void
  removeNotification: (id: string) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  clearAll: () => void
  clearRead: () => void

  // UI actions
  togglePanel: () => void
  openPanel: () => void
  closePanel: () => void

  // Settings
  updateSettings: (settings: Partial<NotificationState['settings']>) => void

  // Utilities
  getUnreadNotifications: () => Notification[]
  getNotificationsByType: (type: Notification['type']) => Notification[]
  clearError: () => void
}

export type NotificationStore = NotificationState & NotificationActions

const DEFAULT_SETTINGS: NotificationState['settings'] = {
  enableSound: true,
  enableDesktop: true,
  enableEmail: false,
  maxNotifications: 50,
  autoMarkAsRead: true,
  autoMarkAsReadDelay: 5000, // 5 seconds
}

export const useNotificationStore = create<NotificationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      notifications: [],
      unreadCount: 0,
      isOpen: false,
      loading: false,
      error: null,
      settings: DEFAULT_SETTINGS,
      _hydrated: false,

      // Actions
      addNotification: (notificationData) => {
        const notification: Notification = {
          ...notificationData,
          id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString(),
          read: false,
        }

        set(state => {
          const notifications = [notification, ...state.notifications]
          
          // Limit notifications to maxNotifications
          const limitedNotifications = notifications.slice(0, state.settings.maxNotifications)
          
          // Calculate unread count
          const unreadCount = limitedNotifications.filter(n => !n.read).length

          return {
            notifications: limitedNotifications,
            unreadCount,
          }
        })

        // Auto-mark as read if enabled
        const { settings } = get()
        if (settings.autoMarkAsRead && !notification.persistent) {
          setTimeout(() => {
            get().markAsRead(notification.id)
          }, settings.autoMarkAsReadDelay)
        }

        // Play sound if enabled
        if (get().settings.enableSound) {
          // Play notification sound (implement based on your needs)
          try {
            const audio = new Audio('/sounds/notification.mp3')
            audio.play().catch(() => {
              // Ignore audio play errors (user interaction required)
            })
          } catch {
            // Ignore audio errors
          }
        }

        // Show desktop notification if enabled and permission granted
        if (get().settings.enableDesktop && 'Notification' in window) {
          if (Notification.permission === 'granted') {
            new Notification(notification.title, {
              body: notification.message,
              icon: '/favicon.ico',
            })
          } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
              if (permission === 'granted') {
                new Notification(notification.title, {
                  body: notification.message,
                  icon: '/favicon.ico',
                })
              }
            })
          }
        }
      },

      removeNotification: (id: string) => {
        set(state => {
          const notifications = state.notifications.filter(n => n.id !== id)
          const unreadCount = notifications.filter(n => !n.read).length

          return {
            notifications,
            unreadCount,
          }
        })
      },

      markAsRead: (id: string) => {
        set(state => {
          const notifications = state.notifications.map(n =>
            n.id === id ? { ...n, read: true } : n
          )
          const unreadCount = notifications.filter(n => !n.read).length

          return {
            notifications,
            unreadCount,
          }
        })
      },

      markAllAsRead: () => {
        set(state => ({
          notifications: state.notifications.map(n => ({ ...n, read: true })),
          unreadCount: 0,
        }))
      },

      clearAll: () => {
        set({
          notifications: [],
          unreadCount: 0,
        })
      },

      clearRead: () => {
        set(state => {
          const notifications = state.notifications.filter(n => !n.read)
          return {
            notifications,
            unreadCount: notifications.length,
          }
        })
      },

      togglePanel: () => {
        set(state => ({ isOpen: !state.isOpen }))
      },

      openPanel: () => {
        set({ isOpen: true })
      },

      closePanel: () => {
        set({ isOpen: false })
      },

      updateSettings: (newSettings) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings }
        }))
      },

      getUnreadNotifications: () => {
        return get().notifications.filter(n => !n.read)
      },

      getNotificationsByType: (type: Notification['type']) => {
        return get().notifications.filter(n => n.type === type)
      },

      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'notification-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        notifications: state.notifications.filter(n => n.persistent), // Only persist persistent notifications
        settings: state.settings,
        unreadCount: state.unreadCount,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
          // Recalculate unread count after hydration
          state.unreadCount = state.notifications.filter(n => !n.read).length
        }
      },
    }
  )
)

// Selector hooks
export const useNotifications = () => useNotificationStore((state) => state.notifications)
export const useUnreadCount = () => useNotificationStore((state) => state.unreadCount)
export const useNotificationPanel = () => useNotificationStore((state) => state.isOpen)
export const useNotificationSettings = () => useNotificationStore((state) => state.settings)

// Action hooks
export const useAddNotification = () => useNotificationStore((state) => state.addNotification)
export const useMarkAsRead = () => useNotificationStore((state) => state.markAsRead)
export const useToggleNotificationPanel = () => useNotificationStore((state) => state.togglePanel)

// Utility hooks
export const useNotificationHelpers = () => {
  const addNotification = useAddNotification()

  return {
    showSuccess: (title: string, message: string) => 
      addNotification({ type: 'success', title, message }),
    showError: (title: string, message: string) => 
      addNotification({ type: 'error', title, message }),
    showWarning: (title: string, message: string) => 
      addNotification({ type: 'warning', title, message }),
    showInfo: (title: string, message: string) => 
      addNotification({ type: 'info', title, message }),
  }
}

// Main notification hook for components
export const useNotification = () => {
  const notifications = useNotifications()
  const unreadCount = useUnreadCount()
  const isOpen = useNotificationPanel()
  const settings = useNotificationSettings()
  const addNotification = useAddNotification()
  const markAsRead = useMarkAsRead()
  const togglePanel = useToggleNotificationPanel()

  return {
    notifications,
    unreadCount,
    isOpen,
    settings,
    addNotification,
    markAsRead,
    togglePanel,
  }
}
