/**
 * Unified External Services Function
 * Consolidates cloud storage integration, payment webhooks, and subscription management
 * Replaces: cloud-storage-integration.ts, lemonsqueezy-webhooks.ts, subscription-management.ts
 * 
 * Production-ready implementation with comprehensive error handling,
 * full Service Bus, Event Hub, Redis integrations, and enterprise-grade external service management
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app, Timer } from '@azure/functions';
import { BlobServiceClient } from "@azure/storage-blob";
import { createHmac, timingSafeEqual } from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';

// Unified external service types and enums
enum ExternalServiceType {
  CLOUD_STORAGE = 'CLOUD_STORAGE',
  PAYMENT_WEBHOOK = 'PAYMENT_WEBHOOK',
  SUBSCRIPTION_MANAGEMENT = 'SUBSCRIPTION_MANAGEMENT'
}

enum StorageProvider {
  AZURE_BLOB = 'azure_blob',
  AWS_S3 = 'aws_s3',
  GOOGLE_CLOUD = 'google_cloud',
  DROPBOX = 'dropbox',
  ONEDRIVE = 'onedrive',
  SHAREPOINT = 'sharepoint'
}

enum SyncDirection {
  UPLOAD = 'UPLOAD',
  DOWNLOAD = 'DOWNLOAD',
  BIDIRECTIONAL = 'BIDIRECTIONAL'
}

enum SyncStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

enum SubscriptionTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE',
  CUSTOM = 'CUSTOM'
}

enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED',
  SUSPENDED = 'SUSPENDED',
  TRIAL = 'TRIAL',
  PAST_DUE = 'PAST_DUE',
  PAUSED = 'PAUSED'
}

enum BillingCycle {
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
  LIFETIME = 'LIFETIME'
}

enum LemonSqueezyEventType {
  ORDER_CREATED = 'order_created',
  ORDER_REFUNDED = 'order_refunded',
  SUBSCRIPTION_CREATED = 'subscription_created',
  SUBSCRIPTION_UPDATED = 'subscription_updated',
  SUBSCRIPTION_CANCELLED = 'subscription_cancelled',
  SUBSCRIPTION_RESUMED = 'subscription_resumed',
  SUBSCRIPTION_EXPIRED = 'subscription_expired',
  SUBSCRIPTION_PAUSED = 'subscription_paused',
  SUBSCRIPTION_UNPAUSED = 'subscription_unpaused',
  SUBSCRIPTION_PAYMENT_SUCCESS = 'subscription_payment_success',
  SUBSCRIPTION_PAYMENT_FAILED = 'subscription_payment_failed',
  SUBSCRIPTION_PAYMENT_RECOVERED = 'subscription_payment_recovered',
  LICENSE_KEY_CREATED = 'license_key_created',
  LICENSE_KEY_UPDATED = 'license_key_updated'
}

enum Priority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Comprehensive interfaces
interface ExternalServiceRequest {
  serviceType: ExternalServiceType;
  storageRequest?: CloudStorageRequest;
  webhookRequest?: PaymentWebhookRequest;
  subscriptionRequest?: SubscriptionRequest;
  organizationId: string;
  projectId?: string;
  priority?: Priority;
  callbackUrl?: string;
}

interface CloudStorageRequest {
  operation: 'configure' | 'sync' | 'bulk_sync' | 'list_configs' | 'delete_config';
  storageConfig?: StorageConfiguration;
  syncOptions?: SyncOptions;
  bulkSyncOptions?: BulkSyncOptions;
  configId?: string;
}

interface PaymentWebhookRequest {
  operation: 'process_webhook' | 'verify_signature' | 'list_events' | 'replay_event';
  webhookPayload?: LemonSqueezyWebhookPayload;
  signature?: string;
  eventId?: string;
}

interface SubscriptionRequest {
  operation: 'create' | 'update' | 'cancel' | 'get' | 'list' | 'usage_update';
  subscriptionData?: CreateSubscriptionData;
  updateData?: UpdateSubscriptionData;
  subscriptionId?: string;
  usageData?: UsageUpdateData;
}

interface StorageConfiguration {
  name: string;
  provider: StorageProvider;
  configuration: {
    connectionString?: string;
    accessKey?: string;
    secretKey?: string;
    region?: string;
    bucket?: string;
    container?: string;
    endpoint?: string;
    authToken?: string;
  };
  isDefault?: boolean;
  syncSettings?: {
    autoSync?: boolean;
    syncInterval?: number;
    conflictResolution?: 'overwrite' | 'rename' | 'skip';
    includeMetadata?: boolean;
    encryptionEnabled?: boolean;
  };
  // Additional properties for production provider support
  connectionString?: string;
  credentials?: {
    accessKey?: string;
    secretKey?: string;
    accessToken?: string;
    keyFile?: string;
  };
  region?: string;
  projectId?: string;
  siteUrl?: string;
}

interface SyncOptions {
  documentId: string;
  storageConfigId: string;
  targetPath?: string;
  syncDirection: SyncDirection;
  overwrite?: boolean;
  preserveMetadata?: boolean;
}

interface BulkSyncOptions {
  documentIds: string[];
  storageConfigId: string;
  targetFolder?: string;
  syncDirection: SyncDirection;
  overwrite?: boolean;
  preserveMetadata?: boolean;
  batchSize?: number;
}

interface LemonSqueezyWebhookPayload {
  meta: {
    event_name: LemonSqueezyEventType;
    custom_data?: Record<string, any>;
    test_mode: boolean;
    webhook_id: string;
  };
  data: {
    type: string;
    id: string;
    attributes: Record<string, any>;
    relationships?: Record<string, any>;
  };
}

interface CreateSubscriptionData {
  organizationId: string;
  tier: SubscriptionTier;
  billingCycle: BillingCycle;
  startDate?: string;
  trialDays?: number;
  customLimits?: {
    maxUsers?: number;
    maxDocuments?: number;
    maxStorage?: number;
    maxApiCalls?: number;
    maxWorkflows?: number;
  };
  paymentMethodId?: string;
  couponCode?: string;
}

interface UpdateSubscriptionData {
  subscriptionId: string;
  tier?: SubscriptionTier;
  billingCycle?: BillingCycle;
  status?: SubscriptionStatus;
  customLimits?: {
    maxUsers?: number;
    maxDocuments?: number;
    maxStorage?: number;
    maxApiCalls?: number;
    maxWorkflows?: number;
  };
  paymentMethodId?: string;
}

interface UsageUpdateData {
  subscriptionId: string;
  usage: {
    currentUsers?: number;
    currentDocuments?: number;
    currentStorage?: number;
    currentApiCalls?: number;
    currentWorkflows?: number;
  };
}

interface ExternalServiceResults {
  operationId: string;
  serviceType: ExternalServiceType;
  storageResults?: CloudStorageResults;
  webhookResults?: PaymentWebhookResults;
  subscriptionResults?: SubscriptionResults;
  processingTime: number;
  success: boolean;
  errors?: string[];
}

interface CloudStorageResults {
  operation: string;
  configId?: string;
  syncJobId?: string;
  documentsProcessed?: number;
  bytesTransferred?: number;
  configurations?: StorageConfiguration[];
  syncStatus?: SyncStatus;
}

interface PaymentWebhookResults {
  operation: string;
  eventId?: string;
  eventType?: LemonSqueezyEventType;
  processed?: boolean;
  subscriptionId?: string;
  customerId?: string;
  events?: any[];
}

interface SubscriptionResults {
  operation: string;
  subscriptionId?: string;
  subscription?: any;
  subscriptions?: any[];
  usageUpdated?: boolean;
  billingInfo?: any;
}

// Validation schemas
const externalServiceSchema = Joi.object({
  serviceType: Joi.string().valid(...Object.values(ExternalServiceType)).required(),
  storageRequest: Joi.object({
    operation: Joi.string().valid('configure', 'sync', 'bulk_sync', 'list_configs', 'delete_config').required(),
    storageConfig: Joi.object({
      name: Joi.string().min(2).max(100).required(),
      provider: Joi.string().valid(...Object.values(StorageProvider)).required(),
      configuration: Joi.object().required(),
      isDefault: Joi.boolean().default(false),
      syncSettings: Joi.object().optional()
    }).optional(),
    syncOptions: Joi.object({
      documentId: Joi.string().uuid().required(),
      storageConfigId: Joi.string().uuid().required(),
      targetPath: Joi.string().optional(),
      syncDirection: Joi.string().valid(...Object.values(SyncDirection)).required(),
      overwrite: Joi.boolean().default(false),
      preserveMetadata: Joi.boolean().default(true)
    }).optional(),
    bulkSyncOptions: Joi.object({
      documentIds: Joi.array().items(Joi.string().uuid()).min(1).max(100).required(),
      storageConfigId: Joi.string().uuid().required(),
      targetFolder: Joi.string().optional(),
      syncDirection: Joi.string().valid(...Object.values(SyncDirection)).required(),
      overwrite: Joi.boolean().default(false),
      preserveMetadata: Joi.boolean().default(true),
      batchSize: Joi.number().min(1).max(50).default(10)
    }).optional(),
    configId: Joi.string().uuid().optional()
  }).optional(),
  webhookRequest: Joi.object({
    operation: Joi.string().valid('process_webhook', 'verify_signature', 'list_events', 'replay_event').required(),
    webhookPayload: Joi.object().optional(),
    signature: Joi.string().optional(),
    eventId: Joi.string().optional()
  }).optional(),
  subscriptionRequest: Joi.object({
    operation: Joi.string().valid('create', 'update', 'cancel', 'get', 'list', 'usage_update').required(),
    subscriptionData: Joi.object({
      organizationId: Joi.string().uuid().required(),
      tier: Joi.string().valid(...Object.values(SubscriptionTier)).required(),
      billingCycle: Joi.string().valid(...Object.values(BillingCycle)).required(),
      startDate: Joi.string().isoDate().optional(),
      trialDays: Joi.number().min(0).max(90).optional(),
      customLimits: Joi.object().optional(),
      paymentMethodId: Joi.string().optional(),
      couponCode: Joi.string().max(50).optional()
    }).optional(),
    updateData: Joi.object({
      subscriptionId: Joi.string().uuid().required(),
      tier: Joi.string().valid(...Object.values(SubscriptionTier)).optional(),
      billingCycle: Joi.string().valid(...Object.values(BillingCycle)).optional(),
      status: Joi.string().valid(...Object.values(SubscriptionStatus)).optional(),
      customLimits: Joi.object().optional(),
      paymentMethodId: Joi.string().optional()
    }).optional(),
    subscriptionId: Joi.string().uuid().optional(),
    usageData: Joi.object({
      subscriptionId: Joi.string().uuid().required(),
      usage: Joi.object().required()
    }).optional()
  }).optional(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  priority: Joi.string().valid(...Object.values(Priority)).default(Priority.NORMAL),
  callbackUrl: Joi.string().uri().optional()
});

/**
 * Unified External Services Manager
 * Handles all external service operations with comprehensive error handling, caching, and real-time processing
 */
class UnifiedExternalServicesManager {

  private serviceBusService: ServiceBusEnhancedService;

  constructor() {
    // Initialize Service Bus service for external service processing
    this.serviceBusService = ServiceBusEnhancedService.getInstance();
  }

  /**
   * Process external service request
   */
  async processExternalService(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = externalServiceSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const serviceRequest: ExternalServiceRequest = value;

      // Check organization access
      const hasAccess = await this.checkOrganizationAccess(serviceRequest.organizationId, user.id);
      if (!hasAccess) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied to organization' }
        }, request);
      }

      // Generate operation ID
      const operationId = uuidv4();

      // Cache operation for tracking
      await redis.setex(
        `external-service-operation:${operationId}`,
        3600,
        JSON.stringify({
          operationId,
          serviceType: serviceRequest.serviceType,
          organizationId: serviceRequest.organizationId,
          status: 'processing',
          startTime: new Date().toISOString(),
          userId: user.id
        })
      );

      // Process service based on type
      let results: ExternalServiceResults;

      switch (serviceRequest.serviceType) {
        case ExternalServiceType.CLOUD_STORAGE:
          results = await this.processCloudStorageService(
            serviceRequest,
            user,
            operationId,
            correlationId
          );
          break;

        case ExternalServiceType.PAYMENT_WEBHOOK:
          results = await this.processPaymentWebhookService(
            serviceRequest,
            user,
            operationId,
            correlationId
          );
          break;

        case ExternalServiceType.SUBSCRIPTION_MANAGEMENT:
          results = await this.processSubscriptionService(
            serviceRequest,
            user,
            operationId,
            correlationId
          );
          break;

        default:
          throw new Error(`Unsupported service type: ${serviceRequest.serviceType}`);
      }

      // Update operation cache
      await redis.setex(
        `external-service-operation:${operationId}`,
        3600,
        JSON.stringify({
          ...results,
          status: 'completed',
          completedAt: new Date().toISOString()
        })
      );

      // Send to Service Bus for background processing
      await this.serviceBusService.sendToQueue('external-services', {
        body: {
          operationId,
          serviceType: serviceRequest.serviceType,
          organizationId: serviceRequest.organizationId,
          userId: user.id,
          results,
          timestamp: new Date().toISOString()
        },
        correlationId,
        messageId: `external-service-${operationId}-${Date.now()}`
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'ExternalServices.OperationCompleted',
        subject: `external-services/operations/${operationId}/completed`,
        data: {
          operationId,
          serviceType: serviceRequest.serviceType,
          organizationId: serviceRequest.organizationId,
          processingTime: results.processingTime,
          success: results.success,
          createdBy: user.id,
          correlationId
        }
      });

      // Create activity record
      await db.createItem('activities', {
        id: uuidv4(),
        type: `external_service_${serviceRequest.serviceType.toLowerCase()}`,
        userId: user.id,
        organizationId: serviceRequest.organizationId,
        projectId: serviceRequest.projectId,
        timestamp: new Date().toISOString(),
        details: {
          operationId,
          serviceType: serviceRequest.serviceType,
          processingTime: results.processingTime,
          success: results.success,
          priority: serviceRequest.priority
        },
        tenantId: user.tenantId
      });

      logger.info('External service operation completed successfully', {
        correlationId,
        operationId,
        serviceType: serviceRequest.serviceType,
        organizationId: serviceRequest.organizationId,
        processingTime: results.processingTime,
        userId: user.id
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          operationId,
          serviceType: serviceRequest.serviceType,
          organizationId: serviceRequest.organizationId,
          results,
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('External service operation failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Process LemonSqueezy webhook
   */
  async processLemonSqueezyWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Get webhook signature
      const signature = request.headers.get('x-signature');
      if (!signature) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Missing webhook signature' }
        }, request);
      }

      // Get webhook payload
      const body = await request.text();
      const payload: LemonSqueezyWebhookPayload = JSON.parse(body);

      // Verify webhook signature
      const isValid = await this.verifyLemonSqueezySignature(body, signature);
      if (!isValid) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Invalid webhook signature' }
        }, request);
      }

      // Check for duplicate webhook
      const eventId = `${payload.meta.webhook_id}-${payload.data.id}-${payload.meta.event_name}`;
      const isDuplicate = await this.checkDuplicateWebhook(eventId);
      if (isDuplicate) {
        return addCorsHeaders({
          status: 200,
          jsonBody: { message: 'Webhook already processed', eventId }
        }, request);
      }

      // Process webhook
      const operationId = uuidv4();
      const serviceRequest: ExternalServiceRequest = {
        serviceType: ExternalServiceType.PAYMENT_WEBHOOK,
        webhookRequest: {
          operation: 'process_webhook',
          webhookPayload: payload,
          signature
        },
        organizationId: 'system', // System-level webhook
        priority: Priority.HIGH
      };

      const results = await this.processPaymentWebhookService(
        serviceRequest,
        { id: 'system', tenantId: 'system' } as any,
        operationId,
        correlationId
      );

      logger.info('LemonSqueezy webhook processed successfully', {
        correlationId,
        eventId,
        eventType: payload.meta.event_name,
        dataType: payload.data.type,
        dataId: payload.data.id,
        testMode: payload.meta.test_mode
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          message: 'Webhook processed successfully',
          correlationId,
          eventId,
          eventType: payload.meta.event_name,
          results
        }
      }, request);

    } catch (error) {
      logger.error('LemonSqueezy webhook processing failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Process cloud storage service operations
   */
  private async processCloudStorageService(
    request: ExternalServiceRequest,
    user: any,
    operationId: string,
    correlationId: string
  ): Promise<ExternalServiceResults> {
    const startTime = Date.now();

    try {
      const storageRequest = request.storageRequest!;
      let results: CloudStorageResults;

      switch (storageRequest.operation) {
        case 'configure':
          results = await this.configureStorageProvider(
            storageRequest.storageConfig!,
            request.organizationId,
            user,
            correlationId
          );
          break;

        case 'sync':
          results = await this.syncDocument(
            storageRequest.syncOptions!,
            request.organizationId,
            user,
            correlationId
          );
          break;

        case 'bulk_sync':
          results = await this.bulkSyncDocuments(
            storageRequest.bulkSyncOptions!,
            request.organizationId,
            user,
            correlationId
          );
          break;

        case 'list_configs':
          results = await this.listStorageConfigurations(
            request.organizationId,
            user,
            correlationId
          );
          break;

        case 'delete_config':
          results = await this.deleteStorageConfiguration(
            storageRequest.configId!,
            request.organizationId,
            user,
            correlationId
          );
          break;

        default:
          throw new Error(`Unsupported storage operation: ${storageRequest.operation}`);
      }

      logger.info('Cloud storage operation completed', {
        operationId,
        operation: storageRequest.operation,
        organizationId: request.organizationId,
        correlationId
      });

      return {
        operationId,
        serviceType: ExternalServiceType.CLOUD_STORAGE,
        storageResults: results,
        processingTime: Date.now() - startTime,
        success: true
      };

    } catch (error) {
      logger.error('Cloud storage operation failed', {
        operationId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return {
        operationId,
        serviceType: ExternalServiceType.CLOUD_STORAGE,
        processingTime: Date.now() - startTime,
        success: false,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  /**
   * Process payment webhook service operations
   */
  private async processPaymentWebhookService(
    request: ExternalServiceRequest,
    _user: any,
    operationId: string,
    correlationId: string
  ): Promise<ExternalServiceResults> {
    const startTime = Date.now();

    try {
      const webhookRequest = request.webhookRequest!;
      let results: PaymentWebhookResults;

      switch (webhookRequest.operation) {
        case 'process_webhook':
          results = await this.processWebhookEvent(
            webhookRequest.webhookPayload!,
            correlationId
          );
          break;

        case 'verify_signature':
          const isValid = await this.verifyLemonSqueezySignature(
            JSON.stringify(webhookRequest.webhookPayload),
            webhookRequest.signature!
          );
          results = {
            operation: 'verify_signature',
            processed: isValid
          };
          break;

        case 'list_events':
          results = await this.listWebhookEvents(
            request.organizationId,
            correlationId
          );
          break;

        case 'replay_event':
          results = await this.replayWebhookEvent(
            webhookRequest.eventId!,
            correlationId
          );
          break;

        default:
          throw new Error(`Unsupported webhook operation: ${webhookRequest.operation}`);
      }

      logger.info('Payment webhook operation completed', {
        operationId,
        operation: webhookRequest.operation,
        correlationId
      });

      return {
        operationId,
        serviceType: ExternalServiceType.PAYMENT_WEBHOOK,
        webhookResults: results,
        processingTime: Date.now() - startTime,
        success: true
      };

    } catch (error) {
      logger.error('Payment webhook operation failed', {
        operationId,
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return {
        operationId,
        serviceType: ExternalServiceType.PAYMENT_WEBHOOK,
        processingTime: Date.now() - startTime,
        success: false,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  /**
   * Process subscription service operations
   */
  private async processSubscriptionService(
    request: ExternalServiceRequest,
    user: any,
    operationId: string,
    correlationId: string
  ): Promise<ExternalServiceResults> {
    const startTime = Date.now();

    try {
      const subscriptionRequest = request.subscriptionRequest!;
      let results: SubscriptionResults;

      switch (subscriptionRequest.operation) {
        case 'create':
          results = await this.createSubscription(
            subscriptionRequest.subscriptionData!,
            user,
            correlationId
          );
          break;

        case 'update':
          results = await this.updateSubscription(
            subscriptionRequest.updateData!,
            user,
            correlationId
          );
          break;

        case 'cancel':
          results = await this.cancelSubscription(
            subscriptionRequest.subscriptionId!,
            user,
            correlationId
          );
          break;

        case 'get':
          results = await this.getSubscription(
            subscriptionRequest.subscriptionId!,
            user,
            correlationId
          );
          break;

        case 'list':
          results = await this.listSubscriptions(
            request.organizationId,
            user,
            correlationId
          );
          break;

        case 'usage_update':
          results = await this.updateSubscriptionUsage(
            subscriptionRequest.usageData!,
            user,
            correlationId
          );
          break;

        default:
          throw new Error(`Unsupported subscription operation: ${subscriptionRequest.operation}`);
      }

      logger.info('Subscription operation completed', {
        operationId,
        operation: subscriptionRequest.operation,
        organizationId: request.organizationId,
        correlationId
      });

      return {
        operationId,
        serviceType: ExternalServiceType.SUBSCRIPTION_MANAGEMENT,
        subscriptionResults: results,
        processingTime: Date.now() - startTime,
        success: true
      };

    } catch (error) {
      logger.error('Subscription operation failed', {
        operationId,
        organizationId: request.organizationId,
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return {
        operationId,
        serviceType: ExternalServiceType.SUBSCRIPTION_MANAGEMENT,
        processingTime: Date.now() - startTime,
        success: false,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  /**
   * Helper Methods with Full Production Integrations
   */
  private async checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
    try {
      const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [
        { name: '@orgId', value: organizationId },
        { name: '@userId', value: userId },
        { name: '@status', value: 'ACTIVE' }
      ]);
      return memberships.length > 0;
    } catch (error) {
      logger.error('Failed to check organization access', {
        error: error instanceof Error ? error.message : String(error),
        organizationId,
        userId
      });
      return false;
    }
  }

  private async verifyLemonSqueezySignature(payload: string, signature: string): Promise<boolean> {
    try {
      const secret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET;
      if (!secret) {
        logger.error('LemonSqueezy webhook secret not configured');
        return false;
      }

      const hmac = createHmac('sha256', secret);
      hmac.update(payload, 'utf8');
      const digest = hmac.digest('hex');
      const expectedSignature = `sha256=${digest}`;

      return timingSafeEqual(
        Buffer.from(signature, 'utf8'),
        Buffer.from(expectedSignature, 'utf8')
      );
    } catch (error) {
      logger.error('Failed to verify LemonSqueezy signature', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async checkDuplicateWebhook(eventId: string): Promise<boolean> {
    try {
      // Check Redis cache first
      const cached = await redis.get(`webhook-event:${eventId}`);
      if (cached) {
        return true;
      }

      // Check database
      const existingQuery = 'SELECT * FROM c WHERE c.eventId = @eventId';
      const existing = await db.queryItems('lemonsqueezy-webhook-events', existingQuery, [
        { name: '@eventId', value: eventId }
      ]);

      if (existing.length > 0) {
        // Cache for future checks
        await redis.setex(`webhook-event:${eventId}`, 3600, 'processed');
        return true;
      }

      return false;
    } catch (error) {
      logger.error('Failed to check duplicate webhook', {
        error: error instanceof Error ? error.message : String(error),
        eventId
      });
      return false;
    }
  }

  private async configureStorageProvider(
    config: StorageConfiguration,
    organizationId: string,
    user: any,
    correlationId: string
  ): Promise<CloudStorageResults> {
    try {
      const configId = uuidv4();

      // Encrypt sensitive configuration data
      const encryptedConfig = {
        ...config.configuration,
        accessKey: config.configuration.accessKey ? this.encrypt(config.configuration.accessKey) : undefined,
        secretKey: config.configuration.secretKey ? this.encrypt(config.configuration.secretKey) : undefined,
        authToken: config.configuration.authToken ? this.encrypt(config.configuration.authToken) : undefined
      };

      const storageConfig = {
        id: configId,
        name: config.name,
        provider: config.provider,
        configuration: encryptedConfig,
        organizationId,
        isDefault: config.isDefault || false,
        syncSettings: config.syncSettings || {
          autoSync: false,
          syncInterval: 3600,
          conflictResolution: 'rename',
          includeMetadata: true,
          encryptionEnabled: true
        },
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tenantId: user.tenantId
      };

      await db.createItem('storage-configurations', storageConfig);

      // Cache configuration for quick access
      await redis.setex(`storage-config:${configId}`, 1800, JSON.stringify(storageConfig));

      // Test connection
      const connectionTest = await this.testStorageConnection(config);

      logger.info('Storage provider configured successfully', {
        configId,
        provider: config.provider,
        organizationId,
        connectionTest: connectionTest.success,
        correlationId
      });

      return {
        operation: 'configure',
        configId,
        syncStatus: connectionTest.success ? SyncStatus.COMPLETED : SyncStatus.FAILED
      };

    } catch (error) {
      logger.error('Storage provider configuration failed', {
        error: error instanceof Error ? error.message : String(error),
        organizationId,
        correlationId
      });
      throw error;
    }
  }

  private async syncDocument(
    syncOptions: SyncOptions,
    organizationId: string,
    user: any,
    correlationId: string
  ): Promise<CloudStorageResults> {
    try {
      // Get document
      const document = await db.readItem('documents', syncOptions.documentId, syncOptions.documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      // Get storage configuration
      const storageConfig = await db.readItem('storage-configurations', syncOptions.storageConfigId, syncOptions.storageConfigId);
      if (!storageConfig) {
        throw new Error('Storage configuration not found');
      }

      // Create sync job
      const syncJobId = uuidv4();
      const syncJob = {
        id: syncJobId,
        documentId: syncOptions.documentId,
        storageConfigId: syncOptions.storageConfigId,
        targetPath: syncOptions.targetPath || `documents/${(document as any).name}`,
        syncDirection: syncOptions.syncDirection,
        overwrite: syncOptions.overwrite || false,
        preserveMetadata: syncOptions.preserveMetadata || true,
        status: SyncStatus.PENDING,
        startedBy: user.id,
        startedAt: new Date().toISOString(),
        progress: 0,
        organizationId,
        tenantId: user.tenantId
      };

      await db.createItem('sync-jobs', syncJob);

      // Send to Service Bus for background processing
      await this.serviceBusService.sendToQueue('storage-sync', {
        body: {
          syncJobId,
          documentId: syncOptions.documentId,
          storageConfigId: syncOptions.storageConfigId,
          syncDirection: syncOptions.syncDirection,
          organizationId,
          userId: user.id
        },
        correlationId,
        messageId: `sync-${syncJobId}-${Date.now()}`
      });

      logger.info('Document sync initiated', {
        syncJobId,
        documentId: syncOptions.documentId,
        storageConfigId: syncOptions.storageConfigId,
        syncDirection: syncOptions.syncDirection,
        correlationId
      });

      return {
        operation: 'sync',
        syncJobId,
        documentsProcessed: 1,
        syncStatus: SyncStatus.PENDING
      };

    } catch (error) {
      logger.error('Document sync failed', {
        error: error instanceof Error ? error.message : String(error),
        documentId: syncOptions.documentId,
        correlationId
      });
      throw error;
    }
  }

  private async bulkSyncDocuments(
    bulkOptions: BulkSyncOptions,
    organizationId: string,
    user: any,
    correlationId: string
  ): Promise<CloudStorageResults> {
    try {
      const batchSize = bulkOptions.batchSize || 10;
      const batches = [];

      // Split documents into batches
      for (let i = 0; i < bulkOptions.documentIds.length; i += batchSize) {
        batches.push(bulkOptions.documentIds.slice(i, i + batchSize));
      }

      const syncJobIds: string[] = [];
      let totalBytesTransferred = 0;

      // Process each batch
      for (const batch of batches) {
        for (const documentId of batch) {
          const syncOptions: SyncOptions = {
            documentId,
            storageConfigId: bulkOptions.storageConfigId,
            targetPath: bulkOptions.targetFolder ? `${bulkOptions.targetFolder}/${documentId}` : undefined,
            syncDirection: bulkOptions.syncDirection,
            overwrite: bulkOptions.overwrite,
            preserveMetadata: bulkOptions.preserveMetadata
          };

          const result = await this.syncDocument(syncOptions, organizationId, user, correlationId);
          if (result.syncJobId) {
            syncJobIds.push(result.syncJobId);
          }
        }
      }

      logger.info('Bulk document sync initiated', {
        totalDocuments: bulkOptions.documentIds.length,
        batches: batches.length,
        syncJobs: syncJobIds.length,
        storageConfigId: bulkOptions.storageConfigId,
        correlationId
      });

      return {
        operation: 'bulk_sync',
        documentsProcessed: bulkOptions.documentIds.length,
        bytesTransferred: totalBytesTransferred,
        syncStatus: SyncStatus.PENDING
      };

    } catch (error) {
      logger.error('Bulk document sync failed', {
        error: error instanceof Error ? error.message : String(error),
        documentCount: bulkOptions.documentIds.length,
        correlationId
      });
      throw error;
    }
  }

  private async listStorageConfigurations(
    organizationId: string,
    _user: any,
    correlationId: string
  ): Promise<CloudStorageResults> {
    try {
      const configurationsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId ORDER BY c.createdAt DESC';
      const configurations = await db.queryItems('storage-configurations', configurationsQuery, [
        { name: '@orgId', value: organizationId }
      ]);

      // Remove sensitive data from configurations
      const sanitizedConfigs = configurations.map((config: any) => ({
        ...config,
        configuration: {
          ...config.configuration,
          accessKey: config.configuration.accessKey ? '***' : undefined,
          secretKey: config.configuration.secretKey ? '***' : undefined,
          authToken: config.configuration.authToken ? '***' : undefined
        }
      }));

      return {
        operation: 'list_configs',
        configurations: sanitizedConfigs
      };

    } catch (error) {
      logger.error('List storage configurations failed', {
        error: error instanceof Error ? error.message : String(error),
        organizationId,
        correlationId
      });
      throw error;
    }
  }

  private async deleteStorageConfiguration(
    configId: string,
    organizationId: string,
    _user: any,
    correlationId: string
  ): Promise<CloudStorageResults> {
    try {
      // Get configuration
      const config = await db.readItem('storage-configurations', configId, configId);
      if (!config) {
        throw new Error('Storage configuration not found');
      }

      // Check ownership
      if ((config as any).organizationId !== organizationId) {
        throw new Error('Access denied to storage configuration');
      }

      // Delete configuration
      await db.deleteItem('storage-configurations', configId, configId);

      // Remove from cache
      await redis.del(`storage-config:${configId}`);

      logger.info('Storage configuration deleted', {
        configId,
        organizationId,
        correlationId
      });

      return {
        operation: 'delete_config',
        configId
      };

    } catch (error) {
      logger.error('Delete storage configuration failed', {
        error: error instanceof Error ? error.message : String(error),
        configId,
        organizationId,
        correlationId
      });
      throw error;
    }
  }

  private async processWebhookEvent(
    payload: LemonSqueezyWebhookPayload,
    correlationId: string
  ): Promise<PaymentWebhookResults> {
    try {
      const eventId = `${payload.meta.webhook_id}-${payload.data.id}-${payload.meta.event_name}`;

      // Store webhook event for audit
      await this.storeWebhookEvent(eventId, payload, correlationId);

      // Process based on event type
      let subscriptionId: string | undefined;
      let customerId: string | undefined;

      switch (payload.meta.event_name) {
        case LemonSqueezyEventType.SUBSCRIPTION_CREATED:
        case LemonSqueezyEventType.SUBSCRIPTION_UPDATED:
        case LemonSqueezyEventType.SUBSCRIPTION_CANCELLED:
        case LemonSqueezyEventType.SUBSCRIPTION_RESUMED:
        case LemonSqueezyEventType.SUBSCRIPTION_EXPIRED:
        case LemonSqueezyEventType.SUBSCRIPTION_PAUSED:
        case LemonSqueezyEventType.SUBSCRIPTION_UNPAUSED:
          await this.processSubscriptionWebhook(payload, correlationId);
          subscriptionId = payload.data.id;
          customerId = payload.data.attributes.user_email;
          break;

        case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_SUCCESS:
        case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_FAILED:
        case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_RECOVERED:
          await this.processPaymentWebhook(payload, correlationId);
          subscriptionId = payload.data.attributes.subscription_id;
          break;

        case LemonSqueezyEventType.ORDER_CREATED:
        case LemonSqueezyEventType.ORDER_REFUNDED:
          await this.processOrderWebhook(payload, correlationId);
          customerId = payload.data.attributes.user_email;
          break;

        default:
          logger.warn('Unhandled webhook event type', {
            eventType: payload.meta.event_name,
            correlationId
          });
      }

      // Cache processed event
      await redis.setex(`webhook-event:${eventId}`, 3600, 'processed');

      return {
        operation: 'process_webhook',
        eventId,
        eventType: payload.meta.event_name,
        processed: true,
        subscriptionId,
        customerId
      };

    } catch (error) {
      logger.error('Webhook event processing failed', {
        error: error instanceof Error ? error.message : String(error),
        eventType: payload.meta.event_name,
        correlationId
      });
      throw error;
    }
  }

  private async listWebhookEvents(
    organizationId: string,
    correlationId: string
  ): Promise<PaymentWebhookResults> {
    try {
      const eventsQuery = 'SELECT * FROM c ORDER BY c.processedAt DESC OFFSET 0 LIMIT 100';
      const events = await db.queryItems('lemonsqueezy-webhook-events', eventsQuery, []);

      return {
        operation: 'list_events',
        events
      };

    } catch (error) {
      logger.error('List webhook events failed', {
        error: error instanceof Error ? error.message : String(error),
        organizationId,
        correlationId
      });
      throw error;
    }
  }

  private async replayWebhookEvent(
    eventId: string,
    correlationId: string
  ): Promise<PaymentWebhookResults> {
    try {
      // Get stored webhook event
      const eventQuery = 'SELECT * FROM c WHERE c.eventId = @eventId';
      const events = await db.queryItems('lemonsqueezy-webhook-events', eventQuery, [
        { name: '@eventId', value: eventId }
      ]);

      if (events.length === 0) {
        throw new Error('Webhook event not found');
      }

      const storedEvent = events[0] as any;

      // Replay the event
      const result = await this.processWebhookEvent(storedEvent.payload, correlationId);

      logger.info('Webhook event replayed', {
        eventId,
        eventType: storedEvent.eventType,
        correlationId
      });

      return {
        operation: 'replay_event',
        eventId,
        eventType: storedEvent.eventType,
        processed: result.processed
      };

    } catch (error) {
      logger.error('Webhook event replay failed', {
        error: error instanceof Error ? error.message : String(error),
        eventId,
        correlationId
      });
      throw error;
    }
  }

  private async createSubscription(
    subscriptionData: CreateSubscriptionData,
    user: any,
    correlationId: string
  ): Promise<SubscriptionResults> {
    try {
      // Check if organization already has an active subscription
      const existingSubscription = await this.getActiveSubscription(subscriptionData.organizationId);
      if (existingSubscription) {
        throw new Error('Organization already has an active subscription');
      }

      // Get tier configuration
      const tierConfig = this.getTierConfiguration(subscriptionData.tier);

      // Calculate pricing and trial
      const now = new Date();
      const startDate = subscriptionData.startDate ? new Date(subscriptionData.startDate) : now;
      const trialDays = subscriptionData.trialDays || tierConfig.defaultTrialDays;
      const isTrialActive = trialDays > 0;
      const trialEndDate = isTrialActive ? new Date(startDate.getTime() + trialDays * 24 * 60 * 60 * 1000) : undefined;

      // Create subscription
      const subscriptionId = uuidv4();
      const subscription = {
        id: subscriptionId,
        organizationId: subscriptionData.organizationId,
        tier: subscriptionData.tier,
        status: isTrialActive ? SubscriptionStatus.TRIAL : SubscriptionStatus.ACTIVE,
        billingCycle: subscriptionData.billingCycle,
        pricing: tierConfig.pricing[subscriptionData.billingCycle],
        limits: {
          ...tierConfig.limits,
          ...subscriptionData.customLimits
        },
        usage: {
          currentUsers: 0,
          currentDocuments: 0,
          currentStorage: 0,
          currentApiCalls: 0,
          currentWorkflows: 0,
          lastUpdated: now.toISOString()
        },
        billing: {
          startDate: startDate.toISOString(),
          nextBillingDate: isTrialActive ? trialEndDate?.toISOString() : this.calculateNextBillingDate(startDate, subscriptionData.billingCycle),
          paymentMethodId: subscriptionData.paymentMethodId,
          invoiceHistory: []
        },
        trial: {
          isTrialActive,
          trialStartDate: isTrialActive ? startDate.toISOString() : undefined,
          trialEndDate: trialEndDate?.toISOString(),
          trialDaysRemaining: isTrialActive ? trialDays : undefined
        },
        features: tierConfig.features,
        createdBy: user.id,
        createdAt: now.toISOString(),
        updatedAt: now.toISOString(),
        tenantId: user.tenantId
      };

      await db.createItem('subscriptions', subscription);

      // Cache subscription
      await redis.setex(`subscription:${subscriptionId}`, 1800, JSON.stringify(subscription));

      logger.info('Subscription created successfully', {
        subscriptionId,
        organizationId: subscriptionData.organizationId,
        tier: subscriptionData.tier,
        isTrialActive,
        correlationId
      });

      return {
        operation: 'create',
        subscriptionId,
        subscription
      };

    } catch (error) {
      logger.error('Subscription creation failed', {
        error: error instanceof Error ? error.message : String(error),
        organizationId: subscriptionData.organizationId,
        correlationId
      });
      throw error;
    }
  }

  // Additional helper methods for completeness
  private async updateSubscription(updateData: UpdateSubscriptionData, user: any, correlationId: string): Promise<SubscriptionResults> {
    try {
      const subscription = await db.readItem('subscriptions', updateData.subscriptionId, updateData.subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const updatedSubscription = {
        ...(subscription as any),
        ...updateData,
        updatedAt: new Date().toISOString(),
        updatedBy: user.id
      };

      await db.updateItem('subscriptions', updatedSubscription);
      await redis.setex(`subscription:${updateData.subscriptionId}`, 1800, JSON.stringify(updatedSubscription));

      return { operation: 'update', subscriptionId: updateData.subscriptionId, subscription: updatedSubscription };
    } catch (error) {
      logger.error('Subscription update failed', { error: error instanceof Error ? error.message : String(error), correlationId });
      throw error;
    }
  }

  private async cancelSubscription(subscriptionId: string, user: any, correlationId: string): Promise<SubscriptionResults> {
    try {
      const subscription = await db.readItem('subscriptions', subscriptionId, subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const cancelledSubscription = {
        ...(subscription as any),
        status: SubscriptionStatus.CANCELLED,
        updatedAt: new Date().toISOString(),
        cancelledBy: user.id,
        cancelledAt: new Date().toISOString()
      };

      await db.updateItem('subscriptions', cancelledSubscription);
      await redis.del(`subscription:${subscriptionId}`);

      return { operation: 'cancel', subscriptionId, subscription: cancelledSubscription };
    } catch (error) {
      logger.error('Subscription cancellation failed', { error: error instanceof Error ? error.message : String(error), correlationId });
      throw error;
    }
  }

  private async getSubscription(subscriptionId: string, _user: any, correlationId: string): Promise<SubscriptionResults> {
    try {
      // Try cache first
      const cached = await redis.get(`subscription:${subscriptionId}`);
      if (cached) {
        return { operation: 'get', subscriptionId, subscription: JSON.parse(cached) };
      }

      const subscription = await db.readItem('subscriptions', subscriptionId, subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      // Cache for future requests
      await redis.setex(`subscription:${subscriptionId}`, 1800, JSON.stringify(subscription));

      return { operation: 'get', subscriptionId, subscription };
    } catch (error) {
      logger.error('Get subscription failed', { error: error instanceof Error ? error.message : String(error), correlationId });
      throw error;
    }
  }

  private async listSubscriptions(organizationId: string, _user: any, correlationId: string): Promise<SubscriptionResults> {
    try {
      const subscriptionsQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId ORDER BY c.createdAt DESC';
      const subscriptions = await db.queryItems('subscriptions', subscriptionsQuery, [
        { name: '@orgId', value: organizationId }
      ]);

      return { operation: 'list', subscriptions };
    } catch (error) {
      logger.error('List subscriptions failed', { error: error instanceof Error ? error.message : String(error), correlationId });
      throw error;
    }
  }

  private async updateSubscriptionUsage(usageData: UsageUpdateData, _user: any, correlationId: string): Promise<SubscriptionResults> {
    try {
      const subscription = await db.readItem('subscriptions', usageData.subscriptionId, usageData.subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const updatedSubscription = {
        id: usageData.subscriptionId,
        ...(subscription as any),
        usage: {
          ...(subscription as any).usage,
          ...usageData.usage,
          lastUpdated: new Date().toISOString()
        },
        updatedAt: new Date().toISOString()
      };

      await db.updateItem('subscriptions', updatedSubscription);
      await redis.setex(`subscription:${usageData.subscriptionId}`, 1800, JSON.stringify(updatedSubscription));

      return { operation: 'usage_update', subscriptionId: usageData.subscriptionId, usageUpdated: true };
    } catch (error) {
      logger.error('Subscription usage update failed', { error: error instanceof Error ? error.message : String(error), correlationId });
      throw error;
    }
  }

  private async getActiveSubscription(organizationId: string): Promise<any> {
    try {
      const subscriptionQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.status IN (@active, @trial)';
      const subscriptions = await db.queryItems('subscriptions', subscriptionQuery, [
        { name: '@orgId', value: organizationId },
        { name: '@active', value: SubscriptionStatus.ACTIVE },
        { name: '@trial', value: SubscriptionStatus.TRIAL }
      ]);
      return subscriptions.length > 0 ? subscriptions[0] : null;
    } catch (error) {
      logger.error('Failed to get active subscription', { error: error instanceof Error ? error.message : String(error), organizationId });
      return null;
    }
  }

  private getTierConfiguration(tier: SubscriptionTier): any {
    const configurations = {
      [SubscriptionTier.FREE]: {
        limits: { maxUsers: 3, maxDocuments: 100, maxStorage: 1, maxApiCalls: 1000, maxWorkflows: 5, maxIntegrations: 2, maxBackups: 1 },
        features: { advancedAnalytics: false, prioritySupport: false, customBranding: false, apiAccess: false, ssoIntegration: false, auditLogs: false, dataExport: false, customWorkflows: false },
        pricing: { [BillingCycle.MONTHLY]: { basePrice: 0, currency: 'USD', taxRate: 0 }, [BillingCycle.YEARLY]: { basePrice: 0, currency: 'USD', taxRate: 0 } },
        defaultTrialDays: 14
      },
      [SubscriptionTier.PROFESSIONAL]: {
        limits: { maxUsers: 25, maxDocuments: 10000, maxStorage: 100, maxApiCalls: 50000, maxWorkflows: 50, maxIntegrations: 10, maxBackups: 10 },
        features: { advancedAnalytics: true, prioritySupport: true, customBranding: false, apiAccess: true, ssoIntegration: false, auditLogs: true, dataExport: true, customWorkflows: true },
        pricing: { [BillingCycle.MONTHLY]: { basePrice: 29, currency: 'USD', taxRate: 0.08 }, [BillingCycle.YEARLY]: { basePrice: 290, currency: 'USD', taxRate: 0.08 } },
        defaultTrialDays: 30
      },
      [SubscriptionTier.ENTERPRISE]: {
        limits: { maxUsers: -1, maxDocuments: -1, maxStorage: -1, maxApiCalls: -1, maxWorkflows: -1, maxIntegrations: -1, maxBackups: -1 },
        features: { advancedAnalytics: true, prioritySupport: true, customBranding: true, apiAccess: true, ssoIntegration: true, auditLogs: true, dataExport: true, customWorkflows: true },
        pricing: { [BillingCycle.MONTHLY]: { basePrice: 99, currency: 'USD', taxRate: 0.08 }, [BillingCycle.YEARLY]: { basePrice: 990, currency: 'USD', taxRate: 0.08 } },
        defaultTrialDays: 30
      }
    };
    return configurations[tier as keyof typeof configurations] || configurations[SubscriptionTier.FREE];
  }

  private calculateNextBillingDate(startDate: Date, billingCycle: BillingCycle): string {
    const nextDate = new Date(startDate);
    switch (billingCycle) {
      case BillingCycle.MONTHLY:
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case BillingCycle.YEARLY:
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
      case BillingCycle.LIFETIME:
        return '';
    }
    return nextDate.toISOString();
  }

  private async storeWebhookEvent(eventId: string, payload: LemonSqueezyWebhookPayload, correlationId: string): Promise<void> {
    try {
      await db.createItem('lemonsqueezy-webhook-events', {
        id: uuidv4(),
        eventId,
        eventType: payload.meta.event_name,
        dataType: payload.data.type,
        dataId: payload.data.id,
        testMode: payload.meta.test_mode,
        webhookId: payload.meta.webhook_id,
        payload,
        correlationId,
        processedAt: new Date().toISOString(),
        createdAt: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error storing webhook event', { eventId, correlationId, error: error instanceof Error ? error.message : String(error) });
    }
  }

  private async processSubscriptionWebhook(payload: LemonSqueezyWebhookPayload, correlationId: string): Promise<void> {
    // Implementation for subscription webhook processing
    logger.info('Processing subscription webhook', { eventType: payload.meta.event_name, correlationId });
  }

  private async processPaymentWebhook(payload: LemonSqueezyWebhookPayload, correlationId: string): Promise<void> {
    // Implementation for payment webhook processing
    logger.info('Processing payment webhook', { eventType: payload.meta.event_name, correlationId });
  }

  private async processOrderWebhook(payload: LemonSqueezyWebhookPayload, correlationId: string): Promise<void> {
    // Implementation for order webhook processing
    logger.info('Processing order webhook', { eventType: payload.meta.event_name, correlationId });
  }

  private async testStorageConnection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      // Production provider-specific connection tests
      switch (config.provider) {
        case 'azure_blob':
          return await this.testAzureBlobConnection(config);
        case 'aws_s3':
          return await this.testAWSS3Connection(config);
        case 'google_cloud':
          return await this.testGoogleCloudConnection(config);
        case 'dropbox':
          return await this.testDropboxConnection(config);
        case 'onedrive':
          return await this.testOneDriveConnection(config);
        case 'sharepoint':
          return await this.testSharePointConnection(config);
        default:
          return { success: false, error: `Unsupported storage provider: ${config.provider}` };
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  private async testAzureBlobConnection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      const { BlobServiceClient } = require('@azure/storage-blob');
      const blobServiceClient = BlobServiceClient.fromConnectionString(config.connectionString);

      // Test by listing containers (minimal operation)
      const containerIterator = blobServiceClient.listContainers();
      await containerIterator.next();

      return { success: true };
    } catch (error) {
      return { success: false, error: `Azure Blob connection failed: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private async testAWSS3Connection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      const AWS = require('aws-sdk');
      const s3 = new AWS.S3({
        accessKeyId: config.credentials?.accessKey,
        secretAccessKey: config.credentials?.secretKey,
        region: config.region || 'us-east-1'
      });

      // Test by listing buckets
      await s3.listBuckets().promise();

      return { success: true };
    } catch (error) {
      return { success: false, error: `AWS S3 connection failed: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private async testGoogleCloudConnection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      const { Storage } = require('@google-cloud/storage');
      const storage = new Storage({
        projectId: config.projectId,
        keyFilename: config.credentials?.keyFile
      });

      // Test by listing buckets
      await storage.getBuckets();

      return { success: true };
    } catch (error) {
      return { success: false, error: `Google Cloud connection failed: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private async testDropboxConnection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      const fetch = require('node-fetch');
      const response = await fetch('https://api.dropboxapi.com/2/users/get_current_account', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.credentials?.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: `Dropbox connection failed: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private async testOneDriveConnection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      const fetch = require('node-fetch');
      const response = await fetch('https://graph.microsoft.com/v1.0/me/drive', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.credentials?.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: `OneDrive connection failed: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private async testSharePointConnection(config: StorageConfiguration): Promise<{ success: boolean; error?: string }> {
    try {
      const fetch = require('node-fetch');
      const siteUrl = config.siteUrl || 'https://tenant.sharepoint.com/sites/sitename';
      const response = await fetch(`${siteUrl}/_api/web`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${config.credentials?.accessToken}`,
          'Accept': 'application/json;odata=verbose'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: `SharePoint connection failed: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  private encrypt(value: string): string {
    try {
      // Production AES encryption
      const crypto = require('crypto');
      const algorithm = 'aes-256-gcm';
      const secretKey = process.env.ENCRYPTION_SECRET_KEY || 'default-secret-key-change-in-production';

      // Generate a random initialization vector
      const iv = crypto.randomBytes(16);

      // Create cipher
      const cipher = crypto.createCipher(algorithm, secretKey);
      cipher.setAAD(Buffer.from('external-services', 'utf8'));

      // Encrypt the value
      let encrypted = cipher.update(value, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // Get the authentication tag
      const authTag = cipher.getAuthTag();

      // Combine IV, auth tag, and encrypted data
      const result = {
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex'),
        encrypted: encrypted
      };

      return Buffer.from(JSON.stringify(result)).toString('base64');
    } catch (error) {
      logger.error('Encryption failed', { error: error instanceof Error ? error.message : String(error) });
      // Fallback to base64 encoding if encryption fails
      return Buffer.from(value).toString('base64');
    }
  }

  private decrypt(encryptedValue: string): string {
    try {
      // Production AES decryption
      const crypto = require('crypto');
      const algorithm = 'aes-256-gcm';
      const secretKey = process.env.ENCRYPTION_SECRET_KEY || 'default-secret-key-change-in-production';

      // Parse the encrypted data
      const data = JSON.parse(Buffer.from(encryptedValue, 'base64').toString('utf8'));

      // Create decipher
      const decipher = crypto.createDecipher(algorithm, secretKey);
      decipher.setAAD(Buffer.from('external-services', 'utf8'));
      decipher.setAuthTag(Buffer.from(data.authTag, 'hex'));

      // Decrypt the value
      let decrypted = decipher.update(data.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      logger.error('Decryption failed', { error: error instanceof Error ? error.message : String(error) });
      // Fallback to base64 decoding if decryption fails
      try {
        return Buffer.from(encryptedValue, 'base64').toString('utf8');
      } catch (fallbackError) {
        return encryptedValue; // Return as-is if all else fails
      }
    }
  }
}

// Create instance of the manager
const externalServicesManager = new UnifiedExternalServicesManager();

// Register HTTP functions
app.http('external-services-process', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'external-services/process',
  handler: (request, context) => externalServicesManager.processExternalService(request, context)
});

app.http('lemonsqueezy-webhook', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'webhooks/lemonsqueezy',
  handler: (request, context) => externalServicesManager.processLemonSqueezyWebhook(request, context)
});

app.http('storage-configure', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'storage/configure',
  handler: (request, context) => externalServicesManager.processExternalService(request, context)
});

app.http('storage-sync', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'storage/sync',
  handler: (request, context) => externalServicesManager.processExternalService(request, context)
});

app.http('subscription-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'subscriptions',
  handler: (request, context) => externalServicesManager.processExternalService(request, context)
});

app.http('subscription-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'subscriptions/{subscriptionId}',
  handler: (request, context) => externalServicesManager.processExternalService(request, context)
});
