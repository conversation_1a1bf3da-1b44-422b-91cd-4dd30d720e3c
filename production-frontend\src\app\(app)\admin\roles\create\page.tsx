"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { AdminOnly } from "@/components/permission-guard";
import { RoleScope } from "@/types/role";
import { PermissionAction } from "@/lib/permissions";
import { useOrganizations } from "@/hooks/organizations";
import { ArrowLeft, Save } from "lucide-react";
import Link from "next/link";

// Form schema
const formSchema = z.object({
  name: z.string().min(3, "Name must be at least 3 characters").max(50, "Name must be at most 50 characters"),
  description: z.string().max(200, "Description must be at most 200 characters").optional(),
  scope: z.enum([RoleScope.SYSTEM, RoleScope.ORGANIZATION, RoleScope.PROJECT]),
  organizationId: z.string().optional(),
  permissions: z.array(z.string()).min(1, "At least one permission must be selected")
});

type FormValues = z.infer<typeof formSchema>;

// Permission categories
const permissionCategories = [
  {
    name: "Organization",
    permissions: [
      { id: PermissionAction.CREATE_ORGANIZATION, label: "Create Organization" },
      { id: PermissionAction.UPDATE_ORGANIZATION, label: "Update Organization" },
      { id: PermissionAction.DELETE_ORGANIZATION, label: "Delete Organization" },
      { id: PermissionAction.VIEW_ORGANIZATION, label: "View Organization" },
      { id: PermissionAction.MANAGE_ORGANIZATION, label: "Manage Organization" },
      { id: PermissionAction.VIEW_ORGANIZATION_MEMBERS, label: "View Organization Members" },
      { id: PermissionAction.INVITE_ORGANIZATION_MEMBER, label: "Invite Organization Member" },
      { id: PermissionAction.REMOVE_ORGANIZATION_MEMBER, label: "Remove Organization Member" },
      { id: PermissionAction.MANAGE_ORGANIZATION_MEMBERS, label: "Manage Organization Members" }
    ]
  },
  {
    name: "Project",
    permissions: [
      { id: PermissionAction.CREATE_PROJECT, label: "Create Project" },
      { id: PermissionAction.UPDATE_PROJECT, label: "Update Project" },
      { id: PermissionAction.DELETE_PROJECT, label: "Delete Project" },
      { id: PermissionAction.VIEW_PROJECT, label: "View Project" },
      { id: PermissionAction.MANAGE_PROJECT, label: "Manage Project" },
      { id: PermissionAction.ADD_PROJECT_MEMBER, label: "Add Project Member" },
      { id: PermissionAction.REMOVE_PROJECT_MEMBER, label: "Remove Project Member" },
      { id: PermissionAction.MANAGE_PROJECT_MEMBERS, label: "Manage Project Members" }
    ]
  },
  {
    name: "Document",
    permissions: [
      { id: PermissionAction.UPLOAD_DOCUMENT, label: "Upload Document" },
      { id: PermissionAction.VIEW_DOCUMENT, label: "View Document" },
      { id: PermissionAction.UPDATE_DOCUMENT, label: "Update Document" },
      { id: PermissionAction.DELETE_DOCUMENT, label: "Delete Document" },
      { id: PermissionAction.SHARE_DOCUMENT, label: "Share Document" },
      { id: PermissionAction.COMMENT_DOCUMENT, label: "Comment on Document" },
      { id: PermissionAction.EDIT_ANY_COMMENT, label: "Edit Any Comment" },
      { id: PermissionAction.DELETE_ANY_COMMENT, label: "Delete Any Comment" }
    ]
  },
  {
    name: "Workflow",
    permissions: [
      { id: PermissionAction.CREATE_WORKFLOW, label: "Create Workflow" },
      { id: PermissionAction.UPDATE_WORKFLOW, label: "Update Workflow" },
      { id: PermissionAction.DELETE_WORKFLOW, label: "Delete Workflow" },
      { id: PermissionAction.EXECUTE_WORKFLOW, label: "Execute Workflow" },
      { id: PermissionAction.VIEW_WORKFLOW, label: "View Workflow" },
      { id: PermissionAction.APPROVE_WORKFLOW, label: "Approve Workflow" },
      { id: PermissionAction.REJECT_WORKFLOW, label: "Reject Workflow" },
      { id: PermissionAction.ASSIGN_WORKFLOW, label: "Assign Workflow" }
    ]
  },
  {
    name: "Template",
    permissions: [
      { id: PermissionAction.CREATE_TEMPLATE, label: "Create Template" },
      { id: PermissionAction.UPDATE_TEMPLATE, label: "Update Template" },
      { id: PermissionAction.DELETE_TEMPLATE, label: "Delete Template" },
      { id: PermissionAction.SHARE_TEMPLATE, label: "Share Template" },
      { id: PermissionAction.VIEW_TEMPLATE, label: "View Template" },
      { id: PermissionAction.USE_TEMPLATE, label: "Use Template" }
    ]
  },
  {
    name: "Analytics",
    permissions: [
      { id: PermissionAction.VIEW_ANALYTICS, label: "View Analytics" },
      { id: PermissionAction.CREATE_REPORT, label: "Create Report" },
      { id: PermissionAction.UPDATE_REPORT, label: "Update Report" },
      { id: PermissionAction.DELETE_REPORT, label: "Delete Report" },
      { id: PermissionAction.GENERATE_REPORT, label: "Generate Report" },
      { id: PermissionAction.EXPORT_DATA, label: "Export Data" }
    ]
  },
  {
    name: "Permission Management",
    permissions: [
      { id: PermissionAction.VIEW_USER_PERMISSIONS, label: "View User Permissions" },
      { id: PermissionAction.CHECK_USER_PERMISSIONS, label: "Check User Permissions" },
      { id: PermissionAction.GRANT_PERMISSION, label: "Grant Permission" },
      { id: PermissionAction.REVOKE_PERMISSION, label: "Revoke Permission" }
    ]
  }
];

export default function CreateRolePage() {
  const router = useRouter();
  const { toast } = useToast();
  const { organizations, isLoading: isLoadingOrgs } = useOrganizations();
  const [activeTab, setActiveTab] = useState("organization");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      scope: RoleScope.ORGANIZATION,
      permissions: []
    }
  });

  // Watch form values
  const scope = form.watch("scope");
  // organizationId is watched but not used directly in this component

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      const response = await fetch('/management/advanced-roles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values)
      });

      if (!response.ok) {
        throw new Error('Failed to create role');
      }

      const createdRole = await response.json();

      toast({
        title: "Role created",
        description: `Role "${values.name}" has been created successfully.`
      });

      router.push("/admin/roles");
    } catch (error: any) {
      toast({
        title: "Error creating role",
        description: error.message || "An error occurred while creating the role. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle scope change
  const handleScopeChange = (value: string) => {
    // Validate that the value is a valid RoleScope
    if (value === RoleScope.SYSTEM || value === RoleScope.ORGANIZATION || value === RoleScope.PROJECT) {
      form.setValue("scope", value);
    }

    // Clear organization ID if scope is not organization or project
    if (value !== RoleScope.ORGANIZATION && value !== RoleScope.PROJECT) {
      form.setValue("organizationId", undefined);
    }
  };

  // Select all permissions in a category
  const selectAllInCategory = (category: string) => {
    const categoryPermissions = permissionCategories
      .find(c => c.name === category)
      ?.permissions.map(p => p.id) || [];

    const currentPermissions = form.getValues("permissions");
    const newPermissions = [...new Set([...currentPermissions, ...categoryPermissions])];

    form.setValue("permissions", newPermissions);
  };

  // Deselect all permissions in a category
  const deselectAllInCategory = (category: string) => {
    const categoryPermissions = permissionCategories
      .find(c => c.name === category)
      ?.permissions.map(p => p.id) || [];

    const currentPermissions = form.getValues("permissions");
    const newPermissions = currentPermissions.filter((p: string) => !categoryPermissions.includes(p as PermissionAction));

    form.setValue("permissions", newPermissions);
  };

  return (
    <AdminOnly>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" asChild>
              <Link href="/admin/roles">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">Create Role</h1>
          </div>
          <Button
            type="submit"
            form="create-role-form"
            disabled={isSubmitting}
          >
            <Save className="mr-2 h-4 w-4" />
            Save Role
          </Button>
        </div>

        <Form {...form}>
          <form id="create-role-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Role Details</CardTitle>
                <CardDescription>
                  Basic information about the role
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Role name" {...field} />
                      </FormControl>
                      <FormDescription>
                        A unique name for the role
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Role description"
                          className="resize-none"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        A brief description of the role&apos;s purpose
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="scope"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Scope</FormLabel>
                      <Select
                        onValueChange={handleScopeChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select scope" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={RoleScope.SYSTEM}>System</SelectItem>
                          <SelectItem value={RoleScope.ORGANIZATION}>Organization</SelectItem>
                          <SelectItem value={RoleScope.PROJECT}>Project</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The scope in which this role applies
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {(scope === RoleScope.ORGANIZATION || scope === RoleScope.PROJECT) && (
                  <FormField
                    control={form.control}
                    name="organizationId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={isLoadingOrgs}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select organization" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {organizations?.map((org: { id: string; name: string }) => (
                              <SelectItem key={org.id} value={org.id}>
                                {org.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The organization this role belongs to
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Permissions</CardTitle>
                <CardDescription>
                  Select the permissions for this role
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-7">
                    {permissionCategories.map(category => (
                      <TabsTrigger key={category.name} value={category.name.toLowerCase()}>
                        {category.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  {permissionCategories.map(category => (
                    <TabsContent key={category.name} value={category.name.toLowerCase()} className="space-y-4">
                      <div className="flex justify-between">
                        <h3 className="text-lg font-medium">{category.name} Permissions</h3>
                        <div className="space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => selectAllInCategory(category.name)}
                          >
                            Select All
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => deselectAllInCategory(category.name)}
                          >
                            Deselect All
                          </Button>
                        </div>
                      </div>

                      <Separator />

                      <div className="grid grid-cols-2 gap-4">
                        {category.permissions.map(permission => (
                          <FormField
                            key={permission.id}
                            control={form.control}
                            name="permissions"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(permission.id)}
                                    onCheckedChange={(checked) => {
                                      const currentPermissions = field.value || [];
                                      if (checked) {
                                        field.onChange([...currentPermissions, permission.id]);
                                      } else {
                                        field.onChange(
                                          currentPermissions.filter(p => p !== permission.id)
                                        );
                                      }
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {permission.label}
                                </FormLabel>
                              </FormItem>
                            )}
                          />
                        ))}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>

                <FormField
                  control={form.control}
                  name="permissions"
                  render={() => (
                    <FormItem className="mt-4">
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Creating..." : "Create Role"}
                </Button>
              </CardFooter>
            </Card>
          </form>
        </Form>
      </div>
    </AdminOnly>
  );
}
