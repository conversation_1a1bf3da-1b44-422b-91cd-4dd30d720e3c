/**
 * AI Personalization Hook
 * Provides personalized AI experiences, recommendations, and behavior tracking
 */

import { useState, useEffect, useCallback } from 'react'
import { personalizationService, PersonalizationRecommendation, BehaviorAnalysis, PersonalizedPrompt } from '@/services/personalization-service'
import { useToast } from '@/hooks/use-toast'

interface PersonalizationState {
  recommendations: PersonalizationRecommendation[]
  behaviorAnalysis: BehaviorAnalysis | null
  personalizedPrompts: PersonalizedPrompt[]
  isLoading: boolean
  error: string | null
  lastUpdated: number | null
}

interface PersonalizationActions {
  getRecommendations: (type?: string, limit?: number) => Promise<void>
  getBehaviorAnalysis: (timeRange?: string) => Promise<void>
  getPersonalizedPrompts: (context?: string, documentId?: string) => Promise<void>
  trackFeatureUsage: (feature: string, context?: Record<string, any>) => Promise<void>
  trackDocumentInteraction: (documentId: string, action: string, outcome?: 'positive' | 'negative' | 'neutral') => Promise<void>
  trackAIFeedback: (operationId: string, feedback: 'helpful' | 'not_helpful' | 'incorrect', details?: string) => Promise<void>
  getSmartSuggestions: (context: Record<string, any>) => Promise<PersonalizationRecommendation[]>
  refreshData: () => Promise<void>
  clearCache: () => void
}

export function usePersonalization(): PersonalizationState & PersonalizationActions {
  const [state, setState] = useState<PersonalizationState>({
    recommendations: [],
    behaviorAnalysis: null,
    personalizedPrompts: [],
    isLoading: false,
    error: null,
    lastUpdated: null
  })

  const { toast } = useToast()

  // Get personalized recommendations
  const getRecommendations = useCallback(async (type?: string, limit: number = 10) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const recommendations = await personalizationService.getRecommendations(type, limit)
      setState(prev => ({
        ...prev,
        recommendations,
        isLoading: false,
        lastUpdated: Date.now()
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get recommendations'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }))
      
      toast({
        title: 'Error',
        description: 'Failed to load personalized recommendations',
        variant: 'destructive'
      })
    }
  }, [toast])

  // Get behavior analysis
  const getBehaviorAnalysis = useCallback(async (timeRange: string = '30d') => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const analysis = await personalizationService.getBehaviorAnalysis(timeRange)
      setState(prev => ({
        ...prev,
        behaviorAnalysis: analysis,
        isLoading: false,
        lastUpdated: Date.now()
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get behavior analysis'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }))
    }
  }, [])

  // Get personalized prompts
  const getPersonalizedPrompts = useCallback(async (context?: string, documentId?: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const prompts = await personalizationService.getPersonalizedPrompts(context, documentId)
      setState(prev => ({
        ...prev,
        personalizedPrompts: prompts,
        isLoading: false,
        lastUpdated: Date.now()
      }))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get personalized prompts'
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isLoading: false
      }))
    }
  }, [])

  // Track feature usage
  const trackFeatureUsage = useCallback(async (feature: string, context: Record<string, any> = {}) => {
    try {
      await personalizationService.trackFeatureUsage(feature, context)
    } catch (error) {
      console.warn('Failed to track feature usage:', error)
    }
  }, [])

  // Track document interaction
  const trackDocumentInteraction = useCallback(async (
    documentId: string,
    action: string,
    outcome: 'positive' | 'negative' | 'neutral' = 'positive'
  ) => {
    try {
      await personalizationService.trackDocumentInteraction(documentId, action, outcome)
    } catch (error) {
      console.warn('Failed to track document interaction:', error)
    }
  }, [])

  // Track AI feedback
  const trackAIFeedback = useCallback(async (
    operationId: string,
    feedback: 'helpful' | 'not_helpful' | 'incorrect',
    details?: string
  ) => {
    try {
      await personalizationService.trackAIFeedback(operationId, feedback, details)
      
      // Show feedback confirmation
      toast({
        title: 'Feedback Recorded',
        description: 'Thank you for your feedback! This helps improve AI responses.',
      })
    } catch (error) {
      console.warn('Failed to track AI feedback:', error)
    }
  }, [toast])

  // Get smart suggestions based on context
  const getSmartSuggestions = useCallback(async (context: Record<string, any>): Promise<PersonalizationRecommendation[]> => {
    try {
      return await personalizationService.getSmartSuggestions(context)
    } catch (error) {
      console.warn('Failed to get smart suggestions:', error)
      return []
    }
  }, [])

  // Refresh all personalization data
  const refreshData = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      await Promise.all([
        getRecommendations(),
        getBehaviorAnalysis(),
        getPersonalizedPrompts()
      ])
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to refresh personalization data',
        isLoading: false
      }))
    }
  }, [getRecommendations, getBehaviorAnalysis, getPersonalizedPrompts])

  // Clear cache
  const clearCache = useCallback(() => {
    personalizationService.clearCache()
    setState({
      recommendations: [],
      behaviorAnalysis: null,
      personalizedPrompts: [],
      isLoading: false,
      error: null,
      lastUpdated: null
    })
  }, [])

  // Auto-load initial data
  useEffect(() => {
    getRecommendations()
  }, [getRecommendations])

  return {
    ...state,
    getRecommendations,
    getBehaviorAnalysis,
    getPersonalizedPrompts,
    trackFeatureUsage,
    trackDocumentInteraction,
    trackAIFeedback,
    getSmartSuggestions,
    refreshData,
    clearCache
  }
}

// Hook for tracking user interactions automatically
export function useInteractionTracking() {
  const { trackFeatureUsage, trackDocumentInteraction } = usePersonalization()

  // Track page views
  const trackPageView = useCallback((page: string, context: Record<string, any> = {}) => {
    trackFeatureUsage('page_view', { page, ...context })
  }, [trackFeatureUsage])

  // Track button clicks
  const trackButtonClick = useCallback((button: string, context: Record<string, any> = {}) => {
    trackFeatureUsage('button_click', { button, ...context })
  }, [trackFeatureUsage])

  // Track search queries
  const trackSearch = useCallback((query: string, resultCount: number, context: Record<string, any> = {}) => {
    trackFeatureUsage('search', { query, resultCount, ...context })
  }, [trackFeatureUsage])

  // Track AI interactions
  const trackAIInteraction = useCallback((type: string, context: Record<string, any> = {}) => {
    trackFeatureUsage('ai_interaction', { type, ...context })
  }, [trackFeatureUsage])

  return {
    trackPageView,
    trackButtonClick,
    trackSearch,
    trackAIInteraction,
    trackDocumentInteraction
  }
}

// Hook for personalized content recommendations
export function usePersonalizedContent(contentType?: string) {
  const [recommendations, setRecommendations] = useState<PersonalizationRecommendation[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { getRecommendations } = usePersonalization()

  useEffect(() => {
    const loadRecommendations = async () => {
      setIsLoading(true)
      try {
        await getRecommendations(contentType, 5)
      } finally {
        setIsLoading(false)
      }
    }

    loadRecommendations()
  }, [contentType, getRecommendations])

  return {
    recommendations,
    isLoading,
    refresh: () => getRecommendations(contentType, 5)
  }
}

// Hook for context-aware AI prompts
export function useContextualPrompts(context?: string, documentId?: string) {
  const [prompts, setPrompts] = useState<PersonalizedPrompt[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { getPersonalizedPrompts } = usePersonalization()

  useEffect(() => {
    const loadPrompts = async () => {
      setIsLoading(true)
      try {
        await getPersonalizedPrompts(context, documentId)
      } finally {
        setIsLoading(false)
      }
    }

    if (context || documentId) {
      loadPrompts()
    }
  }, [context, documentId, getPersonalizedPrompts])

  return {
    prompts,
    isLoading,
    refresh: () => getPersonalizedPrompts(context, documentId)
  }
}
