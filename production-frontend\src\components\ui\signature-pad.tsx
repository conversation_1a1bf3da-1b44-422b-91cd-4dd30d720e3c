"use client";

import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { cn } from '@/lib/utils';

export interface SignaturePadRef {
  clear: () => void;
  isEmpty: () => boolean;
  toDataURL: (type?: string, quality?: number) => string;
  fromDataURL: (dataURL: string) => void;
}

export interface SignaturePadProps {
  width?: number;
  height?: number;
  className?: string;
  penColor?: string;
  backgroundColor?: string;
  minWidth?: number;
  maxWidth?: number;
  velocityFilterWeight?: number;
  onBegin?: () => void;
  onEnd?: () => void;
  onChange?: (signature: string) => void;
}

export const SignaturePad = forwardRef<SignaturePadRef, SignaturePadProps>(
  ({
    width = 400,
    height = 200,
    className,
    penColor = '#000000',
    backgroundColor = '#ffffff',
    minWidth = 0.5,
    maxWidth = 2.5,
    velocityFilterWeight = 0.7,
    onBegin,
    onEnd,
    onChange,
  }, ref) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [isDrawing, setIsDrawing] = useState(false);
    const [lastPoint, setLastPoint] = useState<{ x: number; y: number } | null>(null);

    useImperativeHandle(ref, () => ({
      clear: () => {
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = backgroundColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            onChange?.('');
          }
        }
      },
      isEmpty: () => {
        const canvas = canvasRef.current;
        if (!canvas) return true;
        
        const ctx = canvas.getContext('2d');
        if (!ctx) return true;
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Check if all pixels are the background color or transparent
        for (let i = 0; i < data.length; i += 4) {
          if (data[i + 3] !== 0) { // Alpha channel
            return false;
          }
        }
        return true;
      },
      toDataURL: (type = 'image/png', quality = 1) => {
        const canvas = canvasRef.current;
        return canvas ? canvas.toDataURL(type, quality) : '';
      },
      fromDataURL: (dataURL: string) => {
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          if (ctx) {
            const img = new Image();
            img.onload = () => {
              ctx.clearRect(0, 0, canvas.width, canvas.height);
              ctx.drawImage(img, 0, 0);
              onChange?.(dataURL);
            };
            img.src = dataURL;
          }
        }
      },
    }));

    useEffect(() => {
      const canvas = canvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Set canvas size
      canvas.width = width;
      canvas.height = height;

      // Set background
      ctx.fillStyle = backgroundColor;
      ctx.fillRect(0, 0, width, height);

      // Set drawing styles
      ctx.strokeStyle = penColor;
      ctx.lineWidth = minWidth;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
    }, [width, height, backgroundColor, penColor, minWidth]);

    const getPointFromEvent = (event: React.MouseEvent | React.TouchEvent): { x: number; y: number } => {
      const canvas = canvasRef.current;
      if (!canvas) return { x: 0, y: 0 };

      const rect = canvas.getBoundingClientRect();
      const clientX = 'touches' in event ? event.touches[0].clientX : event.clientX;
      const clientY = 'touches' in event ? event.touches[0].clientY : event.clientY;

      return {
        x: clientX - rect.left,
        y: clientY - rect.top,
      };
    };

    const startDrawing = (event: React.MouseEvent | React.TouchEvent) => {
      event.preventDefault();
      const point = getPointFromEvent(event);
      setIsDrawing(true);
      setLastPoint(point);
      onBegin?.();
    };

    const draw = (event: React.MouseEvent | React.TouchEvent) => {
      event.preventDefault();
      if (!isDrawing || !lastPoint) return;

      const canvas = canvasRef.current;
      const ctx = canvas?.getContext('2d');
      if (!canvas || !ctx) return;

      const currentPoint = getPointFromEvent(event);

      ctx.beginPath();
      ctx.moveTo(lastPoint.x, lastPoint.y);
      ctx.lineTo(currentPoint.x, currentPoint.y);
      ctx.stroke();

      setLastPoint(currentPoint);
    };

    const stopDrawing = () => {
      if (isDrawing) {
        setIsDrawing(false);
        setLastPoint(null);
        onEnd?.();
        
        const canvas = canvasRef.current;
        if (canvas) {
          onChange?.(canvas.toDataURL());
        }
      }
    };

    return (
      <canvas
        ref={canvasRef}
        className={cn(
          'border border-gray-300 rounded-md cursor-crosshair',
          className
        )}
        onMouseDown={startDrawing}
        onMouseMove={draw}
        onMouseUp={stopDrawing}
        onMouseLeave={stopDrawing}
        onTouchStart={startDrawing}
        onTouchMove={draw}
        onTouchEnd={stopDrawing}
        style={{ touchAction: 'none' }}
      />
    );
  }
);

SignaturePad.displayName = 'SignaturePad';
