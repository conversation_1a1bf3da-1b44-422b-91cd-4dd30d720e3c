/**
 * PKI Configuration Panel
 * User interface for configuring PKI providers and policies
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import {
  Shield,
  Key,
  Award as Certificate,
  Settings,
  Plus,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface PKIProvider {
  providerId: string
  name: string
  type: 'cloud' | 'enterprise' | 'self-signed'
  status: 'active' | 'inactive' | 'error'
  certificateCount: number
  lastSync: Date
  compliance: {
    eidas: boolean
    esignAct: boolean
    cfr21Part11: boolean
  }
}

interface CertificatePolicy {
  id: string
  name: string
  certificateType: string
  validityPeriod: number
  autoRenewal: boolean
  complianceLevel: 'basic' | 'advanced' | 'qualified'
}

interface PKIConfigurationPanelProps {
  organizationId: string
  onConfigurationChange?: (config: any) => void
}

export function PKIConfigurationPanel({ 
  organizationId, 
  onConfigurationChange 
}: PKIConfigurationPanelProps) {
  const [providers, setProviders] = useState<PKIProvider[]>([])
  const [policies, setPolicies] = useState<CertificatePolicy[]>([])
  const [selectedProvider, setSelectedProvider] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [showAddProvider, setShowAddProvider] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    loadPKIConfiguration()
  }, [organizationId])

  const loadPKIConfiguration = async () => {
    try {
      setIsLoading(true)
      // Load PKI configuration from API
      const [providersResponse, policiesResponse] = await Promise.all([
        fetch('/api/pki/providers'),
        fetch('/api/pki/policies')
      ]);

      if (!providersResponse.ok) {
        throw new Error(`Failed to load providers: ${providersResponse.statusText}`);
      }

      if (!policiesResponse.ok) {
        throw new Error(`Failed to load policies: ${policiesResponse.statusText}`);
      }

      const providersData = await providersResponse.json();
      const policiesData = await policiesResponse.json();

      const loadedProviders: PKIProvider[] = providersData.providers || [];
      const loadedPolicies: CertificatePolicy[] = policiesData.policies || [];

      setProviders(loadedProviders);
      setPolicies(loadedPolicies);
      setSelectedProvider(loadedProviders[0]?.providerId || '');
    } catch (error) {
      toast({
        title: 'Error loading PKI configuration',
        description: 'Failed to load PKI settings. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleAddProvider = () => {
    setShowAddProvider(true)
  }

  const handleRemoveProvider = async (providerId: string) => {
    try {
      // Remove provider via API
      setProviders(prev => prev.filter(p => p.providerId !== providerId))
      toast({
        title: 'Provider removed',
        description: 'PKI provider has been removed successfully.'
      })
    } catch (error) {
      toast({
        title: 'Error removing provider',
        description: 'Failed to remove PKI provider. Please try again.',
        variant: 'destructive'
      })
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Info className="h-4 w-4 text-gray-500" />
    }
  }

  const getComplianceBadges = (compliance: PKIProvider['compliance']) => {
    const badges = []
    if (compliance.eidas) badges.push(<Badge key="eidas" variant="secondary">eIDAS</Badge>)
    if (compliance.esignAct) badges.push(<Badge key="esign" variant="secondary">ESIGN Act</Badge>)
    if (compliance.cfr21Part11) badges.push(<Badge key="cfr21" variant="secondary">21 CFR Part 11</Badge>)
    return badges
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading PKI configuration...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">PKI Configuration</h2>
          <p className="text-muted-foreground">
            Manage PKI providers, certificates, and signing policies for your organization
          </p>
        </div>
        <Button onClick={handleAddProvider} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Provider
        </Button>
      </div>

      <Tabs defaultValue="providers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="providers" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Providers
          </TabsTrigger>
          <TabsTrigger value="policies" className="flex items-center gap-2">
            <Certificate className="h-4 w-4" />
            Policies
          </TabsTrigger>
          <TabsTrigger value="compliance" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Compliance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <div className="grid gap-4">
            {providers.map((provider) => (
              <Card key={provider.providerId}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(provider.status)}
                    <CardTitle className="text-lg">{provider.name}</CardTitle>
                    <Badge variant={provider.type === 'cloud' ? 'default' : 'outline'}>
                      {provider.type}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedProvider(provider.providerId)}
                    >
                      Configure
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveProvider(provider.providerId)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <Label className="text-muted-foreground">Certificates</Label>
                      <p className="font-medium">{provider.certificateCount}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Last Sync</Label>
                      <p className="font-medium">{provider.lastSync.toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Label className="text-muted-foreground">Compliance</Label>
                    <div className="flex gap-2 mt-1">
                      {getComplianceBadges(provider.compliance)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {providers.length === 0 && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-8">
                <Shield className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No PKI Providers Configured</h3>
                <p className="text-muted-foreground text-center mb-4">
                  Add a PKI provider to enable advanced document signing capabilities
                </p>
                <Button onClick={handleAddProvider}>Add Your First Provider</Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="policies" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Certificate Policies</h3>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Policy
            </Button>
          </div>

          <div className="grid gap-4">
            {policies.map((policy) => (
              <Card key={policy.id}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-lg">{policy.name}</CardTitle>
                  <Badge variant={policy.complianceLevel === 'qualified' ? 'default' : 'secondary'}>
                    {policy.complianceLevel}
                  </Badge>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <Label className="text-muted-foreground">Certificate Type</Label>
                      <p className="font-medium">{policy.certificateType}</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Validity Period</Label>
                      <p className="font-medium">{policy.validityPeriod} months</p>
                    </div>
                    <div>
                      <Label className="text-muted-foreground">Auto Renewal</Label>
                      <p className="font-medium">{policy.autoRenewal ? 'Enabled' : 'Disabled'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Settings</CardTitle>
              <CardDescription>
                Configure compliance requirements for your organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>eIDAS Compliance</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable European electronic signature standards
                    </p>
                  </div>
                  <Switch />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>ESIGN Act Compliance</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable US federal electronic signature law compliance
                    </p>
                  </div>
                  <Switch />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>21 CFR Part 11</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable FDA electronic records and signatures compliance
                    </p>
                  </div>
                  <Switch />
                </div>
              </div>

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Compliance settings affect which PKI providers and certificate types are available.
                  Changes may require administrator approval.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Audit Settings</CardTitle>
              <CardDescription>
                Configure audit logging and monitoring
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="audit-level">Audit Level</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select audit level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">Basic</SelectItem>
                      <SelectItem value="detailed">Detailed</SelectItem>
                      <SelectItem value="comprehensive">Comprehensive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="retention-period">Retention Period</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select retention period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 year</SelectItem>
                      <SelectItem value="3">3 years</SelectItem>
                      <SelectItem value="7">7 years</SelectItem>
                      <SelectItem value="10">10 years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Real-time Monitoring</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable real-time monitoring of PKI operations
                  </p>
                </div>
                <Switch />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
