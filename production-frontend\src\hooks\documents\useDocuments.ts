/**
 * Documents Hook
 * Manages document operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { documentService } from '@/services/document-service'
import { useToast } from '@/hooks/use-toast'
import type { ID, DocumentProcessingStatus, DocumentFilters } from '@/types'
import type { Document } from '@/types/backend'

export interface UseDocumentsOptions {
  autoLoad?: boolean
  projectId?: ID
  organizationId?: ID
  filters?: DocumentFilters
  includeContent?: boolean
  includeMetadata?: boolean
}

export interface UseDocumentsResult {
  // State
  documents: Document[]
  currentDocument: Document | null
  loading: boolean
  error: string | null
  
  // Document operations
  loadDocuments: () => Promise<void>
  loadDocument: (documentId: ID) => Promise<void>
  createDocument: (data: any) => Promise<Document>
  updateDocument: (documentId: ID, updates: Partial<Document>) => Promise<void>
  deleteDocument: (documentId: ID) => Promise<void>
  
  // File operations
  uploadDocument: (file: File, metadata?: any) => Promise<Document>
  downloadDocument: (documentId: ID) => Promise<void>
  
  // Processing operations
  processDocument: (documentId: ID, options?: any) => Promise<void>
  analyzeDocument: (documentId: ID, options?: any) => Promise<any>
  getProcessingStatus: (documentId: ID) => Promise<DocumentProcessingStatus>
  
  // Sharing operations
  shareDocument: (documentId: ID, shareData: any) => Promise<void>
  getSharedDocuments: () => Promise<Document[]>
  
  // Bulk operations
  bulkDelete: (documentIds: ID[]) => Promise<{ success: ID[]; failed: ID[] }>
  bulkProcess: (documentIds: ID[], options?: any) => Promise<{ success: ID[]; failed: ID[] }>
  bulkDownload: (documentIds: ID[]) => Promise<void>
  
  // Search and filtering
  searchDocuments: (query: string) => Document[]
  filterDocuments: (filters: DocumentFilters) => Document[]
  
  // Utilities
  getDocumentsByStatus: (status: string) => Document[]
  getDocumentsByType: (type: string) => Document[]
  
  // Refresh
  refresh: () => Promise<void>
}

export function useDocuments(options: UseDocumentsOptions = {}): UseDocumentsResult {
  const { 
    autoLoad = true, 
    projectId, 
    organizationId, 
    filters,
    includeContent = false,
    includeMetadata = true
  } = options
  const { toast } = useToast()
  
  const [documents, setDocuments] = useState<Document[]>([])
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load documents
  const loadDocuments = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await documentService.getDocuments({
        projectId,
        organizationId
      })
      setDocuments(response.data || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load documents'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [projectId, organizationId, filters, includeContent, includeMetadata, toast])

  // Load specific document
  const loadDocument = useCallback(async (documentId: ID) => {
    setLoading(true)
    setError(null)

    try {
      const document = await documentService.getDocument(documentId)
      setCurrentDocument(document)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load document'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Create document
  const createDocument = useCallback(async (data: any): Promise<Document> => {
    try {
      const document = await documentService.createDocument(data)
      await loadDocuments()
      
      toast({
        type: 'success',
        title: 'Document created',
        description: `Document "${data.name || 'Untitled'}" has been created successfully.`,
      })
      
      return document
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create document'
      
      toast({
        type: 'error',
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadDocuments, toast])

  // Update document
  const updateDocument = useCallback(async (documentId: ID, updates: Partial<Document>) => {
    try {
      await documentService.updateDocument(documentId, updates)
      await loadDocuments()
      
      if (currentDocument?.id === documentId) {
        setCurrentDocument(prev => prev ? { ...prev, ...updates } : null)
      }
      
      toast({
        type: 'success',
        title: 'Document updated',
        description: 'Document has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update document'
      
      toast({
        type: 'error',
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [loadDocuments, currentDocument, toast])

  // Delete document
  const deleteDocument = useCallback(async (documentId: ID) => {
    try {
      await documentService.deleteDocument(documentId)
      await loadDocuments()
      
      if (currentDocument?.id === documentId) {
        setCurrentDocument(null)
      }
      
      toast({
        type: 'success',
        title: 'Document deleted',
        description: 'Document has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete document'
      
      toast({
        type: 'error',
        title: 'Deletion failed',
        description: errorMessage,
      })
    }
  }, [loadDocuments, currentDocument, toast])

  // Upload document
  const uploadDocument = useCallback(async (file: File, metadata?: any): Promise<Document> => {
    try {
      const document = await documentService.uploadDocument(file, metadata)
      await loadDocuments()
      
      toast({
        type: 'success',
        title: 'Document uploaded',
        description: `Document "${file.name}" has been uploaded successfully.`,
      })
      
      return document
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to upload document'
      
      toast({
        type: 'error',
        title: 'Upload failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadDocuments, toast])

  // Download document
  const downloadDocument = useCallback(async (documentId: ID) => {
    try {
      await documentService.downloadDocument(documentId)
      
      toast({
        type: 'success',
        title: 'Download started',
        description: 'Document download has been started.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to download document'
      
      toast({
        type: 'error',
        title: 'Download failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Process document
  const processDocument = useCallback(async (documentId: ID, options?: any) => {
    try {
      await documentService.processDocument(documentId, options)
      await loadDocuments()
      
      toast({
        type: 'success',
        title: 'Processing started',
        description: 'Document processing has been started.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to process document'
      
      toast({
        type: 'error',
        title: 'Processing failed',
        description: errorMessage,
      })
    }
  }, [loadDocuments, toast])

  // Analyze document
  const analyzeDocument = useCallback(async (documentId: ID, options?: any) => {
    try {
      const result = await documentService.analyzeDocument(documentId, options)
      
      toast({
        type: 'success',
        title: 'Analysis completed',
        description: 'Document analysis has been completed.',
      })
      
      return result
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to analyze document'
      
      toast({
        type: 'error',
        title: 'Analysis failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Get processing status
  const getProcessingStatus = useCallback(async (documentId: ID): Promise<DocumentProcessingStatus> => {
    try {
      return await documentService.getProcessingStatus(documentId)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to get processing status'
      
      toast({
        type: 'error',
        title: 'Status check failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Share document
  const shareDocument = useCallback(async (documentId: ID, shareData: any) => {
    try {
      await documentService.shareDocument(documentId, shareData)
      
      toast({
        type: 'success',
        title: 'Document shared',
        description: 'Document has been shared successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to share document'
      
      toast({
        type: 'error',
        title: 'Sharing failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Get shared documents
  const getSharedDocuments = useCallback(async (): Promise<Document[]> => {
    try {
      return await documentService.getSharedDocuments()
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load shared documents'
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Bulk operations
  const bulkDelete = useCallback(async (documentIds: ID[]): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const result = await documentService.bulkDeleteDocuments(documentIds)
      await loadDocuments()
      
      toast({
        type: 'success',
        title: 'Bulk deletion completed',
        description: `${result.success.length} documents deleted successfully.`,
      })
      
      return result
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete documents'
      
      toast({
        type: 'error',
        title: 'Bulk deletion failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadDocuments, toast])

  const bulkProcess = useCallback(async (documentIds: ID[], options?: any): Promise<{ success: ID[]; failed: ID[] }> => {
    try {
      const result = await documentService.bulkProcessDocuments(documentIds, options)
      await loadDocuments()
      
      toast({
        type: 'success',
        title: 'Bulk processing started',
        description: `${result.success.length} documents queued for processing.`,
      })
      
      return result
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to process documents'
      
      toast({
        type: 'error',
        title: 'Bulk processing failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadDocuments, toast])

  const bulkDownload = useCallback(async (documentIds: ID[]) => {
    try {
      await documentService.bulkDownloadDocuments(documentIds)
      
      toast({
        type: 'success',
        title: 'Bulk download started',
        description: 'Documents are being prepared for download.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to download documents'
      
      toast({
        type: 'error',
        title: 'Bulk download failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Search documents
  const searchDocuments = useCallback((query: string): Document[] => {
    if (!query.trim()) return documents
    
    const lowercaseQuery = query.toLowerCase()
    return documents.filter(doc =>
      doc.name.toLowerCase().includes(lowercaseQuery) ||
      doc.description?.toLowerCase().includes(lowercaseQuery)
    )
  }, [documents])

  // Filter documents
  const filterDocuments = useCallback((filters: DocumentFilters): Document[] => {
    let filtered = [...documents]

    if (filters.status && Array.isArray(filters.status)) {
      filtered = filtered.filter(doc => filters.status!.includes(doc.status))
    }

    if (filters.type && Array.isArray(filters.type)) {
      filtered = filtered.filter(doc => filters.type!.includes(doc.type))
    }

    if (filters.projectId) {
      filtered = filtered.filter(doc => doc.projectId === filters.projectId)
    }

    // Tags filtering removed as it's not in DocumentFilters interface
    
    if (filters.dateRange) {
      const start = new Date(filters.dateRange.start)
      const end = new Date(filters.dateRange.end)
      filtered = filtered.filter(doc => {
        const docDate = new Date(doc.createdAt)
        return docDate >= start && docDate <= end
      })
    }
    
    return filtered
  }, [documents])

  // Get documents by status
  const getDocumentsByStatus = useCallback((status: string): Document[] => {
    return documents.filter(doc => doc.status === status)
  }, [documents])

  // Get documents by type
  const getDocumentsByType = useCallback((type: string): Document[] => {
    return documents.filter(doc => doc.type === type)
  }, [documents])

  // Load documents on mount if autoLoad is enabled
  useEffect(() => {
    if (autoLoad) {
      loadDocuments()
    }
  }, [autoLoad, loadDocuments])

  return {
    // State
    documents,
    currentDocument,
    loading,
    error,
    
    // Document operations
    loadDocuments,
    loadDocument,
    createDocument,
    updateDocument,
    deleteDocument,
    
    // File operations
    uploadDocument,
    downloadDocument,
    
    // Processing operations
    processDocument,
    analyzeDocument,
    getProcessingStatus,
    
    // Sharing operations
    shareDocument,
    getSharedDocuments,
    
    // Bulk operations
    bulkDelete,
    bulkProcess,
    bulkDownload,
    
    // Search and filtering
    searchDocuments,
    filterDocuments,
    
    // Utilities
    getDocumentsByStatus,
    getDocumentsByType,
    
    // Refresh
    refresh: loadDocuments,
  }
}
