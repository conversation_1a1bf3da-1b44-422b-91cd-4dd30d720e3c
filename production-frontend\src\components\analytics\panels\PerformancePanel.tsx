import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/components/ui/charts';
import { Skeleton } from '@/components/ui/skeleton';

interface PerformanceData {
  responseTime: { date: string; time: number }[];
  errorRate: { date: string; rate: number }[];
  apiUsage: { endpoint: string; count: number }[];
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
}

interface PerformancePanelProps {
  data?: PerformanceData;
  isLoading?: boolean;
}

export function PerformancePanel({ data, isLoading = false }: PerformancePanelProps) {
  if (isLoading) {
    return (
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-72" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
          <CardDescription>No performance data available</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[300px]">
          <p className="text-muted-foreground">No performance data recorded yet</p>
        </CardContent>
      </Card>
    );
  }

  const responseTimeData = {
    labels: data.responseTime.map(item => item.date),
    datasets: [
      {
        label: 'Response Time (ms)',
        data: data.responseTime.map(item => item.time),
        borderColor: 'hsl(var(--primary))',
        backgroundColor: 'hsl(var(--primary) / 0.2)',
        tension: 0.3,
      },
    ],
  };

  const errorRateData = {
    labels: data.errorRate.map(item => item.date),
    datasets: [
      {
        label: 'Error Rate (%)',
        data: data.errorRate.map(item => item.rate * 100),
        borderColor: 'hsl(var(--destructive))',
        backgroundColor: 'hsl(var(--destructive) / 0.2)',
        tension: 0.3,
      },
    ],
  };

  const apiUsageData = {
    labels: data.apiUsage.map(item => item.endpoint),
    datasets: [
      {
        label: 'API Calls',
        data: data.apiUsage.map(item => item.count),
        backgroundColor: 'hsl(var(--primary))',
      },
    ],
  };

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Performance Metrics</CardTitle>
        <CardDescription>
          System performance and API usage statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="response-time">
          <TabsList className="mb-4">
            <TabsTrigger value="response-time">Response Time</TabsTrigger>
            <TabsTrigger value="error-rate">Error Rate</TabsTrigger>
            <TabsTrigger value="api-usage">API Usage</TabsTrigger>
            <TabsTrigger value="summary">Summary</TabsTrigger>
          </TabsList>
          <TabsContent value="response-time" className="h-[300px]">
            <LineChart data={responseTimeData} />
          </TabsContent>
          <TabsContent value="error-rate" className="h-[300px]">
            <LineChart data={errorRateData} />
          </TabsContent>
          <TabsContent value="api-usage" className="h-[300px]">
            <BarChart data={apiUsageData} />
          </TabsContent>
          <TabsContent value="summary">
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-1">Avg. Response Time</h3>
                <p className="text-2xl font-bold">{data.averageResponseTime.toFixed(0)} ms</p>
              </div>
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-1">P95 Response Time</h3>
                <p className="text-2xl font-bold">{data.p95ResponseTime.toFixed(0)} ms</p>
              </div>
              <div className="bg-muted/20 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-1">P99 Response Time</h3>
                <p className="text-2xl font-bold">{data.p99ResponseTime.toFixed(0)} ms</p>
              </div>
              <div className="bg-muted/20 p-4 rounded-lg col-span-3">
                <h3 className="text-sm font-medium mb-1">Current Error Rate</h3>
                <p className="text-2xl font-bold">
                  {(data.errorRate[data.errorRate.length - 1]?.rate * 100 || 0).toFixed(2)}%
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
