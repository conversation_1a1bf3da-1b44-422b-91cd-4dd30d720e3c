import { useState, useMemo, useCallback } from 'react'

/**
 * Sort Hook
 * Manages sorting state and provides sorting utilities
 */

// Helper function to check if value is a Date
function isDate(value: any): value is Date {
  return value instanceof Date || (typeof value === 'object' && value !== null && Object.prototype.toString.call(value) === '[object Date]')
}

export type SortDirection = 'asc' | 'desc'

export interface SortConfig<T> {
  key: keyof T
  direction: SortDirection
}

export interface UseSortOptions<T> {
  initialSort?: SortConfig<T>
  multiSort?: boolean
}

export interface UseSortResult<T> {
  sortConfig: SortConfig<T> | null
  sortConfigs: SortConfig<T>[]
  sortedItems: T[]
  sort: (key: keyof T, direction?: SortDirection) => void
  toggleSort: (key: keyof T) => void
  addSort: (key: keyof T, direction?: SortDirection) => void
  removeSort: (key: keyof T) => void
  clearSort: () => void
  getSortDirection: (key: keyof T) => SortDirection | null
  isSorted: (key: keyof T) => boolean
}

export function useSort<T extends Record<string, any>>(
  items: T[],
  options: UseSortOptions<T> = {}
): UseSortResult<T> {
  const { initialSort, multiSort = false } = options

  const [sortConfigs, setSortConfigs] = useState<SortConfig<T>[]>(
    initialSort ? [initialSort] : []
  )

  const sortConfig = sortConfigs.length > 0 ? sortConfigs[0] : null

  const sortedItems = useMemo(() => {
    if (sortConfigs.length === 0) {
      return items
    }

    return [...items].sort((a, b) => {
      for (const config of sortConfigs) {
        const { key, direction } = config
        const aValue = a[key]
        const bValue = b[key]

        let comparison = 0

        if (aValue === null || aValue === undefined) {
          comparison = bValue === null || bValue === undefined ? 0 : 1
        } else if (bValue === null || bValue === undefined) {
          comparison = -1
        } else if (typeof aValue === 'string' && typeof bValue === 'string') {
          comparison = aValue.localeCompare(bValue)
        } else if (typeof aValue === 'number' && typeof bValue === 'number') {
          comparison = aValue - bValue
        } else if (isDate(aValue) && isDate(bValue)) {
          comparison = aValue.getTime() - bValue.getTime()
        } else {
          comparison = String(aValue).localeCompare(String(bValue))
        }

        if (comparison !== 0) {
          return direction === 'desc' ? -comparison : comparison
        }
      }

      return 0
    })
  }, [items, sortConfigs])

  const sort = useCallback((key: keyof T, direction: SortDirection = 'asc') => {
    if (multiSort) {
      setSortConfigs(prev => {
        const existing = prev.find(config => config.key === key)
        if (existing) {
          return prev.map(config =>
            config.key === key ? { ...config, direction } : config
          )
        } else {
          return [...prev, { key, direction }]
        }
      })
    } else {
      setSortConfigs([{ key, direction }])
    }
  }, [multiSort])

  const toggleSort = useCallback((key: keyof T) => {
    const currentDirection = getSortDirection(key)
    if (currentDirection === null) {
      sort(key, 'asc')
    } else if (currentDirection === 'asc') {
      sort(key, 'desc')
    } else {
      if (multiSort) {
        removeSort(key)
      } else {
        clearSort()
      }
    }
  }, [sort, multiSort])

  const addSort = useCallback((key: keyof T, direction: SortDirection = 'asc') => {
    if (multiSort) {
      setSortConfigs(prev => {
        const existing = prev.find(config => config.key === key)
        if (existing) {
          return prev.map(config =>
            config.key === key ? { ...config, direction } : config
          )
        } else {
          return [...prev, { key, direction }]
        }
      })
    } else {
      setSortConfigs([{ key, direction }])
    }
  }, [multiSort])

  const removeSort = useCallback((key: keyof T) => {
    setSortConfigs(prev => prev.filter(config => config.key !== key))
  }, [])

  const clearSort = useCallback(() => {
    setSortConfigs([])
  }, [])

  const getSortDirection = useCallback((key: keyof T): SortDirection | null => {
    const config = sortConfigs.find(config => config.key === key)
    return config ? config.direction : null
  }, [sortConfigs])

  const isSorted = useCallback((key: keyof T): boolean => {
    return sortConfigs.some(config => config.key === key)
  }, [sortConfigs])

  return {
    sortConfig,
    sortConfigs,
    sortedItems,
    sort,
    toggleSort,
    addSort,
    removeSort,
    clearSort,
    getSortDirection,
    isSorted,
  }
}

/**
 * Simple sort hook for single column sorting
 */
export function useSimpleSort<T extends Record<string, any>>(
  items: T[],
  initialKey?: keyof T,
  initialDirection: SortDirection = 'asc'
) {
  const { sortedItems, sort, toggleSort, getSortDirection, clearSort } = useSort(
    items,
    {
      initialSort: initialKey ? { key: initialKey, direction: initialDirection } : undefined,
      multiSort: false
    }
  )

  return {
    sortedItems,
    sort,
    toggleSort,
    getSortDirection,
    clearSort,
  }
}
