/**
 * Validation utilities for Azure Functions
 * Provides common validation functions and schemas
 */

import * as Joi from 'joi';
import { HttpRequest } from '@azure/functions';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  value?: any;
}

/**
 * Common validation schemas
 */
export const schemas = {
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  uuid: Joi.string().uuid().required(),
  tenantId: Joi.string().uuid().required(),
  userId: Joi.string().uuid().required(),
  documentId: Joi.string().uuid().required(),
  workflowId: Joi.string().uuid().required(),
  templateId: Joi.string().uuid().required(),
  
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  }),

  dateRange: Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
  })
};

/**
 * Validate request body against schema
 */
export async function validateBody(request: HttpRequest, schema: Joi.Schema): Promise<ValidationResult> {
  try {
    const body = await request.json();
    const { error, value } = schema.validate(body, { abortEarly: false });
    
    if (error) {
      return {
        isValid: false,
        error: error.details.map(detail => detail.message).join(', ')
      };
    }

    return {
      isValid: true,
      value
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid JSON in request body'
    };
  }
}

/**
 * Validate query parameters against schema
 */
export function validateQuery(request: HttpRequest, schema: Joi.Schema): ValidationResult {
  try {
    const query = Object.fromEntries(request.query.entries());
    const { error, value } = schema.validate(query, { abortEarly: false });
    
    if (error) {
      return {
        isValid: false,
        error: error.details.map(detail => detail.message).join(', ')
      };
    }

    return {
      isValid: true,
      value
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid query parameters'
    };
  }
}

/**
 * Validate path parameters against schema
 */
export function validateParams(params: Record<string, string>, schema: Joi.Schema): ValidationResult {
  try {
    const { error, value } = schema.validate(params, { abortEarly: false });
    
    if (error) {
      return {
        isValid: false,
        error: error.details.map(detail => detail.message).join(', ')
      };
    }

    return {
      isValid: true,
      value
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid path parameters'
    };
  }
}

/**
 * Create validation middleware
 */
export function withValidation(
  bodySchema?: Joi.Schema,
  querySchema?: Joi.Schema,
  paramsSchema?: Joi.Schema
) {
  return function(handler: (request: HttpRequest, context: any, validated: any) => Promise<any>) {
    return async (request: HttpRequest, context: any) => {
      const validated: any = {};

      // Validate body if schema provided
      if (bodySchema) {
        const bodyResult = await validateBody(request, bodySchema);
        if (!bodyResult.isValid) {
          return {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
              error: 'Validation Error',
              message: bodyResult.error
            }
          };
        }
        validated.body = bodyResult.value;
      }

      // Validate query if schema provided
      if (querySchema) {
        const queryResult = validateQuery(request, querySchema);
        if (!queryResult.isValid) {
          return {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
              error: 'Validation Error',
              message: queryResult.error
            }
          };
        }
        validated.query = queryResult.value;
      }

      // Validate params if schema provided
      if (paramsSchema) {
        const params = request.params || {};
        const paramsResult = validateParams(params, paramsSchema);
        if (!paramsResult.isValid) {
          return {
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
              error: 'Validation Error',
              message: paramsResult.error
            }
          };
        }
        validated.params = paramsResult.value;
      }

      return await handler(request, context, validated);
    };
  };
}
