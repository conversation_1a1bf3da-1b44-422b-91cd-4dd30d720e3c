/**
 * AI Hooks Index
 * Re-exports all AI-related hooks
 */

// Core AI Hooks
export { useAI } from './useAI'

// AI Models Management
export * from './useAIModels'

// Datasets Management
export * from './useDatasets'

// Document Analysis
export { useDocumentAnalysis, useBatchAnalyze, useAnalyzeDocument } from './useDocumentAnalysis'

// Text Generation - export what actually exists
export {
  useAIReasoning,
  useContentTemplates,
  useGenerateContent,
  useGenerateContentVariations,
  useOptimizeContent,
  useSummarizeText,
  useTextGenerationHistory
} from './useTextGeneration'

// Image Analysis - export what actually exists
export {
  useAnalyzeImage,
  useBarcodeDetection,
  useImageAnalysisResult,
  useImageAnalysisResults,
  useImageOCR,
  useObjectDetection,
  useSignatureDetection
} from './useImageAnalysis'

// Chat Completion - export what actually exists
export {
  useChatSession,
  useChatSessions,
  useChatUsageStats,
  useCreateChatSession,
  useDeleteChatSession,
  useSendChatMessage,
  useStreamChatCompletion,
  useUpdateChatSession
} from './useChatCompletion'

// Embeddings - export what actually exists
export {
  useBatchIndexDocuments,
  useDeleteEmbeddings,
  useEmbeddingAnalytics,
  useEmbeddingHistory,
  useEmbeddingModels,
  useFindSimilarDocuments,
  useGenerateEmbeddings,
  useIndexDocument,
  useSimilaritySearch,
  useVectorIndexStatus
} from './useEmbeddings'

// RAG (Retrieval-Augmented Generation)
export * from './useRAG'

// AI Operations Management
export * from './useAIOperations'

// Smart Form Processing
export { useSmartFormProcessing } from './useSmartFormProcessing'

export type {
  AIModel,
  AIProvider,
  AIResponse,
  UseAIResult,
  DocumentAnalysisResult,
  TextGenerationOptions,
  ImageAnalysisResult,
  ChatMessage,
  EmbeddingResult,
  FormFieldDefinition,
  FormFieldType
} from './types'
