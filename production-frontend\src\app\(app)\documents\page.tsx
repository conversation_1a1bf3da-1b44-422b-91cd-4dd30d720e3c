"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Plus, Search } from "lucide-react";
import { DocumentCard } from "@/components/documents/document-card";
import { EmptyState } from "@/components/empty-state";
import { Document } from "@/types/backend";
import { DocumentStatus } from "@/types/document";
import { useToast } from "@/hooks/use-toast";
import { useOrganizations } from "@/hooks/organizations/useOrganizations";
import { useProjects } from "@/hooks/projects/useProjects";
import { useDocuments } from "@/hooks/documents/useDocuments";

export default function DocumentsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<DocumentStatus | "ALL">("ALL");
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const { toast } = useToast();

  const { organizations, currentOrganization } = useOrganizations();

  const {
    projects,
    loading: isLoadingProjects
  } = useProjects({
    organizationId: currentOrganization?.id,
  });

  const {
    documents: documentsResponse,
    loading: isLoadingDocuments,
    refresh: refetch
  } = useDocuments({
    organizationId: currentOrganization?.id,
    projectId: selectedProject || '',
  });

  const documents = documentsResponse || [];

  // Refetch documents when organization or project changes
  useEffect(() => {
    if (currentOrganization?.id) {
      refetch();
    }
  }, [currentOrganization?.id, selectedProject, activeTab, refetch]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Documents</h1>
          <p className="text-muted-foreground">
            {selectedProject
              ? `Documents in ${projects.find((p: any) => p.id === selectedProject)?.name || "selected project"}`
              : currentOrganization
                ? `All documents in ${currentOrganization.name}`
                : "Select an organization to view documents"}
          </p>
        </div>
        <Button
          asChild
          disabled={!currentOrganization}
        >
          <Link href={currentOrganization ? `/documents/upload${selectedProject ? `?projectId=${selectedProject}&organizationId=${currentOrganization.id}` : `?organizationId=${currentOrganization.id}`}` : "#"}>
            <Plus className="mr-2 h-4 w-4" />
            Upload Document
          </Link>
        </Button>
      </div>

      {!currentOrganization ? (
        <EmptyState
          icon={<FileText className="h-10 w-10 text-muted-foreground" />}
          title="No organization selected"
          description="Select an organization from the dropdown to view its documents"
          action={
            <Button asChild>
              <Link href="/organizations">
                View Organizations
              </Link>
            </Button>
          }
        />
      ) : (
        <>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search documents..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    // Trigger search on Enter
                    refetch();

                    // Show toast for better UX
                    if (searchQuery) {
                      toast({
                        title: "Searching documents",
                        description: `Searching for "${searchQuery}"`,
                      });
                    }
                  }
                }}
              />
            </div>
            <div className="flex gap-2">
              <Select
                value={selectedProject || ""}
                onValueChange={(value) => setSelectedProject(value || null)}
              >
                <SelectTrigger className="w-full md:w-[200px]">
                  <SelectValue placeholder="All Projects" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Projects</SelectItem>
                  {projects.map((project: any) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  refetch();
                  toast({
                    title: "Refreshing documents",
                    description: "Fetching the latest documents",
                  });
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-refresh-cw"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
              </Button>
            </div>
          </div>

          <Tabs
            defaultValue="ALL"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as DocumentStatus | "ALL")}
          >
            <TabsList className="grid grid-cols-5 w-full md:w-auto">
              <TabsTrigger value="ALL">All</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="processing">Processing</TabsTrigger>
              <TabsTrigger value="processed">Processed</TabsTrigger>
              <TabsTrigger value="failed">Failed</TabsTrigger>
            </TabsList>
          </Tabs>

          {isLoadingDocuments || isLoadingProjects ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-40 w-full bg-muted animate-pulse rounded-lg" />
              ))}
            </div>
          ) : documents.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {documents.map((document: Document) => (
                <DocumentCard
                  key={document.id}
                  document={document as any}
                  onDownload={(doc: any) => {
                    // Handle download
                    window.open(doc.url || '#', '_blank');
                  }}
                />
              ))}
            </div>
          ) : (
            <EmptyState
              icon={<FileText className="h-10 w-10 text-muted-foreground" />}
              title={searchQuery ? "No documents found" : "No documents"}
              description={
                searchQuery
                  ? `No documents match "${searchQuery}"`
                  : selectedProject
                    ? `This project doesn't have any documents yet. Upload your first document to get started.`
                    : `You don't have any documents in ${currentOrganization.name} yet. Upload your first document to get started.`
              }
              action={
                <Button asChild>
                  <Link href={`/documents/upload${selectedProject ? `?projectId=${selectedProject}&organizationId=${currentOrganization.id}` : `?organizationId=${currentOrganization.id}`}`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Upload Document
                  </Link>
                </Button>
              }
            />
          )}
        </>
      )}
    </div>
  );
}
