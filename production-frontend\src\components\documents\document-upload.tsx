"use client";

import { useState, useCallback, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import { v4 as uuidv4 } from "uuid";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Upload, File, X, Plus, Trash, CheckCircle, AlertCircle, Loader2 } from "lucide-react";

import { DocumentType } from "@/types/document";
import serviceBusService, { MessageStatus } from "@/services/service-bus-service";
import { useEventSubscription } from "@/hooks/infrastructure";
import { EventType } from "@/services/event-grid-service";
import {
  useUploadDocument,
  useProcessDocument
} from "@/stores/document-store";

interface UploadedFile {
  id: string;
  file: File;
  progress: number;
  error?: string;
  documentId?: string;
  messageId?: string;
  processingStatus?: MessageStatus;
  processingResult?: Record<string, any>;
}

interface DocumentUploadProps {
  projectId?: string;
  organizationId?: string;
  onUploadComplete?: (documentIds: string[]) => void;
  maxFileSize?: number;
  allowedFileTypes?: string[];
}

export function DocumentUpload({
  projectId,
  organizationId,
  onUploadComplete,
  maxFileSize = 20 * 1024 * 1024, // 20MB default
  allowedFileTypes = ['.pdf', '.docx', '.xlsx', '.pptx', '.jpg', '.jpeg', '.png'],
}: DocumentUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [documentType, setDocumentType] = useState<DocumentType>('general' as DocumentType);
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);

  const [processingOptions, setProcessingOptions] = useState({
    extractText: true,
    extractEntities: true,
    classify: true
  });
  const { toast } = useToast();

  // Store hooks
  const uploadDocument = useUploadDocument();
  const processDocument = useProcessDocument();

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    if (rejectedFiles.length > 0) {
      rejectedFiles.forEach(({ file, errors }) => {
        const errorMessages = errors.map((error: { message: string }) => error.message).join(", ");
        toast({
          title: "File rejected",
          description: `${file.name}: ${errorMessages}`,
          variant: "destructive",
        });
      });
    }

    // Add accepted files to state
    const newFiles = acceptedFiles.map(file => ({
      id: uuidv4(),
      file,
      progress: 0
    }));

    setFiles(prev => [...prev, ...newFiles]);
  }, [toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png']
    },
    maxSize: maxFileSize,
    multiple: true
  });

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(file => file.id !== id));
  };

  const addTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags(prev => [...prev, tagInput.trim()]);
      setTagInput("");
    }
  };

  const removeTag = (tag: string) => {
    setTags(prev => prev.filter(t => t !== tag));
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  // Subscribe to document processing events
  useEventSubscription([EventType.DOCUMENT_PROCESSED, EventType.DOCUMENT_PROCESSING_FAILED], (event) => {
    // Update file status based on event
    setFiles(prev =>
      prev.map(file => {
        if (file.documentId === event.data.documentId) {
          if (event.type === EventType.DOCUMENT_PROCESSED) {
            return {
              ...file,
              processingStatus: MessageStatus.COMPLETED,
              processingResult: event.data.result
            };
          } else if (event.type === EventType.DOCUMENT_PROCESSING_FAILED) {
            return {
              ...file,
              processingStatus: MessageStatus.FAILED,
              error: event.data.error || "Processing failed"
            };
          }
        }
        return file;
      })
    );

    // Show toast notification
    if (event.type === EventType.DOCUMENT_PROCESSED) {
      toast({
        title: "Document Processed",
        description: `Document "${event.data.documentName}" was processed successfully`,
      });
    } else if (event.type === EventType.DOCUMENT_PROCESSING_FAILED) {
      toast({
        title: "Processing Failed",
        description: `Processing failed for document "${event.data.documentName}": ${event.data.error || "Unknown error"}`,
        variant: "destructive",
      });
    }
  });

  // Check processing status periodically
  useEffect(() => {
    const checkProcessingStatus = async () => {
      const filesToCheck = files.filter(file =>
        file.messageId &&
        (file.processingStatus === MessageStatus.QUEUED || file.processingStatus === MessageStatus.PROCESSING)
      );

      for (const file of filesToCheck) {
        if (file.messageId) {
          try {
            const status = await serviceBusService.getMessageStatus(file.messageId);

            setFiles(prev =>
              prev.map(f => {
                if (f.id === file.id) {
                  return {
                    ...f,
                    processingStatus: status.status,
                    processingResult: (status as any).result
                  };
                }
                return f;
              })
            );
          } catch (error) {
            console.error("Failed to check processing status:", error);
          }
        }
      }
    };

    const interval = setInterval(checkProcessingStatus, 5000);
    return () => clearInterval(interval);
  }, [files]);



  const handleUpload = async () => {
    if (files.length === 0) {
      toast({
        title: "No files selected",
        description: "Please select at least one file to upload",
        variant: "destructive",
      });
      return;
    }

    if (!projectId || !organizationId) {
      toast({
        title: "Missing information",
        description: "Project and organization information is required",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    const uploadedDocumentIds: string[] = [];

    try {
      // Upload each file using the store
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
          // Upload using the document store
          const result = await uploadDocument(file.file, {
            projectId,
            tags
          });

          // Add to uploaded document IDs
          if (result.id) {
            uploadedDocumentIds.push(result.id);

            // Update file with document ID
            setFiles(prev =>
              prev.map(f => f.id === file.id ? {
                ...f,
                progress: 100,
                documentId: result.id
              } : f)
            );

            // Start processing the document using the hook
            await processDocument(result.id, {
              analysisType: 'ANALYSIS',
              extractTables: true,
              extractKeyValuePairs: true,
              extractEntities: processingOptions.extractEntities,
              forceReprocess: false
            });
          }
        } catch (error) {
          // Handle error
          setFiles(prev =>
            prev.map(f => f.id === file.id ? { ...f, error: "Upload failed" } : f)
          );

          toast({
            title: "Upload failed",
            description: `Failed to upload ${file.file.name}`,
            variant: "destructive",
          });
        }
      }

      if (uploadedDocumentIds.length > 0) {
        toast({
          title: "Upload complete",
          description: `Successfully uploaded ${uploadedDocumentIds.length} document(s)`,
        });

        if (onUploadComplete) {
          onUploadComplete(uploadedDocumentIds);
        }

        // Reset state
        setFiles([]);
        setTags([]);
      }
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "There was an error uploading your documents",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Upload Documents</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Dropzone */}
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/20'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
          <p className="mt-2 text-sm text-muted-foreground">
            Drag & drop documents here, or click to select files
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Supports {allowedFileTypes.join(', ')} (max {maxFileSize / (1024 * 1024)}MB)
          </p>
        </div>

        {/* Document Type */}
        <div className="space-y-2">
          <Label htmlFor="document-type">Document Type</Label>
          <Select
            value={documentType}
            onValueChange={(value) => setDocumentType(value as DocumentType)}
          >
            <SelectTrigger id="document-type">
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={DocumentType.GENERAL}>General</SelectItem>
              <SelectItem value={DocumentType.INVOICE}>Invoice</SelectItem>
              <SelectItem value={DocumentType.CONTRACT}>Contract</SelectItem>
              <SelectItem value={DocumentType.REPORT}>Report</SelectItem>
              <SelectItem value={DocumentType.FORM}>Form</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Processing Options */}
        <div className="space-y-2">
          <Label>Processing Options</Label>
          <div className="space-y-2 border rounded-md p-3">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="extractText"
                checked={processingOptions.extractText}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, extractText: e.target.checked }))}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="extractText" className="text-sm">Extract Text</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="extractEntities"
                checked={processingOptions.extractEntities}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, extractEntities: e.target.checked }))}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="extractEntities" className="text-sm">Extract Entities</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="classify"
                checked={processingOptions.classify}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, classify: e.target.checked }))}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="classify" className="text-sm">Classify Document</Label>
            </div>
          </div>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label htmlFor="tags">Tags</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {tags.map(tag => (
              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                {tag}
                <button
                  type="button"
                  onClick={() => removeTag(tag)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X size={14} />
                </button>
              </Badge>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              id="tags"
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyDown={handleTagInputKeyDown}
              placeholder="Add tags..."
              className="flex-1"
            />
            <Button type="button" size="icon" onClick={addTag}>
              <Plus size={16} />
            </Button>
          </div>
        </div>

        {/* File List */}
        {files.length > 0 && (
          <div className="space-y-2">
            <Label>Selected Files</Label>
            <div className="space-y-2">
              {files.map((file) => (
                <div key={file.id} className="flex flex-col p-2 border rounded">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center flex-1 min-w-0">
                      <File className="h-4 w-4 mr-2 flex-shrink-0" />
                      <span className="text-sm truncate">{file.file.name}</span>
                    </div>

                    {file.progress > 0 && file.progress < 100 ? (
                      <div className="w-24 ml-2">
                        <Progress value={file.progress} className="h-2" />
                      </div>
                    ) : file.error ? (
                      <Badge variant="destructive" className="ml-2">Failed</Badge>
                    ) : file.progress === 100 && !file.processingStatus ? (
                      <Badge variant="success" className="ml-2">Uploaded</Badge>
                    ) : file.processingStatus === MessageStatus.QUEUED ? (
                      <Badge variant="secondary" className="ml-2 flex items-center gap-1">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Queued
                      </Badge>
                    ) : file.processingStatus === MessageStatus.PROCESSING ? (
                      <Badge variant="secondary" className="ml-2 flex items-center gap-1">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        Processing
                      </Badge>
                    ) : file.processingStatus === MessageStatus.COMPLETED ? (
                      <Badge variant="success" className="ml-2 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Processed
                      </Badge>
                    ) : file.processingStatus === MessageStatus.FAILED ? (
                      <Badge variant="destructive" className="ml-2 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        Failed
                      </Badge>
                    ) : (
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeFile(file.id)}
                        disabled={isUploading}
                        className="ml-2"
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {file.processingStatus === MessageStatus.COMPLETED && file.processingResult && (
                    <div className="mt-2 text-xs bg-muted p-2 rounded">
                      <div className="font-medium mb-1">Processing Results:</div>
                      <div className="space-y-1">
                        {file.processingResult.entities && (
                          <div>
                            <span className="font-medium">Entities:</span> {file.processingResult.entities.length} found
                          </div>
                        )}
                        {file.processingResult.classification && (
                          <div>
                            <span className="font-medium">Classification:</span> {file.processingResult.classification}
                          </div>
                        )}
                        {file.processingResult.confidence && (
                          <div>
                            <span className="font-medium">Confidence:</span> {(file.processingResult.confidence * 100).toFixed(1)}%
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {file.processingStatus === MessageStatus.FAILED && file.error && (
                    <div className="mt-2 text-xs text-destructive bg-destructive/10 p-2 rounded">
                      Error: {file.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-col gap-2">
        <Button
          onClick={handleUpload}
          disabled={files.length === 0 || isUploading}
          className="w-full"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            "Upload & Process Documents"
          )}
        </Button>
        <p className="text-xs text-muted-foreground text-center">
          Documents will be automatically processed after upload
        </p>
      </CardFooter>
    </Card>
  );
}
