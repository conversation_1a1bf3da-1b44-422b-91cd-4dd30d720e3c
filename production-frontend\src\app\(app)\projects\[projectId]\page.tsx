'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { ProjectDetails } from '@/components/projects/project-details';
import { ProjectMembers } from '@/components/projects/project-members';
import { ProjectDocuments } from '@/components/projects/project-documents';
import { useProjects } from '@/hooks/projects/useProjects';

export default function ProjectPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;
  const [activeTab, setActiveTab] = useState('overview');

  const { projects, loading: isLoading } = useProjects({ organizationId: undefined });
  const project = projects?.find((p: any) => p.id === projectId);
  const error = null;

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load project details',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading project details...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">{project?.name || 'Project Details'}</h1>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push('/projects')}
          >
            Back to Projects
          </Button>
          <Button
            onClick={() => router.push(`/projects/${projectId}/documents/upload`)}
          >
            Upload Document
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Project Overview</CardTitle>
              <CardDescription>View and manage project details</CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectDetails projectId={projectId} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents">
          <Card>
            <CardHeader>
              <CardTitle>Project Documents</CardTitle>
              <CardDescription>Manage documents in this project</CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectDocuments projectId={projectId} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members">
          <Card>
            <CardHeader>
              <CardTitle>Project Members</CardTitle>
              <CardDescription>Manage team members and their access</CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectMembers projectId={projectId} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
