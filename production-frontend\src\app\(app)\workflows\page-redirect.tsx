'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, GitBranch } from 'lucide-react';
import { useProjects } from '@/hooks/projects/useProjects';

export default function WorkflowsRedirectPage() {
  const router = useRouter();
  const { projects, isLoading } = useProjects({});

  // Redirect to the first project's workflows if available
  useEffect(() => {
    if (!isLoading && projects.length > 0) {
      router.push(`/projects/${projects[0].id}/workflows`);
    }
  }, [isLoading, projects, router]);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Workflows</h1>
        <p className="text-muted-foreground">
          Workflows are now organized within projects
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Workflows Have Moved</CardTitle>
          <CardDescription>
            Workflows are now organized within projects for better document management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center py-6">
            <GitBranch className="h-16 w-16 text-muted-foreground" />
          </div>
          
          {isLoading ? (
            <p className="text-center">Loading your projects...</p>
          ) : projects.length > 0 ? (
            <div className="space-y-4">
              <p className="text-center">Select a project to view its workflows:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {projects.slice(0, 4).map((project) => (
                  <Button 
                    key={project.id} 
                    variant="outline" 
                    className="justify-between"
                    onClick={() => router.push(`/projects/${project.id}/workflows`)}
                  >
                    <span>{project.name}</span>
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                ))}
              </div>
              {projects.length > 4 && (
                <Button 
                  variant="link" 
                  className="w-full"
                  onClick={() => router.push('/projects')}
                >
                  View all projects
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-center">You need to create a project first to manage workflows</p>
              <Button 
                className="w-full"
                onClick={() => router.push('/projects/create')}
              >
                Create Your First Project
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
