/**
 * Token Extraction API
 * Extracts JWT access token from HTTP-only cookies for frontend use
 */

import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    
    // Get the access token from HTTP-only cookie
    const accessTokenCookie = cookieStore.get('msal_access_token')
    const sessionCookie = cookieStore.get('msal_session')
    const userInfoCookie = cookieStore.get('msal_user_info')
    
    if (!accessTokenCookie || !sessionCookie) {
      return NextResponse.json(
        { error: 'No authentication session found' },
        { status: 401 }
      )
    }
    
    // Verify session is still valid
    try {
      const sessionData = JSON.parse(sessionCookie.value)
      if (!sessionData.isAuthenticated || (sessionData.expiresOn && Date.now() > sessionData.expiresOn)) {
        return NextResponse.json(
          { error: 'Session expired' },
          { status: 401 }
        )
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid session data' },
        { status: 401 }
      )
    }
    
    // Parse user info if available
    let userInfo = null
    if (userInfoCookie) {
      try {
        userInfo = JSON.parse(userInfoCookie.value)
      } catch (error) {
        console.warn('[Token API] Failed to parse user info cookie:', error)
      }
    }
    
    console.log('[Token API] Successfully extracted token for user:', userInfo?.username)
    
    // Return the access token and user info
    return NextResponse.json({
      success: true,
      accessToken: accessTokenCookie.value,
      userInfo: userInfo,
      expiresOn: JSON.parse(sessionCookie.value).expiresOn
    })
    
  } catch (error) {
    console.error('[Token API] Token extraction failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Token extraction failed',
      },
      { status: 500 }
    )
  }
}
