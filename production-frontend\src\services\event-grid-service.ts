/**
 * Event Grid Service
 * Handles Event Grid operations and monitoring
 */

import { backendApiClient } from './backend-api-client'

export interface EventGridConfig {
  endpoint: string
  accessKey: string
  retryAttempts?: number
  timeoutMs?: number
  batchSize?: number
  throttleMs?: number
}

export interface CustomEventData {
  eventType: string
  subject: string
  data: any
  dataVersion?: string
  eventTime?: Date
  id?: string
  topic?: string
  filters?: EventFilter[]
}

export interface EventSubscription {
  name: string
  eventTypes: string[]
  endpoint: string
  filters?: EventFilter[]
}

export interface EventFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn'
  value: string | string[]
}

export interface EventSchema {
  eventType: string
  version: string
  schema: any
  required: string[]
}

export interface EventMetrics {
  totalEvents: number
  successfulEvents: number
  failedEvents: number
  averageProcessingTime: number
  lastEventTime?: Date
  eventsFiltered: number
  eventsDeadLettered: number
  eventsBatched: number
  throttleCount: number
}

export interface RetryPolicy {
  maxRetries: number
  retryDelay: number
  exponentialBackoff: boolean
  maxRetryDelay: number
}

export interface EventHealthStatus {
  isHealthy: boolean
  eventGridConnected: boolean
  serviceBusConnected: boolean
  processingErrors: number
  lastHealthCheck: string
  metrics: EventMetrics[]
  detailedMetrics?: EventMetrics[]
}

export interface MetricsSummary {
  totalEvents: number
  totalSuccesses: number
  totalFailures: number
  successRate: number
  failureRate: number
  averageProcessingTime: number
  eventTypes: number
}

class EventGridService {
  private subscriptions = new Map<string, ((event: EventData) => void)[]>()

  /**
   * Subscribe to events
   */
  subscribe(eventTypes: EventType[] | EventType, callback: (event: EventData) => void): (() => void) | undefined {
    const types = Array.isArray(eventTypes) ? eventTypes : [eventTypes]

    types.forEach(eventType => {
      if (!this.subscriptions.has(eventType)) {
        this.subscriptions.set(eventType, [])
      }
      this.subscriptions.get(eventType)!.push(callback)
    })

    // Return unsubscribe function
    return () => {
      types.forEach(eventType => {
        const callbacks = this.subscriptions.get(eventType)
        if (callbacks) {
          const index = callbacks.indexOf(callback)
          if (index > -1) {
            callbacks.splice(index, 1)
          }
        }
      })
    }
  }

  /**
   * Emit event to subscribers
   */
  private emitEvent(event: EventData): void {
    const callbacks = this.subscriptions.get(event.type)
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(event)
        } catch (error) {
          console.error('Error in event callback:', error)
        }
      })
    }
  }
  /**
   * Publish event to Event Grid
   */
  async publishEvent(eventData: CustomEventData): Promise<{ success: boolean; eventId: string }> {
    return await backendApiClient.publishEvent(eventData)
  }

  /**
   * Publish multiple events in batch
   */
  async publishEvents(events: CustomEventData[]): Promise<{ 
    success: boolean
    results: Array<{ eventId: string; success: boolean; error?: string }>
  }> {
    return await backendApiClient.publishEvents(events)
  }

  /**
   * Get Event Grid health status
   */
  async getHealthStatus(): Promise<EventHealthStatus> {
    return await backendApiClient.getEventGridHealth()
  }

  /**
   * Get event metrics
   */
  async getEventMetrics(timeRange?: string): Promise<EventMetrics> {
    return await backendApiClient.getEventMetrics(timeRange)
  }

  /**
   * Get metrics summary
   */
  async getMetricsSummary(timeRange?: string): Promise<MetricsSummary> {
    return await backendApiClient.getEventMetricsSummary(timeRange)
  }

  /**
   * Get event subscriptions
   */
  async getSubscriptions(): Promise<EventSubscription[]> {
    return await backendApiClient.getEventSubscriptions()
  }

  /**
   * Create event subscription
   */
  async createSubscription(subscription: Omit<EventSubscription, 'id'>): Promise<EventSubscription> {
    return await backendApiClient.createEventSubscription(subscription)
  }

  /**
   * Update event subscription
   */
  async updateSubscription(id: string, subscription: Partial<EventSubscription>): Promise<EventSubscription> {
    return await backendApiClient.updateEventSubscription(id, subscription)
  }

  /**
   * Delete event subscription
   */
  async deleteSubscription(id: string): Promise<void> {
    return await backendApiClient.deleteEventSubscription(id)
  }

  /**
   * Test event subscription
   */
  async testSubscription(id: string): Promise<{ success: boolean; message: string }> {
    return await backendApiClient.testEventSubscription(id)
  }

  /**
   * Get event schemas
   */
  async getEventSchemas(): Promise<EventSchema[]> {
    return await backendApiClient.getEventSchemas()
  }

  /**
   * Register event schema
   */
  async registerEventSchema(schema: EventSchema): Promise<EventSchema> {
    return await backendApiClient.registerEventSchema(schema)
  }

  /**
   * Validate event against schema
   */
  async validateEvent(eventType: string, eventData: any): Promise<{
    valid: boolean
    errors: string[]
  }> {
    return await backendApiClient.validateEvent(eventType, eventData)
  }

  /**
   * Get event history
   */
  async getEventHistory(params?: {
    eventType?: string
    subject?: string
    startTime?: string
    endTime?: string
    page?: number
    pageSize?: number
  }): Promise<{
    events: Array<{
      id: string
      eventType: string
      subject: string
      eventTime: string
      data: any
      status: string
    }>
    pagination: {
      page: number
      pageSize: number
      totalCount: number
      totalPages: number
    }
  }> {
    return await backendApiClient.getEventHistory(params)
  }

  /**
   * Replay failed events
   */
  async replayFailedEvents(eventIds: string[]): Promise<{
    success: boolean
    results: Array<{ eventId: string; success: boolean; error?: string }>
  }> {
    return await backendApiClient.replayFailedEvents(eventIds)
  }

  /**
   * Get dead letter events
   */
  async getDeadLetterEvents(params?: {
    page?: number
    pageSize?: number
  }): Promise<{
    events: Array<{
      id: string
      originalEventType: string
      failureReason: string
      failureTime: string
      retryCount: number
      data: any
    }>
    pagination: {
      page: number
      pageSize: number
      totalCount: number
      totalPages: number
    }
  }> {
    return await backendApiClient.getDeadLetterEvents(params)
  }

  /**
   * Configure Event Grid settings
   */
  async configureEventGrid(config: EventGridConfig): Promise<{ success: boolean; message: string }> {
    return await backendApiClient.configureEventGrid(config)
  }

  /**
   * Get Event Grid configuration
   */
  async getEventGridConfig(): Promise<EventGridConfig> {
    return await backendApiClient.getEventGridConfig()
  }
}

// Export singleton instance
export const eventGridService = new EventGridService()

// Export event types and data interfaces
export const EventType = {
  DOCUMENT_UPLOADED: 'DOCUMENT_UPLOADED',
  DOCUMENT_PROCESSED: 'DOCUMENT_PROCESSED',
  DOCUMENT_PROCESSING_FAILED: 'DOCUMENT_PROCESSING_FAILED',
  DOCUMENT_UPDATED: 'DOCUMENT_UPDATED',
  DOCUMENT_DELETED: 'DOCUMENT_DELETED',
  DOCUMENT_SHARED: 'DOCUMENT_SHARED',
  DOCUMENT_COMMENTED: 'DOCUMENT_COMMENTED',
  DOCUMENT_VERSION_CREATED: 'DOCUMENT_VERSION_CREATED',
  PROJECT_CREATED: 'PROJECT_CREATED',
  PROJECT_UPDATED: 'PROJECT_UPDATED',
  PROJECT_DELETED: 'PROJECT_DELETED',
  PROJECT_MEMBER_ADDED: 'PROJECT_MEMBER_ADDED',
  PROJECT_MEMBER_REMOVED: 'PROJECT_MEMBER_REMOVED',
  PROJECT_MEMBER_ROLE_UPDATED: 'PROJECT_MEMBER_ROLE_UPDATED',
  ORGANIZATION_CREATED: 'ORGANIZATION_CREATED',
  ORGANIZATION_UPDATED: 'ORGANIZATION_UPDATED',
  ORGANIZATION_DELETED: 'ORGANIZATION_DELETED',
  ORGANIZATION_MEMBER_ADDED: 'ORGANIZATION_MEMBER_ADDED',
  ORGANIZATION_MEMBER_REMOVED: 'ORGANIZATION_MEMBER_REMOVED',
  ORGANIZATION_MEMBER_ROLE_UPDATED: 'ORGANIZATION_MEMBER_ROLE_UPDATED',
  USER_REGISTERED: 'USER_REGISTERED',
  USER_LOGGED_IN: 'USER_LOGGED_IN',
  USER_LOGGED_OUT: 'USER_LOGGED_OUT',
  USER_PROFILE_UPDATED: 'USER_PROFILE_UPDATED',
  COMMENT_CREATED: 'COMMENT_CREATED',
  COMMENT_UPDATED: 'COMMENT_UPDATED',
  COMMENT_DELETED: 'COMMENT_DELETED',
  COMMENT_RESOLVED: 'COMMENT_RESOLVED',
  COMMENT_REOPENED: 'COMMENT_REOPENED',
  WORKFLOW_CREATED: 'WORKFLOW_CREATED',
  WORKFLOW_UPDATED: 'WORKFLOW_UPDATED',
  WORKFLOW_DELETED: 'WORKFLOW_DELETED',
  WORKFLOW_EXECUTED: 'WORKFLOW_EXECUTED',
  WORKFLOW_COMPLETED: 'WORKFLOW_COMPLETED',
  WORKFLOW_FAILED: 'WORKFLOW_FAILED'
} as const

export type EventType = typeof EventType[keyof typeof EventType]

export interface EventData {
  id: string
  type: EventType
  timestamp: string
  data: any
}
