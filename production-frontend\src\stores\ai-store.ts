/**
 * AI Operations Store - Production Ready
 * Manages AI operations, models, and real-time processing status
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '../services/backend-api-client'
import type {
  AIOperation,
  AIModel
} from '../types/backend'

// Define the types that match the backend implementation
export type AIOperationType =
  | 'DOCUMENT_ANALYSIS'
  | 'FORM_PROCESSING'
  | 'SMART_FORM_PROCESSING'
  | 'BATCH_PROCESSING'
  | 'MODEL_TRAINING'
  | 'MODEL_DEPLOYMENT'
  | 'CONTENT_GENERATION'
  | 'CLASSIFICATION'
  | 'EXTRACTION'
  | 'ORCHESTRATION'
  | 'TEXT_GENERATION'
  | 'IMAGE_ANALYSIS'
  | 'CHAT_COMPLETION'
  | 'EMBEDDING_GENERATION'
  | 'RAG_QUERY'
  | 'RAG_SESSION'
  | 'KNOWLEDGE_BASE'
  | 'KNOWLEDGE_BASE_UPDATE'
  | 'RAG_SUGGESTIONS'
  | 'RAG_EXPORT'
  | 'DATASET'
  | 'DATASET_CREATE'
  | 'DATASET_PREVIEW'

export type AIOperationStatus =
  | 'PENDING'
  | 'QUEUED'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'PAUSED'

export interface AIOperationResults {
  success: boolean
  data?: any
  content?: string
  answer?: string
  sources?: Array<{
    title: string
    excerpt: string
    relevance: number
  }>
  reasoning?: string
  confidence?: number
  tokensUsed?: number
  metrics?: {
    processingTime: number
    tokensUsed?: number
    confidence?: number
    accuracy?: number
  }
  outputs?: Array<{
    type: string
    content: any
    metadata?: Record<string, any>
  }>
  errors?: string[]
  warnings?: string[]
}

export interface AIState {
  // Operations state
  operations: AIOperation[]
  currentOperation: AIOperation | null
  operationHistory: AIOperation[]
  
  // Models state
  models: AIModel[]
  activeModel: AIModel | null
  
  // Processing state
  isProcessing: boolean
  processingQueue: string[]
  completedOperations: string[]
  failedOperations: string[]
  
  // Real-time updates
  realTimeUpdates: boolean
  lastUpdate: string | null
  
  // UI state
  loading: boolean
  error: string | null
  
  // Cache state
  operationsCache: Map<string, AIOperation>
  modelsCache: Map<string, AIModel>
  cacheExpiry: number
}

export interface AIActions {
  // Operation management
  startOperation: (params: {
    type: AIOperationType
    parameters: Record<string, any>
    organizationId: string
    projectId?: string
    documentId?: string
    priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  }) => Promise<AIOperation>
  
  getOperation: (operationId: string) => Promise<AIOperation>
  listOperations: (filters?: {
    status?: AIOperationStatus
    type?: AIOperationType
    organizationId?: string
    projectId?: string
    page?: number
    limit?: number
  }) => Promise<{ operations: AIOperation[], total: number }>
  
  cancelOperation: (operationId: string) => Promise<void>
  retryOperation: (operationId: string) => Promise<AIOperation>
  
  // Model management
  createModel: (params: {
    name: string
    type: string
    configuration: Record<string, any>
    organizationId: string
  }) => Promise<AIModel>
  
  trainModel: (modelId: string, trainingData: any) => Promise<AIOperation>
  deployModel: (modelId: string) => Promise<void>
  listModels: (organizationId: string) => Promise<AIModel[]>
  
  // Document analysis
  analyzeDocument: (params: {
    documentId: string
    analysisType: string
    organizationId: string
    options?: Record<string, any>
  }) => Promise<AIOperation>
  
  // Batch processing
  processBatch: (params: {
    documentIds: string[]
    operationType: AIOperationType
    organizationId: string
    batchName?: string
  }) => Promise<AIOperation>
  
  // Real-time updates
  enableRealTimeUpdates: () => void
  disableRealTimeUpdates: () => void
  updateOperationStatus: (operationId: string, status: AIOperationStatus, results?: AIOperationResults) => void
  
  // Cache management
  clearCache: () => void
  refreshCache: () => Promise<void>
  
  // Utility methods
  getOperationProgress: (operationId: string) => number
  getEstimatedCompletion: (operationId: string) => Date | null
  isOperationComplete: (operationId: string) => boolean
  
  // State management
  setCurrentOperation: (operation: AIOperation | null) => void
  clearError: () => void
  reset: () => void
}

export type AIStore = AIState & AIActions

export const useAIStore = create<AIStore>()(
  persist(
    (set, get) => ({
      // Initial state
      operations: [],
      currentOperation: null,
      operationHistory: [],
      models: [],
      activeModel: null,
      isProcessing: false,
      processingQueue: [],
      completedOperations: [],
      failedOperations: [],
      realTimeUpdates: false,
      lastUpdate: null,
      loading: false,
      error: null,
      operationsCache: new Map(),
      modelsCache: new Map(),
      cacheExpiry: Date.now() + 5 * 60 * 1000, // 5 minutes

      // Operation management
      startOperation: async (params) => {
        set({ loading: true, error: null })

        try {
          const operation = await backendApiClient.startAIOperation(params)
          
          const { operations, processingQueue } = get()
          
          set({
            operations: [operation, ...operations],
            currentOperation: operation,
            processingQueue: [...processingQueue, operation.id],
            loading: false,
            lastUpdate: new Date().toISOString(),
          })

          return operation
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to start AI operation',
          })
          throw error
        }
      },

      getOperation: async (operationId) => {
        const { operationsCache, cacheExpiry } = get()
        
        // Check cache first
        if (Date.now() < cacheExpiry && operationsCache.has(operationId)) {
          return operationsCache.get(operationId)!
        }

        set({ loading: true, error: null })

        try {
          const operation = await backendApiClient.getAIOperation(operationId)
          
          // Update cache
          operationsCache.set(operationId, operation)
          
          // Update operations list
          const { operations } = get()
          const updatedOperations = operations.map(op => 
            op.id === operationId ? operation : op
          )
          
          set({
            operations: updatedOperations,
            operationsCache,
            loading: false,
            lastUpdate: new Date().toISOString(),
          })

          return operation
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to get AI operation',
          })
          throw error
        }
      },

      listOperations: async (filters = {}) => {
        set({ loading: true, error: null })

        try {
          const result = await backendApiClient.listAIOperations(filters)

          // Handle both possible response formats
          const operations = Array.isArray(result) ? result : (result.data || [])
          const total = Array.isArray(result) ? result.length : operations.length

          set({
            operations,
            operationHistory: [...get().operationHistory, ...operations],
            loading: false,
            lastUpdate: new Date().toISOString(),
          })

          return { operations, total }
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to list AI operations',
          })
          throw error
        }
      },

      cancelOperation: async (operationId) => {
        set({ loading: true, error: null })

        try {
          await backendApiClient.cancelAIOperation(operationId)
          
          const { operations, processingQueue } = get()

          set({
            operations: operations.map(op =>
              op.id === operationId
                ? { ...op, status: 'failed' as const }
                : op
            ),
            processingQueue: processingQueue.filter(id => id !== operationId),
            loading: false,
            lastUpdate: new Date().toISOString(),
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to cancel AI operation',
          })
          throw error
        }
      },

      retryOperation: async (operationId) => {
        const operation = get().operations.find(op => op.id === operationId)
        if (!operation) {
          throw new Error('Operation not found')
        }

        // Create new operation with same parameters
        return get().startOperation({
          type: operation.type as AIOperationType,
          parameters: operation.parameters,
          organizationId: operation.organizationId,
          priority: 'NORMAL',
        })
      },

      // Model management
      createModel: async (params) => {
        set({ loading: true, error: null })

        try {
          const model = await backendApiClient.createAIModel(params)
          
          const { models } = get()
          
          set({
            models: [model, ...models],
            loading: false,
            lastUpdate: new Date().toISOString(),
          })

          return model
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to create AI model',
          })
          throw error
        }
      },

      trainModel: async (modelId, trainingData) => {
        return get().startOperation({
          type: 'MODEL_TRAINING',
          parameters: { modelId, trainingData },
          organizationId: get().models.find(m => m.id === modelId)?.organizationId || '',
        })
      },

      deployModel: async (modelId) => {
        set({ loading: true, error: null })

        try {
          await backendApiClient.deployAIModel(modelId)
          
          const { models } = get()
          
          set({
            models: models.map(model => 
              model.id === modelId 
                ? { ...model, status: 'deployed' }
                : model
            ),
            loading: false,
            lastUpdate: new Date().toISOString(),
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to deploy AI model',
          })
          throw error
        }
      },

      listModels: async (organizationId) => {
        set({ loading: true, error: null })

        try {
          const models = await backendApiClient.listAIModels(organizationId)
          
          set({
            models,
            loading: false,
            lastUpdate: new Date().toISOString(),
          })

          return models
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to list AI models',
          })
          throw error
        }
      },

      // Document analysis
      analyzeDocument: async (params) => {
        return get().startOperation({
          type: 'DOCUMENT_ANALYSIS',
          parameters: params,
          organizationId: params.organizationId,
          documentId: params.documentId,
        })
      },

      // Batch processing
      processBatch: async (params) => {
        return get().startOperation({
          type: 'BATCH_PROCESSING',
          parameters: params,
          organizationId: params.organizationId,
        })
      },

      // Real-time updates
      enableRealTimeUpdates: () => {
        set({ realTimeUpdates: true })
      },

      disableRealTimeUpdates: () => {
        set({ realTimeUpdates: false })
      },

      updateOperationStatus: (operationId, status, results) => {
        const { operations, processingQueue, completedOperations, failedOperations } = get()

        // Map status to backend format
        const backendStatus = status === 'COMPLETED' ? 'completed' :
                             status === 'FAILED' ? 'failed' :
                             status === 'PROCESSING' ? 'running' :
                             status === 'PENDING' ? 'pending' : status

        const updatedOperations = operations.map(op =>
          op.id === operationId
            ? { ...op, status: backendStatus as any, results, updatedAt: new Date().toISOString() }
            : op
        )

        let newProcessingQueue = [...processingQueue]
        let newCompletedOperations = [...completedOperations]
        let newFailedOperations = [...failedOperations]

        if (status === 'COMPLETED') {
          newProcessingQueue = processingQueue.filter(id => id !== operationId)
          if (!completedOperations.includes(operationId)) {
            newCompletedOperations.push(operationId)
          }
        } else if (status === 'FAILED') {
          newProcessingQueue = processingQueue.filter(id => id !== operationId)
          if (!failedOperations.includes(operationId)) {
            newFailedOperations.push(operationId)
          }
        }

        set({
          operations: updatedOperations,
          processingQueue: newProcessingQueue,
          completedOperations: newCompletedOperations,
          failedOperations: newFailedOperations,
          lastUpdate: new Date().toISOString(),
        })
      },

      // Cache management
      clearCache: () => {
        set({
          operationsCache: new Map(),
          modelsCache: new Map(),
          cacheExpiry: Date.now() + 5 * 60 * 1000,
        })
      },

      refreshCache: async () => {
        const { operations } = get()
        const newCache = new Map()
        
        // Refresh operation cache
        for (const operation of operations) {
          try {
            const updated = await backendApiClient.getAIOperation(operation.id)
            newCache.set(operation.id, updated)
          } catch (error) {
            // Keep existing operation if refresh fails
            newCache.set(operation.id, operation)
          }
        }

        set({
          operationsCache: newCache,
          cacheExpiry: Date.now() + 5 * 60 * 1000,
        })
      },

      // Utility methods
      getOperationProgress: (operationId) => {
        const operation = get().operations.find(op => op.id === operationId)
        return typeof operation?.progress === 'number' ? operation.progress : 0
      },

      getEstimatedCompletion: (operationId) => {
        const operation = get().operations.find(op => op.id === operationId)
        // This would need to be implemented based on backend response
        return null
      },

      isOperationComplete: (operationId) => {
        const operation = get().operations.find(op => op.id === operationId)
        return operation?.status === 'completed' || operation?.status === 'failed'
      },

      // State management
      setCurrentOperation: (operation) => {
        set({ currentOperation: operation })
      },

      clearError: () => {
        set({ error: null })
      },

      reset: () => {
        set({
          operations: [],
          currentOperation: null,
          operationHistory: [],
          models: [],
          activeModel: null,
          isProcessing: false,
          processingQueue: [],
          completedOperations: [],
          failedOperations: [],
          loading: false,
          error: null,
          operationsCache: new Map(),
          modelsCache: new Map(),
          cacheExpiry: Date.now() + 5 * 60 * 1000,
          lastUpdate: null,
        })
      },
    }),
    {
      name: 'ai-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        operationHistory: state.operationHistory.slice(-50), // Keep last 50 operations
        models: state.models,
        completedOperations: state.completedOperations.slice(-100),
        failedOperations: state.failedOperations.slice(-50),
        lastUpdate: state.lastUpdate,
      }),
    }
  )
)

// ============================================================================
// SELECTOR HOOKS
// ============================================================================

// Core selectors
export const useAIOperations = () => useAIStore((state) => state.operations)
export const useCurrentOperation = () => useAIStore((state) => state.currentOperation)
export const useAIModels = () => useAIStore((state) => state.models)
export const useAILoading = () => useAIStore((state) => state.loading)
export const useAIError = () => useAIStore((state) => state.error)

// Processing state selectors
export const useProcessingQueue = () => useAIStore((state) => state.processingQueue)
export const useCompletedOperations = () => useAIStore((state) => state.completedOperations)
export const useFailedOperations = () => useAIStore((state) => state.failedOperations)
export const useIsProcessing = () => useAIStore((state) => state.isProcessing)

// Action hooks
export const useStartOperation = () => useAIStore((state) => state.startOperation) // For compatibility
export const useStartAIOperation = () => useAIStore((state) => state.startOperation)
export const useGetAIOperation = () => useAIStore((state) => state.getOperation)
export const useListAIOperations = () => useAIStore((state) => state.listOperations)
export const useCancelAIOperation = () => useAIStore((state) => state.cancelOperation)
export const useRetryAIOperation = () => useAIStore((state) => state.retryOperation)

// Model management hooks
export const useCreateAIModel = () => useAIStore((state) => state.createModel)
export const useTrainAIModel = () => useAIStore((state) => state.trainModel)
export const useDeployAIModel = () => useAIStore((state) => state.deployModel)
export const useListAIModels = () => useAIStore((state) => state.listModels)

// Document analysis hooks
export const useAnalyzeDocument = () => useAIStore((state) => state.analyzeDocument)
export const useProcessBatch = () => useAIStore((state) => state.processBatch)

// Utility hooks
export const useOperationProgress = () => {
  const getProgress = useAIStore((state) => state.getOperationProgress)
  const getEstimatedCompletion = useAIStore((state) => state.getEstimatedCompletion)
  const isComplete = useAIStore((state) => state.isOperationComplete)

  return {
    getProgress,
    getEstimatedCompletion,
    isComplete,
  }
}

// Real-time updates hook
export const useRealTimeAI = () => {
  const {
    realTimeUpdates,
    enableRealTimeUpdates,
    disableRealTimeUpdates,
    updateOperationStatus,
  } = useAIStore()

  return {
    enabled: realTimeUpdates,
    enable: enableRealTimeUpdates,
    disable: disableRealTimeUpdates,
    updateStatus: updateOperationStatus,
  }
}

// Operations summary hook
export const useAIOperationsSummary = () => {
  const operations = useAIOperations()
  const processingQueue = useProcessingQueue()
  const completedOperations = useCompletedOperations()
  const failedOperations = useFailedOperations()

  const summary = {
    total: operations.length,
    processing: processingQueue.length,
    completed: completedOperations.length,
    failed: failedOperations.length,
    pending: operations.filter(op => op.status === 'pending').length,
    queued: operations.filter(op => op.status === 'running').length,
  }

  return summary
}

// Operation by ID hook
export const useAIOperationById = (operationId: string | null) => {
  return useAIStore((state) =>
    operationId ? state.operations.find(op => op.id === operationId) : null
  )
}

// Operations by status hook
export const useAIOperationsByStatus = (status: string) => {
  return useAIStore((state) =>
    state.operations.filter(op => op.status === status)
  )
}

// Operations by type hook
export const useAIOperationsByType = (type: AIOperationType) => {
  return useAIStore((state) =>
    state.operations.filter(op => op.type === type)
  )
}
