import { useRef, useEffect } from 'react'

/**
 * Previous Hook
 * Tracks the previous value of a variable
 */

export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>()
  
  useEffect(() => {
    ref.current = value
  })
  
  return ref.current
}

/**
 * Previous hook with comparison function
 */
export function usePreviousDistinct<T>(
  value: T,
  compare?: (prev: T | undefined, next: T) => boolean
): T | undefined {
  const ref = useRef<T>()
  
  useEffect(() => {
    if (compare) {
      if (!compare(ref.current, value)) {
        ref.current = value
      }
    } else {
      if (ref.current !== value) {
        ref.current = value
      }
    }
  })
  
  return ref.current
}
