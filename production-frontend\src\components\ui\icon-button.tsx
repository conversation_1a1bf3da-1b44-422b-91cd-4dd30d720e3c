"use client"

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { buttonVariants } from "./button"

export interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  icon: React.ReactNode
  tooltip?: string
  isLoading?: boolean
}

const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ className, variant = "default", size = "icon", asChild = false, icon, tooltip, isLoading, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    
    const buttonContent = (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {isLoading ? (
          <span className="animate-spin">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-loader-2">
              <path d="M21 12a9 9 0 1 1-6.219-8.56" />
            </svg>
          </span>
        ) : (
          icon
        )}
      </Comp>
    )

    if (tooltip) {
      return (
        <div className="relative group">
          {buttonContent}
          <span className="absolute z-10 invisible px-2 py-1 text-xs text-white transition-opacity bg-black rounded opacity-0 pointer-events-none group-hover:visible group-hover:opacity-100 -translate-x-1/2 -translate-y-full left-1/2 top-0 whitespace-nowrap">
            {tooltip}
          </span>
        </div>
      )
    }

    return buttonContent
  }
)
IconButton.displayName = "IconButton"

export { IconButton }
