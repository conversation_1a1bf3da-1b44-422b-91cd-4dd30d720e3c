/**
 * Mobile Device Management Component
 * Manages mobile devices, notifications, and offline sync
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>itle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Bell,
  Download,
  Smartphone,
  RefreshCw as Sync,
  Tablet,
  Wifi,
  WifiOff,
  Send,
  RefreshCw,
  Plus
} from 'lucide-react'

interface MobileDevice {
  id: string
  deviceId: string
  deviceName: string
  deviceType: 'phone' | 'tablet'
  platform: 'ios' | 'android'
  appVersion: string
  lastSeen: string
  isOnline: boolean
  pushToken?: string
  userId: string
  userName: string
  syncStatus: 'synced' | 'pending' | 'error'
  offlineDataSize: number
}

interface NotificationTemplate {
  id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  targetDevices: string[]
}

interface SyncStatus {
  deviceId: string
  lastSync: string
  pendingItems: number
  syncInProgress: boolean
  errorMessage?: string
}

export function MobileDeviceManagement() {
  const { toast } = useToast()
  const [devices, setDevices] = useState<MobileDevice[]>([])
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [sendingNotification, setSendingNotification] = useState(false)
  const [syncingDevice, setSyncingDevice] = useState<string | null>(null)

  // Notification form state
  const [notificationTitle, setNotificationTitle] = useState('')
  const [notificationMessage, setNotificationMessage] = useState('')
  const [notificationType, setNotificationType] = useState<'info' | 'warning' | 'error' | 'success'>('info')
  const [selectedDevices, setSelectedDevices] = useState<string[]>([])
  const [sendToAll, setSendToAll] = useState(false)

  const fetchDevices = async () => {
    try {
      const response = await backendApiClient.request<MobileDevice[]>('/mobile/devices')
      setDevices(response)
    } catch (error: any) {
      console.error('Failed to fetch devices:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch mobile devices',
        variant: 'destructive'
      })
    }
  }

  const fetchSyncStatuses = async () => {
    try {
      const response = await backendApiClient.request<SyncStatus[]>('/mobile/sync/status')
      setSyncStatuses(response)
    } catch (error: any) {
      console.error('Failed to fetch sync statuses:', error)
    }
  }

  const sendNotification = async () => {
    if (!notificationTitle.trim() || !notificationMessage.trim()) {
      toast({
        title: 'Error',
        description: 'Please fill in title and message',
        variant: 'destructive'
      })
      return
    }

    setSendingNotification(true)
    try {
      await backendApiClient.request('/mobile/notifications/send', {
        method: 'POST',
        body: JSON.stringify({
          title: notificationTitle,
          message: notificationMessage,
          type: notificationType,
          targetDevices: sendToAll ? devices.map(d => d.deviceId) : selectedDevices,
          sendToAll
        })
      })

      toast({
        title: 'Success',
        description: 'Notification sent successfully'
      })

      // Reset form
      setNotificationTitle('')
      setNotificationMessage('')
      setSelectedDevices([])
      setSendToAll(false)
    } catch (error: any) {
      console.error('Failed to send notification:', error)
      toast({
        title: 'Error',
        description: 'Failed to send notification',
        variant: 'destructive'
      })
    } finally {
      setSendingNotification(false)
    }
  }

  const syncDevice = async (deviceId: string) => {
    setSyncingDevice(deviceId)
    try {
      await backendApiClient.request('/mobile/devices/sync', {
        method: 'POST',
        body: JSON.stringify({ deviceId })
      })

      toast({
        title: 'Success',
        description: 'Device sync initiated'
      })

      // Refresh sync statuses
      await fetchSyncStatuses()
    } catch (error: any) {
      console.error('Failed to sync device:', error)
      toast({
        title: 'Error',
        description: 'Failed to sync device',
        variant: 'destructive'
      })
    } finally {
      setSyncingDevice(null)
    }
  }

  const getOfflineData = async (deviceId: string) => {
    try {
      const response = await backendApiClient.request(`/mobile/offline-data?deviceId=${deviceId}`)
      
      // Create download link
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `offline-data-${deviceId}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: 'Success',
        description: 'Offline data downloaded'
      })
    } catch (error: any) {
      console.error('Failed to get offline data:', error)
      toast({
        title: 'Error',
        description: 'Failed to download offline data',
        variant: 'destructive'
      })
    }
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([
        fetchDevices(),
        fetchSyncStatuses()
      ])
      setLoading(false)
    }

    loadData()

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      fetchDevices()
      fetchSyncStatuses()
    }, 30000)

    return () => clearInterval(interval)
  }, [])

  const getDeviceIcon = (deviceType: string) => {
    return deviceType === 'tablet' ? <Tablet className="h-4 w-4" /> : <Smartphone className="h-4 w-4" />
  }

  const getSyncStatusBadge = (status: string) => {
    switch (status) {
      case 'synced':
        return <Badge variant="default" className="bg-green-100 text-green-800">Synced</Badge>
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Mobile Device Management</h1>
        <Button onClick={() => { fetchDevices(); fetchSyncStatuses(); }} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{devices.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online Devices</CardTitle>
            <Wifi className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {devices.filter(d => d.isOnline).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Offline Devices</CardTitle>
            <WifiOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {devices.filter(d => !d.isOnline).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sync Pending</CardTitle>
            <Sync className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {devices.filter(d => d.syncStatus === 'pending').length}
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="devices" className="space-y-4">
        <TabsList>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="notifications">Send Notification</TabsTrigger>
          <TabsTrigger value="sync">Sync Management</TabsTrigger>
        </TabsList>

        <TabsContent value="devices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Registered Devices</CardTitle>
              <CardDescription>Manage mobile devices and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Device</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Platform</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sync Status</TableHead>
                    <TableHead>Last Seen</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {devices.map((device) => (
                    <TableRow key={device.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getDeviceIcon(device.deviceType)}
                          <div>
                            <div className="font-medium">{device.deviceName}</div>
                            <div className="text-sm text-muted-foreground">{device.appVersion}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{device.userName}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {device.platform}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {device.isOnline ? (
                            <Wifi className="h-4 w-4 text-green-600" />
                          ) : (
                            <WifiOff className="h-4 w-4 text-red-600" />
                          )}
                          <span className={device.isOnline ? 'text-green-600' : 'text-red-600'}>
                            {device.isOnline ? 'Online' : 'Offline'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getSyncStatusBadge(device.syncStatus)}
                      </TableCell>
                      <TableCell>
                        {new Date(device.lastSeen).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => syncDevice(device.deviceId)}
                            disabled={syncingDevice === device.deviceId}
                          >
                            {syncingDevice === device.deviceId ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Sync className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => getOfflineData(device.deviceId)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Send Push Notification</CardTitle>
              <CardDescription>Send notifications to mobile devices</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={notificationTitle}
                    onChange={(e) => setNotificationTitle(e.target.value)}
                    placeholder="Notification title"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <select
                    id="type"
                    value={notificationType}
                    onChange={(e) => setNotificationType(e.target.value as any)}
                    className="w-full p-2 border rounded-md"
                  >
                    <option value="info">Info</option>
                    <option value="success">Success</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  value={notificationMessage}
                  onChange={(e) => setNotificationMessage(e.target.value)}
                  placeholder="Notification message"
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="send-to-all"
                  checked={sendToAll}
                  onCheckedChange={setSendToAll}
                />
                <Label htmlFor="send-to-all">Send to all devices</Label>
              </div>

              {!sendToAll && (
                <div className="space-y-2">
                  <Label>Select Devices</Label>
                  <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto">
                    {devices.map((device) => (
                      <div key={device.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={device.id}
                          checked={selectedDevices.includes(device.deviceId)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedDevices([...selectedDevices, device.deviceId])
                            } else {
                              setSelectedDevices(selectedDevices.filter(id => id !== device.deviceId))
                            }
                          }}
                        />
                        <Label htmlFor={device.id} className="text-sm">
                          {device.deviceName} ({device.userName})
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button
                onClick={sendNotification}
                disabled={sendingNotification || (!sendToAll && selectedDevices.length === 0)}
                className="w-full"
              >
                {sendingNotification ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Send Notification
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sync Management</CardTitle>
              <CardDescription>Monitor and manage device synchronization</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Device</TableHead>
                    <TableHead>Last Sync</TableHead>
                    <TableHead>Pending Items</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {syncStatuses.map((status) => {
                    const device = devices.find(d => d.deviceId === status.deviceId)
                    return (
                      <TableRow key={status.deviceId}>
                        <TableCell>
                          {device ? device.deviceName : status.deviceId}
                        </TableCell>
                        <TableCell>
                          {new Date(status.lastSync).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant={status.pendingItems > 0 ? 'secondary' : 'default'}>
                            {status.pendingItems}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {status.syncInProgress ? (
                            <Badge variant="secondary">
                              <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                              Syncing
                            </Badge>
                          ) : status.errorMessage ? (
                            <Badge variant="destructive">Error</Badge>
                          ) : (
                            <Badge variant="default">Ready</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => syncDevice(status.deviceId)}
                            disabled={status.syncInProgress || syncingDevice === status.deviceId}
                          >
                            <Sync className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
