"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Wand2, 
  FileText, 
  Sparkles, 
  Download, 
  Copy, 
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  Target,
  Users,
  Clock,
  BarChart3
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useGenerateContent, useOptimizeContent } from '@/hooks/ai/useTextGeneration';

// Types for AI document generation
enum DocumentGenerationType {
  COMPLETE_DOCUMENT = 'complete_document',
  SECTION = 'section',
  OUTLINE = 'outline'
}

interface ContentGenerationRequest {
  type: DocumentGenerationType;
  context: {
    documentType: string;
    purpose: string;
    audience: string;
    tone: string;
    length: string;
  };
  requirements: {
    sections: string[];
    keyPoints: string[];
    constraints: string[];
    mustInclude: string[];
    mustAvoid: string[];
  };
  outputFormat: string;
}

interface GeneratedContentResult {
  content: string;
  structure: {
    metadata: {
      totalWordCount: number;
      completeness: number;
      readingTime: number;
    };
  };
  qualityMetrics: Record<string, number>;
  suggestions: Array<{
    type: string;
    suggestion: string;
  }>;
}

interface DocumentEnhancementOptions {
  enhanceClarity: boolean;
  improveStructure: boolean;
  addMissingContent: boolean;
  fixGrammar: boolean;
  optimizeForAudience: boolean;
  addVisualElements: boolean;
  generateSummary: boolean;
  createOutline: boolean;
}

interface AIDocumentAssistantProps {
  documentId?: string;
  organizationId: string;
  projectId: string;
  onDocumentGenerated?: (documentId: string) => void;
  onContentEnhanced?: (content: string) => void;
}

export function AIDocumentAssistant({
  documentId,
  organizationId,
  projectId,
  onDocumentGenerated,
  onContentEnhanced
}: AIDocumentAssistantProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('generate');

  // AI hooks
  const generateContent = useGenerateContent();
  const optimizeContent = useOptimizeContent();
  
  // Generation state
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationRequest, setGenerationRequest] = useState<Partial<ContentGenerationRequest>>({
    type: DocumentGenerationType.COMPLETE_DOCUMENT,
    context: {
      documentType: '',
      purpose: '',
      audience: '',
      tone: 'formal',
      length: 'medium'
    },
    requirements: {
      sections: [],
      keyPoints: [],
      constraints: [],
      mustInclude: [],
      mustAvoid: []
    },
    outputFormat: 'html'
  });
  const [generatedResult, setGeneratedResult] = useState<GeneratedContentResult | null>(null);
  
  // Enhancement state
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [enhancementOptions, setEnhancementOptions] = useState<DocumentEnhancementOptions>({
    enhanceClarity: true,
    improveStructure: true,
    addMissingContent: true,
    fixGrammar: true,
    optimizeForAudience: true,
    addVisualElements: false,
    generateSummary: true,
    createOutline: true
  });
  
  // Content completion state
  const [partialContent, setPartialContent] = useState('');
  const [completionContext, setCompletionContext] = useState({
    sectionType: 'paragraph',
    desiredLength: 200,
    style: 'professional',
    keyPoints: []
  });
  const [isCompleting, setIsCompleting] = useState(false);

  const handleGenerateDocument = async () => {
    if (!generationRequest.context?.documentType || !generationRequest.context?.purpose) {
      toast({
        title: "Missing Information",
        description: "Please provide document type and purpose.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);
    try {
      const prompt = `Generate a ${generationRequest.context.documentType} document for ${generationRequest.context.purpose}.
      Target audience: ${generationRequest.context.audience}
      Tone: ${generationRequest.context.tone}
      Length: ${generationRequest.context.length}
      Key points to include: ${generationRequest.requirements?.keyPoints?.join(', ')}`;

      const result = await generateContent.mutateAsync({
        prompt,
        contentType: 'document',
        outputFormat: 'html',
        useAdvancedAI: true,
        includeQualityMetrics: true,
        generateSuggestions: true,
        organizationId
      });

      // Transform the result to match expected format
      const transformedResult: GeneratedContentResult = {
        content: result.content,
        structure: {
          metadata: {
            totalWordCount: result.content.split(' ').length,
            completeness: result.confidence * 100,
            readingTime: Math.ceil(result.content.split(' ').length / 200)
          }
        },
        qualityMetrics: result.qualityMetrics || {
          clarity: result.confidence,
          coherence: result.confidence,
          relevance: result.confidence
        },
        suggestions: result.suggestions?.map(s => ({ type: 'improvement', suggestion: s })) || []
      };

      setGeneratedResult(transformedResult);

      toast({
        title: "Document Generated",
        description: `Generated ${transformedResult.structure.metadata.totalWordCount} words with ${Math.round(transformedResult.structure.metadata.completeness)}% completeness.`
      });
    } catch (error) {
      console.error('Failed to generate document:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleEnhanceDocument = async () => {
    if (!documentId) {
      toast({
        title: "No Document",
        description: "Please select a document to enhance.",
        variant: "destructive"
      });
      return;
    }

    setIsEnhancing(true);
    try {
      // Use the optimize content hook instead
      const result = await optimizeContent.mutateAsync({
        content: "Document content to enhance", // This would come from the actual document
        targetAudience: generationRequest.context?.audience || 'general audience',
        tone: generationRequest.context?.tone as any || 'professional',
        purpose: 'inform',
        organizationId
      });
      
      toast({
        title: "Document Enhanced",
        description: `Applied ${result.suggestions?.length || 0} improvements to the document.`
      });

      onContentEnhanced?.(result.content);
    } catch (error) {
      console.error('Failed to enhance document:', error);
      toast({
        title: "Enhancement Failed",
        description: "Failed to enhance document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  const handleCompleteContent = async () => {
    if (!partialContent.trim()) {
      toast({
        title: "No Content",
        description: "Please provide some content to complete.",
        variant: "destructive"
      });
      return;
    }

    setIsCompleting(true);
    try {
      // Use the generate content hook for completion
      const result = await generateContent.mutateAsync({
        prompt: `Complete this content: "${partialContent}". Continue in the same style and tone. Target length: ${completionContext.desiredLength} words.`,
        contentType: 'document',
        outputFormat: 'text',
        useAdvancedAI: true,
        includeQualityMetrics: true,
        organizationId
      });

      toast({
        title: "Content Completed",
        description: `Added ${result.content.length} characters with ${Math.round(result.confidence * 100)}% confidence.`
      });

      onContentEnhanced?.(partialContent + ' ' + result.content);
    } catch (error) {
      console.error('Failed to complete content:', error);
      toast({
        title: "Completion Failed",
        description: "Failed to complete content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCompleting(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Content copied to clipboard."
    });
  };

  const renderQualityMetrics = (metrics: Record<string, number>) => {
    return (
      <div className="grid grid-cols-2 gap-3">
        {Object.entries(metrics).map(([key, value]) => (
          <div key={key} className="space-y-1">
            <div className="flex justify-between text-sm">
              <span className="capitalize">{key}</span>
              <span>{Math.round(value * 100)}%</span>
            </div>
            <Progress value={value * 100} className="h-2" />
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wand2 className="h-5 w-5" />
          AI Document Assistant
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="generate">
              <FileText className="h-4 w-4 mr-2" />
              Generate
            </TabsTrigger>
            <TabsTrigger value="enhance">
              <Sparkles className="h-4 w-4 mr-2" />
              Enhance
            </TabsTrigger>
            <TabsTrigger value="complete">
              <Target className="h-4 w-4 mr-2" />
              Complete
            </TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              {/* Generation Form */}
              <div className="space-y-4">
                <h3 className="font-medium">Document Requirements</h3>
                
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="documentType">Document Type</Label>
                    <Input
                      id="documentType"
                      placeholder="e.g., Business Proposal, Report, Contract"
                      value={generationRequest.context?.documentType || ''}
                      onChange={(e) => setGenerationRequest(prev => ({
                        ...prev,
                        context: { ...prev.context!, documentType: e.target.value }
                      }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="purpose">Purpose</Label>
                    <Input
                      id="purpose"
                      placeholder="What is this document for?"
                      value={generationRequest.context?.purpose || ''}
                      onChange={(e) => setGenerationRequest(prev => ({
                        ...prev,
                        context: { ...prev.context!, purpose: e.target.value }
                      }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="audience">Target Audience</Label>
                    <Input
                      id="audience"
                      placeholder="Who will read this document?"
                      value={generationRequest.context?.audience || ''}
                      onChange={(e) => setGenerationRequest(prev => ({
                        ...prev,
                        context: { ...prev.context!, audience: e.target.value }
                      }))}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label>Tone</Label>
                      <Select
                        value={generationRequest.context?.tone}
                        onValueChange={(value: any) => setGenerationRequest(prev => ({
                          ...prev,
                          context: { ...prev.context!, tone: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="formal">Formal</SelectItem>
                          <SelectItem value="informal">Informal</SelectItem>
                          <SelectItem value="technical">Technical</SelectItem>
                          <SelectItem value="legal">Legal</SelectItem>
                          <SelectItem value="marketing">Marketing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label>Length</Label>
                      <Select
                        value={generationRequest.context?.length}
                        onValueChange={(value: any) => setGenerationRequest(prev => ({
                          ...prev,
                          context: { ...prev.context!, length: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="brief">Brief</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="detailed">Detailed</SelectItem>
                          <SelectItem value="comprehensive">Comprehensive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="keyPoints">Key Points (one per line)</Label>
                    <Textarea
                      id="keyPoints"
                      placeholder="Enter key points to include..."
                      value={generationRequest.requirements?.keyPoints?.join('\n') || ''}
                      onChange={(e) => setGenerationRequest(prev => ({
                        ...prev,
                        requirements: {
                          ...prev.requirements!,
                          keyPoints: e.target.value.split('\n').filter(p => p.trim())
                        }
                      }))}
                    />
                  </div>
                </div>
                
                <Button 
                  onClick={handleGenerateDocument}
                  disabled={isGenerating}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Document
                    </>
                  )}
                </Button>
              </div>
              
              {/* Generated Result */}
              <div className="space-y-4">
                <h3 className="font-medium">Generated Content</h3>
                
                {generatedResult ? (
                  <div className="space-y-4">
                    {/* Quality Metrics */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <BarChart3 className="h-4 w-4" />
                          Quality Metrics
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {renderQualityMetrics(generatedResult.qualityMetrics)}
                      </CardContent>
                    </Card>
                    
                    {/* Content Preview */}
                    <Card>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-sm">Content Preview</CardTitle>
                          <div className="flex gap-2">
                            <Badge variant="outline">
                              {generatedResult.structure.metadata.totalWordCount} words
                            </Badge>
                            <Badge variant="outline">
                              <Clock className="h-3 w-3 mr-1" />
                              {generatedResult.structure.metadata.readingTime}min
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="max-h-64 overflow-y-auto text-sm">
                          <div dangerouslySetInnerHTML={{ __html: generatedResult.content }} />
                        </div>
                        <div className="flex gap-2 mt-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(generatedResult.content)}
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onDocumentGenerated?.('new-document')}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Create Document
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                    
                    {/* Suggestions */}
                    {generatedResult.suggestions.length > 0 && (
                      <Card>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-sm flex items-center gap-2">
                            <Lightbulb className="h-4 w-4" />
                            Suggestions
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-2">
                            {generatedResult.suggestions.slice(0, 3).map((suggestion, index) => (
                              <Alert key={index}>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription className="text-xs">
                                  <strong>{suggestion.type}:</strong> {suggestion.suggestion}
                                </AlertDescription>
                              </Alert>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-12 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Generated content will appear here</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="enhance" className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              {/* Enhancement Options */}
              <div className="space-y-4">
                <h3 className="font-medium">Enhancement Options</h3>
                
                <div className="space-y-3">
                  {Object.entries(enhancementOptions).map(([key, value]) => (
                    <div key={key} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={key}
                        checked={value}
                        onChange={(e) => setEnhancementOptions(prev => ({
                          ...prev,
                          [key]: e.target.checked
                        }))}
                      />
                      <Label htmlFor={key} className="text-sm">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </Label>
                    </div>
                  ))}
                </div>
                
                <Button 
                  onClick={handleEnhanceDocument}
                  disabled={isEnhancing || !documentId}
                  className="w-full"
                >
                  {isEnhancing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Enhancing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Enhance Document
                    </>
                  )}
                </Button>
                
                {!documentId && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Please select a document to enhance.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
              
              {/* Enhancement Preview */}
              <div className="space-y-4">
                <h3 className="font-medium">Enhancement Preview</h3>
                <div className="text-center py-12 text-muted-foreground">
                  <Sparkles className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Enhanced content will appear here</p>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="complete" className="space-y-6">
            <div className="grid grid-cols-2 gap-6">
              {/* Content Input */}
              <div className="space-y-4">
                <h3 className="font-medium">Content Completion</h3>
                
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="partialContent">Partial Content</Label>
                    <Textarea
                      id="partialContent"
                      placeholder="Enter the content you want to complete..."
                      value={partialContent}
                      onChange={(e) => setPartialContent(e.target.value)}
                      rows={6}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label>Section Type</Label>
                      <Select
                        value={completionContext.sectionType}
                        onValueChange={(value) => setCompletionContext(prev => ({
                          ...prev,
                          sectionType: value
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="paragraph">Paragraph</SelectItem>
                          <SelectItem value="introduction">Introduction</SelectItem>
                          <SelectItem value="conclusion">Conclusion</SelectItem>
                          <SelectItem value="summary">Summary</SelectItem>
                          <SelectItem value="analysis">Analysis</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label>Desired Length</Label>
                      <Input
                        type="number"
                        value={completionContext.desiredLength}
                        onChange={(e) => setCompletionContext(prev => ({
                          ...prev,
                          desiredLength: parseInt(e.target.value) || 200
                        }))}
                      />
                    </div>
                  </div>
                </div>
                
                <Button 
                  onClick={handleCompleteContent}
                  disabled={isCompleting || !partialContent.trim()}
                  className="w-full"
                >
                  {isCompleting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Completing...
                    </>
                  ) : (
                    <>
                      <Target className="h-4 w-4 mr-2" />
                      Complete Content
                    </>
                  )}
                </Button>
              </div>
              
              {/* Completion Preview */}
              <div className="space-y-4">
                <h3 className="font-medium">Completed Content</h3>
                <div className="text-center py-12 text-muted-foreground">
                  <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Completed content will appear here</p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
