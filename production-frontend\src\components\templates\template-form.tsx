"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  FileStack,
  Save,
  X,
  Plus,
  FileText,
  Mail,
  Git<PERSON>ranch,
  <PERSON><PERSON><PERSON><PERSON><PERSON>ban
} from "lucide-react";
import { Template, TemplateType, TemplateStatus } from "@/services/template-service";
import { EditorJSTemplateEditor } from "./editorjs-template-editor";
import { TemplateFieldsEditor } from "./template-fields-editor";
import { cn } from "@/lib/utils";

// Define form schema
const templateFormSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name is too long"),
  description: z.string().max(500, "Description is too long").optional(),
  type: z.nativeEnum(TemplateType),
  tags: z.array(z.string()).optional(),
  status: z.nativeEnum(TemplateStatus),
  content: z.string().optional(),
  organizationId: z.string().min(1, "Organization is required"),
  projectId: z.string().optional(),
  isDefault: z.boolean().optional(),
  sections: z.array(z.any()).optional(),
});

type TemplateFormValues = z.infer<typeof templateFormSchema>;

interface TemplateFormProps {
  template?: Template;
  organizationId: string;
  projectId?: string;
  onSubmit: (values: TemplateFormValues) => void;
  isSubmitting?: boolean;
  className?: string;
}

export function TemplateForm({
  template,
  organizationId,
  projectId,
  onSubmit,
  isSubmitting = false,
  className
}: TemplateFormProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("details");
  const [tagInput, setTagInput] = useState("");

  // Initialize form
  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      name: template?.name || "",
      description: template?.description || "",
      type: template?.type || TemplateType.DOCUMENT,
      tags: template?.tags || [],
      status: template?.status || TemplateStatus.DRAFT,
      content: typeof template?.content === 'string' ? template.content : "",
      organizationId: organizationId,
      projectId: projectId || template?.projectId,
      isDefault: template?.isDefault || false,
    },
  });

  // Update form when template changes
  useEffect(() => {
    if (template) {
      form.reset({
        name: template.name,
        description: template.description || "",
        type: template.type,
        tags: template.tags || [],
        status: template.status,
        content: typeof template.content === 'string' ? template.content : "",
        organizationId: template.organizationId,
        projectId: template.projectId,
        isDefault: template.isDefault || false,
      });
    }
  }, [template, form]);

  // Handle form submission
  const handleSubmit = (values: TemplateFormValues) => {
    onSubmit(values);
  };

  // Handle tag input
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && tagInput) {
      e.preventDefault();
      const currentTags = form.getValues("tags") || [];
      if (!currentTags.includes(tagInput)) {
        form.setValue("tags", [...currentTags, tagInput]);
        setTagInput("");
      }
    }
  };

  // Handle tag removal
  const handleRemoveTag = (tag: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter(t => t !== tag));
  };

  // Get type icon - kept for future use
  // const getTypeIcon = (type: TemplateType) => {
  //   switch (type) {
  //     case TemplateType.DOCUMENT:
  //       return <FileText className="h-4 w-4" />;
  //     case TemplateType.EMAIL:
  //       return <Mail className="h-4 w-4" />;
  //     case TemplateType.FORM:
  //       return <FileStack className="h-4 w-4" />;
  //     case TemplateType.WORKFLOW:
  //       return <GitBranch className="h-4 w-4" />;
  //     case TemplateType.PROJECT:
  //       return <FolderKanban className="h-4 w-4" />;
  //     default:
  //       return <FileText className="h-4 w-4" />;
  //   }
  // };

  return (
    <div className={cn("space-y-6", className)}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 w-full md:w-[400px]">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="fields">Fields</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Details</CardTitle>
                  <CardDescription>
                    Basic information about the template
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter template name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter template description"
                            rows={3}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select template type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value={TemplateType.DOCUMENT}>
                                <div className="flex items-center gap-2">
                                  <FileText className="h-4 w-4" />
                                  <span>Document</span>
                                </div>
                              </SelectItem>
                              <SelectItem value={TemplateType.FORM}>
                                <div className="flex items-center gap-2">
                                  <FileStack className="h-4 w-4" />
                                  <span>Form</span>
                                </div>
                              </SelectItem>
                              <SelectItem value={TemplateType.EMAIL}>
                                <div className="flex items-center gap-2">
                                  <Mail className="h-4 w-4" />
                                  <span>Email</span>
                                </div>
                              </SelectItem>
                              <SelectItem value={TemplateType.WORKFLOW}>
                                <div className="flex items-center gap-2">
                                  <GitBranch className="h-4 w-4" />
                                  <span>Workflow</span>
                                </div>
                              </SelectItem>
                              <SelectItem value={TemplateType.PROJECT}>
                                <div className="flex items-center gap-2">
                                  <FolderKanban className="h-4 w-4" />
                                  <span>Project</span>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value={TemplateStatus.DRAFT}>Draft</SelectItem>
                              <SelectItem value={TemplateStatus.PUBLISHED}>Published</SelectItem>
                              <SelectItem value={TemplateStatus.ARCHIVED}>Archived</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div>
                    <FormLabel>Tags</FormLabel>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {form.watch("tags")?.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => handleRemoveTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a tag"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={handleTagInputKeyDown}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => {
                          if (tagInput) {
                            const currentTags = form.getValues("tags") || [];
                            if (!currentTags.includes(tagInput)) {
                              form.setValue("tags", [...currentTags, tagInput]);
                              setTagInput("");
                            }
                          }
                        }}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="content" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Content</CardTitle>
                  <CardDescription>
                    Edit the content of your template
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <EditorJSTemplateEditor
                            value={field.value || ""}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="fields" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Template Fields</CardTitle>
                  <CardDescription>
                    Define fields for your template
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <TemplateFieldsEditor
                    template={template}
                    onChange={(sections) => {
                      // Update the form with the sections
                      form.setValue('sections', sections);
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-4 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>Saving...</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Template
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
