"use client";

import { useState } from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from "@dnd-kit/sortable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Trash2,
  ChevronDown,
  ChevronUp,
  GripVertical,
  FormInput,
  Calendar,
  FileText,
  Type,
  Hash,
  CheckSquare,
  List
} from "lucide-react";
import { TemplateSection, TemplateField, FieldType } from "@/services/template-fields-service";
import { SortableTemplateField } from "./sortable-template-field";
import { cn } from "@/lib/utils";

interface SortableTemplateSectionProps {
  section: TemplateSection;
  onUpdate: (section: TemplateSection) => void;
  onRemove: (id: string) => void;
  onAddField: (sectionId: string, type: FieldType) => void;
  onRemoveField: (sectionId: string, fieldId: string) => void;
  onUpdateField: (sectionId: string, field: TemplateField) => void;
  onFieldDragEnd: (event: DragEndEvent) => void;
}

export function SortableTemplateSection({
  section,
  onUpdate,
  onRemove,
  onAddField,
  onRemoveField,
  onUpdateField,
  onFieldDragEnd
}: SortableTemplateSectionProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: section.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.5 : 1,
  };

  // Set up DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Update section field
  const updateField = (field: string, value: any) => {
    onUpdate({
      ...section,
      [field]: value
    });
  };

  // Toggle expanded state
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div ref={setNodeRef} style={style} className="touch-none">
      <Card className={cn(
        "w-full transition-shadow",
        isDragging ? "shadow-lg" : ""
      )}>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="cursor-grab"
                {...attributes}
                {...listeners}
              >
                <GripVertical className="h-4 w-4" />
              </Button>

              <CardTitle className="text-base flex items-center gap-2">
                Section: {section.name}
              </CardTitle>
            </div>

            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onRemove(section.id)}
              >
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={toggleExpanded}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        {isExpanded && (
          <CardContent className="space-y-4">
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor={`section-name-${section.id}`}>Section Name</Label>
                <Input
                  id={`section-name-${section.id}`}
                  value={section.name}
                  onChange={(e) => updateField("name", e.target.value)}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor={`section-description-${section.id}`}>Description</Label>
                <Textarea
                  id={`section-description-${section.id}`}
                  value={section.description || ""}
                  onChange={(e) => updateField("description", e.target.value)}
                  rows={2}
                />
              </div>
            </div>

            <div className="flex justify-between items-center">
              <h4 className="text-sm font-medium">Fields</h4>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Field
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Field Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onAddField(section.id, 'text')}>
                    <Type className="mr-2 h-4 w-4" />
                    Text
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAddField(section.id, 'number')}>
                    <Hash className="mr-2 h-4 w-4" />
                    Number
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAddField(section.id, 'date')}>
                    <Calendar className="mr-2 h-4 w-4" />
                    Date
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAddField(section.id, 'checkbox')}>
                    <CheckSquare className="mr-2 h-4 w-4" />
                    Checkbox
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAddField(section.id, 'select')}>
                    <List className="mr-2 h-4 w-4" />
                    Select
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onAddField(section.id, 'richtext')}>
                    <FileText className="mr-2 h-4 w-4" />
                    Rich Text
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {section.fields.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-4 border border-dashed rounded-lg bg-muted/50">
                <FormInput className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-muted-foreground text-center text-sm">
                  No fields added yet. Use the "Add Field" button to create fields for this section.
                </p>
              </div>
            ) : (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={onFieldDragEnd}
              >
                <SortableContext
                  items={section.fields.map(field => field.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-2">
                    {section.fields.map((field) => (
                      <SortableTemplateField
                        key={field.id}
                        field={field as any}
                        onUpdate={(updatedField) => onUpdateField(section.id, updatedField as any)}
                        onRemove={() => onRemoveField(section.id, field.id)}
                      />
                    ))}
                  </div>
                </SortableContext>
              </DndContext>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}
