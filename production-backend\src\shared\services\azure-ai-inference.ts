/**
 * Azure AI Inference Service
 * Unified service for all Azure AI operations using the Azure AI Inference SDK
 * Replaces individual Azure OpenAI and Form Recognizer clients
 */

import ModelClient, { isUnexpected } from '@azure-rest/ai-inference';
import { logger } from '../utils/logger';
import { azureIdentityService } from './azure-identity';
import { config } from '../../env';

// AI Inference Types
export interface AIInferenceConfig {
  endpoint: string;
  enabled: boolean;
  model?: string;
}

export interface ChatCompletionRequest {
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  stream?: boolean;
}

export interface ChatCompletionResponse {
  content: string;
  reasoning?: string;
  confidence: number;
  tokensUsed: number;
  model: string;
  processingTime: number;
  finishReason?: string;
}

export interface EmbeddingRequest {
  input: string | string[];
  model?: string;
}

export interface EmbeddingResponse {
  embeddings: number[][];
  dimensions: number;
  model: string;
  tokensUsed: number;
}

export interface DocumentAnalysisRequest {
  documentBuffer: Buffer;
  modelId?: string;
  features?: string[];
}

export interface DocumentAnalysisResponse {
  content: string;
  layout: any;
  tables: any[];
  keyValuePairs: any[];
  entities: any[];
  confidence: number;
  processingTime: number;
  modelUsed: string;
}

/**
 * Azure AI Inference Service
 * Provides unified access to all Azure AI models through the AI Inference SDK
 */
export class AzureAIInferenceService {
  private clients: Map<string, any> = new Map();
  private initialized: boolean = false;
  private configs: Map<string, AIInferenceConfig> = new Map();

  constructor() {
    // Initialize configurations for different AI services
    this.configs.set('deepseek-r1', {
      endpoint: config.ai.deepSeekR1.endpoint,
      enabled: config.ai.deepSeekR1.enabled && !!config.ai.deepSeekR1.endpoint,
      model: config.ai.deepSeekR1.deploymentName
    });

    this.configs.set('llama', {
      endpoint: config.ai.llama.endpoint,
      enabled: config.ai.llama.enabled && !!config.ai.llama.endpoint,
      model: config.ai.llama.deploymentName
    });

    this.configs.set('document-intelligence', {
      endpoint: config.ai.documentIntelligence.endpoint,
      enabled: !!config.ai.documentIntelligence.endpoint
    });

    this.configs.set('cohere-embedding', {
      endpoint: config.ai.cohere.endpoint,
      enabled: !!config.ai.cohere.endpoint,
      model: 'embed-v3-multilingual'
    });
  }

  /**
   * Initialize all AI Inference clients
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // Initialize Azure Identity service if not already done
      if (!azureIdentityService.isReady()) {
        await azureIdentityService.initialize();
      }

      const credential = azureIdentityService.getCredential();

      // Initialize clients for each configured service
      for (const [serviceName, config] of this.configs.entries()) {
        if (config.enabled && config.endpoint) {
          try {
            const client = ModelClient(config.endpoint, credential);
            this.clients.set(serviceName, client);
            
            // Test the connection
            await this.testConnection(serviceName);
            
            logger.info(`Azure AI Inference client initialized for ${serviceName}`, {
              endpoint: config.endpoint,
              model: config.model
            });
          } catch (error) {
            logger.error(`Failed to initialize AI Inference client for ${serviceName}`, {
              error: error instanceof Error ? error.message : String(error),
              endpoint: config.endpoint
            });
            // Don't throw here - allow other services to initialize
          }
        } else {
          logger.warn(`AI Inference service ${serviceName} is disabled or not configured`);
        }
      }

      this.initialized = true;
      logger.info('Azure AI Inference Service initialized successfully', {
        enabledServices: Array.from(this.clients.keys())
      });
    } catch (error) {
      logger.error('Failed to initialize Azure AI Inference Service', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Test connection for a specific service
   */
  private async testConnection(serviceName: string): Promise<void> {
    const client = this.clients.get(serviceName);
    if (!client) {
      throw new Error(`Client not found for service: ${serviceName}`);
    }

    try {
      // Test with a simple chat completion for AI models
      if (serviceName === 'deepseek-r1' || serviceName === 'llama') {
        const response = await client.path('/chat/completions').post({
          body: {
            messages: [{ role: 'user', content: 'Test connection' }],
            max_tokens: 10
          }
        });

        if (isUnexpected(response)) {
          throw new Error(`Connection test failed: ${response.body.error?.message || 'Unknown error'}`);
        }
      }
      // Test embeddings for embedding services
      else if (serviceName === 'cohere-embedding') {
        const response = await client.path('/embeddings').post({
          body: {
            input: ['test'],
            model: this.configs.get(serviceName)?.model
          }
        });

        if (isUnexpected(response)) {
          throw new Error(`Embedding test failed: ${response.body.error?.message || 'Unknown error'}`);
        }
      }

      logger.info(`Connection test successful for ${serviceName}`);
    } catch (error) {
      logger.error(`Connection test failed for ${serviceName}`, {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Get a client for a specific service
   */
  private getClient(serviceName: string): any {
    if (!this.initialized) {
      throw new Error('Azure AI Inference Service not initialized');
    }

    const client = this.clients.get(serviceName);
    if (!client) {
      throw new Error(`Client not available for service: ${serviceName}. Check configuration and initialization.`);
    }

    return client;
  }

  /**
   * Get service configuration
   */
  private getConfig(serviceName: string): AIInferenceConfig {
    const config = this.configs.get(serviceName);
    if (!config) {
      throw new Error(`Configuration not found for service: ${serviceName}`);
    }
    return config;
  }

  /**
   * Check if service is available
   */
  isServiceAvailable(serviceName: string): boolean {
    return this.clients.has(serviceName) && this.configs.get(serviceName)?.enabled === true;
  }

  /**
   * Get list of available services
   */
  getAvailableServices(): string[] {
    return Array.from(this.clients.keys());
  }

  /**
   * Generate chat completion using specified AI model
   */
  async generateChatCompletion(
    serviceName: string,
    request: ChatCompletionRequest
  ): Promise<ChatCompletionResponse> {
    await this.initialize();

    const client = this.getClient(serviceName);
    const config = this.getConfig(serviceName);
    const startTime = Date.now();

    try {
      logger.debug(`Generating chat completion with ${serviceName}`, {
        model: config.model,
        messageCount: request.messages.length,
        maxTokens: request.maxTokens
      });

      const response = await client.path('/chat/completions').post({
        body: {
          messages: request.messages,
          max_tokens: request.maxTokens || 4000,
          temperature: request.temperature || 0.7,
          top_p: request.topP || 0.9,
          stream: request.stream || false,
          model: config.model
        }
      });

      if (isUnexpected(response)) {
        throw new Error(`Chat completion failed: ${response.body.error?.message || 'Unknown error'}`);
      }

      const choice = response.body.choices[0];
      const processingTime = Date.now() - startTime;

      const result: ChatCompletionResponse = {
        content: choice.message.content || '',
        reasoning: this.extractReasoning(choice.message.content || '', serviceName),
        confidence: this.calculateConfidence(choice.finish_reason),
        tokensUsed: response.body.usage?.total_tokens || 0,
        model: config.model || serviceName,
        processingTime,
        finishReason: choice.finish_reason
      };

      logger.debug(`Chat completion generated successfully with ${serviceName}`, {
        tokensUsed: result.tokensUsed,
        processingTime: result.processingTime,
        confidence: result.confidence
      });

      return result;
    } catch (error) {
      logger.error(`Chat completion failed for ${serviceName}`, {
        error: error instanceof Error ? error.message : String(error),
        model: config.model
      });
      throw error;
    }
  }

  /**
   * Generate embeddings using specified embedding model
   */
  async generateEmbeddings(
    serviceName: string,
    request: EmbeddingRequest
  ): Promise<EmbeddingResponse> {
    await this.initialize();

    const client = this.getClient(serviceName);
    const config = this.getConfig(serviceName);

    try {
      logger.debug(`Generating embeddings with ${serviceName}`, {
        model: config.model,
        inputType: typeof request.input,
        inputLength: Array.isArray(request.input) ? request.input.length : 1
      });

      const response = await client.path('/embeddings').post({
        body: {
          input: request.input,
          model: request.model || config.model
        }
      });

      if (isUnexpected(response)) {
        throw new Error(`Embedding generation failed: ${response.body.error?.message || 'Unknown error'}`);
      }

      const embeddings = response.body.data.map((item: any) => item.embedding);
      const dimensions = embeddings.length > 0 ? embeddings[0].length : 0;

      const result: EmbeddingResponse = {
        embeddings,
        dimensions,
        model: request.model || config.model || serviceName,
        tokensUsed: response.body.usage?.total_tokens || 0
      };

      logger.debug(`Embeddings generated successfully with ${serviceName}`, {
        embeddingCount: embeddings.length,
        dimensions: result.dimensions,
        tokensUsed: result.tokensUsed
      });

      return result;
    } catch (error) {
      logger.error(`Embedding generation failed for ${serviceName}`, {
        error: error instanceof Error ? error.message : String(error),
        model: config.model
      });
      throw error;
    }
  }

  /**
   * Extract reasoning from AI response (specific to DeepSeek R1)
   */
  private extractReasoning(content: string, serviceName: string): string {
    if (serviceName !== 'deepseek-r1') return '';

    const reasoningPatterns = [
      /Let me think step by step:(.*?)(?=\n\n|$)/s,
      /Reasoning:(.*?)(?=\n\n|$)/s,
      /Analysis:(.*?)(?=\n\n|$)/s,
      /<thinking>(.*?)<\/thinking>/s
    ];

    for (const pattern of reasoningPatterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return '';
  }

  /**
   * Calculate confidence based on finish reason
   */
  private calculateConfidence(finishReason?: string): number {
    switch (finishReason) {
      case 'stop':
        return 0.9;
      case 'length':
        return 0.7;
      case 'content_filter':
        return 0.3;
      default:
        return 0.5;
    }
  }
}

// Export singleton instance
export const azureAIInference = new AzureAIInferenceService();
