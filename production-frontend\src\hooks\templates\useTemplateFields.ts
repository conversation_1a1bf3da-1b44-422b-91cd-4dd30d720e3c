/**
 * Template Fields Hook
 * Manages template field operations and state
 */

import { useState, useCallback } from 'react'
import { templateService } from '@/services/template-service'
import { useToast } from '@/hooks/use-toast'
import type { TemplateField } from '@/types/backend'
import type { FieldType, ID } from '@/types'

export interface UseTemplateFieldsOptions {
  templateId?: ID
  autoSave?: boolean
}

export interface UseTemplateFieldsResult {
  fields: TemplateField[]
  loading: boolean
  error: string | null

  // Section properties (for template fields editor compatibility)
  sections: any[]
  setSections: (sections: any[] | ((prev: any[]) => any[])) => void
  isLoadingSections: boolean
  sectionsError: string | null

  // Field operations
  addField: (sectionId: string, type: FieldType) => void
  updateField: (fieldId: string, updates: Partial<TemplateField>) => void
  deleteField: (params: { sectionId: string; fieldId: string }) => void
  removeField: (fieldId: string) => void
  createField: (sectionId: string, type: FieldType) => void
  reorderFields: (sectionId: string, oldIndex: number, newIndex: number) => void

  // Section operations
  addSection: (name: string, description?: string) => void
  updateSection: (params: { sectionId: string; section: any }) => void
  deleteSection: (sectionId: string) => void
  removeSection: (sectionId: string) => void
  reorderSections: (oldIndex: number, newIndex: number) => void

  // Validation
  validateFields: () => boolean
  getFieldErrors: () => Record<string, string[]>

  // Save operations
  saveFields: () => Promise<void>
  resetFields: () => void
}

export function useTemplateFields(options: UseTemplateFieldsOptions = {}): UseTemplateFieldsResult {
  const { templateId, autoSave = false } = options
  const { toast } = useToast()

  const [fields, setFields] = useState<TemplateField[]>([])
  const [sections, setSections] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Field operations
  const addField = useCallback((_sectionId: string, type: FieldType) => {
    const newField: TemplateField = {
      id: `field_${Date.now()}`,
      name: `New ${type} field`,
      type: type as any, // Type conversion needed due to type mismatch
      label: `New ${type} field`,
      required: false,
      helpText: '',
    }
    
    setFields(prev => [...prev, newField])
    
    if (autoSave) {
      saveFields()
    }
  }, [autoSave])

  const updateField = useCallback((fieldId: string, updates: Partial<TemplateField>) => {
    setFields(prev => prev.map(field => 
      field.id === fieldId ? { ...field, ...updates } : field
    ))
    
    if (autoSave) {
      saveFields()
    }
  }, [autoSave])

  const removeField = useCallback((fieldId: string) => {
    setFields(prev => prev.filter(field => field.id !== fieldId))

    if (autoSave) {
      saveFields()
    }
  }, [autoSave])

  const deleteField = useCallback((params: { sectionId: string; fieldId: string }) => {
    removeField(params.fieldId)
  }, [removeField])

  const createField = useCallback((sectionId: string, type: FieldType) => {
    addField(sectionId, type)
  }, [addField])

  const reorderFields = useCallback((_sectionId: string, oldIndex: number, newIndex: number) => {
    setFields(prev => {
      const newFields = [...prev]
      const [removed] = newFields.splice(oldIndex, 1)
      newFields.splice(newIndex, 0, removed)
      return newFields
    })

    if (autoSave) {
      saveFields()
    }
  }, [autoSave])

  // Section operations with comprehensive management
  const addSection = useCallback((name: string, description?: string) => {
    const newSection = {
      id: `section_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      name: name.trim(),
      description: description?.trim() || '',
      fields: [],
      order: sections.length,
      isCollapsible: true,
      isCollapsed: false,
      isRequired: false,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    }
    setSections(prev => [...prev, newSection])

    // Auto-save if enabled
    if (autoSave && templateId) {
      templateService.updateTemplate(templateId, {
        fields: [...sections, newSection]
      }).catch(console.error);
    }
  }, [sections, autoSave, templateId])

  const updateSection = useCallback((params: { sectionId: string; section: any }) => {
    setSections(prev => prev.map(section =>
      section.id === params.sectionId ? { ...section, ...params.section } : section
    ))
  }, [])

  const deleteSection = useCallback((sectionId: string) => {
    setSections(prev => prev.filter(section => section.id !== sectionId))
  }, [])

  const removeSection = useCallback((sectionId: string) => {
    deleteSection(sectionId)
  }, [deleteSection])

  const reorderSections = useCallback((oldIndex: number, newIndex: number) => {
    setSections(prev => {
      const newSections = [...prev]
      const [movedSection] = newSections.splice(oldIndex, 1)
      newSections.splice(newIndex, 0, movedSection)

      // Update order property for all sections
      return newSections.map((section, index) => ({
        ...section,
        order: index
      }))
    })
  }, [])

  // Validation
  const validateFields = useCallback((): boolean => {
    const errors = getFieldErrors()
    return Object.keys(errors).length === 0
  }, [])

  const getFieldErrors = useCallback((): Record<string, string[]> => {
    const errors: Record<string, string[]> = {}
    
    fields.forEach(field => {
      const fieldErrors: string[] = []
      
      if (!field.name.trim()) {
        fieldErrors.push('Field name is required')
      }
      
      if (field.type === 'select' && (!field.options || field.options.length === 0)) {
        fieldErrors.push('Select fields must have at least one option')
      }
      
      if (fieldErrors.length > 0) {
        errors[field.id] = fieldErrors
      }
    })
    
    return errors
  }, [fields])

  // Save operations
  const saveFields = useCallback(async () => {
    if (!templateId) {
      setError('No template ID provided')
      return
    }

    setLoading(true)
    setError(null)

    try {
      await templateService.updateTemplate(templateId, { fields })
      
      toast({
        type: 'success',
        title: 'Fields saved',
        description: 'Template fields have been saved successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to save fields'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Save failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [templateId, fields, toast])

  const resetFields = useCallback(() => {
    setFields([])
    setError(null)
  }, [])

  return {
    fields,
    loading,
    error,

    // Section properties
    sections,
    setSections,
    isLoadingSections: loading,
    sectionsError: error,

    // Field operations
    addField,
    updateField,
    deleteField,
    removeField,
    createField,
    reorderFields,

    // Section operations
    addSection,
    updateSection,
    deleteSection,
    removeSection,
    reorderSections,

    // Validation
    validateFields,
    getFieldErrors,

    // Save operations
    saveFields,
    resetFields,
  }
}
