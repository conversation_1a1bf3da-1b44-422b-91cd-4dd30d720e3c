/**
 * User and Authentication Types
 */

import type { ID, Timestamp, Email, URL, Permission, Role, Organization } from './index'

// Core user interface
export interface User {
  id: ID
  email: Email
  firstName: string
  lastName: string
  name: string // Computed from firstName + lastName or displayName
  displayName: string
  avatar?: URL
  avatarUrl?: string // Alternative property name used by some components
  bio?: string
  title?: string
  department?: string
  phone?: string
  timezone: string
  language: string
  theme: 'light' | 'dark' | 'system'
  status: UserStatus
  roles: Role[]
  systemRoles?: string[] // System-level roles
  permissions: Permission[]
  organizations: UserOrganization[]
  organizationIds?: ID[] // Legacy property for backward compatibility
  preferences: UserPreferences
  lastLoginAt?: Timestamp
  createdAt: Timestamp
  updatedAt: Timestamp
  isActive: boolean
  isVerified: boolean
  // Additional properties expected by components
  company?: string
  jobTitle?: string
  website?: string
}

// User status
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending'
}

// User organization membership
export interface UserOrganization {
  organizationId: ID
  organization: Organization
  role: Role
  joinedAt: Timestamp
  isDefault: boolean
}

// User preferences
export interface UserPreferences {
  notifications: NotificationPreferences
  dashboard: DashboardPreferences
  privacy: PrivacyPreferences
  accessibility: AccessibilityPreferences
}

export interface NotificationPreferences {
  email: {
    enabled: boolean
    frequency: 'immediate' | 'daily' | 'weekly'
    types: string[]
  }
  push: {
    enabled: boolean
    types: string[]
  }
  inApp: {
    enabled: boolean
    types: string[]
  }
  // Specific notification types expected by components
  documentUploaded?: boolean
  documentProcessed?: boolean
  commentAdded?: boolean
  mentionedInComment?: boolean
  projectInvitation?: boolean
  organizationInvitation?: boolean
}

export interface DashboardPreferences {
  layout: 'grid' | 'list'
  widgets: DashboardWidget[]
  defaultView: string
  autoRefresh: boolean
  refreshInterval: number // in seconds
  showWelcomeMessage?: boolean // Additional property expected by components
}

export interface DashboardWidget {
  id: string
  type: string
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  config: Record<string, any>
  visible: boolean
}

export interface PrivacyPreferences {
  profileVisibility: 'public' | 'organization' | 'private'
  activityVisibility: 'public' | 'organization' | 'private'
  allowAnalytics: boolean
  allowMarketing: boolean
}

export interface AccessibilityPreferences {
  fontSize: 'small' | 'medium' | 'large'
  highContrast: boolean
  reduceMotion: boolean
  screenReader: boolean
}

// Authentication types
export interface AuthState {
  user: User | null
  token: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  expiresAt: number | null
}

// Standard token format (from backend)
export interface StandardToken {
  accessToken: string
  refreshToken: string
  expiresAt: number
  userId: ID
  email: Email
  roles: string[]
  permissions: string[]
  organizationIds: ID[]
  tenantId: ID
}

// Login credentials
export interface LoginCredentials {
  email: Email
  password: string
  rememberMe?: boolean
  organizationId?: ID
}

// Registration data
export interface RegisterData {
  email: Email
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  organizationName?: string
  inviteCode?: string
  acceptTerms: boolean
}

// Password reset
export interface PasswordResetRequest {
  email: Email
}

export interface PasswordResetData {
  token: string
  password: string
  confirmPassword: string
}

// Password change
export interface PasswordChangeData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// Profile update
export interface ProfileUpdateData {
  firstName?: string
  lastName?: string
  displayName?: string
  bio?: string
  title?: string
  department?: string
  phone?: string
  timezone?: string
  language?: string
  avatar?: File
}

// User session
export interface UserSession {
  id: ID
  userId: ID
  deviceInfo: DeviceInfo
  ipAddress: string
  location?: {
    country: string
    city: string
  }
  createdAt: Timestamp
  lastActiveAt: Timestamp
  expiresAt: Timestamp
  isActive: boolean
}

export interface DeviceInfo {
  userAgent: string
  browser: string
  os: string
  device: string
  isMobile: boolean
}

// User activity
export interface UserActivity {
  id: ID
  userId: ID
  type: string
  description: string
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: Timestamp
}

// User invitation
export interface UserInvitation {
  id: ID
  email: Email
  organizationId: ID
  roleId: ID
  invitedBy: ID
  invitedAt: Timestamp
  expiresAt: Timestamp
  acceptedAt?: Timestamp
  status: 'pending' | 'accepted' | 'expired' | 'cancelled'
  message?: string
}

// User search/filter
export interface UserSearchQuery {
  query?: string
  organizationId?: ID
  role?: string
  status?: UserStatus
  isActive?: boolean
  createdAfter?: Timestamp
  createdBefore?: Timestamp
}

// User statistics
export interface UserStats {
  totalUsers: number
  activeUsers: number
  newUsersThisMonth: number
  usersByRole: Record<string, number>
  usersByOrganization: Record<string, number>
  loginActivity: {
    date: string
    count: number
  }[]
}

// Two-factor authentication
export interface TwoFactorAuth {
  enabled: boolean
  method: 'totp' | 'sms' | 'email'
  backupCodes: string[]
  lastUsed?: Timestamp
}

export interface TwoFactorSetup {
  secret: string
  qrCode: string
  backupCodes: string[]
}

export interface TwoFactorVerification {
  code: string
  method: 'totp' | 'sms' | 'email' | 'backup'
}

// API key management
export interface ApiKey {
  id: ID
  name: string
  key: string // Only shown once during creation
  permissions: Permission[]
  expiresAt?: Timestamp
  lastUsed?: Timestamp
  createdAt: Timestamp
  isActive: boolean
}

export interface ApiKeyCreateData {
  name: string
  permissions: Permission[]
  expiresAt?: Timestamp
}

// OAuth provider
export interface OAuthProvider {
  id: string
  name: string
  enabled: boolean
  clientId: string
  scopes: string[]
  config: Record<string, any>
}

// Social login
export interface SocialLoginData {
  provider: string
  code: string
  state?: string
  redirectUri: string
}

// User export data
export interface UserExportData {
  user: User
  activities: UserActivity[]
  sessions: UserSession[]
  preferences: UserPreferences
  exportedAt: Timestamp
}

// User import data
export interface UserImportData {
  email: Email
  firstName: string
  lastName: string
  role?: string
  organizationId?: ID
  sendInvite?: boolean
}

// Bulk user operations
export interface BulkUserOperation {
  userIds: ID[]
  operation: 'activate' | 'deactivate' | 'delete' | 'changeRole' | 'changeOrganization'
  data?: Record<string, any>
}

export interface BulkUserResult {
  success: ID[]
  failed: {
    userId: ID
    error: string
  }[]
  total: number
}

// User audit
export interface UserAudit {
  id: ID
  userId: ID
  action: string
  changes: Record<string, {
    old: any
    new: any
  }>
  performedBy: ID
  timestamp: Timestamp
  ipAddress?: string
  userAgent?: string
}

// User notification
export interface UserNotification {
  id: ID
  userId: ID
  type: string
  title: string
  message: string
  data?: Record<string, any>
  read: boolean
  readAt?: Timestamp
  createdAt: Timestamp
  expiresAt?: Timestamp
}

// User team/group
export interface UserTeam {
  id: ID
  name: string
  description?: string
  members: UserTeamMember[]
  organizationId: ID
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}

export interface UserTeamMember {
  userId: ID
  user: User
  role: 'admin' | 'member'
  joinedAt: Timestamp
}

// User permission check
export interface PermissionCheck {
  permission: Permission
  resource?: {
    type: string
    id: ID
  }
  context?: Record<string, any>
}

export interface PermissionResult {
  allowed: boolean
  reason?: string
}

// User context (for components)
export interface UserContext {
  user: User | null
  permissions: Permission[]
  hasPermission: (permission: Permission, resource?: any) => boolean
  isInRole: (role: string) => boolean
  isInOrganization: (organizationId: ID) => boolean
  switchOrganization: (organizationId: ID) => Promise<void>
  updateProfile: (data: ProfileUpdateData) => Promise<void>
  changePassword: (data: PasswordChangeData) => Promise<void>
  logout: () => Promise<void>
}
