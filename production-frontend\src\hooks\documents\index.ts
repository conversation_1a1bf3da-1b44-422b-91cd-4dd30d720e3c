/**
 * Documents Hooks Index
 * Centralized exports for all document-related hooks
 */

import { useState, useEffect } from 'react'

// Main documents hooks
export {
  useCreateDocument,
  useUpdateDocument,
  useDeleteDocument,
  useProcessDocument,
  useDownloadDocument,
  useProjectDocuments
} from '../documents'

// Document sharing - only export non-conflicting items
export {
  useDocumentSharing,
  useUpdateDocumentShare,
  useRevokeDocumentShare
} from './useDocumentSharing'

// Document versions - only export non-conflicting items
export {
  useDocumentVersionsWithActions
} from './useDocumentVersions'

// Document processing hook
export const useDocumentProcessing = (documentId: string) => {
  const [data, setData] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const startProcessing = async (options?: any) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/documents/${documentId}/process`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(options || {})
      })

      if (!response.ok) {
        throw new Error(`Processing failed: ${response.statusText}`)
      }

      const result = await response.json()
      setData(result)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const stopProcessing = async () => {
    try {
      await fetch(`/documents/${documentId}/process/stop`, {
        method: 'POST'
      })
    } catch (err: any) {
      setError(err.message)
    }
  }

  return { data, isLoading, error, startProcessing, stopProcessing }
}

// Document analytics hook
export const useDocumentAnalytics = (documentId: string) => {
  const [data, setData] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (!documentId) return

    const loadAnalytics = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/documents/${documentId}/analytics`)

        if (!response.ok) {
          throw new Error(`Analytics failed: ${response.statusText}`)
        }

        const result = await response.json()
        setData(result.analytics)
      } catch (err: any) {
        setError(err.message)
      } finally {
        setIsLoading(false)
      }
    }

    loadAnalytics()
  }, [documentId])

  return { data, isLoading, error }
}

// Document comments hook
export const useDocumentComments = (documentId: string) => {
  const [data, setData] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const loadComments = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/documents/${documentId}/comments`)

      if (!response.ok) {
        throw new Error(`Comments failed: ${response.statusText}`)
      }

      const comments = await response.json()
      setData(comments.data || [])
    } catch (err: any) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const addComment = async (content: string) => {
    try {
      const response = await fetch(`/documents/${documentId}/comments/create`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content })
      })

      if (!response.ok) {
        throw new Error(`Add comment failed: ${response.statusText}`)
      }

      await loadComments()
    } catch (err: any) {
      setError(err.message)
    }
  }

  const updateComment = async (commentId: string, content: string) => {
    try {
      const response = await fetch(`/documents/${documentId}/comments/${commentId}/update`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content })
      })

      if (!response.ok) {
        throw new Error(`Update comment failed: ${response.statusText}`)
      }

      await loadComments()
    } catch (err: any) {
      setError(err.message)
    }
  }

  const deleteComment = async (commentId: string) => {
    try {
      const response = await fetch(`/documents/${documentId}/comments/${commentId}/delete`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error(`Delete comment failed: ${response.statusText}`)
      }

      await loadComments()
    } catch (err: any) {
      setError(err.message)
    }
  }

  useEffect(() => {
    if (documentId) {
      loadComments()
    }
  }, [documentId])

  return { data, isLoading, error, addComment, updateComment, deleteComment }
}

// Document collaboration hook
export const useDocumentCollaboration = (documentId: string) => {
  const [data, setData] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const startSession = async (options?: any) => {
    try {
      setIsLoading(true)
      setError(null)

      const response = await fetch(`/documents/${documentId}/collaboration/start`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(options || {})
      })

      if (!response.ok) {
        throw new Error(`Start session failed: ${response.statusText}`)
      }

      const session = await response.json()
      setData(session)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const endSession = async () => {
    try {
      await fetch(`/documents/${documentId}/collaboration/end`, {
        method: 'POST'
      })
      setData(null)
    } catch (err: any) {
      setError(err.message)
    }
  }

  return { data, isLoading, error, startSession, endSession }
}
