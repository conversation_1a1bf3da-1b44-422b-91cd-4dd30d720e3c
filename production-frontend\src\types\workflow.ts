/**
 * Workflow Automation Types
 */

import type { ID, Timestamp, User, Organization, Document, Template } from './index'

// Core workflow interface
export interface Workflow {
  id: ID
  name: string
  description?: string
  category: WorkflowCategory
  status: WorkflowStatus
  version: number
  versions: WorkflowVersion[]
  definition: WorkflowDefinition
  triggers: WorkflowTrigger[]
  variables: WorkflowVariable[]
  settings: WorkflowSettings
  permissions: WorkflowPermission[]
  analytics: WorkflowAnalytics
  organizationId: ID
  organization: Organization
  createdBy: ID
  creator: User
  isPublic: boolean
  isTemplate: boolean
  tags: string[]
  createdAt: Timestamp
  updatedAt: Timestamp
  publishedAt?: Timestamp
  lastExecuted?: Timestamp

  // Additional properties expected by components
  steps?: WorkflowStep[]
  currentStepId?: string
  documentId?: string
}

// Workflow status
export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  PAUSED = 'paused',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated',
  // Legacy aliases for backward compatibility
  COMPLETED = 'completed',
  CANCELED = 'canceled'
}

// Workflow category
export interface WorkflowCategory {
  id: ID
  name: string
  description?: string
  icon?: string
  color?: string
  parentId?: ID
  children?: WorkflowCategory[]
  workflowCount: number
}

// Workflow version
export interface WorkflowVersion {
  id: ID
  version: number
  name?: string
  description?: string
  changes: string[]
  definition: WorkflowDefinition
  createdBy: ID
  createdAt: Timestamp
  isActive: boolean
}

// Workflow definition
export interface WorkflowDefinition {
  steps: WorkflowStep[]
  connections: WorkflowConnection[]
  layout: WorkflowLayout
  metadata: WorkflowMetadata
}

// Workflow step status
export enum WorkflowStepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
  CANCELLED = 'cancelled'
}

// Workflow step
export interface WorkflowStep {
  id: string
  type: WorkflowStepType
  name: string
  description?: string
  config: WorkflowStepConfig
  position: WorkflowPosition
  timeout?: number
  retries?: number
  onError?: WorkflowErrorHandling
  conditions?: WorkflowCondition[]
  variables?: WorkflowStepVariable[]
  // Additional properties expected by components
  status?: WorkflowStepStatus
  order?: number
  assigneeId?: ID
  dueDate?: Timestamp
  completedAt?: Timestamp
}

// Workflow step types
export type WorkflowStepType = 
  | 'start'
  | 'end'
  | 'task'
  | 'approval'
  | 'notification'
  | 'delay'
  | 'condition'
  | 'loop'
  | 'parallel'
  | 'merge'
  | 'api_call'
  | 'email'
  | 'webhook'
  | 'document_process'
  | 'template_fill'
  | 'data_transform'
  | 'integration'
  | 'custom'

// Workflow step configuration
export interface WorkflowStepConfig {
  // Task step
  assignees?: ID[]
  dueDate?: string // relative or absolute
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  form?: {
    templateId?: ID
    fields?: any[]
  }
  
  // Approval step
  approvers?: ID[]
  approvalType?: 'any' | 'all' | 'majority'
  escalation?: WorkflowEscalation
  
  // Notification step
  recipients?: ID[]
  template?: string
  channels?: ('email' | 'sms' | 'push' | 'slack')[]
  
  // Delay step
  duration?: number
  unit?: 'minutes' | 'hours' | 'days' | 'weeks'
  
  // Condition step
  conditions?: WorkflowCondition[]
  
  // Loop step
  collection?: string
  itemVariable?: string
  maxIterations?: number
  
  // API call step
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  url?: string
  headers?: Record<string, string>
  requestBody?: any

  // Email step
  to?: string[]
  cc?: string[]
  bcc?: string[]
  subject?: string
  emailBody?: string
  attachments?: string[]
  
  // Webhook step
  webhookUrl?: string
  secret?: string
  
  // Document process step
  documentId?: string
  operations?: string[]
  
  // Template fill step
  templateId?: ID
  data?: Record<string, any>
  
  // Data transform step
  transformations?: WorkflowTransformation[]
  
  // Integration step
  integrationType?: string
  integrationConfig?: Record<string, any>
  
  // Custom step
  customCode?: string
  customConfig?: Record<string, any>
}

// Workflow escalation
export interface WorkflowEscalation {
  enabled: boolean
  delay: number
  unit: 'minutes' | 'hours' | 'days'
  escalateTo: ID[]
  maxLevels: number
}

// Workflow condition
export interface WorkflowCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater' | 'less' | 'greater_equal' | 'less_equal' | 'empty' | 'not_empty' | 'in' | 'not_in'
  value?: any
  logic?: 'and' | 'or'
}

// Workflow transformation
export interface WorkflowTransformation {
  type: 'map' | 'filter' | 'reduce' | 'sort' | 'group' | 'join' | 'split' | 'format'
  source: string
  target: string
  config?: Record<string, any>
}

// Workflow connection
export interface WorkflowConnection {
  id: string
  sourceStepId: string
  targetStepId: string
  condition?: WorkflowCondition[]
  label?: string
}

// Workflow position (for visual editor)
export interface WorkflowPosition {
  x: number
  y: number
  width?: number
  height?: number
}

// Workflow layout
export interface WorkflowLayout {
  zoom: number
  pan: { x: number; y: number }
  grid: boolean
  snapToGrid: boolean
  gridSize: number
}

// Workflow metadata
export interface WorkflowMetadata {
  estimatedDuration?: number
  complexity?: 'simple' | 'medium' | 'complex'
  category?: string
  keywords?: string[]
  documentation?: string
  changelog?: WorkflowChange[]
}

export interface WorkflowChange {
  version: number
  date: Timestamp
  author: ID
  description: string
  changes: string[]
}

// Workflow trigger
export interface WorkflowTrigger {
  id: string
  type: WorkflowTriggerType
  name: string
  enabled: boolean
  config: WorkflowTriggerConfig
  conditions?: WorkflowCondition[]
}

// Workflow trigger types
export type WorkflowTriggerType = 
  | 'manual'
  | 'schedule'
  | 'webhook'
  | 'document_upload'
  | 'document_processed'
  | 'form_submit'
  | 'approval_complete'
  | 'task_complete'
  | 'email_received'
  | 'api_call'
  | 'integration_event'
  | 'custom'

// Workflow trigger configuration
export interface WorkflowTriggerConfig {
  // Schedule trigger
  cron?: string
  timezone?: string
  
  // Webhook trigger
  webhookUrl?: string
  secret?: string
  
  // Document triggers
  documentTypes?: string[]
  projectId?: ID
  
  // Form trigger
  templateId?: ID
  
  // Email trigger
  emailFilters?: {
    from?: string[]
    subject?: string
    body?: string
  }
  
  // API trigger
  endpoint?: string
  method?: string
  
  // Integration trigger
  integrationType?: string
  eventType?: string
  
  // Custom trigger
  customConfig?: Record<string, any>
}

// Workflow variable
export interface WorkflowVariable {
  name: string
  type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array'
  value?: any
  description?: string
  required?: boolean
  scope: 'global' | 'step' | 'execution'
}

// Workflow step variable
export interface WorkflowStepVariable {
  name: string
  source: 'input' | 'output' | 'config' | 'context'
  mapping?: string
}

// Workflow error handling
export interface WorkflowErrorHandling {
  strategy: 'stop' | 'continue' | 'retry' | 'escalate' | 'custom'
  retries?: number
  retryDelay?: number
  escalateTo?: ID[]
  customHandler?: string
}

// Workflow settings
export interface WorkflowSettings {
  maxConcurrentExecutions: number
  executionTimeout: number
  retentionDays: number
  enableLogging: boolean
  enableMetrics: boolean
  enableNotifications: boolean
  notificationSettings: WorkflowNotificationSettings
  securitySettings: WorkflowSecuritySettings
}

export interface WorkflowNotificationSettings {
  onStart: boolean
  onComplete: boolean
  onError: boolean
  onTimeout: boolean
  recipients: ID[]
  channels: ('email' | 'sms' | 'push' | 'slack')[]
}

export interface WorkflowSecuritySettings {
  requireApproval: boolean
  approvers: ID[]
  allowedUsers: ID[]
  allowedRoles: string[]
  ipWhitelist: string[]
  encryptData: boolean
}

// Workflow permissions
export interface WorkflowPermission {
  userId: ID
  user: User
  permission: 'view' | 'execute' | 'edit' | 'admin'
  grantedBy: ID
  grantedAt: Timestamp
  expiresAt?: Timestamp
}

// Workflow execution
export interface WorkflowExecution {
  id: ID
  workflowId: ID
  workflow: Workflow
  triggerId?: string
  trigger?: WorkflowTrigger
  status: WorkflowExecutionStatus
  priority: 'low' | 'medium' | 'high' | 'urgent'
  input?: any
  output?: any
  context: WorkflowExecutionContext
  steps: WorkflowStepExecution[]
  variables: Record<string, any>
  metrics: WorkflowExecutionMetrics
  error?: WorkflowExecutionError
  startedAt: Timestamp
  completedAt?: Timestamp
  createdBy?: ID
}

// Workflow execution status
export type WorkflowExecutionStatus = 
  | 'pending'
  | 'running'
  | 'waiting'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'timeout'

// Workflow execution context
export interface WorkflowExecutionContext {
  userId?: ID
  organizationId: ID
  projectId?: ID
  documentId?: ID
  templateId?: ID
  source?: string
  metadata?: Record<string, any>
}

// Workflow step execution
export interface WorkflowStepExecution {
  id: ID
  stepId: string
  step: WorkflowStep
  status: WorkflowExecutionStatus
  input?: any
  output?: any
  error?: WorkflowExecutionError
  assignedTo?: ID[]
  startedAt?: Timestamp
  completedAt?: Timestamp
  duration?: number
  retryCount: number
  logs: WorkflowExecutionLog[]
}

// Workflow execution error
export interface WorkflowExecutionError {
  code: string
  message: string
  details?: any
  stackTrace?: string
  timestamp: Timestamp
}

// Workflow execution log
export interface WorkflowExecutionLog {
  id: ID
  level: 'debug' | 'info' | 'warn' | 'error'
  message: string
  data?: any
  timestamp: Timestamp
}

// Workflow execution metrics
export interface WorkflowExecutionMetrics {
  duration: number
  stepsCompleted: number
  stepsTotal: number
  retryCount: number
  errorCount: number
  resourceUsage?: {
    cpu?: number
    memory?: number
    network?: number
  }
}

// Workflow analytics
export interface WorkflowAnalytics {
  overview: WorkflowAnalyticsOverview
  performance: WorkflowAnalyticsPerformance
  usage: WorkflowAnalyticsUsage
  errors: WorkflowAnalyticsErrors
}

export interface WorkflowAnalyticsOverview {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageDuration: number
  successRate: number
  lastExecution?: Timestamp
}

export interface WorkflowAnalyticsPerformance {
  executionTrend: { date: string; count: number; avgDuration: number }[]
  stepPerformance: { stepId: string; stepName: string; avgDuration: number; successRate: number }[]
  bottlenecks: { stepId: string; stepName: string; avgWaitTime: number }[]
  resourceUsage: { date: string; cpu: number; memory: number }[]
}

export interface WorkflowAnalyticsUsage {
  byUser: { userId: ID; userName: string; count: number }[]
  byTrigger: { triggerId: string; triggerName: string; count: number }[]
  byTime: { hour: number; count: number }[]
  byDay: { day: string; count: number }[]
}

export interface WorkflowAnalyticsErrors {
  errorTypes: { type: string; count: number; percentage: number }[]
  errorTrend: { date: string; count: number }[]
  failurePoints: { stepId: string; stepName: string; failureRate: number }[]
  commonErrors: { message: string; count: number }[]
}

// Workflow operations
export interface CreateWorkflowRequest {
  name: string
  description?: string
  categoryId: ID
  definition: WorkflowDefinition
  triggers?: Omit<WorkflowTrigger, 'id'>[]
  variables?: WorkflowVariable[]
  settings?: Partial<WorkflowSettings>
  isPublic?: boolean
  tags?: string[]
}

export interface UpdateWorkflowRequest {
  name?: string
  description?: string
  categoryId?: ID
  definition?: WorkflowDefinition
  triggers?: WorkflowTrigger[]
  variables?: WorkflowVariable[]
  settings?: Partial<WorkflowSettings>
  status?: WorkflowStatus
  isPublic?: boolean
  tags?: string[]
}

export interface ExecuteWorkflowRequest {
  workflowId: ID
  triggerId?: string
  input?: any
  context?: Partial<WorkflowExecutionContext>
  priority?: 'low' | 'medium' | 'high' | 'urgent'
}

export interface WorkflowSearchQuery {
  query?: string
  categoryId?: ID
  status?: WorkflowStatus[]
  isPublic?: boolean
  organizationId?: ID
  createdBy?: ID
  tags?: string[]
  dateRange?: {
    start: Timestamp
    end: Timestamp
  }
  hasExecutions?: boolean
  complexity?: string[]
}

export interface WorkflowSearchResult {
  workflows: Workflow[]
  total: number
  facets: {
    categories: { categoryId: ID; categoryName: string; count: number }[]
    statuses: { status: WorkflowStatus; count: number }[]
    tags: { tag: string; count: number }[]
    organizations: { organizationId: ID; organizationName: string; count: number }[]
  }
  suggestions: string[]
}

// Workflow template
export interface WorkflowTemplate {
  id: ID
  name: string
  description?: string
  category: WorkflowCategory
  definition: WorkflowDefinition
  variables: WorkflowVariable[]
  settings: WorkflowSettings
  isPublic: boolean
  usageCount: number
  rating: number
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Workflow integration
export interface WorkflowIntegration {
  id: ID
  workflowId: ID
  type: string
  name: string
  config: Record<string, any>
  enabled: boolean
  lastSync?: Timestamp
  syncStatus: 'success' | 'error' | 'pending'
  syncError?: string
  createdBy: ID
  createdAt: Timestamp
  updatedAt: Timestamp
}
