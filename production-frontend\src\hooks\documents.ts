/**
 * Documents Hooks
 * React hooks for document management using Zustand store
 */

import { useState, useCallback, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useDocumentStore } from '@/stores/document-store'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'
import type { Document } from '@/types/backend'

export interface CreateDocumentRequest {
  name: string
  description?: string
  projectId?: string
  organizationId: string
  file?: File
  content?: any
  metadata?: Record<string, any>
}

export interface UpdateDocumentRequest {
  name?: string
  description?: string
  content?: any
  metadata?: Record<string, any>
}

/**
 * Hook to get all documents
 */
export function useDocuments(params?: {
  organizationId?: string
  projectId?: string
  page?: number
  pageSize?: number
  search?: string
}) {
  const documents = useDocumentStore(state => state.documents)
  const loading = useDocumentStore(state => state.loading)
  const error = useDocumentStore(state => state.error)
  const fetchDocuments = useDocumentStore(state => state.fetchDocuments)
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchDocuments(params, params?.page, params?.pageSize)
    } catch (error: any) {
      toast({
        title: 'Error fetching documents',
        description: error.message || 'Failed to fetch documents',
        variant: 'destructive',
      })
    }
  }, [fetchDocuments, params, toast])

  return {
    data: documents,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get a specific document
 */
export function useDocument(documentId: string) {
  const selectedDocument = useDocumentStore(state => state.selectedDocument)
  const loading = useDocumentStore(state => state.loading)
  const error = useDocumentStore(state => state.error)
  const fetchDocument = useDocumentStore(state => state.fetchDocument)
  const { toast } = useToast()

  const document = selectedDocument?.id === documentId ? selectedDocument : null

  const refetch = useCallback(async () => {
    if (!documentId) return
    try {
      await fetchDocument(documentId, true)
    } catch (error: any) {
      toast({
        title: 'Error fetching document',
        description: error.message || 'Failed to fetch document',
        variant: 'destructive',
      })
    }
  }, [fetchDocument, documentId, toast])

  return {
    data: document,
    isLoading: loading,
    error,
    refetch,
    enabled: !!documentId
  }
}

/**
 * Hook to get document versions
 */
export function useDocumentVersions(documentId: string) {
  return useQuery({
    queryKey: ['document', documentId, 'versions'],
    queryFn: async () => {
      return await backendApiClient.request<any[]>(`/documents/${documentId}/versions`)
    },
    enabled: !!documentId,
  })
}

/**
 * Hook to create a new document
 */
export function useCreateDocument() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: CreateDocumentRequest) => {
      if (data.file) {
        return await backendApiClient.uploadDocument(data.file, {
          name: data.name,
          description: data.description,
          projectId: data.projectId,
        })
      } else {
        // For text documents, use a different approach since createDocument doesn't exist
        const formData = new FormData()
        const blob = new Blob([data.content || ''], { type: 'text/plain' })
        formData.append('file', blob, data.name || 'document.txt')

        return await backendApiClient.uploadDocument(blob as File, {
          name: data.name,
          description: data.description,
          projectId: data.projectId,
        })
      }
    },
    onSuccess: (document) => {
      const queryClient = useQueryClient()
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      if (document.projectId) {
        queryClient.invalidateQueries({ queryKey: ['project', document.projectId, 'documents'] })
      }
      toast({
        title: 'Document created',
        description: `Document "${document.name}" has been created successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error creating document',
        description: 'There was a problem creating the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to update a document
 */
export function useUpdateDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, data }: { documentId: string; data: UpdateDocumentRequest }) => {
      return await backendApiClient.updateDocument(documentId, data as Partial<Document>)
    },
    onSuccess: (document) => {
      queryClient.invalidateQueries({ queryKey: ['document', document.id] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      if (document.projectId) {
        queryClient.invalidateQueries({ queryKey: ['project', document.projectId, 'documents'] })
      }
      toast({
        title: 'Document updated',
        description: `Document "${document.name}" has been updated successfully.`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error updating document',
        description: 'There was a problem updating the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to delete a document
 */
export function useDeleteDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (documentId: string) => {
      await backendApiClient.deleteDocument(documentId)
      return documentId
    },
    onSuccess: (documentId) => {
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      queryClient.removeQueries({ queryKey: ['document', documentId] })
      toast({
        title: 'Document deleted',
        description: 'The document has been deleted successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error deleting document',
        description: 'There was a problem deleting the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to share a document
 */
export function useShareDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      documentId, 
      shareData 
    }: { 
      documentId: string
      shareData: {
        emails?: string[]
        permissions?: string[]
        expiresAt?: string
        message?: string
      }
    }) => {
      return await backendApiClient.request(`/documents/${documentId}/share`, {
        method: 'POST',
        body: JSON.stringify(shareData)
      })
    },
    onSuccess: (_result, { documentId }) => {
      queryClient.invalidateQueries({ queryKey: ['document', documentId] })
      toast({
        title: 'Document shared',
        description: 'The document has been shared successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error sharing document',
        description: 'There was a problem sharing the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to process a document
 */
export function useProcessDocument() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ 
      documentId, 
      processingOptions 
    }: { 
      documentId: string
      processingOptions?: Record<string, any>
    }) => {
      return await backendApiClient.request(`/documents/${documentId}/process`, {
        method: 'POST',
        body: JSON.stringify(processingOptions || {})
      })
    },
    onSuccess: (_result, { documentId }) => {
      queryClient.invalidateQueries({ queryKey: ['document', documentId] })
      toast({
        title: 'Document processing started',
        description: 'The document is being processed. You will be notified when it\'s complete.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error processing document',
        description: 'There was a problem processing the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to download a document
 */
export function useDownloadDocument() {
  const { toast } = useToast()

  return useMutation({
    mutationFn: async ({ documentId, version }: { documentId: string; version?: string }) => {
      const url = version 
        ? `/documents/${documentId}/download?version=${version}`
        : `/documents/${documentId}/download`
      
      return await backendApiClient.request(url, {
        method: 'GET'
      })
    },
    onSuccess: (result) => {
      // Handle download URL or blob
      if (result.downloadUrl) {
        window.open(result.downloadUrl, '_blank')
      }
    },
    onError: (_error) => {
      toast({
        title: 'Error downloading document',
        description: 'There was a problem downloading the document. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook for managing project documents
 */
export function useProjectDocuments(projectId: string) {
  const [documents, setDocuments] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadDocuments = useCallback(async () => {
    if (!projectId) return

    setIsLoading(true)
    setError(null)

    try {
      // Fetch real project documents from API
      const response = await fetch(`/projects/${projectId}/documents`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch project documents: ${response.statusText}`);
      }

      const data = await response.json();
      setDocuments(data.data || []);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load project documents'
      setError(errorMessage)
      console.error('Error loading project documents:', err)
    } finally {
      setIsLoading(false)
    }
  }, [projectId])

  const downloadDocument = useMutation({
    mutationFn: async ({ documentId, version }: { documentId: string; version?: string }) => {
      try {
        const url = version
          ? `/documents/${documentId}/download?version=${version}`
          : `/documents/${documentId}/download`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to download document: ${response.statusText}`);
        }

        // Get the blob and create download link
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);

        // Get filename from response headers or use default
        const contentDisposition = response.headers.get('content-disposition');
        const filename = contentDisposition
          ? contentDisposition.split('filename=')[1]?.replace(/"/g, '')
          : `document-${documentId}.pdf`;

        // Create and trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up
        window.URL.revokeObjectURL(downloadUrl);

        return { success: true, filename };
      } catch (error) {
        console.error('Error downloading document:', error);
        throw error;
      }
    },
    onError: (error: any) => {
      console.error('Error downloading document:', error)
    }
  })

  useEffect(() => {
    loadDocuments()
  }, [loadDocuments])

  return {
    documents,
    isLoading,
    error,
    downloadDocument,
    refresh: loadDocuments,
  }
}
