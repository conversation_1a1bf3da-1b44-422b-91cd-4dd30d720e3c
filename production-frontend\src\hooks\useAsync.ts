import { useState, useEffect, useCallback, useRef } from 'react'

/**
 * Async Hook
 * Manages async operations with loading, error, and data states
 */

export interface UseAsyncOptions<T> {
  immediate?: boolean
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
}

export interface UseAsyncResult<T> {
  data: T | null
  loading: boolean
  error: Error | null
  execute: (...args: any[]) => Promise<T | undefined>
  reset: () => void
}

export function useAsync<T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: UseAsyncOptions<T> = {}
): UseAsyncResult<T> {
  const { immediate = false, onSuccess, onError } = options
  
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(immediate)
  const [error, setError] = useState<Error | null>(null)
  
  const mountedRef = useRef(true)
  const abortControllerRef = useRef<AbortController>()

  const execute = useCallback(
    async (...args: any[]): Promise<T | undefined> => {
      // Cancel previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      
      // Create new abort controller
      abortControllerRef.current = new AbortController()
      
      setLoading(true)
      setError(null)

      try {
        const result = await asyncFunction(...args)
        
        if (mountedRef.current && !abortControllerRef.current.signal.aborted) {
          setData(result)
          setLoading(false)
          onSuccess?.(result)
          return result
        }
      } catch (err) {
        if (mountedRef.current && !abortControllerRef.current.signal.aborted) {
          const error = err instanceof Error ? err : new Error(String(err))
          setError(error)
          setLoading(false)
          onError?.(error)
          throw error
        }
      }
    },
    [asyncFunction, onSuccess, onError]
  )

  const reset = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setData(null)
    setLoading(false)
    setError(null)
  }, [])

  useEffect(() => {
    if (immediate) {
      execute()
    }
  }, [execute, immediate])

  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  return {
    data,
    loading,
    error,
    execute,
    reset,
  }
}
