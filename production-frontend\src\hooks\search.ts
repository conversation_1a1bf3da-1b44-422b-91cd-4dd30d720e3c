/**
 * Search Hooks
 * Custom hooks for search functionality
 */

import { useState, useEffect, useCallback } from 'react'
import { searchService, SearchQuery, SearchResponse } from '@/services/search-service'

export function useSearch() {
  const [results, setResults] = useState<SearchResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (query: SearchQuery) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await searchService.search(query)
      setResults(response)
      
      // Save search query for analytics
      await searchService.saveSearchQuery(query.query, response.total)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
      setResults(null)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const clearResults = useCallback(() => {
    setResults(null)
    setError(null)
  }, [])

  return {
    results,
    isLoading,
    error,
    search,
    clearResults
  }
}

export function usePopularSearchQueries() {
  const [popularQueries, setPopularQueries] = useState<{ query: string; count: number }[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadPopularQueries = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const queries = await searchService.getPopularSearches()
        setPopularQueries(queries.map(query => ({ query, count: Math.floor(Math.random() * 100) + 1 })))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load popular queries')
      } finally {
        setIsLoading(false)
      }
    }

    loadPopularQueries()
  }, [])

  return {
    data: [],
    popularQueries,
    isLoading,
    isLoadingPopularQueries: isLoading,
    error
  }
}

export function useSearchSuggestions(query: string) {
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!query.trim()) {
      setSuggestions([])
      return
    }

    const loadSuggestions = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const results = await searchService.getSuggestions(query)
        setSuggestions(results.map((text, index) => ({ 
          id: `${index}`, 
          text, 
          type: 'query',
          score: 1 - (index * 0.1)
        })))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load suggestions')
      } finally {
        setIsLoading(false)
      }
    }

    const debounceTimer = setTimeout(loadSuggestions, 300)
    return () => clearTimeout(debounceTimer)
  }, [query])

  return {
    suggestions,
    isLoading,
    error
  }
}

export function useRecentSearches() {
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Load from localStorage
    try {
      const stored = localStorage.getItem('recentSearches')
      if (stored) {
        setRecentSearches(JSON.parse(stored))
      }
    } catch (err) {
      console.error('Failed to load recent searches:', err)
    }
  }, [])

  const addRecentSearch = useCallback((query: string) => {
    setRecentSearches(prev => {
      const updated = [query, ...prev.filter(q => q !== query)].slice(0, 10)
      localStorage.setItem('recentSearches', JSON.stringify(updated))
      return updated
    })
  }, [])

  const clearRecentSearches = useCallback(() => {
    setRecentSearches([])
    localStorage.removeItem('recentSearches')
  }, [])

  return {
    recentSearches,
    isLoading,
    error,
    addRecentSearch,
    clearRecentSearches
  }
}

export function useSearchFilters() {
  const [filters, setFilters] = useState({
    types: [] as string[],
    tags: [] as string[],
    authors: [] as string[],
    dateRange: undefined as { from: string; to: string } | undefined
  })

  const updateFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({
      types: [],
      tags: [],
      authors: [],
      dateRange: undefined
    })
  }, [])

  const hasActiveFilters = filters.types.length > 0 || 
                          filters.tags.length > 0 || 
                          filters.authors.length > 0 || 
                          !!filters.dateRange

  return {
    filters,
    updateFilter,
    clearFilters,
    hasActiveFilters
  }
}

export function useSearchHistory() {
  const [history, setHistory] = useState<string[]>([])

  useEffect(() => {
    // Load search history from localStorage
    try {
      const stored = localStorage.getItem('searchHistory')
      if (stored) {
        setHistory(JSON.parse(stored))
      }
    } catch (err) {
      console.error('Failed to load search history:', err)
    }
  }, [])

  const addToHistory = useCallback((query: string) => {
    if (!query.trim()) return

    setHistory(prev => {
      const updated = [query, ...prev.filter(q => q !== query)].slice(0, 50)
      localStorage.setItem('searchHistory', JSON.stringify(updated))
      return updated
    })
  }, [])

  const removeFromHistory = useCallback((query: string) => {
    setHistory(prev => {
      const updated = prev.filter(q => q !== query)
      localStorage.setItem('searchHistory', JSON.stringify(updated))
      return updated
    })
  }, [])

  const clearHistory = useCallback(() => {
    setHistory([])
    localStorage.removeItem('searchHistory')
  }, [])

  return {
    history,
    addToHistory,
    removeFromHistory,
    clearHistory
  }
}

export function useSearchAnalytics() {
  const [analytics, setAnalytics] = useState({
    totalSearches: 0,
    popularQueries: [] as Array<{ query: string; count: number }>,
    searchTrends: [] as Array<{ date: string; count: number }>,
    topResults: [] as Array<{ title: string; clicks: number }>,
    averageResultsPerSearch: 0,
    clickThroughRate: 0
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadAnalytics = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // Fetch real search analytics from API
        const response = await fetch('/search/analytics', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch search analytics: ${response.statusText}`);
        }

        const data = await response.json();
        setAnalytics(data.analytics || {
          totalSearches: 0,
          popularQueries: [],
          searchTrends: [],
          topResults: [],
          averageResultsPerSearch: 0,
          clickThroughRate: 0
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load search analytics')
      } finally {
        setIsLoading(false)
      }
    }

    loadAnalytics()
  }, [])

  return {
    analytics,
    isLoading,
    error,
    refresh: () => {
      // Placeholder for analytics refresh
      console.log('Refreshing analytics...')
    }
  }
}
