/**
 * AI Models Types
 * Type definitions for AI models and related operations
 */

export interface AIModel {
  id: string
  name: string
  description?: string
  type: AIModelType
  provider: AIProvider
  version: string
  capabilities: AICapability[]
  isActive: boolean
  configuration: AIModelConfiguration
  metrics?: AIModelMetrics
  createdBy: string
  createdAt: string
  updatedAt: string
  organizationId: string
}

export enum AIModelType {
  TEXT_GENERATION = 'text_generation',
  TEXT_CLASSIFICATION = 'text_classification',
  TEXT_SUMMARIZATION = 'text_summarization',
  QUESTION_ANSWERING = 'question_answering',
  SENTIMENT_ANALYSIS = 'sentiment_analysis',
  NAMED_ENTITY_RECOGNITION = 'named_entity_recognition',
  IMAGE_CLASSIFICATION = 'image_classification',
  OBJECT_DETECTION = 'object_detection',
  IMAGE_SEGMENTATION = 'image_segmentation',
  OCR = 'ocr',
  DOCUMENT_ANALYSIS = 'document_analysis',
  SPEECH_TO_TEXT = 'speech_to_text',
  TEXT_TO_SPEECH = 'text_to_speech',
  TRANSLATION = 'translation',
  EMBEDDING = 'embedding',
  CHAT_COMPLETION = 'chat_completion',
  CODE_GENERATION = 'code_generation',
  CUSTOM = 'custom'
}

export enum AIProvider {
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic',
  AZURE_OPENAI = 'azure_openai',
  AZURE_AI = 'azure_ai',
  DEEPSEEK = 'deepseek',
  LLAMA = 'llama',
  COHERE = 'cohere',
  HUGGING_FACE = 'hugging_face',
  GOOGLE = 'google',
  CUSTOM = 'custom'
}

export enum AICapability {
  TEXT_INPUT = 'text_input',
  IMAGE_INPUT = 'image_input',
  AUDIO_INPUT = 'audio_input',
  VIDEO_INPUT = 'video_input',
  DOCUMENT_INPUT = 'document_input',
  STRUCTURED_OUTPUT = 'structured_output',
  STREAMING = 'streaming',
  FUNCTION_CALLING = 'function_calling',
  FINE_TUNING = 'fine_tuning',
  REASONING = 'reasoning',
  MULTIMODAL = 'multimodal',
  REAL_TIME = 'real_time'
}

export interface AIModelConfiguration {
  endpoint?: string
  apiKey?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stopSequences?: string[]
  systemPrompt?: string
  customParameters?: Record<string, any>
  rateLimits?: {
    requestsPerMinute: number
    tokensPerMinute: number
    requestsPerDay: number
  }
  retryPolicy?: {
    maxRetries: number
    backoffMultiplier: number
    maxBackoffTime: number
  }
}

export interface AIModelMetrics {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  averageTokensUsed: number
  totalTokensUsed: number
  totalCost: number
  errorRate: number
  lastUsed?: string
  usage: {
    daily: Array<{
      date: string
      requests: number
      tokens: number
      cost: number
    }>
    hourly: Array<{
      hour: string
      requests: number
      tokens: number
      cost: number
    }>
  }
  performance: {
    p50ResponseTime: number
    p95ResponseTime: number
    p99ResponseTime: number
    throughput: number
  }
}

export interface CreateAIModelRequest {
  name: string
  description?: string
  type: AIModelType
  provider: AIProvider
  version: string
  capabilities: AICapability[]
  configuration: AIModelConfiguration
  organizationId: string
  isActive?: boolean
}

export interface UpdateAIModelRequest {
  name?: string
  description?: string
  capabilities?: AICapability[]
  configuration?: Partial<AIModelConfiguration>
  isActive?: boolean
}

export interface AIModelTrainingJob {
  id: string
  modelId: string
  status: TrainingStatus
  datasetId: string
  trainingParameters: {
    epochs: number
    batchSize: number
    learningRate: number
    validationSplit: number
    customParameters?: Record<string, any>
  }
  progress: {
    currentEpoch: number
    totalEpochs: number
    loss: number
    accuracy: number
    validationLoss: number
    validationAccuracy: number
    estimatedTimeRemaining: number
  }
  results?: {
    finalLoss: number
    finalAccuracy: number
    validationLoss: number
    validationAccuracy: number
    trainingTime: number
    modelSize: number
  }
  createdAt: string
  startedAt?: string
  completedAt?: string
  error?: string
}

export enum TrainingStatus {
  PENDING = 'pending',
  PREPARING = 'preparing',
  TRAINING = 'training',
  VALIDATING = 'validating',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface AIModelDeployment {
  id: string
  modelId: string
  version: string
  status: DeploymentStatus
  endpoint: string
  configuration: {
    instanceType: string
    minInstances: number
    maxInstances: number
    autoScaling: boolean
    healthCheckPath: string
  }
  metrics: {
    requestsPerSecond: number
    averageLatency: number
    errorRate: number
    cpuUtilization: number
    memoryUtilization: number
  }
  deployedAt: string
  lastHealthCheck: string
}

export enum DeploymentStatus {
  DEPLOYING = 'deploying',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  FAILED = 'failed',
  UPDATING = 'updating',
  SCALING = 'scaling'
}

export interface AIModelEvaluation {
  id: string
  modelId: string
  datasetId: string
  metrics: {
    accuracy: number
    precision: number
    recall: number
    f1Score: number
    customMetrics?: Record<string, number>
  }
  results: Array<{
    input: any
    expectedOutput: any
    actualOutput: any
    score: number
    correct: boolean
  }>
  evaluatedAt: string
  evaluationTime: number
}

export interface AIModelComparison {
  models: Array<{
    modelId: string
    modelName: string
    metrics: AIModelMetrics
    evaluation?: AIModelEvaluation
  }>
  comparison: {
    bestPerformance: string
    mostCostEffective: string
    fastest: string
    recommendations: string[]
  }
  benchmarks: Array<{
    task: string
    results: Record<string, number>
  }>
}

export interface AIModelTemplate {
  id: string
  name: string
  description: string
  type: AIModelType
  provider: AIProvider
  defaultConfiguration: AIModelConfiguration
  requiredCapabilities: AICapability[]
  isPublic: boolean
  usageCount: number
  rating: number
  tags: string[]
  createdBy: string
  createdAt: string
}

export interface AIModelUsage {
  modelId: string
  organizationId: string
  projectId?: string
  userId: string
  requestId: string
  timestamp: string
  inputTokens: number
  outputTokens: number
  totalTokens: number
  cost: number
  responseTime: number
  success: boolean
  error?: string
  metadata?: Record<string, any>
}

export interface AIModelQuota {
  organizationId: string
  modelId: string
  quotaType: 'requests' | 'tokens' | 'cost'
  limit: number
  used: number
  remaining: number
  resetDate: string
  isExceeded: boolean
}

export interface AIModelAlert {
  id: string
  modelId: string
  type: 'performance' | 'cost' | 'quota' | 'error'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  threshold: number
  currentValue: number
  triggeredAt: string
  acknowledged: boolean
  resolvedAt?: string
}
