"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";

import { useServiceBus } from "@/hooks/infrastructure";

import { MessageStatus, MessageType } from "@/services/service-bus-service";
import { documentService } from "@/services/optimized-document-service";
import {
  FileText,
  CheckCircle,
  AlertCircle,
  Loader2,
  Tag,
  Clock,
  Cpu,
  Bar<PERSON>hart,
  FileSearch,
  Layers,
  Sparkles
} from "lucide-react";

interface DocumentProcessingProps {
  documentId: string;
  documentName: string;
  projectId?: string;
  organizationId?: string;
  onProcessingComplete?: (result: any) => void;
}

export function DocumentProcessing({
  documentId,
  documentName,
  projectId: _projectId,
  organizationId,
  onProcessingComplete
}: DocumentProcessingProps) {
  const { toast } = useToast();
  const [processingOptions, setProcessingOptions] = useState({
    extractText: true,
    extractEntities: true,
    classify: true,
    structureContent: true,
    extractMetadata: true,
    vectorizeDocument: false,
    indexToKnowledgeGraph: false
  });
  const [processingStartTime, setProcessingStartTime] = useState<Date | null>(null);
  const [processingElapsedTime, setProcessingElapsedTime] = useState<number>(0);
  const [processingProgress, setProcessingProgress] = useState<number>(0);
  const [processingStage, setProcessingStage] = useState<string>("");

  // Use the service bus hook for real-time updates
  const { subscribeToMessages } = useServiceBus();
  const [messageId, setMessageId] = useState<string | null>(null);
  const [processingStatus, setProcessingStatus] = useState<MessageStatus | null>(null);
  const [processingResult, setProcessingResult] = useState<any>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Subscribe to document processing result messages
  useEffect(() => {
    if (!documentId) return;

    // Subscribe to document processing results
    const unsubscribe = subscribeToMessages(MessageType.DOCUMENT_PROCESSING_RESULT, (message) => {
      // Check if this message is for our document
      if (message.data?.documentId === documentId) {
        // Update processing status
        setProcessingStatus(message.data.status);

        // Update progress if available
        if (message.data.progress !== undefined) {
          setProcessingProgress(message.data.progress);
        }

        // Update stage if available
        if (message.data.stage) {
          setProcessingStage(message.data.stage);
        }

        // If processing is complete, update the result
        if (message.data.status === MessageStatus.COMPLETED && message.data.result) {
          setProcessingResult(message.data.result);

          toast({
            title: "Document Processed",
            description: `Document "${documentName}" was processed successfully`,
          });

          // Call the callback if provided
          if (onProcessingComplete) {
            onProcessingComplete(message.data.result);
          }
        }

        // If processing failed, update the error
        if (message.data.status === MessageStatus.FAILED) {
          setError(new Error(message.data.error || "Unknown error"));

          toast({
            title: "Processing Failed",
            description: `Processing failed for document "${documentName}": ${message.data.error || "Unknown error"}`,
            variant: "destructive",
          });
        }
      }
    });

    // Clean up subscription on unmount
    return () => {
      unsubscribe();
    };
  }, [documentId, documentName, subscribeToMessages, onProcessingComplete, toast]);

  // Handle processing start
  const handleStartProcessing = async () => {
    try {
      setIsLoading(true);
      setProcessingStartTime(new Date());
      setProcessingProgress(0);
      setProcessingStage("Initializing");
      setProcessingStatus(MessageStatus.QUEUED);
      setError(null);

      // Process the document using the document service
      const result = await documentService.processDocument({
        documentId,
        processingType: 'comprehensive',
        organizationId: organizationId || '',
        options: {
          extractText: processingOptions.extractText,
          extractEntities: processingOptions.extractEntities,
          extractTables: processingOptions.structureContent,
          extractKeyValuePairs: processingOptions.extractMetadata,
          classify: processingOptions.classify,
          vectorize: processingOptions.vectorizeDocument,
          indexToKnowledgeGraph: processingOptions.indexToKnowledgeGraph
        }
      });
      const msgId = result.jobId || result.documentId;

      // Store the message ID for status checking
      setMessageId(msgId);

      toast({
        title: "Processing Started",
        description: `Document "${documentName}" is being processed`,
      });
    } catch (err) {
      console.error("Failed to start processing:", err);
      setError(err instanceof Error ? err : new Error(String(err)));

      toast({
        title: "Processing Failed",
        description: `Failed to start processing for document "${documentName}"`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle processing cancellation
  const handleCancelProcessing = async () => {
    if (!messageId) return;

    try {
      setIsLoading(true);

      // Cancel the processing via Service Bus
      await fetch(`/api/service-bus/cancel/${messageId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      });

      // Update the status
      setProcessingStatus(MessageStatus.CANCELLED);
      setProcessingStage("Processing cancelled");

      toast({
        title: "Processing Cancelled",
        description: "Document processing has been cancelled.",
      });
    } catch (err) {
      console.error("Failed to cancel processing:", err);

      toast({
        title: "Cancellation Failed",
        description: "Failed to cancel document processing.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Update progress based on status
  useEffect(() => {
    if (processingStatus === MessageStatus.QUEUED) {
      setProcessingProgress(10);
      setProcessingStage("Queued for processing");
    } else if (processingStatus === MessageStatus.PROCESSING) {
      // Simulate progress for different stages
      const interval = setInterval(() => {
        setProcessingProgress((prev) => {
          if (prev < 90) {
            const newProgress = prev + 5;

            // Update processing stage based on progress
            if (newProgress < 30) {
              setProcessingStage("Extracting text");
            } else if (newProgress < 50) {
              setProcessingStage("Analyzing content");
            } else if (newProgress < 70) {
              setProcessingStage("Identifying entities");
            } else if (newProgress < 90) {
              setProcessingStage("Finalizing processing");
            }

            return newProgress;
          }
          return prev;
        });
      }, 2000);

      return () => clearInterval(interval);
    } else if (processingStatus === MessageStatus.COMPLETED) {
      setProcessingProgress(100);
      setProcessingStage("Processing complete");
    } else if (processingStatus === MessageStatus.FAILED) {
      setProcessingProgress(0);
      setProcessingStage("Processing failed");
    } else if (processingStatus === MessageStatus.CANCELLED) {
      setProcessingProgress(0);
      setProcessingStage("Processing cancelled");
    }
  }, [processingStatus]);

  // Update elapsed time
  useEffect(() => {
    if (processingStartTime && (processingStatus === MessageStatus.QUEUED || processingStatus === MessageStatus.PROCESSING)) {
      const interval = setInterval(() => {
        const elapsed = Math.floor((new Date().getTime() - processingStartTime.getTime()) / 1000);
        setProcessingElapsedTime(elapsed);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [processingStartTime, processingStatus]);

  // Format elapsed time
  const formatElapsedTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Check status periodically
  useEffect(() => {
    if (messageId && (processingStatus === MessageStatus.QUEUED || processingStatus === MessageStatus.PROCESSING)) {
      const checkStatus = async () => {
        try {
          // Get the message status from the API
          const response = await fetch(`/api/service-bus/status/${messageId}`);

          if (response.ok) {
            const data = await response.json();

            // Update the status
            setProcessingStatus(data.status);

            // Update the result if available
            if (data.result) {
              setProcessingResult(data.result);

              // If processing is complete, call the callback
              if (data.status === MessageStatus.COMPLETED && onProcessingComplete) {
                onProcessingComplete(data.result);
              }
            }
          }
        } catch (err) {
          console.error("Failed to check processing status:", err);
        }
      };

      // Check status immediately
      checkStatus();

      // Set up interval for periodic checks
      const interval = setInterval(checkStatus, 5000);

      return () => clearInterval(interval);
    }
  }, [messageId, processingStatus, onProcessingComplete]);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Cpu className="h-5 w-5" />
          Document Processing
        </CardTitle>
        <CardDescription>
          Process document using AI to extract insights
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Processing Options */}
        {!processingStatus && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Basic Processing</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="extractText"
                      checked={processingOptions.extractText}
                      onCheckedChange={(checked) =>
                        setProcessingOptions(prev => ({ ...prev, extractText: !!checked }))
                      }
                    />
                    <Label htmlFor="extractText" className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Extract Text
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="extractEntities"
                      checked={processingOptions.extractEntities}
                      onCheckedChange={(checked) =>
                        setProcessingOptions(prev => ({ ...prev, extractEntities: !!checked }))
                      }
                    />
                    <Label htmlFor="extractEntities" className="flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Extract Entities
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="classify"
                      checked={processingOptions.classify}
                      onCheckedChange={(checked) =>
                        setProcessingOptions(prev => ({ ...prev, classify: !!checked }))
                      }
                    />
                    <Label htmlFor="classify" className="flex items-center gap-2">
                      <Layers className="h-4 w-4" />
                      Classify Document
                    </Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Advanced Processing</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="structureContent"
                      checked={processingOptions.structureContent}
                      onCheckedChange={(checked) =>
                        setProcessingOptions(prev => ({ ...prev, structureContent: !!checked }))
                      }
                    />
                    <Label htmlFor="structureContent" className="flex items-center gap-2">
                      <BarChart className="h-4 w-4" />
                      Structure Content
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="extractMetadata"
                      checked={processingOptions.extractMetadata}
                      onCheckedChange={(checked) =>
                        setProcessingOptions(prev => ({ ...prev, extractMetadata: !!checked }))
                      }
                    />
                    <Label htmlFor="extractMetadata" className="flex items-center gap-2">
                      <FileSearch className="h-4 w-4" />
                      Extract Metadata
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="vectorizeDocument"
                      checked={processingOptions.vectorizeDocument}
                      onCheckedChange={(checked) =>
                        setProcessingOptions(prev => ({ ...prev, vectorizeDocument: !!checked }))
                      }
                    />
                    <Label htmlFor="vectorizeDocument" className="flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      Vectorize Document
                    </Label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Processing Status */}
        {processingStatus && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="font-medium">Status:</span>
                {processingStatus === MessageStatus.QUEUED && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Queued
                  </Badge>
                )}
                {processingStatus === MessageStatus.PROCESSING && (
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Processing
                  </Badge>
                )}
                {processingStatus === MessageStatus.COMPLETED && (
                  <Badge variant="success" className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Completed
                  </Badge>
                )}
                {processingStatus === MessageStatus.FAILED && (
                  <Badge variant="destructive" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    Failed
                  </Badge>
                )}
                {processingStatus === MessageStatus.CANCELLED && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    Cancelled
                  </Badge>
                )}
              </div>
              {processingElapsedTime > 0 && (processingStatus === MessageStatus.QUEUED || processingStatus === MessageStatus.PROCESSING) && (
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {formatElapsedTime(processingElapsedTime)}
                </div>
              )}
            </div>

            {(processingStatus === MessageStatus.QUEUED || processingStatus === MessageStatus.PROCESSING) && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{processingStage}</span>
                  <span>{processingProgress}%</span>
                </div>
                <Progress value={processingProgress} className="h-2" />
              </div>
            )}

            {processingStatus === MessageStatus.COMPLETED && processingResult && (
              <div className="space-y-2 bg-muted p-3 rounded-md">
                <h3 className="text-sm font-medium">Processing Results</h3>
                <Separator />
                <div className="space-y-1 text-sm">
                  {processingResult.documentType && (
                    <div className="flex justify-between">
                      <span>Document Type:</span>
                      <Badge variant="outline">{processingResult.documentType}</Badge>
                    </div>
                  )}
                  {processingResult.confidence && (
                    <div className="flex justify-between">
                      <span>Confidence:</span>
                      <span>{(processingResult.confidence * 100).toFixed(1)}%</span>
                    </div>
                  )}
                  {processingResult.entities && (
                    <div className="flex justify-between">
                      <span>Entities Extracted:</span>
                      <span>{processingResult.entities.length}</span>
                    </div>
                  )}
                  {processingResult.processingTime && (
                    <div className="flex justify-between">
                      <span>Processing Time:</span>
                      <span>{formatElapsedTime(Math.floor((processingResult.processingTime - (processingStartTime?.getTime() || 0)) / 1000))}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {processingStatus === MessageStatus.FAILED && error && (
              <div className="bg-destructive/10 p-3 rounded-md text-sm text-destructive">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">Error:</span>
                </div>
                <p className="mt-1">{error.message}</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {!processingStatus ? (
          <Button
            onClick={handleStartProcessing}
            disabled={isLoading || !Object.values(processingOptions).some(Boolean)}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Starting...
              </>
            ) : (
              <>
                <Cpu className="mr-2 h-4 w-4" />
                Process Document
              </>
            )}
          </Button>
        ) : (
          <>
            {(processingStatus === MessageStatus.QUEUED || processingStatus === MessageStatus.PROCESSING) && (
              <Button
                variant="outline"
                onClick={handleCancelProcessing}
                className="w-full"
              >
                Cancel Processing
              </Button>
            )}
            {(processingStatus === MessageStatus.COMPLETED || processingStatus === MessageStatus.FAILED || processingStatus === MessageStatus.CANCELLED) && (
              <Button
                variant="outline"
                onClick={handleStartProcessing}
                className="w-full"
              >
                Process Again
              </Button>
            )}
          </>
        )}
      </CardFooter>
    </Card>
  );
}
