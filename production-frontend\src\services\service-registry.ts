/**
 * Service Registry - Lazy Loading Configuration
 * Registers all services with their lazy loading configurations
 * Optimizes resource usage by loading services only when needed
 */

import { lazyServiceManager } from './lazy-service-manager'

/**
 * Register all services with lazy loading configurations
 */
export function registerServices() {
  // ============================================================================
  // COLLABORATION SERVICES - Only for collaborative features
  // ============================================================================
  
  lazyServiceManager.registerService({
    name: 'collaboration',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 180000, // 3 minutes
    initializer: async () => {
      const { collaborationService } = await import('./collaboration-service')
      return collaborationService
    }
  })

  // ============================================================================
  // AI SERVICES - Only when AI features are used
  // ============================================================================
  
  lazyServiceManager.registerService({
    name: 'aiService',
    scope: 'session',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    initializer: async () => {
      // Use existing backend API client for AI operations
      const { backendApiClient } = await import('./backend-api-client')
      return {
        generateSummary: async (content: string) => {
          // Placeholder for AI summary generation
          return `Summary of: ${content.substring(0, 100)}...`
        },
        extractEntities: async (content: string) => {
          // Placeholder for entity extraction
          return []
        },
        askQuestion: async (question: string, context?: string) => {
          // Placeholder for question answering
          return `Answer to: ${question}`
        },
        cleanup: () => console.log('AI service cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'documentAnalysis',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 300000, // 5 minutes
    dependencies: ['aiService'],
    initializer: async () => {
      return {
        analyzeDocument: async (documentId: string, options?: any) => {
          console.log('Analyzing document:', documentId)
          return { analysis: 'Document analysis results' }
        },
        processDocument: async (file: File, options?: any) => {
          console.log('Processing document:', file.name)
          return { jobId: `job_${Date.now()}`, status: 'processing' }
        },
        getAnalysisResults: async (documentId: string) => {
          return { results: 'Analysis results for ' + documentId }
        },
        cleanup: () => console.log('Document analysis service cleaned up')
      }
    }
  })

  // ============================================================================
  // INFRASTRUCTURE SERVICES - Only for admin/monitoring
  // ============================================================================
  
  lazyServiceManager.registerService({
    name: 'eventGrid',
    scope: 'session',
    autoCleanup: true,
    maxIdleTime: 900000, // 15 minutes
    initializer: async () => {
      const { eventGridService } = await import('./event-grid-service')
      return eventGridService
    }
  })

  lazyServiceManager.registerService({
    name: 'serviceBus',
    scope: 'session',
    autoCleanup: true,
    maxIdleTime: 900000, // 15 minutes
    initializer: async () => {
      const { serviceBusService } = await import('./service-bus-service')
      return serviceBusService
    }
  })

  lazyServiceManager.registerService({
    name: 'monitoring',
    scope: 'session',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    initializer: async () => {
      return {
        getSystemHealth: async () => ({ status: 'healthy', services: {} }),
        getMetrics: async () => ({ cpu: 50, memory: 60 }),
        cleanup: () => console.log('Monitoring service cleaned up')
      }
    }
  })

  // ============================================================================
  // DOCUMENT PROCESSING - Only when processing documents
  // ============================================================================

  lazyServiceManager.registerService({
    name: 'documentProcessor',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 300000, // 5 minutes
    initializer: async () => {
      return {
        processDocument: async (file: File, options?: any) => {
          console.log('Processing document:', file.name)
          return { id: `job_${Date.now()}`, status: 'processing', progress: 0 }
        },
        getProcessingStatus: async (jobId: string) => {
          return { id: jobId, status: 'completed', progress: 100 }
        },
        cancelProcessing: async (jobId: string) => {
          console.log('Cancelled processing:', jobId)
        },
        analyzeLayout: async (file: File) => ({ layout: 'analyzed' }),
        extractTables: async (file: File) => [],
        extractKeyValuePairs: async (file: File) => [],
        cleanup: () => console.log('Document processor cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'ocrService',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 300000, // 5 minutes
    initializer: async () => {
      return {
        extractText: async (file: File) => {
          console.log('Extracting text from:', file.name)
          return 'Extracted text content'
        },
        extractTextFromImage: async (imageUrl: string) => {
          console.log('Extracting text from image:', imageUrl)
          return 'Extracted image text'
        },
        cleanup: () => console.log('OCR service cleaned up')
      }
    }
  })

  // ============================================================================
  // NOTIFICATION SERVICES - Only when notifications are enabled
  // ============================================================================
  
  lazyServiceManager.registerService({
    name: 'notifications',
    scope: 'session',
    autoCleanup: false, // Keep notifications active
    maxIdleTime: 1800000, // 30 minutes
    initializer: async () => {
      const { notificationService } = await import('./notification-service')
      return {
        ...notificationService,
        sendNotification: async (notification: any) => {
          console.log('Sending notification:', notification)
        },
        cleanup: () => console.log('Notification service cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'pushNotifications',
    scope: 'session',
    autoCleanup: false,
    maxIdleTime: 1800000, // 30 minutes
    dependencies: ['notifications'],
    initializer: async () => {
      return {
        sendPushNotification: async (notification: any) => {
          console.log('Sending push notification:', notification)
        },
        subscribe: async (userId: string) => {
          console.log('Subscribing user to push notifications:', userId)
        },
        cleanup: () => console.log('Push notification service cleaned up')
      }
    }
  })

  // ============================================================================
  // SEARCH SERVICES - Only when search is used
  // ============================================================================
  
  lazyServiceManager.registerService({
    name: 'search',
    scope: 'session',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    initializer: async () => {
      const { searchService } = await import('./search-service')
      return {
        ...searchService,
        search: async (query: string) => {
          console.log('Searching for:', query)
          return { results: [], total: 0 }
        },
        cleanup: () => console.log('Search service cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'advancedSearch',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 300000, // 5 minutes
    dependencies: ['search'],
    initializer: async () => {
      return {
        advancedSearch: async (query: any) => {
          console.log('Advanced search:', query)
          return { results: [], facets: {} }
        },
        buildQuery: (filters: any) => ({ query: filters }),
        cleanup: () => console.log('Advanced search service cleaned up')
      }
    }
  })

  // ============================================================================
  // ANALYTICS SERVICES - Only for analytics features
  // ============================================================================

  lazyServiceManager.registerService({
    name: 'analytics',
    scope: 'session',
    autoCleanup: true,
    maxIdleTime: 900000, // 15 minutes
    initializer: async () => {
      return {
        trackEvent: async (event: string, data: any) => {
          console.log('Tracking event:', event, data)
        },
        getMetrics: async () => ({ pageViews: 100, users: 50 }),
        cleanup: () => console.log('Analytics service cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'reporting',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    dependencies: ['analytics'],
    initializer: async () => {
      return {
        generateReport: async (type: string) => {
          console.log('Generating report:', type)
          return { report: 'Generated report data' }
        },
        cleanup: () => console.log('Reporting service cleaned up')
      }
    }
  })

  // ============================================================================
  // WORKFLOW SERVICES - Only for workflow features
  // ============================================================================

  lazyServiceManager.registerService({
    name: 'workflow',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    initializer: async () => {
      const { workflowService } = await import('./workflow-service')
      return {
        ...workflowService,
        executeWorkflow: async (workflowId: string) => {
          console.log('Executing workflow:', workflowId)
          return { status: 'running' }
        },
        cleanup: () => console.log('Workflow service cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'automation',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    dependencies: ['workflow'],
    initializer: async () => {
      return {
        createAutomation: async (config: any) => {
          console.log('Creating automation:', config)
          return { id: 'auto_' + Date.now() }
        },
        cleanup: () => console.log('Automation service cleaned up')
      }
    }
  })

  // ============================================================================
  // INTEGRATION SERVICES - Only when integrations are used
  // ============================================================================

  lazyServiceManager.registerService({
    name: 'thirdPartyIntegrations',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    initializer: async () => {
      return {
        connectService: async (service: string, config: any) => {
          console.log('Connecting to service:', service, config)
          return { connected: true }
        },
        cleanup: () => console.log('Integration service cleaned up')
      }
    }
  })

  lazyServiceManager.registerService({
    name: 'webhooks',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 600000, // 10 minutes
    initializer: async () => {
      return {
        createWebhook: async (url: string, events: string[]) => {
          console.log('Creating webhook:', url, events)
          return { id: 'webhook_' + Date.now() }
        },
        cleanup: () => console.log('Webhook service cleaned up')
      }
    }
  })

  // ============================================================================
  // BACKUP SERVICES - Only for backup operations
  // ============================================================================

  lazyServiceManager.registerService({
    name: 'backup',
    scope: 'component',
    autoCleanup: true,
    maxIdleTime: 300000, // 5 minutes
    initializer: async () => {
      return {
        createBackup: async (data: any) => {
          console.log('Creating backup:', data)
          return { backupId: 'backup_' + Date.now() }
        },
        restoreBackup: async (backupId: string) => {
          console.log('Restoring backup:', backupId)
          return { restored: true }
        },
        cleanup: () => console.log('Backup service cleaned up')
      }
    }
  })

  console.info('All services registered for lazy loading')
}

/**
 * Service categories for conditional loading
 */
export const ServiceCategories = {
  COLLABORATION: ['collaboration'],
  AI_FEATURES: ['aiService', 'documentAnalysis'],
  INFRASTRUCTURE: ['eventGrid', 'serviceBus', 'monitoring'],
  DOCUMENT_PROCESSING: ['documentProcessor', 'ocrService'],
  NOTIFICATIONS: ['notifications', 'pushNotifications'],
  SEARCH: ['search', 'advancedSearch'],
  ANALYTICS: ['analytics', 'reporting'],
  WORKFLOW: ['workflow', 'automation'],
  INTEGRATIONS: ['thirdPartyIntegrations', 'webhooks'],
  BACKUP: ['backup']
} as const

/**
 * Check if user has access to service category
 */
export function hasServiceAccess(category: keyof typeof ServiceCategories, userRoles: any[]): boolean {
  const accessMap: Record<keyof typeof ServiceCategories, string[]> = {
    COLLABORATION: ['user', 'admin', 'editor'],
    AI_FEATURES: ['user', 'admin', 'editor'],
    INFRASTRUCTURE: ['admin'],
    DOCUMENT_PROCESSING: ['user', 'admin', 'editor'],
    NOTIFICATIONS: ['user', 'admin', 'editor'],
    SEARCH: ['user', 'admin', 'editor'],
    ANALYTICS: ['admin', 'analyst'],
    WORKFLOW: ['admin', 'editor'],
    INTEGRATIONS: ['admin'],
    BACKUP: ['admin']
  }

  const requiredRoles = accessMap[category] || []
  return requiredRoles.some(role => userRoles.includes(role))
}

// Auto-register services when module is imported
if (typeof window !== 'undefined') {
  registerServices()
}
