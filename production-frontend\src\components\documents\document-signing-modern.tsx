"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/components/ui/use-toast";
import dynamic from 'next/dynamic';

// Dynamically import PDF components to avoid SSR issues
const Document = dynamic(() => import('react-pdf').then(mod => ({ default: mod.Document })), {
  ssr: false,
  loading: () => <div className="flex justify-center p-8">Loading PDF...</div>
});

const Page = dynamic(() => import('react-pdf').then(mod => ({ default: mod.Page })), {
  ssr: false
});

// Dynamic import for pdfjs
let pdfjs: any = null;
if (typeof window !== 'undefined') {
  import('react-pdf').then(mod => {
    pdfjs = mod.pdfjs;
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  });
}
import {
  PenTool,
  Plus,
  Trash,
  Save,
  Check,
  ArrowLeft,
  ArrowRight,
  Upload,
  Image,
  User,
  Loader2
} from "lucide-react";
import { cn } from "@/lib/utils";
import SignaturePad from 'react-signature-canvas';
import { documentService } from "@/services/optimized-document-service";

// PDF.js worker is set dynamically above

interface DocumentSigningProps {
  documentId: string;
  documentName: string;
  projectId?: string;
  organizationId?: string;
  onSigningComplete?: (signedDocumentId: string) => void;
  onCancel?: () => void;
}

interface SignatureLocation {
  id: string;
  page: number;
  x: number;
  y: number;
  width: number;
  height: number;
  signatureId?: string;
}

interface UserSignature {
  id: string;
  name: string;
  url: string;
  createdAt: string;
}

export function DocumentSigningModern({
  documentId,
  documentName,
  projectId,
  organizationId,
  onSigningComplete,
  onCancel
}: DocumentSigningProps) {
  const { toast } = useToast();
  const [activeStep, setActiveStep] = useState<number>(0);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [numPages, setNumPages] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [_isLoading, setIsLoading] = useState<boolean>(true);
  const [isAddingSignature, setIsAddingSignature] = useState<boolean>(false);
  const [signatureLocations, setSignatureLocations] = useState<SignatureLocation[]>([]);
  const [userSignatures, setUserSignatures] = useState<UserSignature[]>([]);
  const [selectedSignatureId, setSelectedSignatureId] = useState<string | null>(null);
  const [newSignatureName, setNewSignatureName] = useState<string>("");
  const [isCreatingSignature, setIsCreatingSignature] = useState<boolean>(false);
  const [isSigningDocument, setIsSigningDocument] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>("draw");

  const documentRef = useRef<HTMLDivElement>(null);
  const signaturePadRef = useRef<SignaturePad>(null);

  const steps = ["Place Signatures", "Select Signature", "Sign Document"];

  // Load document and signatures on mount
  useEffect(() => {
    fetchDocument();
    fetchUserSignatures();
  }, [documentId]);

  const fetchDocument = async () => {
    try {
      setIsLoading(true);
      // Fetch document content URL
      const response = await documentService.getDocumentDownloadUrl(documentId);
      setDocumentUrl(response.url);
    } catch (error) {
      console.error("Error fetching document:", error);
      toast({
        title: "Error",
        description: "Failed to load document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserSignatures = async () => {
    try {
      // Fetch user signatures
      const response = await documentService.getUserSignatures();
      setUserSignatures(response.map(sig => ({
        ...sig,
        url: sig.url || sig.imageUrl
      })));

      // Select the first signature by default if available
      if (response.length > 0) {
        setSelectedSignatureId(response[0].id);
      }
    } catch (error) {
      console.error("Error fetching signatures:", error);
      toast({
        title: "Error",
        description: "Failed to load your signatures. You can still create a new one.",
        variant: "destructive"
      });
    }
  };

  const handleDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= (numPages || 1)) {
      setCurrentPage(newPage);
    }
  };

  const handleDocumentClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isAddingSignature || !documentRef.current) return;

    const rect = documentRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Add new signature location
    const newLocation: SignatureLocation = {
      id: `sig-${Date.now()}`,
      page: currentPage,
      x,
      y,
      width: 150,
      height: 60
    };

    setSignatureLocations([...signatureLocations, newLocation]);
    setIsAddingSignature(false);
  };

  const handleRemoveSignatureLocation = (id: string) => {
    setSignatureLocations(signatureLocations.filter(loc => loc.id !== id));
  };

  const handleCreateSignature = async () => {
    if (!signaturePadRef.current || signaturePadRef.current.isEmpty()) {
      toast({
        title: "Empty Signature",
        description: "Please draw your signature before saving.",
        variant: "destructive"
      });
      return;
    }

    if (!newSignatureName.trim()) {
      toast({
        title: "Missing Name",
        description: "Please provide a name for your signature.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsCreatingSignature(true);

      // Get signature as data URL
      const signatureDataUrl = signaturePadRef.current.toDataURL('image/png');

      // Convert data URL to blob
      const response = await fetch(signatureDataUrl);
      const blob = await response.blob();

      // Create form data
      const formData = new FormData();
      formData.append('signature', blob, 'signature.png');
      formData.append('name', newSignatureName);
      formData.append('removeBackground', 'true');

      // Upload signature
      const result = await documentService.uploadSignature(formData);

      // Add to signatures list and select it
      setUserSignatures([...userSignatures, {
        id: result.id,
        name: newSignatureName,
        url: result.imageUrl,
        createdAt: new Date().toISOString()
      }]);

      setSelectedSignatureId(result.id);
      setNewSignatureName("");
      signaturePadRef.current.clear();

      toast({
        title: "Signature Created",
        description: "Your signature has been created successfully.",
      });

      // Move to next step
      setActiveStep(1);
    } catch (error) {
      console.error("Error creating signature:", error);
      toast({
        title: "Error",
        description: "Failed to create signature. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingSignature(false);
    }
  };

  const handleSignDocument = async () => {
    if (!selectedSignatureId || signatureLocations.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please add signature locations and select a signature.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSigningDocument(true);

      // Prepare request payload
      // Use the first signature location for the position
      const firstLocation = signatureLocations[0];
      const payload = {
        documentId,
        signatureId: selectedSignatureId,
        position: {
          x: firstLocation.x,
          y: firstLocation.y,
          page: firstLocation.page
        }
      };

      // Send request to sign document
      const response = await documentService.signDocument(payload);

      toast({
        title: "Document Signed",
        description: "Your document has been signed successfully.",
      });

      // Notify parent component
      if (onSigningComplete) {
        onSigningComplete(response.id);
      }
    } catch (error) {
      console.error("Error signing document:", error);
      toast({
        title: "Error",
        description: "Failed to sign document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSigningDocument(false);
    }
  };

  const handleNext = () => {
    if (activeStep === 0 && signatureLocations.length === 0) {
      toast({
        title: "Missing Signature Locations",
        description: "Please add at least one signature location.",
        variant: "destructive"
      });
      return;
    }

    if (activeStep === 1 && !selectedSignatureId) {
      toast({
        title: "No Signature Selected",
        description: "Please select or create a signature.",
        variant: "destructive"
      });
      return;
    }

    setActiveStep(prevStep => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep(prevStep => prevStep - 1);
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Click "Add Signature" and then click on the document where you want to place your signature.
            </p>

            <Button
              variant={isAddingSignature ? "default" : "outline"}
              onClick={() => setIsAddingSignature(!isAddingSignature)}
              className="mb-4"
            >
              <Plus className="mr-2 h-4 w-4" />
              {isAddingSignature ? "Click on Document" : "Add Signature"}
            </Button>

            <div
              ref={documentRef}
              onClick={handleDocumentClick}
              className={cn(
                "relative border rounded-md overflow-hidden",
                isAddingSignature && "cursor-crosshair"
              )}
            >
              {documentUrl ? (
                <Document
                  file={documentUrl}
                  onLoadSuccess={handleDocumentLoadSuccess}
                  loading={<div className="flex justify-center p-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}
                  error={<div className="p-8 text-center text-destructive">Failed to load document</div>}
                >
                  <Page
                    pageNumber={currentPage}
                    scale={1.0}
                    renderTextLayer={false}
                    renderAnnotationLayer={false}
                  />
                </Document>
              ) : (
                <div className="flex justify-center items-center h-[500px]">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              )}

              {/* Render signature placeholders */}
              {signatureLocations
                .filter(loc => loc.page === currentPage)
                .map(location => (
                  <div
                    key={location.id}
                    style={{
                      position: 'absolute',
                      left: location.x,
                      top: location.y,
                      width: location.width,
                      height: location.height,
                    }}
                    className="border-2 border-dashed border-primary flex items-center justify-center bg-primary/5"
                  >
                    <span className="text-xs text-muted-foreground">Signature</span>

                    <Button
                      size="icon"
                      variant="destructive"
                      className="absolute -top-3 -right-3 h-6 w-6 rounded-full"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRemoveSignatureLocation(location.id);
                      }}
                    >
                      <Trash className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
            </div>

            {/* Page navigation */}
            {numPages && numPages > 1 && (
              <div className="flex items-center justify-center gap-4 mt-4">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>

                <span className="text-sm">
                  Page {currentPage} of {numPages}
                </span>

                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= (numPages || 1)}
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Your Signatures</CardTitle>
                  <CardDescription>Select an existing signature</CardDescription>
                </CardHeader>
                <CardContent>
                  {userSignatures.length > 0 ? (
                    <div className="grid grid-cols-1 gap-3">
                      {userSignatures.map(sig => (
                        <div
                          key={sig.id}
                          className={cn(
                            "flex items-center gap-3 p-3 border rounded-md cursor-pointer hover:bg-muted transition-colors",
                            selectedSignatureId === sig.id && "border-primary bg-primary/5"
                          )}
                          onClick={() => setSelectedSignatureId(sig.id)}
                        >
                          <div className="bg-white p-2 border rounded-md">
                            <img
                              src={sig.url}
                              alt={sig.name}
                              className="h-8 object-contain"
                            />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-sm">{sig.name}</p>
                          </div>
                          {selectedSignatureId === sig.id && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-muted-foreground">
                      <User className="h-8 w-8 mx-auto mb-2" />
                      <p>You don't have any signatures yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Create New Signature</CardTitle>
                  <CardDescription>Draw or upload your signature</CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid grid-cols-2 mb-4">
                      <TabsTrigger value="draw">
                        <PenTool className="h-4 w-4 mr-2" />
                        Draw
                      </TabsTrigger>
                      <TabsTrigger value="upload">
                        <Upload className="h-4 w-4 mr-2" />
                        Upload
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="draw">
                      <div className="space-y-4">
                        <div className="border rounded-md bg-white p-2">
                          <SignaturePad
                            ref={signaturePadRef}
                            canvasProps={{
                              className: "w-full h-32 border rounded-sm",
                            }}
                          />
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => signaturePadRef.current?.clear()}
                        >
                          Clear
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="upload">
                      <div className="border rounded-md p-8 text-center">
                        <Image className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground mb-4">
                          Upload an image of your signature
                        </p>
                        <Input
                          type="file"
                          accept="image/*"
                          className="mb-2"
                        />
                      </div>
                    </TabsContent>
                  </Tabs>

                  <div className="mt-4 space-y-2">
                    <Label htmlFor="signature-name">Signature Name</Label>
                    <Input
                      id="signature-name"
                      placeholder="e.g., My Signature"
                      value={newSignatureName}
                      onChange={(e) => setNewSignatureName(e.target.value)}
                    />
                  </div>

                  <Button
                    className="w-full mt-4"
                    onClick={handleCreateSignature}
                    disabled={isCreatingSignature}
                  >
                    {isCreatingSignature ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Signature
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {selectedSignatureId && (
              <div className="bg-muted p-4 rounded-md">
                <h3 className="text-sm font-medium mb-2">Selected Signature</h3>
                <div className="bg-white p-4 border rounded-md inline-block">
                  <img
                    src={userSignatures.find(s => s.id === selectedSignatureId)?.url}
                    alt="Selected Signature"
                    className="h-12 object-contain"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="bg-muted p-4 rounded-md">
              <h3 className="font-medium mb-2">Review and Sign</h3>
              <p className="text-sm text-muted-foreground mb-4">
                You are about to sign "{documentName}" with {signatureLocations.length} signature{signatureLocations.length !== 1 ? 's' : ''}.
                This action cannot be undone.
              </p>

              <div className="bg-white p-4 border rounded-md inline-block mb-4">
                <img
                  src={userSignatures.find(s => s.id === selectedSignatureId)?.url}
                  alt="Selected Signature"
                  className="h-12 object-contain"
                />
              </div>

              <Button
                onClick={handleSignDocument}
                disabled={isSigningDocument}
                className="w-full"
              >
                {isSigningDocument ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing Document...
                  </>
                ) : (
                  <>
                    <PenTool className="mr-2 h-4 w-4" />
                    Sign Document
                  </>
                )}
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PenTool className="h-5 w-5" />
          Sign Document
        </CardTitle>
        <CardDescription>
          Add your signature to the document
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="mb-6">
          <div className="flex justify-between mb-2">
            {steps.map((step, index) => (
              <div
                key={index}
                className={cn(
                  "flex-1 text-center relative",
                  index < activeStep && "text-primary",
                  index === activeStep && "text-primary font-medium",
                  index > activeStep && "text-muted-foreground"
                )}
              >
                <div
                  className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center mx-auto mb-2 border-2",
                    index < activeStep && "bg-primary text-primary-foreground border-primary",
                    index === activeStep && "border-primary text-primary",
                    index > activeStep && "border-muted-foreground text-muted-foreground"
                  )}
                >
                  {index < activeStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                <div className="text-xs">{step}</div>

                {/* Connector line */}
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "absolute top-4 left-1/2 w-full h-0.5",
                      index < activeStep && "bg-primary",
                      index >= activeStep && "bg-muted-foreground"
                    )}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        <Separator className="mb-6" />

        {renderStepContent()}
      </CardContent>

      <CardFooter className="flex justify-between">
        {activeStep === 0 ? (
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        ) : (
          <Button variant="outline" onClick={handleBack}>
            Back
          </Button>
        )}

        {activeStep < steps.length - 1 && (
          <Button onClick={handleNext}>
            Next
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
