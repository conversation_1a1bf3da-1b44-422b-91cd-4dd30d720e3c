/**
 * Organizational Workflow Service
 * Frontend service for enhanced organizational paperwork processing workflows
 */

import { backendApiClient } from './backend-api-client';
import { memoryCache } from '../lib/cache';
// import { performanceMonitor } from '../lib/monitoring';

// Types for organizational workflows
export interface OrganizationalWorkflow {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  department: Department;
  complianceFramework: ComplianceFramework[];
  documentTypes: DocumentType[];
  approvalHierarchy: ApprovalLevel[];
  automationRules: AutomationRule[];
  retentionPolicy: RetentionPolicy;
  securityClassification: SecurityClassification;
  status: WorkflowStatus;
  version: string;
  isTemplate: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastExecuted?: string;
  statistics: WorkflowStatistics;
  settings: OrganizationalWorkflowSettings;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  workflowName: string;
  documentIds: string[];
  status: ExecutionStatus;
  priority: Priority;
  dueDate?: string;
  variables: Record<string, any>;
  currentApprovalLevel: number;
  approvalHistory: ApprovalRecord[];
  complianceTracking: ComplianceTracking;
  auditTrail: AuditEntry[];
  organizationId: string;
  department: Department;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface DocumentClassification {
  id: string;
  documentId: string;
  classifiedType: DocumentType;
  confidence: number;
  extractedData: Record<string, any>;
  complianceFlags: ComplianceFlag[];
  suggestedWorkflow: string;
  processingRecommendations: ProcessingRecommendation[];
  classifiedAt: string;
  classifiedBy: 'ai' | 'user';
}

export interface ComplianceDashboard {
  overview: ComplianceOverview;
  recentViolations: ComplianceViolation[];
  upcomingDeadlines: ComplianceDeadline[];
  riskAssessment: RiskAssessment;
  generatedAt: string;
}

// Enums
export enum WorkflowCategory {
  HR = 'hr',
  FINANCE = 'finance',
  LEGAL = 'legal',
  PROCUREMENT = 'procurement',
  OPERATIONS = 'operations',
  COMPLIANCE = 'compliance',
  IT = 'it',
  GENERAL = 'general'
}

export enum Department {
  HUMAN_RESOURCES = 'human_resources',
  FINANCE = 'finance',
  LEGAL = 'legal',
  PROCUREMENT = 'procurement',
  OPERATIONS = 'operations',
  IT = 'it',
  EXECUTIVE = 'executive',
  AUDIT = 'audit',
  RISK_MANAGEMENT = 'risk_management'
}

export enum DocumentType {
  INVOICE = 'invoice',
  PURCHASE_ORDER = 'purchase_order',
  CONTRACT = 'contract',
  EMPLOYEE_ONBOARDING = 'employee_onboarding',
  EXPENSE_REPORT = 'expense_report',
  POLICY_DOCUMENT = 'policy_document',
  COMPLIANCE_REPORT = 'compliance_report',
  FINANCIAL_STATEMENT = 'financial_statement',
  LEGAL_DOCUMENT = 'legal_document',
  VENDOR_AGREEMENT = 'vendor_agreement',
  TIMESHEET = 'timesheet',
  LEAVE_REQUEST = 'leave_request',
  PERFORMANCE_REVIEW = 'performance_review',
  BUDGET_REQUEST = 'budget_request',
  AUDIT_REPORT = 'audit_report'
}

export enum ComplianceFramework {
  SOX = 'sox',
  GDPR = 'gdpr',
  HIPAA = 'hipaa',
  PCI_DSS = 'pci_dss',
  ISO_27001 = 'iso_27001',
  SOC2 = 'soc2',
  CCPA = 'ccpa',
  FERPA = 'ferpa',
  FISMA = 'fisma',
  NIST = 'nist'
}

export enum SecurityClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted',
  TOP_SECRET = 'top_secret'
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated'
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused'
}

export enum Priority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

// Supporting interfaces
export interface ApprovalLevel {
  id: string;
  level: number;
  name: string;
  description: string;
  approverRoles: string[];
  approverUsers: string[];
  requiredApprovals: number;
  escalationTimeHours: number;
  escalationTo: string[];
  conditions: ApprovalCondition[];
  parallelApproval: boolean;
  skipConditions?: SkipCondition[];
}

export interface AutomationRule {
  id: string;
  name: string;
  trigger: AutomationTrigger;
  conditions: RuleCondition[];
  actions: AutomationAction[];
  priority: number;
  enabled: boolean;
  aiAssisted: boolean;
}

export interface RetentionPolicy {
  retentionPeriodYears: number;
  archiveAfterYears: number;
  deleteAfterYears: number;
  legalHoldExempt: boolean;
  complianceRequirements: string[];
}

export interface OrganizationalWorkflowSettings {
  allowParallelExecution: boolean;
  requireDigitalSignature: boolean;
  enableAuditTrail: boolean;
  autoClassifyDocuments: boolean;
  enableAIAssistance: boolean;
  notificationSettings: NotificationSettings;
  escalationSettings: EscalationSettings;
  complianceSettings: ComplianceSettings;
}

export interface WorkflowStatistics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageCompletionTimeHours: number;
  averageApprovalTimeHours: number;
  complianceScore: number;
  documentsProcessed: number;
  currentActiveExecutions: number;
}

export interface ApprovalRecord {
  id: string;
  levelId: string;
  approverId: string;
  approverName: string;
  action: 'approved' | 'rejected' | 'delegated' | 'escalated';
  comment?: string;
  timestamp: string;
  digitalSignature?: string;
}

export interface ComplianceTracking {
  frameworks: ComplianceFramework[];
  status: 'compliant' | 'non_compliant' | 'pending_review' | 'requires_attention';
  violations: ComplianceViolation[];
  lastAssessment: string;
}

export interface AuditEntry {
  id: string;
  timestamp: string;
  userId: string;
  action: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  result: 'success' | 'failure' | 'warning';
}

export interface ComplianceFlag {
  framework: ComplianceFramework;
  requirement: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  remediation: string;
}

export interface ProcessingRecommendation {
  type: 'workflow' | 'approval' | 'classification' | 'compliance';
  recommendation: string;
  confidence: number;
  reasoning: string;
}

export interface ComplianceOverview {
  totalWorkflows: number;
  compliantWorkflows: number;
  violationsCount: number;
  complianceScore: number;
}

export interface ComplianceViolation {
  id: string;
  requirement: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: string;
  resolvedAt?: string;
  remediation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
}

export interface ComplianceDeadline {
  id: string;
  requirement: string;
  framework: ComplianceFramework;
  dueDate: string;
  status: 'upcoming' | 'overdue' | 'completed';
  priority: Priority;
}

export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high' | 'critical';
  riskFactors: RiskFactor[];
  recommendations: string[];
}

export interface RiskFactor {
  category: string;
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  likelihood: 'low' | 'medium' | 'high';
  mitigation: string;
}

// Additional supporting interfaces
export interface ApprovalCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface SkipCondition {
  field: string;
  operator: string;
  value: any;
  reason: string;
}

export interface AutomationTrigger {
  type: 'document_uploaded' | 'approval_completed' | 'deadline_approaching' | 'compliance_violation' | 'custom';
  conditions: RuleCondition[];
}

export interface RuleCondition {
  field: string;
  operator: string;
  value: any;
  type: 'document' | 'user' | 'time' | 'system';
}

export interface AutomationAction {
  type: 'send_notification' | 'assign_task' | 'update_field' | 'trigger_workflow' | 'generate_report' | 'ai_analysis';
  parameters: Record<string, any>;
  delay?: number;
}

export interface NotificationSettings {
  emailEnabled: boolean;
  smsEnabled: boolean;
  inAppEnabled: boolean;
  slackEnabled: boolean;
  teamsEnabled: boolean;
  customWebhooks: string[];
}

export interface EscalationSettings {
  enableAutoEscalation: boolean;
  escalationTimeHours: number;
  maxEscalationLevels: number;
  escalationChain: string[];
}

export interface ComplianceSettings {
  enableComplianceTracking: boolean;
  requiredFrameworks: ComplianceFramework[];
  autoGenerateReports: boolean;
  alertOnViolations: boolean;
}

class OrganizationalWorkflowService {
  private readonly CACHE_TTL = 300000; // 5 minutes

  /**
   * Create organizational workflow
   */
  async createOrganizationalWorkflow(workflowData: {
    name: string;
    description: string;
    category: WorkflowCategory;
    department: Department;
    documentTypes: DocumentType[];
    complianceFramework?: ComplianceFramework[];
    approvalHierarchy: Partial<ApprovalLevel>[];
    automationRules?: AutomationRule[];
    retentionPolicy?: Partial<RetentionPolicy>;
    securityClassification?: SecurityClassification;
    settings?: Partial<OrganizationalWorkflowSettings>;
  }): Promise<OrganizationalWorkflow> {
    const startTime = performance.now();

    try {
      const workflow = await backendApiClient.request<OrganizationalWorkflow>('/workflows/organizational', {
        method: 'POST',
        body: JSON.stringify(workflowData)
      });

      // performanceMonitor.recordMetric(
      //   'createOrganizationalWorkflow',
      //   performance.now() - startTime
      // );

      // Cache the workflow
      memoryCache.set(`org-workflow:${workflow.id}`, workflow, this.CACHE_TTL);

      return workflow;
    } catch (error) {
      // performanceMonitor.recordError('createOrganizationalWorkflow', error as Error);
      throw error;
    }
  }

  /**
   * Classify document and suggest workflow
   */
  async classifyDocumentAndSuggestWorkflow(documentId: string): Promise<{
    classification: DocumentClassification;
    suggestedWorkflows: OrganizationalWorkflow[];
    complianceRecommendations: ProcessingRecommendation[];
    autoProcessingAvailable: boolean;
  }> {
    const startTime = performance.now();

    try {
      // Check cache first
      const cacheKey = `doc-classification:${documentId}`;
      const cached = memoryCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const result = await backendApiClient.request(`/documents/${documentId}/classify-workflow`, {
        method: 'POST'
      });

      // performanceMonitor.recordMetric(
      //   'classifyDocumentAndSuggestWorkflow',
      //   performance.now() - startTime
      // );

      // Cache the result
      memoryCache.set(cacheKey, result, this.CACHE_TTL);

      return result;
    } catch (error) {
      // performanceMonitor.recordError('classifyDocumentAndSuggestWorkflow', error as Error);
      throw error;
    }
  }

  /**
   * Execute organizational workflow
   */
  async executeOrganizationalWorkflow(executionData: {
    workflowId: string;
    documentIds: string[];
    priority?: Priority;
    dueDate?: string;
    variables?: Record<string, any>;
    skipApprovalLevels?: number[];
    emergencyExecution?: boolean;
    complianceOverride?: boolean;
  }): Promise<{
    executionId: string;
    status: string;
    message: string;
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request('/workflows/organizational/execute', {
        method: 'POST',
        body: JSON.stringify(executionData)
      });

      // performanceMonitor.recordMetric(
      //   'executeOrganizationalWorkflow',
      //   performance.now() - startTime
      // );

      return result;
    } catch (error) {
      // performanceMonitor.recordError('executeOrganizationalWorkflow', error as Error);
      throw error;
    }
  }

  /**
   * Get compliance dashboard
   */
  async getComplianceDashboard(options?: {
    timeRange?: string;
    framework?: ComplianceFramework;
  }): Promise<ComplianceDashboard> {
    const startTime = performance.now();

    try {
      // Check cache first
      const cacheKey = `compliance-dashboard:${options?.timeRange || '30d'}:${options?.framework || 'all'}`;
      const cached = memoryCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const params = new URLSearchParams();
      if (options?.timeRange) params.append('timeRange', options.timeRange);
      if (options?.framework) params.append('framework', options.framework);

      const dashboard = await backendApiClient.request<ComplianceDashboard>(`/compliance/dashboard?${params}`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getComplianceDashboard',
      //   performance.now() - startTime
      // );

      // Cache the dashboard
      memoryCache.set(cacheKey, dashboard, this.CACHE_TTL);

      return dashboard;
    } catch (error) {
      // performanceMonitor.recordError('getComplianceDashboard', error as Error);
      throw error;
    }
  }

  /**
   * Get organizational workflows
   */
  async getOrganizationalWorkflows(filters?: {
    category?: WorkflowCategory;
    department?: Department;
    status?: WorkflowStatus;
    documentType?: DocumentType;
  }): Promise<OrganizationalWorkflow[]> {
    const startTime = performance.now();

    try {
      const params = new URLSearchParams();
      if (filters?.category) params.append('category', filters.category);
      if (filters?.department) params.append('department', filters.department);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.documentType) params.append('documentType', filters.documentType);

      const workflows = await backendApiClient.request<OrganizationalWorkflow[]>(`/workflows/organizational?${params}`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getOrganizationalWorkflows',
      //   performance.now() - startTime
      // );

      return workflows;
    } catch (error) {
      // performanceMonitor.recordError('getOrganizationalWorkflows', error as Error);
      throw error;
    }
  }

  /**
   * Get workflow executions
   */
  async getWorkflowExecutions(filters?: {
    workflowId?: string;
    status?: ExecutionStatus;
    department?: Department;
    priority?: Priority;
    limit?: number;
    offset?: number;
  }): Promise<{
    executions: WorkflowExecution[];
    total: number;
    hasMore: boolean;
  }> {
    const startTime = performance.now();

    try {
      const params = new URLSearchParams();
      if (filters?.workflowId) params.append('workflowId', filters.workflowId);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.department) params.append('department', filters.department);
      if (filters?.priority) params.append('priority', filters.priority);
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.offset) params.append('offset', filters.offset.toString());

      const result = await backendApiClient.request(`/workflows/organizational/executions?${params}`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getWorkflowExecutions',
      //   performance.now() - startTime
      // );

      return result;
    } catch (error) {
      // performanceMonitor.recordError('getWorkflowExecutions', error as Error);
      throw error;
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    try {
      memoryCache.clear();
    } catch (error) {
      console.warn('Failed to clear cache');
    }
  }
}

// Create and export singleton instance
export const organizationalWorkflowService = new OrganizationalWorkflowService();
export default organizationalWorkflowService;
