/**
 * Auth Cleanup Utility
 * Handles cleanup of authentication-related data and sessions
 */

import { logger } from './logger'

export interface AuthCleanupOptions {
  clearTokens?: boolean
  clearUserData?: boolean
  clearPreferences?: boolean
  clearCache?: boolean
  redirectTo?: string
}

class AuthCleanup {
  private readonly storageKeys = [
    'auth-token',
    'refresh-token',
    'user-data',
    'user-preferences',
    'session-data',
    'auth-state',
    'remember-me',
    'last-login',
    'auth-expires',
    'device-id',
    'auth-cache'
  ]

  /**
   * Perform complete authentication cleanup
   */
  async cleanup(options: AuthCleanupOptions = {}): Promise<void> {
    const {
      clearTokens = true,
      clearUserData = true,
      clearPreferences = false,
      clearCache = true,
      redirectTo
    } = options

    try {
      logger.info('Starting authentication cleanup', 'auth-cleanup', { options })

      // Clear tokens
      if (clearTokens) {
        await this.clearTokens()
      }

      // Clear user data
      if (clearUserData) {
        await this.clearUserData()
      }

      // Clear user preferences (optional)
      if (clearPreferences) {
        await this.clearPreferences()
      }

      // Clear cache
      if (clearCache) {
        await this.clearCache()
      }

      // Clear session storage
      await this.clearSessionStorage()

      // Clear cookies
      await this.clearAuthCookies()

      // Notify other tabs/windows
      this.notifyOtherTabs()

      // Redirect if specified
      if (redirectTo) {
        window.location.href = redirectTo
      }

      logger.info('Authentication cleanup completed successfully', 'auth-cleanup')
    } catch (error) {
      logger.error('Authentication cleanup failed', 'auth-cleanup', { error })
      throw error
    }
  }

  /**
   * Clear authentication tokens
   */
  private async clearTokens(): Promise<void> {
    try {
      // Clear from localStorage
      localStorage.removeItem('auth-token')
      localStorage.removeItem('refresh-token')
      localStorage.removeItem('auth-expires')

      // Clear from sessionStorage
      sessionStorage.removeItem('auth-token')
      sessionStorage.removeItem('refresh-token')

      logger.debug('Authentication tokens cleared', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to clear tokens', 'auth-cleanup', { error })
    }
  }

  /**
   * Clear user data
   */
  private async clearUserData(): Promise<void> {
    try {
      // Clear from localStorage
      localStorage.removeItem('user-data')
      localStorage.removeItem('session-data')
      localStorage.removeItem('last-login')
      localStorage.removeItem('device-id')

      // Clear from sessionStorage
      sessionStorage.removeItem('user-data')
      sessionStorage.removeItem('session-data')

      logger.debug('User data cleared', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to clear user data', 'auth-cleanup', { error })
    }
  }

  /**
   * Clear user preferences
   */
  private async clearPreferences(): Promise<void> {
    try {
      localStorage.removeItem('user-preferences')
      localStorage.removeItem('theme')
      localStorage.removeItem('language')
      localStorage.removeItem('notifications-settings')

      logger.debug('User preferences cleared', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to clear preferences', 'auth-cleanup', { error })
    }
  }

  /**
   * Clear authentication cache
   */
  private async clearCache(): Promise<void> {
    try {
      // Clear auth-related cache
      localStorage.removeItem('auth-cache')
      localStorage.removeItem('permissions-cache')
      localStorage.removeItem('roles-cache')

      // Clear API cache if using a cache library
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        const authCaches = cacheNames.filter(name => 
          name.includes('auth') || name.includes('api')
        )
        
        await Promise.all(
          authCaches.map(cacheName => caches.delete(cacheName))
        )
      }

      logger.debug('Authentication cache cleared', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to clear cache', 'auth-cleanup', { error })
    }
  }

  /**
   * Clear session storage
   */
  private async clearSessionStorage(): Promise<void> {
    try {
      // Clear all auth-related items from session storage
      this.storageKeys.forEach(key => {
        sessionStorage.removeItem(key)
      })

      logger.debug('Session storage cleared', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to clear session storage', 'auth-cleanup', { error })
    }
  }

  /**
   * Clear authentication cookies
   */
  private async clearAuthCookies(): Promise<void> {
    try {
      const authCookies = [
        'auth-token',
        'refresh-token',
        'session-id',
        'remember-me',
        'csrf-token',
        'auth-state'
      ]

      authCookies.forEach(cookieName => {
        // Clear cookie by setting it to expire in the past
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname};`
        
        // Also try with leading dot for subdomain cookies
        if (window.location.hostname.includes('.')) {
          const domain = '.' + window.location.hostname.split('.').slice(-2).join('.')
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain};`
        }
      })

      logger.debug('Authentication cookies cleared', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to clear cookies', 'auth-cleanup', { error })
    }
  }

  /**
   * Notify other tabs/windows about logout
   */
  private notifyOtherTabs(): void {
    try {
      // Use localStorage event to notify other tabs
      localStorage.setItem('auth-logout', Date.now().toString())
      localStorage.removeItem('auth-logout')

      // Use BroadcastChannel if available
      if ('BroadcastChannel' in window) {
        const channel = new BroadcastChannel('auth-channel')
        channel.postMessage({ type: 'logout', timestamp: Date.now() })
        channel.close()
      }

      logger.debug('Other tabs notified about logout', 'auth-cleanup')
    } catch (error) {
      logger.error('Failed to notify other tabs', 'auth-cleanup', { error })
    }
  }

  /**
   * Partial cleanup for specific scenarios
   */
  async partialCleanup(keys: string[]): Promise<void> {
    try {
      keys.forEach(key => {
        localStorage.removeItem(key)
        sessionStorage.removeItem(key)
      })

      logger.debug('Partial cleanup completed', 'auth-cleanup', { keys })
    } catch (error) {
      logger.error('Partial cleanup failed', 'auth-cleanup', { error, keys })
    }
  }

  /**
   * Check if cleanup is needed
   */
  isCleanupNeeded(): boolean {
    try {
      // Check if any auth tokens exist
      const hasTokens = Boolean(
        localStorage.getItem('auth-token') ||
        sessionStorage.getItem('auth-token') ||
        localStorage.getItem('refresh-token')
      )

      // Check if tokens are expired
      const expiresAt = localStorage.getItem('auth-expires')
      const isExpired = expiresAt ? Date.now() > parseInt(expiresAt) : false

      return hasTokens && isExpired
    } catch (error) {
      logger.error('Failed to check cleanup status', 'auth-cleanup', { error })
      return false
    }
  }

  /**
   * Schedule automatic cleanup
   */
  scheduleCleanup(intervalMs: number = 60000): () => void {
    const interval = setInterval(() => {
      if (this.isCleanupNeeded()) {
        this.cleanup({ redirectTo: '/login' })
      }
    }, intervalMs)

    return () => clearInterval(interval)
  }

  /**
   * Emergency cleanup - clears everything
   */
  async emergencyCleanup(): Promise<void> {
    try {
      // Clear all localStorage
      localStorage.clear()

      // Clear all sessionStorage
      sessionStorage.clear()

      // Clear all cookies
      document.cookie.split(";").forEach(cookie => {
        const eqPos = cookie.indexOf("=")
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
        document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`
      })

      // Clear all caches
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(name => caches.delete(name)))
      }

      logger.warn('Emergency cleanup completed', 'auth-cleanup')
    } catch (error) {
      logger.error('Emergency cleanup failed', 'auth-cleanup', { error })
    }
  }
}

// Export singleton instance
export const authCleanup = new AuthCleanup()

// Export convenience functions
export const cleanupAuth = (options?: AuthCleanupOptions) => authCleanup.cleanup(options)
export const clearAllAuthData = (options?: AuthCleanupOptions) => authCleanup.cleanup(options)
export const emergencyCleanup = () => authCleanup.emergencyCleanup()
export const isCleanupNeeded = () => authCleanup.isCleanupNeeded()
export const scheduleCleanup = (intervalMs?: number) => authCleanup.scheduleCleanup(intervalMs)

// Debug function
export const debugAuthState = () => {
  console.log('Auth Debug State:', {
    localStorage: Object.keys(localStorage).filter(key => key.includes('auth')),
    sessionStorage: Object.keys(sessionStorage).filter(key => key.includes('auth')),
    cookies: document.cookie.split(';').filter(cookie => cookie.includes('auth')),
    isCleanupNeeded: isCleanupNeeded()
  })
}

export default authCleanup
