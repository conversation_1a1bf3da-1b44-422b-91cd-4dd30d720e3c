"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Share,
  Users,
  Building2,
  FolderKanban,
  User,
  Trash2,
  Search,
  Calendar
} from "lucide-react";
import { Template } from "@/services/template-service";
import { DatePicker } from "@/components/ui/date-picker";
import { useTemplateSharing } from "@/hooks/templates/useTemplateSharing";

interface TemplateSharingDialogProps {
  template: Template;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onShare?: (data: {
    templateId: string;
    entityId: string;
    entityType: string;
    permissions: string[];
    expiresAt?: string;
  }) => Promise<void>;
}

export function TemplateSharingDialog({
  template,
  open,
  onOpenChange,
  onShare
}: TemplateSharingDialogProps) {
  // Use the template sharing hook
  const {
    sharedEntities,
    availableEntities: entities,
    searchQuery,
    setSearchQuery,
    filteredEntities,
    shareWithEntity: handleShareTemplate,
    loading: isSubmitting,
    removeSharing
  } = useTemplateSharing({ templateId: template.id });

  // Additional state for the dialog
  const [entityType, setEntityType] = useState<'user' | 'team' | 'organization'>('user');
  const [selectedEntityId, setSelectedEntityId] = useState<string>('');
  const [permissions, setPermissions] = useState<string[]>(['view']);
  const [expiresAt, setExpiresAt] = useState<Date | undefined>();

  // Toggle permission helper
  const togglePermission = (permission: string) => {
    setPermissions(prev =>
      prev.includes(permission)
        ? prev.filter(p => p !== permission)
        : [...prev, permission]
    );
  };

  // Handle share
  const handleShare = async () => {
    if (onShare) {
      // If the parent component provided an onShare callback, use it
      // This allows for custom handling of the share action
      await onShare({
        templateId: template.id,
        entityId: selectedEntityId,
        entityType,
        permissions,
        expiresAt: expiresAt?.toISOString()
      });
    } else {
      // Otherwise, use the hook's handleShare function
      await handleShareTemplate(selectedEntityId, permissions.join(','));
    }
  };

  // Handle remove sharing
  const handleRemoveSharing = (id: string) => {
    removeSharing(id);
  };

  // Get entity type icon
  const getEntityTypeIcon = (type: string) => {
    switch (type) {
      case 'user':
        return <User className="h-4 w-4" />;
      case 'team':
        return <Users className="h-4 w-4" />;
      case 'organization':
        return <Building2 className="h-4 w-4" />;
      case 'project':
        return <FolderKanban className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share className="h-5 w-5" />
            Share Template
          </DialogTitle>
          <DialogDescription>
            Share the template "{template.name}" with users, teams, organizations, or projects.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          <div className="grid gap-4">
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-1">
                <Label htmlFor="entity-type">Share with</Label>
                <Select
                  value={entityType}
                  onValueChange={(value: any) => {
                    setEntityType(value);
                    setSelectedEntityId("");
                  }}
                >
                  <SelectTrigger id="entity-type">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span>User</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="team">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span>Team</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="organization">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4" />
                        <span>Organization</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="project">
                      <div className="flex items-center gap-2">
                        <FolderKanban className="h-4 w-4" />
                        <span>Project</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="col-span-3">
                <Label htmlFor="entity">Select {entityType}</Label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="search-entity"
                    placeholder={`Search ${entityType}s...`}
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>

            <div className="border rounded-md p-2 h-[120px] overflow-y-auto">
              {filteredEntities.length === 0 ? (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  No {entityType}s found
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredEntities.map((entity) => (
                    <div
                      key={entity.id}
                      className={`flex items-center justify-between p-2 rounded-md cursor-pointer ${
                        selectedEntityId === entity.id ? 'bg-primary/10' : 'hover:bg-muted'
                      }`}
                      onClick={() => setSelectedEntityId(entity.id)}
                    >
                      <div className="flex items-center gap-2">
                        {getEntityTypeIcon(entityType)}
                        <span>{entity.name}</span>
                      </div>
                      <div>
                        <Checkbox
                          checked={selectedEntityId === entity.id}
                          onCheckedChange={() => setSelectedEntityId(entity.id)}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Permissions</Label>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="permission-view"
                      checked={permissions.includes("view")}
                      onCheckedChange={() => togglePermission("view")}
                    />
                    <Label htmlFor="permission-view">View</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="permission-edit"
                      checked={permissions.includes("edit")}
                      onCheckedChange={() => togglePermission("edit")}
                    />
                    <Label htmlFor="permission-edit">Edit</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="permission-share"
                      checked={permissions.includes("share")}
                      onCheckedChange={() => togglePermission("share")}
                    />
                    <Label htmlFor="permission-share">Share</Label>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="expires-at">Expires (optional)</Label>
                <div className="mt-2">
                  <DatePicker
                    date={expiresAt}
                    onSelect={setExpiresAt}
                    fromDate={new Date()}
                  />
                </div>
              </div>
            </div>

            <Button
              onClick={handleShare}
              disabled={!selectedEntityId || permissions.length === 0 || isSubmitting}
            >
              {isSubmitting ? "Sharing..." : "Share Template"}
            </Button>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-2">Shared with</h3>
            {sharedEntities.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                This template has not been shared with anyone yet.
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Entity</TableHead>
                    <TableHead>Permissions</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sharedEntities.map((sharing) => (
                    <TableRow key={sharing.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getEntityTypeIcon(sharing.entityType)}
                          <span>{sharing.entityId}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {[sharing.permission].map((permission: string) => (
                            <span
                              key={permission}
                              className="px-1.5 py-0.5 bg-muted text-xs rounded"
                            >
                              {permission}
                            </span>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        {sharing.expiresAt ? (
                          <div className="flex items-center gap-1 text-sm">
                            <Calendar className="h-3 w-3" />
                            {new Date(sharing.expiresAt).toLocaleDateString()}
                          </div>
                        ) : (
                          <span className="text-muted-foreground text-sm">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveSharing(sharing.id)}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
