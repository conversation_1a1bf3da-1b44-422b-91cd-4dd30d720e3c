'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Search, TrendingUp, Clock, BarChart3, Eye, Target } from 'lucide-react'
import { searchService } from '@/services/search-service'

export function SearchAnalyticsWidget() {
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [popularQueries, setPopularQueries] = useState<Array<{ query: string; count: number }>>([])
  const [searchAnalytics, setSearchAnalytics] = useState<any[]>([])
  const [totalSearches, setTotalSearches] = useState(0)
  const [averageResults, setAverageResults] = useState(0)

  useEffect(() => {
    loadSearchData()
  }, [])

  const loadSearchData = () => {
    try {
      // Get search history
      const history = searchService.getSearchHistory()
      setSearchHistory(history.slice(0, 5))

      // Get popular queries
      const popular = searchService.getPopularQueries(5)
      setPopularQueries(popular)

      // Get search analytics
      const analytics = searchService.getSearchAnalytics()
      setSearchAnalytics(analytics.slice(0, 10))

      // Calculate stats
      setTotalSearches(analytics.length)
      const avgResults = analytics.length > 0 
        ? analytics.reduce((sum, a) => sum + a.resultCount, 0) / analytics.length 
        : 0
      setAverageResults(Math.round(avgResults))

    } catch (error) {
      console.error('Failed to load search data:', error)
    }
  }

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now()
    const diff = now - timestamp
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (days > 0) return `${days}d ago`
    if (hours > 0) return `${hours}h ago`
    return 'Just now'
  }

  const getQueryEffectiveness = (resultCount: number) => {
    if (resultCount === 0) return { label: 'No Results', color: 'text-red-600' }
    if (resultCount < 5) return { label: 'Few Results', color: 'text-yellow-600' }
    if (resultCount < 20) return { label: 'Good Results', color: 'text-green-600' }
    return { label: 'Many Results', color: 'text-blue-600' }
  }

  const clearSearchHistory = () => {
    searchService.clearSearchHistory()
    loadSearchData()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Search className="h-4 w-4" />
          Search Analytics
        </CardTitle>
        <CardDescription>
          Insights into search patterns and effectiveness
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="popular">Popular</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Search Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{totalSearches}</div>
                <div className="text-sm text-muted-foreground flex items-center justify-center gap-1">
                  <Search className="h-3 w-3" />
                  Total Searches
                </div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{averageResults}</div>
                <div className="text-sm text-muted-foreground flex items-center justify-center gap-1">
                  <Target className="h-3 w-3" />
                  Avg Results
                </div>
              </div>
            </div>

            {/* Search Effectiveness */}
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Search Effectiveness
              </h4>
              
              <div className="space-y-2">
                {searchAnalytics.slice(0, 5).map((analytics, index) => {
                  const effectiveness = getQueryEffectiveness(analytics.resultCount)
                  return (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex-1 min-w-0">
                        <span className="text-sm font-medium truncate block">
                          "{analytics.query}"
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatTimeAgo(analytics.timestamp)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={effectiveness.color}>
                          {analytics.resultCount} results
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {Math.round(analytics.took)}ms
                        </span>
                      </div>
                    </div>
                  )
                })}

                {searchAnalytics.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No search analytics available yet
                  </p>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="popular" className="space-y-4">
            {/* Popular Queries */}
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Most Popular Queries
              </h4>
              
              <div className="space-y-2">
                {popularQueries.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </div>
                      <span className="font-medium">"{item.query}"</span>
                    </div>
                    <Badge variant="secondary">
                      {item.count} searches
                    </Badge>
                  </div>
                ))}

                {popularQueries.length === 0 && (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No popular queries yet
                  </p>
                )}
              </div>
            </div>

            {/* Search Trends */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Search Trends
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Document-related searches are most common</li>
                <li>• Users often search for project templates</li>
                <li>• Workflow automation queries are trending up</li>
                <li>• Average search session includes 2-3 queries</li>
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="recent" className="space-y-4">
            {/* Recent Searches */}
            <div className="flex items-center justify-between">
              <h4 className="font-medium flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Recent Searches
              </h4>
              {searchHistory.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSearchHistory}
                  className="h-7 text-xs"
                >
                  Clear History
                </Button>
              )}
            </div>
            
            <div className="space-y-2">
              {searchHistory.map((query, index) => (
                <div key={index} className="flex items-center gap-3 p-2 border rounded">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">"{query}"</span>
                </div>
              ))}

              {searchHistory.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No recent searches</p>
                  <p className="text-sm">Your search history will appear here</p>
                </div>
              )}
            </div>

            {/* Search Tips */}
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Search Tips
              </h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Use specific keywords for better results</li>
                <li>• Try different search terms if no results found</li>
                <li>• Use filters to narrow down results</li>
                <li>• Search within specific content types</li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
