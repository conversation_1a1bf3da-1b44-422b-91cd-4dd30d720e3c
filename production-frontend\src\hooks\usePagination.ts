import { useState, useMemo, useCallback } from 'react'

/**
 * Pagination Hook
 * Manages pagination state and provides pagination utilities
 */

export interface UsePaginationOptions {
  initialPage?: number
  initialPageSize?: number
  pageSizeOptions?: number[]
}

export interface UsePaginationResult<T> {
  // Current state
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
  
  // Paginated data
  paginatedItems: T[]
  
  // Navigation
  goToPage: (page: number) => void
  goToFirstPage: () => void
  goToLastPage: () => void
  goToNextPage: () => void
  goToPreviousPage: () => void
  
  // Page size
  setPageSize: (size: number) => void
  
  // State checks
  canGoNext: boolean
  canGoPrevious: boolean
  
  // Page info
  startIndex: number
  endIndex: number
  pageNumbers: number[]
}

export function usePagination<T>(
  items: T[],
  options: UsePaginationOptions = {}
): UsePaginationResult<T> {
  const {
    initialPage = 1,
    initialPageSize = 10,
    pageSizeOptions = [5, 10, 20, 50, 100],
  } = options

  const [currentPage, setCurrentPage] = useState(initialPage)
  const [pageSize, setPageSizeState] = useState(initialPageSize)

  const totalItems = items.length
  const totalPages = Math.ceil(totalItems / pageSize)

  // Ensure current page is valid
  const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages || 1)

  const startIndex = (validCurrentPage - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, totalItems)

  const paginatedItems = useMemo(() => {
    return items.slice(startIndex, endIndex)
  }, [items, startIndex, endIndex])

  const pageNumbers = useMemo(() => {
    const pages: number[] = []
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i)
    }
    return pages
  }, [totalPages])

  const goToPage = useCallback((page: number) => {
    const validPage = Math.min(Math.max(1, page), totalPages || 1)
    setCurrentPage(validPage)
  }, [totalPages])

  const goToFirstPage = useCallback(() => {
    setCurrentPage(1)
  }, [])

  const goToLastPage = useCallback(() => {
    setCurrentPage(totalPages || 1)
  }, [totalPages])

  const goToNextPage = useCallback(() => {
    if (validCurrentPage < totalPages) {
      setCurrentPage(validCurrentPage + 1)
    }
  }, [validCurrentPage, totalPages])

  const goToPreviousPage = useCallback(() => {
    if (validCurrentPage > 1) {
      setCurrentPage(validCurrentPage - 1)
    }
  }, [validCurrentPage])

  const setPageSize = useCallback((size: number) => {
    setPageSizeState(size)
    // Reset to first page when page size changes
    setCurrentPage(1)
  }, [])

  const canGoNext = validCurrentPage < totalPages
  const canGoPrevious = validCurrentPage > 1

  return {
    currentPage: validCurrentPage,
    pageSize,
    totalItems,
    totalPages,
    paginatedItems,
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToNextPage,
    goToPreviousPage,
    setPageSize,
    canGoNext,
    canGoPrevious,
    startIndex,
    endIndex,
    pageNumbers,
  }
}

/**
 * Server-side pagination hook
 */
export interface UseServerPaginationOptions {
  initialPage?: number
  initialPageSize?: number
  onPageChange?: (page: number, pageSize: number) => void
}

export interface UseServerPaginationResult {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
  
  goToPage: (page: number) => void
  goToFirstPage: () => void
  goToLastPage: () => void
  goToNextPage: () => void
  goToPreviousPage: () => void
  setPageSize: (size: number) => void
  
  canGoNext: boolean
  canGoPrevious: boolean
  
  startIndex: number
  endIndex: number
}

export function useServerPagination(
  totalItems: number,
  options: UseServerPaginationOptions = {}
): UseServerPaginationResult {
  const {
    initialPage = 1,
    initialPageSize = 10,
    onPageChange,
  } = options

  const [currentPage, setCurrentPage] = useState(initialPage)
  const [pageSize, setPageSizeState] = useState(initialPageSize)

  const totalPages = Math.ceil(totalItems / pageSize)
  const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages || 1)

  const startIndex = (validCurrentPage - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, totalItems)

  const goToPage = useCallback((page: number) => {
    const validPage = Math.min(Math.max(1, page), totalPages || 1)
    setCurrentPage(validPage)
    onPageChange?.(validPage, pageSize)
  }, [totalPages, pageSize, onPageChange])

  const goToFirstPage = useCallback(() => {
    goToPage(1)
  }, [goToPage])

  const goToLastPage = useCallback(() => {
    goToPage(totalPages || 1)
  }, [goToPage, totalPages])

  const goToNextPage = useCallback(() => {
    if (validCurrentPage < totalPages) {
      goToPage(validCurrentPage + 1)
    }
  }, [validCurrentPage, totalPages, goToPage])

  const goToPreviousPage = useCallback(() => {
    if (validCurrentPage > 1) {
      goToPage(validCurrentPage - 1)
    }
  }, [validCurrentPage, goToPage])

  const setPageSize = useCallback((size: number) => {
    setPageSizeState(size)
    setCurrentPage(1)
    onPageChange?.(1, size)
  }, [onPageChange])

  const canGoNext = validCurrentPage < totalPages
  const canGoPrevious = validCurrentPage > 1

  return {
    currentPage: validCurrentPage,
    pageSize,
    totalItems,
    totalPages,
    goToPage,
    goToFirstPage,
    goToLastPage,
    goToNextPage,
    goToPreviousPage,
    setPageSize,
    canGoNext,
    canGoPrevious,
    startIndex,
    endIndex,
  }
}
