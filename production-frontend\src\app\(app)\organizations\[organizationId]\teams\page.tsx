"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { useTeams } from "@/hooks/teams";
import { useDeleteTeam } from "@/hooks/teams";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Users, Plus, Search } from "lucide-react";
import { TeamCard } from "@/components/teams";
import { EmptyState } from "@/components/empty-state";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function TeamsPage() {
  const params = useParams();
  // const router = useRouter(); // Uncomment when navigation is needed

  // Ensure params is not null
  if (!params) {
    return <div>Loading...</div>;
  }

  const organizationId = params.organizationId as string;

  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<string>("ALL");
  const [teamToDelete, setTeamToDelete] = useState<string | null>(null);

  // Fetch teams
  const { data, isLoading } = useTeams(organizationId);

  // Delete team mutation
  const deleteTeamMutation = useDeleteTeam();

  // Filter teams based on search and active tab
  const teams = data || [];
  const filteredTeams = teams;

  // Handle team deletion
  const handleDeleteTeam = (teamId: string) => {
    setTeamToDelete(teamId);
  };

  const confirmDeleteTeam = () => {
    if (teamToDelete) {
      deleteTeamMutation.mutate(teamToDelete);
      setTeamToDelete(null);
    }
  };

  const cancelDeleteTeam = () => {
    setTeamToDelete(null);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Teams</h1>
          <p className="text-muted-foreground">
            Manage teams and their members within your organization.
          </p>
        </div>
        <Button asChild>
          <Link href={`/organizations/${organizationId}/teams/create`}>
            <Plus className="mr-2 h-4 w-4" />
            Create Team
          </Link>
        </Button>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search teams..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Tabs
          defaultValue="ALL"
          value={activeTab}
          onValueChange={(value) => setActiveTab(value)}
          className="w-full md:w-auto"
        >
          <TabsList className="grid grid-cols-3 w-full md:w-auto">
            <TabsTrigger value="ALL">All</TabsTrigger>
            <TabsTrigger value="true">Active</TabsTrigger>
            <TabsTrigger value="false">Inactive</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      ) : filteredTeams.length === 0 ? (
        <EmptyState
          icon={<Users className="h-10 w-10 text-muted-foreground" />}
          title={searchQuery ? "No teams found" : "No teams"}
          description={
            searchQuery
              ? `No teams match "${searchQuery}"`
              : "You haven't created any teams yet."
          }
          action={
            <Button asChild>
              <Link href={`/organizations/${organizationId}/teams/create`}>
                <Plus className="mr-2 h-4 w-4" />
                Create Team
              </Link>
            </Button>
          }
        />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTeams.map((team: any) => (
            <TeamCard
              key={team.id}
              team={team}
              onDelete={handleDeleteTeam}
            />
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!teamToDelete} onOpenChange={(open) => !open && setTeamToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the team
              and remove all members from it.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteTeam}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTeam}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
