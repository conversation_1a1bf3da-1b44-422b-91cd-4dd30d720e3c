"use client";

import { useState } from "react";
import { useBatch<PERSON>nalyze, DocumentAnalysisResult } from "@/hooks/ai";

// Define types locally since they're not exported
type AnalysisType = "COMPREHENSIVE" | "PATTERN_DETECTION" | "RELATIONSHIP_MAPPING" | "INSIGHT_GENERATION" | "ANOMALY_DETECTION" | "TREND_ANALYSIS";

interface DocumentAnalysisOptions {
  depth: "basic" | "standard" | "deep";
  includeVisualizations: boolean;
  compareDocuments: boolean;
  organizationId?: string;
  projectId?: string;
}
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { FileText, Search, Lightbulb, Network } from "lucide-react";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorDisplay } from "@/components/ui/error-display";
import { DocumentVisualization } from "./document-visualization";

interface DocumentAnalysisProps {
  documentIds: string[];
  documentNames?: string[];
  organizationId?: string;
  projectId?: string;
  onAnalysisComplete?: (result: any) => void;
}

export function DocumentAnalysis({
  documentIds,
  documentNames,
  organizationId,
  projectId,
  onAnalysisComplete,
}: DocumentAnalysisProps) {
  const [analysisType, setAnalysisType] = useState<AnalysisType>("COMPREHENSIVE");
  const [depth, setDepth] = useState<"basic" | "standard" | "deep">("standard");
  const [includeVisualizations, setIncludeVisualizations] = useState(true);
  const [compareDocuments, setCompareDocuments] = useState(documentIds.length > 1);
  const [activeTab, setActiveTab] = useState("summary");

  // Use the batch analyze hook
  const batchAnalyze = useBatchAnalyze();

  // Create a custom hook interface
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isLoadingResult, setIsLoadingResult] = useState(false);
  const [error, setError] = useState<any>(null);
  const [isError, setIsError] = useState(false);

  const analyzeDocuments = async (documentIds: string[], analysisType: AnalysisType, options: DocumentAnalysisOptions) => {
    setIsAnalyzing(true);
    setError(null);
    setIsError(false);

    try {
      const result = await batchAnalyze.mutateAsync({
        documentIds,
        analysisTypes: [analysisType],
        options
      });

      // Set real analysis result from API response
      if (result && typeof result === 'object') {
        setAnalysisResult({
          summary: result.summary || "Analysis completed successfully",
          analysisType,
          documentIds,
          patterns: result.patterns || [],
          relationships: result.relationships || [],
          insights: result.insights || [],
          visualizations: result.visualizations || [],
          confidence: result.confidence || 0,
          processingTime: result.processingTime || 0,
          metadata: result.metadata || {}
        });
      } else {
        // Fallback structure if result is not properly formatted
        setAnalysisResult({
          summary: "Analysis completed but results may be incomplete",
          analysisType,
          documentIds,
          patterns: [],
          relationships: [],
          insights: [],
          visualizations: [],
          confidence: 0,
          processingTime: 0,
          metadata: {}
        });
      }

      return result;
    } catch (err) {
      setError(err);
      setIsError(true);
      throw err;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const clearAnalysisCache = () => {
    setAnalysisResult(null);
    setError(null);
    setIsError(false);
  };

  // Handle start analysis
  const handleStartAnalysis = async () => {
    try {
      const options: DocumentAnalysisOptions = {
        depth,
        includeVisualizations,
        compareDocuments: compareDocuments && documentIds.length > 1,
        organizationId,
        projectId,
      };

      const result = await analyzeDocuments(documentIds, analysisType, options);

      if (result && onAnalysisComplete) {
        onAnalysisComplete(result);
      }
    } catch (error) {
      console.error("Failed to start document analysis", error);
    }
  };

  // Get document names for display
  const getDocumentNames = () => {
    if (documentNames && documentNames.length === documentIds.length) {
      return documentNames;
    }
    return documentIds.map(id => `Document ${id.substring(0, 8)}...`);
  };

  // Render loading state
  if (isAnalyzing || isLoadingResult) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Document Analysis
          </CardTitle>
          <CardDescription>
            Analyzing {documentIds.length} document{documentIds.length !== 1 ? "s" : ""}
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <LoadingState
            title="Analyzing documents..."
            description="This may take a few moments depending on the complexity of the documents."
            variant="spinner"
          />
          <Progress value={45} className="w-full max-w-md mt-6" />
        </CardContent>
      </Card>
    );
  }

  // Render error state
  if (isError) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Document Analysis
          </CardTitle>
          <CardDescription>
            Failed to analyze {documentIds.length} document{documentIds.length !== 1 ? "s" : ""}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ErrorDisplay
            title="Analysis Failed"
            description="There was an error analyzing the documents."
            error={error}
            variant="card"
          />
        </CardContent>
        <CardFooter>
          <Button onClick={handleStartAnalysis} variant="outline">
            Try Again
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Render analysis configuration if no result yet
  if (!analysisResult) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Document Analysis
          </CardTitle>
          <CardDescription>
            Analyze {documentIds.length} document{documentIds.length !== 1 ? "s" : ""} to extract insights
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="analysis-type">Analysis Type</Label>
            <Select
              value={analysisType}
              onValueChange={(value) => setAnalysisType(value as AnalysisType)}
            >
              <SelectTrigger id="analysis-type">
                <SelectValue placeholder="Select analysis type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="COMPREHENSIVE">Comprehensive Analysis</SelectItem>
                <SelectItem value="PATTERN_DETECTION">Pattern Detection</SelectItem>
                <SelectItem value="RELATIONSHIP_MAPPING">Relationship Mapping</SelectItem>
                <SelectItem value="INSIGHT_GENERATION">Insight Generation</SelectItem>
                <SelectItem value="ANOMALY_DETECTION">Anomaly Detection</SelectItem>
                <SelectItem value="TREND_ANALYSIS">Trend Analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="analysis-depth">Analysis Depth</Label>
            <Select
              value={depth}
              onValueChange={(value) => setDepth(value as "basic" | "standard" | "deep")}
            >
              <SelectTrigger id="analysis-depth">
                <SelectValue placeholder="Select analysis depth" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="basic">Basic (Faster)</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="deep">Deep (More Thorough)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2 pt-2">
            <Checkbox
              id="include-visualizations"
              checked={includeVisualizations}
              onCheckedChange={(checked) => setIncludeVisualizations(checked as boolean)}
            />
            <Label htmlFor="include-visualizations">Include Visualizations</Label>
          </div>

          {documentIds.length > 1 && (
            <div className="flex items-center space-x-2">
              <Checkbox
                id="compare-documents"
                checked={compareDocuments}
                onCheckedChange={(checked) => setCompareDocuments(checked as boolean)}
              />
              <Label htmlFor="compare-documents">Compare Documents</Label>
            </div>
          )}

          <div className="pt-2">
            <h3 className="text-sm font-medium mb-2">Documents to Analyze:</h3>
            <div className="flex flex-wrap gap-2">
              {getDocumentNames().map((name, index) => (
                <Badge key={index} variant="outline">
                  {name}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleStartAnalysis}>
            Start Analysis
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Render analysis results
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="mr-2 h-5 w-5" />
          Document Analysis Results
        </CardTitle>
        <CardDescription>
          Analysis of {documentIds.length} document{documentIds.length !== 1 ? "s" : ""}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-5 mb-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="patterns">Patterns</TabsTrigger>
            <TabsTrigger value="relationships">Relationships</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="visualizations">Visualizations</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <div className="p-4 border rounded-md bg-muted/50">
              <p className="text-sm">{analysisResult.summary}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardHeader className="py-3">
                  <CardTitle className="text-sm flex items-center">
                    <Search className="h-4 w-4 mr-2" />
                    Analysis Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm font-medium">{analysisResult.analysisType}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="py-3">
                  <CardTitle className="text-sm flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    Documents Analyzed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm font-medium">{analysisResult.documentIds.length}</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="patterns">
            {analysisResult.patterns && analysisResult.patterns.length > 0 ? (
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {analysisResult.patterns.map((pattern: any) => (
                    <Card key={pattern.id}>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm flex items-center justify-between">
                          <span className="flex items-center">
                            <Search className="h-4 w-4 mr-2" />
                            {pattern.name}
                          </span>
                          <Badge variant="outline">
                            Confidence: {Math.round(pattern.confidence * 100)}%
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="py-2">
                        <p className="text-sm">{pattern.description}</p>
                        <p className="text-xs text-muted-foreground mt-2">
                          Occurrences: {pattern.occurrences}
                        </p>
                        {pattern.examples.length > 0 && (
                          <div className="mt-2">
                            <p className="text-xs font-medium">Examples:</p>
                            <ul className="text-xs mt-1 space-y-1">
                              {pattern.examples.slice(0, 3).map((example: any, i: number) => (
                                <li key={i} className="text-muted-foreground">
                                  "{example.length > 100 ? example.substring(0, 100) + '...' : example}"
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground">No patterns detected</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="relationships">
            {analysisResult.relationships && analysisResult.relationships.length > 0 ? (
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {analysisResult.relationships.map((relationship: any) => (
                    <Card key={`${relationship.source.id}-${relationship.target.id}`}>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm flex items-center justify-between">
                          <span className="flex items-center">
                            <Network className="h-4 w-4 mr-2" />
                            {relationship.type}
                          </span>
                          <Badge variant="outline">
                            Strength: {Math.round(relationship.strength * 100)}%
                          </Badge>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="py-2">
                        <div className="flex justify-between text-sm">
                          <div>
                            <span className="font-medium">From:</span>{" "}
                            <span className="text-muted-foreground">{relationship.source.name}</span>
                            <span className="text-xs text-muted-foreground ml-1">
                              ({relationship.source.type})
                            </span>
                          </div>
                          <div>
                            <span className="font-medium">To:</span>{" "}
                            <span className="text-muted-foreground">{relationship.target.name}</span>
                            <span className="text-xs text-muted-foreground ml-1">
                              ({relationship.target.type})
                            </span>
                          </div>
                        </div>
                        <p className="text-sm mt-2">{relationship.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground">No relationships mapped</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="insights">
            {analysisResult.insights && analysisResult.insights.length > 0 ? (
              <ScrollArea className="h-[400px]">
                <div className="space-y-4">
                  {analysisResult.insights.map((insight: any) => (
                    <Card key={insight.id}>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm flex items-center justify-between">
                          <span className="flex items-center">
                            <Lightbulb className="h-4 w-4 mr-2" />
                            {insight.title}
                          </span>
                          <div className="flex space-x-2">
                            <Badge variant={
                              insight.importance === 'critical' ? 'destructive' :
                              insight.importance === 'high' ? 'default' :
                              insight.importance === 'medium' ? 'secondary' : 'outline'
                            }>
                              {insight.importance}
                            </Badge>
                            <Badge variant="outline">
                              {insight.category}
                            </Badge>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="py-2">
                        <p className="text-sm">{insight.description}</p>

                        {insight.actionItems && insight.actionItems.length > 0 && (
                          <div className="mt-3">
                            <p className="text-xs font-medium">Suggested Actions:</p>
                            <ul className="text-xs mt-1 space-y-1">
                              {insight.actionItems.map((action: any, i: number) => (
                                <li key={i} className="flex items-start">
                                  <span className="mr-2">•</span>
                                  <span>{action}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground">No insights generated</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="visualizations">
            {analysisResult.visualizations && analysisResult.visualizations.length > 0 ? (
              <ScrollArea className="h-[400px]">
                <div className="space-y-6">
                  {analysisResult.visualizations.map((visualization: any) => (
                    <Card key={visualization.id}>
                      <CardHeader>
                        <CardTitle className="text-sm">{visualization.title}</CardTitle>
                        <CardDescription>{visualization.description}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <DocumentVisualization visualization={visualization} />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center py-10">
                <p className="text-muted-foreground">No visualizations available</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => clearAnalysisCache()}>
          New Analysis
        </Button>
        <Button onClick={handleStartAnalysis}>
          Refresh Analysis
        </Button>
      </CardFooter>
    </Card>
  );
}
