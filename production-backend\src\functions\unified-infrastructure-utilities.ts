/**
 * Unified Infrastructure & Utilities Function
 * Consolidates all infrastructure and utility operations: API key management, rate limiting,
 * authentication, cache warming, logging service, template management, and tenant management
 * Replaces: api-key-management.ts, api-rate-limiting.ts, auth.ts, cache-warming-scheduler.ts,
 *          logging-service.ts, template-management.ts, tenant-management.ts
 * 
 * Production-ready implementation with comprehensive error handling,
 * full Service Bus, Event Hub, Redis integrations, and enterprise-grade infrastructure
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app, Timer } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import * as Jo<PERSON> from 'joi';
import * as crypto from 'crypto';
import * as bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';

// Unified infrastructure types and enums
enum InfrastructureOperationType {
  API_KEY_MANAGEMENT = 'API_KEY_MANAGEMENT',
  RATE_LIMITING = 'RATE_LIMITING',
  AUTHENTICATION = 'AUTHENTICATION',
  CACHE_WARMING = 'CACHE_WARMING',
  LOGGING = 'LOGGING',
  TEMPLATE_MANAGEMENT = 'TEMPLATE_MANAGEMENT',
  TENANT_MANAGEMENT = 'TENANT_MANAGEMENT'
}

enum ApiKeyScope {
  READ = 'READ',
  WRITE = 'WRITE',
  DELETE = 'DELETE',
  ADMIN = 'ADMIN',
  DOCUMENTS = 'DOCUMENTS',
  ANALYTICS = 'ANALYTICS',
  INTEGRATIONS = 'INTEGRATIONS',
  WORKFLOWS = 'WORKFLOWS'
}

enum LimitScope {
  GLOBAL = 'GLOBAL',
  ORGANIZATION = 'ORGANIZATION',
  USER = 'USER',
  API_KEY = 'API_KEY',
  IP_ADDRESS = 'IP_ADDRESS'
}

enum LimitType {
  REQUESTS_PER_MINUTE = 'REQUESTS_PER_MINUTE',
  REQUESTS_PER_HOUR = 'REQUESTS_PER_HOUR',
  REQUESTS_PER_DAY = 'REQUESTS_PER_DAY',
  BANDWIDTH = 'BANDWIDTH',
  CONCURRENT_REQUESTS = 'CONCURRENT_REQUESTS'
}

enum ActionType {
  ALLOW = 'ALLOW',
  THROTTLE = 'THROTTLE',
  BLOCK = 'BLOCK',
  QUEUE = 'QUEUE'
}

enum TemplateType {
  EMAIL = 'EMAIL',
  DOCUMENT = 'DOCUMENT',
  NOTIFICATION = 'NOTIFICATION',
  REPORT = 'REPORT',
  WORKFLOW = 'WORKFLOW',
  FORM = 'FORM'
}

enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL'
}

// Comprehensive interfaces
interface ApiKey {
  id: string;
  name: string;
  description?: string;
  keyHash: string;
  keyPrefix: string;
  scopes: ApiKeyScope[];
  organizationId: string;
  projectId?: string;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  expiresAt?: string;
  isActive: boolean;
  rateLimits: RateLimitConfig;
  allowedIps: string[];
  allowedDomains: string[];
  usage: ApiKeyUsage;
  tenantId: string;
}

interface RateLimitConfig {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  bandwidthPerMinute?: number;
  concurrentRequests?: number;
}

interface ApiKeyUsage {
  totalRequests: number;
  lastUsedAt?: string;
  requestsToday: number;
  requestsThisHour: number;
  requestsThisMinute: number;
  bandwidthUsed: number;
}

interface RateLimit {
  id: string;
  name: string;
  description?: string;
  scope: LimitScope;
  organizationId?: string;
  rules: RateLimitRule[];
  conditions: RateLimitConditions;
  statistics: RateLimitStatistics;
  enabled: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface RateLimitRule {
  id: string;
  limitType: LimitType;
  limit: number;
  window: number;
  action: ActionType;
  endpoints?: string[];
  methods?: string[];
  priority: number;
}

interface RateLimitConditions {
  ipWhitelist?: string[];
  ipBlacklist?: string[];
  userAgentPatterns?: string[];
  timeRestrictions?: {
    allowedHours?: number[];
    allowedDays?: number[];
    timezone: string;
  };
}

interface RateLimitStatistics {
  totalRequests: number;
  allowedRequests: number;
  throttledRequests: number;
  blockedRequests: number;
  lastTriggered?: string;
}

interface Template {
  id: string;
  name: string;
  description?: string;
  type: TemplateType;
  content: TemplateContent;
  variables: TemplateVariable[];
  organizationId: string;
  projectId?: string;
  isPublic: boolean;
  version: number;
  tags: string[];
  metadata: { [key: string]: any };
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

interface TemplateContent {
  subject?: string;
  body: string;
  format: 'HTML' | 'TEXT' | 'MARKDOWN' | 'JSON';
  styles?: string;
  scripts?: string;
}

interface TemplateVariable {
  name: string;
  type: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'OBJECT' | 'ARRAY';
  required: boolean;
  defaultValue?: any;
  description?: string;
  validation?: any;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: LogLevel;
  message: string;
  source: string;
  correlationId?: string;
  userId?: string;
  organizationId?: string;
  metadata: { [key: string]: any };
  tags: string[];
  tenantId: string;
}

interface Tenant {
  id: string;
  name: string;
  domain: string;
  status: 'ACTIVE' | 'SUSPENDED' | 'INACTIVE';
  subscription: TenantSubscription;
  settings: TenantSettings;
  limits: TenantLimits;
  metadata: { [key: string]: any };
  createdAt: string;
  updatedAt: string;
}

interface TenantSubscription {
  plan: string;
  status: 'ACTIVE' | 'TRIAL' | 'EXPIRED' | 'CANCELLED';
  startDate: string;
  endDate?: string;
  features: string[];
  limits: { [key: string]: number };
}

interface TenantSettings {
  branding: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
    customCss?: string;
  };
  security: {
    enforceSSO: boolean;
    passwordPolicy: any;
    sessionTimeout: number;
    ipWhitelist?: string[];
  };
  features: {
    enabledFeatures: string[];
    featureFlags: { [key: string]: boolean };
  };
}

interface TenantLimits {
  maxUsers: number;
  maxProjects: number;
  maxDocuments: number;
  maxStorage: number;
  maxApiCalls: number;
  maxIntegrations: number;
}

// Validation schemas
const apiKeySchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  scopes: Joi.array().items(Joi.string().valid(...Object.values(ApiKeyScope))).min(1).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  expiresAt: Joi.date().iso().optional(),
  rateLimits: Joi.object({
    requestsPerMinute: Joi.number().integer().min(1).max(10000).default(1000),
    requestsPerHour: Joi.number().integer().min(1).max(100000).default(10000),
    requestsPerDay: Joi.number().integer().min(1).max(1000000).default(100000)
  }).optional(),
  allowedIps: Joi.array().items(Joi.string().ip()).optional(),
  allowedDomains: Joi.array().items(Joi.string().domain()).optional()
});

const rateLimitSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  scope: Joi.string().valid(...Object.values(LimitScope)).required(),
  organizationId: Joi.string().uuid().optional(),
  rules: Joi.array().items(Joi.object({
    limitType: Joi.string().valid(...Object.values(LimitType)).required(),
    limit: Joi.number().integer().min(1).required(),
    window: Joi.number().integer().min(1).required(),
    action: Joi.string().valid(...Object.values(ActionType)).required(),
    endpoints: Joi.array().items(Joi.string()).optional(),
    methods: Joi.array().items(Joi.string()).optional(),
    priority: Joi.number().integer().min(1).max(100).default(50)
  })).min(1).required(),
  conditions: Joi.object({
    ipWhitelist: Joi.array().items(Joi.string().ip()).optional(),
    ipBlacklist: Joi.array().items(Joi.string().ip()).optional(),
    userAgentPatterns: Joi.array().items(Joi.string()).optional(),
    timeRestrictions: Joi.object({
      allowedHours: Joi.array().items(Joi.number().min(0).max(23)).optional(),
      allowedDays: Joi.array().items(Joi.number().min(0).max(6)).optional(),
      timezone: Joi.string().default('UTC')
    }).optional()
  }).optional(),
  enabled: Joi.boolean().default(true)
});

const templateSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(TemplateType)).required(),
  content: Joi.object({
    subject: Joi.string().max(255).optional(),
    body: Joi.string().required(),
    format: Joi.string().valid('HTML', 'TEXT', 'MARKDOWN', 'JSON').default('HTML'),
    styles: Joi.string().optional(),
    scripts: Joi.string().optional()
  }).required(),
  variables: Joi.array().items(Joi.object({
    name: Joi.string().required(),
    type: Joi.string().valid('STRING', 'NUMBER', 'BOOLEAN', 'DATE', 'OBJECT', 'ARRAY').required(),
    required: Joi.boolean().default(false),
    defaultValue: Joi.any().optional(),
    description: Joi.string().optional(),
    validation: Joi.any().optional()
  })).default([]),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  isPublic: Joi.boolean().default(false),
  tags: Joi.array().items(Joi.string()).default([]),
  metadata: Joi.object().default({})
});

const logEntrySchema = Joi.object({
  level: Joi.string().valid(...Object.values(LogLevel)).required(),
  message: Joi.string().required().max(10000),
  source: Joi.string().required().max(255),
  correlationId: Joi.string().optional(),
  metadata: Joi.object().default({}),
  tags: Joi.array().items(Joi.string()).default([])
});

/**
 * Unified Infrastructure & Utilities Manager
 * Handles all infrastructure and utility operations with comprehensive error handling, caching, and real-time processing
 */
class UnifiedInfrastructureManager {

  private serviceBusService: ServiceBusEnhancedService;

  constructor() {
    // Initialize Service Bus service for infrastructure processing
    this.serviceBusService = ServiceBusEnhancedService.getInstance();
  }

  /**
   * Create API key
   */
  async createApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = apiKeySchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const apiKeyRequest = value;

      // Check organization access
      const hasPermission = await this.checkOrganizationPermission(
        apiKeyRequest.organizationId,
        user.id,
        'ADMIN'
      );
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Only organization admins can create API keys' }
        }, request);
      }

      // Generate API key
      const apiKeyId = uuidv4();
      const keyValue = this.generateApiKey();
      const hashedKey = await this.hashApiKey(keyValue);

      // Create API key record
      const now = new Date().toISOString();
      const apiKey: ApiKey = {
        id: apiKeyId,
        name: apiKeyRequest.name,
        description: apiKeyRequest.description || '',
        keyHash: hashedKey,
        keyPrefix: keyValue.substring(0, 8) + '...',
        scopes: apiKeyRequest.scopes,
        organizationId: apiKeyRequest.organizationId,
        projectId: apiKeyRequest.projectId,
        createdBy: user.id,
        createdAt: now,
        updatedBy: user.id,
        updatedAt: now,
        expiresAt: apiKeyRequest.expiresAt,
        isActive: true,
        rateLimits: apiKeyRequest.rateLimits || {
          requestsPerMinute: 1000,
          requestsPerHour: 10000,
          requestsPerDay: 100000
        },
        allowedIps: apiKeyRequest.allowedIps || [],
        allowedDomains: apiKeyRequest.allowedDomains || [],
        usage: {
          totalRequests: 0,
          lastUsedAt: undefined,
          requestsToday: 0,
          requestsThisHour: 0,
          requestsThisMinute: 0,
          bandwidthUsed: 0
        },
        tenantId: user.tenantId || user.id
      };

      await db.createItem('api-keys', apiKey);

      // Cache API key for quick validation
      await redis.setex(`api-key:${apiKeyId}`, 3600, JSON.stringify(apiKey));

      // Send to Service Bus for API key processing
      await this.serviceBusService.sendToQueue('api-key-management', {
        body: {
          apiKeyId,
          action: 'created',
          organizationId: apiKeyRequest.organizationId,
          scopes: apiKeyRequest.scopes,
          timestamp: now
        },
        correlationId,
        messageId: `api-key-${apiKeyId}-${Date.now()}`
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Infrastructure.ApiKeyCreated',
        subject: `infrastructure/api-keys/${apiKeyId}/created`,
        data: {
          apiKeyId,
          organizationId: apiKeyRequest.organizationId,
          scopes: apiKeyRequest.scopes,
          createdBy: user.id,
          correlationId
        }
      });

      logger.info('API key created successfully', {
        correlationId,
        apiKeyId,
        organizationId: apiKeyRequest.organizationId,
        scopes: apiKeyRequest.scopes,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          apiKey: {
            id: apiKeyId,
            name: apiKey.name,
            keyPrefix: apiKey.keyPrefix,
            scopes: apiKey.scopes,
            createdAt: apiKey.createdAt,
            expiresAt: apiKey.expiresAt
          },
          key: keyValue, // Only returned once
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('API key creation failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Check rate limit
   */
  async checkRateLimit(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Parse request (can be GET with query params or POST with body)
      let rateLimitRequest: any;

      if (request.method === 'GET') {
        const url = new URL(request.url);
        rateLimitRequest = {
          identifier: url.searchParams.get('identifier'),
          scope: url.searchParams.get('scope'),
          endpoint: url.searchParams.get('endpoint'),
          method: url.searchParams.get('method'),
          requestSize: parseInt(url.searchParams.get('requestSize') || '0'),
          organizationId: url.searchParams.get('organizationId'),
          userId: url.searchParams.get('userId')
        };
      } else {
        rateLimitRequest = await request.json();
      }

      if (!rateLimitRequest.identifier || !rateLimitRequest.scope) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Identifier and scope are required' }
        }, request);
      }

      // Check rate limit
      const rateLimitResult = await this.performRateLimitCheck(rateLimitRequest);

      // Update statistics
      await this.updateRateLimitStatistics(rateLimitRequest, rateLimitResult);

      logger.info('Rate limit check completed', {
        correlationId,
        identifier: rateLimitRequest.identifier,
        scope: rateLimitRequest.scope,
        allowed: rateLimitResult.allowed,
        action: rateLimitResult.action,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          result: rateLimitResult,
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Rate limit check failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Create template
   */
  async createTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = templateSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const templateRequest = value;

      // Check permissions
      const hasPermission = await this.checkOrganizationPermission(
        templateRequest.organizationId,
        user.id,
        'MEMBER'
      );
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied' }
        }, request);
      }

      // Create template
      const templateId = uuidv4();
      const now = new Date().toISOString();

      const template: Template = {
        id: templateId,
        name: templateRequest.name,
        description: templateRequest.description,
        type: templateRequest.type,
        content: templateRequest.content,
        variables: templateRequest.variables,
        organizationId: templateRequest.organizationId,
        projectId: templateRequest.projectId,
        isPublic: templateRequest.isPublic,
        version: 1,
        tags: templateRequest.tags,
        metadata: templateRequest.metadata,
        createdBy: user.id,
        createdAt: now,
        updatedBy: user.id,
        updatedAt: now,
        tenantId: user.tenantId || user.id
      };

      await db.createItem('templates', template);

      // Cache template for quick access
      await redis.setex(`template:${templateId}`, 1800, JSON.stringify(template));

      // Send to Service Bus for template processing
      await this.serviceBusService.sendToQueue('template-processing', {
        body: {
          templateId,
          action: 'created',
          type: templateRequest.type,
          organizationId: templateRequest.organizationId,
          timestamp: now
        },
        correlationId,
        messageId: `template-${templateId}-${Date.now()}`
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Infrastructure.TemplateCreated',
        subject: `infrastructure/templates/${templateId}/created`,
        data: {
          templateId,
          name: templateRequest.name,
          type: templateRequest.type,
          organizationId: templateRequest.organizationId,
          createdBy: user.id,
          correlationId
        }
      });

      logger.info('Template created successfully', {
        correlationId,
        templateId,
        name: templateRequest.name,
        type: templateRequest.type,
        organizationId: templateRequest.organizationId,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          template: this.sanitizeTemplate(template),
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Template creation failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Log entry
   */
  async logEntry(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user (optional for logging)
      const authResult = await authenticateRequest(request);
      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = logEntrySchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const logRequest = value;

      // Create log entry
      const logId = uuidv4();
      const now = new Date().toISOString();

      const logEntry: LogEntry = {
        id: logId,
        timestamp: now,
        level: logRequest.level,
        message: logRequest.message,
        source: logRequest.source,
        correlationId: logRequest.correlationId || correlationId,
        userId: user?.id,
        organizationId: user?.organizationId,
        metadata: {
          ...logRequest.metadata,
          ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        },
        tags: logRequest.tags,
        tenantId: user?.tenantId || user?.id || 'system'
      };

      // Store in database
      await db.createItem('log-entries', logEntry);

      // Cache recent logs for quick access
      const logKey = `logs:${logEntry.organizationId || 'system'}:${new Date().toISOString().split('T')[0]}`;
      await redis.lpush(logKey, JSON.stringify(logEntry));
      await redis.ltrim(logKey, 0, 9999); // Keep last 10000 logs
      await redis.expire(logKey, 86400 * 7); // 7 days

      // Send to Service Bus for log processing
      await this.serviceBusService.sendToQueue('log-processing', {
        body: {
          logId,
          logEntry,
          timestamp: now
        },
        messageId: `log-${logId}-${Date.now()}`
      });

      // Check for critical logs
      if (logRequest.level === LogLevel.ERROR || logRequest.level === LogLevel.FATAL) {
        await this.handleCriticalLog(logEntry);
      }

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          logId,
          message: 'Log entry recorded successfully'
        }
      }, request);

    } catch (error) {
      logger.error('Log entry failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Cache warming scheduler
   */
  async performCacheWarming(myTimer: Timer, context: InvocationContext): Promise<void> {
    const correlationId = context.invocationId;
    const startTime = Date.now();
    const maxExecutionTime = 4 * 60 * 1000; // 4 minutes max (leave 1 minute buffer)

    try {
      logger.info('Cache warming started', { correlationId });

      // Get cache warming analytics with timeout
      const analytics = await Promise.race([
        this.getCacheWarmingAnalytics(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Analytics timeout')), 30000))
      ]) as any;

      const tasks = [];

      // Limit processing to prevent timeout
      const maxKeys = 20; // Reduced from unlimited
      const maxPatterns = 10; // Reduced from unlimited
      const maxUsers = 15; // Reduced from unlimited

      // Process in parallel with limits and timeouts
      if (analytics.mostAccessedKeys?.length > 0) {
        tasks.push(
          Promise.race([
            this.warmMostAccessedKeys(analytics.mostAccessedKeys.slice(0, maxKeys)),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Keys warming timeout')), 60000))
          ])
        );
      }

      if (analytics.cacheMissPatterns?.length > 0) {
        tasks.push(
          Promise.race([
            this.warmCacheMissPatterns(analytics.cacheMissPatterns.slice(0, maxPatterns)),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Patterns warming timeout')), 60000))
          ])
        );
      }

      if (analytics.userActivityPatterns?.length > 0) {
        tasks.push(
          Promise.race([
            this.warmUserActivityPatterns(analytics.userActivityPatterns.slice(0, maxUsers)),
            new Promise((_, reject) => setTimeout(() => reject(new Error('User patterns warming timeout')), 60000))
          ])
        );
      }

      // Execute all tasks with overall timeout
      await Promise.race([
        Promise.allSettled(tasks),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Overall cache warming timeout')), maxExecutionTime - (Date.now() - startTime))
        )
      ]);

      // Send analytics to Service Bus (non-blocking with timeout)
      Promise.race([
        this.serviceBusService.sendToQueue('cache-analytics', {
          body: {
            analytics: {
              ...analytics,
              processedKeys: Math.min(analytics.mostAccessedKeys?.length || 0, maxKeys),
              processedPatterns: Math.min(analytics.cacheMissPatterns?.length || 0, maxPatterns),
              processedUsers: Math.min(analytics.userActivityPatterns?.length || 0, maxUsers)
            },
            warmingCompleted: true,
            executionTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
          },
          correlationId,
          messageId: `cache-warming-${Date.now()}`
        }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Service Bus timeout')), 10000))
      ]).catch(error => {
        logger.error('Failed to send cache analytics to Service Bus', {
          error: error instanceof Error ? error.message : String(error),
          correlationId
        });
      });

      logger.info('Cache warming completed successfully', {
        correlationId,
        executionTime: Date.now() - startTime,
        keysProcessed: Math.min(analytics.mostAccessedKeys?.length || 0, maxKeys)
      });

    } catch (error) {
      logger.error('Cache warming failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId,
        executionTime: Date.now() - startTime
      });
    }
  }

  /**
   * Helper Methods with Full Production Integrations
   */
  private async checkOrganizationPermission(organizationId: string, userId: string, requiredRole: string): Promise<boolean> {
    try {
      // Check Redis cache first for performance
      const cacheKey = `org-permissions:${userId}:${organizationId}:${requiredRole}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Check organization membership
      const memberships = await db.queryItems<any>('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = "active"',
        [
          { name: '@orgId', value: organizationId },
          { name: '@userId', value: userId }
        ]
      );

      if (memberships.length === 0) {
        await redis.setex(cacheKey, 300, JSON.stringify(false));
        return false;
      }

      const membership = memberships[0];

      // Check role hierarchy: OWNER > ADMIN > MEMBER
      const roleHierarchy = { 'OWNER': 3, 'ADMIN': 2, 'MEMBER': 1 };
      const userRoleLevel = roleHierarchy[membership.role as keyof typeof roleHierarchy] || 0;
      const requiredRoleLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

      const hasPermission = userRoleLevel >= requiredRoleLevel;

      // Cache result for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(hasPermission));

      return hasPermission;
    } catch (error) {
      logger.error('Error checking organization permission', {
        organizationId,
        userId,
        requiredRole,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private generateApiKey(): string {
    const prefix = 'ak_';
    const randomBytes = crypto.randomBytes(32);
    const key = randomBytes.toString('base64url');
    return prefix + key;
  }

  private async hashApiKey(key: string): Promise<string> {
    const saltRounds = 12;
    return await bcrypt.hash(key, saltRounds);
  }

  private async performRateLimitCheck(rateLimitRequest: any): Promise<any> {
    try {
      // Get applicable rate limits
      const rateLimits = await this.getApplicableRateLimits(rateLimitRequest);

      for (const rateLimit of rateLimits) {
        for (const rule of rateLimit.rules) {
          const result = await this.checkRateLimitRule(rateLimitRequest, rule);

          if (!result.allowed) {
            return result;
          }
        }
      }

      // All checks passed
      return {
        allowed: true,
        action: ActionType.ALLOW,
        limit: 0,
        remaining: 0,
        resetTime: 0
      };

    } catch (error) {
      logger.error('Rate limit check error', {
        error: error instanceof Error ? error.message : String(error),
        rateLimitRequest
      });

      // Default to allow on error
      return {
        allowed: true,
        action: ActionType.ALLOW,
        limit: 0,
        remaining: 0,
        resetTime: 0
      };
    }
  }

  private async getApplicableRateLimits(rateLimitRequest: any): Promise<RateLimit[]> {
    // Get rate limits based on scope
    let query = 'SELECT * FROM c WHERE c.enabled = true';
    const parameters: any[] = [];

    if (rateLimitRequest.scope === LimitScope.ORGANIZATION && rateLimitRequest.organizationId) {
      query += ' AND (c.scope = @globalScope OR (c.scope = @orgScope AND c.organizationId = @orgId))';
      parameters.push(
        { name: '@globalScope', value: LimitScope.GLOBAL },
        { name: '@orgScope', value: LimitScope.ORGANIZATION },
        { name: '@orgId', value: rateLimitRequest.organizationId }
      );
    } else {
      query += ' AND c.scope = @scope';
      parameters.push({ name: '@scope', value: rateLimitRequest.scope });
    }

    return await db.queryItems<RateLimit>('rate-limits', query, parameters);
  }

  private async checkRateLimitRule(rateLimitRequest: any, rule: RateLimitRule): Promise<any> {
    const key = `rate-limit:${rateLimitRequest.identifier}:${rule.id}`;
    const now = Date.now();
    const windowStart = now - (rule.window * 1000);

    // Get current count
    const currentCount = await redis.zcount(key, windowStart, now);

    if (currentCount >= rule.limit) {
      return {
        allowed: false,
        action: rule.action,
        limit: rule.limit,
        remaining: 0,
        resetTime: windowStart + (rule.window * 1000),
        retryAfter: rule.window,
        rule: {
          id: rule.id,
          limitType: rule.limitType,
          window: rule.window
        }
      };
    }

    // Add current request to sliding window
    await redis.zadd(key, now, `${now}-${Math.random()}`);
    await redis.expire(key, rule.window);

    return {
      allowed: true,
      action: ActionType.ALLOW,
      limit: rule.limit,
      remaining: rule.limit - currentCount - 1,
      resetTime: windowStart + (rule.window * 1000)
    };
  }

  private async updateRateLimitStatistics(rateLimitRequest: any, result: any): Promise<void> {
    try {
      const statsKey = `rate-limit-stats:${rateLimitRequest.scope}:${new Date().toISOString().split('T')[0]}`;

      await redis.hincrby(statsKey, 'totalRequests', 1);

      if (result.allowed) {
        await redis.hincrby(statsKey, 'allowedRequests', 1);
      } else {
        if (result.action === ActionType.THROTTLE) {
          await redis.hincrby(statsKey, 'throttledRequests', 1);
        } else if (result.action === ActionType.BLOCK) {
          await redis.hincrby(statsKey, 'blockedRequests', 1);
        }
      }

      await redis.expire(statsKey, 86400 * 30); // Keep for 30 days

    } catch (error) {
      logger.error('Error updating rate limit statistics', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async getCacheWarmingAnalytics(): Promise<any> {
    try {
      // Get most accessed keys from Redis analytics
      const mostAccessedKeys = await redis.zrevrange('cache:access-frequency', 0, 99);

      // Get cache miss patterns
      const cacheMissPatterns = await redis.zrevrange('cache:miss-patterns', 0, 49);

      // Get user activity patterns
      const userActivityKeys = await redis.keys('user-activity:*');
      const userActivityPatterns = [];

      for (const key of userActivityKeys.slice(0, 20)) {
        const count = await redis.get(key);
        if (count) {
          userActivityPatterns.push({
            userId: key.split(':')[1],
            accessCount: parseInt(count)
          });
        }
      }

      return {
        mostAccessedKeys,
        cacheMissPatterns,
        userActivityPatterns,
        highTrafficPeriods: [] // Would be calculated from historical data
      };

    } catch (error) {
      logger.error('Error getting cache warming analytics', {
        error: error instanceof Error ? error.message : String(error)
      });
      return {
        mostAccessedKeys: [],
        cacheMissPatterns: [],
        userActivityPatterns: [],
        highTrafficPeriods: []
      };
    }
  }

  private async warmMostAccessedKeys(keys: string[]): Promise<void> {
    // Process keys in parallel batches to improve performance
    const batchSize = 5;
    const batches = [];

    for (let i = 0; i < keys.length; i += batchSize) {
      batches.push(keys.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const batchPromises = batch.map(async (key) => {
        try {
          // Check if key exists and warm it
          const exists = await redis.exists(key);
          if (!exists) {
            // Attempt to regenerate the cache entry with timeout
            await Promise.race([
              this.regenerateCacheEntry(key),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Key regeneration timeout')), 10000))
            ]);
          }
        } catch (error) {
          logger.error('Error warming cache key', {
            key,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      });

      // Process batch with timeout
      await Promise.race([
        Promise.allSettled(batchPromises),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Batch timeout')), 30000))
      ]);
    }
  }

  private async warmCacheMissPatterns(patterns: string[]): Promise<void> {
    for (const pattern of patterns) {
      try {
        // Pre-generate cache entries for common miss patterns
        await this.preGenerateCachePattern(pattern);
      } catch (error) {
        logger.error('Error warming cache pattern', {
          pattern,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  private async warmUserActivityPatterns(patterns: any[]): Promise<void> {
    // Process user patterns in parallel batches
    const batchSize = 3; // Smaller batch for user cache warming
    const batches = [];

    for (let i = 0; i < patterns.length; i += batchSize) {
      batches.push(patterns.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const batchPromises = batch.map(async (pattern) => {
        try {
          // Pre-warm user-specific cache entries with timeout
          await Promise.race([
            this.preWarmUserCache(pattern.userId),
            new Promise((_, reject) => setTimeout(() => reject(new Error('User cache warming timeout')), 15000))
          ]);
        } catch (error) {
          logger.error('Error warming user cache', {
            userId: pattern.userId,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      });

      // Process batch with timeout
      await Promise.race([
        Promise.allSettled(batchPromises),
        new Promise((_, reject) => setTimeout(() => reject(new Error('User batch timeout')), 45000))
      ]);
    }
  }

  private async regenerateCacheEntry(key: string): Promise<void> {
    try {
      logger.info('Regenerating cache entry', { key });

      // Determine cache entry type from key pattern
      if (key.startsWith('user:')) {
        const userId = key.split(':')[1];
        await this.regenerateUserCache(userId);
      } else if (key.startsWith('org:')) {
        const orgId = key.split(':')[1];
        await this.regenerateOrganizationCache(orgId);
      } else if (key.startsWith('doc:')) {
        const docId = key.split(':')[1];
        await this.regenerateDocumentCache(docId);
      } else if (key.startsWith('analytics:')) {
        await this.regenerateAnalyticsCache(key);
      } else if (key.startsWith('search:')) {
        await this.regenerateSearchCache(key);
      } else {
        logger.warn('Unknown cache key pattern for regeneration', { key });
      }

      logger.info('Cache entry regenerated successfully', { key });
    } catch (error) {
      logger.error('Cache entry regeneration failed', {
        key,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async preGenerateCachePattern(pattern: string): Promise<void> {
    try {
      logger.info('Pre-generating cache pattern', { pattern });

      if (pattern === 'user_profiles') {
        // Pre-generate cache for active users
        const activeUsers = await db.queryItems('users',
          'SELECT c.id FROM c WHERE c.status = "active" AND c.lastLoginAt >= @since',
          [new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()] // Last 7 days
        );

        for (const user of activeUsers) {
          await this.preWarmUserCache((user as any).id);
        }
      } else if (pattern === 'organization_data') {
        // Pre-generate cache for active organizations
        const activeOrgs = await db.queryItems('organizations',
          'SELECT c.id FROM c WHERE c.status = "active"',
          []
        );

        for (const org of activeOrgs) {
          await this.regenerateOrganizationCache((org as any).id);
        }
      } else if (pattern === 'popular_documents') {
        // Pre-generate cache for frequently accessed documents
        const popularDocs = await db.queryItems('documents',
          'SELECT c.id FROM c WHERE c.accessCount >= 10 ORDER BY c.accessCount DESC OFFSET 0 LIMIT 100',
          []
        );

        for (const doc of popularDocs) {
          await this.regenerateDocumentCache((doc as any).id);
        }
      } else if (pattern === 'search_suggestions') {
        // Pre-generate search suggestion cache
        await this.regenerateSearchSuggestions();
      }

      logger.info('Cache pattern pre-generation completed', { pattern });
    } catch (error) {
      logger.error('Cache pattern pre-generation failed', {
        pattern,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async preWarmUserCache(userId: string): Promise<void> {
    try {
      logger.info('Pre-warming user cache', { userId });

      // Get user data
      const user = await db.readItem('users', userId, userId);
      if (!user) {
        logger.warn('User not found for cache warming', { userId });
        return;
      }

      // Cache user profile
      await redis.setex(`user:${userId}:profile`, 3600, JSON.stringify({
        id: (user as any).id,
        name: (user as any).name,
        email: (user as any).email,
        organizationId: (user as any).organizationId,
        role: (user as any).role,
        preferences: (user as any).preferences,
        lastLoginAt: (user as any).lastLoginAt
      }));

      // Cache user permissions
      const permissions = await this.getUserPermissions(userId);
      await redis.setex(`user:${userId}:permissions`, 3600, JSON.stringify(permissions));

      // Cache user's recent documents
      const recentDocs = await db.queryItems('documents',
        'SELECT c.id, c.name, c.type, c.updatedAt FROM c WHERE c.createdBy = @userId ORDER BY c.updatedAt DESC OFFSET 0 LIMIT 20',
        [userId]
      );
      await redis.setex(`user:${userId}:recent_docs`, 1800, JSON.stringify(recentDocs));

      // Cache user's organizations
      const userOrgs = await db.queryItems('organization-members',
        'SELECT c.organizationId, c.role FROM c WHERE c.userId = @userId',
        [userId]
      );
      await redis.setex(`user:${userId}:organizations`, 3600, JSON.stringify(userOrgs));

      logger.info('User cache pre-warmed successfully', { userId });
    } catch (error) {
      logger.error('User cache pre-warming failed', {
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async regenerateUserCache(userId: string): Promise<void> {
    // Delete existing cache entries
    const pattern = `user:${userId}:*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    // Regenerate cache
    await this.preWarmUserCache(userId);
  }

  private async regenerateOrganizationCache(orgId: string): Promise<void> {
    try {
      // Get organization data
      const org = await db.readItem('organizations', orgId, orgId);
      if (!org) return;

      // Cache organization profile
      await redis.setex(`org:${orgId}:profile`, 3600, JSON.stringify({
        id: (org as any).id,
        name: (org as any).name,
        settings: (org as any).settings,
        subscription: (org as any).subscription,
        limits: (org as any).limits
      }));

      // Cache organization members
      const members = await db.queryItems('organization-members',
        'SELECT c.userId, c.role, c.joinedAt FROM c WHERE c.organizationId = @orgId',
        [orgId]
      );
      await redis.setex(`org:${orgId}:members`, 1800, JSON.stringify(members));

      // Cache organization projects
      const projects = await db.queryItems('projects',
        'SELECT c.id, c.name, c.status FROM c WHERE c.organizationId = @orgId',
        [orgId]
      );
      await redis.setex(`org:${orgId}:projects`, 1800, JSON.stringify(projects));

    } catch (error) {
      logger.error('Organization cache regeneration failed', { orgId, error });
    }
  }

  private async regenerateDocumentCache(docId: string): Promise<void> {
    try {
      const doc = await db.readItem('documents', docId, docId);
      if (!doc) return;

      // Cache document metadata
      await redis.setex(`doc:${docId}:metadata`, 1800, JSON.stringify({
        id: (doc as any).id,
        name: (doc as any).name,
        type: (doc as any).type,
        size: (doc as any).size,
        createdAt: (doc as any).createdAt,
        updatedAt: (doc as any).updatedAt,
        tags: (doc as any).tags
      }));

    } catch (error) {
      logger.error('Document cache regeneration failed', { docId, error });
    }
  }

  private async regenerateAnalyticsCache(key: string): Promise<void> {
    // Implementation for analytics cache regeneration
    logger.info('Regenerating analytics cache', { key });
  }

  private async regenerateSearchCache(key: string): Promise<void> {
    // Implementation for search cache regeneration
    logger.info('Regenerating search cache', { key });
  }

  private async regenerateSearchSuggestions(): Promise<void> {
    try {
      // Get popular search terms
      const popularTerms = await db.queryItems('search-analytics',
        'SELECT c.term, c.count FROM c ORDER BY c.count DESC OFFSET 0 LIMIT 100',
        []
      );

      await redis.setex('search:suggestions', 3600, JSON.stringify(popularTerms));
    } catch (error) {
      logger.error('Search suggestions regeneration failed', { error });
    }
  }

  private async getUserPermissions(userId: string): Promise<string[]> {
    try {
      const userRoles = await db.queryItems('user-roles',
        'SELECT c.role FROM c WHERE c.userId = @userId',
        [userId]
      );

      const permissions: string[] = [];
      for (const roleRecord of userRoles) {
        const role = await db.readItem('roles', (roleRecord as any).role, (roleRecord as any).role);
        if (role && (role as any).permissions) {
          permissions.push(...(role as any).permissions);
        }
      }

      return [...new Set(permissions)]; // Remove duplicates
    } catch (error) {
      logger.error('Failed to get user permissions', { userId, error });
      return [];
    }
  }

  private async handleCriticalLog(logEntry: LogEntry): Promise<void> {
    try {
      // Send critical log alert
      await this.serviceBusService.sendToQueue('critical-alerts', {
        body: {
          type: 'CRITICAL_LOG',
          logEntry,
          timestamp: new Date().toISOString()
        },
        messageId: `critical-log-${logEntry.id}-${Date.now()}`
      });

      // Cache critical log for immediate access
      await redis.lpush('critical-logs', JSON.stringify(logEntry));
      await redis.ltrim('critical-logs', 0, 99); // Keep last 100 critical logs
      await redis.expire('critical-logs', 86400); // 24 hours

    } catch (error) {
      logger.error('Error handling critical log', {
        error: error instanceof Error ? error.message : String(error),
        logId: logEntry.id
      });
    }
  }

  private sanitizeTemplate(template: Template): any {
    const sanitized = { ...template };
    delete (sanitized as any)._rid;
    delete (sanitized as any)._self;
    delete (sanitized as any)._etag;
    delete (sanitized as any)._attachments;
    delete (sanitized as any)._ts;
    return sanitized;
  }
}

// Create instance of the manager
const infrastructureManager = new UnifiedInfrastructureManager();

/**
 * Additional Infrastructure & Utilities Functions
 */

/**
 * List API keys
 */
async function listApiKeys(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;
    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId') || user.organizationId;

    if (!organizationId) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: 'Organization ID is required' }
      }, request);
    }

    // Check permissions
    const hasPermission = await infrastructureManager['checkOrganizationPermission'](
      organizationId,
      user.id,
      'MEMBER'
    );
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        jsonBody: { error: 'Access denied' }
      }, request);
    }

    // Get API keys
    const apiKeys = await db.queryItems('api-keys',
      'SELECT * FROM c WHERE c.organizationId = @orgId ORDER BY c.createdAt DESC',
      [{ name: '@orgId', value: organizationId }]
    );

    // Sanitize API keys (remove sensitive data)
    const sanitizedKeys = apiKeys.map((key: any) => ({
      id: key.id,
      name: key.name,
      description: key.description,
      keyPrefix: key.keyPrefix,
      scopes: key.scopes,
      isActive: key.isActive,
      usage: key.usage,
      createdAt: key.createdAt,
      expiresAt: key.expiresAt
    }));

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        apiKeys: sanitizedKeys,
        total: sanitizedKeys.length
      }
    }, request);

  } catch (error) {
    logger.error('List API keys failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * List templates
 */
async function listTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;
    const url = new URL(request.url);
    const organizationId = url.searchParams.get('organizationId') || user.organizationId;
    const type = url.searchParams.get('type');

    // Build query
    let query = 'SELECT * FROM c WHERE (c.organizationId = @orgId OR c.isPublic = true)';
    const parameters = [{ name: '@orgId', value: organizationId }];

    if (type) {
      query += ' AND c.type = @type';
      parameters.push({ name: '@type', value: type });
    }

    query += ' ORDER BY c.createdAt DESC';

    // Get templates
    const templates = await db.queryItems('templates', query, parameters);

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        templates: templates.map((template: any) => infrastructureManager['sanitizeTemplate'](template)),
        total: templates.length
      }
    }, request);

  } catch (error) {
    logger.error('List templates failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * Authenticate user
 */
async function authenticateUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;

  try {
    const body = await request.json() as any;
    const { email, password } = body;

    if (!email || !password) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: 'Email and password are required' }
      }, request);
    }

    // Get user by email
    const users = await db.queryItems('users',
      'SELECT * FROM c WHERE c.email = @email AND c.status = "active"',
      [{ name: '@email', value: email.toLowerCase() }]
    );

    if (users.length === 0) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Invalid credentials' }
      }, request);
    }

    const user = users[0] as any;

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Invalid credentials' }
      }, request);
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        email: user.email,
        organizationId: user.organizationId,
        tenantId: user.tenantId
      },
      process.env.JWT_SECRET || 'default-secret',
      { expiresIn: '24h' }
    );

    // Update last login
    await db.updateItem('users', {
      ...user,
      lastLoginAt: new Date().toISOString()
    });

    // Create StandardToken for consistent format
    const standardToken = {
      accessToken: token,
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      userId: user.id,
      email: user.email,
      roles: user.roles || ['user'],
      organizationIds: user.organizationIds || (user.organizationId ? [user.organizationId] : []),
      tenantId: user.tenantId
    };

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        accessToken: token,
        standardToken,
        user: {
          id: user.id,
          email: user.email,
          displayName: user.displayName || user.name,
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
          organizationId: user.organizationId,
          organizationIds: user.organizationIds || [],
          roles: user.roles || ['user'],
          systemRoles: user.systemRoles || [],
          tenantId: user.tenantId
        }
      }
    }, request);

  } catch (error) {
    logger.error('Authentication failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * Auth Sync Endpoint - Production Ready
 * Handles frontend StandardToken sync requests from NextAuth
 */
async function handleAuthSync(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();

  logger.info('Auth sync request received', { correlationId });

  try {
    const body = await request.json() as any;
    const { azureProfile, source, clientInfo } = body;

    if (!azureProfile || !azureProfile.email) {
      return addCorsHeaders({
        status: 400,
        jsonBody: {
          success: false,
          error: 'Azure profile with email is required'
        }
      }, request);
    }

    // Extract user information from Azure profile
    const firstName = azureProfile.given_name || '';
    const lastName = azureProfile.family_name || '';
    const displayName = azureProfile.name || `${firstName} ${lastName}`.trim() ||
      azureProfile.email.split('@')[0];

    // Check if user exists
    let users = await db.queryItems('users',
      'SELECT * FROM c WHERE c.email = @email',
      [{ name: '@email', value: azureProfile.email.toLowerCase() }]
    );

    let user: any;
    const now = new Date().toISOString();

    if (users.length === 0) {
      // Create new user
      user = {
        id: azureProfile.sub || azureProfile.oid || uuidv4(),
        email: azureProfile.email.toLowerCase(),
        firstName,
        lastName,
        displayName,
        avatar: azureProfile.picture,
        status: 'active',
        roles: ['user'],
        systemRoles: ['organization_member'],
        organizationIds: [],
        tenantId: azureProfile.tid,
        source: source || 'azure-b2c',
        createdAt: now,
        updatedAt: now,
        lastLoginAt: now
      };

      await db.createItem('users', user);
      logger.info('New user created from Azure profile', {
        correlationId,
        userId: user.id,
        email: user.email
      });
    } else {
      // Update existing user
      user = users[0];
      user.firstName = firstName || user.firstName;
      user.lastName = lastName || user.lastName;
      user.displayName = displayName || user.displayName;
      user.avatar = azureProfile.picture || user.avatar;
      user.lastLoginAt = now;
      user.updatedAt = now;

      await db.updateItem('users', user);
      logger.info('Existing user updated from Azure profile', {
        correlationId,
        userId: user.id,
        email: user.email
      });
    }

    // Generate production JWT token
    const accessToken = jwt.sign(
      {
        sub: user.id,
        userId: user.id,
        email: user.email,
        displayName: user.displayName,
        organizationId: user.organizationId,
        organizationIds: user.organizationIds || [],
        roles: user.roles || ['user'],
        systemRoles: user.systemRoles || [],
        tenantId: user.tenantId,
        source: 'auth-sync'
      },
      process.env.JWT_SECRET || 'default-secret',
      { expiresIn: '24h' }
    );

    // Create StandardToken for frontend
    const standardToken = {
      accessToken,
      expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      userId: user.id,
      email: user.email,
      roles: user.roles || ['user'],
      organizationIds: user.organizationIds || [],
      tenantId: user.tenantId
    };

    logger.info('Auth sync successful', {
      correlationId,
      userId: user.id,
      email: user.email,
      source,
      clientInfo: clientInfo?.userAgent ? 'provided' : 'missing'
    });

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        accessToken,
        standardToken,
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          displayName: user.displayName,
          avatar: user.avatar,
          organizationId: user.organizationId,
          organizationIds: user.organizationIds || [],
          roles: user.roles || ['user'],
          systemRoles: user.systemRoles || [],
          tenantId: user.tenantId,
          status: user.status,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    }, request);

  } catch (error) {
    logger.error('Auth sync failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: {
        success: false,
        error: 'Internal server error'
      }
    }, request);
  }
}

// Register HTTP functions
app.http('api-keys-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/api-keys',
  handler: (request, context) => infrastructureManager.createApiKey(request, context)
});

app.http('api-keys-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/api-keys/list',
  handler: listApiKeys
});

app.http('rate-limit-check', {
  methods: ['GET', 'POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/rate-limit/check',
  handler: (request, context) => infrastructureManager.checkRateLimit(request, context)
});

app.http('templates-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/templates',
  handler: (request, context) => infrastructureManager.createTemplate(request, context)
});

app.http('infrastructure-templates-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/templates/list',
  handler: listTemplates
});

app.http('logging-entry', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/logging',
  handler: (request, context) => infrastructureManager.logEntry(request, context)
});

app.http('auth-login', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'infrastructure/auth/login',
  handler: authenticateUser
});

app.http('auth-sync', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'auth/sync',
  handler: handleAuthSync
});

// Register timer function for cache warming
app.timer('cache-warming-scheduler', {
  schedule: '0 */15 * * * *', // Every 15 minutes
  handler: (myTimer, context) => infrastructureManager.performCacheWarming(myTimer, context)
});
