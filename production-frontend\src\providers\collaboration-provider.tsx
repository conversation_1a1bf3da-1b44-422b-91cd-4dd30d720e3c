"use client"

/**
 * Collaboration Provider - Lazy-loaded SignalR for Collaborative Features Only
 * This provider should only be used by components that require real-time collaboration
 * It prevents unnecessary SignalR connections on dashboard and other non-collaborative pages
 */

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { HubConnection, HubConnectionBuilder, LogLevel, HubConnectionState } from '@microsoft/signalr'
import { useAuth } from '@/hooks/useAuth'
import { toast } from 'sonner'

interface CollaborationContextType {
  connection: HubConnection | null
  connectionState: HubConnectionState
  isConnected: boolean
  connectionId: string | null
  connect: () => Promise<void>
  disconnect: () => Promise<void>
  joinGroup: (groupName: string) => Promise<void>
  leaveGroup: (groupName: string) => Promise<void>
  sendMessage: (method: string, ...args: any[]) => Promise<void>
  on: (eventName: string, handler: (...args: any[]) => void) => void
  off: (eventName: string, handler: (...args: any[]) => void) => void
}

const CollaborationContext = createContext<CollaborationContextType | null>(null)

interface CollaborationProviderProps {
  children: ReactNode
  autoConnect?: boolean
}

export function CollaborationProvider({ children, autoConnect = false }: CollaborationProviderProps) {
  const { user, token } = useAuth()
  const [connection, setConnection] = useState<HubConnection | null>(null)
  const [connectionState, setConnectionState] = useState<HubConnectionState>(HubConnectionState.Disconnected)
  const [connectionId, setConnectionId] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)

  // Create connection instance
  const createConnection = useCallback(() => {
    if (connection || !user || !token) return null

    const hubUrl = `${process.env.NEXT_PUBLIC_API_URL || 'https://hepzlogic.azurewebsites.net'}/api/signalr`
    
    const newConnection = new HubConnectionBuilder()
      .withUrl(hubUrl, {
        accessTokenFactory: () => token.accessToken || '',
        withCredentials: false,
      })
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.previousRetryCount < 5) {
            return Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000)
          }
          return null // Stop reconnecting after 5 attempts
        }
      })
      .configureLogging(
        process.env.NODE_ENV === 'development' ? LogLevel.Information : LogLevel.Warning
      )
      .build()

    // Set up connection event handlers
    newConnection.onclose((error) => {
      console.warn('SignalR connection closed:', error)
      setIsConnected(false)
      setConnectionState(HubConnectionState.Disconnected)
      setConnectionId(null)
    })

    newConnection.onreconnecting((error) => {
      console.warn('SignalR reconnecting:', error)
      setIsConnected(false)
      setConnectionState(HubConnectionState.Reconnecting)
    })

    newConnection.onreconnected((connectionId) => {
      console.info('SignalR reconnected:', connectionId)
      setIsConnected(true)
      setConnectionState(HubConnectionState.Connected)
      setConnectionId(connectionId || null)
    })

    return newConnection
  }, [connection, user, token])

  // Connect to SignalR
  const connect = useCallback(async () => {
    if (isConnecting || isConnected || !user || !token) return

    setIsConnecting(true)
    
    try {
      let conn = connection
      if (!conn) {
        conn = createConnection()
        if (!conn) {
          throw new Error('Failed to create SignalR connection')
        }
        setConnection(conn)
      }

      if (conn.state === HubConnectionState.Disconnected) {
        await conn.start()
        setIsConnected(true)
        setConnectionState(conn.state)
        setConnectionId(conn.connectionId || null)
        
        console.info('SignalR connected successfully for collaboration')
        toast.success('Connected to collaboration service')
      }
    } catch (error) {
      console.error('Failed to connect to SignalR:', error)
      setIsConnected(false)
      setConnectionState(HubConnectionState.Disconnected)
      toast.error('Failed to connect to collaboration service')
      throw error
    } finally {
      setIsConnecting(false)
    }
  }, [connection, createConnection, isConnecting, isConnected, user, token])

  // Disconnect from SignalR
  const disconnect = useCallback(async () => {
    if (connection && connection.state !== HubConnectionState.Disconnected) {
      try {
        await connection.stop()
        console.info('SignalR disconnected')
      } catch (error) {
        console.warn('Error disconnecting SignalR:', error)
      }
    }
    
    setConnection(null)
    setIsConnected(false)
    setConnectionState(HubConnectionState.Disconnected)
    setConnectionId(null)
  }, [connection])

  // Join a SignalR group
  const joinGroup = useCallback(async (groupName: string) => {
    if (!connection || !isConnected) {
      throw new Error('SignalR not connected')
    }
    
    try {
      await connection.invoke('JoinGroup', groupName)
      console.info(`Joined SignalR group: ${groupName}`)
    } catch (error) {
      console.error(`Failed to join group ${groupName}:`, error)
      throw error
    }
  }, [connection, isConnected])

  // Leave a SignalR group
  const leaveGroup = useCallback(async (groupName: string) => {
    if (!connection || !isConnected) return
    
    try {
      await connection.invoke('LeaveGroup', groupName)
      console.info(`Left SignalR group: ${groupName}`)
    } catch (error) {
      console.error(`Failed to leave group ${groupName}:`, error)
    }
  }, [connection, isConnected])

  // Send message through SignalR
  const sendMessage = useCallback(async (method: string, ...args: any[]) => {
    if (!connection || !isConnected) {
      throw new Error('SignalR not connected')
    }
    
    try {
      await connection.invoke(method, ...args)
    } catch (error) {
      console.error(`Failed to send SignalR message ${method}:`, error)
      throw error
    }
  }, [connection, isConnected])

  // Add event listener
  const on = useCallback((eventName: string, handler: (...args: any[]) => void) => {
    if (connection) {
      connection.on(eventName, handler)
    }
  }, [connection])

  // Remove event listener
  const off = useCallback((eventName: string, handler: (...args: any[]) => void) => {
    if (connection) {
      connection.off(eventName, handler)
    }
  }, [connection])

  // Auto-connect if enabled and user is authenticated
  useEffect(() => {
    if (autoConnect && user && token && !isConnected && !isConnecting) {
      connect().catch(console.error)
    }
  }, [autoConnect, user, token, isConnected, isConnecting, connect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (connection) {
        disconnect().catch(console.error)
      }
    }
  }, [connection, disconnect])

  // Update connection state when connection changes
  useEffect(() => {
    if (connection) {
      setConnectionState(connection.state)
    }
  }, [connection])

  const value: CollaborationContextType = {
    connection,
    connectionState,
    isConnected,
    connectionId,
    connect,
    disconnect,
    joinGroup,
    leaveGroup,
    sendMessage,
    on,
    off,
  }

  return (
    <CollaborationContext.Provider value={value}>
      {children}
    </CollaborationContext.Provider>
  )
}

// Hook to use collaboration context
export function useCollaborationConnection() {
  const context = useContext(CollaborationContext)
  if (!context) {
    throw new Error('useCollaborationConnection must be used within a CollaborationProvider')
  }
  return context
}

// Hook for collaborative components that need SignalR
export function useCollaborativeSignalR(groupName?: string) {
  const collaboration = useCollaborationConnection()
  const [isInGroup, setIsInGroup] = useState(false)

  // Auto-connect when component mounts
  useEffect(() => {
    if (!collaboration.isConnected) {
      collaboration.connect().catch(console.error)
    }
  }, [collaboration])

  // Auto-join group if specified
  useEffect(() => {
    if (groupName && collaboration.isConnected && !isInGroup) {
      collaboration.joinGroup(groupName)
        .then(() => setIsInGroup(true))
        .catch(console.error)
    }

    return () => {
      if (groupName && isInGroup) {
        collaboration.leaveGroup(groupName).catch(console.error)
        setIsInGroup(false)
      }
    }
  }, [groupName, collaboration, isInGroup])

  return {
    ...collaboration,
    isInGroup,
    groupName,
  }
}
