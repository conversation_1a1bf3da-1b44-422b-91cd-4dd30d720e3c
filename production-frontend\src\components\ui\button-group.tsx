"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonGroupVariants = cva(
  "inline-flex items-center justify-center",
  {
    variants: {
      variant: {
        default: "rounded-md overflow-hidden",
        outline: "rounded-md overflow-hidden",
        vertical: "flex-col rounded-md overflow-hidden",
      },
      size: {
        default: "",
        sm: "",
        lg: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonGroupProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof buttonGroupVariants> {
  children: React.ReactNode
}

const ButtonGroup = React.forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ className, variant, size, children, ...props }, ref) => {
    // Clone children to add special styling for button group
    const childrenWithProps = React.Children.map(children, (child, index) => {
      if (React.isValidElement(child)) {
        // Add special styling to buttons in the group
        const isFirst = index === 0
        const isLast = index === React.Children.count(children) - 1

        let additionalClassName = ""

        if (variant === "vertical") {
          // Vertical styling
          additionalClassName = cn(
            "rounded-none border-b",
            isFirst && "rounded-t-md",
            isLast && "rounded-b-md border-b-0"
          )
        } else {
          // Horizontal styling
          additionalClassName = cn(
            "rounded-none border-r",
            isFirst && "rounded-l-md",
            isLast && "rounded-r-md border-r-0"
          )
        }

        return React.cloneElement(child, {
          ...(child.props as any),
          className: cn(child.props.className, additionalClassName),
          size: child.props.size || size,
          variant: child.props.variant || "default",
        })
      }
      return child
    })

    return (
      <div
        className={cn(buttonGroupVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {childrenWithProps}
      </div>
    )
  }
)
ButtonGroup.displayName = "ButtonGroup"

export { ButtonGroup, buttonGroupVariants }
