/**
 * Authentication Hook
 * Provides authentication state and methods
 */

import React, { useCallback, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '../stores/auth-store'
import type { User, LoginCredentials, RegisterData, PasswordChangeData, StandardToken } from '../types/user'

export interface UseAuthReturn {
  // State
  user: User | null
  token: StandardToken | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: (redirectUrl?: string) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  refreshAuth: () => Promise<void>
  updateProfile: (data: Partial<User>) => Promise<void>
  changePassword: (data: PasswordChangeData) => Promise<void>
  resetPassword?: (email: string) => Promise<void>
  clearError?: () => void

  // Utilities
  hasPermission: (permission: string) => boolean
  hasRole: (role: string) => boolean
  isInOrganization: (organizationId: string) => boolean
  checkAuthStatus: () => boolean
  getToken: () => string | null
  isTokenExpired: () => boolean
  timeUntilExpiry: () => number
}

/**
 * Custom hook for authentication
 */
export function useAuth(): UseAuthReturn {
  const router = useRouter()
  const {
    user,
    token,
    isAuthenticated,
    loading,
    error,
    login: storeLogin,
    logout: storeLogout,
    refreshAuth: storeRefreshAuth,
    updateProfile: storeUpdateProfile,
    setToken,
    clearAuth,
    checkAuthStatus,
    isTokenExpired,
    timeUntilExpiry,
    syncUserWithBackend,

  } = useAuthStore()

  const set = useAuthStore.setState
  const initializationRef = useRef(false)

  // Initialize B2C token extraction on mount - ONLY ONCE
  useEffect(() => {
    // Prevent multiple initializations but allow re-initialization if not authenticated
    if (initializationRef.current) return
    initializationRef.current = true

    let mounted = true

    const initializeAuth = async () => {
      console.log('[Auth Hook] Initializing auth, current state:', { isAuthenticated, loading, hasUser: !!user })

      // Set loading state
      set({ loading: true })

      try {
        const response = await fetch('/api/auth/token', {
          method: 'GET',
          credentials: 'include',
        })

        console.log('[Auth Hook] Token API response status:', response.status)

        if (response.ok && mounted) {
          const tokenData = await response.json()
          console.log('[Auth Hook] Token data received:', {
            hasAccessToken: !!tokenData.accessToken,
            hasUserInfo: !!tokenData.userInfo,
            userEmail: tokenData.userInfo?.username
          })

          if (tokenData.accessToken && tokenData.userInfo) {
            const standardToken = {
              accessToken: tokenData.accessToken, // Use actual B2C access token
              refreshToken: '',
              userId: tokenData.userInfo.localAccountId || 'unknown',
              email: tokenData.userInfo.username || '',
              roles: ['user'],
              organizationIds: [],
              tenantId: tokenData.userInfo.tenantId || 'default',
              expiresAt: tokenData.expiresOn,
              scope: 'openid profile email',
              tokenType: 'Bearer'
            }

            // Create user object from B2C data
            const user = {
              id: tokenData.userInfo.localAccountId,
              email: tokenData.userInfo.username,
              firstName: tokenData.userInfo.name?.split(' ')[0] || '',
              lastName: tokenData.userInfo.name?.split(' ').slice(1).join(' ') || '',
              displayName: tokenData.userInfo.name || tokenData.userInfo.username,
              avatar: undefined,
              tenantId: tokenData.userInfo.tenantId || 'default',
              organizationId: undefined,
              organizationIds: [],
              roles: ['user'],
              systemRoles: [],
              permissions: [],
              status: 'active',
              preferences: {},
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              lastLoginAt: new Date().toISOString(),
              isEmailVerified: true,
              isActive: true
            }

            // Set token using the store's method (this will set isAuthenticated: true)
            console.log('[Auth Hook] Setting token and user data:', { token: standardToken, user })
            setToken(standardToken)

            // Update user data immediately - don't wait for backend sync
            set({
              user,
              loading: false,
              error: null,
              lastUpdated: new Date().toISOString()
            })

            console.log('[Auth Hook] Auth state updated successfully - user should now be available')

            // Sync with backend to create/update user in CosmosDB
            console.log('[Auth Hook] Syncing user with backend...')
            try {
              await syncUserWithBackend(standardToken.accessToken)
              console.log('[Auth Hook] Backend sync completed successfully')
            } catch (syncError) {
              console.error('[Auth Hook] Backend sync failed:', syncError)
              // Don't fail the auth flow if backend sync fails
            }
          } else {
            // No valid token/user data
            console.log('[Auth Hook] No valid token/user data in response')
            set({ loading: false })
          }
        } else {
          // No B2C session available
          console.log('[Auth Hook] No B2C session available or response not ok')
          set({ loading: false })
        }
      } catch (error) {
        // Silent fail - no B2C session available
        console.warn('[Auth] Token extraction failed:', error)
        if (mounted) {
          set({ loading: false })
        }
      }
    }

    initializeAuth()

    return () => {
      mounted = false
    }
  }, []) // Empty dependency array - run only once

  // Login function
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      await storeLogin(credentials)
      // Redirect to dashboard or intended page
      const redirectTo = new URLSearchParams(window.location.search).get('redirect') || '/dashboard'
      router.push(redirectTo)
    } catch (error) {
      // Error is handled by the store
      throw error
    }
  }, [storeLogin, router])

  // Logout function
  const logout = useCallback(async (redirectUrl?: string) => {
    try {
      await storeLogout()
      router.push(redirectUrl || '/auth/login')
    } catch (error) {
      // Even if logout fails, clear local state and redirect
      clearAuth()
      router.push(redirectUrl || '/auth/login')
    }
  }, [storeLogout, clearAuth, router])

  // Register function
  const register = useCallback(async (data: RegisterData) => {
    try {
      // Call the registration API
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Registration failed')
      }

      const result = await response.json()
      
      // Set token if registration includes auto-login
      if (result.token) {
        setToken(result.token)
        router.push('/dashboard')
      } else {
        // Redirect to login with success message
        router.push('/auth/login?message=Registration successful. Please log in.')
      }
    } catch (error) {
      throw error
    }
  }, [setToken, router])

  // Refresh authentication
  const refreshAuth = useCallback(async () => {
    try {
      await storeRefreshAuth()
    } catch (error) {
      // If refresh fails, logout user
      await logout()
      throw error
    }
  }, [storeRefreshAuth, logout])

  // Update profile
  const updateProfile = useCallback(async (data: Partial<User>) => {
    try {
      // Convert User type to UserContext type
      const userContextData = {
        ...data,
        roles: data.roles?.map(role => role.name) || []
      }
      await storeUpdateProfile(userContextData as any)
    } catch (error) {
      throw error
    }
  }, [storeUpdateProfile])

  // Change password
  const changePassword = useCallback(async (data: PasswordChangeData) => {
    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Password change failed')
      }

      // Optionally refresh auth to get updated user data
      await refreshAuth()
    } catch (error) {
      throw error
    }
  }, [token, refreshAuth])

  // Check if user has specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user || !user.permissions) return false
    return user.permissions.includes(permission)
  }, [user])

  // Check if user has specific role
  const hasRole = useCallback((role: string): boolean => {
    if (!user || !user.roles) return false
    // user.roles is string[] in UserContext
    return user.roles.includes(role)
  }, [user])

  // Check if user is in specific organization
  const isInOrganization = useCallback((organizationId: string): boolean => {
    if (!user || !user.organizationIds) return false
    return user.organizationIds.includes(organizationId)
  }, [user])

  // Reset password
  const resetPassword = useCallback(async (email: string) => {
    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Password reset failed')
      }
    } catch (error) {
      throw error
    }
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    // Clear authentication errors from the store
    useAuthStore.setState({ error: null });
  }, [])

  // Get current token
  const getToken = useCallback((): string | null => {
    return token?.accessToken || null
  }, [token])

  // Auto-refresh token when it's about to expire (B2C compatible)
  useEffect(() => {
    if (!isAuthenticated || !token) return

    const refreshThreshold = 5 * 60 * 1000 // 5 minutes

    // For Azure AD B2C, we don't have refresh tokens in browser
    // Instead, we'll check if the token is expired and redirect to login
    const interval = setInterval(() => {
      if (isTokenExpired()) {
        console.warn('[Auth] Token expired, logging out user')
        logout()
      } else {
        const timeLeft = timeUntilExpiry()
        // For B2C, when token is about to expire, we need to re-authenticate
        if (timeLeft > 0 && timeLeft <= refreshThreshold) {
          console.warn('[Auth] Token expiring soon, user will need to re-authenticate')
          // Don't try to refresh automatically for B2C - let user re-authenticate when needed
        }
      }
    }, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [isAuthenticated, token, timeUntilExpiry, isTokenExpired, logout])

  // Check auth status on mount - only once
  useEffect(() => {
    checkAuthStatus()
  }, []) // Empty dependency array to run only once

  return {
    // State
    user: user as any, // Cast UserContext to User for interface compatibility
    token: token as StandardToken | null,
    isAuthenticated,
    isLoading: loading,
    error,

    // Actions
    login,
    logout,
    register,
    refreshAuth,
    updateProfile,
    changePassword,
    resetPassword,
    clearError,

    // Utilities
    hasPermission,
    hasRole,
    isInOrganization,
    checkAuthStatus,
    getToken,
    isTokenExpired,
    timeUntilExpiry,
  }
}

// Higher-order component for protected routes
export function withAuth<P extends object>(
  _Component: React.ComponentType<P>,
  options: {
    redirectTo?: string
    requiredPermissions?: string[]
    requiredRoles?: string[]
  } = {}
) {
  return function AuthenticatedComponent(_props: P) {
    const { isAuthenticated, isLoading, hasPermission, hasRole } = useAuth()
    const router = useRouter()

    useEffect(() => {
      if (isLoading) return

      if (!isAuthenticated) {
        const redirectUrl = options.redirectTo || '/auth/login'
        const currentPath = window.location.pathname
        router.push(`${redirectUrl}?redirect=${encodeURIComponent(currentPath)}`)
        return
      }

      // Check required permissions
      if (options.requiredPermissions) {
        const hasAllPermissions = options.requiredPermissions.every(permission =>
          hasPermission(permission)
        )
        if (!hasAllPermissions) {
          router.push('/unauthorized')
          return
        }
      }

      // Check required roles
      if (options.requiredRoles) {
        const hasAnyRole = options.requiredRoles.some(role => hasRole(role))
        if (!hasAnyRole) {
          router.push('/unauthorized')
          return
        }
      }
    }, [isAuthenticated, isLoading, hasPermission, hasRole, router])

    if (isLoading) {
      return null // Replace with proper loading component
    }

    if (!isAuthenticated) {
      return null
    }

    return null // Component will be rendered by Next.js routing
  }
}

// Hook for checking permissions
export function usePermissions() {
  const { hasPermission, hasRole, user } = useAuth()

  return {
    hasPermission,
    hasRole,
    permissions: user?.permissions || [],
    roles: user?.roles || [],
    canAccess: (resource: string, action: string) => {
      return hasPermission(`${resource}:${action}`)
    },
  }
}
