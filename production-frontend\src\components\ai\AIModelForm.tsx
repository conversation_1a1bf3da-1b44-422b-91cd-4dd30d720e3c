'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '../ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Checkbox } from '../ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Loader2, Save, X } from 'lucide-react';
import { useToast } from '../ui/use-toast';
import { AIModelType } from '@/types/ai-models';
import { AIModelDatasetSelector } from './AIModelDatasetSelector';
import { AIModelConfigEditor } from './AIModelConfigEditor';
import { useAIModels } from '@/hooks/ai/useAIModels';

// Form schema
const aiModelFormSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  type: z.enum(['classification', 'extraction', 'summarization', 'custom']),
  baseModel: z.string().min(1, 'Base model is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  projectId: z.string().optional(),
  isPublic: z.boolean().default(false),
});

type AIModelFormValues = z.infer<typeof aiModelFormSchema>;

interface AIModelFormProps {
  defaultValues?: Partial<AIModelFormValues & { tags?: string[] }>;
  modelId?: string;
  modelTypes?: { label: string; value: AIModelType }[];
  baseModels?: { label: string; value: string }[];
  projects?: { label: string; value: string }[];
  organizations?: { label: string; value: string }[];
  suggestedTags?: string[];
  isLoading?: boolean;
  onSubmit: (data: AIModelFormValues & {
    config: any;
    trainingDatasetIds: string[];
    evaluationDatasetIds: string[];
    tags: string[]
  }) => void;
  onCancel?: () => void;
  className?: string;
}

export function AIModelForm({
  defaultValues,
  modelId,
  modelTypes = [
    { label: 'Classification', value: 'classification' as AIModelType },
    { label: 'Extraction', value: 'extraction' as AIModelType },
    { label: 'Summarization', value: 'summarization' as AIModelType },
    { label: 'Custom', value: 'custom' as AIModelType },
  ],
  baseModels = [],
  projects = [],
  organizations = [],
  suggestedTags = [],
  isLoading = false,
  onSubmit,
  onCancel,
  className,
}: AIModelFormProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('basic');
  const [tags, setTags] = useState<string[]>(defaultValues?.tags || []);
  const [newTag, setNewTag] = useState('');
  const [trainingDatasetIds, setTrainingDatasetIds] = useState<string[]>([]);
  const [evaluationDatasetIds, setEvaluationDatasetIds] = useState<string[]>([]);
  const [modelConfig, setModelConfig] = useState<any>({
    parameters: {},
    hyperparameters: {},
    preprocessingSteps: [],
    threshold: 0.5,
    temperature: 0.7,
  });

  // Initialize form
  const form = useForm<AIModelFormValues>({
    resolver: zodResolver(aiModelFormSchema),
    defaultValues: {
      name: '',
      description: '',
      type: 'classification',
      baseModel: '',
      organizationId: '',
      projectId: '',
      isPublic: false,
      ...defaultValues,
    },
  });

  // Handle form submission
  const handleSubmit = (data: AIModelFormValues) => {
    if (trainingDatasetIds.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please select at least one training dataset',
        variant: 'destructive',
      });
      setActiveTab('datasets');
      return;
    }

    onSubmit({
      ...data,
      config: modelConfig,
      trainingDatasetIds,
      evaluationDatasetIds,
      tags,
    });
  };

  // Handle adding a tag
  const handleAddTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
      setNewTag('');
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter((t) => t !== tag));
  };

  // Use the AI models hook to get base models
  const aiModelsQuery = useAIModels({
    type: form.watch('type') as AIModelType
  });
  const availableBaseModels = (aiModelsQuery.data || []).map((model: any) => ({
    value: model.id,
    label: model.name
  }));

  return (
    <div className={className}>
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="basic">Basic Info</TabsTrigger>
          <TabsTrigger value="datasets">Datasets</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <TabsContent value="basic">
              <Card>
                <CardHeader>
                  <CardTitle>Model Information</CardTitle>
                  <CardDescription>
                    Enter the basic information for your AI model.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter model name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter model description"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Model Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select model type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {modelTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The type of AI model determines its capabilities and use cases
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="baseModel"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Base Model</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select base model" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {availableBaseModels.length > 0
                              ? availableBaseModels.map((model: any) => (
                                  <SelectItem key={model.value} value={model.value}>
                                    {model.label}
                                  </SelectItem>
                                ))
                              : baseModels.map((model) => (
                                  <SelectItem key={model.value} value={model.value}>
                                    {model.label}
                                  </SelectItem>
                                ))
                            }
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          The foundation model that will be fine-tuned
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="organizationId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select organization" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {organizations.map((org) => (
                              <SelectItem key={org.value} value={org.value}>
                                {org.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="projectId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select project (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="">None</SelectItem>
                            {projects.map((project) => (
                              <SelectItem key={project.value} value={project.value}>
                                {project.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          If selected, this model will only be available in this project
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isPublic"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Public Model</FormLabel>
                          <FormDescription>
                            Make this model available to all members of your organization
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <div>
                    <FormLabel>Tags</FormLabel>
                    <div className="flex flex-wrap gap-2 mt-2 mb-4">
                      {tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="gap-1">
                          {tag}
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 p-0"
                            onClick={() => handleRemoveTag(tag)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                    <div className="flex gap-2">
                      <Input
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        placeholder="Add a tag"
                        className="flex-1"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            handleAddTag();
                          }
                        }}
                      />
                      <Button type="button" onClick={handleAddTag} size="sm">
                        Add
                      </Button>
                    </div>
                    {suggestedTags.length > 0 && (
                      <div className="mt-2">
                        <p className="text-sm text-muted-foreground mb-1">Suggested tags:</p>
                        <div className="flex flex-wrap gap-1">
                          {suggestedTags.map((tag) => (
                            <Badge
                              key={tag}
                              variant="outline"
                              className="cursor-pointer"
                              onClick={() => {
                                if (!tags.includes(tag)) {
                                  setTags([...tags, tag]);
                                }
                              }}
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="datasets">
              <Card>
                <CardHeader>
                  <CardTitle>Training & Evaluation Datasets</CardTitle>
                  <CardDescription>
                    Select datasets for training and evaluating your model
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-2">Training Datasets</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select datasets to use for training your model
                    </p>
                    <AIModelDatasetSelector
                      selectedDatasets={trainingDatasetIds}
                      onSelectDatasets={setTrainingDatasetIds}
                      className="w-full"
                    />
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-2">Evaluation Datasets</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Select datasets to use for evaluating your model (optional)
                    </p>
                    <AIModelDatasetSelector
                      selectedDatasets={evaluationDatasetIds}
                      onSelectDatasets={setEvaluationDatasetIds}
                      className="w-full"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="config">
              <AIModelConfigEditor
                config={modelConfig}
                onChange={setModelConfig}
              />
            </TabsContent>

            <div className="flex justify-end gap-2 mt-6">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {modelId ? 'Update Model' : 'Create Model'}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </Tabs>
    </div>
  );
}
