/**
 * Unified Project Management Function
 * Consolidates all project CRUD operations, settings, members, and collaboration
 * Replaces: project-create.ts, project-list.ts, project-manage.ts, project-settings.ts,
 *          project-members-management.ts, project-collaboration.ts
 * 
 * Production-ready implementation with comprehensive error handling,
 * caching, Azure best practices, and Service Bus integration
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Unified project types and enums
enum ProjectVisibility {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  PUBLIC = 'PUBLIC'
}

enum ProjectStatus {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
  DRAFT = 'DRAFT',
  SUSPENDED = 'SUSPENDED'
}

enum ProjectRole {
  OWNER = 'OWNER',
  MANAGER = 'MANAGER',
  CONTRIBUTOR = 'CONTRIBUTOR',
  VIEWER = 'VIEWER'
}

enum MemberStatus {
  ACTIVE = 'ACTIVE',
  INVITED = 'INVITED',
  SUSPENDED = 'SUSPENDED',
  REMOVED = 'REMOVED'
}

// Comprehensive interfaces
interface Project {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  visibility: ProjectVisibility;
  status: ProjectStatus;
  tags: string[];
  documentIds: string[];
  workflowIds: string[];
  memberIds: string[];
  teamIds: string[];
  settings: ProjectSettings;
  statistics: ProjectStatistics;
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  tenantId: string;
}

interface ProjectSettings {
  general: {
    visibility: ProjectVisibility;
    allowGuestAccess: boolean;
    requireApprovalForDocuments: boolean;
    enableVersioning: boolean;
    autoArchiveInactiveDays: number;
    defaultDocumentPermissions: 'view' | 'comment' | 'edit';
    maxMembers?: number;
    enablePublicSharing: boolean;
  };
  collaboration: {
    enableComments: boolean;
    enableRealTimeEditing: boolean;
    allowExternalSharing: boolean;
    requireApprovalForSharing: boolean;
    maxSharesPerDocument: number;
    enableMentions: boolean;
    enableNotifications: boolean;
    enableDiscussions: boolean;
  };
  workflow: {
    enableWorkflows: boolean;
    allowCustomWorkflows: boolean;
    requireApprovalForWorkflowChanges: boolean;
    maxConcurrentWorkflows: number;
    defaultWorkflowTimeout: number;
    enableWorkflowNotifications: boolean;
    defaultWorkflowId?: string;
  };
  security: {
    enableEncryption: boolean;
    requireTwoFactorForSensitiveActions: boolean;
    allowedFileTypes: string[];
    maxFileSize: number;
    enableAuditLog: boolean;
    dataRetentionDays: number;
    allowDownloads: boolean;
    ipRestrictions: string[];
  };
  integrations: {
    enabledIntegrations: string[];
    webhookEndpoints: string[];
    apiAccess: boolean;
    allowThirdPartyApps: boolean;
    customFields: CustomField[];
  };
  notifications: {
    enableEmailNotifications: boolean;
    enableSlackNotifications: boolean;
    enableTeamsNotifications: boolean;
    notificationEvents: NotificationEvents;
    digestFrequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  };
}

interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'select';
  required: boolean;
  options?: string[];
  defaultValue?: any;
}

interface NotificationEvents {
  documentUploaded: boolean;
  documentProcessed: boolean;
  commentAdded: boolean;
  workflowAssigned: boolean;
  memberAdded: boolean;
  projectUpdated: boolean;
  deadlineApproaching: boolean;
  taskCompleted: boolean;
}

interface ProjectStatistics {
  memberCount: number;
  documentCount: number;
  workflowCount: number;
  storageUsed: number;
  lastActivity: string;
  activeWorkflows: number;
  completedWorkflows: number;
  pendingApprovals: number;
}

interface ProjectMember {
  id: string;
  userId: string;
  projectId: string;
  organizationId: string;
  role: ProjectRole;
  permissions: string[];
  status: MemberStatus;
  joinedAt: string;
  invitedBy: string;
  lastActivity?: string;
  expiresAt?: string;
  tenantId: string;
}

interface ProjectInvitation {
  id: string;
  projectId: string;
  email: string;
  role: ProjectRole;
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  message?: string;
  token: string;
  tenantId: string;
}

// Validation schemas
const createProjectSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  organizationId: Joi.string().uuid().required(),
  visibility: Joi.string().valid(...Object.values(ProjectVisibility)).default(ProjectVisibility.PRIVATE),
  tags: Joi.array().items(Joi.string().max(50)).max(10).default([]),
  settings: Joi.object().optional(),
  templateId: Joi.string().uuid().optional()
});

const updateProjectSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  description: Joi.string().max(500).optional(),
  visibility: Joi.string().valid(...Object.values(ProjectVisibility)).optional(),
  status: Joi.string().valid(...Object.values(ProjectStatus)).optional(),
  tags: Joi.array().items(Joi.string().max(50)).max(10).optional(),
  settings: Joi.object().optional()
});

const listProjectsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  organizationId: Joi.string().uuid().optional(),
  search: Joi.string().max(100).optional(),
  visibility: Joi.string().valid(...Object.values(ProjectVisibility)).optional(),
  status: Joi.string().valid(...Object.values(ProjectStatus)).optional(),
  tags: Joi.string().optional(), // Comma-separated tags
  memberOf: Joi.boolean().default(true),
  sort: Joi.string().valid('name', 'createdAt', 'updatedAt', 'memberCount', 'documentCount').default('updatedAt'),
  order: Joi.string().valid('asc', 'desc').default('desc')
});

const addMemberSchema = Joi.object({
  userId: Joi.string().uuid().optional(),
  email: Joi.string().email().optional(),
  role: Joi.string().valid(...Object.values(ProjectRole)).default(ProjectRole.CONTRIBUTOR),
  permissions: Joi.array().items(Joi.string()).optional(),
  message: Joi.string().max(500).optional(),
  expiresAt: Joi.string().isoDate().optional()
}).xor('userId', 'email');

const updateMemberSchema = Joi.object({
  role: Joi.string().valid(...Object.values(ProjectRole)).optional(),
  permissions: Joi.array().items(Joi.string()).optional(),
  status: Joi.string().valid(...Object.values(MemberStatus)).optional()
});

const updateSettingsSchema = Joi.object({
  general: Joi.object().optional(),
  collaboration: Joi.object().optional(),
  workflow: Joi.object().optional(),
  security: Joi.object().optional(),
  integrations: Joi.object().optional(),
  notifications: Joi.object().optional()
});

/**
 * Unified Project Management Class
 * Handles all project operations with comprehensive error handling and caching
 */
class UnifiedProjectManager {

  /**
   * Create project
   */
  async createProject(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = createProjectSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const { name, description, organizationId, visibility, tags, settings, templateId } = value;

      // Verify organization exists and user has access
      const organization = await db.readItem('organizations', organizationId, user.tenantId || 'default');
      if (!organization) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Organization not found' }
        }, request);
      }

      // Check organization membership and permissions
      const hasPermission = await this.checkOrganizationPermission(organizationId, user.id, 'create_project');
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'You do not have permission to create projects in this organization' }
        }, request);
      }

      // Check organization project limits
      const projectCount = await this.getOrganizationProjectCount(organizationId);
      const maxProjects = organization.settings?.general?.maxProjects || 10;
      
      if (projectCount >= maxProjects) {
        return addCorsHeaders({
          status: 403,
          jsonBody: {
            error: 'Project limit reached',
            message: `This organization has reached its limit of ${maxProjects} projects. Please upgrade or archive existing projects.`
          }
        }, request);
      }

      // Create project
      const projectId = uuidv4();
      const project = await this.createProjectRecord({
        id: projectId,
        name,
        description: description || '',
        organizationId,
        visibility,
        tags: tags || [],
        settings: settings || this.getDefaultProjectSettings(),
        templateId,
        createdBy: user.id,
        tenantId: user.tenantId || user.id
      });

      // Create project membership for creator
      await this.createProjectMembership(projectId, user.id, ProjectRole.OWNER, user.id, organizationId);

      // Update organization project list
      await this.updateOrganizationProjects(organizationId, projectId, user.id);

      // Cache project
      await this.cacheProject(project);

      // Log activity
      await this.logProjectActivity(user.id, 'project_created', {
        projectId,
        projectName: name,
        organizationId,
        visibility,
        memberCount: 1
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Project.Created',
        subject: `projects/${projectId}/created`,
        data: {
          projectId,
          projectName: name,
          organizationId,
          organizationName: organization.name,
          visibility,
          createdBy: user.id,
          memberCount: 1,
          correlationId
        }
      });

      logger.info('Project created successfully', {
        correlationId,
        projectId,
        name,
        organizationId,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          project: this.sanitizeProject(project),
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Project creation failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * List projects for user
   */
  async listProjects(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Parse query parameters
      const url = new URL(request.url);
      const queryParams: any = Object.fromEntries(url.searchParams.entries());

      // Convert string values to appropriate types
      if (queryParams.page) queryParams.page = parseInt(queryParams.page);
      if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);
      if (queryParams.memberOf) queryParams.memberOf = queryParams.memberOf === 'true';

      const { error, value } = listProjectsSchema.validate(queryParams);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const { page, limit, organizationId, search, visibility, status, tags, memberOf, sort, order } = value;

      // Check cache first
      const cacheKey = `user:${user.id}:projects:${JSON.stringify(value)}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return addCorsHeaders({
          status: 200,
          jsonBody: JSON.parse(cached)
        }, request);
      }

      // Build query
      let queryText = '';
      let parameters: any[] = [];

      if (memberOf) {
        // Get projects where user is a member
        const memberships = await db.queryItems<ProjectMember>('project-members',
          'SELECT * FROM c WHERE c.userId = @userId AND c.status = "ACTIVE"',
          [{ name: '@userId', value: user.id }]
        );

        const projectIds = memberships.map(m => m.projectId);

        if (projectIds.length === 0) {
          const result = {
            projects: [],
            pagination: {
              page,
              limit,
              totalCount: 0,
              totalPages: 0,
              hasNext: false,
              hasPrev: false
            }
          };

          await redis.setex(cacheKey, 300, JSON.stringify(result));
          return addCorsHeaders({
            status: 200,
            jsonBody: result
          }, request);
        }

        queryText = `SELECT * FROM c WHERE c.id IN (${projectIds.map((_, i) => `@projectId${i}`).join(', ')})`;
        parameters = projectIds.map((id, i) => ({ name: `@projectId${i}`, value: id }));
      } else {
        // Get all accessible projects (public + organization projects)
        queryText = 'SELECT * FROM c WHERE (c.visibility = "PUBLIC"';

        if (organizationId) {
          queryText += ' OR (c.organizationId = @organizationId AND c.visibility = "ORGANIZATION"))';
          parameters.push({ name: '@organizationId', value: organizationId });
        } else {
          queryText += ')';
        }
      }

      // Add tenant isolation
      if (user.tenantId) {
        queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
        parameters.push({ name: '@tenantId', value: user.tenantId });
      }

      // Add filters
      if (organizationId && memberOf) {
        queryText += ' AND c.organizationId = @orgId';
        parameters.push({ name: '@orgId', value: organizationId });
      }

      if (search) {
        queryText += ' AND (CONTAINS(c.name, @search) OR CONTAINS(c.description, @search))';
        parameters.push({ name: '@search', value: search });
      }

      if (visibility) {
        queryText += ' AND c.visibility = @visibility';
        parameters.push({ name: '@visibility', value: visibility });
      }

      if (status) {
        queryText += ' AND c.status = @status';
        parameters.push({ name: '@status', value: status });
      }

      if (tags) {
        const tagArray = tags.split(',').map((tag: string) => tag.trim());
        queryText += ` AND EXISTS(SELECT VALUE t FROM t IN c.tags WHERE t IN (${tagArray.map((_: string, i: number) => `@tag${i}`).join(', ')}))`;
        tagArray.forEach((tag: string, i: number) => {
          parameters.push({ name: `@tag${i}`, value: tag });
        });
      }

      // Add ordering and pagination
      queryText += ` ORDER BY c.${sort} ${order.toUpperCase()}`;
      queryText += ` OFFSET ${(page - 1) * limit} LIMIT ${limit}`;

      // Execute query
      const projects = await db.queryItems<Project>('projects', queryText, parameters);

      // Get total count
      const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)').split(' ORDER BY')[0];
      const totalCountResult = await db.queryItems<number>('projects', countQuery, parameters);
      const totalCount = totalCountResult[0] || 0;

      // Enrich projects with statistics and user role
      const enrichedProjects = await Promise.all(
        projects.map(async (project) => {
          const [stats, userRole] = await Promise.all([
            this.getProjectStatistics(project.id),
            this.getUserProjectRole(project.id, user.id)
          ]);

          return {
            ...this.sanitizeProject(project),
            statistics: stats,
            userRole
          };
        })
      );

      const result = {
        projects: enrichedProjects,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      };

      // Cache result for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(result));

      logger.info('Projects listed successfully', {
        correlationId,
        userId: user.id,
        page,
        limit,
        totalCount,
        organizationId
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: result
      }, request);

    } catch (error) {
      logger.error('Project listing failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Get project details
   */
  async getProject(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const projectId = request.url.split('/')[4]; // Extract from URL path

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Check cache first
      const cacheKey = `project:${projectId}:details:${user.id}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return addCorsHeaders({
          status: 200,
          jsonBody: JSON.parse(cached)
        }, request);
      }

      // Get project
      const project = await db.readItem('projects', projectId, user.tenantId || 'default');
      if (!project) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Project not found' }
        }, request);
      }

      // Check access permissions
      const hasAccess = await this.checkProjectAccess(projectId, user.id);
      if (!hasAccess) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied' }
        }, request);
      }

      // Get additional project data
      const [stats, userRole, organization] = await Promise.all([
        this.getProjectStatistics(projectId),
        this.getUserProjectRole(projectId, user.id),
        db.readItem('organizations', project.organizationId, user.tenantId || 'default')
      ]);

      const result = {
        success: true,
        project: {
          ...this.sanitizeProject(project),
          statistics: stats,
          userRole,
          organizationName: organization?.name
        }
      };

      // Cache for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(result));

      // Track project access
      await this.trackProjectAccess(projectId, user.id);

      logger.info('Project retrieved successfully', {
        correlationId,
        projectId,
        userId: user.id
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: result
      }, request);

    } catch (error) {
      logger.error('Project retrieval failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId,
        projectId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Helper Methods
   */
  private async checkOrganizationPermission(organizationId: string, userId: string, permission: string): Promise<boolean> {
    try {
      const memberships = await db.queryItems<any>('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = "active"',
        [
          { name: '@orgId', value: organizationId },
          { name: '@userId', value: userId }
        ]
      );

      if (memberships.length === 0) {
        return false;
      }

      const membership = memberships[0];

      // Admin has all permissions
      if (membership.role === 'ADMIN') {
        return true;
      }

      // Check specific permission
      return membership.permissions?.includes(permission) || false;
    } catch (error) {
      logger.error('Error checking organization permission', {
        organizationId,
        userId,
        permission,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async getOrganizationProjectCount(organizationId: string): Promise<number> {
    try {
      const result = await db.queryItems<number>('projects',
        'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status != "ARCHIVED"',
        [{ name: '@orgId', value: organizationId }]
      );
      return result[0] || 0;
    } catch (error) {
      logger.error('Error getting organization project count', {
        organizationId,
        error: error instanceof Error ? error.message : String(error)
      });
      return 0;
    }
  }

  private async createProjectRecord(data: any): Promise<Project> {
    const now = new Date().toISOString();

    const project: Project = {
      id: data.id,
      name: data.name,
      description: data.description,
      organizationId: data.organizationId,
      visibility: data.visibility,
      status: ProjectStatus.ACTIVE,
      tags: data.tags,
      documentIds: [],
      workflowIds: [],
      memberIds: [data.createdBy],
      teamIds: [],
      settings: data.settings,
      statistics: {
        memberCount: 1,
        documentCount: 0,
        workflowCount: 0,
        storageUsed: 0,
        lastActivity: now,
        activeWorkflows: 0,
        completedWorkflows: 0,
        pendingApprovals: 0
      },
      createdBy: data.createdBy,
      createdAt: now,
      updatedBy: data.createdBy,
      updatedAt: now,
      tenantId: data.tenantId
    };

    await db.createItem('projects', project);
    return project;
  }

  private getDefaultProjectSettings(): ProjectSettings {
    return {
      general: {
        visibility: ProjectVisibility.PRIVATE,
        allowGuestAccess: false,
        requireApprovalForDocuments: false,
        enableVersioning: true,
        autoArchiveInactiveDays: 90,
        defaultDocumentPermissions: 'comment',
        enablePublicSharing: false
      },
      collaboration: {
        enableComments: true,
        enableRealTimeEditing: true,
        allowExternalSharing: false,
        requireApprovalForSharing: true,
        maxSharesPerDocument: 50,
        enableMentions: true,
        enableNotifications: true,
        enableDiscussions: true
      },
      workflow: {
        enableWorkflows: true,
        allowCustomWorkflows: false,
        requireApprovalForWorkflowChanges: true,
        maxConcurrentWorkflows: 10,
        defaultWorkflowTimeout: 24,
        enableWorkflowNotifications: true
      },
      security: {
        enableEncryption: true,
        requireTwoFactorForSensitiveActions: false,
        allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png', 'gif'],
        maxFileSize: 100,
        enableAuditLog: true,
        dataRetentionDays: 365,
        allowDownloads: true,
        ipRestrictions: []
      },
      integrations: {
        enabledIntegrations: [],
        webhookEndpoints: [],
        apiAccess: false,
        allowThirdPartyApps: false,
        customFields: []
      },
      notifications: {
        enableEmailNotifications: true,
        enableSlackNotifications: false,
        enableTeamsNotifications: false,
        notificationEvents: {
          documentUploaded: true,
          documentProcessed: true,
          commentAdded: true,
          workflowAssigned: true,
          memberAdded: true,
          projectUpdated: false,
          deadlineApproaching: true,
          taskCompleted: true
        },
        digestFrequency: 'daily'
      }
    };
  }

  private async createProjectMembership(
    projectId: string,
    userId: string,
    role: ProjectRole,
    invitedBy: string,
    organizationId: string
  ): Promise<void> {
    const membership: ProjectMember = {
      id: uuidv4(),
      userId,
      projectId,
      organizationId,
      role,
      permissions: this.getRolePermissions(role),
      status: MemberStatus.ACTIVE,
      joinedAt: new Date().toISOString(),
      invitedBy,
      tenantId: userId
    };

    await db.createItem('project-members', membership);
  }

  private getRolePermissions(role: ProjectRole): string[] {
    switch (role) {
      case ProjectRole.OWNER:
        return ['*']; // All permissions
      case ProjectRole.MANAGER:
        return ['manage_project', 'manage_members', 'create_workflow', 'manage_workflow', 'view_project', 'upload_document', 'update_document'];
      case ProjectRole.CONTRIBUTOR:
        return ['view_project', 'upload_document', 'update_document', 'execute_workflow', 'comment_document'];
      case ProjectRole.VIEWER:
        return ['view_project', 'view_document', 'comment_document'];
      default:
        return ['view_project'];
    }
  }

  private async updateOrganizationProjects(organizationId: string, projectId: string, userId: string): Promise<void> {
    try {
      const organization = await db.readItem('organizations', organizationId, userId);
      if (organization) {
        const updatedOrganization = {
          ...organization,
          id: organizationId,
          projectIds: [...(organization.projectIds || []), projectId],
          updatedAt: new Date().toISOString(),
          updatedBy: userId
        };
        await db.updateItem('organizations', updatedOrganization);
      }
    } catch (error) {
      logger.error('Error updating organization projects', {
        organizationId,
        projectId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async cacheProject(project: Project): Promise<void> {
    try {
      await redis.setex(`project:${project.id}:details`, 1800, JSON.stringify(project));
    } catch (error) {
      logger.error('Error caching project', {
        projectId: project.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async getProjectStatistics(projectId: string): Promise<ProjectStatistics> {
    try {
      // Check cache first
      const cacheKey = `project:${projectId}:stats`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Calculate statistics
      const [memberCount, documentCount, workflowCount] = await Promise.all([
        this.getProjectMemberCount(projectId),
        this.getProjectDocumentCount(projectId),
        this.getProjectWorkflowCount(projectId)
      ]);

      const stats: ProjectStatistics = {
        memberCount,
        documentCount,
        workflowCount,
        storageUsed: 0, // Would calculate from actual storage usage
        lastActivity: new Date().toISOString(),
        activeWorkflows: 0, // Would calculate from workflow status
        completedWorkflows: 0,
        pendingApprovals: 0
      };

      // Cache for 10 minutes
      await redis.setex(cacheKey, 600, JSON.stringify(stats));
      return stats;
    } catch (error) {
      logger.error('Error getting project statistics', {
        projectId,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        memberCount: 0,
        documentCount: 0,
        workflowCount: 0,
        storageUsed: 0,
        lastActivity: new Date().toISOString(),
        activeWorkflows: 0,
        completedWorkflows: 0,
        pendingApprovals: 0
      };
    }
  }

  private async getProjectMemberCount(projectId: string): Promise<number> {
    const result = await db.queryItems<number>('project-members',
      'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId AND c.status = "ACTIVE"',
      [{ name: '@projectId', value: projectId }]
    );
    return result[0] || 0;
  }

  private async getProjectDocumentCount(projectId: string): Promise<number> {
    const result = await db.queryItems<number>('documents',
      'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId',
      [{ name: '@projectId', value: projectId }]
    );
    return result[0] || 0;
  }

  private async getProjectWorkflowCount(projectId: string): Promise<number> {
    const result = await db.queryItems<number>('workflows',
      'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId',
      [{ name: '@projectId', value: projectId }]
    );
    return result[0] || 0;
  }

  private async getUserProjectRole(projectId: string, userId: string): Promise<ProjectRole | null> {
    try {
      const memberships = await db.queryItems<ProjectMember>('project-members',
        'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = "ACTIVE"',
        [
          { name: '@projectId', value: projectId },
          { name: '@userId', value: userId }
        ]
      );

      return memberships.length > 0 ? memberships[0].role : null;
    } catch (error) {
      logger.error('Error getting user project role', {
        projectId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }

  private async checkProjectAccess(projectId: string, userId: string): Promise<boolean> {
    try {
      const project = await db.readItem('projects', projectId, userId);
      if (!project) {
        return false;
      }

      // Check if project is public
      if (project.visibility === ProjectVisibility.PUBLIC) {
        return true;
      }

      // Check if user is a member
      const membership = await this.getUserProjectRole(projectId, userId);
      if (membership) {
        return true;
      }

      // Check organization access for organization-visible projects
      if (project.visibility === ProjectVisibility.ORGANIZATION) {
        return await this.checkOrganizationPermission(project.organizationId, userId, 'view_project');
      }

      return false;
    } catch (error) {
      logger.error('Error checking project access', {
        projectId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async trackProjectAccess(projectId: string, userId: string): Promise<void> {
    try {
      // Update access tracking
      const accessKey = `project-access:${projectId}:${userId}`;
      await redis.setex(accessKey, 86400, new Date().toISOString()); // 24 hours

      // Log access for analytics
      await this.logProjectActivity(userId, 'project_accessed', {
        projectId,
        accessedAt: new Date().toISOString()
      });
    } catch (error) {
      logger.error('Error tracking project access', {
        projectId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async logProjectActivity(userId: string, activity: string, details: any): Promise<void> {
    try {
      await db.createItem('activities', {
        id: uuidv4(),
        type: activity,
        userId,
        projectId: details.projectId,
        organizationId: details.organizationId,
        timestamp: new Date().toISOString(),
        details,
        tenantId: userId
      });
    } catch (error) {
      logger.error('Error logging project activity', {
        userId,
        activity,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private sanitizeProject(project: any): any {
    // Remove sensitive fields before returning
    const sanitized = { ...project };
    delete sanitized._rid;
    delete sanitized._self;
    delete sanitized._etag;
    delete sanitized._attachments;
    delete sanitized._ts;

    return sanitized;
  }
}

// Create instance of the manager
const projectManager = new UnifiedProjectManager();

/**
 * Additional Project Management Functions
 */

/**
 * Update project
 */
async function updateProject(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const projectId = request.url.split('/')[4]; // Extract from URL path

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    // Validate request
    const body = await request.json();
    const { error, value } = updateProjectSchema.validate(body);
    if (error) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: error.details[0].message }
      }, request);
    }

    const updateData = value;

    // Get project
    const project = await db.readItem('projects', projectId, user.tenantId || 'default');
    if (!project) {
      return addCorsHeaders({
        status: 404,
        jsonBody: { error: 'Project not found' }
      }, request);
    }

    // Check permissions
    const userRole = await projectManager['getUserProjectRole'](projectId, user.id);
    if (!userRole || (userRole !== ProjectRole.OWNER && userRole !== ProjectRole.MANAGER)) {
      return addCorsHeaders({
        status: 403,
        jsonBody: { error: 'You do not have permission to update this project' }
      }, request);
    }

    // Update project
    const updatedProject = {
      ...project,
      ...updateData,
      updatedBy: user.id,
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('projects', updatedProject);

    // Invalidate cache
    await redis.del(`project:${projectId}:details`);
    await redis.del(`project:${projectId}:stats`);

    // Log activity
    await projectManager['logProjectActivity'](user.id, 'project_updated', {
      projectId,
      changes: updateData,
      projectName: updatedProject.name,
      organizationId: project.organizationId
    });

    // Publish event
    await eventGridIntegration.publishEvent({
      eventType: 'Project.Updated',
      subject: `projects/${projectId}/updated`,
      data: {
        projectId,
        changes: updateData,
        updatedBy: user.id,
        organizationId: project.organizationId,
        correlationId
      }
    });

    logger.info('Project updated successfully', {
      correlationId,
      projectId,
      userId: user.id,
      changes: Object.keys(updateData)
    });

    return addCorsHeaders({
      status: 200,
      jsonBody: {
        success: true,
        project: projectManager['sanitizeProject'](updatedProject)
      }
    }, request);

  } catch (error) {
    logger.error('Project update failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId,
      projectId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * Add member to project
 */
async function addProjectMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const projectId = request.url.split('/')[4]; // Extract from URL path

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    // Validate request
    const body = await request.json();
    const { error, value } = addMemberSchema.validate(body);
    if (error) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: error.details[0].message }
      }, request);
    }

    const { userId: targetUserId, email, role, message, expiresAt } = value;

    // Get project
    const project = await db.readItem('projects', projectId, user.tenantId || 'default');
    if (!project) {
      return addCorsHeaders({
        status: 404,
        jsonBody: { error: 'Project not found' }
      }, request);
    }

    // Check permissions
    const userRole = await projectManager['getUserProjectRole'](projectId, user.id);
    if (!userRole || (userRole !== ProjectRole.OWNER && userRole !== ProjectRole.MANAGER)) {
      return addCorsHeaders({
        status: 403,
        jsonBody: { error: 'You do not have permission to add members to this project' }
      }, request);
    }

    let memberId: string;

    if (targetUserId) {
      // Adding existing user
      const existingMember = await projectManager['getUserProjectRole'](projectId, targetUserId);
      if (existingMember) {
        return addCorsHeaders({
          status: 409,
          jsonBody: { error: 'User is already a member of this project' }
        }, request);
      }

      // Create membership
      await projectManager['createProjectMembership'](projectId, targetUserId, role, user.id, project.organizationId);
      memberId = targetUserId;

      // Log activity
      await projectManager['logProjectActivity'](user.id, 'member_added', {
        projectId,
        memberId: targetUserId,
        role,
        organizationId: project.organizationId
      });

    } else if (email) {
      // Inviting by email
      const invitationId = uuidv4();
      const invitation: ProjectInvitation = {
        id: invitationId,
        projectId,
        email,
        role,
        invitedBy: user.id,
        invitedAt: new Date().toISOString(),
        expiresAt: expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'pending',
        message,
        token: uuidv4(),
        tenantId: user.tenantId || user.id
      };

      await db.createItem('project-invitations', invitation);
      memberId = invitationId;

      // Log activity
      await projectManager['logProjectActivity'](user.id, 'member_invited', {
        projectId,
        email,
        role,
        invitationId,
        organizationId: project.organizationId
      });
    }

    // Publish event
    await eventGridIntegration.publishEvent({
      eventType: targetUserId ? 'Project.MemberAdded' : 'Project.MemberInvited',
      subject: `projects/${projectId}/members/${targetUserId ? 'added' : 'invited'}`,
      data: {
        projectId,
        projectName: project.name,
        memberId: targetUserId || email,
        role,
        addedBy: user.id,
        organizationId: project.organizationId,
        correlationId
      }
    });

    logger.info('Project member added successfully', {
      correlationId,
      projectId,
      memberId: targetUserId || email,
      role,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      jsonBody: {
        success: true,
        memberId: memberId!,
        role,
        status: targetUserId ? 'active' : 'invited'
      }
    }, request);

  } catch (error) {
    logger.error('Add project member failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId,
      projectId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

// Register HTTP functions
app.http('project-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects',
  handler: (request, context) => projectManager.createProject(request, context)
});

app.http('project-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/list',
  handler: (request, context) => projectManager.listProjects(request, context)
});

app.http('project-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/{projectId}',
  handler: (request, context) => projectManager.getProject(request, context)
});

app.http('project-update', {
  methods: ['PUT', 'PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/{projectId}/update',
  handler: updateProject
});

app.http('project-add-member', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/{projectId}/members',
  handler: addProjectMember
});

// Project activity endpoint
app.http('project-activity', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'projects/{projectId}/activity',
  handler: async (request, context) => {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    try {
      const user = await authenticateRequest(request);
      if (!user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const projectId = (context as any).bindingData?.projectId;
      if (!projectId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Project ID is required' }
        }, request);
      }

      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const pageSize = parseInt(url.searchParams.get('pageSize') || '20');
      const activityType = url.searchParams.get('activityType');

      // Verify project exists and user has access
      const project = await db.readItem('projects', projectId, user.user?.tenantId || 'default');
      if (!project) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Project not found' }
        }, request);
      }

      // Check permissions
      if (project.organizationId !== (user as any).organizationId) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Access denied' }
        }, request);
      }

      // Build query for project activities
      let sqlQuery = 'SELECT * FROM c WHERE c.projectId = @projectId';
      let parameters = [{ name: '@projectId', value: projectId }];

      if (activityType) {
        sqlQuery += ' AND c.type = @activityType';
        parameters.push({ name: '@activityType', value: activityType });
      }

      sqlQuery += ' ORDER BY c.timestamp DESC';

      // Get project activities
      const activities = await db.queryItems('activities', sqlQuery, parameters);

      // Apply pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedActivities = activities.slice(startIndex, endIndex);

      // Format activities for response
      const formattedActivities = paginatedActivities.map((activity: any) => ({
        id: activity.id,
        type: activity.type,
        description: activity.details?.description || `${activity.type} activity`,
        userId: activity.userId,
        projectId: activity.projectId,
        metadata: activity.details || {},
        createdAt: activity.timestamp,
        user: {
          id: activity.userId,
          email: '<EMAIL>', // Would be populated from user lookup
          firstName: 'User',
          lastName: 'Name'
        }
      }));

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          data: formattedActivities,
          pagination: {
            page,
            pageSize,
            total: activities.length,
            totalPages: Math.ceil(activities.length / pageSize)
          }
        }
      }, request);

    } catch (error) {
      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }
});
