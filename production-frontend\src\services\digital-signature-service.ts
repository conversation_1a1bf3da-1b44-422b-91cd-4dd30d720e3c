/**
 * Digital Signature Service
 * Frontend service for digital signature and approval management
 */

import { backendApiClient } from './backend-api-client';
import { memoryCache } from '../lib/cache';
// import { performanceMonitor } from '../lib/monitoring';

// Types for digital signatures
export interface DigitalSignature {
  id: string;
  documentId: string;
  signerId: string;
  signerName: string;
  signerEmail: string;
  signatureType: SignatureType;
  signatureData: SignatureData;
  position: SignaturePosition;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
  certificateInfo?: CertificateInfo;
  biometricData?: BiometricData;
  complianceLevel: ComplianceLevel;
  status: SignatureStatus;
  validationResults: ValidationResult[];
  organizationId: string;
  workflowExecutionId?: string;
  approvalLevelId?: string;
}

export interface ApprovalRequest {
  id: string;
  documentId: string;
  workflowExecutionId: string;
  approvalLevelId: string;
  requesterId: string;
  requesterName: string;
  approvers: ApprovalAssignment[];
  dueDate: string;
  priority: ApprovalPriority;
  message?: string;
  attachments: string[];
  status: ApprovalStatus;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  escalatedAt?: string;
  organizationId: string;
}

export interface ApprovalAssignment {
  id: string;
  approverId: string;
  approverName: string;
  approverEmail: string;
  role: string;
  status: ApprovalStatus;
  assignedAt: string;
  respondedAt?: string;
  response?: ApprovalResponse;
  delegatedTo?: string;
  escalationLevel: number;
}

export interface ApprovalResponse {
  action: ApprovalAction;
  comment?: string;
  conditions?: string[];
  attachments: string[];
  signatureRequired: boolean;
  digitalSignature?: DigitalSignature;
  timestamp: string;
  ipAddress: string;
  userAgent: string;
}

export interface SignatureWorkflow {
  id: string;
  name: string;
  description: string;
  documentTypes: string[];
  signatureRequirements: SignatureRequirement[];
  approvalFlow: ApprovalFlow;
  complianceSettings: SignatureComplianceSettings;
  organizationId: string;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Enums
export enum SignatureType {
  ELECTRONIC = 'electronic',
  DIGITAL = 'digital',
  BIOMETRIC = 'biometric',
  CERTIFICATE_BASED = 'certificate_based',
  MULTI_FACTOR = 'multi_factor'
}

export enum SignatureStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  EXPIRED = 'expired',
  REVOKED = 'revoked'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  DELEGATED = 'delegated',
  ESCALATED = 'escalated',
  EXPIRED = 'expired'
}

export enum ApprovalAction {
  APPROVE = 'approve',
  REJECT = 'reject',
  REQUEST_CHANGES = 'request_changes',
  DELEGATE = 'delegate',
  ESCALATE = 'escalate'
}

export enum ApprovalPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical'
}

export enum ComplianceLevel {
  BASIC = 'basic',
  STANDARD = 'standard',
  ENHANCED = 'enhanced',
  REGULATORY = 'regulatory',
  LEGAL = 'legal'
}

// Supporting interfaces
export interface SignatureData {
  imageData?: string;
  vectorData?: string;
  biometricHash?: string;
  certificateData?: string;
  encryptedData?: string;
  metadata: Record<string, any>;
}

export interface SignaturePosition {
  page: number;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface CertificateInfo {
  issuer: string;
  subject: string;
  serialNumber: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
  algorithm: string;
  keyUsage: string[];
}

export interface BiometricData {
  type: 'fingerprint' | 'voice' | 'facial' | 'iris';
  hash: string;
  confidence: number;
  template: string;
  deviceInfo: Record<string, any>;
}

export interface ValidationResult {
  type: 'integrity' | 'authenticity' | 'non_repudiation' | 'compliance';
  status: 'valid' | 'invalid' | 'warning';
  message: string;
  details: Record<string, any>;
  timestamp: string;
}

export interface SignatureRequirement {
  level: number;
  signerRoles: string[];
  signerUsers: string[];
  signatureType: SignatureType;
  complianceLevel: ComplianceLevel;
  required: boolean;
  conditions: SignatureCondition[];
}

export interface SignatureCondition {
  field: string;
  operator: string;
  value: any;
  description: string;
}

export interface ApprovalFlow {
  levels: ApprovalLevel[];
  parallelApproval: boolean;
  escalationEnabled: boolean;
  delegationEnabled: boolean;
  skipConditions: SkipCondition[];
}

export interface ApprovalLevel {
  level: number;
  name: string;
  approverRoles: string[];
  approverUsers: string[];
  requiredApprovals: number;
  timeoutHours: number;
  escalationChain: string[];
  signatureRequired: boolean;
}

export interface SkipCondition {
  field: string;
  operator: string;
  value: any;
  reason: string;
}

export interface SignatureComplianceSettings {
  frameworks: string[];
  auditRequired: boolean;
  timestampRequired: boolean;
  certificateValidation: boolean;
  biometricRequired: boolean;
  ipLogging: boolean;
  deviceFingerprinting: boolean;
}

class DigitalSignatureService {
  private readonly CACHE_TTL = 300000; // 5 minutes

  /**
   * Create signature request
   */
  async createSignatureRequest(requestData: {
    documentId: string;
    workflowExecutionId?: string;
    signatureType?: SignatureType;
    position: SignaturePosition;
    complianceLevel?: ComplianceLevel;
    message?: string;
    dueDate?: string;
  }): Promise<{
    signatureId: string;
    status: string;
    message: string;
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request('/signatures/request', {
        method: 'POST',
        body: JSON.stringify(requestData)
      });

      // // performanceMonitor.recordMetric(
      //   'createSignatureRequest',
      //   performance.now() - startTime
      // );

      return result;
    } catch (error) {
      // // performanceMonitor.recordError('createSignatureRequest', error as Error);
      throw error;
    }
  }

  /**
   * Apply digital signature
   */
  async applyDigitalSignature(signatureId: string, signatureData: {
    signatureData: SignatureData;
    pin?: string;
    biometricData?: BiometricData;
    certificateInfo?: CertificateInfo;
  }): Promise<{
    signatureId: string;
    status: string;
    validationResults: ValidationResult[];
    signedDocumentUrl: string;
    message: string;
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request(`/signatures/${signatureId}/apply`, {
        method: 'POST',
        body: JSON.stringify(signatureData)
      });

      // // performanceMonitor.recordMetric(
      //   'applyDigitalSignature',
      //   performance.now() - startTime
      // );

      // Clear related cache
      this.clearSignatureCache(signatureId);

      return result;
    } catch (error) {
      // // performanceMonitor.recordError('applyDigitalSignature', error as Error);
      throw error;
    }
  }

  /**
   * Get signature details
   */
  async getSignature(signatureId: string): Promise<DigitalSignature> {
    const startTime = performance.now();

    try {
      // Check cache first
      const cacheKey = `signature:${signatureId}`;
      const cached = memoryCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const signature = await backendApiClient.request<DigitalSignature>(`/signatures/${signatureId}`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getSignature',
      //   performance.now() - startTime
      // );

      // Cache the signature
      memoryCache.set(cacheKey, signature, this.CACHE_TTL);

      return signature;
    } catch (error) {
      // performanceMonitor.recordError('getSignature', error as Error);
      throw error;
    }
  }

  /**
   * Get document signatures
   */
  async getDocumentSignatures(documentId: string): Promise<DigitalSignature[]> {
    const startTime = performance.now();

    try {
      // Check cache first
      const cacheKey = `doc-signatures:${documentId}`;
      const cached = memoryCache.get(cacheKey);
      if (cached) {
        return cached;
      }

      const signatures = await backendApiClient.request<DigitalSignature[]>(`/documents/${documentId}/signatures`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getDocumentSignatures',
      //   performance.now() - startTime
      // );

      // Cache the signatures
      memoryCache.set(cacheKey, signatures, this.CACHE_TTL);

      return signatures;
    } catch (error) {
      // performanceMonitor.recordError('getDocumentSignatures', error as Error);
      throw error;
    }
  }

  /**
   * Create approval request
   */
  async createApprovalRequest(requestData: {
    documentId: string;
    workflowExecutionId: string;
    approvalLevelId: string;
    approvers: string[];
    dueDate: string;
    priority?: ApprovalPriority;
    message?: string;
    attachments?: string[];
  }): Promise<ApprovalRequest> {
    const startTime = performance.now();

    try {
      const request = await backendApiClient.request<ApprovalRequest>('/approvals/request', {
        method: 'POST',
        body: JSON.stringify(requestData)
      });

      // performanceMonitor.recordMetric(
      //   'createApprovalRequest',
      //   performance.now() - startTime
      // );

      return request;
    } catch (error) {
      // performanceMonitor.recordError('createApprovalRequest', error as Error);
      throw error;
    }
  }

  /**
   * Respond to approval request
   */
  async respondToApproval(approvalId: string, response: {
    action: ApprovalAction;
    comment?: string;
    conditions?: string[];
    attachments?: string[];
    signatureRequired?: boolean;
    signatureData?: SignatureData;
  }): Promise<{
    approvalId: string;
    status: string;
    message: string;
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request(`/approvals/${approvalId}/respond`, {
        method: 'POST',
        body: JSON.stringify(response)
      });

      // performanceMonitor.recordMetric(
      //   'respondToApproval',
      //   performance.now() - startTime
      // );

      // Clear related cache
      this.clearApprovalCache(approvalId);

      return result;
    } catch (error) {
      // performanceMonitor.recordError('respondToApproval', error as Error);
      throw error;
    }
  }

  /**
   * Get pending approvals for user
   */
  async getPendingApprovals(filters?: {
    priority?: ApprovalPriority;
    department?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    approvals: ApprovalRequest[];
    total: number;
    hasMore: boolean;
  }> {
    const startTime = performance.now();

    try {
      const params = new URLSearchParams();
      if (filters?.priority) params.append('priority', filters.priority);
      if (filters?.department) params.append('department', filters.department);
      if (filters?.limit) params.append('limit', filters.limit.toString());
      if (filters?.offset) params.append('offset', filters.offset.toString());

      const result = await backendApiClient.request(`/approvals/pending?${params}`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getPendingApprovals',
      //   performance.now() - startTime
      // );

      return result;
    } catch (error) {
      // performanceMonitor.recordError('getPendingApprovals', error as Error);
      throw error;
    }
  }

  /**
   * Validate signature
   */
  async validateSignature(signatureId: string): Promise<{
    isValid: boolean;
    validationResults: ValidationResult[];
    certificateStatus?: 'valid' | 'expired' | 'revoked' | 'unknown';
    trustLevel: 'high' | 'medium' | 'low';
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request(`/signatures/${signatureId}/validate`, {
        method: 'POST'
      });

      // performanceMonitor.recordMetric(
      //   'validateSignature',
      //   performance.now() - startTime
      // );

      return result;
    } catch (error) {
      // performanceMonitor.recordError('validateSignature', error as Error);
      throw error;
    }
  }

  /**
   * Get signature workflows
   */
  async getSignatureWorkflows(filters?: {
    documentType?: string;
    isActive?: boolean;
  }): Promise<SignatureWorkflow[]> {
    const startTime = performance.now();

    try {
      const params = new URLSearchParams();
      if (filters?.documentType) params.append('documentType', filters.documentType);
      if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString());

      const workflows = await backendApiClient.request<SignatureWorkflow[]>(`/signatures/workflows?${params}`, {
        method: 'GET'
      });

      // performanceMonitor.recordMetric(
      //   'getSignatureWorkflows',
      //   performance.now() - startTime
      // );

      return workflows;
    } catch (error) {
      // performanceMonitor.recordError('getSignatureWorkflows', error as Error);
      throw error;
    }
  }

  /**
   * Delegate approval
   */
  async delegateApproval(approvalId: string, delegateToUserId: string, reason?: string): Promise<{
    approvalId: string;
    status: string;
    message: string;
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request(`/approvals/${approvalId}/delegate`, {
        method: 'POST',
        body: JSON.stringify({
          delegateToUserId,
          reason
        })
      });

      // performanceMonitor.recordMetric(
      //   'delegateApproval',
      //   performance.now() - startTime
      // );

      // Clear related cache
      this.clearApprovalCache(approvalId);

      return result;
    } catch (error) {
      // performanceMonitor.recordError('delegateApproval', error as Error);
      throw error;
    }
  }

  /**
   * Escalate approval
   */
  async escalateApproval(approvalId: string, reason: string): Promise<{
    approvalId: string;
    status: string;
    message: string;
  }> {
    const startTime = performance.now();

    try {
      const result = await backendApiClient.request(`/approvals/${approvalId}/escalate`, {
        method: 'POST',
        body: JSON.stringify({ reason })
      });

      // performanceMonitor.recordMetric(
      //   'escalateApproval',
      //   performance.now() - startTime
      // );

      // Clear related cache
      this.clearApprovalCache(approvalId);

      return result;
    } catch (error) {
      // performanceMonitor.recordError('escalateApproval', error as Error);
      throw error;
    }
  }

  /**
   * Clear signature cache
   */
  private clearSignatureCache(signatureId: string): void {
    try {
      memoryCache.delete(`signature:${signatureId}`);
      // Clear document signatures cache that might contain this signature
      memoryCache.clear();
    } catch (error) {
      console.warn('Failed to clear signature cache:', signatureId);
    }
  }

  /**
   * Clear approval cache
   */
  private clearApprovalCache(approvalId: string): void {
    try {
      memoryCache.delete(`approval:${approvalId}`);
      // Clear pending approvals cache
      memoryCache.clear();
    } catch (error) {
      console.warn('Failed to clear approval cache:', approvalId);
    }
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    try {
      memoryCache.clear();
    } catch (error) {
      console.warn('Failed to clear cache');
    }
  }
}

// Create and export singleton instance
export const digitalSignatureService = new DigitalSignatureService();
export default digitalSignatureService;
