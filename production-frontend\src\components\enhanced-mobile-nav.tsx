"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, SheetTitle } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import {
  Menu,
  X,
  Home,
  Settings,
  LogOut,
  ChevronRight,
  User,
  Bell,
  Search,
  FileText,
  FolderKanban,
  Building2,
  Users,
  BarChart,
} from "lucide-react";

export interface MobileNavProps {
  user: {
    id: string;
    name: string;
    email: string;
    avatarUrl?: string;
  };
  onLogout: () => void;
  notifications?: {
    count: number;
    hasUnread: boolean;
  };
  customLinks?: {
    title: string;
    href: string;
    icon?: React.ReactNode;
  }[];
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function EnhancedMobileNav({
  user,
  onLogout,
  notifications,
  customLinks,
  className,
  open,
  onOpenChange,
}: MobileNavProps) {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  // Use controlled or uncontrolled state
  const isControlled = open !== undefined;
  const isSheetOpen = isControlled ? open : isOpen;

  const handleOpenChange = (newOpen: boolean) => {
    if (!isControlled) {
      setIsOpen(newOpen);
    }
    onOpenChange?.(newOpen);
  };

  // Close the menu when the path changes
  useEffect(() => {
    if (!isControlled) {
      setIsOpen(false);
    } else {
      onOpenChange?.(false);
    }
  }, [pathname, isControlled, onOpenChange]);

  // Get user initials for avatar fallback
  const getUserInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  // Default navigation links
  const defaultLinks = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: <Home className="h-5 w-5" />,
    },
    {
      title: "Projects",
      href: "/projects",
      icon: <FolderKanban className="h-5 w-5" />,
    },
    {
      title: "Organizations",
      href: "/organizations",
      icon: <Building2 className="h-5 w-5" />,
    },
    {
      title: "Teams",
      href: "/teams",
      icon: <Users className="h-5 w-5" />,
    },
    {
      title: "Analytics",
      href: "/analytics",
      icon: <BarChart className="h-5 w-5" />,
    },
    {
      title: "Settings",
      href: "/settings",
      icon: <Settings className="h-5 w-5" />,
    },
  ];

  // Combine default and custom links
  const links = customLinks ? [...defaultLinks, ...customLinks] : defaultLinks;

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className={cn("md:hidden", className)}
        onClick={() => handleOpenChange(true)}
      >
        <Menu className="h-5 w-5" />
      </Button>

      <Sheet open={isSheetOpen} onOpenChange={handleOpenChange}>
        <SheetContent side="left" className="p-0 flex flex-col">
          <SheetHeader className="p-4 border-b">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-lg font-bold">hepz</SheetTitle>
              <Button variant="ghost" size="icon" onClick={() => handleOpenChange(false)}>
                <X className="h-5 w-5" />
              </Button>
            </div>
          </SheetHeader>

          <div className="p-4 border-b">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user.avatarUrl} alt={user.name} />
                <AvatarFallback>{getUserInitials(user.name)}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{user.name}</div>
                <div className="text-sm text-muted-foreground">{user.email}</div>
              </div>
            </div>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-2">
              <div className="space-y-1">
                {links.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={cn(
                      "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
                      pathname === link.href
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    )}
                  >
                    {link.icon}
                    <span>{link.title}</span>
                    {pathname === link.href && (
                      <ChevronRight className="ml-auto h-4 w-4" />
                    )}
                  </Link>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="space-y-1">
                <Link
                  href="/profile"
                  className="flex items-center gap-3 rounded-md px-3 py-2 text-sm hover:bg-muted"
                >
                  <User className="h-5 w-5" />
                  <span>Profile</span>
                </Link>
                <Link
                  href="/notifications"
                  className="flex items-center gap-3 rounded-md px-3 py-2 text-sm hover:bg-muted"
                >
                  <div className="relative">
                    <Bell className="h-5 w-5" />
                    {notifications?.hasUnread && (
                      <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-primary" />
                    )}
                  </div>
                  <span>Notifications</span>
                  {notifications && notifications.count > 0 && (
                    <span className="ml-auto rounded-full bg-muted px-2 py-0.5 text-xs">
                      {notifications.count}
                    </span>
                  )}
                </Link>
                <Link
                  href="/search"
                  className="flex items-center gap-3 rounded-md px-3 py-2 text-sm hover:bg-muted"
                >
                  <Search className="h-5 w-5" />
                  <span>Search</span>
                </Link>
                <Link
                  href="/settings"
                  className="flex items-center gap-3 rounded-md px-3 py-2 text-sm hover:bg-muted"
                >
                  <Settings className="h-5 w-5" />
                  <span>Settings</span>
                </Link>
              </div>
            </div>
          </ScrollArea>

          <div className="border-t p-4">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={onLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Log out
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
