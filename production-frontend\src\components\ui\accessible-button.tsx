"use client";

import React, { forwardRef, useRef, useImperative<PERSON><PERSON><PERSON> } from "react";
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

export interface AccessibleButtonProps extends ButtonProps {
  /** ARIA label for screen readers */
  ariaLabel?: string;
  /** ARIA description for additional context */
  ariaDescription?: string;
  /** Loading state */
  isLoading?: boolean;
  /** Loading text for screen readers */
  loadingText?: string;
  /** Keyboard shortcut hint */
  shortcut?: string;
  /** Whether the button controls an expanded element */
  ariaExpanded?: boolean;
  /** ID of the element this button controls */
  ariaControls?: string;
  /** Whether this button has a popup */
  ariaHasPopup?: boolean | "false" | "true" | "menu" | "listbox" | "tree" | "grid" | "dialog";
  /** Whether the button is pressed (for toggle buttons) */
  ariaPressed?: boolean;
  /** Role override */
  role?: string;
  /** Tab index override */
  tabIndex?: number;
  /** Focus management */
  autoFocus?: boolean;
  /** Callback when focus is received */
  onFocus?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  /** Callback when focus is lost */
  onBlur?: (event: React.FocusEvent<HTMLButtonElement>) => void;
  /** Callback for keyboard events */
  onKeyDown?: (event: React.KeyboardEvent<HTMLButtonElement>) => void;
}

/**
 * Accessible button component with enhanced ARIA support and keyboard navigation
 */
export const AccessibleButton = forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  (
    {
      children,
      className,
      ariaLabel,
      ariaDescription,
      isLoading = false,
      loadingText = "Loading...",
      shortcut,
      ariaExpanded,
      ariaControls,
      ariaHasPopup,
      ariaPressed,
      role,
      tabIndex,
      autoFocus,
      disabled,
      onFocus,
      onBlur,
      onKeyDown,
      onClick,
      ...props
    },
    ref
  ) => {
    const internalRef = useRef<HTMLButtonElement>(null);
    
    // Use imperative handle to expose focus methods
    useImperativeHandle(ref, () => ({
      ...internalRef.current!,
      focus: () => internalRef.current?.focus(),
      blur: () => internalRef.current?.blur(),
    }));

    // Handle keyboard navigation
    const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
      // Call custom onKeyDown if provided
      onKeyDown?.(event);

      // Handle Enter and Space keys for activation
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        if (!disabled && !isLoading) {
          onClick?.(event as any);
        }
      }

      // Handle Escape key for dismissing popups
      if (event.key === "Escape" && ariaHasPopup) {
        event.preventDefault();
        internalRef.current?.blur();
      }
    };

    // Handle click events
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      if (disabled || isLoading) {
        event.preventDefault();
        return;
      }
      onClick?.(event);
    };

    // Build ARIA attributes
    const ariaAttributes = {
      "aria-label": ariaLabel,
      "aria-describedby": ariaDescription ? `${props.id || 'button'}-description` : undefined,
      "aria-expanded": ariaExpanded,
      "aria-controls": ariaControls,
      "aria-haspopup": ariaHasPopup,
      "aria-pressed": ariaPressed,
      "aria-disabled": disabled || isLoading,
      "aria-busy": isLoading,
    };

    // Filter out undefined values
    const cleanAriaAttributes = Object.fromEntries(
      Object.entries(ariaAttributes).filter(([_, value]) => value !== undefined)
    );

    return (
      <>
        <Button
          ref={internalRef}
          className={cn(
            // Focus styles for better accessibility
            "focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            // High contrast mode support
            "forced-colors:border-[ButtonBorder] forced-colors:text-[ButtonText]",
            className
          )}
          disabled={disabled || isLoading}
          role={role}
          tabIndex={tabIndex}
          autoFocus={autoFocus}
          onFocus={onFocus}
          onBlur={onBlur}
          onKeyDown={handleKeyDown}
          onClick={handleClick}
          {...cleanAriaAttributes}
          {...props}
        >
          {isLoading && (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
              <span className="sr-only">{loadingText}</span>
            </>
          )}
          
          {children}
          
          {shortcut && (
            <span className="ml-auto text-xs opacity-60" aria-hidden="true">
              {shortcut}
            </span>
          )}
        </Button>

        {/* Hidden description for screen readers */}
        {ariaDescription && (
          <span
            id={`${props.id || 'button'}-description`}
            className="sr-only"
          >
            {ariaDescription}
          </span>
        )}
      </>
    );
  }
);

AccessibleButton.displayName = "AccessibleButton";

/**
 * Accessible icon button component
 */
export interface AccessibleIconButtonProps extends AccessibleButtonProps {
  /** Icon component */
  icon: React.ReactNode;
  /** Required label for icon buttons */
  label: string;
}

export const AccessibleIconButton = forwardRef<HTMLButtonElement, AccessibleIconButtonProps>(
  ({ icon, label, children, ...props }, ref) => {
    return (
      <AccessibleButton
        ref={ref}
        ariaLabel={label}
        {...props}
      >
        {icon}
        {children && <span className="sr-only">{children}</span>}
      </AccessibleButton>
    );
  }
);

AccessibleIconButton.displayName = "AccessibleIconButton";

/**
 * Accessible toggle button component
 */
export interface AccessibleToggleButtonProps extends AccessibleButtonProps {
  /** Whether the toggle is pressed/active */
  pressed: boolean;
  /** Label for the pressed state */
  pressedLabel?: string;
  /** Label for the unpressed state */
  unpressedLabel?: string;
}

export const AccessibleToggleButton = forwardRef<HTMLButtonElement, AccessibleToggleButtonProps>(
  ({ pressed, pressedLabel, unpressedLabel, ariaLabel, children, ...props }, ref) => {
    const toggleLabel = pressed 
      ? (pressedLabel || `${ariaLabel || 'Toggle'} (active)`)
      : (unpressedLabel || `${ariaLabel || 'Toggle'} (inactive)`);

    return (
      <AccessibleButton
        ref={ref}
        ariaLabel={toggleLabel}
        ariaPressed={pressed}
        {...props}
      >
        {children}
      </AccessibleButton>
    );
  }
);

AccessibleToggleButton.displayName = "AccessibleToggleButton";

/**
 * Hook for managing focus within a button group
 */
export function useButtonGroupFocus(buttonCount: number) {
  const [focusedIndex, setFocusedIndex] = React.useState(0);
  const buttonRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const handleKeyDown = (event: React.KeyboardEvent, index: number) => {
    switch (event.key) {
      case "ArrowRight":
      case "ArrowDown":
        event.preventDefault();
        const nextIndex = (index + 1) % buttonCount;
        setFocusedIndex(nextIndex);
        buttonRefs.current[nextIndex]?.focus();
        break;
      case "ArrowLeft":
      case "ArrowUp":
        event.preventDefault();
        const prevIndex = (index - 1 + buttonCount) % buttonCount;
        setFocusedIndex(prevIndex);
        buttonRefs.current[prevIndex]?.focus();
        break;
      case "Home":
        event.preventDefault();
        setFocusedIndex(0);
        buttonRefs.current[0]?.focus();
        break;
      case "End":
        event.preventDefault();
        const lastIndex = buttonCount - 1;
        setFocusedIndex(lastIndex);
        buttonRefs.current[lastIndex]?.focus();
        break;
    }
  };

  const getButtonProps = (index: number) => ({
    ref: (el: HTMLButtonElement | null) => {
      buttonRefs.current[index] = el;
    },
    tabIndex: index === focusedIndex ? 0 : -1,
    onKeyDown: (event: React.KeyboardEvent<HTMLButtonElement>) => handleKeyDown(event, index),
    onFocus: () => setFocusedIndex(index),
  });

  return { getButtonProps, focusedIndex };
}
