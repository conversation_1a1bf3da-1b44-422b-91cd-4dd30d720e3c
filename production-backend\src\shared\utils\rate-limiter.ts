/**
 * Advanced Rate Limiter with Redis backend
 * Supports multiple rate limiting strategies and enterprise features
 */

import { redis } from '../services/redis';
import { logger } from './logger';

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  keyGenerator?: (identifier: string, operation: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (identifier: string, operation: string) => void;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
}

export class RateLimiter {
  private static instance: RateLimiter;
  private defaultConfig: RateLimitConfig = {
    windowMs: 60000, // 1 minute
    maxRequests: 100,
    keyGenerator: (identifier: string, operation: string) => `rate_limit:${operation}:${identifier}`
  };

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  /**
   * Check if request is within rate limit
   */
  async checkLimit(
    identifier: string, 
    operation: string, 
    config?: Partial<RateLimitConfig>
  ): Promise<RateLimitResult> {
    const finalConfig = { ...this.defaultConfig, ...config };
    const key = finalConfig.keyGenerator!(identifier, operation);
    const window = Math.floor(Date.now() / finalConfig.windowMs);
    const windowKey = `${key}:${window}`;

    try {
      // Use Redis operations for atomic operations
      const currentCount = await redis.incr(windowKey);
      await redis.expire(windowKey, Math.ceil(finalConfig.windowMs / 1000));

      const allowed = currentCount <= finalConfig.maxRequests;
      const remaining = Math.max(0, finalConfig.maxRequests - currentCount);
      const resetTime = (window + 1) * finalConfig.windowMs;

      if (!allowed && finalConfig.onLimitReached) {
        finalConfig.onLimitReached(identifier, operation);
      }

      return {
        allowed,
        remaining,
        resetTime,
        totalHits: currentCount
      };

    } catch (error) {
      logger.error('Rate limiter error', {
        identifier,
        operation,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // Fail open - allow request if Redis is down
      return {
        allowed: true,
        remaining: finalConfig.maxRequests,
        resetTime: Date.now() + finalConfig.windowMs,
        totalHits: 0
      };
    }
  }

  /**
   * Advanced rate limiting with multiple windows
   */
  async checkMultiWindowLimit(
    identifier: string,
    operation: string,
    configs: Array<{ window: number; limit: number }>
  ): Promise<RateLimitResult> {
    const results = await Promise.all(
      configs.map(config => 
        this.checkLimit(identifier, `${operation}:${config.window}`, {
          windowMs: config.window,
          maxRequests: config.limit
        })
      )
    );

    // Return the most restrictive result
    const restrictive = results.find(r => !r.allowed) || results[0];
    return restrictive;
  }

  /**
   * Burst rate limiting - allows short bursts but limits sustained usage
   */
  async checkBurstLimit(
    identifier: string,
    operation: string,
    burstLimit: number,
    sustainedLimit: number,
    burstWindow: number = 10000, // 10 seconds
    sustainedWindow: number = 300000 // 5 minutes
  ): Promise<RateLimitResult> {
    return this.checkMultiWindowLimit(identifier, operation, [
      { window: burstWindow, limit: burstLimit },
      { window: sustainedWindow, limit: sustainedLimit }
    ]);
  }

  /**
   * Get current usage statistics
   */
  async getUsageStats(identifier: string, operation: string): Promise<{
    currentWindow: number;
    previousWindow: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }> {
    const key = this.defaultConfig.keyGenerator!(identifier, operation);
    const currentWindow = Math.floor(Date.now() / this.defaultConfig.windowMs);
    const currentKey = `${key}:${currentWindow}`;
    const previousKey = `${key}:${currentWindow - 1}`;

    try {
      const [current, previous] = await Promise.all([
        redis.get(currentKey),
        redis.get(previousKey)
      ]);

      const currentCount = parseInt(current || '0');
      const previousCount = parseInt(previous || '0');

      let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
      if (currentCount > previousCount) trend = 'increasing';
      else if (currentCount < previousCount) trend = 'decreasing';

      return {
        currentWindow: currentCount,
        previousWindow: previousCount,
        trend
      };

    } catch (error) {
      logger.error('Failed to get usage stats', {
        identifier,
        operation,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        currentWindow: 0,
        previousWindow: 0,
        trend: 'stable'
      };
    }
  }

  /**
   * Reset rate limit for specific identifier and operation
   */
  async resetLimit(identifier: string, operation: string): Promise<void> {
    const key = this.defaultConfig.keyGenerator!(identifier, operation);
    const pattern = `${key}:*`;

    try {
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }
      
      logger.info('Rate limit reset', { identifier, operation, keysDeleted: keys.length });
    } catch (error) {
      logger.error('Failed to reset rate limit', {
        identifier,
        operation,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Predefined rate limit configurations for common operations
   */
  static readonly CONFIGS = {
    DOCUMENT_UPLOAD: {
      windowMs: 60000, // 1 minute
      maxRequests: 10, // 10 uploads per minute
      onLimitReached: (id: string) => logger.warn('Document upload rate limit reached', { userId: id })
    },
    
    AI_PROCESSING: {
      windowMs: 300000, // 5 minutes
      maxRequests: 20, // 20 AI operations per 5 minutes
      onLimitReached: (id: string) => logger.warn('AI processing rate limit reached', { userId: id })
    },
    
    API_CALLS: {
      windowMs: 60000, // 1 minute
      maxRequests: 1000, // 1000 API calls per minute
      onLimitReached: (id: string) => logger.warn('API rate limit reached', { userId: id })
    },
    
    SEARCH_QUERIES: {
      windowMs: 60000, // 1 minute
      maxRequests: 100, // 100 searches per minute
      onLimitReached: (id: string) => logger.warn('Search rate limit reached', { userId: id })
    },
    
    REPORT_GENERATION: {
      windowMs: 3600000, // 1 hour
      maxRequests: 10, // 10 reports per hour
      onLimitReached: (id: string) => logger.warn('Report generation rate limit reached', { userId: id })
    }
  };
}

export const rateLimiter = RateLimiter.getInstance();
