"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight, FileText } from "lucide-react";
import { useProjects } from "@/hooks/projects";
import { Project } from "@/types/project";

export default function UploadDocumentRedirectPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams?.get('projectId');
  const { data: projectsResponse, isLoading } = useProjects({});
  const projects = projectsResponse?.data || [];

  // If projectId is in URL, redirect to that project's upload page
  useEffect(() => {
    if (projectId) {
      router.push(`/projects/${projectId}/documents/upload`);
    } else if (!isLoading && projects.length > 0) {
      router.push(`/projects/${projects[0].id}/documents/upload`);
    }
  }, [isLoading, projectId, projects, router]);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Upload Document</h1>
        <p className="text-muted-foreground">
          Documents are now uploaded within projects
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Document Upload Has Moved</CardTitle>
          <CardDescription>
            Documents are now organized within projects for better workflow management
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex justify-center py-6">
            <FileText className="h-16 w-16 text-muted-foreground" />
          </div>

          {isLoading ? (
            <p className="text-center">Loading your projects...</p>
          ) : projects.length > 0 ? (
            <div className="space-y-4">
              <p className="text-center">Select a project to upload your document:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {projects.slice(0, 4).map((project: Project) => (
                  <Button
                    key={project.id}
                    variant="outline"
                    className="justify-between"
                    onClick={() => router.push(`/projects/${project.id}/documents/upload`)}
                  >
                    <span>{project.name}</span>
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                ))}
              </div>
              {projects.length > 4 && (
                <Button
                  variant="link"
                  className="w-full"
                  onClick={() => router.push('/projects')}
                >
                  View all projects
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-center">You need to create a project first to upload documents</p>
              <Button
                className="w-full"
                onClick={() => router.push('/projects/create')}
              >
                Create Your First Project
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
