/**
 * Enhanced PKI Signature Dialog
 * Component for creating Azure Key Vault PKI signatures with advanced compliance
 */

'use client';

import React, { useState, useRef } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  Clock, 
  Key,
  Lock,
  Award,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

import { usePKISignatures } from '@/hooks/usePKISignatures';
// import { SignaturePad } from '@/components/ui/signature-pad';

interface EnhancedPKISignatureDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentId: string;
  documentName: string;
  onSignatureCreated?: (signatureId: string) => void;
}

export function EnhancedPKISignatureDialog({
  open,
  onOpenChange,
  documentId,
  documentName,
  onSignatureCreated
}: EnhancedPKISignatureDialogProps) {
  const [step, setStep] = useState<'info' | 'sign' | 'confirm'>('info');
  const [signatureData, setSignatureData] = useState<string>('');
  const [signerName, setSignerName] = useState('');
  const [signerTitle, setSignerTitle] = useState('');
  const [signerReason, setSignerReason] = useState('');
  const [algorithm, setAlgorithm] = useState<'RS256' | 'RS384' | 'RS512'>('RS256');
  const signaturePadRef = useRef<any>(null);

  const {
    createSignature,
    isLoading,
    error,
    getComplianceInfo
  } = usePKISignatures({ documentId });

  const complianceInfo = getComplianceInfo('advanced');

  const handleSignatureComplete = (signature: string) => {
    setSignatureData(signature);
  };

  const handleClearSignature = () => {
    setSignatureData('');
    signaturePadRef.current?.clear();
  };

  const handleCreateSignature = async () => {
    if (!signatureData) {
      toast.error('Signature Required', {
        description: 'Please provide your signature before proceeding'
      });
      return;
    }

    try {
      const result = await createSignature({
        documentId,
        visualSignature: signatureData,
        algorithm
      });

      toast.success('Digital Signature Created', {
        description: 'Your secure digital signature has been successfully created using Azure Key Vault PKI'
      });

      onSignatureCreated?.(result.signatureId);
      onOpenChange(false);
      
      // Reset form
      setStep('info');
      setSignatureData('');
      setSignerName('');
      setSignerTitle('');
      setSignerReason('');
      
    } catch (err) {
      console.error('Failed to create PKI signature:', err);
    }
  };

  const renderInfoStep = () => (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Shield className="h-8 w-8 text-blue-600" />
        <div>
          <h3 className="text-lg font-semibold">Azure Key Vault PKI</h3>
          <p className="text-sm text-muted-foreground">
            Enterprise-grade digital signatures with HSM security
          </p>
        </div>
      </div>

      <Alert>
        <Award className="h-4 w-4" />
        <AlertDescription>
          <strong>Advanced Electronic Signature</strong> - Cryptographically secure with strong legal validity
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            Legal Compliance
          </h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            {complianceInfo.standards.map((standard: string) => (
              <li key={standard}>• {standard}</li>
            ))}
          </ul>
        </div>

        <div className="space-y-3">
          <h4 className="font-medium flex items-center gap-2">
            <Lock className="h-4 w-4 text-blue-600" />
            Security Features
          </h4>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li>• Hardware Security Module (HSM)</li>
            <li>• 4096-bit RSA encryption</li>
            <li>• Non-repudiation guarantee</li>
            <li>• Azure-managed certificates</li>
          </ul>
        </div>
      </div>

      <Separator />

      <div className="space-y-4">
        <h4 className="font-medium">Document Information</h4>
        <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
          <FileText className="h-5 w-5 text-blue-600" />
          <div>
            <p className="font-medium">{documentName}</p>
            <p className="text-sm text-muted-foreground">Document ID: {documentId}</p>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Signature Configuration</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="algorithm">Cryptographic Algorithm</Label>
            <select
              id="algorithm"
              value={algorithm}
              onChange={(e) => setAlgorithm(e.target.value as 'RS256' | 'RS384' | 'RS512')}
              className="w-full p-2 border rounded-md"
            >
              <option value="RS256">RSA-SHA256 (Standard)</option>
              <option value="RS384">RSA-SHA384 (Enhanced)</option>
              <option value="RS512">RSA-SHA512 (Maximum Security)</option>
            </select>
          </div>
          <div className="space-y-2">
            <Label>Key Strength</Label>
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded-md">
              <Key className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">4096-bit RSA</span>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h4 className="font-medium">Signer Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="signerName">Full Name</Label>
            <Input
              id="signerName"
              value={signerName}
              onChange={(e) => setSignerName(e.target.value)}
              placeholder="Enter your full name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="signerTitle">Title/Position</Label>
            <Input
              id="signerTitle"
              value={signerTitle}
              onChange={(e) => setSignerTitle(e.target.value)}
              placeholder="e.g., CEO, Manager, etc."
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="signerReason">Reason for Signing</Label>
          <Textarea
            id="signerReason"
            value={signerReason}
            onChange={(e) => setSignerReason(e.target.value)}
            placeholder="Enter the reason for signing this document"
            rows={3}
          />
        </div>
      </div>
    </div>
  );

  const renderSignStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Provide Your Signature</h3>
        <p className="text-sm text-muted-foreground">
          Draw your signature in the box below. This will be combined with your PKI certificate.
        </p>
      </div>

      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4">
        {/* <SignaturePad
          ref={signaturePadRef}
          onSignatureComplete={handleSignatureComplete}
          width={400}
          height={200}
          className="w-full"
        /> */}
        <div className="w-full h-48 bg-muted/20 rounded flex items-center justify-center text-muted-foreground">
          Signature Pad Component (To be implemented)
        </div>
      </div>

      <div className="flex justify-center gap-2">
        <Button
          variant="outline"
          onClick={handleClearSignature}
          size="sm"
        >
          Clear Signature
        </Button>
      </div>

      {signatureData && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Signature captured successfully. Click "Create Digital Signature" to proceed.
          </AlertDescription>
        </Alert>
      )}

      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Security Information</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="font-medium text-blue-800">Algorithm</p>
            <p className="text-blue-700">{algorithm}</p>
          </div>
          <div>
            <p className="font-medium text-blue-800">Key Storage</p>
            <p className="text-blue-700">Azure Key Vault HSM</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderConfirmStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Digital Signature Created Successfully</h3>
        <p className="text-sm text-muted-foreground">
          Your advanced digital signature has been applied to the document.
        </p>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-green-600" />
            <div>
              <p className="font-medium text-green-900">Advanced Digital Signature</p>
              <p className="text-sm text-green-700">Cryptographically secure with HSM protection</p>
            </div>
          </div>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            Legally Valid
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="font-medium">Timestamp</p>
            <p className="text-muted-foreground flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {new Date().toLocaleString()}
            </p>
          </div>
          <div>
            <p className="font-medium">Certificate Authority</p>
            <p className="text-muted-foreground">Azure Key Vault PKI</p>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            Enhanced Digital Signature
          </DialogTitle>
          <DialogDescription>
            Create a secure digital signature with advanced compliance using Azure Key Vault PKI
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {step === 'info' && renderInfoStep()}
        {step === 'sign' && renderSignStep()}
        {step === 'confirm' && renderConfirmStep()}

        <DialogFooter className="flex justify-between">
          <div className="flex gap-2">
            {step === 'sign' && (
              <Button
                variant="outline"
                onClick={() => setStep('info')}
                disabled={isLoading}
              >
                Back
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>

            {step === 'info' && (
              <Button
                onClick={() => setStep('sign')}
                disabled={!signerName.trim()}
              >
                Continue to Signature
              </Button>
            )}

            {step === 'sign' && (
              <Button
                onClick={handleCreateSignature}
                disabled={!signatureData || isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Signature...
                  </>
                ) : (
                  'Create Digital Signature'
                )}
              </Button>
            )}

            {step === 'confirm' && (
              <Button onClick={() => onOpenChange(false)}>
                Close
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default EnhancedPKISignatureDialog;
