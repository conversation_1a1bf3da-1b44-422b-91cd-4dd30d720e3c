/**
 * Bulk Operations Hooks
 * React hooks for bulk operations management based on backend API structure
 */

import { useState, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAILoading, useAIError, useAIOperations } from '@/stores/ai-store'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'

export interface BulkOperationConfig {
  batchSize: number
  maxConcurrency: number
  retryAttempts: number
  validateBeforeProcess: boolean
  parallelProcessing?: boolean
  generateThumbnails?: boolean
  extractText?: boolean
  autoProcess?: boolean
}

export interface FileUploadInfo {
  name: string
  size: number
  contentType: string
  content: string // Base64 encoded
  metadata?: Record<string, any>
}

export interface BulkUploadRequest {
  files: FileUploadInfo[]
  organizationId?: string
  projectId?: string
  config: BulkOperationConfig
}

export interface StorageOperation {
  id: string
  operationType: 'BULK_UPLOAD' | 'BULK_DELETE' | 'BULK_MOVE' | 'BULK_COPY' | 'DATA_MIGRATION'
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'
  organizationId: string
  parameters: {
    bulkConfig?: BulkOperationConfig
    migrationConfig?: any
    encryptionConfig?: any
    searchConfig?: any
    fileProcessingConfig?: any
    customParameters?: Record<string, any>
  }
  progress: {
    totalItems: number
    processedItems: number
    failedItems: number
    percentage: number
    currentItem?: string
    estimatedTimeRemaining?: number
  }
  results?: {
    successCount: number
    failureCount: number
    warnings: string[]
    errors: string[]
    summary: Record<string, any>
  }
  createdAt: string
  startedAt?: string
  completedAt?: string
  error?: string
}

export interface BulkDeleteRequest {
  documentIds: string[]
  organizationId?: string
  config?: {
    validateBeforeDelete: boolean
    createBackup: boolean
    batchSize: number
  }
}

export interface BulkMoveRequest {
  documentIds: string[]
  targetProjectId: string
  organizationId?: string
  config?: {
    preserveMetadata: boolean
    updateReferences: boolean
    batchSize: number
  }
}

export interface BulkProcessRequest {
  documentIds: string[]
  processingType: 'OCR' | 'ANALYSIS' | 'EXTRACTION' | 'CLASSIFICATION'
  organizationId?: string
  config?: {
    analysisOptions?: Record<string, any>
    extractionOptions?: Record<string, any>
    batchSize: number
    maxConcurrency: number
  }
}

/**
 * Hook to get all bulk operations
 */
export function useBulkOperations(params?: {
  organizationId?: string
  operationType?: string
  status?: string
  page?: number
  pageSize?: number
}) {
  const operations = useAIOperations()
  const loading = useAILoading()
  const error = useAIError()

  // Filter operations based on params
  const bulkOperations = operations.filter(op =>
    op.type === 'BULK_OPERATION' &&
    (!params?.organizationId || op.organizationId === params.organizationId) &&
    (!params?.status || op.status === params.status)
  )

  const refetch = useCallback(async () => {
    // Trigger a refresh of AI operations
    // This is a placeholder - in a real implementation you'd call the API
    console.log('Refreshing bulk operations...')
  }, [])

  return {
    data: bulkOperations,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get a specific bulk operation
 */
export function useBulkOperation(operationId: string) {
  const operations = useAIOperations()
  const loading = useAILoading()
  const error = useAIError()

  const operation = operations.find(op => op.id === operationId)

  return {
    data: operation,
    isLoading: loading,
    error,
    enabled: !!operationId
  }
}

/**
 * Hook to perform bulk upload
 */
export function useBulkUpload() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: BulkUploadRequest) => {
      return await backendApiClient.request<StorageOperation>('/storage/bulk/upload', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      toast({
        title: 'Bulk upload started',
        description: `Uploading ${operation.progress.totalItems} files. Operation ID: ${operation.id}`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error starting bulk upload',
        description: 'There was a problem starting the bulk upload. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to perform bulk delete
 */
export function useBulkDelete() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: BulkDeleteRequest) => {
      return await backendApiClient.request<StorageOperation>('/storage/bulk/delete', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      toast({
        title: 'Bulk delete started',
        description: `Deleting ${operation.progress.totalItems} documents. Operation ID: ${operation.id}`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error starting bulk delete',
        description: 'There was a problem starting the bulk delete. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to perform bulk move
 */
export function useBulkMove() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: BulkMoveRequest) => {
      return await backendApiClient.request<StorageOperation>('/storage/bulk/move', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      queryClient.invalidateQueries({ queryKey: ['documents'] })
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      toast({
        title: 'Bulk move started',
        description: `Moving ${operation.progress.totalItems} documents. Operation ID: ${operation.id}`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error starting bulk move',
        description: 'There was a problem starting the bulk move. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to perform bulk processing
 */
export function useBulkProcess() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (data: BulkProcessRequest) => {
      return await backendApiClient.request<StorageOperation>('/storage/bulk/process', {
        method: 'POST',
        body: JSON.stringify(data)
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      toast({
        title: 'Bulk processing started',
        description: `Processing ${operation.progress.totalItems} documents. Operation ID: ${operation.id}`,
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error starting bulk processing',
        description: 'There was a problem starting the bulk processing. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to cancel a bulk operation
 */
export function useCancelBulkOperation() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (operationId: string) => {
      return await backendApiClient.request(`/storage/operations/${operationId}/cancel`, {
        method: 'POST'
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operation', operation.id] })
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      toast({
        title: 'Operation cancelled',
        description: 'The bulk operation has been cancelled successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error cancelling operation',
        description: 'There was a problem cancelling the operation. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to retry a failed bulk operation
 */
export function useRetryBulkOperation() {
  const queryClient = useQueryClient()
  const { toast } = useToast()

  return useMutation({
    mutationFn: async (operationId: string) => {
      return await backendApiClient.request(`/storage/operations/${operationId}/retry`, {
        method: 'POST'
      })
    },
    onSuccess: (operation) => {
      queryClient.invalidateQueries({ queryKey: ['bulk-operation', operation.id] })
      queryClient.invalidateQueries({ queryKey: ['bulk-operations'] })
      toast({
        title: 'Operation restarted',
        description: 'The bulk operation has been restarted successfully.',
      })
    },
    onError: (_error) => {
      toast({
        title: 'Error retrying operation',
        description: 'There was a problem retrying the operation. Please try again.',
        variant: 'destructive',
      })
    },
  })
}

/**
 * Hook to get bulk operation statistics
 */
export function useBulkOperationStats(organizationId?: string) {
  return useQuery({
    queryKey: ['bulk-operation-stats', organizationId],
    queryFn: async () => {
      return await backendApiClient.request('/storage/operations/stats', {
        params: { organizationId }
      })
    },
    enabled: !!organizationId,
  })
}
