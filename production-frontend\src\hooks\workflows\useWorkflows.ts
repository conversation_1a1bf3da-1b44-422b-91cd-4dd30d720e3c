/**
 * Workflows Hook
 * Manages workflow operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'
import type { Workflow } from '@/types/backend'

export interface UseWorkflowsResult {
  workflows: Workflow[]
  data: Workflow[]
  loading: boolean
  isLoading: boolean
  error: string | null

  loadWorkflows: () => Promise<void>
  createWorkflow: (data: Partial<Workflow>) => Promise<Workflow>
  updateWorkflow: (workflowId: ID, updates: Partial<Workflow>) => Promise<void>
  deleteWorkflow: (workflowId: ID) => Promise<void>

  refresh: () => Promise<void>
}

export function useWorkflows(params?: { projectId?: ID } | ID): UseWorkflowsResult {
  // Handle both object and string parameters for backward compatibility
  const projectId = typeof params === 'string' ? params : params?.projectId
  const { toast } = useToast()
  
  const [workflows, setWorkflows] = useState<Workflow[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadWorkflows = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.getWorkflows({
        projectId: projectId || undefined
      })
      setWorkflows(response.data || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflows'
      setError(errorMessage)

      toast({
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [projectId, toast])

  const createWorkflow = useCallback(async (data: Partial<Workflow>): Promise<Workflow> => {
    try {
      const workflow = await backendApiClient.createWorkflow({
        name: data.name || '',
        description: data.description,
        organizationId: data.organizationId || '',
        projectId: data.projectId,
        steps: data.steps,
        settings: data.settings
      })
      await loadWorkflows()

      toast({
        title: 'Workflow created',
        description: `Workflow "${data.name}" has been created successfully.`,
      })

      return workflow
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create workflow'

      toast({
        title: 'Creation failed',
        description: errorMessage,
      })

      throw err
    }
  }, [loadWorkflows, toast])

  const updateWorkflow = useCallback(async (workflowId: ID, updates: Partial<Workflow>) => {
    try {
      await backendApiClient.updateWorkflow(workflowId, updates)
      await loadWorkflows()

      toast({
        title: 'Workflow updated',
        description: 'Workflow has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update workflow'

      toast({
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [loadWorkflows, toast])

  const deleteWorkflow = useCallback(async (workflowId: ID) => {
    try {
      await backendApiClient.deleteWorkflow(workflowId)
      await loadWorkflows()

      toast({
        title: 'Workflow deleted',
        description: 'Workflow has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete workflow'

      toast({
        title: 'Deletion failed',
        description: errorMessage,
      })
    }
  }, [loadWorkflows, toast])

  useEffect(() => {
    loadWorkflows()
  }, [loadWorkflows])

  return {
    workflows,
    data: workflows,
    loading,
    isLoading: loading,
    error,
    loadWorkflows,
    createWorkflow,
    updateWorkflow,
    deleteWorkflow,
    refresh: loadWorkflows,
  }
}

// Individual workflow hook
export function useWorkflow(workflowId: ID) {
  const [workflow, setWorkflow] = useState<Workflow | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const loadWorkflow = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const workflow = await backendApiClient.getWorkflow(workflowId)
      setWorkflow(workflow)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow'
      setError(errorMessage)

      toast({
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [workflowId, toast])

  useEffect(() => {
    if (workflowId) {
      loadWorkflow()
    }
  }, [workflowId, loadWorkflow])

  return {
    workflow,
    data: workflow, // For backward compatibility
    loading,
    isLoading: loading, // For backward compatibility
    error,
    isError: !!error, // For backward compatibility
    refresh: loadWorkflow,
    refetch: loadWorkflow, // For backward compatibility
  }
}

// Workflow execution hooks
export function useWorkflowExecution() {
  const { toast } = useToast()

  const executeWorkflow = useCallback(async (_workflowId: ID, _input?: Record<string, any>) => {
    try {
      // TODO: Add workflow execution endpoint to backend API client
      // const response = await backendApiClient.executeWorkflow(workflowId, input)

      toast({
        title: 'Workflow started',
        description: 'Workflow execution has been started.',
      })

      return { id: 'temp-execution-id' } // Temporary return
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to execute workflow'

      toast({
        title: 'Execution failed',
        description: errorMessage,
      })

      throw err
    }
  }, [toast])

  const stopWorkflow = useCallback(async (_workflowId: ID, _executionId: ID) => {
    try {
      // TODO: Add workflow stop endpoint to backend API client
      // await backendApiClient.stopWorkflowExecution(executionId)

      toast({
        title: 'Workflow stopped',
        description: 'Workflow execution has been stopped.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to stop workflow'

      toast({
        title: 'Stop failed',
        description: errorMessage,
      })
    }
  }, [toast])

  return {
    executeWorkflow,
    stopWorkflow,
  }
}

// Project-specific workflows hook
export function useProjectWorkflows(projectId: ID) {
  const result = useWorkflows(projectId)
  return {
    ...result,
    data: result.workflows, // For backward compatibility
    isLoading: result.loading, // For backward compatibility
  }
}

// Missing workflow hooks - placeholders
export function useCreateWorkflow() {
  const { toast } = useToast()

  return {
    mutate: async (data: any, options?: any) => {
      // TODO: Implement create workflow
      toast({
        title: 'Workflow created',
        description: 'Workflow has been created successfully.',
      })
      if (options?.onSuccess) {
        options.onSuccess(data)
      }
    },
    isLoading: false,
    error: null
  }
}

export function useUpdateWorkflow(_workflowId?: string) {
  const { toast } = useToast()

  return {
    mutate: async (data: any, options?: any) => {
      // TODO: Implement update workflow
      toast({
        title: 'Workflow updated',
        description: 'Workflow has been updated successfully.',
      })
      if (options?.onSuccess) {
        options.onSuccess(data)
      }
    },
    isPending: false,
    isLoading: false,
    error: null
  }
}

export function useCompleteWorkflowStep() {
  const { toast } = useToast()

  return {
    mutate: async (_stepId: string, _data?: any) => {
      // TODO: Implement complete workflow step
      toast({
        title: 'Step completed',
        description: 'Workflow step has been completed.',
      })
    },
    isLoading: false,
    error: null
  }
}

export function useRejectWorkflowStep() {
  const { toast } = useToast()

  return {
    mutate: async (_stepId: string, _reason?: string) => {
      // TODO: Implement reject workflow step
      toast({
        title: 'Step rejected',
        description: 'Workflow step has been rejected.',
      })
    },
    isLoading: false,
    error: null
  }
}

export default useWorkflows
