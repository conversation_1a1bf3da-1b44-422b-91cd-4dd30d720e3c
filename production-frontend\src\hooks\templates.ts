/**
 * Templates Hooks
 * React hooks for template management using Zustand store
 */

import { useCallback } from 'react'
import { useToast } from '@/hooks/use-toast'
import {
  useTemplateStore,
  useFetchTemplates,
  useFetchTemplate,
  useCreateTemplate,
  useUpdateTemplate,
  useDeleteTemplate,
  useCloneTemplate,
  useFetchFeaturedTemplates,
  useFetchPopularTemplates,
  useFetchPublicTemplates,
  useFetchTemplateInstances,
  useCreateTemplateInstance,
  useFetchTemplateAnalytics,
  useValidateTemplateData,
  useExportTemplate,
  usePreviewTemplate,
  useToggleTemplatePublish,
  useSearchTemplates,
  useTemplates,
  useSelectedTemplate,
  useFeaturedTemplates,
  usePopularTemplates,
  usePublicTemplates,
  useTemplateInstances,
  useTemplateAnalytics,
  useTemplateLoading,
  useTemplateError
} from '@/stores/template-store'
import type { Template } from '@/types/backend'
import type {
  TemplateSearchQuery,
  CreateTemplateRequest,
  UpdateTemplateRequest
} from '@/services/template-service'
import type { ID } from '@/types'

/**
 * Hook to get all templates with optional filters
 */
export function useTemplatesQuery(params?: TemplateSearchQuery) {
  const templates = useTemplates()
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchTemplates = useFetchTemplates()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchTemplates(params)
    } catch (error: any) {
      toast({
        title: 'Error fetching templates',
        description: error.message || 'Failed to fetch templates',
        variant: 'destructive',
      })
    }
  }, [fetchTemplates, params, toast])

  return {
    data: templates,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get a specific template by ID
 */
export function useTemplate(templateId: string) {
  const selectedTemplate = useSelectedTemplate()
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchTemplate = useFetchTemplate()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    if (!templateId) return
    try {
      await fetchTemplate(templateId)
    } catch (error: any) {
      toast({
        title: 'Error fetching template',
        description: error.message || 'Failed to fetch template',
        variant: 'destructive',
      })
    }
  }, [fetchTemplate, templateId, toast])

  return {
    data: selectedTemplate?.id === templateId ? selectedTemplate : null,
    isLoading: loading,
    error,
    refetch,
    enabled: !!templateId
  }
}

/**
 * Hook to get template categories
 */
export function useTemplateCategoriesQuery() {
  const categories = useTemplateStore(state => state.categories)
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchCategories = useTemplateStore(state => state.fetchCategories)
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchCategories()
    } catch (error: any) {
      toast({
        title: 'Error fetching categories',
        description: error.message || 'Failed to fetch template categories',
        variant: 'destructive',
      })
    }
  }, [fetchCategories, toast])

  return {
    data: categories,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get public templates
 */
export function usePublicTemplatesQuery() {
  const publicTemplates = usePublicTemplates()
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchPublicTemplates = useFetchPublicTemplates()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchPublicTemplates()
    } catch (error: any) {
      toast({
        title: 'Error fetching public templates',
        description: error.message || 'Failed to fetch public templates',
        variant: 'destructive',
      })
    }
  }, [fetchPublicTemplates, toast])

  return {
    data: publicTemplates,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get featured templates
 */
export function useFeaturedTemplatesQuery() {
  const featuredTemplates = useFeaturedTemplates()
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchFeaturedTemplates = useFetchFeaturedTemplates()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchFeaturedTemplates()
    } catch (error: any) {
      toast({
        title: 'Error fetching featured templates',
        description: error.message || 'Failed to fetch featured templates',
        variant: 'destructive',
      })
    }
  }, [fetchFeaturedTemplates, toast])

  return {
    data: featuredTemplates,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get popular templates
 */
export function usePopularTemplatesQuery(limit?: number) {
  const popularTemplates = usePopularTemplates()
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchPopularTemplates = useFetchPopularTemplates()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    try {
      await fetchPopularTemplates(limit)
    } catch (error: any) {
      toast({
        title: 'Error fetching popular templates',
        description: error.message || 'Failed to fetch popular templates',
        variant: 'destructive',
      })
    }
  }, [fetchPopularTemplates, limit, toast])

  return {
    data: popularTemplates,
    isLoading: loading,
    error,
    refetch
  }
}

/**
 * Hook to get template instances
 */
export function useTemplateInstancesQuery(templateId: string) {
  const instances = useTemplateInstances(templateId)
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchTemplateInstances = useFetchTemplateInstances()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    if (!templateId) return
    try {
      await fetchTemplateInstances(templateId)
    } catch (error: any) {
      toast({
        title: 'Error fetching template instances',
        description: error.message || 'Failed to fetch template instances',
        variant: 'destructive',
      })
    }
  }, [fetchTemplateInstances, templateId, toast])

  return {
    data: instances,
    isLoading: loading,
    error,
    refetch,
    enabled: !!templateId
  }
}

/**
 * Hook to get a specific template instance
 */
export function useTemplateInstance(templateId: string, instanceId: string) {
  const instances = useTemplateInstances(templateId)
  const loading = useTemplateLoading()
  const error = useTemplateError()

  const instance = instances.find((inst: any) => inst.id === instanceId)

  return {
    data: instance || null,
    isLoading: loading,
    error,
    enabled: !!templateId && !!instanceId
  }
}

/**
 * Hook to get template analytics
 */
export function useTemplateAnalyticsQuery(templateId: string) {
  const analytics = useTemplateAnalytics(templateId)
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const fetchTemplateAnalytics = useFetchTemplateAnalytics()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    if (!templateId) return
    try {
      await fetchTemplateAnalytics(templateId)
    } catch (error: any) {
      toast({
        title: 'Error fetching template analytics',
        description: error.message || 'Failed to fetch template analytics',
        variant: 'destructive',
      })
    }
  }, [fetchTemplateAnalytics, templateId, toast])

  return {
    data: analytics,
    isLoading: loading,
    error,
    refetch,
    enabled: !!templateId
  }
}

/**
 * Hook to create a new template
 */
export function useCreateTemplateHook() {
  const createTemplate = useCreateTemplate()
  const { toast } = useToast()

  const mutate = useCallback(async (data: CreateTemplateRequest) => {
    try {
      const template = await createTemplate(data as any)
      toast({
        title: 'Template created',
        description: `Template "${template.name}" has been created successfully.`,
      })
      return template
    } catch (error: any) {
      toast({
        title: 'Error creating template',
        description: error.message || 'There was a problem creating the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [createTemplate, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to update a template
 */
export function useUpdateTemplateHook() {
  const updateTemplate = useUpdateTemplate()
  const { toast } = useToast()

  const mutate = useCallback(async ({ templateId, data }: { templateId: string; data: UpdateTemplateRequest }) => {
    try {
      await updateTemplate(templateId, data)
      toast({
        title: 'Template updated',
        description: 'Template has been updated successfully.',
      })
    } catch (error: any) {
      toast({
        title: 'Error updating template',
        description: error.message || 'There was a problem updating the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [updateTemplate, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to delete a template
 */
export function useDeleteTemplateHook() {
  const deleteTemplate = useDeleteTemplate()
  const { toast } = useToast()

  const mutate = useCallback(async (templateId: string) => {
    try {
      await deleteTemplate(templateId)
      toast({
        title: 'Template deleted',
        description: 'The template has been deleted successfully.',
      })
      return templateId
    } catch (error: any) {
      toast({
        title: 'Error deleting template',
        description: error.message || 'There was a problem deleting the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [deleteTemplate, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to clone a template
 */
export function useCloneTemplateHook() {
  const cloneTemplate = useCloneTemplate()
  const { toast } = useToast()

  const mutate = useCallback(async ({ templateId, name }: { templateId: string; name: string }) => {
    try {
      const template = await cloneTemplate(templateId, name)
      toast({
        title: 'Template cloned',
        description: `Template "${template.name}" has been cloned successfully.`,
      })
      return template
    } catch (error: any) {
      toast({
        title: 'Error cloning template',
        description: error.message || 'There was a problem cloning the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [cloneTemplate, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to publish/unpublish a template
 */
export function useToggleTemplatePublishHook() {
  const toggleTemplatePublish = useToggleTemplatePublish()
  const { toast } = useToast()

  const mutate = useCallback(async ({ templateId, publish }: { templateId: string; publish: boolean }) => {
    try {
      await toggleTemplatePublish(templateId, publish)
      toast({
        title: publish ? 'Template published' : 'Template unpublished',
        description: `Template has been ${publish ? 'published' : 'unpublished'} successfully.`,
      })
    } catch (error: any) {
      toast({
        title: 'Error updating template',
        description: error.message || 'There was a problem updating the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [toggleTemplatePublish, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to create a template instance
 */
export function useCreateTemplateInstanceHook() {
  const createTemplateInstance = useCreateTemplateInstance()
  const { toast } = useToast()

  const mutate = useCallback(async ({
    templateId,
    data,
    name
  }: {
    templateId: string
    data: Record<string, any>
    name?: string
  }) => {
    try {
      const instance = await createTemplateInstance(templateId, data)
      toast({
        title: 'Instance created',
        description: `Template instance has been created successfully.`,
      })
      return instance
    } catch (error: any) {
      toast({
        title: 'Error creating instance',
        description: error.message || 'There was a problem creating the template instance. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [createTemplateInstance, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to search templates
 */
export function useSearchTemplatesQuery(query: TemplateSearchQuery) {
  const templates = useTemplates()
  const loading = useTemplateLoading()
  const error = useTemplateError()
  const searchTemplates = useSearchTemplates()
  const { toast } = useToast()

  const refetch = useCallback(async () => {
    if (!query.query || query.query.length === 0) return
    try {
      await searchTemplates(query.query, query)
    } catch (error: any) {
      toast({
        title: 'Error searching templates',
        description: error.message || 'Failed to search templates',
        variant: 'destructive',
      })
    }
  }, [searchTemplates, query, toast])

  return {
    data: templates,
    isLoading: loading,
    error,
    refetch,
    enabled: !!(query.query && query.query.length > 0)
  }
}

/**
 * Hook to preview a template
 */
export function usePreviewTemplateHook() {
  const previewTemplate = usePreviewTemplate()
  const { toast } = useToast()

  const mutate = useCallback(async ({ templateId, data }: { templateId: string; data?: Record<string, any> }) => {
    try {
      return await previewTemplate(templateId, data)
    } catch (error: any) {
      toast({
        title: 'Error previewing template',
        description: error.message || 'There was a problem previewing the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [previewTemplate, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to export a template
 */
export function useExportTemplateHook() {
  const exportTemplate = useExportTemplate()
  const { toast } = useToast()

  const mutate = useCallback(async ({ templateId, format }: { templateId: string; format?: string }) => {
    try {
      await exportTemplate(templateId, format)
      toast({
        title: 'Export started',
        description: 'Your template export is being prepared for download.',
      })
    } catch (error: any) {
      toast({
        title: 'Error exporting template',
        description: error.message || 'There was a problem exporting the template. Please try again.',
        variant: 'destructive',
      })
      throw error
    }
  }, [exportTemplate, toast])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}

/**
 * Hook to validate template data
 */
export function useValidateTemplateDataHook() {
  const validateTemplateData = useValidateTemplateData()

  const mutate = useCallback(async ({ templateId, data }: { templateId: string; data: Record<string, any> }) => {
    return await validateTemplateData(templateId, data)
  }, [validateTemplateData])

  const mutateAsync = mutate

  return {
    mutate,
    mutateAsync,
    isLoading: useTemplateLoading(),
    error: useTemplateError()
  }
}
