"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { ZoomIn, ZoomOut, Download, Printer, ChevronLeft, ChevronRight, Eye, Code, FileText } from "lucide-react";
import { Document } from "@/types/backend";


interface DocumentViewerProps {
  document: Document;
  isLoading?: boolean;
  onDownload?: () => void;
}

export function DocumentViewer({ document, isLoading = false, onDownload }: DocumentViewerProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [zoom, setZoom] = useState(100);
  const [activeTab, setActiveTab] = useState("preview");

  useEffect(() => {
    // Reset to first page when document changes
    setCurrentPage(1);

    // Set total pages based on document metadata
    if (document?.metadata?.extracted?.pageCount) {
      setTotalPages(document.metadata.extracted.pageCount);
    }
  }, [document]);

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 25, 50));
  };

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePrint = () => {
    if (document.downloadUrl) {
      const printWindow = window.open(document.downloadUrl, '_blank');
      if (printWindow) {
        printWindow.addEventListener('load', () => {
          printWindow.print();
        });
      }
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center justify-between p-4 border-b">
            <Skeleton className="h-8 w-40" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
              <Skeleton className="h-8 w-8 rounded-md" />
            </div>
          </div>
          <Skeleton className="h-[calc(100vh-300px)] w-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <Tabs defaultValue="preview" value={activeTab} onValueChange={setActiveTab}>
          <div className="flex items-center justify-between p-4 border-b">
            <TabsList>
              <TabsTrigger value="preview" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="text" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Text
              </TabsTrigger>
              <TabsTrigger value="data" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Data
              </TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2">
              {activeTab === "preview" && (
                <>
                  <Button variant="outline" size="icon" onClick={handleZoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm">{zoom}%</span>
                  <Button variant="outline" size="icon" onClick={handleZoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </>
              )}
              <Button variant="outline" size="icon" onClick={onDownload}>
                <Download className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" onClick={handlePrint}>
                <Printer className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <TabsContent value="preview" className="p-0">
            <div className="relative">
              {totalPages > 1 && (
                <div className="absolute bottom-4 left-0 right-0 flex items-center justify-center gap-4 z-10">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handlePreviousPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm bg-background/80 px-2 py-1 rounded">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}

              <div
                className="flex justify-center"
                style={{ zoom: `${zoom}%` }}
              >
                {document.downloadUrl ? (
                  <iframe
                    src={document.downloadUrl}
                    className="w-full h-[calc(100vh-300px)]"
                    title={document.name}
                  />
                ) : (
                  <div className="flex items-center justify-center h-[calc(100vh-300px)] bg-muted/20 rounded-md">
                    <p className="text-muted-foreground">Preview not available</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="text" className="p-4">
            {document.metadata?.computed?.searchableText ? (
              <pre className="whitespace-pre-wrap text-sm p-4 bg-muted/20 rounded-md h-[calc(100vh-300px)] overflow-auto">
                {document.metadata.computed.searchableText}
              </pre>
            ) : (
              <div className="flex items-center justify-center h-[calc(100vh-300px)] bg-muted/20 rounded-md">
                <p className="text-muted-foreground">Text content not available</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="data" className="p-4">
            <div className="flex items-center justify-center h-[calc(100vh-300px)] bg-muted/20 rounded-md">
              <p className="text-muted-foreground">No extracted data available</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
