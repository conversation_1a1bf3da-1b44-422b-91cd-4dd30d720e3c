'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { But<PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Activity, Zap, Download, RefreshCw } from 'lucide-react'
import { performanceMonitor } from '@/lib/performance'

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  status: 'good' | 'warning' | 'poor'
  description: string
}

export function PerformanceWidget() {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([])
  const [score, setScore] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadPerformanceData()
    
    // Refresh every 30 seconds
    const interval = setInterval(loadPerformanceData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadPerformanceData = async () => {
    setIsLoading(true)
    
    try {
      // Fetch real performance stats from the performance monitor API
      const response = await fetch('/api/performance/metrics', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        const stats = data.stats || {};

        const currentMetrics: PerformanceMetric[] = [
          {
            name: 'Cache Hit Rate',
            value: (stats.cacheHitRate || 0) * 100,
            unit: '%',
            status: (stats.cacheHitRate || 0) > 0.8 ? 'good' : (stats.cacheHitRate || 0) > 0.6 ? 'warning' : 'poor',
            description: 'Percentage of requests served from cache'
          },
          {
            name: 'Average Response Time',
            value: (stats.averageResponseTime || 0) / 1000,
            unit: 's',
            status: (stats.averageResponseTime || 0) < 1000 ? 'good' : (stats.averageResponseTime || 0) < 3000 ? 'warning' : 'poor',
            description: 'Average API response time'
          },
          {
            name: 'Total API Calls',
            value: stats.totalApiCalls || 0,
            unit: '',
            status: 'good',
            description: 'Total number of API requests made'
          },
          {
            name: 'Cache Size',
            value: stats.cacheSize || 0,
            unit: 'items',
            status: (stats.cacheSize || 0) < (stats.maxCacheSize || 100) * 0.8 ? 'good' : 'warning',
            description: 'Current number of cached items'
          },
          {
            name: 'Memory Usage',
            value: stats.memoryUsage || 0,
            unit: 'MB',
            status: (stats.memoryUsage || 0) < 512 ? 'good' : (stats.memoryUsage || 0) < 1024 ? 'warning' : 'poor',
            description: 'Current memory consumption'
          },
          {
            name: 'Error Rate',
            value: (stats.errorRate || 0) * 100,
            unit: '%',
            status: (stats.errorRate || 0) < 0.01 ? 'good' : (stats.errorRate || 0) < 0.05 ? 'warning' : 'poor',
            description: 'Percentage of failed requests'
          }
        ];

        setMetrics(currentMetrics);
        setScore(data.performanceScore || 0);
      } else {
        // Fallback to basic performance metrics
        const fallbackMetrics = await generateFallbackMetrics();
        setMetrics(fallbackMetrics);
        setScore(calculateFallbackScore(fallbackMetrics));
      }
    } catch (error) {
      console.error('Failed to load performance data:', error);

      // Generate fallback metrics from browser performance API
      const fallbackMetrics = await generateFallbackMetrics();
      setMetrics(fallbackMetrics);
      setScore(calculateFallbackScore(fallbackMetrics));
    } finally {
      setIsLoading(false)
    }
  }

  // Generate fallback metrics using browser performance API
  const generateFallbackMetrics = async (): Promise<PerformanceMetric[]> => {
    const metrics: PerformanceMetric[] = [];

    try {
      // Use Performance API if available
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.loadEventStart;
          const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;

          metrics.push({
            name: 'Page Load Time',
            value: loadTime,
            unit: 'ms',
            status: loadTime < 2000 ? 'good' : loadTime < 5000 ? 'warning' : 'poor',
            description: 'Time to fully load the page'
          });

          metrics.push({
            name: 'DOM Content Loaded',
            value: domContentLoaded,
            unit: 'ms',
            status: domContentLoaded < 1000 ? 'good' : domContentLoaded < 3000 ? 'warning' : 'poor',
            description: 'Time to load DOM content'
          });
        }

        // Memory usage if available
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          const memoryUsage = memory.usedJSHeapSize / (1024 * 1024); // Convert to MB

          metrics.push({
            name: 'Memory Usage',
            value: memoryUsage,
            unit: 'MB',
            status: memoryUsage < 50 ? 'good' : memoryUsage < 100 ? 'warning' : 'poor',
            description: 'JavaScript heap memory usage'
          });
        }
      }

      // Add basic connection info
      if (typeof navigator !== 'undefined' && 'connection' in navigator) {
        const connection = (navigator as any).connection;
        if (connection) {
          metrics.push({
            name: 'Connection Speed',
            value: connection.downlink || 0,
            unit: 'Mbps',
            status: (connection.downlink || 0) > 10 ? 'good' : (connection.downlink || 0) > 1 ? 'warning' : 'poor',
            description: 'Network connection speed'
          });
        }
      }

      // If no metrics available, provide basic defaults
      if (metrics.length === 0) {
        metrics.push({
          name: 'System Status',
          value: 100,
          unit: '%',
          status: 'good',
          description: 'Overall system health'
        });
      }

    } catch (error) {
      console.error('Error generating fallback metrics:', error);

      // Minimal fallback
      metrics.push({
        name: 'System Status',
        value: 85,
        unit: '%',
        status: 'good',
        description: 'Basic system health check'
      });
    }

    return metrics;
  };

  // Calculate performance score from metrics
  const calculateFallbackScore = (metrics: PerformanceMetric[]): number => {
    if (metrics.length === 0) return 50;

    const scores = metrics.map(metric => {
      switch (metric.status) {
        case 'good': return 90;
        case 'warning': return 70;
        case 'poor': return 40;
        default: return 60;
      }
    });

    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreStatus = (score: number) => {
    if (score >= 90) return 'Excellent'
    if (score >= 70) return 'Good'
    if (score >= 50) return 'Fair'
    return 'Poor'
  }

  const getMetricColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-600'
      case 'warning':
        return 'text-yellow-600'
      case 'poor':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const exportMetrics = () => {
    // Export performance metrics to JSON file
    const data = JSON.stringify({
      timestamp: new Date().toISOString(),
      metrics: metrics,
      score: score
    }, null, 2)
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-metrics-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Performance Metrics
          </CardTitle>
          <CardDescription>
            Application performance and optimization insights
          </CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={exportMetrics}
            className="h-8"
          >
            <Download className="h-3 w-3 mr-1" />
            Export
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={loadPerformanceData}
            disabled={isLoading}
            className="h-8"
          >
            <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Performance Score */}
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className={`text-3xl font-bold ${getScoreColor(score)}`}>
                {score}
              </div>
              <div className="text-sm text-muted-foreground">
                Performance Score - {getScoreStatus(score)}
              </div>
              <Progress value={score} className="mt-2" />
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-2 gap-4">
              {metrics.slice(0, 4).map((metric, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{metric.name}</span>
                    <Badge variant="outline" className={getMetricColor(metric.status)}>
                      {metric.status}
                    </Badge>
                  </div>
                  <div className={`text-2xl font-bold ${getMetricColor(metric.status)}`}>
                    {typeof metric.value === 'number' 
                      ? metric.value.toLocaleString(undefined, { 
                          maximumFractionDigits: metric.unit === '%' ? 1 : 0 
                        })
                      : metric.value
                    }
                    <span className="text-sm font-normal text-muted-foreground ml-1">
                      {metric.unit}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {metric.description}
                  </p>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            {/* Detailed Metrics */}
            <div className="space-y-3">
              {metrics.map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{metric.name}</span>
                      <Badge variant="outline" className={getMetricColor(metric.status)}>
                        {metric.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {metric.description}
                    </p>
                  </div>
                  <div className={`text-lg font-bold ${getMetricColor(metric.status)}`}>
                    {typeof metric.value === 'number' 
                      ? metric.value.toLocaleString(undefined, { 
                          maximumFractionDigits: metric.unit === '%' ? 1 : 0 
                        })
                      : metric.value
                    }
                    <span className="text-sm font-normal text-muted-foreground ml-1">
                      {metric.unit}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Performance Tips */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Performance Tips
              </h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Enable caching for frequently accessed data</li>
                <li>• Optimize API calls by batching requests</li>
                <li>• Use pagination for large data sets</li>
                <li>• Implement proper error handling and retry logic</li>
              </ul>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
