/**
 * Templates Hooks Index
 * Re-exports all template-related hooks
 */

export { useTemplates } from './useTemplates'
export type { UseTemplatesOptions, UseTemplatesResult } from './useTemplates'

// Import actual implementations from template store
export {
  useFetchTemplate as useTemplate,
  useToggleTemplatePublish as usePublishTemplate
} from '@/stores/template-store'

// Export the actual hook functions with correct names
export { useCreateTemplateHook as useCreateTemplate } from '../templates'
export { useUpdateTemplateHook as useUpdateTemplate } from '../templates'
export { useDeleteTemplateHook as useDeleteTemplate } from '../templates'
export { useToggleTemplatePublishHook as useArchiveTemplate } from '../templates'








