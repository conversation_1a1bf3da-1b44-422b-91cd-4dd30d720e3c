"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Search, FileText, Users, FolderKanban, Loader2, Command, Brain, Sparkles } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useRouter } from 'next/navigation'
import { useDebounce } from '@/hooks/useDebounce'
import { useUser } from '@/stores'
import { backendApiClient } from '@/services/backend-api-client'

interface SearchResult {
  id: string
  title: string
  type: 'document' | 'project' | 'organization' | 'user' | 'ai-answer'
  description?: string
  url?: string
  content?: string
  score?: number
  metadata?: {
    projectName?: string
    organizationName?: string
    lastModified?: string
    author?: string
    documentType?: string
    tags?: string[]
    aiGenerated?: boolean
    confidence?: number
  }
}

interface AISearchResult {
  answer: string
  sources: Array<{
    documentId: string
    documentName: string
    content: string
    relevanceScore: number
    pageNumber?: number
  }>
  confidence: number
  reasoning?: string
}

export function GlobalSearch() {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [aiResult, setAiResult] = useState<AISearchResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [searchMode, setSearchMode] = useState<'regular' | 'ai'>('regular')
  const debouncedQuery = useDebounce(query, 300)
  const router = useRouter()
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const user = useUser()

  // Intelligent search using existing backend endpoint
  const performIntelligentSearch = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim() || !user?.organizationId) return []

    try {
      const response = await backendApiClient.request('/search/intelligent', {
        method: 'POST',
        body: JSON.stringify({
          query: searchQuery,
          organizationId: user.organizationId,
          searchType: 'hybrid',
          maxResults: 10
        })
      })

      return response.results?.map((result: any) => ({
        id: result.id,
        title: result.title || result.name || 'Untitled',
        type: result.type || 'document' as const,
        description: result.description || result.content?.substring(0, 200) + '...',
        url: result.url || `/documents/${result.id}`,
        content: result.content,
        score: result.score || result.relevanceScore,
        metadata: {
          projectName: result.metadata?.projectName,
          organizationName: result.metadata?.organizationName,
          lastModified: result.metadata?.lastModified || result.updatedAt,
          author: result.metadata?.author || result.createdBy,
          documentType: result.metadata?.documentType || result.type,
          tags: result.metadata?.tags
        }
      })) || []
    } catch (error) {
      console.error('Intelligent search error:', error)
      return []
    }
  }

  // AI-powered search with RAG
  const performAISearch = async (searchQuery: string): Promise<AISearchResult | null> => {
    if (!searchQuery.trim() || !user?.organizationId) return null

    try {
      const response = await backendApiClient.request('/rag/query', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'RAG_QUERY',
          ragRequest: {
            operation: 'QUERY',
            queryData: {
              query: searchQuery,
              maxResults: 5,
              minRelevance: 0.7,
              includeReasoning: true,
              useAdvancedAI: true,
              temperature: 0.3,
              maxTokens: 2000,
              contextWindow: 4000
            }
          },
          organizationId: user.organizationId
        })
      })

      return response
    } catch (error) {
      console.error('AI search error:', error)
      return null
    }
  }

  // Basic search using existing backend endpoint
  const performBasicSearch = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim() || !user?.organizationId) return []

    try {
      const response = await backendApiClient.request('/search', {
        method: 'GET',
        params: {
          q: searchQuery,
          organizationId: user.organizationId,
          limit: 10
        }
      })

      return response.results?.map((result: any) => ({
        id: result.id,
        title: result.title || result.name || 'Untitled',
        type: result.type || 'document' as const,
        description: result.description || result.content?.substring(0, 200) + '...',
        url: result.url || `/documents/${result.id}`,
        metadata: {
          lastModified: result.updatedAt || result.lastModified,
          author: result.author || result.createdBy
        }
      })) || []
    } catch (error) {
      console.error('Basic search error:', error)
      return []
    }
  }

  useEffect(() => {
    if (debouncedQuery) {
      setIsLoading(true)

      const performSearch = async () => {
        try {
          if (searchMode === 'ai') {
            // AI search with RAG
            const [regularResults, aiResult] = await Promise.all([
              performIntelligentSearch(debouncedQuery),
              performAISearch(debouncedQuery)
            ])

            setResults(regularResults)
            setAiResult(aiResult)
          } else {
            // Regular search with intelligent search fallback
            let searchResults = await performIntelligentSearch(debouncedQuery)

            // Fallback to basic search if intelligent search fails
            if (searchResults.length === 0) {
              searchResults = await performBasicSearch(debouncedQuery)
            }

            setResults(searchResults)
            setAiResult(null)
          }
        } finally {
          setIsLoading(false)
        }
      }

      performSearch()
    } else {
      setResults([])
      setAiResult(null)
    }
  }, [debouncedQuery, searchMode, user?.organizationId])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault()
        inputRef.current?.focus()
        setIsOpen(true)
      }

      if (isOpen) {
        if (event.key === 'Escape') {
          setIsOpen(false)
          inputRef.current?.blur()
        } else if (event.key === 'ArrowDown') {
          event.preventDefault()
          setSelectedIndex(prev => Math.min(prev + 1, results.length - 1))
        } else if (event.key === 'ArrowUp') {
          event.preventDefault()
          setSelectedIndex(prev => Math.max(prev - 1, 0))
        } else if (event.key === 'Enter') {
          event.preventDefault()
          if (results[selectedIndex]?.url) {
            router.push(results[selectedIndex].url!)
            setIsOpen(false)
            setQuery('')
          }
        } else if ((event.metaKey || event.ctrlKey) && event.key === 'i') {
          event.preventDefault()
          setSearchMode(prev => prev === 'ai' ? 'regular' : 'ai')
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, results, selectedIndex, router])

  const getTypeIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'document':
        return <FileText className="h-4 w-4" />
      case 'project':
        return <FolderKanban className="h-4 w-4" />
      case 'user':
        return <Users className="h-4 w-4" />
      case 'ai-answer':
        return <Brain className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  const getTypeBadgeColor = (type: SearchResult['type']) => {
    switch (type) {
      case 'document':
        return 'bg-blue-100 text-blue-800'
      case 'project':
        return 'bg-green-100 text-green-800'
      case 'user':
        return 'bg-purple-100 text-purple-800'
      case 'ai-answer':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div ref={searchRef} className="relative w-full max-w-md">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={searchMode === 'ai' ? "Ask AI anything... (⌘K)" : "Search... (⌘K)"}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          className={cn(
            "pl-10 pr-20",
            searchMode === 'ai' && "border-orange-200 focus:border-orange-400"
          )}
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {isLoading && <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />}
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-6 px-2 text-xs",
              searchMode === 'ai' ? "bg-orange-100 text-orange-700" : "bg-muted"
            )}
            onClick={() => setSearchMode(prev => prev === 'ai' ? 'regular' : 'ai')}
          >
            {searchMode === 'ai' ? <Brain className="h-3 w-3" /> : <Sparkles className="h-3 w-3" />}
          </Button>
          <kbd className="hidden sm:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
            <Command className="h-3 w-3" />K
          </kbd>
        </div>
      </div>

      {isOpen && (query || results.length > 0 || aiResult) && (
        <Card className="absolute top-full mt-2 w-full z-50 shadow-lg max-w-2xl">
          <CardContent className="p-0">
            {/* AI Answer Section */}
            {aiResult && (
              <div className="border-b bg-orange-50/50 p-4">
                <div className="flex items-start space-x-3">
                  <Brain className="h-5 w-5 text-orange-600 mt-0.5" />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="text-sm font-medium text-orange-900">AI Answer</h4>
                      <Badge className="bg-orange-100 text-orange-700 text-xs">
                        {Math.round(aiResult.confidence * 100)}% confident
                      </Badge>
                    </div>
                    <p className="text-sm text-orange-800 mb-3">{aiResult.answer}</p>
                    {aiResult.sources.length > 0 && (
                      <div>
                        <p className="text-xs font-medium text-orange-700 mb-1">Sources:</p>
                        <div className="space-y-1">
                          {aiResult.sources.slice(0, 3).map((source, idx) => (
                            <div key={idx} className="text-xs text-orange-600">
                              • {source.documentName} (relevance: {Math.round(source.relevanceScore * 100)}%)
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Regular Search Results */}
            {results.length > 0 ? (
              <div className="max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <div
                    key={result.id}
                    className={cn(
                      "flex items-start space-x-3 p-3 cursor-pointer border-b last:border-b-0 hover:bg-muted/50",
                      index === selectedIndex && "bg-muted/50"
                    )}
                    onClick={() => {
                      if (result.url) {
                        router.push(result.url)
                        setIsOpen(false)
                        setQuery('')
                      }
                    }}
                  >
                    <div className="flex-shrink-0 mt-1">
                      {getTypeIcon(result.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium truncate">{result.title}</h4>
                        <Badge className={cn("text-xs", getTypeBadgeColor(result.type))}>
                          {result.type}
                        </Badge>
                        {result.score && (
                          <Badge variant="outline" className="text-xs">
                            {Math.round(result.score * 100)}%
                          </Badge>
                        )}
                      </div>
                      {result.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {result.description}
                        </p>
                      )}
                      {result.metadata && (
                        <div className="flex items-center space-x-2 mt-1 text-xs text-muted-foreground">
                          {result.metadata.projectName && (
                            <span>{result.metadata.projectName}</span>
                          )}
                          {result.metadata.organizationName && (
                            <span>• {result.metadata.organizationName}</span>
                          )}
                          {result.metadata.lastModified && (
                            <span>• {result.metadata.lastModified}</span>
                          )}
                          {result.metadata.tags && result.metadata.tags.length > 0 && (
                            <div className="flex space-x-1">
                              {result.metadata.tags.slice(0, 2).map(tag => (
                                <Badge key={tag} variant="outline" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : query && !isLoading && !aiResult ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                No results found for "{query}"
                {searchMode === 'regular' && (
                  <div className="mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSearchMode('ai')}
                      className="text-xs"
                    >
                      <Brain className="h-3 w-3 mr-1" />
                      Try AI Search
                    </Button>
                  </div>
                )}
              </div>
            ) : !query ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                <div className="space-y-2">
                  <p>Start typing to search...</p>
                  <div className="flex justify-center space-x-2 text-xs">
                    <kbd className="px-2 py-1 bg-muted rounded">⌘K</kbd>
                    <span>to focus</span>
                    <kbd className="px-2 py-1 bg-muted rounded">⌘I</kbd>
                    <span>to toggle AI</span>
                  </div>
                </div>
              </div>
            ) : null}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default GlobalSearch
