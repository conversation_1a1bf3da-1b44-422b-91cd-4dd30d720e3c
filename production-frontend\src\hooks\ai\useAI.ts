/**
 * AI Hook
 * General-purpose AI hook for various AI operations
 */

import { useState, useCallback, useRef } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { AIResponse, UseAIResult, AIProvider } from './types'

export interface UseAIOptions {
  provider?: AIProvider
  model?: string
  autoReset?: boolean
}

export function useAI(options: UseAIOptions = {}): UseAIResult {
  const { provider = 'deepseek', model = 'deepseek-r1-chat', autoReset = true } = options
  const { toast } = useToast()
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [response, setResponse] = useState<AIResponse | null>(null)
  
  const abortControllerRef = useRef<AbortController | null>(null)

  const generate = useCallback(async (prompt: string, generateOptions?: any): Promise<AIResponse> => {
    if (autoReset) {
      setError(null)
      setResponse(null)
    }
    
    setLoading(true)

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController()

    try {
      // Use the correct backend endpoint for AI content generation
      const aiResponse: AIResponse = await backendApiClient.request('/ai/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          operationType: 'CONTENT_GENERATION',
          contentRequest: {
            prompt,
            contentType: 'document',
            outputFormat: 'text',
            options: {
              useAdvancedAI: provider === 'deepseek',
              maxTokens: generateOptions?.maxTokens || 4000,
              temperature: generateOptions?.temperature || 0.7,
              topP: generateOptions?.topP || 0.9
            }
          },
          organizationId: generateOptions?.organizationId,
          projectId: generateOptions?.projectId
        })
      })
      setResponse(aiResponse)
      
      return aiResponse
    } catch (err: any) {
      if (err.name === 'AbortError') {
        // Request was cancelled
        return Promise.reject(new Error('Request cancelled'))
      }

      const errorMessage = err.message || 'AI generation failed'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'AI Generation Failed',
        description: errorMessage,
      })
      
      throw err
    } finally {
      setLoading(false)
      abortControllerRef.current = null
    }
  }, [provider, model, autoReset, toast])

  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      setLoading(false)
    }
  }, [])

  const reset = useCallback(() => {
    setError(null)
    setResponse(null)
    setLoading(false)
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
  }, [])

  return {
    loading,
    error,
    response,
    generate,
    cancel,
    reset,
  }
}

export default useAI
