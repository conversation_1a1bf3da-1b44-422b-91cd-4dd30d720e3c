/**
 * Lazy Feature Loader - Dynamic Feature Loading Component
 * Loads features only when they are explicitly needed by the user
 * Prevents unnecessary service initialization and improves performance
 */

"use client"

import React, { Suspense, lazy, useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useFeatureServices } from '@/hooks/useLazyServices'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'

// Lazy load feature components - using actual existing components
const AIChat = lazy(() => import('@/components/ai/ai-chat').then(module => ({ default: module.AIChat })))
const DocumentCollaboration = lazy(() => import('@/components/collaboration/document-collaboration').then(module => ({ default: module.DocumentCollaboration })))
const DocumentProcessingCenter = lazy(() => import('@/components/document-processing/DocumentProcessingCenter').then(module => ({ default: module.DocumentProcessingCenter })))
const ComprehensiveAnalyticsDashboard = lazy(() => import('@/components/analytics/comprehensive-analytics-dashboard').then(module => ({ default: module.ComprehensiveAnalyticsDashboard })))
const WorkflowBuilder = lazy(() => import('@/components/workflows/workflow-builder').then(module => ({ default: module.WorkflowBuilder })))
const IntelligentSearchBar = lazy(() => import('@/components/search/intelligent-search-bar').then(module => ({ default: module.IntelligentSearchBar })))

interface LazyFeatureLoaderProps {
  feature: 'ai' | 'collaboration' | 'documentProcessing' | 'analytics' | 'workflow' | 'search'
  enabled?: boolean
  fallback?: React.ReactNode
  children?: React.ReactNode
}

export function LazyFeatureLoader({ 
  feature, 
  enabled = false, 
  fallback,
  children 
}: LazyFeatureLoaderProps) {
  const { user } = useAuth()
  const [shouldLoad, setShouldLoad] = useState(false)
  const [hasError, setHasError] = useState(false)

  // Determine if feature should be loaded
  useEffect(() => {
    if (enabled && user) {
      setShouldLoad(true)
    } else {
      setShouldLoad(false)
    }
  }, [enabled, user])

  // Load services conditionally
  const services = useFeatureServices({
    ai: feature === 'ai' && shouldLoad,
    collaboration: feature === 'collaboration' && shouldLoad,
    documentProcessing: feature === 'documentProcessing' && shouldLoad,
    analytics: feature === 'analytics' && shouldLoad,
    workflow: feature === 'workflow' && shouldLoad,
    search: feature === 'search' && shouldLoad
  })

  // Loading state
  if (shouldLoad && services.isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="text-sm text-muted-foreground">
            Loading {feature} features...
          </p>
        </div>
      </div>
    )
  }

  // Error state
  if (hasError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load {feature} features. 
          <Button 
            variant="link" 
            className="p-0 h-auto ml-2"
            onClick={() => {
              setHasError(false)
              setShouldLoad(true)
            }}
          >
            Try again
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  // Not enabled or no user
  if (!shouldLoad || !user) {
    return fallback ? <>{fallback}</> : null
  }

  // Feature not available for user
  const currentService = services[feature as keyof typeof services]
  const hasAccess = currentService && typeof currentService === 'object' && 'hasAccess' in currentService
    ? (currentService as any).hasAccess
    : true

  if (!hasAccess) {
    return (
      <Alert>
        <AlertDescription>
          {feature} features are not available for your account.
        </AlertDescription>
      </Alert>
    )
  }

  // Render feature component with error boundary
  return (
    <Suspense fallback={<FeatureLoadingSkeleton feature={feature} />}>
      <FeatureErrorBoundary 
        feature={feature}
        onError={() => setHasError(true)}
      >
        {children || <DefaultFeatureComponent feature={feature} />}
      </FeatureErrorBoundary>
    </Suspense>
  )
}

// Default feature components
function DefaultFeatureComponent({ feature }: { feature: string }) {
  switch (feature) {
    case 'ai':
      return <AIChat />
    case 'collaboration':
      return <DocumentCollaboration documentId="demo" currentUserId="user" currentUserName="User" />
    case 'documentProcessing':
      return <DocumentProcessingCenter documentIds={[]} projectId="demo" organizationId="demo" />
    case 'analytics':
      return <ComprehensiveAnalyticsDashboard />
    case 'workflow':
      return <WorkflowBuilder />
    case 'search':
      return <IntelligentSearchBar />
    default:
      return <div>Feature not found: {feature}</div>
  }
}

// Loading skeleton for different features
function FeatureLoadingSkeleton({ feature }: { feature: string }) {
  const getSkeletonLayout = () => {
    switch (feature) {
      case 'ai':
        return (
          <div className="space-y-4 p-4">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-10 w-24" />
          </div>
        )
      case 'collaboration':
        return (
          <div className="space-y-4 p-4">
            <Skeleton className="h-6 w-32" />
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <Skeleton className="h-4 w-24" />
                </div>
              ))}
            </div>
          </div>
        )
      case 'documentProcessing':
        return (
          <div className="space-y-4 p-4">
            <Skeleton className="h-32 w-full border-2 border-dashed" />
            <Skeleton className="h-4 w-48" />
            <Skeleton className="h-10 w-32" />
          </div>
        )
      case 'analytics':
        return (
          <div className="grid grid-cols-2 gap-4 p-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-16 w-full" />
              </div>
            ))}
          </div>
        )
      case 'workflow':
        return (
          <div className="space-y-4 p-4">
            <Skeleton className="h-8 w-40" />
            <div className="grid grid-cols-3 gap-4">
              {[...Array(6)].map((_, i) => (
                <Skeleton key={i} className="h-24 w-full" />
              ))}
            </div>
          </div>
        )
      case 'search':
        return (
          <div className="space-y-4 p-4">
            <Skeleton className="h-10 w-full" />
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="space-y-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              ))}
            </div>
          </div>
        )
      default:
        return <Skeleton className="h-32 w-full" />
    }
  }

  return (
    <div className="animate-pulse">
      {getSkeletonLayout()}
    </div>
  )
}

// Error boundary for feature components
class FeatureErrorBoundary extends React.Component<
  { 
    children: React.ReactNode
    feature: string
    onError: () => void
  },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError() {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Error in ${this.props.feature} feature:`, error, errorInfo)
    this.props.onError()
  }

  render() {
    if (this.state.hasError) {
      return (
        <Alert variant="destructive">
          <AlertDescription>
            Something went wrong with the {this.props.feature} feature.
          </AlertDescription>
        </Alert>
      )
    }

    return this.props.children
  }
}

// Hook for conditional feature loading
export function useConditionalFeature(_feature: string, condition: boolean) {
  const [isEnabled, setIsEnabled] = useState(false)

  useEffect(() => {
    if (condition) {
      setIsEnabled(true)
    } else {
      // Delay disabling to allow for cleanup
      const timer = setTimeout(() => setIsEnabled(false), 1000)
      return () => clearTimeout(timer)
    }
  }, [condition])

  return isEnabled
}

// Higher-order component for lazy feature wrapping
export function withLazyFeature<P extends object>(
  feature: 'ai' | 'collaboration' | 'documentProcessing' | 'analytics' | 'workflow' | 'search',
  Component: React.ComponentType<P>
) {
  return function LazyFeatureComponent(props: P & { enabled?: boolean }) {
    const { enabled = false, ...componentProps } = props

    return (
      <LazyFeatureLoader feature={feature} enabled={enabled}>
        <Component {...(componentProps as P)} />
      </LazyFeatureLoader>
    )
  }
}

// Preload feature for better UX
export function preloadFeature(feature: string) {
  switch (feature) {
    case 'ai':
      import('@/components/ai/ai-chat')
      break
    case 'collaboration':
      import('@/components/collaboration/document-collaboration')
      break
    case 'documentProcessing':
      import('@/components/document-processing/DocumentProcessingCenter')
      break
    case 'analytics':
      import('@/components/analytics/comprehensive-analytics-dashboard')
      break
    case 'workflow':
      import('@/components/workflows/workflow-builder')
      break
    case 'search':
      import('@/components/search/intelligent-search-bar')
      break
  }
}
