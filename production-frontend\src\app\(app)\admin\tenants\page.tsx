"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Search,
  MoreHorizontal,
  Pencil,
  Trash,
  Building2,
  Users,
  Globe,
  Settings,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { EmptyState } from "@/components/empty-state";
import { AdminOnly } from "@/components/permission-guard";
import { TenantStatus } from "@/types/tenant";
import type { Tenant } from "@/types/tenant";
import { formatDistanceToNow } from "date-fns";
import { useTenants, useDeleteTenant } from "@/hooks/admin/useTenants";

export default function TenantsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<TenantStatus | "ALL">("ALL");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTenantId, setSelectedTenantId] = useState<string | null>(null);

  // Fetch tenants from the API
  const { tenants, loading: isLoading, refresh: refetch } = useTenants();

  // Filter tenants based on active tab and search query
  const filteredTenants = tenants.filter((tenant) => {
    const matchesSearch = !searchQuery ||
      tenant.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (tenant as any).displayName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (tenant as any).description?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = activeTab === "ALL" || (tenant as any).status === activeTab;

    return matchesSearch && matchesStatus;
  });

  // Handle tenant deletion
  const deleteTenant = useDeleteTenant();

  const handleDeleteTenant = async () => {
    if (selectedTenantId) {
      try {
        await deleteTenant(selectedTenantId);
        setIsDeleteDialogOpen(false);
        setSelectedTenantId(null);
        refetch();
      } catch (error) {
        console.error('Failed to delete tenant:', error);
      }
    }
  };

  // Get badge variant for tenant status
  const getStatusVariant = (status: TenantStatus) => {
    switch (status) {
      case TenantStatus.ACTIVE:
        return "success";
      case TenantStatus.INACTIVE:
        return "secondary";
      case TenantStatus.SUSPENDED:
        return "destructive";
      case TenantStatus.PENDING:
        return "outline";
      default:
        return "outline";
    }
  };

  // Get icon for tenant status
  const getStatusIcon = (status: TenantStatus) => {
    switch (status) {
      case TenantStatus.ACTIVE:
        return <CheckCircle className="h-4 w-4" />;
      case TenantStatus.INACTIVE:
        return <XCircle className="h-4 w-4" />;
      case TenantStatus.SUSPENDED:
        return <AlertCircle className="h-4 w-4" />;
      case TenantStatus.PENDING:
        return <Clock className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <AdminOnly>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Tenants</h1>
            <p className="text-muted-foreground">
              Manage tenants across the system
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/tenants/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Tenant
            </Link>
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tenants..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Tabs
            defaultValue="ALL"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as TenantStatus | "ALL")}
            className="w-full md:w-auto"
          >
            <TabsList className="grid grid-cols-5 w-full md:w-auto">
              <TabsTrigger value="ALL">All</TabsTrigger>
              <TabsTrigger value={TenantStatus.ACTIVE}>Active</TabsTrigger>
              <TabsTrigger value={TenantStatus.INACTIVE}>Inactive</TabsTrigger>
              <TabsTrigger value={TenantStatus.SUSPENDED}>Suspended</TabsTrigger>
              <TabsTrigger value={TenantStatus.PENDING}>Pending</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-48 w-full" />
            ))}
          </div>
        ) : filteredTenants.length === 0 ? (
          <EmptyState
            icon={<Building2 className="h-10 w-10 text-muted-foreground" />}
            title={searchQuery ? "No tenants found" : "No tenants"}
            description={
              searchQuery
                ? `No tenants match "${searchQuery}"`
                : "You haven't created any tenants yet."
            }
            action={
              <Button asChild>
                <Link href="/admin/tenants/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Tenant
                </Link>
              </Button>
            }
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTenants.map((tenant) => (
              <Card key={tenant.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{(tenant as any).displayName || tenant.name}</CardTitle>
                    <Badge variant={getStatusVariant((tenant as any).status || TenantStatus.PENDING)} className="flex items-center gap-1">
                      {getStatusIcon((tenant as any).status || TenantStatus.PENDING)}
                      {(tenant as any).status || 'PENDING'}
                    </Badge>
                  </div>
                  <CardDescription>{(tenant as any).description || 'No description'}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <span>{(tenant as any).domain || 'No domain'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>
                        {(tenant as any).settings?.maxUsers || 'Unlimited'} users •{" "}
                        {(tenant as any).settings?.maxProjects || 'Unlimited'} projects
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>
                        Created {formatDistanceToNow(new Date((tenant as any).createdAt || Date.now()))} ago
                      </span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button asChild variant="outline">
                    <Link href={`/admin/tenants/${tenant.id}`}>
                      View
                    </Link>
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/tenants/${tenant.id}/edit`}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/tenants/${tenant.id}/settings`}>
                          <Settings className="mr-2 h-4 w-4" />
                          Settings
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/tenants/${tenant.id}/users`}>
                          <Users className="mr-2 h-4 w-4" />
                          Users
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-destructive focus:text-destructive"
                        onClick={() => {
                          setSelectedTenantId(tenant.id);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Tenant</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this tenant? This action cannot be undone and will delete all associated data.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteTenant}
              >
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnly>
  );
}
