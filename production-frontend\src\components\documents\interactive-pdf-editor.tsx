"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  Wand2,
  Save,
  Eye,
  EyeOff,
  AlertTriangle,
  Lightbulb,
  Sparkles,
  Layout,
  Type,
  Image as ImageIcon,
  Table
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { PDFFormEngine, PDFFormField, PDFFieldType, DocumentLayout } from '@/lib/document-processing/pdf-form-engine';
import { aiDocumentGenerator, SmartContentSuggestion } from '@/lib/document-processing/ai-document-generator';

interface InteractivePDFEditorProps {
  documentId: string;
  documentName: string;
  documentUrl: string;
  organizationId: string;
  projectId: string;
  onSave?: (documentId: string) => void;
  onClose?: () => void;
}

export function InteractivePDFEditor({
  documentId,
  documentName,
  documentUrl,
  organizationId,
  projectId,
  onSave,
  onClose
}: InteractivePDFEditorProps) {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('form-fields');

  // PDF Form Engine
  const [pdfEngine] = useState(() => new PDFFormEngine());
  const [aiGenerator] = useState(() => aiDocumentGenerator);

  // Form fields state
  const [formFields, setFormFields] = useState<PDFFormField[]>([]);
  const [isDetectingFields, setIsDetectingFields] = useState(false);
  const [isFillingFields, setIsFillingFields] = useState(false);
  const [completionPercentage, setCompletionPercentage] = useState(0);

  // Layout editing state
  const [documentLayout, setDocumentLayout] = useState<DocumentLayout | null>(null);
  const [isLoadingLayout, setIsLoadingLayout] = useState(false);


  // AI suggestions state
  const [aiSuggestions, setAiSuggestions] = useState<SmartContentSuggestion[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [showAISuggestions, setShowAISuggestions] = useState(true);

  // Validation state
  const [validationErrors, setValidationErrors] = useState<Array<{
    fieldId: string;
    message: string;
    severity: 'error' | 'warning';
  }>>([]);

  // Initialize PDF editor
  useEffect(() => {
    initializePDFEditor();
  }, [documentId]);

  const initializePDFEditor = async () => {
    try {
      // Detect form fields
      await detectFormFields();

      // Extract document layout
      await extractDocumentLayout();

      // Get AI suggestions
      await getAISuggestions();
    } catch (error) {
      console.error('Failed to initialize PDF editor:', error);
      toast({
        title: "Initialization Failed",
        description: "Failed to initialize PDF editor. Some features may not work.",
        variant: "destructive"
      });
    }
  };

  const detectFormFields = async () => {
    setIsDetectingFields(true);
    try {
      const fields = await pdfEngine.detectFormFields(documentId);
      setFormFields(fields);

      // Calculate completion percentage
      const filledFields = fields.filter(field => field.value !== undefined && field.value !== '').length;
      const percentage = fields.length > 0 ? Math.round((filledFields / fields.length) * 100) : 0;
      setCompletionPercentage(percentage);

      toast({
        title: "Form Fields Detected",
        description: `Found ${fields.length} form fields in the document.`
      });
    } catch (error) {
      console.error('Failed to detect form fields:', error);
      toast({
        title: "Detection Failed",
        description: "Failed to detect form fields in the document.",
        variant: "destructive"
      });
    } finally {
      setIsDetectingFields(false);
    }
  };

  const extractDocumentLayout = async () => {
    setIsLoadingLayout(true);
    try {
      const layout = await pdfEngine.extractDocumentLayout(documentId);
      setDocumentLayout(layout);
    } catch (error) {
      console.error('Failed to extract document layout:', error);
    } finally {
      setIsLoadingLayout(false);
    }
  };

  const getAISuggestions = async () => {
    setIsLoadingSuggestions(true);
    try {
      const suggestions = await aiGenerator.getContentSuggestions(documentId, {
        documentPurpose: 'form_completion',
        userIntent: 'complete_document'
      });
      setAiSuggestions(suggestions);
    } catch (error) {
      console.error('Failed to get AI suggestions:', error);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleFieldChange = (fieldId: string, value: string | boolean | number) => {
    setFormFields(prev => prev.map(field =>
      field.id === fieldId ? { ...field, value } : field
    ));

    // Update completion percentage
    const updatedFields = formFields.map(field =>
      field.id === fieldId ? { ...field, value } : field
    );
    const filledFields = updatedFields.filter(field => field.value !== undefined && field.value !== '').length;
    const percentage = updatedFields.length > 0 ? Math.round((filledFields / updatedFields.length) * 100) : 0;
    setCompletionPercentage(percentage);
  };

  const handleAISuggestionApply = async (suggestion: SmartContentSuggestion) => {
    try {
      if (suggestion.type === 'text') {
        // Apply text completion suggestion
        const fieldId = suggestion.position?.section;
        if (fieldId) {
          handleFieldChange(fieldId, suggestion.suggestion);
        }
      }

      // Remove applied suggestion
      setAiSuggestions(prev => prev.filter(s => s.id !== suggestion.id));

      toast({
        title: "Suggestion Applied",
        description: "AI suggestion has been applied to the document."
      });
    } catch (error) {
      console.error('Failed to apply AI suggestion:', error);
      toast({
        title: "Application Failed",
        description: "Failed to apply AI suggestion.",
        variant: "destructive"
      });
    }
  };

  const handleFillAllFields = async () => {
    setIsFillingFields(true);
    try {
      const result = await pdfEngine.fillFormFields(documentId, formFields, {
        useAISuggestions: true,
        validateFields: true,
        preserveLayout: true,
        generateMissingFields: true
      });

      setFormFields(result.fields);
      setCompletionPercentage(result.completionPercentage);
      setValidationErrors(result.validationErrors.map((error: any) => ({
        ...error,
        severity: error.severity || 'error'
      })));

      toast({
        title: "Fields Filled",
        description: `Form completion: ${result.completionPercentage}%`
      });
    } catch (error) {
      console.error('Failed to fill fields:', error);
      toast({
        title: "Fill Failed",
        description: "Failed to fill form fields automatically.",
        variant: "destructive"
      });
    } finally {
      setIsFillingFields(false);
    }
  };

  const handleSaveDocument = async () => {
    try {
      // Validate form completion
      const validation = await pdfEngine.validateFormCompletion(documentId, formFields);

      if (!validation.isValid) {
        setValidationErrors(validation.errors);
        toast({
          title: "Validation Failed",
          description: `Please fix ${validation.errors.length} validation errors before saving.`,
          variant: "destructive"
        });
        return;
      }

      // Save the document
      await pdfEngine.fillFormFields(documentId, formFields, {
        validateFields: true,
        preserveLayout: true
      });

      toast({
        title: "Document Saved",
        description: "Document has been saved successfully."
      });

      onSave?.(documentId);
    } catch (error) {
      console.error('Failed to save document:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save the document.",
        variant: "destructive"
      });
    }
  };

  const renderFormField = (field: PDFFormField) => {
    const hasError = validationErrors.some(error => error.fieldId === field.id);
    const fieldError = validationErrors.find(error => error.fieldId === field.id);

    return (
      <div key={field.id} className="space-y-2">
        <div className="flex items-center justify-between">
          <Label htmlFor={field.id} className={hasError ? 'text-red-600' : ''}>
            {field.name}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.aiSuggestion && (
            <Badge variant="outline" className="text-xs">
              <Sparkles className="h-3 w-3 mr-1" />
              AI: {Math.round(field.aiSuggestion.confidence * 100)}%
            </Badge>
          )}
        </div>

        {field.type === PDFFieldType.TEXT && (
          <Input
            id={field.id}
            value={field.value as string || ''}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder || field.aiSuggestion?.suggestedValue}
            className={hasError ? 'border-red-500' : ''}
            disabled={field.readOnly}
          />
        )}

        {field.type === PDFFieldType.CHECKBOX && (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={field.id}
              checked={field.value as boolean || false}
              onChange={(e) => handleFieldChange(field.id, e.target.checked)}
              disabled={field.readOnly}
            />
            <Label htmlFor={field.id}>{field.placeholder}</Label>
          </div>
        )}

        {field.aiSuggestion && (
          <div className="text-xs text-muted-foreground">
            <Lightbulb className="h-3 w-3 inline mr-1" />
            Suggested: {field.aiSuggestion.suggestedValue}
          </div>
        )}

        {fieldError && (
          <Alert variant="destructive" className="py-2">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {fieldError.message}
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FileText className="h-5 w-5" />
            <div>
              <h2 className="font-semibold">{documentName}</h2>
              <p className="text-sm text-muted-foreground">Interactive PDF Editor</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline">
              {completionPercentage}% Complete
            </Badge>
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
            <Button size="sm" onClick={handleSaveDocument}>
              <Save className="h-4 w-4 mr-2" />
              Save
            </Button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <Progress value={completionPercentage} className="h-2" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex">
        {/* PDF Viewer */}
        <div className="flex-1 p-4">
          <Card className="h-full">
            <CardContent className="p-4 h-full">
              <iframe
                src={documentUrl}
                className="w-full h-full border rounded"
                title={documentName}
              />
            </CardContent>
          </Card>
        </div>

        {/* Editor Panel */}
        <div className="w-96 border-l">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="form-fields">
                <Type className="h-4 w-4 mr-2" />
                Fields
              </TabsTrigger>
              <TabsTrigger value="layout">
                <Layout className="h-4 w-4 mr-2" />
                Layout
              </TabsTrigger>
              <TabsTrigger value="ai-assist">
                <Wand2 className="h-4 w-4 mr-2" />
                AI Assist
              </TabsTrigger>
            </TabsList>

            <TabsContent value="form-fields" className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Form Fields</h3>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={detectFormFields}
                      disabled={isDetectingFields}
                    >
                      {isDetectingFields ? 'Detecting...' : 'Re-detect'}
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleFillAllFields}
                      disabled={isFillingFields}
                    >
                      <Wand2 className="h-4 w-4 mr-2" />
                      {isFillingFields ? 'Filling...' : 'AI Fill'}
                    </Button>
                  </div>
                </div>

                <Separator />

                {formFields.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No form fields detected</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={detectFormFields}
                    >
                      Detect Fields
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formFields.map(renderFormField)}
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="layout" className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Layout Elements</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={extractDocumentLayout}
                    disabled={isLoadingLayout}
                  >
                    {isLoadingLayout ? 'Loading...' : 'Refresh'}
                  </Button>
                </div>

                <Separator />

                {documentLayout ? (
                  <div className="space-y-3">
                    {documentLayout.pages.map((page, pageIndex) => (
                      <Card key={pageIndex}>
                        <CardHeader className="pb-2">
                          <CardTitle className="text-sm">Page {page.pageNumber}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Type className="h-3 w-3" />
                            {page.textBlocks.length} text blocks
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <ImageIcon className="h-3 w-3" />
                            {page.images.length} images
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <Table className="h-3 w-3" />
                            {page.tables.length} tables
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Layout className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Layout not loaded</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="ai-assist" className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">AI Suggestions</h3>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowAISuggestions(!showAISuggestions)}
                    >
                      {showAISuggestions ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={getAISuggestions}
                      disabled={isLoadingSuggestions}
                    >
                      {isLoadingSuggestions ? 'Loading...' : 'Refresh'}
                    </Button>
                  </div>
                </div>

                <Separator />

                {showAISuggestions && aiSuggestions.length > 0 ? (
                  <div className="space-y-3">
                    {aiSuggestions.map((suggestion) => (
                      <Card key={suggestion.id}>
                        <CardContent className="p-3">
                          <div className="flex items-start justify-between mb-2">
                            <Badge variant="outline" className="text-xs">
                              {suggestion.type.replace('_', ' ')}
                            </Badge>
                            <Badge variant={
                              suggestion.impact === 'high' ? 'default' :
                              suggestion.impact === 'medium' ? 'secondary' : 'outline'
                            } className="text-xs">
                              {suggestion.impact} impact
                            </Badge>
                          </div>
                          <p className="text-sm mb-2">{suggestion.suggestion}</p>
                          <p className="text-xs text-muted-foreground mb-3">{suggestion.reasoning}</p>
                          <Button
                            size="sm"
                            className="w-full"
                            onClick={() => handleAISuggestionApply(suggestion)}
                          >
                            Apply Suggestion
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Sparkles className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No AI suggestions available</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
