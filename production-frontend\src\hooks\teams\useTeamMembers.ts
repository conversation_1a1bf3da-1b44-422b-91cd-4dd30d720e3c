/**
 * Team Members Hook
 * Manages team member operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'
import type { TeamMember } from './useTeams'

export interface UseTeamMembersOptions {
  teamId: ID
  autoLoad?: boolean
}

export interface UseTeamMembersResult {
  members: TeamMember[]
  data: TeamMember[]
  loading: boolean
  isLoading: boolean
  error: string | null

  loadMembers: () => Promise<void>
  addMember: (userId: ID, role: string) => Promise<void>
  updateMember: (userId: ID, updates: Partial<TeamMember>) => Promise<void>
  removeMember: (userId: ID) => Promise<void>

  refresh: () => Promise<void>
}

export function useTeamMembers(options: UseTeamMembersOptions): UseTeamMembersResult {
  const { teamId, autoLoad = true } = options
  const { toast } = useToast()
  
  const [members, setMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadMembers = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request(`/teams/${teamId}/members/list`)
      setMembers(response || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load team members'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [teamId, toast])

  const addMember = useCallback(async (userId: ID, role: string) => {
    try {
      await backendApiClient.request(`/teams/${teamId}/members`, {
        method: 'POST',
        body: JSON.stringify({ userId, role })
      })
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Member added',
        description: 'Member has been added to the team.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to add member'
      
      toast({
        type: 'error',
        title: 'Add failed',
        description: errorMessage,
      })
    }
  }, [teamId, loadMembers, toast])

  const updateMember = useCallback(async (userId: ID, updates: Partial<TeamMember>) => {
    try {
      await backendApiClient.request(`/teams/${teamId}/members/${userId}/update`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Member updated',
        description: 'Member has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update member'
      
      toast({
        type: 'error',
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [teamId, loadMembers, toast])

  const removeMember = useCallback(async (userId: ID) => {
    try {
      await backendApiClient.request(`/teams/${teamId}/members/${userId}/remove`, {
        method: 'DELETE'
      })
      await loadMembers()
      
      toast({
        type: 'success',
        title: 'Member removed',
        description: 'Member has been removed from the team.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to remove member'
      
      toast({
        type: 'error',
        title: 'Remove failed',
        description: errorMessage,
      })
    }
  }, [teamId, loadMembers, toast])

  useEffect(() => {
    if (autoLoad) {
      loadMembers()
    }
  }, [autoLoad, loadMembers])

  return {
    members,
    data: members,
    loading,
    isLoading: loading,
    error,
    loadMembers,
    addMember,
    updateMember,
    removeMember,
    refresh: loadMembers,
  }
}
