'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Clock, 
  User, 
  MessageSquare, 
  CheckCircle, 
  AlertCircle,
  Play,
  Pause,
  Search,
  Filter
} from 'lucide-react';

interface WorkflowHistoryEvent {
  id: string;
  type: 'step_started' | 'step_completed' | 'step_failed' | 'step_skipped' | 'comment_added' | 'assignment_changed';
  stepId?: string;
  stepName?: string;
  userId: string;
  userName: string;
  timestamp: string;
  description: string;
  metadata?: Record<string, any>;
}

interface WorkflowHistoryProps {
  events: WorkflowHistoryEvent[];
  loading?: boolean;
}

export function WorkflowHistory({ events, loading = false }: WorkflowHistoryProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');

  const filteredEvents = useMemo(() => {
    let filtered = events;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(event =>
        event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.stepName?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(event => event.type === filterType);
    }

    return filtered.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [events, searchTerm, filterType]);

  const getEventIcon = (type: WorkflowHistoryEvent['type']) => {
    switch (type) {
      case 'step_started':
        return <Play className="h-4 w-4 text-blue-600" />;
      case 'step_completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'step_failed':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'step_skipped':
        return <Pause className="h-4 w-4 text-gray-400" />;
      case 'comment_added':
        return <MessageSquare className="h-4 w-4 text-purple-600" />;
      case 'assignment_changed':
        return <User className="h-4 w-4 text-orange-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getEventBadgeVariant = (type: WorkflowHistoryEvent['type']) => {
    switch (type) {
      case 'step_completed':
        return 'default' as const;
      case 'step_started':
        return 'secondary' as const;
      case 'step_failed':
        return 'destructive' as const;
      case 'comment_added':
        return 'outline' as const;
      default:
        return 'outline' as const;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
  };

  const getEventTypeLabel = (type: WorkflowHistoryEvent['type']) => {
    switch (type) {
      case 'step_started':
        return 'Step Started';
      case 'step_completed':
        return 'Step Completed';
      case 'step_failed':
        return 'Step Failed';
      case 'step_skipped':
        return 'Step Skipped';
      case 'comment_added':
        return 'Comment Added';
      case 'assignment_changed':
        return 'Assignment Changed';
      default:
        return 'Unknown';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Workflow History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Workflow History</span>
          <Badge variant="outline">{filteredEvents.length} events</Badge>
        </CardTitle>
        
        {/* Filters */}
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-48">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Events</SelectItem>
              <SelectItem value="step_started">Step Started</SelectItem>
              <SelectItem value="step_completed">Step Completed</SelectItem>
              <SelectItem value="step_failed">Step Failed</SelectItem>
              <SelectItem value="step_skipped">Step Skipped</SelectItem>
              <SelectItem value="comment_added">Comments</SelectItem>
              <SelectItem value="assignment_changed">Assignments</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        {filteredEvents.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">
              {searchTerm || filterType !== 'all' 
                ? 'No events match your filters' 
                : 'No workflow history available'
              }
            </p>
            {(searchTerm || filterType !== 'all') && (
              <Button
                variant="outline"
                size="sm"
                className="mt-2"
                onClick={() => {
                  setSearchTerm('');
                  setFilterType('all');
                }}
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredEvents.map((event, index) => (
              <div key={event.id} className="relative">
                {/* Timeline line */}
                {index < filteredEvents.length - 1 && (
                  <div className="absolute left-4 top-8 w-0.5 h-8 bg-border" />
                )}
                
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-background border-2 border-border flex items-center justify-center">
                    {getEventIcon(event.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-sm">{event.userName}</p>
                        <Badge variant={getEventBadgeVariant(event.type)} className="text-xs">
                          {getEventTypeLabel(event.type)}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {formatTimestamp(event.timestamp)}
                      </p>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-1">
                      {event.description}
                    </p>
                    
                    {event.stepName && (
                      <p className="text-xs text-muted-foreground">
                        Step: {event.stepName}
                      </p>
                    )}
                    
                    {event.metadata && Object.keys(event.metadata).length > 0 && (
                      <div className="mt-2 p-2 bg-muted rounded text-xs">
                        {Object.entries(event.metadata).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="font-medium">{key}:</span>
                            <span>{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
