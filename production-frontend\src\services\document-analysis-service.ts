/**
 * Document Analysis Service
 * Service for document analysis and insights
 */

import { backendApiClient } from './backend-api-client'

export interface DocumentAnalysis {
  id: string
  documentId: string
  analysisType: DocumentAnalysisType
  status: 'pending' | 'processing' | 'completed' | 'failed'
  results: DocumentAnalysisResults
  confidence: number
  processingTime: number
  createdAt: string
  completedAt?: string
  organizationId: string
}

export enum DocumentAnalysisType {
  CONTENT_ANALYSIS = 'content_analysis',
  STRUCTURE_ANALYSIS = 'structure_analysis',
  SENTIMENT_ANALYSIS = 'sentiment_analysis',
  ENTITY_EXTRACTION = 'entity_extraction',
  TOPIC_MODELING = 'topic_modeling',
  READABILITY_ANALYSIS = 'readability_analysis',
  COMPLIANCE_CHECK = 'compliance_check',
  QUALITY_ASSESSMENT = 'quality_assessment',
  COMPREHENSIVE = 'comprehensive'
}

export interface DocumentAnalysisResults {
  contentAnalysis?: {
    wordCount: number
    characterCount: number
    paragraphCount: number
    sentenceCount: number
    averageWordsPerSentence: number
    averageSentencesPerParagraph: number
    keyPhrases: Array<{
      phrase: string
      frequency: number
      relevance: number
    }>
    topics: Array<{
      topic: string
      confidence: number
      keywords: string[]
    }>
  }
  structureAnalysis?: {
    hasTitle: boolean
    hasHeaders: boolean
    headerHierarchy: Array<{
      level: number
      text: string
      position: number
    }>
    hasTables: boolean
    tableCount: number
    hasImages: boolean
    imageCount: number
    hasLists: boolean
    listCount: number
    structureScore: number
  }
  sentimentAnalysis?: {
    overallSentiment: 'positive' | 'negative' | 'neutral'
    sentimentScore: number
    confidence: number
    sentimentDistribution: {
      positive: number
      negative: number
      neutral: number
    }
    emotionalTone: string[]
  }
  entityExtraction?: {
    entities: Array<{
      text: string
      type: string
      confidence: number
      position: {
        start: number
        end: number
      }
      metadata?: Record<string, any>
    }>
    entityTypes: Record<string, number>
    totalEntities: number
  }
  readabilityAnalysis?: {
    fleschKincaidGrade: number
    fleschReadingEase: number
    gunningFogIndex: number
    smogIndex: number
    automatedReadabilityIndex: number
    colemanLiauIndex: number
    readabilityScore: number
    readingLevel: string
    estimatedReadingTime: number
  }
  complianceCheck?: {
    regulations: Array<{
      name: string
      compliant: boolean
      confidence: number
      violations: Array<{
        rule: string
        description: string
        severity: 'low' | 'medium' | 'high'
        location?: string
      }>
    }>
    overallCompliance: number
    criticalViolations: number
  }
  qualityAssessment?: {
    overallQuality: number
    dimensions: {
      clarity: number
      coherence: number
      completeness: number
      accuracy: number
      relevance: number
    }
    issues: Array<{
      type: string
      description: string
      severity: 'low' | 'medium' | 'high'
      suggestions: string[]
    }>
    strengths: string[]
    improvements: string[]
  }
}

export interface AnalysisRequest {
  documentId: string
  analysisTypes: DocumentAnalysisType[]
  options?: {
    includeConfidence?: boolean
    includeMetadata?: boolean
    customParameters?: Record<string, any>
  }
  organizationId: string
}

export interface BatchAnalysisRequest {
  documentIds: string[]
  analysisTypes: DocumentAnalysisType[]
  options?: {
    includeConfidence?: boolean
    includeMetadata?: boolean
    customParameters?: Record<string, any>
  }
  organizationId: string
}

export interface Visualization {
  id: string
  type: 'wordcloud' | 'heatmap' | 'chart' | 'graph' | 'timeline' | 'bar' | 'pie' | 'line' | 'scatter' | 'treemap' | 'network' | 'table'
  title: string
  description?: string
  data: any
  config?: {
    width?: number
    height?: number
    colors?: string[]
    interactive?: boolean
    [key: string]: any
  }
  metadata?: {
    generatedAt: string
    dataSource: string
    analysisId?: string
    [key: string]: any
  }
}

class DocumentAnalysisService {
  /**
   * Analyze a single document
   */
  async analyzeDocument(request: AnalysisRequest): Promise<DocumentAnalysis> {
    return await backendApiClient.request<DocumentAnalysis>('/documents/analyze', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Batch analyze multiple documents
   */
  async batchAnalyzeDocuments(request: BatchAnalysisRequest): Promise<DocumentAnalysis[]> {
    return await backendApiClient.request<DocumentAnalysis[]>('/documents/batch-analyze', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Get analysis result
   */
  async getAnalysis(analysisId: string): Promise<DocumentAnalysis> {
    return await backendApiClient.request<DocumentAnalysis>(`/documents/analysis/${analysisId}`)
  }

  /**
   * Get all analyses for a document
   */
  async getDocumentAnalyses(documentId: string): Promise<DocumentAnalysis[]> {
    return await backendApiClient.request<DocumentAnalysis[]>(`/documents/${documentId}/analyses`)
  }

  /**
   * Get analysis history
   */
  async getAnalysisHistory(params?: {
    organizationId?: string
    documentId?: string
    analysisType?: DocumentAnalysisType
    status?: string
    page?: number
    pageSize?: number
    dateRange?: { start: string; end: string }
  }): Promise<DocumentAnalysis[]> {
    return await backendApiClient.request<DocumentAnalysis[]>('/documents/analysis/history', {
      params
    })
  }

  /**
   * Compare document analyses
   */
  async compareAnalyses(analysisIds: string[]): Promise<{
    analyses: DocumentAnalysis[]
    comparison: {
      similarities: Array<{
        metric: string
        similarity: number
        description: string
      }>
      differences: Array<{
        metric: string
        difference: number
        description: string
      }>
    }
    insights: string[]
  }> {
    return await backendApiClient.request('/documents/analysis/compare', {
      method: 'POST',
      body: JSON.stringify({ analysisIds })
    })
  }

  /**
   * Get analysis insights
   */
  async getAnalysisInsights(documentId: string): Promise<{
    documentId: string
    insights: Array<{
      category: string
      title: string
      description: string
      confidence: number
      actionable: boolean
      recommendations?: string[]
    }>
    summary: {
      overallScore: number
      strengths: string[]
      weaknesses: string[]
      opportunities: string[]
    }
    trends: Array<{
      metric: string
      trend: 'improving' | 'declining' | 'stable'
      change: number
      timeframe: string
    }>
  }> {
    return await backendApiClient.request(`/documents/${documentId}/analysis/insights`)
  }

  /**
   * Generate analysis report
   */
  async generateAnalysisReport(data: {
    documentIds: string[]
    analysisTypes: DocumentAnalysisType[]
    reportFormat: 'pdf' | 'html' | 'json'
    includeCharts?: boolean
    includeRecommendations?: boolean
    organizationId: string
  }): Promise<{
    reportId: string
    downloadUrl: string
    expiresAt: string
  }> {
    return await backendApiClient.request('/documents/analysis/report', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * Get analysis statistics
   */
  async getAnalysisStatistics(organizationId: string, params?: {
    dateRange?: { start: string; end: string }
    analysisType?: DocumentAnalysisType
  }): Promise<{
    totalAnalyses: number
    analysesByType: Record<DocumentAnalysisType, number>
    analysesByStatus: Record<string, number>
    averageProcessingTime: number
    averageConfidence: number
    topDocuments: Array<{
      documentId: string
      documentName: string
      analysisCount: number
      averageScore: number
    }>
    trends: Array<{
      date: string
      count: number
      averageScore: number
    }>
  }> {
    return await backendApiClient.request('/documents/analysis/statistics', {
      params: { organizationId, ...params }
    })
  }

  /**
   * Cancel analysis
   */
  async cancelAnalysis(analysisId: string): Promise<{
    analysisId: string
    status: 'cancelled'
    cancelledAt: string
  }> {
    return await backendApiClient.request(`/documents/analysis/${analysisId}/cancel`, {
      method: 'POST'
    })
  }

  /**
   * Retry failed analysis
   */
  async retryAnalysis(analysisId: string): Promise<DocumentAnalysis> {
    return await backendApiClient.request<DocumentAnalysis>(`/documents/analysis/${analysisId}/retry`, {
      method: 'POST'
    })
  }

  /**
   * Get available analysis types
   */
  async getAvailableAnalysisTypes(): Promise<Array<{
    type: DocumentAnalysisType
    name: string
    description: string
    estimatedTime: number
    capabilities: string[]
  }>> {
    return await backendApiClient.request('/documents/analysis/types')
  }

  /**
   * Validate analysis request
   */
  async validateAnalysisRequest(request: AnalysisRequest): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
    estimatedTime: number
    estimatedCost: number
  }> {
    return await backendApiClient.request('/documents/analysis/validate', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }
}

export const documentAnalysisService = new DocumentAnalysisService()
