'use client'

/**
 * Store Provider - Simplified Zustand Store Provider
 * Basic store provider without complex initialization
 */

import React, { useEffect, useState, ReactNode } from 'react'

interface StoreProviderProps {
  children: ReactNode
}

export function StoreProvider({ children }: StoreProviderProps) {
  const [isHydrated, setIsHydrated] = useState(false)

  // Simple hydration effect
  useEffect(() => {
    // Set hydrated after a short delay to allow for store initialization
    const timer = setTimeout(() => {
      setIsHydrated(true)
    }, 50)

    return () => clearTimeout(timer)
  }, [])

  // Show loading state while hydrating
  if (!isHydrated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading stores...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}


