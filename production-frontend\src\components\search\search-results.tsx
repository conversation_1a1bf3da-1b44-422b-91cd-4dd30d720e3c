'use client';

import React from 'react';
import { FileText, User, Calendar, Tag, ExternalLink } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { formatDistanceToNow } from 'date-fns';

export interface SearchResult {
  id: string;
  title: string;
  description?: string;
  type: 'document' | 'project' | 'template' | 'workflow' | 'user';
  url?: string;
  metadata?: {
    author?: string;
    createdAt?: string;
    updatedAt?: string;
    tags?: string[];
    status?: string;
    [key: string]: any;
  };
  highlight?: {
    title?: string;
    description?: string;
    content?: string[];
  };
  score?: number;
}

export interface SearchResultsProps {
  results: SearchResult[];
  isLoading?: boolean;
  query?: string;
  totalResults?: number;
  onResultClick?: (result: SearchResult) => void;
  className?: string;
}

export function SearchResults({
  results,
  isLoading = false,
  query,
  totalResults,
  onResultClick,
  className
}: SearchResultsProps) {
  const getTypeIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'user':
        return <User className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getTypeBadgeVariant = (type: SearchResult['type']) => {
    switch (type) {
      case 'document':
        return 'default' as const;
      case 'project':
        return 'secondary' as const;
      case 'template':
        return 'outline' as const;
      case 'workflow':
        return 'secondary' as const;
      case 'user':
        return 'outline' as const;
      default:
        return 'default' as const;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return null;
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return null;
    }
  };

  const highlightText = (text: string, highlight?: string) => {
    if (!highlight || !query) return text;
    
    const parts = text.split(new RegExp(`(${query})`, 'gi'));
    return parts.map((part, index) => 
      part.toLowerCase() === query.toLowerCase() ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  if (isLoading) {
    return (
      <div className={className}>
        <div className="space-y-4">
          {[...Array(5)].map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (results.length === 0) {
    return (
      <div className={className}>
        <Card>
          <CardContent className="py-8 text-center">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No results found</h3>
            <p className="text-muted-foreground">
              {query ? `No results found for "${query}"` : 'Try adjusting your search terms or filters'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      {totalResults !== undefined && (
        <div className="mb-4 text-sm text-muted-foreground">
          {totalResults.toLocaleString()} result{totalResults !== 1 ? 's' : ''}
          {query && ` for "${query}"`}
        </div>
      )}
      
      <div className="space-y-4">
        {results.map((result) => (
          <Card 
            key={result.id} 
            className={`transition-all duration-200 ${
              onResultClick ? 'cursor-pointer hover:shadow-md hover:border-primary/50' : ''
            }`}
            onClick={() => onResultClick?.(result)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1 min-w-0">
                  <div className="flex-shrink-0 mt-0.5">
                    {getTypeIcon(result.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg leading-tight">
                      {result.highlight?.title ? (
                        <span dangerouslySetInnerHTML={{ __html: result.highlight.title }} />
                      ) : (
                        highlightText(result.title, result.highlight?.title)
                      )}
                    </CardTitle>
                    {result.url && (
                      <div className="flex items-center mt-1 text-sm text-muted-foreground">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        <span className="truncate">{result.url}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2 flex-shrink-0">
                  <Badge variant={getTypeBadgeVariant(result.type)}>
                    {result.type}
                  </Badge>
                  {result.score && (
                    <Badge variant="outline" className="text-xs">
                      {Math.round(result.score * 100)}%
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              {result.description && (
                <p className="text-muted-foreground mb-3 line-clamp-2">
                  {result.highlight?.description ? (
                    <span dangerouslySetInnerHTML={{ __html: result.highlight.description }} />
                  ) : (
                    highlightText(result.description, result.highlight?.description)
                  )}
                </p>
              )}
              
              {result.highlight?.content && result.highlight.content.length > 0 && (
                <div className="mb-3">
                  <div className="text-sm font-medium mb-1">Matching content:</div>
                  <div className="space-y-1">
                    {result.highlight.content.slice(0, 2).map((content, index) => (
                      <div key={index} className="text-sm text-muted-foreground bg-muted p-2 rounded">
                        <span dangerouslySetInnerHTML={{ __html: content }} />
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center space-x-4">
                  {result.metadata?.author && (
                    <div className="flex items-center space-x-1">
                      <User className="h-3 w-3" />
                      <span>{result.metadata.author}</span>
                    </div>
                  )}
                  
                  {result.metadata?.updatedAt && (
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(result.metadata.updatedAt)}</span>
                    </div>
                  )}
                  
                  {result.metadata?.status && (
                    <Badge variant="outline" className="text-xs">
                      {result.metadata.status}
                    </Badge>
                  )}
                </div>
                
                {result.metadata?.tags && result.metadata.tags.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <Tag className="h-3 w-3" />
                    <div className="flex space-x-1">
                      {result.metadata.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {result.metadata.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{result.metadata.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
