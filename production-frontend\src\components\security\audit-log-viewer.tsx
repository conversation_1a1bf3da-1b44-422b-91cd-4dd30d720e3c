"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  AuditLogFilter,
  AuditSeverity,
  SecurityAuditEventType,
  auditService
} from "@/services/audit-service";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from "@/components/ui/pagination";
import { format } from "date-fns";
import {
  <PERSON><PERSON><PERSON><PERSON>gle,
  AlertCircle,
  Info,
  Shield,
  Search
} from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";

interface AuditLogViewerProps {
  /**
   * Initial filter options
   */
  initialFilter?: Partial<AuditLogFilter>;

  /**
   * Whether to show the filter controls
   */
  showFilters?: boolean;

  /**
   * Whether to auto-refresh the logs
   */
  autoRefresh?: boolean;

  /**
   * Auto-refresh interval in milliseconds
   */
  refreshInterval?: number;

  /**
   * @deprecated Use pageSize in initialFilter instead
   */
  maxLogs?: number;
}

/**
 * Component for viewing security audit logs
 */
export function AuditLogViewer({
  initialFilter = {},
  showFilters = true,
  // Removed deprecated parameter
  autoRefresh = false,
  refreshInterval = 30000
}: AuditLogViewerProps) {
  const { toast } = useToast();
  const [filter, setFilter] = useState<AuditLogFilter>({
    page: 1,
    pageSize: 10, // Default page size
    ...initialFilter
  });

  // Query for audit logs
  const {
    data,
    isLoading,
    isError,
    refetch
  } = useQuery({
    queryKey: ['auditLogs', filter],
    queryFn: () => auditService.getSecurityAlerts(filter),
    refetchInterval: autoRefresh ? refreshInterval : false
  });

  // Handle filter changes
  const handleFilterChange = (newFilter: Partial<AuditLogFilter>) => {
    setFilter(prev => ({
      ...prev,
      ...newFilter,
      page: 1 // Reset to first page on filter change
    }));
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setFilter(prev => ({
      ...prev,
      page
    }));
  };

  // Show error toast if query fails
  useEffect(() => {
    if (isError) {
      toast({
        title: "Error loading audit logs",
        description: "There was a problem loading the security audit logs.",
        variant: "destructive"
      });
    }
  }, [isError, toast]);

  // Get severity icon
  const getSeverityIcon = (severity: AuditSeverity) => {
    switch (severity) {
      case AuditSeverity.CRITICAL:
        return <AlertCircle className="h-4 w-4 text-destructive" />;
      case AuditSeverity.ERROR:
        return <AlertTriangle className="h-4 w-4 text-destructive" />;
      case AuditSeverity.WARNING:
        return <AlertTriangle className="h-4 w-4 text-warning" />;
      case AuditSeverity.INFO:
      default:
        return <Info className="h-4 w-4 text-muted-foreground" />;
    }
  };

  // Get severity badge
  const getSeverityBadge = (severity: AuditSeverity) => {
    switch (severity) {
      case AuditSeverity.CRITICAL:
        return (
          <Badge variant="destructive" className="gap-1">
            {getSeverityIcon(severity)} Critical
          </Badge>
        );
      case AuditSeverity.ERROR:
        return (
          <Badge variant="destructive" className="gap-1">
            {getSeverityIcon(severity)} Error
          </Badge>
        );
      case AuditSeverity.WARNING:
        return (
          <Badge variant="outline" className="gap-1 border-yellow-500 text-yellow-500">
            {getSeverityIcon(severity)} Warning
          </Badge>
        );
      case AuditSeverity.INFO:
      default:
        return (
          <Badge variant="outline" className="gap-1">
            {getSeverityIcon(severity)} Info
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" /> Security Audit Logs
        </CardTitle>
        <CardDescription>
          View security-related events and activities
        </CardDescription>
      </CardHeader>

      {showFilters && (
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <Select
                value={filter.eventType}
                onValueChange={(value) => handleFilterChange({ eventType: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Event Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Event Types</SelectItem>
                  {Object.values(SecurityAuditEventType).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type.replace(/_/g, ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select
                value={filter.severity}
                onValueChange={(value) => handleFilterChange({ severity: value as AuditSeverity })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Severities</SelectItem>
                  {Object.values(AuditSeverity).map((severity) => (
                    <SelectItem key={severity} value={severity}>
                      {severity.charAt(0).toUpperCase() + severity.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Input
                placeholder="User ID or Email"
                value={filter.userId || filter.userEmail || ''}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange({
                    userId: value.includes('@') ? undefined : value,
                    userEmail: value.includes('@') ? value : undefined
                  });
                }}
              />
              <Button variant="outline" size="icon" onClick={() => refetch()}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      )}

      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>
        ) : data?.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No audit logs found matching the current filters.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Time</TableHead>
                  <TableHead>Event</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data?.map((event: any) => (
                  <TableRow key={event.id}>
                    <TableCell className="whitespace-nowrap">
                      {event.timestamp ? format(new Date(event.timestamp), 'MMM d, yyyy HH:mm:ss') : 'N/A'}
                    </TableCell>
                    <TableCell className="whitespace-nowrap">
                      {event.eventType.replace(/_/g, ' ')}
                    </TableCell>
                    <TableCell>
                      {getSeverityBadge(event.severity)}
                    </TableCell>
                    <TableCell>
                      {event.userEmail || event.userId || 'System'}
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {event.details ? JSON.stringify(event.details) : 'No details'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>

      {data && data.length > (filter.pageSize || 10) && (
        <CardFooter>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(Math.max(1, (filter.page || 1) - 1));
                  }}
                  aria-disabled={(filter.page || 1) === 1}
                  className={(filter.page || 1) === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>

              {Array.from(
                { length: Math.min(5, Math.ceil(data.length / (filter.pageSize || 10))) },
                (_, i) => {
                  const page = i + 1;
                  return (
                    <PaginationItem key={page}>
                      <PaginationLink
                        href="#"
                        isActive={page === (filter.page || 1)}
                        onClick={(e) => {
                          e.preventDefault();
                          handlePageChange(page);
                        }}
                      >
                        {page}
                      </PaginationLink>
                    </PaginationItem>
                  );
                }
              )}

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange((filter.page || 1) + 1);
                  }}
                  aria-disabled={(filter.page || 1) >= Math.ceil(data.length / (filter.pageSize || 10))}
                  className={(filter.page || 1) >= Math.ceil(data.length / (filter.pageSize || 10)) ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </CardFooter>
      )}
    </Card>
  );
}
