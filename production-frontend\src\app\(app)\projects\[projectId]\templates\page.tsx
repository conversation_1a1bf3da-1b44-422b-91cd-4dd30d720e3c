'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useProject } from '@/hooks/projects/useProject';
import { useTemplates } from '@/hooks';
import { Plus, Search } from 'lucide-react';
import { TemplateCard } from '@/components/templates/template-card';
import { EmptyState } from '@/components/empty-state';

export default function ProjectTemplatesPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;
  const [searchQuery, setSearchQuery] = useState('');

  const { project, isLoading: projectLoading, error: projectError } = useProject({ projectId });
  const { data: templatesData = [], isLoading: templatesLoading, error: templatesError } = useTemplates({ projectId });

  useEffect(() => {
    if (projectError || templatesError) {
      toast({
        title: 'Error',
        description: 'Failed to load project templates',
        variant: 'destructive',
      });
    }
  }, [projectError, templatesError, toast]);

  const isLoading = projectLoading || templatesLoading;

  // Extract templates array from response
  const templates = Array.isArray(templatesData) ? templatesData : (templatesData as any)?.data || [];

  // Filter templates by search query
  const filteredTemplates = searchQuery
    ? templates.filter((template: any) =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : templates;

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">{project?.name} Templates</h1>
          <p className="text-muted-foreground">Manage document templates for this project</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/projects/${projectId}`)}
          >
            Back to Project
          </Button>
          <Button
            onClick={() => router.push(`/projects/${projectId}/templates/create`)}
          >
            <Plus className="mr-2 h-4 w-4" />
            Create Template
          </Button>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search templates..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-muted rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 bg-muted rounded w-full mb-2"></div>
                <div className="h-4 bg-muted rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredTemplates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTemplates.map((template: any) => (
            <TemplateCard
              key={template.id}
              template={template}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          title="No templates found"
          description={searchQuery ? "Try a different search term" : "Create your first template to get started"}
          action={
            <Button onClick={() => router.push(`/projects/${projectId}/templates/create`)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Template
            </Button>
          }
        />
      )}
    </div>
  );
}
