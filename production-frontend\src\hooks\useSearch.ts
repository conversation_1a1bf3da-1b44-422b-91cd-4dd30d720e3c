import { useState, useMemo, useCallback } from 'react'
import { useDebounce } from './useDebounce'

/**
 * Search Hook
 * Provides search functionality with debouncing and filtering
 */

export interface UseSearchOptions<T> {
  searchFields?: (keyof T)[]
  debounceMs?: number
  caseSensitive?: boolean
  minSearchLength?: number
}

export interface UseSearchResult<T> {
  searchTerm: string
  debouncedSearchTerm: string
  filteredItems: T[]
  setSearchTerm: (term: string) => void
  clearSearch: () => void
  isSearching: boolean
}

export function useSearch<T extends Record<string, any>>(
  items: T[],
  options: UseSearchOptions<T> = {}
): UseSearchResult<T> {
  const {
    searchFields,
    debounceMs = 300,
    caseSensitive = false,
    minSearchLength = 1,
  } = options

  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, debounceMs)

  const filteredItems = useMemo(() => {
    if (!debouncedSearchTerm || debouncedSearchTerm.length < minSearchLength) {
      return items
    }

    const searchValue = caseSensitive ? debouncedSearchTerm : debouncedSearchTerm.toLowerCase()

    return items.filter(item => {
      if (searchFields && searchFields.length > 0) {
        // Search in specified fields
        return searchFields.some(field => {
          const fieldValue = item[field]
          if (fieldValue == null) return false
          
          const stringValue = String(fieldValue)
          const compareValue = caseSensitive ? stringValue : stringValue.toLowerCase()
          return compareValue.includes(searchValue)
        })
      } else {
        // Search in all string fields
        return Object.values(item).some(value => {
          if (value == null) return false
          
          const stringValue = String(value)
          const compareValue = caseSensitive ? stringValue : stringValue.toLowerCase()
          return compareValue.includes(searchValue)
        })
      }
    })
  }, [items, debouncedSearchTerm, searchFields, caseSensitive, minSearchLength])

  const clearSearch = useCallback(() => {
    setSearchTerm('')
  }, [])

  const isSearching = searchTerm !== debouncedSearchTerm

  return {
    searchTerm,
    debouncedSearchTerm,
    filteredItems,
    setSearchTerm,
    clearSearch,
    isSearching,
  }
}

/**
 * Advanced search hook with multiple criteria
 */
export interface SearchCriteria<T> {
  field: keyof T
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte'
  value: any
}

export interface UseAdvancedSearchOptions<T> {
  debounceMs?: number
  caseSensitive?: boolean
}

export function useAdvancedSearch<T extends Record<string, any>>(
  items: T[],
  options: UseAdvancedSearchOptions<T> = {}
) {
  const { debounceMs = 300, caseSensitive = false } = options
  
  const [criteria, setCriteria] = useState<SearchCriteria<T>[]>([])
  const debouncedCriteria = useDebounce(criteria, debounceMs)

  const filteredItems = useMemo(() => {
    if (debouncedCriteria.length === 0) {
      return items
    }

    return items.filter(item => {
      return debouncedCriteria.every(criterion => {
        const fieldValue = item[criterion.field]
        const searchValue = criterion.value

        if (fieldValue == null) return false

        switch (criterion.operator) {
          case 'contains':
            const fieldStr = String(fieldValue)
            const searchStr = String(searchValue)
            return caseSensitive 
              ? fieldStr.includes(searchStr)
              : fieldStr.toLowerCase().includes(searchStr.toLowerCase())

          case 'equals':
            return fieldValue === searchValue

          case 'startsWith':
            return String(fieldValue).startsWith(String(searchValue))

          case 'endsWith':
            return String(fieldValue).endsWith(String(searchValue))

          case 'gt':
            return Number(fieldValue) > Number(searchValue)

          case 'lt':
            return Number(fieldValue) < Number(searchValue)

          case 'gte':
            return Number(fieldValue) >= Number(searchValue)

          case 'lte':
            return Number(fieldValue) <= Number(searchValue)

          default:
            return true
        }
      })
    })
  }, [items, debouncedCriteria, caseSensitive])

  const addCriterion = useCallback((criterion: SearchCriteria<T>) => {
    setCriteria(prev => [...prev, criterion])
  }, [])

  const removeCriterion = useCallback((index: number) => {
    setCriteria(prev => prev.filter((_, i) => i !== index))
  }, [])

  const updateCriterion = useCallback((index: number, criterion: SearchCriteria<T>) => {
    setCriteria(prev => prev.map((c, i) => i === index ? criterion : c))
  }, [])

  const clearCriteria = useCallback(() => {
    setCriteria([])
  }, [])

  return {
    criteria,
    filteredItems,
    addCriterion,
    removeCriterion,
    updateCriterion,
    clearCriteria,
  }
}
