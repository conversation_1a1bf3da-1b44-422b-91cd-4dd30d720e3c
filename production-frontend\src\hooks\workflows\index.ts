/**
 * Workflows Hooks Index
 * Re-exports all workflow-related hooks
 */

export {
  useWorkflows,
  useWorkflow,
  useWorkflowExecution,
  useCreateWorkflow,
  useUpdateWorkflow,
  useCompleteWorkflowStep,
  useRejectWorkflowStep,
  useProjectWorkflows
} from './useWorkflows'

export type {
  UseWorkflowsResult
} from './useWorkflows'

// Re-export types from backend
export type {
  Workflow,
  WorkflowStatus
} from '@/types/backend'
