'use client';

import React from 'react';
import { Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

export interface SearchFilter {
  id: string;
  label: string;
  value: string | string[];
  type: 'select' | 'multiselect' | 'checkbox' | 'date';
  options?: { label: string; value: string }[];
}

export interface SearchFiltersProps {
  filters: SearchFilter[];
  activeFilters: Record<string, any>;
  onFilterChange: (filterId: string, value: any) => void;
  onClearFilters: () => void;
  className?: string;
}

export function SearchFilters({
  filters,
  activeFilters,
  onFilterChange,
  onClearFilters,
  className
}: SearchFiltersProps) {
  const activeFilterCount = Object.keys(activeFilters).filter(
    key => activeFilters[key] !== undefined && activeFilters[key] !== null && activeFilters[key] !== ''
  ).length;

  const renderFilter = (filter: SearchFilter) => {
    const value = activeFilters[filter.id];

    switch (filter.type) {
      case 'select':
        return (
          <Select
            value={value || ''}
            onValueChange={(newValue) => onFilterChange(filter.id, newValue)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder={filter.label} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All {filter.label}</SelectItem>
              {filter.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiselect':
        return (
          <div className="space-y-2">
            <Label className="text-sm font-medium">{filter.label}</Label>
            <div className="space-y-2">
              {filter.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${filter.id}-${option.value}`}
                    checked={Array.isArray(value) && value.includes(option.value)}
                    onCheckedChange={(checked) => {
                      const currentValues = Array.isArray(value) ? value : [];
                      if (checked) {
                        onFilterChange(filter.id, [...currentValues, option.value]);
                      } else {
                        onFilterChange(filter.id, currentValues.filter(v => v !== option.value));
                      }
                    }}
                  />
                  <Label
                    htmlFor={`${filter.id}-${option.value}`}
                    className="text-sm font-normal"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={filter.id}
              checked={!!value}
              onCheckedChange={(checked) => onFilterChange(filter.id, checked)}
            />
            <Label htmlFor={filter.id} className="text-sm font-normal">
              {filter.label}
            </Label>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={className}>
      <div className="flex items-center space-x-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="start">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Filters</h4>
                {activeFilterCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClearFilters}
                    className="h-auto p-0 text-sm"
                  >
                    Clear all
                  </Button>
                )}
              </div>
              <Separator />
              <div className="space-y-4">
                {filters.map((filter) => (
                  <div key={filter.id}>
                    {renderFilter(filter)}
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>

        {/* Active filter badges */}
        {activeFilterCount > 0 && (
          <div className="flex items-center space-x-1 flex-wrap">
            {Object.entries(activeFilters).map(([filterId, value]) => {
              if (!value || value === '' || (Array.isArray(value) && value.length === 0)) {
                return null;
              }

              const filter = filters.find(f => f.id === filterId);
              if (!filter) return null;

              const displayValue = Array.isArray(value) 
                ? value.length === 1 
                  ? value[0] 
                  : `${value.length} selected`
                : value;

              return (
                <Badge key={filterId} variant="secondary" className="text-xs">
                  {filter.label}: {displayValue}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-3 w-3 ml-1 hover:bg-transparent"
                    onClick={() => onFilterChange(filterId, filter.type === 'multiselect' ? [] : '')}
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
