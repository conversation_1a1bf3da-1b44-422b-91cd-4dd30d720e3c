/**
 * PKI Provider Interface
 * Standardized interface for external PKI providers
 */

export interface PKIProviderConfig {
  providerId: string
  name: string
  type: 'cloud' | 'enterprise' | 'self-signed'
  apiEndpoint?: string
  credentials: {
    apiKey?: string
    clientId?: string
    clientSecret?: string
    certificatePath?: string
    customConfig?: Record<string, any>
  }
  settings: {
    defaultValidityPeriod: number // months
    keySize: 2048 | 3072 | 4096
    algorithm: 'RSA' | 'ECDSA'
    hashAlgorithm: 'SHA-256' | 'SHA-384' | 'SHA-512'
    certificateProfile?: string
    timestampingEnabled: boolean
    crlDistributionPoints?: string[]
    ocspResponders?: string[]
  }
  compliance: {
    eidas: boolean
    esignAct: boolean
    cfr21Part11: boolean
    fips140Level?: 1 | 2 | 3 | 4
    commonCriteria?: string
  }
  limits: {
    maxCertificatesPerMonth?: number
    maxKeySize?: number
    allowedCertificateTypes: CertificateType[]
  }
}

export enum CertificateType {
  DOCUMENT_SIGNING = 'document-signing',
  CODE_SIGNING = 'code-signing',
  SSL_TLS = 'ssl-tls',
  EMAIL_PROTECTION = 'email-protection',
  CLIENT_AUTHENTICATION = 'client-authentication',
  SERVER_AUTHENTICATION = 'server-authentication'
}

export enum CertificateStatus {
  PENDING = 'pending',
  ISSUED = 'issued',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  SUSPENDED = 'suspended'
}

export interface CertificateRequest {
  commonName: string
  organizationName?: string
  organizationalUnit?: string
  locality?: string
  state?: string
  country?: string
  emailAddress?: string
  subjectAlternativeNames?: string[]
  keyUsage: string[]
  extendedKeyUsage: string[]
  validityPeriod: number // months
  certificateType: CertificateType
  customAttributes?: Record<string, string>
}

export interface Certificate {
  id: string
  serialNumber: string
  commonName: string
  issuer: string
  subject: string
  notBefore: Date
  notAfter: Date
  status: CertificateStatus
  certificateData: string // Base64 encoded certificate
  certificateChain?: string[] // Base64 encoded certificate chain
  publicKey: string
  keyId: string // Reference to private key in Azure Key Vault
  fingerprint: string
  algorithm: string
  keySize: number
  certificateType: CertificateType
  revocationReason?: string
  crlDistributionPoints?: string[]
  ocspResponders?: string[]
  metadata: {
    providerId: string
    providerCertificateId?: string
    issuedAt: Date
    requestedBy: string
    organizationId: string
    projectId?: string
    complianceLevel: 'basic' | 'advanced' | 'qualified'
    auditTrail: AuditEvent[]
  }
}

export interface AuditEvent {
  timestamp: Date
  event: 'requested' | 'issued' | 'renewed' | 'revoked' | 'expired' | 'validated'
  actor: string
  details: Record<string, any>
  signature?: string // Cryptographic proof of event
}

export interface CertificateValidationResult {
  isValid: boolean
  validationTime: Date
  certificateStatus: CertificateStatus
  chainValid: boolean
  revocationStatus: 'good' | 'revoked' | 'unknown'
  trustAnchor?: string
  validationErrors: string[]
  complianceLevel: 'basic' | 'advanced' | 'qualified'
  validationPath: string[]
}

export interface SigningRequest {
  data: Buffer
  dataType: 'hash' | 'document'
  hashAlgorithm?: 'SHA-256' | 'SHA-384' | 'SHA-512'
  certificateId: string
  timestampingRequired: boolean
  signatureFormat: 'PKCS7' | 'CAdES' | 'PAdES' | 'XAdES'
  includeSigningCertificate: boolean
  includeCertificateChain: boolean
}

export interface SigningResult {
  signature: string // Base64 encoded signature
  signingCertificate: string
  certificateChain?: string[]
  timestamp?: string
  algorithm: string
  signatureFormat: string
  metadata: {
    signingTime: Date
    signerId: string
    certificateId: string
    providerId: string
    complianceLevel: 'basic' | 'advanced' | 'qualified'
  }
}

export interface RevocationRequest {
  certificateId: string
  reason: 'unspecified' | 'keyCompromise' | 'caCompromise' | 'affiliationChanged' | 
          'superseded' | 'cessationOfOperation' | 'certificateHold' | 'removeFromCRL'
  revocationDate?: Date
  invalidityDate?: Date
}

/**
 * Abstract PKI Provider Interface
 * All PKI providers must implement this interface
 */
export abstract class PKIProvider {
  protected config: PKIProviderConfig
  protected initialized: boolean = false

  constructor(config: PKIProviderConfig) {
    this.config = config
  }

  /**
   * Initialize the PKI provider
   */
  abstract initialize(): Promise<void>

  /**
   * Test connection and validate configuration
   */
  abstract testConnection(): Promise<boolean>

  /**
   * Request a new certificate
   */
  abstract requestCertificate(request: CertificateRequest): Promise<Certificate>

  /**
   * Get certificate by ID
   */
  abstract getCertificate(certificateId: string): Promise<Certificate>

  /**
   * List certificates with optional filters
   */
  abstract listCertificates(filters?: {
    status?: CertificateStatus[]
    certificateType?: CertificateType[]
    expiringBefore?: Date
    issuedAfter?: Date
  }): Promise<Certificate[]>

  /**
   * Renew an existing certificate
   */
  abstract renewCertificate(certificateId: string, validityPeriod?: number): Promise<Certificate>

  /**
   * Revoke a certificate
   */
  abstract revokeCertificate(request: RevocationRequest): Promise<void>

  /**
   * Validate certificate and chain
   */
  abstract validateCertificate(certificateId: string): Promise<CertificateValidationResult>

  /**
   * Sign data using certificate
   */
  abstract signData(request: SigningRequest): Promise<SigningResult>

  /**
   * Verify signature
   */
  abstract verifySignature(
    data: Buffer,
    signature: string,
    certificateId: string
  ): Promise<boolean>

  /**
   * Get Certificate Revocation List
   */
  abstract getCRL(): Promise<string>

  /**
   * Check OCSP status
   */
  abstract checkOCSP(certificateId: string): Promise<'good' | 'revoked' | 'unknown'>

  /**
   * Get provider capabilities
   */
  getCapabilities(): PKIProviderConfig {
    return this.config
  }

  /**
   * Check if provider is initialized
   */
  isInitialized(): boolean {
    return this.initialized
  }

  /**
   * Get provider health status
   */
  abstract getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    lastCheck: Date
    responseTime: number
    errors?: string[]
  }>
}

/**
 * PKI Provider Factory
 */
export class PKIProviderFactory {
  private static providers: Map<string, typeof PKIProvider> = new Map()

  static registerProvider(providerId: string, providerClass: typeof PKIProvider): void {
    this.providers.set(providerId, providerClass)
  }

  static createProvider(config: PKIProviderConfig): PKIProvider {
    const ProviderClass = this.providers.get(config.providerId)
    if (!ProviderClass) {
      throw new Error(`PKI provider '${config.providerId}' not found`)
    }
    // Use any to bypass abstract class instantiation check
    return new (ProviderClass as any)(config)
  }

  static getAvailableProviders(): string[] {
    return Array.from(this.providers.keys())
  }
}

/**
 * PKI Provider Registry
 * Manages multiple PKI providers and routing
 */
export class PKIProviderRegistry {
  private providers: Map<string, PKIProvider> = new Map()
  private defaultProvider?: string

  async addProvider(config: PKIProviderConfig): Promise<void> {
    const provider = PKIProviderFactory.createProvider(config)
    await provider.initialize()
    this.providers.set(config.providerId, provider)
    
    if (!this.defaultProvider) {
      this.defaultProvider = config.providerId
    }
  }

  getProvider(providerId?: string): PKIProvider {
    const id = providerId || this.defaultProvider
    if (!id) {
      throw new Error('No PKI provider specified and no default provider set')
    }
    
    const provider = this.providers.get(id)
    if (!provider) {
      throw new Error(`PKI provider '${id}' not found`)
    }
    
    return provider
  }

  listProviders(): PKIProviderConfig[] {
    return Array.from(this.providers.values()).map(p => p.getCapabilities())
  }

  setDefaultProvider(providerId: string): void {
    if (!this.providers.has(providerId)) {
      throw new Error(`PKI provider '${providerId}' not found`)
    }
    this.defaultProvider = providerId
  }

  async removeProvider(providerId: string): Promise<void> {
    this.providers.delete(providerId)
    if (this.defaultProvider === providerId) {
      this.defaultProvider = this.providers.size > 0 ? 
        Array.from(this.providers.keys())[0] : undefined
    }
  }
}
