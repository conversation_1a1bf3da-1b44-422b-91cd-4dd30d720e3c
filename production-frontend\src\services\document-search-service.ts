/**
 * Document Search Service
 * Provides advanced document search and indexing capabilities
 */

import { backendApiClient } from './backend-api-client'
import type { ID } from '../types'

export interface DocumentSearchResult {
  id: ID
  documentId: ID
  title: string
  content: string
  excerpt: string
  highlights: string[]
  score: number
  page?: number
  position?: {
    x: number
    y: number
    width: number
    height: number
  }
  metadata: Record<string, any>
}

export interface SearchOptions {
  query: string
  documentId?: ID
  documentIds?: ID[]
  filters?: {
    documentType?: string[]
    dateRange?: {
      start: string
      end: string
    }
    tags?: string[]
    status?: string[]
    projectId?: ID
    organizationId?: ID
  }
  searchType?: 'exact' | 'fuzzy' | 'semantic' | 'hybrid'
  limit?: number
  offset?: number
  includeContent?: boolean
  includeHighlights?: boolean
  sortBy?: 'relevance' | 'date' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface IndexingOptions {
  extractText?: boolean
  extractMetadata?: boolean
  generateThumbnails?: boolean
  enableOCR?: boolean
  languages?: string[]
  customFields?: Record<string, any>
  // RAG-specific options
  chunkSize?: number
  overlapSize?: number
  extractTables?: boolean
  extractImages?: boolean
}

export interface SearchSuggestion {
  text: string
  type: 'query' | 'filter' | 'document'
  score: number
  metadata?: Record<string, any>
}

export interface SearchAnalytics {
  totalSearches: number
  popularQueries: Array<{
    query: string
    count: number
    avgResultCount: number
  }>
  searchTrends: Array<{
    date: string
    searchCount: number
    avgResponseTime: number
  }>
  documentPopularity: Array<{
    documentId: ID
    title: string
    searchCount: number
    clickCount: number
  }>
}

class DocumentSearchService {
  /**
   * Search documents with advanced options
   */
  async searchDocuments(options: SearchOptions): Promise<{
    results: DocumentSearchResult[]
    total: number
    facets?: Record<string, Array<{ value: string; count: number }>>
    suggestions?: SearchSuggestion[]
    responseTime: number
  }> {
    const response = await backendApiClient.request('/documents/search', {
      method: 'POST',
      body: JSON.stringify(options)
    })
    return response
  }

  /**
   * Search within a specific document
   */
  async searchInDocument(documentId: ID, query: string, options?: Partial<SearchOptions>): Promise<{
    results: DocumentSearchResult[]
    total: number
    pages: Array<{
      page: number
      matches: number
      highlights: Array<{
        text: string
        position: { x: number; y: number; width: number; height: number }
      }>
    }>
  }> {
    const searchOptions: SearchOptions = {
      query,
      documentId,
      includeContent: true,
      includeHighlights: true,
      ...options
    }

    const response = await backendApiClient.request(`/documents/${documentId}/search`, {
      method: 'POST',
      body: JSON.stringify(searchOptions)
    })
    return response
  }

  /**
   * Get search suggestions
   */
  async getSuggestions(query: string, options?: {
    documentId?: ID
    limit?: number
    types?: Array<'query' | 'filter' | 'document'>
  }): Promise<SearchSuggestion[]> {
    const params = new URLSearchParams()
    params.append('q', query)
    if (options?.documentId) params.append('documentId', options.documentId)
    if (options?.limit) params.append('limit', options.limit.toString())
    if (options?.types) params.append('types', options.types.join(','))

    const response = await backendApiClient.request(`/documents/search/suggestions?${params}`)
    return response
  }

  /**
   * Index a document for search
   */
  async indexDocument(documentId: ID, options?: IndexingOptions): Promise<{
    success: boolean
    indexedAt: string
    extractedText?: string
    metadata?: Record<string, any>
    thumbnails?: string[]
  }> {
    // Use RAG query endpoint for document indexing
    const response = await backendApiClient.request('/rag/query', {
      method: 'POST',
      body: JSON.stringify({
        operationType: 'RAG_QUERY',
        ragRequest: {
          operation: 'INDEX_DOCUMENT',
          indexData: {
            documentId,
            chunkSize: options?.chunkSize || 1000,
            overlapSize: options?.overlapSize || 200,
            extractTables: options?.extractTables || false,
            extractImages: options?.extractImages || false
          }
        }
      })
    })
    return response
  }

  /**
   * Bulk index multiple documents
   */
  async bulkIndexDocuments(documentIds: ID[], options?: IndexingOptions): Promise<{
    success: ID[]
    failed: Array<{ documentId: ID; error: string }>
    totalProcessed: number
  }> {
    // Process documents individually using existing endpoint
    const results = await Promise.allSettled(
      documentIds.map(id => this.indexDocument(id, options))
    )

    const success: ID[] = []
    const failed: Array<{ documentId: ID; error: string }> = []

    results.forEach((result, index) => {
      const documentId = documentIds[index]
      if (result.status === 'fulfilled') {
        success.push(documentId)
      } else {
        failed.push({
          documentId,
          error: result.reason?.message || 'Unknown error'
        })
      }
    })

    return {
      success,
      failed,
      totalProcessed: documentIds.length
    }
  }

  /**
   * Remove document from search index
   */
  async removeFromIndex(documentId: ID): Promise<void> {
    await backendApiClient.request(`/documents/${documentId}/index`, {
      method: 'DELETE'
    })
  }

  /**
   * Get document indexing status
   */
  async getIndexingStatus(documentId: ID): Promise<{
    indexed: boolean
    indexedAt?: string
    status: 'pending' | 'processing' | 'completed' | 'failed'
    progress?: number
    error?: string
  }> {
    const response = await backendApiClient.request(`/documents/${documentId}/index/status`)
    return response
  }

  /**
   * Reindex all documents
   */
  async reindexAll(options?: IndexingOptions & {
    batchSize?: number
    delay?: number
  }): Promise<{
    jobId: string
    status: string
    estimatedTime: number
  }> {
    const response = await backendApiClient.request('/documents/reindex', {
      method: 'POST',
      body: JSON.stringify(options)
    })
    return response
  }

  /**
   * Get search analytics
   */
  async getSearchAnalytics(dateRange?: {
    start: string
    end: string
  }): Promise<SearchAnalytics> {
    const params = new URLSearchParams()
    if (dateRange?.start) params.append('start', dateRange.start)
    if (dateRange?.end) params.append('end', dateRange.end)

    const response = await backendApiClient.request(`/documents/search/analytics?${params}`)
    return response
  }

  /**
   * Save search query for analytics
   */
  async trackSearch(query: string, resultCount: number, documentId?: ID): Promise<void> {
    try {
      await backendApiClient.request('/documents/search/track', {
        method: 'POST',
        body: JSON.stringify({
          query,
          resultCount,
          documentId,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      // Don't throw errors for tracking failures
      console.warn('Failed to track search:', error)
    }
  }

  /**
   * Get popular search queries
   */
  async getPopularQueries(limit: number = 10): Promise<Array<{
    query: string
    count: number
    avgResultCount: number
  }>> {
    const response = await backendApiClient.request(`/documents/search/popular?limit=${limit}`)
    return response
  }

  /**
   * Get search history for current user
   */
  async getSearchHistory(limit: number = 20): Promise<Array<{
    query: string
    timestamp: string
    resultCount: number
    documentId?: ID
  }>> {
    const response = await backendApiClient.request(`/documents/search/history?limit=${limit}`)
    return response
  }

  /**
   * Clear search history for current user
   */
  async clearSearchHistory(): Promise<void> {
    await backendApiClient.request('/documents/search/history', {
      method: 'DELETE'
    })
  }

  /**
   * Export search results
   */
  async exportSearchResults(
    options: SearchOptions,
    format: 'csv' | 'json' | 'xlsx' = 'csv'
  ): Promise<Blob> {
    const response = await backendApiClient.request('/documents/search/export', {
      method: 'POST',
      body: JSON.stringify({
        ...options,
        format
      })
    })
    return response as Blob
  }

  /**
   * Get search configuration
   */
  async getSearchConfig(): Promise<{
    maxResults: number
    enableFuzzySearch: boolean
    enableSemanticSearch: boolean
    supportedLanguages: string[]
    indexingOptions: IndexingOptions
  }> {
    const response = await backendApiClient.request('/documents/search/config')
    return response
  }

  /**
   * Update search configuration (admin only)
   */
  async updateSearchConfig(config: {
    maxResults?: number
    enableFuzzySearch?: boolean
    enableSemanticSearch?: boolean
    supportedLanguages?: string[]
    indexingOptions?: Partial<IndexingOptions>
  }): Promise<void> {
    await backendApiClient.request('/documents/search/config', {
      method: 'PUT',
      body: JSON.stringify(config)
    })
  }

  /**
   * Test search functionality
   */
  async testSearch(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    responseTime: number
    indexSize: number
    lastIndexed: string
    errors?: string[]
  }> {
    const response = await backendApiClient.request('/documents/search/health')
    return response
  }

  /**
   * Get search statistics
   */
  async getSearchStats(): Promise<{
    totalDocuments: number
    indexedDocuments: number
    totalSearches: number
    avgResponseTime: number
    indexSize: string
    lastUpdated: string
  }> {
    const response = await backendApiClient.request('/documents/search/stats')
    return response
  }
}

// Export singleton instance
export const documentSearchService = new DocumentSearchService()
export default documentSearchService
