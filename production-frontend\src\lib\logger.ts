/**
 * Logger Utility
 * Provides structured logging with different levels and contexts
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error'

export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: string
  data?: any
  userId?: string
  sessionId?: string
  requestId?: string
  error?: Error
}

export interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  maxLocalEntries: number
  enablePerformanceLogging: boolean
  enableUserTracking: boolean
}

class Logger {
  private config: LoggerConfig
  private localEntries: LogEntry[] = []
  private sessionId: string
  private userId?: string

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: 'info',
      enableConsole: true,
      enableRemote: false,
      maxLocalEntries: 1000,
      enablePerformanceLogging: true,
      enableUserTracking: true,
      ...config
    }

    this.sessionId = this.generateSessionId()
    
    // Set up global error handlers
    this.setupGlobalErrorHandlers()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private setupGlobalErrorHandlers(): void {
    // Only handle uncaught errors (not promise rejections to avoid conflicts with error monitoring)
    window.addEventListener('error', (event) => {
      this.error('Uncaught error', 'global', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
      })
    })
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error']
    const currentLevelIndex = levels.indexOf(this.config.level)
    const messageLevelIndex = levels.indexOf(level)
    return messageLevelIndex >= currentLevelIndex
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: string,
    data?: any,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      data,
      userId: this.userId,
      sessionId: this.sessionId,
      requestId: this.generateRequestId(),
      error
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`
  }

  private logToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return

    const prefix = `[${entry.timestamp}] [${entry.level.toUpperCase()}]`
    const message = entry.context ? `${prefix} [${entry.context}] ${entry.message}` : `${prefix} ${entry.message}`

    switch (entry.level) {
      case 'debug':
        console.debug(message, entry.data)
        break
      case 'info':
        console.info(message, entry.data)
        break
      case 'warn':
        console.warn(message, entry.data)
        break
      case 'error':
        console.error(message, entry.data, entry.error)
        break
    }
  }

  private async logToRemote(entry: LogEntry): Promise<void> {
    if (!this.config.enableRemote || !this.config.remoteEndpoint) return

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(entry)
      })
    } catch (error) {
      // Fallback to console if remote logging fails
      console.error('Failed to send log to remote endpoint:', error)
    }
  }

  private storeLocally(entry: LogEntry): void {
    this.localEntries.push(entry)
    
    // Keep only the most recent entries
    if (this.localEntries.length > this.config.maxLocalEntries) {
      this.localEntries = this.localEntries.slice(-this.config.maxLocalEntries)
    }
  }

  private log(level: LogLevel, message: string, context?: string, data?: any, error?: Error): void {
    if (!this.shouldLog(level)) return

    const entry = this.createLogEntry(level, message, context, data, error)
    
    this.logToConsole(entry)
    this.storeLocally(entry)
    
    if (this.config.enableRemote) {
      this.logToRemote(entry).catch(() => {
        // Silent fail for remote logging
      })
    }
  }

  // Public logging methods
  debug(message: string, context?: string, data?: any): void {
    this.log('debug', message, context, data)
  }

  info(message: string, context?: string, data?: any): void {
    this.log('info', message, context, data)
  }

  warn(message: string, context?: string, data?: any): void {
    this.log('warn', message, context, data)
  }

  error(message: string, context?: string, data?: any, error?: Error): void {
    this.log('error', message, context, data, error)
  }

  // Performance logging
  startTimer(label: string): () => void {
    if (!this.config.enablePerformanceLogging) return () => {}

    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      this.info(`Timer: ${label}`, 'performance', { duration: `${duration.toFixed(2)}ms` })
    }
  }

  // User tracking
  setUserId(userId: string): void {
    if (this.config.enableUserTracking) {
      this.userId = userId
      this.info('User session started', 'auth', { userId })
    }
  }

  clearUserId(): void {
    if (this.config.enableUserTracking && this.userId) {
      this.info('User session ended', 'auth', { userId: this.userId })
      this.userId = undefined
    }
  }

  // API request logging
  logApiRequest(method: string, url: string, data?: any): void {
    this.debug(`API Request: ${method} ${url}`, 'api', data)
  }

  logApiResponse(method: string, url: string, status: number, data?: any, duration?: number): void {
    const level = status >= 400 ? 'error' : 'debug'
    this.log(level, `API Response: ${method} ${url} - ${status}`, 'api', {
      status,
      data,
      duration: duration ? `${duration}ms` : undefined
    })
  }

  logApiError(method: string, url: string, error: Error): void {
    this.error(`API Error: ${method} ${url}`, 'api', { url, method }, error)
  }

  // Component lifecycle logging
  logComponentMount(componentName: string, props?: any): void {
    this.debug(`Component mounted: ${componentName}`, 'component', props)
  }

  logComponentUnmount(componentName: string): void {
    this.debug(`Component unmounted: ${componentName}`, 'component')
  }

  logComponentError(componentName: string, error: Error, errorInfo?: any): void {
    this.error(`Component error: ${componentName}`, 'component', errorInfo, error)
  }

  // State management logging
  logStateChange(storeName: string, action: string, previousState?: any, newState?: any): void {
    this.debug(`State change: ${storeName}.${action}`, 'state', {
      previousState,
      newState
    })
  }

  // Navigation logging
  logNavigation(from: string, to: string): void {
    this.info(`Navigation: ${from} -> ${to}`, 'navigation')
  }

  // Feature usage logging
  logFeatureUsage(feature: string, action: string, data?: any): void {
    this.info(`Feature usage: ${feature}.${action}`, 'feature', data)
  }

  // Configuration
  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
  }

  getConfig(): LoggerConfig {
    return { ...this.config }
  }

  // Log retrieval
  getLocalLogs(filter?: {
    level?: LogLevel
    context?: string
    startTime?: string
    endTime?: string
  }): LogEntry[] {
    let logs = [...this.localEntries]

    if (filter) {
      if (filter.level) {
        logs = logs.filter(log => log.level === filter.level)
      }
      if (filter.context) {
        logs = logs.filter(log => log.context === filter.context)
      }
      if (filter.startTime) {
        logs = logs.filter(log => log.timestamp >= filter.startTime!)
      }
      if (filter.endTime) {
        logs = logs.filter(log => log.timestamp <= filter.endTime!)
      }
    }

    return logs
  }

  // Export logs
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    const logs = this.getLocalLogs()

    if (format === 'csv') {
      const headers = ['timestamp', 'level', 'message', 'context', 'userId', 'sessionId']
      const csvRows = [
        headers.join(','),
        ...logs.map(log => [
          log.timestamp,
          log.level,
          `"${log.message.replace(/"/g, '""')}"`,
          log.context || '',
          log.userId || '',
          log.sessionId
        ].join(','))
      ]
      return csvRows.join('\n')
    }

    return JSON.stringify(logs, null, 2)
  }

  // Clear logs
  clearLogs(): void {
    this.localEntries = []
    this.info('Local logs cleared', 'logger')
  }

  // Health check
  getLoggerHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy'
    localEntriesCount: number
    sessionId: string
    userId?: string
    config: LoggerConfig
  } {
    return {
      status: 'healthy',
      localEntriesCount: this.localEntries.length,
      sessionId: this.sessionId,
      userId: this.userId,
      config: this.config
    }
  }
}

// Create and export singleton instance
export const logger = new Logger({
  level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
  enableConsole: true,
  enableRemote: process.env.NODE_ENV === 'production',
  remoteEndpoint: process.env.NEXT_PUBLIC_LOGGING_ENDPOINT,
  enablePerformanceLogging: true,
  enableUserTracking: true
})

export default logger
