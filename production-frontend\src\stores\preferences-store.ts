/**
 * Preferences Store - Zustand Store for User Preferences
 * Manages user settings, theme, language, and dashboard preferences
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '@/services/backend-api-client'
import type { UserPreferences, DashboardPreferences } from '@/types/preferences'

interface PreferencesState {
  // Core preferences
  preferences: UserPreferences
  
  // UI state
  loading: boolean
  error: string | null
  
  // Cache
  lastUpdated: string | null
  _hydrated: boolean
}

interface PreferencesActions {
  // Preferences management
  fetchPreferences: () => Promise<void>
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>
  resetPreferences: () => Promise<void>
  
  // Theme management
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  toggleTheme: () => void
  
  // Language management
  setLanguage: (language: string) => void
  
  // Dashboard preferences
  updateDashboardPreferences: (preferences: Partial<DashboardPreferences>) => void
  
  // Notification preferences
  updateNotificationPreferences: (preferences: Partial<UserPreferences['notifications']>) => Promise<void>
  
  // Privacy preferences
  updatePrivacyPreferences: (preferences: Partial<UserPreferences['privacy']>) => Promise<void>
  
  // Accessibility preferences
  updateAccessibilityPreferences: (preferences: Partial<UserPreferences['accessibility']>) => Promise<void>
  
  // Utility
  clearError: () => void
  reset: () => void
}

export type PreferencesStore = PreferencesState & PreferencesActions

const defaultPreferences: UserPreferences = {
  theme: 'system',
  language: 'en',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  dateFormat: 'MM/dd/yyyy',
  timeFormat: '12h',
  dashboard: {
    layout: 'grid',
    widgets: ['recent-documents', 'project-overview', 'ai-operations'],
    refreshInterval: 30000,
    showWelcome: true,
    compactMode: false,
  },
  notifications: {
    email: true,
    push: true,
    inApp: true,
    sound: true,
    documentUpdates: true,
    projectInvites: true,
    aiOperations: true,
    systemAlerts: true,
    marketing: false,
  },
  privacy: {
    shareAnalytics: true,
    shareUsageData: false,
    allowTracking: false,
    showOnlineStatus: true,
  },
  accessibility: {
    highContrast: false,
    largeText: false,
    reduceMotion: false,
    screenReader: false,
  },
  editor: {
    fontSize: 14,
    fontFamily: 'Inter',
    lineHeight: 1.5,
    tabSize: 2,
    wordWrap: true,
    showLineNumbers: true,
    autoSave: true,
    autoSaveInterval: 30000,
  },
}

const initialState: PreferencesState = {
  preferences: defaultPreferences,
  loading: false,
  error: null,
  lastUpdated: null,
  _hydrated: false,
}

export const usePreferencesStore = create<PreferencesStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Preferences management
      fetchPreferences: async () => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request('/users/preferences', {
            method: 'GET'
          })
          
          set({
            preferences: { ...defaultPreferences, ...response.preferences },
            lastUpdated: new Date().toISOString(),
            loading: false
          })
        } catch (error: any) {
          console.error('Fetch preferences error:', error)
          set({
            error: error.message || 'Failed to fetch preferences',
            loading: false
          })
        }
      },

      updatePreferences: async (newPreferences: Partial<UserPreferences>) => {
        set({ loading: true, error: null })

        try {
          const response = await backendApiClient.request('/users/preferences/update', {
            method: 'PUT',
            body: JSON.stringify(newPreferences)
          })
          
          set(state => ({
            preferences: { ...state.preferences, ...response.preferences },
            lastUpdated: new Date().toISOString(),
            loading: false
          }))
        } catch (error: any) {
          console.error('Update preferences error:', error)
          set({
            error: error.message || 'Failed to update preferences',
            loading: false
          })
          throw error
        }
      },

      resetPreferences: async () => {
        set({ loading: true, error: null })
        
        try {
          await backendApiClient.request('/users/preferences/reset', {
            method: 'POST'
          })
          
          set({
            preferences: defaultPreferences,
            lastUpdated: new Date().toISOString(),
            loading: false
          })
        } catch (error: any) {
          console.error('Reset preferences error:', error)
          set({
            error: error.message || 'Failed to reset preferences',
            loading: false
          })
          throw error
        }
      },

      // Theme management
      setTheme: (theme: 'light' | 'dark' | 'system') => {
        set(state => ({
          preferences: {
            ...state.preferences,
            theme
          }
        }))
        
        // Apply theme immediately
        const root = document.documentElement
        if (theme === 'system') {
          const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
          root.classList.toggle('dark', systemTheme === 'dark')
        } else {
          root.classList.toggle('dark', theme === 'dark')
        }
        
        // Update backend
        get().updatePreferences({ theme }).catch(console.error)
      },

      toggleTheme: () => {
        const currentTheme = get().preferences.theme
        const newTheme = currentTheme === 'light' ? 'dark' : 'light'
        get().setTheme(newTheme)
      },

      // Language management
      setLanguage: (language: string) => {
        set(state => ({
          preferences: {
            ...state.preferences,
            language
          }
        }))
        
        // Update backend
        get().updatePreferences({ language }).catch(console.error)
      },

      // Dashboard preferences
      updateDashboardPreferences: (dashboardPrefs: Partial<DashboardPreferences>) => {
        set(state => ({
          preferences: {
            ...state.preferences,
            dashboard: {
              ...state.preferences.dashboard,
              ...dashboardPrefs
            }
          }
        }))
        
        // Update backend
        get().updatePreferences({ 
          dashboard: { ...get().preferences.dashboard, ...dashboardPrefs }
        }).catch(console.error)
      },

      // Notification preferences
      updateNotificationPreferences: async (notificationPrefs: Partial<UserPreferences['notifications']>) => {
        const updatedNotifications = {
          ...get().preferences.notifications,
          ...notificationPrefs
        }
        
        await get().updatePreferences({ notifications: updatedNotifications })
      },

      // Privacy preferences
      updatePrivacyPreferences: async (privacyPrefs: Partial<UserPreferences['privacy']>) => {
        const updatedPrivacy = {
          ...get().preferences.privacy,
          ...privacyPrefs
        }
        
        await get().updatePreferences({ privacy: updatedPrivacy })
      },

      // Accessibility preferences
      updateAccessibilityPreferences: async (accessibilityPrefs: Partial<UserPreferences['accessibility']>) => {
        const updatedAccessibility = {
          ...get().preferences.accessibility,
          ...accessibilityPrefs
        }
        
        set(state => ({
          preferences: {
            ...state.preferences,
            accessibility: updatedAccessibility
          }
        }))
        
        // Apply accessibility settings immediately
        const root = document.documentElement
        if (accessibilityPrefs.highContrast !== undefined) {
          root.classList.toggle('high-contrast', accessibilityPrefs.highContrast)
        }
        if (accessibilityPrefs.largeText !== undefined) {
          root.classList.toggle('large-text', accessibilityPrefs.largeText)
        }
        if (accessibilityPrefs.reduceMotion !== undefined) {
          root.classList.toggle('reduce-motion', accessibilityPrefs.reduceMotion)
        }
        
        await get().updatePreferences({ accessibility: updatedAccessibility })
      },

      // Utility
      clearError: () => {
        set({ error: null })
      },

      reset: () => {
        set(initialState)
      },
    }),
    {
      name: 'preferences-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        preferences: state.preferences,
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
          
          // Apply theme on hydration
          const theme = state.preferences.theme
          const root = document.documentElement
          if (theme === 'system') {
            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
            root.classList.toggle('dark', systemTheme === 'dark')
          } else {
            root.classList.toggle('dark', theme === 'dark')
          }
          
          // Apply accessibility settings on hydration
          const accessibility = state.preferences.accessibility
          root.classList.toggle('high-contrast', accessibility.highContrast)
          root.classList.toggle('large-text', accessibility.largeText)
          root.classList.toggle('reduce-motion', accessibility.reduceMotion)
        }
      },
    }
  )
)

// Selector hooks for better performance
export const usePreferences = () => usePreferencesStore(state => state.preferences)
export const useTheme = () => usePreferencesStore(state => state.preferences.theme)
export const useLanguage = () => usePreferencesStore(state => state.preferences.language)
export const useDashboardPreferences = () => usePreferencesStore(state => state.preferences.dashboard)
export const useNotificationPreferences = () => usePreferencesStore(state => state.preferences.notifications)
export const usePrivacyPreferences = () => usePreferencesStore(state => state.preferences.privacy)
export const useAccessibilityPreferences = () => usePreferencesStore(state => state.preferences.accessibility)
export const usePreferencesLoading = () => usePreferencesStore(state => state.loading)
export const usePreferencesError = () => usePreferencesStore(state => state.error)
