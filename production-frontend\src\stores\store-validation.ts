/**
 * Store Validation and Testing Utilities
 * Comprehensive validation and testing tools for Zustand stores
 */

import { 
  useAuthStore,
  useDocumentStore,
  useProjectStore,
  useWorkflowStore,
  useOrganizationStore,
  useCollaborationStore,
  useAIStore,
  useNotificationStore,
  useTenantStore,
  useTemplateStore,
  useDashboardStore,
  usePreferencesStore
} from './index'

// Store validation schemas
export const storeValidationSchemas = {
  auth: {
    user: (value: any) => value === null || (typeof value === 'object' && value.id),
    token: (value: any) => value === null || typeof value === 'string',
    isAuthenticated: (value: any) => typeof value === 'boolean',
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
  
  document: {
    documents: (value: any) => Array.isArray(value),
    selectedDocument: (value: any) => value === null || (typeof value === 'object' && value.id),
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
    filters: (value: any) => typeof value === 'object',
    pagination: (value: any) => typeof value === 'object' && typeof value.page === 'number',
  },
  
  project: {
    projects: (value: any) => Array.isArray(value),
    selectedProject: (value: any) => value === null || (typeof value === 'object' && value.id),
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
  
  workflow: {
    workflows: (value: any) => Array.isArray(value),
    executions: (value: any) => typeof value === 'object',
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
  
  organization: {
    organizations: (value: any) => Array.isArray(value),
    currentOrganization: (value: any) => value === null || (typeof value === 'object' && value.id),
    members: (value: any) => typeof value === 'object',
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
  
  collaboration: {
    isConnected: (value: any) => typeof value === 'boolean',
    currentSession: (value: any) => value === null || typeof value === 'object',
    participants: (value: any) => Array.isArray(value),
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
  
  ai: {
    models: (value: any) => Array.isArray(value),
    operations: (value: any) => Array.isArray(value),
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
  
  notification: {
    notifications: (value: any) => Array.isArray(value),
    unreadCount: (value: any) => typeof value === 'number',
    loading: (value: any) => typeof value === 'boolean',
    error: (value: any) => value === null || typeof value === 'string',
  },
}

// Store registry for validation
const storeRegistry = {
  auth: useAuthStore,
  document: useDocumentStore,
  project: useProjectStore,
  workflow: useWorkflowStore,
  organization: useOrganizationStore,
  collaboration: useCollaborationStore,
  ai: useAIStore,
  notification: useNotificationStore,
  tenant: useTenantStore,
  template: useTemplateStore,
  dashboard: useDashboardStore,
  preferences: usePreferencesStore,
}

// Validation result interface
export interface ValidationResult {
  storeName: string
  isValid: boolean
  errors: string[]
  warnings: string[]
  fieldResults: Record<string, boolean>
}

// Validate a single store
function validateStoreInternal(storeName: keyof typeof storeRegistry): ValidationResult {
  const store = storeRegistry[storeName]
  const schema = storeValidationSchemas[storeName as keyof typeof storeValidationSchemas]
  
  if (!store) {
    return {
      storeName,
      isValid: false,
      errors: [`Store ${storeName} not found`],
      warnings: [],
      fieldResults: {},
    }
  }

  if (!schema) {
    return {
      storeName,
      isValid: true,
      errors: [],
      warnings: [`No validation schema defined for ${storeName}`],
      fieldResults: {},
    }
  }

  const state = store.getState()
  const errors: string[] = []
  const warnings: string[] = []
  const fieldResults: Record<string, boolean> = {}

  // Validate each field in the schema
  Object.entries(schema).forEach(([field, validator]) => {
    try {
      const isValid = validator((state as any)[field])
      fieldResults[field] = isValid

      if (!isValid) {
        errors.push(`Field '${field}' failed validation`)
      }
    } catch (error) {
      errors.push(`Validation error for field '${field}': ${error}`)
      fieldResults[field] = false
    }
  })

  // Check for required methods
  const requiredMethods = ['reset', 'clearError']
  requiredMethods.forEach(method => {
    if (typeof (state as any)[method] !== 'function') {
      warnings.push(`Missing required method: ${method}`)
    }
  })

  return {
    storeName,
    isValid: errors.length === 0,
    errors,
    warnings,
    fieldResults,
  }
}

// Validate all stores
function validateAllStoresInternal(): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {}

  Object.keys(storeRegistry).forEach(storeName => {
    results[storeName] = validateStoreInternal(storeName as keyof typeof storeRegistry)
  })

  return results
}

// Check store health
function checkStoreHealthInternal(): Record<string, boolean> {
  const health: Record<string, boolean> = {}

  Object.entries(storeRegistry).forEach(([name, store]) => {
    try {
      const state = store.getState() as any
      health[name] = state._hydrated === true && !state.error
    } catch (error) {
      health[name] = false
    }
  })

  return health
}

// Store performance metrics
export interface StoreMetrics {
  storeName: string
  stateSize: number
  actionCount: number
  lastUpdate: number
  subscriptionCount: number
}

function getStoreMetricsInternal(): Record<string, StoreMetrics> {
  const metrics: Record<string, StoreMetrics> = {}

  Object.entries(storeRegistry).forEach(([name, store]) => {
    try {
      const state = store.getState() as any
      const stateSize = JSON.stringify(state).length

      metrics[name] = {
        storeName: name,
        stateSize,
        actionCount: Object.keys(state).filter(key => typeof state[key] === 'function').length,
        lastUpdate: state.lastUpdated ? new Date(state.lastUpdated).getTime() : 0,
        subscriptionCount: (store as any).listeners?.size || 0,
      }
    } catch (error) {
      metrics[name] = {
        storeName: name,
        stateSize: 0,
        actionCount: 0,
        lastUpdate: 0,
        subscriptionCount: 0,
      }
    }
  })

  return metrics
}

// Store testing utilities
class StoreTestUtilsInternal {
  // Reset all stores to initial state
  static resetAllStores() {
    Object.values(storeRegistry).forEach(store => {
      const state = store.getState() as any
      if (typeof state.reset === 'function') {
        state.reset()
      }
    })
  }

  // Wait for store hydration
  static async waitForHydration(timeout = 5000): Promise<boolean> {
    const startTime = Date.now()

    while (Date.now() - startTime < timeout) {
      const allHydrated = Object.values(storeRegistry).every(store => {
        try {
          return (store.getState() as any)._hydrated === true
        } catch {
          return false
        }
      })

      if (allHydrated) {
        return true
      }

      await new Promise(resolve => setTimeout(resolve, 50))
    }

    return false
  }

  // Mock store state
  static mockStoreState<T>(storeName: keyof typeof storeRegistry, mockState: Partial<T>) {
    const store = storeRegistry[storeName] as any
    const originalState = store.getState()

    // Apply mock state
    store.setState({ ...originalState, ...mockState })

    // Return cleanup function
    return () => {
      store.setState(originalState)
    }
  }

  // Simulate store actions
  static async simulateUserLogin() {
    const authStore = useAuthStore.getState()
    await authStore.login({
      email: '<EMAIL>',
      password: 'password123'
    })
  }

  static async simulateDocumentUpload() {
    const documentStore = useDocumentStore.getState()
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' })
    await documentStore.uploadDocument(mockFile, { name: 'Test Document' })
  }

  static async simulateWorkflowExecution() {
    const workflowStore = useWorkflowStore.getState()
    await workflowStore.executeWorkflow('test-workflow-id', { input: 'test data' })
  }
}

// Store debugging utilities
class StoreDebuggerInternal {
  private static subscriptions: Array<() => void> = []

  // Start debugging all stores
  static startDebugging() {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('Store debugging is only available in development mode')
      return
    }

    Object.entries(storeRegistry).forEach(([name, store]) => {
      const unsubscribe = (store as any).subscribe((state: any, prevState: any) => {
        console.group(`🏪 ${name} Store Update`)
        console.log('Previous State:', prevState)
        console.log('New State:', state)
        console.log('Changes:', this.getStateChanges(prevState, state))
        console.groupEnd()
      })

      this.subscriptions.push(unsubscribe)
    })

    console.log('🔍 Store debugging started')
  }

  // Stop debugging
  static stopDebugging() {
    this.subscriptions.forEach(unsubscribe => unsubscribe())
    this.subscriptions = []
    console.log('🔍 Store debugging stopped')
  }

  // Get state changes between two states
  private static getStateChanges(prevState: any, newState: any): Record<string, any> {
    const changes: Record<string, any> = {}
    
    Object.keys(newState).forEach(key => {
      if (prevState[key] !== newState[key]) {
        changes[key] = {
          from: prevState[key],
          to: newState[key]
        }
      }
    })
    
    return changes
  }

  // Log current state of all stores
  static logAllStates() {
    console.group('🏪 All Store States')
    Object.entries(storeRegistry).forEach(([name, store]) => {
      console.log(`${name}:`, store.getState())
    })
    console.groupEnd()
  }
}

// Export validation functions for use in tests
export const validateStore = validateStoreInternal
export const validateAllStores = validateAllStoresInternal
export const checkStoreHealth = checkStoreHealthInternal
export const getStoreMetrics = getStoreMetricsInternal
export const StoreTestUtils = StoreTestUtilsInternal
export const StoreDebugger = StoreDebuggerInternal
