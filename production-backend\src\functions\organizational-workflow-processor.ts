/**
 * Azure Function: Organizational Workflow Processor
 * Processes organizational workflow executions and document routing
 */

import { app, HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
import { enhancedDocumentIntelligence } from '../shared/services/enhanced-document-intelligence';
import { azureSearchService } from '../shared/services/azure-search-service';
import { azureServiceBusService } from '../shared/services/azure-service-bus';
import { db } from '../shared/services/database';
import { logger } from '../shared/utils/logger';

export interface OrganizationalWorkflowRequest {
  action: 'analyze_document' | 'execute_workflow' | 'route_document' | 'check_compliance';
  documentId?: string;
  documentUrl?: string;
  workflowId?: string;
  organizationId: string;
  userId: string;
  data?: Record<string, any>;
}

export interface OrganizationalWorkflowResponse {
  success: boolean;
  data?: any;
  error?: string;
  executionId?: string;
  recommendations?: any[];
}

/**
 * Main organizational workflow processor function
 */
async function organizationalWorkflowProcessor(
  request: HttpRequest,
  context: InvocationContext
): Promise<HttpResponseInit> {
  const startTime = Date.now();
  
  try {
    logger.info('Organizational workflow processor started', {
      invocationId: context.invocationId,
      method: request.method,
      url: request.url
    });

    if (request.method !== 'POST') {
      return {
        status: 405,
        jsonBody: {
          success: false,
          error: 'Method not allowed. Use POST.'
        }
      };
    }

    const body = await request.json() as OrganizationalWorkflowRequest;

    if (!body.organizationId || !body.userId) {
      return {
        status: 400,
        jsonBody: {
          success: false,
          error: 'organizationId and userId are required'
        }
      };
    }

    let result: OrganizationalWorkflowResponse;

    switch (body.action) {
      case 'analyze_document':
        result = await analyzeDocument(body);
        break;
      case 'execute_workflow':
        result = await executeWorkflow(body);
        break;
      case 'route_document':
        result = await routeDocument(body);
        break;
      case 'check_compliance':
        result = await checkCompliance(body);
        break;
      default:
        return {
          status: 400,
          jsonBody: {
            success: false,
            error: `Unknown action: ${body.action}`
          }
        };
    }

    const processingTime = Date.now() - startTime;
    
    logger.info('Organizational workflow processor completed', {
      invocationId: context.invocationId,
      action: body.action,
      success: result.success,
      processingTime
    });

    return {
      status: result.success ? 200 : 400,
      jsonBody: {
        ...result,
        processingTime
      }
    };

  } catch (error) {
    const processingTime = Date.now() - startTime;
    
    logger.error('Organizational workflow processor failed', {
      invocationId: context.invocationId,
      error: error instanceof Error ? error.message : String(error),
      processingTime
    });

    return {
      status: 500,
      jsonBody: {
        success: false,
        error: 'Internal server error',
        processingTime
      }
    };
  }
}

/**
 * Analyze document for organizational workflow processing
 */
async function analyzeDocument(request: OrganizationalWorkflowRequest): Promise<OrganizationalWorkflowResponse> {
  try {
    if (!request.documentUrl) {
      throw new Error('documentUrl is required for document analysis');
    }

    // Perform organizational document analysis
    const analysis = await enhancedDocumentIntelligence.analyzeOrganizationalDocument(request.documentUrl);

    // Store analysis results
    const analysisRecord = {
      id: `org-analysis-${request.documentId}-${Date.now()}`,
      documentId: request.documentId,
      organizationId: request.organizationId,
      userId: request.userId,
      analysis,
      createdAt: new Date().toISOString()
    };

    await db.createItem('organizational-analyses', analysisRecord);

    // Index document for search
    if (request.documentId) {
      await azureSearchService.indexDocument({
        id: request.documentId,
        fileName: request.data?.fileName || 'Unknown',
        content: analysis.extractedData.content || '',
        documentType: analysis.documentType,
        department: analysis.routingRecommendations[0]?.department || 'general',
        tags: Object.keys(analysis.extractedData),
        createdAt: new Date().toISOString(),
        modifiedAt: new Date().toISOString(),
        organizationId: request.organizationId,
        complianceFlags: analysis.complianceFlags.map(flag => flag.framework)
      });
    }

    // Send document processing message for further processing
    if (analysis.routingRecommendations.length > 0) {
      await azureServiceBusService.sendDocumentProcessing({
        documentId: request.documentId || '',
        organizationId: request.organizationId,
        userId: request.userId,
        processingType: 'routing',
        documentUrl: request.documentUrl,
        metadata: {
          documentType: analysis.documentType,
          routingRecommendations: analysis.routingRecommendations,
          complianceFlags: analysis.complianceFlags
        },
        timestamp: new Date().toISOString()
      });
    }

    return {
      success: true,
      data: analysis,
      recommendations: analysis.routingRecommendations
    };

  } catch (error) {
    logger.error('Document analysis failed', { error, request });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Document analysis failed'
    };
  }
}

/**
 * Execute organizational workflow
 */
async function executeWorkflow(request: OrganizationalWorkflowRequest): Promise<OrganizationalWorkflowResponse> {
  try {
    if (!request.workflowId) {
      throw new Error('workflowId is required for workflow execution');
    }

    // Get workflow definition
    const workflow = await db.readItem('workflows', request.workflowId, request.organizationId);
    if (!workflow) {
      throw new Error('Workflow not found');
    }

    // Create workflow execution
    const executionId = `exec-${request.workflowId}-${Date.now()}`;
    const execution = {
      id: executionId,
      workflowId: request.workflowId,
      organizationId: request.organizationId,
      userId: request.userId,
      documentId: request.documentId,
      status: 'running',
      startedAt: new Date().toISOString(),
      data: request.data || {},
      steps: []
    };

    await db.createItem('workflow-executions', execution);

    // Send workflow execution message
    await azureServiceBusService.sendWorkflowExecution({
      workflowId: request.workflowId,
      documentId: request.documentId || '',
      organizationId: request.organizationId,
      userId: request.userId,
      executionId,
      priority: 'normal',
      data: request.data || {},
      timestamp: new Date().toISOString()
    });

    return {
      success: true,
      executionId,
      data: {
        workflowId: request.workflowId,
        status: 'running',
        startedAt: execution.startedAt
      }
    };

  } catch (error) {
    logger.error('Workflow execution failed', { error, request });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Workflow execution failed'
    };
  }
}

/**
 * Route document based on analysis
 */
async function routeDocument(request: OrganizationalWorkflowRequest): Promise<OrganizationalWorkflowResponse> {
  try {
    if (!request.documentId) {
      throw new Error('documentId is required for document routing');
    }

    // Get document analysis
    const analyses = await db.queryItems('organizational-analyses',
      'SELECT * FROM c WHERE c.documentId = @documentId AND c.organizationId = @organizationId',
      [
        { name: '@documentId', value: request.documentId },
        { name: '@organizationId', value: request.organizationId }
      ]
    );

    if (analyses.length === 0) {
      throw new Error('Document analysis not found. Please analyze the document first.');
    }

    const latestAnalysis = analyses.sort((a: any, b: any) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0] as any;

    const routingRecommendations = latestAnalysis.analysis.routingRecommendations;

    // Create routing records
    const routingRecords = routingRecommendations.map((recommendation: any) => ({
      id: `routing-${request.documentId}-${recommendation.department}-${Date.now()}`,
      documentId: request.documentId,
      organizationId: request.organizationId,
      userId: request.userId,
      department: recommendation.department,
      workflow: recommendation.workflow,
      priority: recommendation.priority,
      confidence: recommendation.confidence,
      reasoning: recommendation.reasoning,
      status: 'pending',
      createdAt: new Date().toISOString()
    }));

    // Store routing records
    for (const record of routingRecords) {
      await db.createItem('document-routing', record);
    }

    // Send approval requests for high-priority items
    for (const recommendation of routingRecommendations) {
      if (recommendation.priority === 'high' || recommendation.priority === 'urgent') {
        await azureServiceBusService.sendApprovalRequest({
          approvalId: `approval-${request.documentId}-${Date.now()}`,
          documentId: request.documentId,
          workflowId: recommendation.workflow,
          organizationId: request.organizationId,
          requesterId: request.userId,
          approverId: '', // Will be determined by department routing
          approvalType: 'document_routing',
          priority: recommendation.priority,
          data: {
            department: recommendation.department,
            reasoning: recommendation.reasoning
          },
          timestamp: new Date().toISOString()
        });
      }
    }

    return {
      success: true,
      data: {
        routingRecommendations,
        routingRecords: routingRecords.length
      },
      recommendations: routingRecommendations
    };

  } catch (error) {
    logger.error('Document routing failed', { error, request });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Document routing failed'
    };
  }
}

/**
 * Check compliance for document
 */
async function checkCompliance(request: OrganizationalWorkflowRequest): Promise<OrganizationalWorkflowResponse> {
  try {
    if (!request.documentId) {
      throw new Error('documentId is required for compliance check');
    }

    const complianceFrameworks = request.data?.complianceFrameworks || ['GDPR', 'SOX', 'HIPAA'];

    // Send compliance check message
    await azureServiceBusService.sendComplianceCheck({
      documentId: request.documentId,
      organizationId: request.organizationId,
      complianceFramework: complianceFrameworks,
      checkType: 'automated',
      priority: 'normal',
      data: request.data || {},
      timestamp: new Date().toISOString()
    });

    // Create compliance check record
    const complianceCheck = {
      id: `compliance-${request.documentId}-${Date.now()}`,
      documentId: request.documentId,
      organizationId: request.organizationId,
      userId: request.userId,
      frameworks: complianceFrameworks,
      status: 'pending',
      createdAt: new Date().toISOString()
    };

    await db.createItem('compliance-checks', complianceCheck);

    return {
      success: true,
      data: {
        complianceCheckId: complianceCheck.id,
        frameworks: complianceFrameworks,
        status: 'pending'
      }
    };

  } catch (error) {
    logger.error('Compliance check failed', { error, request });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Compliance check failed'
    };
  }
}

// Register the function
app.http('organizational-workflow-processor', {
  methods: ['POST'],
  authLevel: 'function',
  handler: organizationalWorkflowProcessor
});

export default organizationalWorkflowProcessor;
