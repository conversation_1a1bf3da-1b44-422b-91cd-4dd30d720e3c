"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useWorkflow, useUpdateWorkflow } from "@/hooks/workflows";
import { WorkflowDesigner } from "@/components/workflows";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, GitBranch, Save, AlertCircle } from "lucide-react";
import { WorkflowStep, WorkflowStepStatus } from "@/types/workflow";
import { WorkflowStatus } from "@/services/workflow-service";
import { useToast } from "@/components/ui/use-toast";

export default function EditWorkflowPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { toast } = useToast();
  const [workflowName, setWorkflowName] = useState<string>("");
  const [workflowDescription, setWorkflowDescription] = useState<string>("");
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]);
  const [workflowId, setWorkflowId] = useState<string>("");

  // Extract params
  useEffect(() => {
    params.then(({ id }) => {
      setWorkflowId(id);
    });
  }, [params]);

  // Fetch workflow
  const {
    data: workflow,
    isLoading,
    isError
  } = useWorkflow(workflowId);

  // Update workflow mutation
  const { mutate: updateWorkflow, isPending } = useUpdateWorkflow(workflowId);

  // Set initial values when workflow data is loaded
  useEffect(() => {
    if (workflow) {
      setWorkflowName(workflow.name);
      setWorkflowDescription(workflow.description || "");

      // Convert WorkflowStepDto[] to WorkflowStep[]
      const convertedSteps = workflow.steps.map(step => {
        // Ensure id is always a string
        const id = step.id || `temp-${Math.random().toString(36).substring(2, 9)}`;

        return {
          id,
          workflowId: workflow.id,
          name: step.name,
          description: step.description || "",
          type: step.type,
          order: step.order,
          position: { x: 0, y: step.order * 100 },
          status: step.status || WorkflowStepStatus.PENDING,
          assigneeType: 'user' as const,
          assigneeId: step.assigneeId,
          dueDate: step.dueDate,
          config: (step as any).metadata || {},
          options: (step as any).metadata
        } as WorkflowStep;
      });

      setWorkflowSteps(convertedSteps);
    }
  }, [workflow]);

  // Handle go back
  const handleGoBack = () => {
    router.back();
  };

  // Handle workflow save
  const handleSaveWorkflow = (_steps: WorkflowStep[]) => {
    if (!workflow) return;

    // Update the workflow basic info
    updateWorkflow({
      name: workflowName,
      description: workflowDescription,
      status: WorkflowStatus.ACTIVE
    }, {
      onSuccess: () => {
        toast({
          title: "Success",
          description: "Workflow updated successfully.",
        });
        router.push(`/workflows/${workflowId}`);
      },
      onError: (error: any) => {
        console.error('Error updating workflow:', error);
        toast({
          title: "Error",
          description: "Failed to update workflow. Please try again.",
          variant: "destructive",
        });
      }
    });

    // TODO: Implement workflow steps update when API is available
    console.log('Workflow steps to update:', _steps);
  };

  if (isLoading) {
    return (
      <div className="container py-6 space-y-6">
        <Skeleton className="h-10 w-1/3" />
        <Skeleton className="h-6 w-1/4" />
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }

  if (isError || !workflow) {
    return (
      <div className="container py-6 space-y-6">
        <div className="flex flex-col items-center justify-center p-8">
          <AlertCircle className="h-10 w-10 text-destructive mb-4" />
          <h2 className="text-2xl font-bold mb-2">Workflow Not Found</h2>
          <p className="text-muted-foreground mb-4">
            The workflow you are trying to edit does not exist or you don't have permission to edit it.
          </p>
          <Button onClick={handleGoBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  // Check if workflow is editable
  const isEditable = workflow.status === WorkflowStatus.DRAFT;

  if (!isEditable) {
    return (
      <div className="container py-6 space-y-6">
        <Button variant="ghost" size="sm" onClick={handleGoBack} className="mb-2">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        <div className="flex flex-col items-center justify-center p-8">
          <AlertCircle className="h-10 w-10 text-warning mb-4" />
          <h2 className="text-2xl font-bold mb-2">Workflow Cannot Be Edited</h2>
          <p className="text-muted-foreground mb-4 text-center max-w-md">
            This workflow is currently {workflow.status.toLowerCase()} and cannot be edited.
            Only workflows in DRAFT status can be modified.
          </p>
          <Button onClick={handleGoBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <Button variant="ghost" size="sm" onClick={handleGoBack} className="mb-2">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Edit Workflow</h1>
          <p className="text-muted-foreground">
            Modify workflow details and steps
          </p>
        </div>

        <Button onClick={() => handleSaveWorkflow(workflowSteps)} disabled={isPending}>
          <Save className="mr-2 h-4 w-4" />
          {isPending ? "Saving..." : "Save Changes"}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Workflow Details
          </CardTitle>
          <CardDescription>
            Edit the basic information for this workflow
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <label className="text-sm font-medium">Workflow Name</label>
              <Input
                value={workflowName}
                onChange={(e) => setWorkflowName(e.target.value)}
                placeholder="Enter workflow name"
              />
            </div>

            <div className="grid gap-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={workflowDescription}
                onChange={(e) => setWorkflowDescription(e.target.value)}
                placeholder="Enter workflow description"
                rows={3}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <WorkflowDesigner
        initialSteps={workflowSteps}
        onSave={handleSaveWorkflow}
      />
    </div>
  );
}
