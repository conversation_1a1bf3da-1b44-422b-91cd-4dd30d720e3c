'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { DocumentUploader } from '@/components/documents/document-uploader';
import { useProjects } from '@/hooks/projects';

export default function DocumentUploadPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;

  const { data: projectsResponse, isLoading } = useProjects({ organizationId: undefined });
  const projects = projectsResponse?.data || [];
  const project = projects?.find((p: any) => p.id === projectId);
  const error = null;

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load project details',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  const handleUploadComplete = (files: any[]) => {
    toast({
      title: 'Success',
      description: `${files.length} document(s) uploaded successfully`,
    });
    if (files.length > 0) {
      router.push(`/projects/${projectId}/documents/${files[0].id}`);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading project details...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Upload Document</h1>
          <p className="text-muted-foreground">Project: {project?.name}</p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/projects/${projectId}/documents`)}
        >
          Cancel
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Upload New Document</CardTitle>
        </CardHeader>
        <CardContent>
          <DocumentUploader
            projectId={projectId}
            onUploadComplete={handleUploadComplete}
          />
        </CardContent>
      </Card>
    </div>
  );
}
