'use client'

import React, { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { HelpCircle, X, ChevronRight, Lightbulb, Keyboard, Video, BookOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

interface HelpItem {
  id: string
  title: string
  description: string
  type: 'tip' | 'shortcut' | 'tutorial' | 'guide'
  content: string
  videoUrl?: string
  shortcuts?: Array<{ keys: string; description: string }>
}

interface ContextHelpProps {
  className?: string
}

export function ContextHelp({ className }: ContextHelpProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [helpItems, setHelpItems] = useState<HelpItem[]>([])
  const [selectedItem, setSelectedItem] = useState<HelpItem | null>(null)
  const pathname = usePathname()

  useEffect(() => {
    // Load context-specific help based on current route
    const contextHelp = getContextualHelp(pathname)
    setHelpItems(contextHelp)
  }, [pathname])

  const getContextualHelp = (path: string): HelpItem[] => {
    const baseHelp: HelpItem[] = [
      {
        id: 'global-search',
        title: 'Global Search',
        description: 'Quickly find documents, projects, and content',
        type: 'shortcut',
        content: 'Use Cmd/Ctrl + K to open global search from anywhere. You can search across all your documents, projects, and even chat with AI about your content.',
        shortcuts: [
          { keys: '⌘K / Ctrl+K', description: 'Open global search' },
          { keys: 'Enter', description: 'Navigate to result' },
          { keys: 'Esc', description: 'Close search' }
        ]
      },
      {
        id: 'ai-assistant',
        title: 'AI Assistant',
        description: 'Get help with documents and analysis',
        type: 'tutorial',
        content: 'The AI assistant can help you analyze documents, generate content, and answer questions about your projects. It understands the context of your current work.',
        shortcuts: [
          { keys: '⌘J / Ctrl+J', description: 'Open AI chat' },
          { keys: 'Enter', description: 'Send message' },
          { keys: 'Shift+Enter', description: 'New line' }
        ]
      }
    ]

    // Add route-specific help
    if (path.includes('/dashboard')) {
      baseHelp.push({
        id: 'dashboard-overview',
        title: 'Dashboard Overview',
        description: 'Understanding your dashboard widgets',
        type: 'guide',
        content: 'Your dashboard shows key metrics, recent activity, and quick access to important features. Use the tabs to navigate between different views and the floating action button for quick actions.',
      })
    }

    if (path.includes('/documents')) {
      baseHelp.push({
        id: 'document-collaboration',
        title: 'Document Collaboration',
        description: 'Working together on documents',
        type: 'tutorial',
        content: 'See who else is viewing or editing documents in real-time. Lock documents when making important changes to prevent conflicts.',
      })
    }

    if (path.includes('/projects')) {
      baseHelp.push({
        id: 'project-management',
        title: 'Project Management',
        description: 'Organizing your work effectively',
        type: 'guide',
        content: 'Projects help you organize related documents and collaborate with team members. Use workflows to automate common tasks.',
      })
    }

    return baseHelp
  }

  const getTypeIcon = (type: HelpItem['type']) => {
    switch (type) {
      case 'tip': return <Lightbulb className="h-4 w-4" />
      case 'shortcut': return <Keyboard className="h-4 w-4" />
      case 'tutorial': return <Video className="h-4 w-4" />
      case 'guide': return <BookOpen className="h-4 w-4" />
      default: return <HelpCircle className="h-4 w-4" />
    }
  }

  const getTypeColor = (type: HelpItem['type']) => {
    switch (type) {
      case 'tip': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'shortcut': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'tutorial': return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'guide': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className={cn("relative", className)}
      >
        <HelpCircle className="h-4 w-4" />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              Help & Tips
            </DialogTitle>
          </DialogHeader>

          <Tabs defaultValue="contextual" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="contextual">For This Page</TabsTrigger>
              <TabsTrigger value="shortcuts">Shortcuts</TabsTrigger>
              <TabsTrigger value="tutorials">Tutorials</TabsTrigger>
            </TabsList>

            <TabsContent value="contextual" className="space-y-4">
              <div className="grid gap-4">
                {helpItems.map((item) => (
                  <Card
                    key={item.id}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => setSelectedItem(item)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={cn("p-1 rounded border", getTypeColor(item.type))}>
                            {getTypeIcon(item.type)}
                          </div>
                          <CardTitle className="text-base">{item.title}</CardTitle>
                        </div>
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <CardDescription>{item.description}</CardDescription>
                    </CardHeader>
                  </Card>
                ))}

                {helpItems.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <HelpCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No specific help available for this page</p>
                    <p className="text-sm mt-1">Check the shortcuts and tutorials tabs for general help</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="shortcuts" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-3">Global Shortcuts</h3>
                  <div className="space-y-2">
                    {[
                      { keys: '⌘K / Ctrl+K', description: 'Open global search' },
                      { keys: '⌘J / Ctrl+J', description: 'Open AI assistant' },
                      { keys: 'Esc', description: 'Close modals and dialogs' },
                      { keys: '⌘N / Ctrl+N', description: 'Create new document' },
                      { keys: '⌘S / Ctrl+S', description: 'Save current document' }
                    ].map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">{shortcut.description}</span>
                        <kbd className="px-2 py-1 bg-muted rounded text-xs font-mono">
                          {shortcut.keys}
                        </kbd>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium mb-3">Navigation</h3>
                  <div className="space-y-2">
                    {[
                      { keys: '⌘1-9', description: 'Switch between dashboard tabs' },
                      { keys: '⌘B', description: 'Toggle sidebar' },
                      { keys: '⌘/', description: 'Show help' }
                    ].map((shortcut, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <span className="text-sm">{shortcut.description}</span>
                        <kbd className="px-2 py-1 bg-muted rounded text-xs font-mono">
                          {shortcut.keys}
                        </kbd>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="tutorials" className="space-y-4">
              <div className="grid gap-4">
                {[
                  {
                    title: 'Getting Started',
                    description: 'Learn the basics of document management and collaboration',
                    duration: '5 min',
                    type: 'guide'
                  },
                  {
                    title: 'AI-Powered Analysis',
                    description: 'Discover how to use AI features for document analysis',
                    duration: '8 min',
                    type: 'tutorial'
                  },
                  {
                    title: 'Real-time Collaboration',
                    description: 'Master collaborative editing and communication',
                    duration: '6 min',
                    type: 'tutorial'
                  },
                  {
                    title: 'Advanced Search',
                    description: 'Use filters and semantic search for better results',
                    duration: '4 min',
                    type: 'tip'
                  }
                ].map((tutorial, index) => (
                  <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={cn("p-1 rounded border", getTypeColor(tutorial.type as any))}>
                            {getTypeIcon(tutorial.type as any)}
                          </div>
                          <div>
                            <CardTitle className="text-base">{tutorial.title}</CardTitle>
                            <CardDescription>{tutorial.description}</CardDescription>
                          </div>
                        </div>
                        <Badge variant="outline">{tutorial.duration}</Badge>
                      </div>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Detailed Help Modal */}
          {selectedItem && (
            <Dialog open={!!selectedItem} onOpenChange={() => setSelectedItem(null)}>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    {getTypeIcon(selectedItem.type)}
                    {selectedItem.title}
                  </DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                  <p className="text-muted-foreground">{selectedItem.content}</p>

                  {selectedItem.shortcuts && selectedItem.shortcuts.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Keyboard Shortcuts</h4>
                      <div className="space-y-2">
                        {selectedItem.shortcuts.map((shortcut, index) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <span className="text-sm">{shortcut.description}</span>
                            <kbd className="px-2 py-1 bg-muted rounded text-xs font-mono">
                              {shortcut.keys}
                            </kbd>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {selectedItem.videoUrl && (
                    <div>
                      <h4 className="font-medium mb-2">Video Tutorial</h4>
                      <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                        <Video className="h-12 w-12 text-muted-foreground" />
                        <span className="ml-2 text-muted-foreground">Video tutorial coming soon</span>
                      </div>
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
