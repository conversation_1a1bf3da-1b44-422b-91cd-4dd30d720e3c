/**
 * Zustand Stores - Central Export
 */

// Individual stores
export { useAuthStore } from './auth-store'
export { useAIStore } from './ai-store'
export { useCollaborationStore } from './collaboration-store'
export { useDashboardStore } from './dashboard-store'
export { useDocumentStore } from './document-store'
export { useProjectStore } from './project-store'
export { useOrganizationStore } from './organization-store'
export { useNotificationStore } from './notification-store'
export { useTenantStore } from './tenant-store'
export { useTemplateStore } from './template-store'
export { useWorkflowStore } from './workflow-store'
export { usePreferencesStore } from './preferences-store'

// Auth store selectors
export {
  useUser,
  useIsAuthenticated,
  useAuthLoading,
  useAuthError,
  useToken,
  useLogin,
  useLogout,
  useUpdateProfile,
  useRefreshAuth,
  usePermissions,
  useSession,
  useOrganizationContext,
  useAuthStatus,
  useSessionChecked
} from './auth-store'

// AI store selectors
export {
  useAIOperations,
  useCurrentOperation,
  useAIModels,
  useAILoading,
  useAIError,
  useProcessingQueue,
  useCompletedOperations,
  useFailedOperations,
  useIsProcessing,
  useStartAIOperation,
  useGetAIOperation,
  useListAIOperations,
  useCancelAIOperation,
  useRetryAIOperation,
  useCreateAIModel,
  useTrainAIModel,
  useDeployAIModel,
  useListAIModels,
  useAnalyzeDocument,
  useProcessBatch,
  useOperationProgress,
  useRealTimeAI,
  useAIOperationsSummary,
  useAIOperationById,
  useAIOperationsByStatus,
  useAIOperationsByType
} from './ai-store'

// Collaboration store selectors
export {
  useCurrentSession,
  useCollaborationSessions,
  useSessionParticipants,
  useCollaborationLoading,
  useCollaborationError,
  useIsCollaborationConnected,
  useConnectionId,
  useActiveUsers,
  useDocumentLocks,
  useCollaborationMessages,
  useCollaborationSettings,
  useCreateCollaborationSession,
  useJoinCollaborationSession,
  useLeaveCollaborationSession,
  useEndCollaborationSession,
  useConnectCollaboration,
  useDisconnectCollaboration,
  useReconnectCollaboration,
  useUpdateCursor,
  useUpdatePresence,
  useSetTyping,
  useLockDocument,
  useUnlockDocument,
  useRequestDocumentAccess,
  useSendMessage,
  useClearMessages,
  useCollaborationUtils,
  useSessionStatus,
  useDocumentLockStatus,
  useUserPresence,
  useCollaborationPermissions
} from './collaboration-store'

// Store utilities
export { createStore, createPersistentStore, createStoreWithDevtools } from './store-utils'

// Re-export store types
export type { AuthStore } from './auth-store'
export type { AIStore } from './ai-store'
export type { CollaborationStore } from './collaboration-store'

// Re-export other types
export type {
  DashboardStore,
  DocumentStore,
  ProjectStore,
  TemplateStore,
  WorkflowStore,
  NotificationStore,
  PreferencesStore,
  OrganizationStore,
  BaseStore,
} from '../types/store'
