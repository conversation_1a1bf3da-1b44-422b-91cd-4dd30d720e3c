/**
 * Workflow Store - Zustand Store for Workflow Management
 * Manages workflow creation, execution, and monitoring
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { backendApiClient } from '@/services/backend-api-client'
import type { Workflow, WorkflowExecution } from '@/types/workflow'
import { WorkflowStatus } from '@/types/workflow'
import type { ID } from '@/types'

interface WorkflowState {
  // Core state
  workflows: Workflow[]
  selectedWorkflow: Workflow | null
  executions: Record<ID, WorkflowExecution[]>
  
  // UI state
  loading: boolean
  error: string | null
  
  // Execution state
  executing: Record<ID, boolean>
  executionResults: Record<ID, any>
  
  // Cache
  lastUpdated: string | null
  _hydrated: boolean
}

interface WorkflowActions {
  // Workflow CRUD
  fetchWorkflows: () => Promise<void>
  createWorkflow: (data: any) => Promise<Workflow>
  updateWorkflow: (workflowId: ID, data: Partial<Workflow>) => Promise<void>
  deleteWorkflow: (workflowId: ID) => Promise<void>
  
  // Workflow operations
  selectWorkflow: (workflowId: ID) => Promise<void>
  executeWorkflow: (workflowId: ID, input?: any) => Promise<WorkflowExecution>
  pauseWorkflow: (workflowId: ID) => Promise<void>
  resumeWorkflow: (workflowId: ID) => Promise<void>
  cancelWorkflow: (workflowId: ID) => Promise<void>
  
  // Executions
  fetchExecutions: (workflowId: ID) => Promise<void>
  getExecutionStatus: (executionId: ID) => Promise<WorkflowExecution>
  
  // Computed getters
  getActiveWorkflows: () => Workflow[]
  getWorkflowsByProject: (projectId: ID) => Workflow[]
  getRunningExecutions: () => WorkflowExecution[]
  
  // Utility
  clearError: () => void
  reset: () => void
}

export type WorkflowStore = WorkflowState & WorkflowActions

const initialState: WorkflowState = {
  workflows: [],
  selectedWorkflow: null,
  executions: {},
  loading: false,
  error: null,
  executing: {},
  executionResults: {},
  lastUpdated: null,
  _hydrated: false,
}

export const useWorkflowStore = create<WorkflowStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Workflow CRUD
      fetchWorkflows: async () => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request('/workflows/all', {
            method: 'GET'
          })
          
          set({
            workflows: response.workflows || [],
            lastUpdated: new Date().toISOString(),
            loading: false
          })
        } catch (error: any) {
          console.error('Fetch workflows error:', error)
          set({
            error: error.message || 'Failed to fetch workflows',
            loading: false
          })
        }
      },

      createWorkflow: async (data: any) => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request('/workflows', {
            method: 'POST',
            body: JSON.stringify(data)
          })
          
          const newWorkflow = response.workflow
          set(state => ({
            workflows: [newWorkflow, ...state.workflows],
            selectedWorkflow: newWorkflow,
            loading: false
          }))
          
          return newWorkflow
        } catch (error: any) {
          console.error('Create workflow error:', error)
          set({
            error: error.message || 'Failed to create workflow',
            loading: false
          })
          throw error
        }
      },

      updateWorkflow: async (workflowId: ID, data: Partial<Workflow>) => {
        set({ loading: true, error: null })
        
        try {
          const response = await backendApiClient.request(`/workflows/${workflowId}`, {
            method: 'PUT',
            body: JSON.stringify(data)
          })
          
          const updatedWorkflow = response.workflow
          set(state => ({
            workflows: state.workflows.map(w => 
              w.id === workflowId ? updatedWorkflow : w
            ),
            selectedWorkflow: state.selectedWorkflow?.id === workflowId 
              ? updatedWorkflow 
              : state.selectedWorkflow,
            loading: false
          }))
        } catch (error: any) {
          console.error('Update workflow error:', error)
          set({
            error: error.message || 'Failed to update workflow',
            loading: false
          })
          throw error
        }
      },

      deleteWorkflow: async (workflowId: ID) => {
        set({ loading: true, error: null })
        
        try {
          await backendApiClient.request(`/workflows/${workflowId}`, {
            method: 'DELETE'
          })
          
          set(state => ({
            workflows: state.workflows.filter(w => w.id !== workflowId),
            selectedWorkflow: state.selectedWorkflow?.id === workflowId 
              ? null 
              : state.selectedWorkflow,
            loading: false
          }))
        } catch (error: any) {
          console.error('Delete workflow error:', error)
          set({
            error: error.message || 'Failed to delete workflow',
            loading: false
          })
          throw error
        }
      },

      selectWorkflow: async (workflowId: ID) => {
        const workflow = get().workflows.find(w => w.id === workflowId)
        if (workflow) {
          set({ selectedWorkflow: workflow })
        } else {
          // Fetch workflow if not in store
          try {
            const response = await backendApiClient.request(`/workflows/${workflowId}`)
            set({ selectedWorkflow: response.workflow })
          } catch (error: any) {
            console.error('Select workflow error:', error)
            set({ error: error.message || 'Failed to select workflow' })
          }
        }
      },

      executeWorkflow: async (workflowId: ID, input?: any) => {
        set(state => ({
          executing: { ...state.executing, [workflowId]: true },
          error: null
        }))
        
        try {
          const response = await backendApiClient.request('/workflows/execute', {
            method: 'POST',
            body: JSON.stringify({
              workflowId,
              input: input || {}
            })
          })
          
          const execution = response.execution
          set(state => ({
            executions: {
              ...state.executions,
              [workflowId]: [execution, ...(state.executions[workflowId] || [])]
            },
            executing: { ...state.executing, [workflowId]: false },
            executionResults: { ...state.executionResults, [execution.id]: execution }
          }))
          
          return execution
        } catch (error: any) {
          console.error('Execute workflow error:', error)
          set(state => ({
            executing: { ...state.executing, [workflowId]: false },
            error: error.message || 'Failed to execute workflow'
          }))
          throw error
        }
      },

      pauseWorkflow: async (workflowId: ID) => {
        try {
          await backendApiClient.request(`/workflows/${workflowId}/pause`, {
            method: 'POST'
          })
          
          set(state => ({
            workflows: state.workflows.map(w =>
              w.id === workflowId ? { ...w, status: WorkflowStatus.PAUSED } : w
            )
          }))
        } catch (error: any) {
          console.error('Pause workflow error:', error)
          set({ error: error.message || 'Failed to pause workflow' })
          throw error
        }
      },

      resumeWorkflow: async (workflowId: ID) => {
        try {
          await backendApiClient.request(`/workflows/${workflowId}/resume`, {
            method: 'POST'
          })
          
          set(state => ({
            workflows: state.workflows.map(w =>
              w.id === workflowId ? { ...w, status: WorkflowStatus.ACTIVE } : w
            )
          }))
        } catch (error: any) {
          console.error('Resume workflow error:', error)
          set({ error: error.message || 'Failed to resume workflow' })
          throw error
        }
      },

      cancelWorkflow: async (workflowId: ID) => {
        try {
          await backendApiClient.request(`/workflows/${workflowId}/cancel`, {
            method: 'POST'
          })
          
          set(state => ({
            workflows: state.workflows.map(w =>
              w.id === workflowId ? { ...w, status: WorkflowStatus.CANCELED } : w
            ),
            executing: { ...state.executing, [workflowId]: false }
          }))
        } catch (error: any) {
          console.error('Cancel workflow error:', error)
          set({ error: error.message || 'Failed to cancel workflow' })
          throw error
        }
      },

      fetchExecutions: async (workflowId: ID) => {
        try {
          const response = await backendApiClient.request(`/workflows/${workflowId}/executions`)
          
          set(state => ({
            executions: {
              ...state.executions,
              [workflowId]: response.executions || []
            }
          }))
        } catch (error: any) {
          console.error('Fetch executions error:', error)
          set({ error: error.message || 'Failed to fetch executions' })
        }
      },

      getExecutionStatus: async (executionId: ID) => {
        try {
          const response = await backendApiClient.request(`/workflows/executions/${executionId}`)
          const execution = response.execution
          
          set(state => ({
            executionResults: { ...state.executionResults, [executionId]: execution }
          }))
          
          return execution
        } catch (error: any) {
          console.error('Get execution status error:', error)
          set({ error: error.message || 'Failed to get execution status' })
          throw error
        }
      },

      // Computed getters
      getActiveWorkflows: () => {
        return get().workflows.filter(w => w.status === WorkflowStatus.ACTIVE)
      },

      getWorkflowsByProject: (projectId: ID) => {
        return get().workflows.filter(w => (w as any).projectId === projectId)
      },

      getRunningExecutions: () => {
        const executions = Object.values(get().executions).flat()
        return executions.filter(e => e.status === 'running' || e.status === 'pending')
      },

      // Utility
      clearError: () => {
        set({ error: null })
      },

      reset: () => {
        set(initialState)
      },
    }),
    {
      name: 'workflow-store-v1',
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window === 'undefined') return null
          return localStorage.getItem(name)
        },
        setItem: (name, value) => {
          if (typeof window === 'undefined') return
          localStorage.setItem(name, value)
        },
        removeItem: (name) => {
          if (typeof window === 'undefined') return
          localStorage.removeItem(name)
        },
      })),
      partialize: (state) => ({
        selectedWorkflow: state.selectedWorkflow,
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
        }
      },
    }
  )
)

// Selector hooks for better performance
export const useWorkflows = () => useWorkflowStore(state => state.workflows)
export const useSelectedWorkflow = () => useWorkflowStore(state => state.selectedWorkflow)
export const useWorkflowExecutions = (workflowId: ID) => 
  useWorkflowStore(state => state.executions[workflowId] || [])
export const useWorkflowLoading = () => useWorkflowStore(state => state.loading)
export const useWorkflowError = () => useWorkflowStore(state => state.error)
