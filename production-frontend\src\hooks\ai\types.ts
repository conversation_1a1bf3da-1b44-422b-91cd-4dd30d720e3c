/**
 * AI Types
 * Common types for AI-related hooks and services
 */

import type { ID } from '@/types'

export interface AIModel {
  id: string
  name: string
  provider: AIProvider
  type: 'text' | 'image' | 'embedding' | 'multimodal'
  capabilities: string[]
  maxTokens?: number
  costPerToken?: number
  description?: string
}

export enum AIProvider {
  DEEPSEEK = 'deepseek',
  LLAMA = 'llama',
  COHERE = 'cohere',
  AZURE_AI = 'azure_ai',
  AZURE_OPENAI = 'azure_openai',
  OPENAI = 'openai',
  ANTHROPIC = 'anthropic'
}

export interface AIResponse<T = any> {
  id: string
  data: T
  model: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
    cost?: number
  }
  metadata?: Record<string, any>
  timestamp: string
}

export interface UseAIResult {
  loading: boolean
  error: string | null
  response: AIResponse | null
  
  generate: (prompt: string, options?: any) => Promise<AIResponse>
  cancel: () => void
  reset: () => void
}

export interface DocumentAnalysisResult {
  summary: string
  keyPoints: string[]
  entities: Array<{
    text: string
    type: string
    confidence: number
    startOffset: number
    endOffset: number
  }>
  sentiment: {
    overall: 'positive' | 'negative' | 'neutral'
    score: number
    confidence: number
  }
  topics: Array<{
    name: string
    confidence: number
    keywords: string[]
  }>
  language: {
    detected: string
    confidence: number
  }
  readability: {
    score: number
    level: string
    avgSentenceLength: number
    avgWordsPerSentence: number
  }
  structure: {
    sections: Array<{
      title: string
      startPage: number
      endPage: number
      wordCount: number
    }>
    tables: number
    images: number
    charts: number
  }
  compliance: {
    piiDetected: boolean
    sensitiveData: string[]
    riskLevel: 'low' | 'medium' | 'high'
  }
  insights: string[]
  recommendations: string[]
}

export interface TextGenerationOptions {
  model?: string
  maxTokens?: number
  temperature?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  stopSequences?: string[]
  systemPrompt?: string
  context?: string
  format?: 'text' | 'json' | 'markdown'
}

export interface ImageAnalysisResult {
  description: string
  objects: Array<{
    name: string
    confidence: number
    boundingBox: {
      x: number
      y: number
      width: number
      height: number
    }
  }>
  text: Array<{
    text: string
    confidence: number
    boundingBox: {
      x: number
      y: number
      width: number
      height: number
    }
  }>
  faces: Array<{
    age?: number
    gender?: string
    emotion?: string
    confidence: number
    boundingBox: {
      x: number
      y: number
      width: number
      height: number
    }
  }>
  tags: Array<{
    name: string
    confidence: number
  }>
  categories: Array<{
    name: string
    score: number
  }>
  colors: {
    dominant: string[]
    accent: string
    background: string
    foreground: string
  }
  metadata: {
    width: number
    height: number
    format: string
    size: number
  }
}

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  metadata?: Record<string, any>
}

export interface ChatCompletionOptions {
  model?: string
  messages: ChatMessage[]
  maxTokens?: number
  temperature?: number
  topP?: number
  stream?: boolean
  functions?: Array<{
    name: string
    description: string
    parameters: Record<string, any>
  }>
}

export interface EmbeddingResult {
  vector: number[]
  model: string
  dimensions: number
  usage: {
    tokens: number
    cost?: number
  }
}

export interface AIUsage {
  totalRequests: number
  totalTokens: number
  totalCost: number
  requestsByModel: Record<string, number>
  tokensByModel: Record<string, number>
  costByModel: Record<string, number>
  period: {
    start: string
    end: string
  }
}

export interface AIConfiguration {
  defaultModel: string
  maxTokensPerRequest: number
  rateLimits: {
    requestsPerMinute: number
    tokensPerMinute: number
  }
  enabledProviders: AIProvider[]
  apiKeys: Record<AIProvider, string>
  endpoints: Record<AIProvider, string>
}

export interface AIJob {
  id: ID
  type: 'analysis' | 'generation' | 'embedding' | 'chat'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  input: any
  output?: any
  model: string
  provider: AIProvider
  progress?: number
  error?: string
  createdAt: string
  updatedAt: string
  completedAt?: string
  usage?: {
    tokens: number
    cost: number
    duration: number
  }
}

// Smart Form Processing Types
export enum FormFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PHONE = 'phone',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  TEXTAREA = 'textarea',
  URL = 'url',
  CURRENCY = 'currency',
  PERCENTAGE = 'percentage'
}

export interface FormFieldDefinition {
  id: string
  name: string
  label: string
  type: FormFieldType
  required: boolean
  description?: string
  placeholder?: string
  defaultValue?: any
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
  options?: Array<{
    value: string
    label: string
  }>
  metadata?: Record<string, any>
}
