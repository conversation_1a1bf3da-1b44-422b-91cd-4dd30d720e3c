'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useWorkflows } from '@/hooks/workflows';
import { WorkflowCard } from '@/components/workflows/workflow-card';
import { EmptyState } from '@/components/empty-state';
import { GitBranch, Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useState } from 'react';

interface ProjectWorkflowsProps {
  projectId: string;
}

export function ProjectWorkflows({ projectId }: ProjectWorkflowsProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const {
    data: workflows = [],
    isLoading,
    error
  } = useWorkflows({ projectId });

  // Filter workflows by search query
  const filteredWorkflows = searchQuery
    ? workflows.filter(workflow =>
        workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        workflow.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : workflows;

  if (error) {
    return (
      <EmptyState
        icon={<GitBranch className="h-10 w-10 text-muted-foreground" />}
        title="Error loading workflows"
        description="There was a problem loading the workflows for this project."
        action={
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search workflows..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          onClick={() => router.push(`/projects/${projectId}/workflows/create`)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Create
        </Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-40 bg-muted rounded-md animate-pulse" />
          ))}
        </div>
      ) : filteredWorkflows.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredWorkflows.map((workflow) => (
            <div
              key={workflow.id}
              onClick={() => router.push(`/projects/${projectId}/workflows/${workflow.id}`)}
              className="cursor-pointer"
            >
              <WorkflowCard workflow={workflow as any} />
            </div>
          ))}
        </div>
      ) : (
        <EmptyState
          icon={<GitBranch className="h-10 w-10 text-muted-foreground" />}
          title={searchQuery ? "No matching workflows" : "No workflows yet"}
          description={
            searchQuery
              ? "Try a different search term or clear the search"
              : "Create your first workflow to get started"
          }
          action={
            <Button
              onClick={() => router.push(`/projects/${projectId}/workflows/create`)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Workflow
            </Button>
          }
        />
      )}
    </div>
  );
}
