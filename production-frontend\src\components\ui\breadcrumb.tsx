'use client';

import * as React from 'react';
import { ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface BreadcrumbProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode;
  separator?: React.ReactNode;
}

const Breadcrumb = React.forwardRef<HTMLElement, BreadcrumbProps>(
  ({ className, children, separator = <ChevronRight className="h-4 w-4" />, ...props }, ref) => {
    const childrenArray = React.Children.toArray(children);
    const childrenWithSeparators = childrenArray.map((child, index) => {
      if (index === childrenArray.length - 1) {
        return child;
      }
      return (
        <React.Fragment key={index}>
          {child}
          <li className="mx-2 text-muted-foreground">{separator}</li>
        </React.Fragment>
      );
    });

    return (
      <nav
        ref={ref}
        aria-label="Breadcrumb"
        className={cn('flex', className)}
        {...props}
      >
        <ol className="flex items-center">{childrenWithSeparators}</ol>
      </nav>
    );
  }
);
Breadcrumb.displayName = 'Breadcrumb';

interface BreadcrumbItemProps extends React.HTMLAttributes<HTMLLIElement> {
  children: React.ReactNode;
  isCurrent?: boolean;
}

const BreadcrumbItem = React.forwardRef<HTMLLIElement, BreadcrumbItemProps>(
  ({ className, children, isCurrent, ...props }, ref) => {
    return (
      <li
        ref={ref}
        className={cn('inline-flex items-center', className)}
        aria-current={isCurrent ? 'page' : undefined}
        {...props}
      >
        {children}
      </li>
    );
  }
);
BreadcrumbItem.displayName = 'BreadcrumbItem';

interface BreadcrumbLinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  children: React.ReactNode;
  asChild?: boolean;
  href: string;
}

const BreadcrumbLink = React.forwardRef<HTMLAnchorElement, BreadcrumbLinkProps>(
  ({ className, children, href, ...props }, ref) => {
    return (
      <Link
        ref={ref}
        href={href}
        className={cn('text-sm font-medium hover:underline', className)}
        {...props}
      >
        {children}
      </Link>
    );
  }
);
BreadcrumbLink.displayName = 'BreadcrumbLink';

export { Breadcrumb, BreadcrumbItem, BreadcrumbLink };
