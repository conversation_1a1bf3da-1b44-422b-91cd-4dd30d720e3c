"use client";

import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { useTeam, useUpdateTeam } from "@/hooks/teams";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft } from "lucide-react";
import { TeamForm } from "@/components/teams";
import { UpdateTeamDto } from "@/types/team";

export default function EditTeamPage() {
  const params = useParams();
  const router = useRouter();

  // Ensure params is not null
  if (!params) {
    return <div>Loading...</div>;
  }

  const organizationId = params.organizationId as string;
  const teamId = params.teamId as string;

  // Fetch team
  const { data: team, isLoading } = useTeam(teamId);

  // Update team mutation
  const updateTeamMutation = useUpdateTeam();

  // Handle form submission
  const handleUpdateTeam = (data: UpdateTeamDto) => {
    updateTeamMutation.mutate(
      {
        teamId,
        data: data,
      },
      {
        onSuccess: () => {
          router.push(`/organizations/${organizationId}/teams/${teamId}`);
        },
      }
    );
  };

  return (
    <div className="container mx-auto py-6 space-y-6 max-w-2xl">
      <div className="flex items-center gap-2">
        <Button variant="ghost" size="icon" asChild>
          <Link href={`/organizations/${organizationId}/teams/${teamId}`}>
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <h1 className="text-3xl font-bold tracking-tight">
          {isLoading ? <Skeleton className="h-9 w-40" /> : `Edit ${team?.name}`}
        </h1>
      </div>

      {isLoading ? (
        <Skeleton className="h-96 w-full" />
      ) : team ? (
        <div className="border rounded-lg p-6 bg-card">
          <TeamForm
            organizationId={organizationId}
            team={team as any}
            onSubmit={handleUpdateTeam}
            isSubmitting={updateTeamMutation.isPending}
          />
        </div>
      ) : (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold">Team not found</h2>
          <p className="text-muted-foreground mt-2">
            The team you're looking for doesn't exist or you don't have access to it.
          </p>
          <Button asChild className="mt-6">
            <Link href={`/organizations/${organizationId}/teams`}>
              Go back to teams
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
