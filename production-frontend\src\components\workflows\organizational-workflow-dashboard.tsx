/**
 * Organizational Workflow Dashboard
 * Comprehensive dashboard for managing organizational paperwork processing workflows
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Progress } from '../ui/progress';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { useToast } from '../../hooks/use-toast';
import { useOrganizationalWorkflows } from '../../hooks/workflow/useOrganizationalWorkflows';
import { useDigitalSignatures } from '../../hooks/signature/useDigitalSignatures';
import {
  FileText,
  Plus,
  Search,
  Filter,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Users,
  TrendingUp,
  Shield,
  Zap,
  BarChart3,
  Calendar,
  Download,
  Settings,
  Eye,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import {
  WorkflowCategory,
  Department,
  WorkflowStatus,
  ExecutionStatus,
  Priority,
  ComplianceFramework
} from '../../services/organizational-workflow-service';

interface OrganizationalWorkflowDashboardProps {
  className?: string;
}

export function OrganizationalWorkflowDashboard({ className }: OrganizationalWorkflowDashboardProps) {
  const { toast } = useToast();
  
  // Hooks
  const {
    workflows,
    executions,
    complianceDashboard,
    workflowsByCategory,
    workflowsByDepartment,
    executionsByStatus,
    activeExecutions,
    completedExecutions,
    failedExecutions,
    loadingWorkflows,
    loadingExecutions,
    loadingCompliance,
    workflowFilters,
    executionFilters,
    createWorkflow,
    executeWorkflow,
    classifyDocument,
    filterWorkflows,
    filterExecutions,
    refreshAll
  } = useOrganizationalWorkflows({ autoRefresh: true, refreshInterval: 30000 });

  const {
    pendingApprovals,
    urgentApprovals,
    overdueApprovals,
    loadingApprovals
  } = useDigitalSignatures({ autoRefresh: true });

  // Local state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<WorkflowCategory | 'all'>('all');
  const [selectedDepartment, setSelectedDepartment] = useState<Department | 'all'>('all');
  const [selectedStatus, setSelectedStatus] = useState<WorkflowStatus | 'all'>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Filter workflows based on search and filters
  const filteredWorkflows = workflows.filter(workflow => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         workflow.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || workflow.category === selectedCategory;
    const matchesDepartment = selectedDepartment === 'all' || workflow.department === selectedDepartment;
    const matchesStatus = selectedStatus === 'all' || workflow.status === selectedStatus;
    
    return matchesSearch && matchesCategory && matchesDepartment && matchesStatus;
  });

  // Apply filters when they change
  useEffect(() => {
    const filters = {
      ...(selectedCategory !== 'all' && { category: selectedCategory }),
      ...(selectedDepartment !== 'all' && { department: selectedDepartment }),
      ...(selectedStatus !== 'all' && { status: selectedStatus })
    };
    filterWorkflows(filters);
  }, [selectedCategory, selectedDepartment, selectedStatus, filterWorkflows]);

  // Statistics calculations
  const totalWorkflows = workflows.length;
  const activeWorkflowsCount = workflows.filter(w => w.status === WorkflowStatus.ACTIVE).length;
  const totalExecutions = executions.length;
  const successRate = totalExecutions > 0 ? (completedExecutions.length / totalExecutions) * 100 : 0;
  const complianceScore = complianceDashboard?.overview.complianceScore || 0;

  const getStatusColor = (status: WorkflowStatus | ExecutionStatus) => {
    switch (status) {
      case WorkflowStatus.ACTIVE:
      case ExecutionStatus.COMPLETED:
        return 'bg-green-500';
      case WorkflowStatus.DRAFT:
      case ExecutionStatus.PENDING:
        return 'bg-yellow-500';
      case WorkflowStatus.SUSPENDED:
      case ExecutionStatus.PAUSED:
        return 'bg-orange-500';
      case WorkflowStatus.ARCHIVED:
      case ExecutionStatus.CANCELLED:
        return 'bg-gray-500';
      case ExecutionStatus.FAILED:
        return 'bg-red-500';
      case ExecutionStatus.RUNNING:
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getPriorityColor = (priority: Priority) => {
    switch (priority) {
      case Priority.CRITICAL:
        return 'bg-red-500';
      case Priority.URGENT:
        return 'bg-orange-500';
      case Priority.HIGH:
        return 'bg-yellow-500';
      case Priority.NORMAL:
        return 'bg-blue-500';
      case Priority.LOW:
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Organizational Workflows</h1>
          <p className="text-muted-foreground">
            Manage paperwork processing, approvals, and compliance tracking
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={refreshAll}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Workflow
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl">
              <DialogHeader>
                <DialogTitle>Create Organizational Workflow</DialogTitle>
                <DialogDescription>
                  Set up a new workflow for organizational paperwork processing
                </DialogDescription>
              </DialogHeader>
              {/* Workflow creation form would go here */}
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalWorkflows}</div>
            <p className="text-xs text-muted-foreground">
              {activeWorkflowsCount} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Executions</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeExecutions.length}</div>
            <p className="text-xs text-muted-foreground">
              {totalExecutions} total executions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{successRate.toFixed(1)}%</div>
            <Progress value={successRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{complianceScore}%</div>
            <Progress value={complianceScore} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Notifications */}
      {(urgentApprovals.length > 0 || overdueApprovals.length > 0) && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center text-orange-800">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Attention Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {urgentApprovals.length > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-orange-700">
                    {urgentApprovals.length} urgent approval{urgentApprovals.length !== 1 ? 's' : ''} pending
                  </span>
                  <Button variant="outline" size="sm">
                    Review
                  </Button>
                </div>
              )}
              {overdueApprovals.length > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-orange-700">
                    {overdueApprovals.length} overdue approval{overdueApprovals.length !== 1 ? 's' : ''}
                  </span>
                  <Button variant="outline" size="sm">
                    Review
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
          <TabsTrigger value="executions">Executions</TabsTrigger>
          <TabsTrigger value="approvals">Approvals</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Recent Executions */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Executions</CardTitle>
                <CardDescription>Latest workflow executions</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-3">
                    {executions.slice(0, 10).map((execution) => (
                      <div key={execution.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${getStatusColor(execution.status)}`} />
                          <div>
                            <p className="font-medium text-sm">{execution.workflowName}</p>
                            <p className="text-xs text-muted-foreground">
                              {execution.documentIds.length} document{execution.documentIds.length !== 1 ? 's' : ''}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className={`${getPriorityColor(execution.priority)} text-white`}>
                            {execution.priority}
                          </Badge>
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Date(execution.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Pending Approvals */}
            <Card>
              <CardHeader>
                <CardTitle>Pending Approvals</CardTitle>
                <CardDescription>Approvals requiring your attention</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-3">
                    {pendingApprovals.slice(0, 10).map((approval) => (
                      <div key={approval.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium text-sm">{approval.requesterName}</p>
                            <p className="text-xs text-muted-foreground">
                              Due: {new Date(approval.dueDate).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className={`${getPriorityColor(approval.priority as unknown as Priority)} text-white`}>
                            {approval.priority}
                          </Badge>
                          <Button variant="outline" size="sm" className="mt-1">
                            Review
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="workflows" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search workflows..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as WorkflowCategory | 'all')}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {Object.values(WorkflowCategory).map((category) => (
                      <SelectItem key={category} value={category}>
                        {category.replace('_', ' ').toUpperCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedDepartment} onValueChange={(value) => setSelectedDepartment(value as Department | 'all')}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {Object.values(Department).map((department) => (
                      <SelectItem key={department} value={department}>
                        {department.replace('_', ' ').toUpperCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as WorkflowStatus | 'all')}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    {Object.values(WorkflowStatus).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status.replace('_', ' ').toUpperCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Workflows List */}
          <Card>
            <CardHeader>
              <CardTitle>Workflows ({filteredWorkflows.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredWorkflows.map((workflow) => (
                  <div key={workflow.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(workflow.status)}`} />
                      <div>
                        <h3 className="font-medium">{workflow.name}</h3>
                        <p className="text-sm text-muted-foreground">{workflow.description}</p>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge variant="outline">{workflow.category}</Badge>
                          <Badge variant="outline">{workflow.department}</Badge>
                          <span className="text-xs text-muted-foreground">
                            {workflow.statistics.totalExecutions} executions
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Play className="h-4 w-4 mr-2" />
                        Execute
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="executions" className="space-y-4">
          {/* Execution Status Overview */}
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Running</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {executionsByStatus[ExecutionStatus.RUNNING]?.length || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Completed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {executionsByStatus[ExecutionStatus.COMPLETED]?.length || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Failed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {executionsByStatus[ExecutionStatus.FAILED]?.length || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Pending</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {executionsByStatus[ExecutionStatus.PENDING]?.length || 0}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Executions List */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Executions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {executions.map((execution) => (
                  <div key={execution.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(execution.status)}`} />
                      <div>
                        <h3 className="font-medium">{execution.workflowName}</h3>
                        <p className="text-sm text-muted-foreground">
                          {execution.documentIds.length} documents • Level {execution.currentApprovalLevel}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          <Badge variant="outline" className={`${getPriorityColor(execution.priority)} text-white`}>
                            {execution.priority}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Started {new Date(execution.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      {execution.status === ExecutionStatus.RUNNING && (
                        <Button variant="outline" size="sm">
                          <Pause className="h-4 w-4 mr-2" />
                          Pause
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approvals" className="space-y-4">
          {/* Approval metrics and content would go here */}
          <Card>
            <CardHeader>
              <CardTitle>Approval Management</CardTitle>
              <CardDescription>Manage pending approvals and signature requests</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Approval management interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          {/* Compliance dashboard content */}
          <Card>
            <CardHeader>
              <CardTitle>Compliance Dashboard</CardTitle>
              <CardDescription>Monitor compliance status and violations</CardDescription>
            </CardHeader>
            <CardContent>
              {complianceDashboard ? (
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="text-center">
                      <div className="text-2xl font-bold">{complianceDashboard.overview.totalWorkflows}</div>
                      <p className="text-sm text-muted-foreground">Total Workflows</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{complianceDashboard.overview.compliantWorkflows}</div>
                      <p className="text-sm text-muted-foreground">Compliant</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">{complianceDashboard.overview.violationsCount}</div>
                      <p className="text-sm text-muted-foreground">Violations</p>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">Loading compliance data...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics content */}
          <Card>
            <CardHeader>
              <CardTitle>Workflow Analytics</CardTitle>
              <CardDescription>Performance metrics and insights</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Analytics dashboard coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
