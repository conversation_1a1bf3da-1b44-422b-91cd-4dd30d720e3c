"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Share,
  Copy,
  Link as LinkIcon,
  Mail,
  Globe,
  Lock,
  Users,
  X,
  Check,
  AlertCircle
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";


import { useDocumentSharing } from "@/hooks/documents/useDocumentSharing";

interface DocumentSharingProps {
  documentId: string;
}

export function DocumentSharing({ documentId }: DocumentSharingProps) {
  const [activeTab, setActiveTab] = useState("people");

  // Use the document sharing hook
  const {
    sharedUsers,
    isLoading,
    linkSharingSettings,
    shareEmail,
    setShareEmail,
    shareRole,
    setShareRole,
    isSubmitting,
    shareLink,
    linkCopied,
    handleShareWithUser,
    handleCopyLink,
    updateUserPermission,
    removeUser,
    updateLinkSettings
  } = useDocumentSharing(documentId);

  // Handle link sharing toggle
  const handleLinkSharingToggle = (enabled: boolean) => {
    updateLinkSettings({
      enabled,
      permission: linkSharingSettings?.permission || 'view'
    });
  };

  // Handle link permission change
  const handleLinkPermissionChange = (permission: string) => {
    updateLinkSettings({
      permission: permission as 'view' | 'comment' | 'edit'
    });
  };

  // Handle remove shared user
  const handleRemoveUser = (userId: string) => {
    removeUser(userId);
  };

  // Handle change user role
  const handleChangeUserRole = (userId: string, newRole: string) => {
    updateUserPermission(userId, newRole);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Document Sharing</CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-10 w-full mb-4" />
          <div className="space-y-4">
            {[1, 2, 3].map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Share className="h-5 w-5" />
          Share Document
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="people" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 w-full mb-6">
            <TabsTrigger value="people">People</TabsTrigger>
            <TabsTrigger value="link">Link Sharing</TabsTrigger>
          </TabsList>

          <TabsContent value="people">
            <div className="space-y-6">
              {/* Add people form */}
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Add people by email"
                    value={shareEmail}
                    onChange={(e) => setShareEmail(e.target.value)}
                  />
                </div>
                <Select
                  value={shareRole}
                  onValueChange={(value) => setShareRole(value as "viewer" | "commenter" | "editor")}
                >
                  <SelectTrigger className="w-full md:w-[150px]">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="viewer">Viewer</SelectItem>
                    <SelectItem value="commenter">Commenter</SelectItem>
                    <SelectItem value="editor">Editor</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleShareWithUser}
                  disabled={!shareEmail.trim() || isSubmitting}
                >
                  {isSubmitting ? "Sharing..." : "Share"}
                </Button>
              </div>

              {/* Shared users list */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">People with access</h3>

                {sharedUsers.length === 0 ? (
                  <div className="text-center py-8 border rounded-md">
                    <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">No users have access</p>
                    <p className="text-sm text-muted-foreground mt-1">Share this document with others</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {sharedUsers.map((user: any) => (
                      <div key={user.id} className="flex items-center gap-4 p-3 border rounded-md">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={user.avatarUrl || user.sharedWith?.[0]} alt={user.sharedWith?.[0] || 'User'} />
                          <AvatarFallback>
                            {(user.sharedWith?.[0] || 'U').split(" ").map((n: string) => n[0]).join("").toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-medium">{user.sharedWith?.[0] || 'Unknown User'}</p>
                          <p className="text-sm text-muted-foreground">{user.sharedWith?.[0] || 'No email'}</p>
                        </div>
                        <Select
                          value={user.permissions?.includes('write') ? 'editor' : 'viewer'}
                          onValueChange={(value) => handleChangeUserRole(user.id, value)}
                        >
                          <SelectTrigger className="w-[120px]">
                            <SelectValue placeholder="Select role" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="viewer">Viewer</SelectItem>
                            <SelectItem value="commenter">Commenter</SelectItem>
                            <SelectItem value="editor">Editor</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleRemoveUser(user.id)}
                          className="text-destructive"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="link">
            <div className="space-y-6">
              {/* Link sharing toggle */}
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="link-sharing">Link sharing</Label>
                  <p className="text-sm text-muted-foreground">
                    Anyone with the link can access this document
                  </p>
                </div>
                <Switch
                  id="link-sharing"
                  checked={linkSharingSettings?.enabled || false}
                  onCheckedChange={handleLinkSharingToggle}
                />
              </div>

              {/* Link sharing options */}
              {linkSharingSettings?.enabled && (
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Select
                        value={linkSharingSettings?.permission || 'view'}
                        onValueChange={handleLinkPermissionChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select permission" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="view">
                            <div className="flex items-center gap-2">
                              <Globe className="h-4 w-4" />
                              <span>Anyone with the link can view</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="comment">
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              <span>Anyone with the link can comment</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="edit">
                            <div className="flex items-center gap-2">
                              <Lock className="h-4 w-4" />
                              <span>Anyone with the link can edit</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Share link */}
                  <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                      <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        value={shareLink}
                        readOnly
                        className="pl-9 pr-24"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8"
                        onClick={handleCopyLink}
                      >
                        {linkCopied ? (
                          <>
                            <Check className="mr-2 h-4 w-4" />
                            Copied
                          </>
                        ) : (
                          <>
                            <Copy className="mr-2 h-4 w-4" />
                            Copy
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {/* Share buttons */}
                  <div className="flex gap-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="outline" size="icon">
                            <Mail className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Share via email</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <Button variant="outline" className="flex-1">
                      <Copy className="mr-2 h-4 w-4" />
                      Copy link
                    </Button>
                  </div>

                  {/* Security note */}
                  <div className="flex items-start gap-2 p-3 bg-muted/50 rounded-md">
                    <AlertCircle className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
                    <p className="text-sm text-muted-foreground">
                      Anyone with this link will be able to access this document with the selected permission level.
                      Be careful who you share this link with.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
