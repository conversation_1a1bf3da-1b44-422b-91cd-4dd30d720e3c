"use client";

import { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Calendar,
  Shield,
  Building,
  FolderKanban,
  Settings,
  Key,
  Bell,
  LogOut
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { getUserPermissions } from "@/lib/permissions";
import { useTenant } from "@/components/tenant-provider";
import { SystemRole } from "@/types/role";

export default function ProfilePage() {
  const { user, logout } = useAuth();
  const { currentTenant } = useTenant();
  const [activeTab, setActiveTab] = useState("overview");

  if (!user) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-4rem)]">
        <p>Loading user profile...</p>
      </div>
    );
  }

  // Get user's permissions
  const permissions = getUserPermissions(user);

  // Get user initials for avatar fallback
  const initials = `${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`;

  return (
    <div className="container py-8 max-w-5xl">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar */}
        <div className="w-full md:w-1/3 space-y-6">
          <Card>
            <CardHeader className="text-center">
              <Avatar className="w-24 h-24 mx-auto">
                <AvatarImage src={user.avatarUrl} alt={user.displayName} />
                <AvatarFallback className="text-2xl">{initials}</AvatarFallback>
              </Avatar>
              <CardTitle className="mt-4">{user.displayName}</CardTitle>
              <CardDescription className="flex items-center justify-center gap-2">
                <Mail className="h-4 w-4" />
                {user.email}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {user.firstName} {user.lastName}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Joined {formatDistanceToNow(new Date(user.createdAt))} ago
                  </span>
                </div>
                {currentTenant && (
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      Tenant: {(currentTenant as any).displayName || currentTenant.name}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" onClick={() => logout()}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">System Roles</CardTitle>
              <CardDescription>
                Your assigned roles in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {user.systemRoles && user.systemRoles.length > 0 ? (
                  user.systemRoles.map((role) => (
                    <Badge key={role} variant="secondary" className="mr-2 mb-2">
                      {role}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-muted-foreground">No system roles assigned</p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main content */}
        <div className="flex-1">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Overview</CardTitle>
                  <CardDescription>
                    Your account information and status
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium">Account Status</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        <Badge variant={(user.status as any) === 'ACTIVE' ? 'default' : 'secondary'}>
                          {user.status}
                        </Badge>
                      </p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium">Organizations</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {user.organizationIds && user.organizationIds.length > 0 ? (
                          <span>Member of {user.organizationIds.length} organization(s)</span>
                        ) : (
                          <span>Not a member of any organizations</span>
                        )}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium">Last Login</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {user.lastLoginAt ? (
                          formatDistanceToNow(new Date(user.lastLoginAt), { addSuffix: true })
                        ) : (
                          "Unknown"
                        )}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Role Summary</CardTitle>
                  <CardDescription>
                    Your roles and access levels
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {user.systemRoles?.includes(SystemRole.SYSTEM_ADMIN) && (
                      <div className="flex items-start gap-3">
                        <Shield className="h-5 w-5 text-primary mt-0.5" />
                        <div>
                          <h3 className="text-sm font-medium">System Administrator</h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            You have full access to all system features and resources
                          </p>
                        </div>
                      </div>
                    )}

                    {user.systemRoles?.includes(SystemRole.ORGANIZATION_ADMIN) && (
                      <div className="flex items-start gap-3">
                        <Building className="h-5 w-5 text-primary mt-0.5" />
                        <div>
                          <h3 className="text-sm font-medium">Organization Administrator</h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            You have full access to organization resources
                          </p>
                        </div>
                      </div>
                    )}

                    {user.systemRoles?.includes(SystemRole.PROJECT_ADMIN) && (
                      <div className="flex items-start gap-3">
                        <FolderKanban className="h-5 w-5 text-primary mt-0.5" />
                        <div>
                          <h3 className="text-sm font-medium">Project Administrator</h3>
                          <p className="text-sm text-muted-foreground mt-1">
                            You have full access to project resources
                          </p>
                        </div>
                      </div>
                    )}

                    {!user.systemRoles?.some(role =>
                      [SystemRole.SYSTEM_ADMIN, SystemRole.ORGANIZATION_ADMIN, SystemRole.PROJECT_ADMIN].includes(role as SystemRole)
                    ) && (
                      <p className="text-sm text-muted-foreground">
                        You have standard user access to the system
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="permissions" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Your Permissions</CardTitle>
                  <CardDescription>
                    Permissions granted by your roles
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {permissions.length > 0 ? (
                      <>
                        <div>
                          <h3 className="text-sm font-medium mb-2">Organization Permissions</h3>
                          <div className="flex flex-wrap gap-2">
                            {permissions
                              .filter(p => p.startsWith("VIEW_ORGANIZATION") || p.startsWith("MANAGE_ORGANIZATION"))
                              .map(permission => (
                                <Badge key={permission} variant="outline">
                                  {permission}
                                </Badge>
                              ))}
                          </div>
                        </div>

                        <Separator />

                        <div>
                          <h3 className="text-sm font-medium mb-2">Project Permissions</h3>
                          <div className="flex flex-wrap gap-2">
                            {permissions
                              .filter(p => p.includes("PROJECT"))
                              .map(permission => (
                                <Badge key={permission} variant="outline">
                                  {permission}
                                </Badge>
                              ))}
                          </div>
                        </div>

                        <Separator />

                        <div>
                          <h3 className="text-sm font-medium mb-2">Document Permissions</h3>
                          <div className="flex flex-wrap gap-2">
                            {permissions
                              .filter(p => p.includes("DOCUMENT"))
                              .map(permission => (
                                <Badge key={permission} variant="outline">
                                  {permission}
                                </Badge>
                              ))}
                          </div>
                        </div>
                      </>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        No specific permissions assigned
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>
                    Manage your account preferences
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button variant="outline" className="w-full justify-start">
                      <Settings className="mr-2 h-4 w-4" />
                      Edit Profile
                    </Button>

                    <Button variant="outline" className="w-full justify-start" onClick={() => window.location.href = `/api/auth/signin?callbackUrl=${encodeURIComponent(window.location.href)}`}>
                      <Key className="mr-2 h-4 w-4" />
                      Change Password
                    </Button>

                    <Button variant="outline" className="w-full justify-start" asChild>
                      <Link href="/profile/security">
                        <Shield className="mr-2 h-4 w-4" />
                        Security Settings
                      </Link>
                    </Button>

                    <Button variant="outline" className="w-full justify-start">
                      <Bell className="mr-2 h-4 w-4" />
                      Notification Preferences
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
