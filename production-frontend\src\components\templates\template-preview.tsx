"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Eye, 
  FileText, 
  Download, 
  Printer, 
  RefreshCw, 
  Calendar, 
  User, 
  Building, 
  FolderKanban 
} from "lucide-react";
import { Template, TemplatePreviewResult } from "@/services/template-service";
import { cn } from "@/lib/utils";

interface TemplatePreviewProps {
  template: Template;
  previewResult?: TemplatePreviewResult;
  isLoading?: boolean;
  onPreview?: (values: Record<string, any>) => void;
  className?: string;
}

export function TemplatePreview({ 
  template, 
  previewResult, 
  isLoading = false,
  onPreview,
  className 
}: TemplatePreviewProps) {
  const [activeTab, setActiveTab] = useState("preview");
  const [previewValues, setPreviewValues] = useState<Record<string, any>>({
    user: {
      name: "John Doe",
      email: "<EMAIL>",
      role: "Member"
    },
    organization: {
      name: "Acme Corp",
      address: "123 Main St, Anytown, USA",
      phone: "+****************"
    },
    project: {
      name: "Website Redesign",
      description: "Redesign of the company website",
      status: "In Progress"
    },
    date: new Date().toLocaleDateString(),
    "date.short": new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
    "date.long": new Date().toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })
  });
  
  // Handle preview
  const handlePreview = () => {
    if (onPreview) {
      onPreview(previewValues);
    }
  };
  
  // Update preview values
  const updatePreviewValue = (path: string, value: any) => {
    const parts = path.split('.');
    const newValues = { ...previewValues };
    
    let current = newValues;
    for (let i = 0; i < parts.length - 1; i++) {
      if (!current[parts[i]]) {
        current[parts[i]] = {};
      }
      current = current[parts[i]];
    }
    
    current[parts[parts.length - 1]] = value;
    setPreviewValues(newValues);
  };
  
  return (
    <div className={cn("space-y-6", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="preview" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="variables" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Variables
            </TabsTrigger>
          </TabsList>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={handlePreview}
              disabled={isLoading}
            >
              <RefreshCw className={cn(
                "mr-2 h-4 w-4",
                isLoading && "animate-spin"
              )} />
              Refresh Preview
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              disabled={!previewResult}
              onClick={() => {
                // In a real app, you would download the preview
                console.log("Download preview");
              }}
            >
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              disabled={!previewResult}
              onClick={() => {
                // In a real app, you would print the preview
                console.log("Print preview");
              }}
            >
              <Printer className="mr-2 h-4 w-4" />
              Print
            </Button>
          </div>
        </div>
        
        <TabsContent value="preview" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Template Preview</CardTitle>
              <CardDescription>
                Preview of the template with sample data
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-4/5" />
                </div>
              ) : previewResult ? (
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: previewResult.content || previewResult.html || '' }}
                />
              ) : (
                <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
                  <Eye className="h-8 w-8 text-muted-foreground mb-2" />
                  <p className="text-muted-foreground text-center">
                    Click "Refresh Preview" to see a preview of the template with sample data.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="variables" className="mt-0">
          <Card>
            <CardHeader>
              <CardTitle>Template Variables</CardTitle>
              <CardDescription>
                Customize the values used in the preview
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-sm font-medium flex items-center gap-2 mb-3">
                  <User className="h-4 w-4" />
                  User Variables
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="user-name">Name</Label>
                    <Input 
                      id="user-name" 
                      value={previewValues.user.name}
                      onChange={(e) => updatePreviewValue('user.name', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="user-email">Email</Label>
                    <Input 
                      id="user-email" 
                      value={previewValues.user.email}
                      onChange={(e) => updatePreviewValue('user.email', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="user-role">Role</Label>
                    <Input 
                      id="user-role" 
                      value={previewValues.user.role}
                      onChange={(e) => updatePreviewValue('user.role', e.target.value)}
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium flex items-center gap-2 mb-3">
                  <Building className="h-4 w-4" />
                  Organization Variables
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="org-name">Name</Label>
                    <Input 
                      id="org-name" 
                      value={previewValues.organization.name}
                      onChange={(e) => updatePreviewValue('organization.name', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="org-address">Address</Label>
                    <Input 
                      id="org-address" 
                      value={previewValues.organization.address}
                      onChange={(e) => updatePreviewValue('organization.address', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="org-phone">Phone</Label>
                    <Input 
                      id="org-phone" 
                      value={previewValues.organization.phone}
                      onChange={(e) => updatePreviewValue('organization.phone', e.target.value)}
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium flex items-center gap-2 mb-3">
                  <FolderKanban className="h-4 w-4" />
                  Project Variables
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="project-name">Name</Label>
                    <Input 
                      id="project-name" 
                      value={previewValues.project.name}
                      onChange={(e) => updatePreviewValue('project.name', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="project-description">Description</Label>
                    <Input 
                      id="project-description" 
                      value={previewValues.project.description}
                      onChange={(e) => updatePreviewValue('project.description', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="project-status">Status</Label>
                    <Input 
                      id="project-status" 
                      value={previewValues.project.status}
                      onChange={(e) => updatePreviewValue('project.status', e.target.value)}
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium flex items-center gap-2 mb-3">
                  <Calendar className="h-4 w-4" />
                  Date Variables
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="date">Date</Label>
                    <Input 
                      id="date" 
                      value={previewValues.date}
                      onChange={(e) => updatePreviewValue('date', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="date-short">Short Date</Label>
                    <Input 
                      id="date-short" 
                      value={previewValues["date.short"]}
                      onChange={(e) => updatePreviewValue('date.short', e.target.value)}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="date-long">Long Date</Label>
                    <Input 
                      id="date-long" 
                      value={previewValues["date.long"]}
                      onChange={(e) => updatePreviewValue('date.long', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={handlePreview}
                disabled={isLoading}
              >
                <RefreshCw className={cn(
                  "mr-2 h-4 w-4",
                  isLoading && "animate-spin"
                )} />
                Update Preview
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
