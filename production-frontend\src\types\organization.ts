/**
 * Organization Types
 * Types and interfaces for organization management
 */

import type { ID, Timestamp, User } from './index'

export interface Organization {
  id: ID
  name: string
  displayName: string
  description?: string
  logo?: string
  website?: string
  industry?: string
  size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
  location?: {
    country: string
    state?: string
    city?: string
    address?: string
    timezone?: string
  }
  settings: OrganizationSettings
  subscription?: OrganizationSubscription
  status: OrganizationStatus
  metadata?: Record<string, any>
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: ID
  ownerId: ID

  // Additional properties for compatibility with backend types
  tier?: 'FREE' | 'PROFESSIONAL' | 'ENTERPRISE'
  storageUsed?: number
  projectIds?: string[]
  memberIds?: string[]
}

export enum OrganizationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
  ARCHIVED = 'archived'
}

export enum OrganizationTier {
  FREE = 'free',
  BASIC = 'basic',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise'
}

export interface OrganizationSettings {
  allowPublicProjects: boolean
  requireApprovalForMembers: boolean
  defaultMemberRole: string
  maxMembers?: number
  maxProjects?: number
  maxStorage?: number // in bytes
  maxFileSize?: number
  features: string[] | {
    aiAnalysis?: boolean
    [key: string]: any
  }
  branding: OrganizationBranding
  security: OrganizationSecurity
  integrations: OrganizationIntegrations
}

export interface OrganizationBranding {
  primaryColor?: string
  secondaryColor?: string
  logo?: string
  favicon?: string
  customDomain?: string
  emailTemplate?: string
}

export interface OrganizationSecurity {
  mfaRequired: boolean
  ssoEnabled: boolean
  ssoProvider?: string
  ssoConfig?: Record<string, any>
  ipWhitelist?: string[]
  sessionTimeout: number // in minutes
  passwordPolicy: {
    minLength: number
    requireUppercase: boolean
    requireLowercase: boolean
    requireNumbers: boolean
    requireSpecialChars: boolean
    maxAge?: number // in days
  }
}

export interface OrganizationIntegrations {
  slack?: {
    enabled: boolean
    webhookUrl?: string
    channels?: string[]
  }
  teams?: {
    enabled: boolean
    webhookUrl?: string
  }
  email?: {
    enabled: boolean
    provider: string
    config: Record<string, any>
  }
  storage?: {
    provider: string
    config: Record<string, any>
  }
}

export interface OrganizationSubscription {
  plan: string
  status: 'active' | 'inactive' | 'cancelled' | 'past_due' | 'trialing'
  currentPeriodStart: Timestamp
  currentPeriodEnd: Timestamp
  cancelAtPeriodEnd: boolean
  trialEnd?: Timestamp
  seats: number
  usedSeats: number
  billingEmail?: string
  paymentMethod?: {
    type: string
    last4?: string
    brand?: string
    expiryMonth?: number
    expiryYear?: number
  }
}

export interface OrganizationMember {
  id: ID
  organizationId: ID
  userId: ID
  user: User
  role: string
  permissions: string[]
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  joinedAt: Timestamp
  invitedBy?: ID
  lastActiveAt?: Timestamp
  metadata?: Record<string, any>

  // Additional properties for compatibility
  name?: string
  email?: string
}

export interface OrganizationInvitation {
  id: ID
  organizationId: ID
  email: string
  role: string
  permissions?: string[]
  invitedBy: ID
  invitedAt: Timestamp
  expiresAt: Timestamp
  acceptedAt?: Timestamp
  status: 'pending' | 'accepted' | 'expired' | 'cancelled'
  message?: string
  metadata?: Record<string, any>
}

export interface OrganizationRole {
  id: ID
  organizationId: ID
  name: string
  displayName: string
  description?: string
  permissions: string[]
  isDefault: boolean
  isCustom: boolean
  createdAt: Timestamp
  updatedAt: Timestamp
  createdBy: ID
}

export interface CreateOrganizationRequest {
  name: string
  displayName: string
  description?: string
  industry?: string
  size?: Organization['size']
  website?: string
  location?: Organization['location']
  settings?: Partial<OrganizationSettings>
  metadata?: Record<string, any>
}

export interface UpdateOrganizationRequest {
  name?: string
  displayName?: string
  description?: string
  logo?: string
  website?: string
  industry?: string
  size?: Organization['size']
  location?: Partial<Organization['location']>
  settings?: Partial<OrganizationSettings>
  metadata?: Record<string, any>
}

export interface OrganizationUsage {
  organizationId: ID
  members: {
    total: number
    active: number
    limit?: number
  }
  projects: {
    total: number
    active: number
    limit?: number
  }
  storage: {
    used: number // in bytes
    limit?: number // in bytes
  }
  apiCalls: {
    current: number
    limit?: number
    resetDate: Timestamp
  }
  lastUpdated: Timestamp
}

export interface OrganizationAnalytics {
  organizationId: ID
  period: {
    start: Timestamp
    end: Timestamp
  }
  members: {
    total: number
    new: number
    active: number
    retention: number
  }
  projects: {
    total: number
    new: number
    active: number
    completed: number
  }
  documents: {
    total: number
    processed: number
    storage: number
  }
  activity: {
    logins: number
    uploads: number
    downloads: number
    collaborations: number
  }
}

export interface OrganizationAuditLog {
  id: ID
  organizationId: ID
  userId?: ID
  action: string
  resource: string
  resourceId?: ID
  changes?: Record<string, {
    old: any
    new: any
  }>
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  timestamp: Timestamp
}

export interface OrganizationFilter {
  status?: OrganizationStatus
  industry?: string
  size?: Organization['size']
  search?: string
  createdAfter?: Timestamp
  createdBefore?: Timestamp
  hasCustomDomain?: boolean
  subscriptionStatus?: string
}

export interface OrganizationSearchResult {
  organizations: Organization[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

// Organization context for components
export interface OrganizationContext {
  currentOrganization: Organization | null
  organizations: Organization[]
  switchOrganization: (organizationId: ID) => Promise<void>
  updateOrganization: (updates: UpdateOrganizationRequest) => Promise<void>
  inviteMember: (email: string, role: string, permissions?: string[]) => Promise<void>
  removeMember: (userId: ID) => Promise<void>
  isLoading: boolean
  error: string | null
}

// Organization permissions
export const ORGANIZATION_PERMISSIONS = {
  // General
  VIEW_ORGANIZATION: 'organization:view',
  EDIT_ORGANIZATION: 'organization:edit',
  DELETE_ORGANIZATION: 'organization:delete',
  
  // Members
  VIEW_MEMBERS: 'organization:members:view',
  INVITE_MEMBERS: 'organization:members:invite',
  EDIT_MEMBERS: 'organization:members:edit',
  REMOVE_MEMBERS: 'organization:members:remove',
  
  // Projects
  VIEW_PROJECTS: 'organization:projects:view',
  CREATE_PROJECTS: 'organization:projects:create',
  EDIT_PROJECTS: 'organization:projects:edit',
  DELETE_PROJECTS: 'organization:projects:delete',
  
  // Settings
  VIEW_SETTINGS: 'organization:settings:view',
  EDIT_SETTINGS: 'organization:settings:edit',
  MANAGE_BILLING: 'organization:billing:manage',
  MANAGE_INTEGRATIONS: 'organization:integrations:manage',
  
  // Security
  MANAGE_SECURITY: 'organization:security:manage',
  VIEW_AUDIT_LOGS: 'organization:audit:view',
  MANAGE_ROLES: 'organization:roles:manage',
  
  // Analytics
  VIEW_ANALYTICS: 'organization:analytics:view',
  EXPORT_DATA: 'organization:data:export',
} as const

// Default organization roles
export const DEFAULT_ORGANIZATION_ROLES = {
  OWNER: {
    name: 'owner',
    displayName: 'Owner',
    description: 'Full access to the organization',
    permissions: Object.values(ORGANIZATION_PERMISSIONS)
  },
  ADMIN: {
    name: 'admin',
    displayName: 'Administrator',
    description: 'Administrative access to the organization',
    permissions: [
      ORGANIZATION_PERMISSIONS.VIEW_ORGANIZATION,
      ORGANIZATION_PERMISSIONS.EDIT_ORGANIZATION,
      ORGANIZATION_PERMISSIONS.VIEW_MEMBERS,
      ORGANIZATION_PERMISSIONS.INVITE_MEMBERS,
      ORGANIZATION_PERMISSIONS.EDIT_MEMBERS,
      ORGANIZATION_PERMISSIONS.REMOVE_MEMBERS,
      ORGANIZATION_PERMISSIONS.VIEW_PROJECTS,
      ORGANIZATION_PERMISSIONS.CREATE_PROJECTS,
      ORGANIZATION_PERMISSIONS.EDIT_PROJECTS,
      ORGANIZATION_PERMISSIONS.DELETE_PROJECTS,
      ORGANIZATION_PERMISSIONS.VIEW_SETTINGS,
      ORGANIZATION_PERMISSIONS.EDIT_SETTINGS,
      ORGANIZATION_PERMISSIONS.MANAGE_INTEGRATIONS,
      ORGANIZATION_PERMISSIONS.VIEW_ANALYTICS,
    ]
  },
  MEMBER: {
    name: 'member',
    displayName: 'Member',
    description: 'Standard member access',
    permissions: [
      ORGANIZATION_PERMISSIONS.VIEW_ORGANIZATION,
      ORGANIZATION_PERMISSIONS.VIEW_MEMBERS,
      ORGANIZATION_PERMISSIONS.VIEW_PROJECTS,
      ORGANIZATION_PERMISSIONS.CREATE_PROJECTS,
      ORGANIZATION_PERMISSIONS.EDIT_PROJECTS,
    ]
  },
  VIEWER: {
    name: 'viewer',
    displayName: 'Viewer',
    description: 'Read-only access',
    permissions: [
      ORGANIZATION_PERMISSIONS.VIEW_ORGANIZATION,
      ORGANIZATION_PERMISSIONS.VIEW_MEMBERS,
      ORGANIZATION_PERMISSIONS.VIEW_PROJECTS,
    ]
  }
} as const
