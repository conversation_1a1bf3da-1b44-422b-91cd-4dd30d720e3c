/**
 * Enhanced Caching Service
 * Multi-layer caching with TTL, LRU eviction, and persistence
 */

interface CacheItem<T> {
  value: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum number of items
  persistent?: boolean // Whether to persist to localStorage
  namespace?: string // Namespace for localStorage keys
}

class EnhancedCache<T = any> {
  private cache = new Map<string, CacheItem<T>>()
  private options: Required<CacheOptions>
  private accessOrder: string[] = []

  constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: options.ttl || 5 * 60 * 1000, // 5 minutes default
      maxSize: options.maxSize || 100,
      persistent: options.persistent || false,
      namespace: options.namespace || 'cache',
    }

    if (this.options.persistent) {
      this.loadFromStorage()
    }

    // Cleanup expired items periodically
    setInterval(() => this.cleanup(), 60000) // Every minute
  }

  set(key: string, value: T, ttl?: number): void {
    const now = Date.now()
    const itemTtl = ttl || this.options.ttl

    // Remove from access order if exists
    this.removeFromAccessOrder(key)

    // Add to front of access order
    this.accessOrder.unshift(key)

    // Set the item
    this.cache.set(key, {
      value,
      timestamp: now,
      ttl: itemTtl,
      accessCount: 1,
      lastAccessed: now,
    })

    // Enforce max size with LRU eviction
    if (this.cache.size > this.options.maxSize) {
      this.evictLRU()
    }

    if (this.options.persistent) {
      this.saveToStorage()
    }
  }

  get(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    const now = Date.now()

    // Check if expired
    if (now - item.timestamp > item.ttl) {
      this.delete(key)
      return null
    }

    // Update access info
    item.accessCount++
    item.lastAccessed = now

    // Move to front of access order
    this.removeFromAccessOrder(key)
    this.accessOrder.unshift(key)

    return item.value
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): boolean {
    this.removeFromAccessOrder(key)
    const deleted = this.cache.delete(key)
    
    if (deleted && this.options.persistent) {
      this.saveToStorage()
    }
    
    return deleted
  }

  clear(): void {
    this.cache.clear()
    this.accessOrder = []
    
    if (this.options.persistent) {
      this.clearStorage()
    }
  }

  size(): number {
    return this.cache.size
  }

  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  values(): T[] {
    return Array.from(this.cache.values()).map(item => item.value)
  }

  // Get cache statistics
  getStats() {
    const items = Array.from(this.cache.values())
    const now = Date.now()
    
    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      hitRate: this.calculateHitRate(),
      averageAge: items.reduce((sum, item) => sum + (now - item.timestamp), 0) / items.length || 0,
      totalAccesses: items.reduce((sum, item) => sum + item.accessCount, 0),
      expiredItems: items.filter(item => now - item.timestamp > item.ttl).length,
    }
  }

  private removeFromAccessOrder(key: string): void {
    const index = this.accessOrder.indexOf(key)
    if (index > -1) {
      this.accessOrder.splice(index, 1)
    }
  }

  private evictLRU(): void {
    if (this.accessOrder.length === 0) return
    
    const lruKey = this.accessOrder.pop()
    if (lruKey) {
      this.cache.delete(lruKey)
    }
  }

  private cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
  }

  private calculateHitRate(): number {
    // This would need to be tracked separately in a real implementation
    return 0.85 // Placeholder
  }

  private loadFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const stored = localStorage.getItem(`${this.options.namespace}_cache`)
      if (stored) {
        const data = JSON.parse(stored)
        const now = Date.now()

        for (const [key, item] of Object.entries(data.cache)) {
          const cacheItem = item as CacheItem<T>
          // Only load non-expired items
          if (now - cacheItem.timestamp < cacheItem.ttl) {
            this.cache.set(key, cacheItem)
          }
        }

        this.accessOrder = data.accessOrder || []
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error)
    }
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const data = {
        cache: Object.fromEntries(this.cache.entries()),
        accessOrder: this.accessOrder,
      }
      localStorage.setItem(`${this.options.namespace}_cache`, JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to save cache to storage:', error)
    }
  }

  private clearStorage(): void {
    if (typeof window === 'undefined') return

    try {
      localStorage.removeItem(`${this.options.namespace}_cache`)
    } catch (error) {
      console.warn('Failed to clear cache storage:', error)
    }
  }
}

// Create default cache instances
export const memoryCache = new EnhancedCache({
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
  persistent: false,
  namespace: 'memory',
})

export const persistentCache = new EnhancedCache({
  ttl: 30 * 60 * 1000, // 30 minutes
  maxSize: 50,
  persistent: true,
  namespace: 'persistent',
})

export const sessionCache = new EnhancedCache({
  ttl: 60 * 60 * 1000, // 1 hour
  maxSize: 200,
  persistent: true,
  namespace: 'session',
})

// Cache decorators and utilities
export function cached<T extends (...args: any[]) => any>(
  fn: T,
  options: { cache?: EnhancedCache; ttl?: number; keyGenerator?: (...args: Parameters<T>) => string } = {}
): T {
  const cache = options.cache || memoryCache
  const keyGenerator = options.keyGenerator || ((...args) => JSON.stringify(args))

  return ((...args: Parameters<T>) => {
    const key = `${fn.name}_${keyGenerator(...args)}`
    
    // Try to get from cache first
    const cached = cache.get(key)
    if (cached !== null) {
      return cached
    }

    // Execute function and cache result
    const result = fn(...args)
    
    // Handle promises
    if (result instanceof Promise) {
      return result.then(value => {
        cache.set(key, value, options.ttl)
        return value
      })
    }

    cache.set(key, result, options.ttl)
    return result
  }) as T
}

export { EnhancedCache }
