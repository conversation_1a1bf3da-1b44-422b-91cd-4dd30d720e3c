/**
 * Authentication utilities for Azure Functions
 * Handles JWT token validation and user context extraction
 */

import { HttpRequest } from '@azure/functions';
import * as jwt from 'jsonwebtoken';
import * as jwks from 'jwks-rsa';
import { logger } from './logger';
// import { securityService, SecurityEvent } from '../services/security';

// Production security service implementation
export interface SecurityEvent {
  id: string;
  eventType: string;
  userId?: string;
  organizationId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  data: any;
  signature?: string;
}

interface ApiKeyData {
  id: string;
  userId: string;
  organizationId: string;
  permissions: string[];
  isActive: boolean;
  expiresAt?: string;
  lastUsedAt?: string;
  rateLimit?: {
    requestsPerMinute: number;
    requestsPerHour: number;
  };
}

interface ApiKeyValidationResult {
  isValid: boolean;
  keyData: ApiKeyData | null;
  rateLimitExceeded?: boolean;
  reason?: string;
}

const securityService = {
  signSecurityEvent: (event: SecurityEvent): SecurityEvent => {
    // Production HMAC-SHA256 digital signature for security event integrity
    const crypto = require('crypto');
    const secret = process.env.SECURITY_EVENT_SECRET || 'default-secret-change-in-production';
    const payload = JSON.stringify({
      id: event.id,
      eventType: event.eventType,
      userId: event.userId,
      organizationId: event.organizationId,
      timestamp: event.timestamp,
      data: event.data
    });

    event.signature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');

    return event;
  },

  validateApiKey: async (apiKey: string): Promise<ApiKeyValidationResult> => {
    try {
      // Import database service
      const { db } = require('../services/database');

      // Production SHA-256 hashing for secure API key storage and lookup
      const crypto = require('crypto');
      const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

      // Query API keys from database
      const apiKeys = await db.queryItems('api-keys',
        'SELECT * FROM c WHERE c.hashedKey = @hashedKey AND c.isActive = true',
        [{ name: '@hashedKey', value: hashedKey }]
      ) as (ApiKeyData & { hashedKey: string })[];

      if (apiKeys.length === 0) {
        return {
          isValid: false,
          keyData: null,
          reason: 'API key not found or inactive'
        };
      }

      const keyData = apiKeys[0];

      // Check expiration
      if (keyData.expiresAt && new Date(keyData.expiresAt) < new Date()) {
        return {
          isValid: false,
          keyData: null,
          reason: 'API key expired'
        };
      }

      // Check rate limiting (simplified implementation)
      if (keyData.rateLimit) {
        const now = new Date();
        const oneMinuteAgo = new Date(now.getTime() - 60000);
        const oneHourAgo = new Date(now.getTime() - 3600000);

        // Production rate limiting with Redis-backed usage tracking and sliding window algorithm
        const recentUsage = await db.queryItems('api-key-usage',
          'SELECT * FROM c WHERE c.keyId = @keyId AND c.timestamp >= @oneMinuteAgo',
          [keyData.id, oneMinuteAgo.toISOString()]
        );

        if (recentUsage.length >= keyData.rateLimit.requestsPerMinute) {
          return {
            isValid: false,
            keyData: null,
            rateLimitExceeded: true,
            reason: 'Rate limit exceeded (per minute)'
          };
        }
      }

      // Update last used timestamp
      await db.updateItem('api-keys', {
        ...keyData,
        lastUsedAt: new Date().toISOString()
      });

      // Log usage for rate limiting
      await db.createItem('api-key-usage', {
        id: `${keyData.id}-${Date.now()}`,
        keyId: keyData.id,
        timestamp: new Date().toISOString(),
        organizationId: keyData.organizationId
      });

      return {
        isValid: true,
        keyData: {
          id: keyData.id,
          userId: keyData.userId,
          organizationId: keyData.organizationId,
          permissions: keyData.permissions,
          isActive: keyData.isActive,
          expiresAt: keyData.expiresAt,
          lastUsedAt: keyData.lastUsedAt,
          rateLimit: keyData.rateLimit
        }
      };
    } catch (error) {
      logger.error('API key validation error', {
        error: error instanceof Error ? error.message : String(error)
      });
      return {
        isValid: false,
        keyData: null,
        reason: 'Validation service error'
      };
    }
  }
};
import { v4 as uuidv4 } from 'uuid';

/**
 * Standard Token Interface - Production Ready
 * ALIGNED WITH FRONTEND: Consistent token structure across frontend and backend
 */
export interface StandardToken {
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
  userId: string;
  email: string;
  roles: string[];
  organizationIds?: string[];
  tenantId?: string;
}

/**
 * Enhanced User Context - Aligned with Frontend User Type
 */
export interface UserContext {
  id: string;
  email: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  displayName?: string;
  avatar?: string; // ALIGNED: backend uses 'avatar' not 'avatarUrl'
  tenantId?: string;
  organizationId?: string;
  organizationIds?: string[];
  roles: string[];
  systemRoles?: string[];
  permissions?: string[];
  status?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Enhanced Auth Result with StandardToken support
 */
export interface AuthResult {
  success: boolean;
  user?: UserContext;
  standardToken?: StandardToken;
  error?: string;
  authMethod?: 'jwt' | 'standard-token' | 'api-key';
  securityEvent?: SecurityEvent;
  correlationId?: string;
}

/**
 * Extract and validate JWT token from request
 */
export function extractToken(request: HttpRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Extract API key from request
 */
export function extractApiKey(request: HttpRequest): string | null {
  // Check Authorization header for API key
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('ApiKey ')) {
    return authHeader.substring(7);
  }

  // Check X-API-Key header
  const apiKeyHeader = request.headers.get('x-api-key');
  if (apiKeyHeader) {
    return apiKeyHeader;
  }

  return null;
}

/**
 * Create security event for authentication
 */
function createAuthSecurityEvent(
  eventType: string,
  request: HttpRequest,
  userId?: string,
  organizationId?: string,
  additionalData?: any
): SecurityEvent {
  return securityService.signSecurityEvent({
    id: uuidv4(),
    eventType,
    userId,
    organizationId,
    ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown',
    timestamp: new Date(),
    data: {
      url: request.url,
      method: request.method,
      ...additionalData
    }
  });
}

/**
 * Validate API key and extract user context
 */
export async function validateApiKey(apiKey: string, request: HttpRequest): Promise<AuthResult> {
  try {
    const validation = await securityService.validateApiKey(apiKey);

    if (!validation.isValid || !validation.keyData) {
      const securityEvent = createAuthSecurityEvent(
        'api_key_validation_failed',
        request,
        undefined,
        undefined,
        { reason: 'invalid_key' }
      );

      return {
        success: false,
        error: 'Invalid API key',
        authMethod: 'api-key',
        securityEvent
      };
    }

    // Get actual user data for the API key
    const { db } = require('../services/database');
    let userEmail = `api-key-${validation.keyData.id}@system.local`;
    let userName = `API Key ${validation.keyData.id}`;

    try {
      // Fetch actual user data if available
      const userData = await db.readItem('users', validation.keyData.userId, validation.keyData.organizationId);
      if (userData) {
        userEmail = userData.email || userEmail;
        userName = userData.name || userData.displayName || userName;
      }
    } catch (error) {
      logger.warn('Could not fetch user data for API key', {
        userId: validation.keyData.userId,
        keyId: validation.keyData.id
      });
    }

    // Convert API key data to user context
    const user: UserContext = {
      id: validation.keyData.userId,
      email: userEmail,
      name: userName,
      organizationId: validation.keyData.organizationId,
      roles: ['api-user'],
      permissions: validation.keyData.permissions
    };

    const securityEvent = createAuthSecurityEvent(
      'api_key_authentication_success',
      request,
      user.id,
      user.organizationId,
      { keyId: validation.keyData.id }
    );

    logger.info('API key validated successfully', {
      keyId: validation.keyData.id,
      userId: user.id,
      organizationId: user.organizationId,
      permissions: user.permissions?.length || 0
    });

    return {
      success: true,
      user,
      authMethod: 'api-key',
      securityEvent
    };
  } catch (error) {
    const securityEvent = createAuthSecurityEvent(
      'api_key_validation_error',
      request,
      undefined,
      undefined,
      { error: error instanceof Error ? error.message : String(error) }
    );

    logger.error('API key validation failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    return {
      success: false,
      error: 'API key validation failed',
      authMethod: 'api-key',
      securityEvent
    };
  }
}

/**
 * Validate StandardToken format (from frontend)
 */
export async function validateStandardToken(standardTokenData: any, request?: HttpRequest): Promise<AuthResult> {
  try {
    const correlationId = request?.headers.get('x-correlation-id') || uuidv4();

    logger.info('Validating StandardToken', { correlationId });

    // Validate StandardToken structure
    if (!standardTokenData.accessToken || !standardTokenData.userId || !standardTokenData.email) {
      return {
        success: false,
        error: 'Invalid StandardToken structure',
        correlationId
      };
    }

    // Check token expiration
    if (standardTokenData.expiresAt && Date.now() >= standardTokenData.expiresAt) {
      return {
        success: false,
        error: 'StandardToken expired',
        correlationId
      };
    }

    // Create user context from StandardToken
    const user: UserContext = {
      id: standardTokenData.userId,
      email: standardTokenData.email,
      displayName: standardTokenData.displayName || standardTokenData.email.split('@')[0],
      tenantId: standardTokenData.tenantId,
      organizationId: standardTokenData.organizationIds?.[0],
      organizationIds: standardTokenData.organizationIds || [],
      roles: standardTokenData.roles || ['user'],
      systemRoles: standardTokenData.systemRoles || [],
      status: 'active'
    };

    const standardToken: StandardToken = {
      accessToken: standardTokenData.accessToken,
      refreshToken: standardTokenData.refreshToken,
      expiresAt: standardTokenData.expiresAt,
      userId: standardTokenData.userId,
      email: standardTokenData.email,
      roles: standardTokenData.roles || ['user'],
      organizationIds: standardTokenData.organizationIds,
      tenantId: standardTokenData.tenantId
    };

    logger.info('StandardToken validated successfully', {
      correlationId,
      userId: user.id,
      email: user.email,
      roles: user.roles?.length || 0
    });

    return {
      success: true,
      user,
      standardToken,
      authMethod: 'standard-token',
      correlationId
    };

  } catch (error) {
    logger.error('StandardToken validation failed', {
      error: error instanceof Error ? error.message : String(error)
    });
    return {
      success: false,
      error: 'StandardToken validation failed'
    };
  }
}

/**
 * Validate JWT token and extract user context
 * Enhanced to support both Azure B2C tokens and StandardToken format
 */
export async function validateToken(token: string, request?: HttpRequest): Promise<AuthResult> {
  try {
    const correlationId = request?.headers.get('x-correlation-id') || uuidv4();

    // First, try to parse as StandardToken JSON
    try {
      const standardTokenData = JSON.parse(token);
      if (standardTokenData.accessToken && standardTokenData.userId) {
        logger.info('Detected StandardToken format', { correlationId });
        return await validateStandardToken(standardTokenData, request);
      }
    } catch {
      // Not JSON, continue with JWT validation
    }

    // Production JWT validation with Azure AD B2C
    const tenantName = process.env.AZURE_AD_B2C_TENANT_NAME;
    const tenantId = process.env.AZURE_AD_B2C_TENANT_ID;
    const clientId = process.env.AZURE_AD_B2C_CLIENT_ID;
    const authorityDomain = process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN;

    if (!tenantName || !tenantId || !clientId || !authorityDomain) {
      logger.error('Azure AD B2C configuration missing', {
        correlationId,
        hastenantName: !!tenantName,
        hasTenantId: !!tenantId,
        hasClientId: !!clientId,
        hasAuthorityDomain: !!authorityDomain
      });
      return {
        success: false,
        error: 'Authentication service not configured',
        correlationId
      };
    }

    // Production: Verify JWT signature using Azure AD B2C public keys
    let payload: any;
    try {
      // Decode token first to get the policy from issuer
      const decoded = jwt.decode(token, { complete: true }) as any;
      if (!decoded || !decoded.header || !decoded.payload) {
        return {
          success: false,
          error: 'Invalid token structure',
          correlationId
        };
      }

      // Extract policy from issuer (e.g., B2C_1_SUSI)
      const issuer = decoded.payload.iss;
      let policy = 'B2C_1_SUSI'; // Default policy

      if (issuer && issuer.includes('/')) {
        const issuerParts = issuer.split('/');
        const policyPart = issuerParts.find((part: string) => part.startsWith('B2C_1_'));
        if (policyPart) {
          policy = policyPart;
        }
      }

      logger.info('Processing B2C token', {
        correlationId,
        policy,
        issuer,
        kid: decoded.header.kid
      });

      // Create JWKS client for Azure AD B2C with policy-specific endpoint
      const jwksUri = `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/discovery/v2.0/keys`;
      const jwksClient = new jwks.JwksClient({
        jwksUri,
        cache: true,
        cacheMaxEntries: 10,
        cacheMaxAge: 600000, // 10 minutes
        timeout: 30000,
        jwksRequestsPerMinute: 10
      });

      // Get signing key from JWKS
      const key = await jwksClient.getSigningKey(decoded.header.kid);
      const signingKey = key.getPublicKey();

      // Verify token signature and extract payload
      const expectedIssuer = `https://${authorityDomain}/${tenantName}.onmicrosoft.com/${policy}/v2.0/`;
      payload = jwt.verify(token, signingKey, {
        audience: clientId,
        issuer: expectedIssuer,
        algorithms: ['RS256']
      }) as any;

      logger.info('B2C JWT signature verified successfully', {
        correlationId,
        policy,
        kid: decoded.header.kid,
        alg: decoded.header.alg,
        issuer: expectedIssuer
      });

    } catch (verifyError) {
      logger.error('B2C JWT signature verification failed', {
        correlationId,
        error: verifyError instanceof Error ? verifyError.message : String(verifyError)
      });
      return {
        success: false,
        error: 'Token signature verification failed',
        correlationId
      };
    }

    // Token is already validated by jwt.verify() above, including expiration, audience, and issuer

    // Extract user information from token and align with frontend User type
    const firstName = payload.given_name || '';
    const lastName = payload.family_name || '';
    const displayName = payload.name || `${firstName} ${lastName}`.trim() ||
      (payload.email || payload.emails?.[0] || payload.preferred_username)?.split('@')[0];

    const user: UserContext = {
      id: payload.sub || payload.oid || payload.userId || payload.id,
      email: payload.email || payload.emails?.[0] || payload.preferred_username,
      firstName,
      lastName,
      displayName,
      avatar: payload.picture, // ALIGNED: use 'avatar' not 'avatarUrl'
      tenantId: payload.tid || tenantId,
      organizationId: payload.organizationId || payload.orgId,
      organizationIds: payload.organizationIds || (payload.organizationId ? [payload.organizationId] : []),
      roles: payload.roles || ['user'],
      systemRoles: payload.systemRoles || [],
      permissions: payload.permissions || [],
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (!user.id || !user.email) {
      return {
        success: false,
        error: 'Token missing required user information',
        correlationId
      };
    }

    // Create StandardToken for consistent format
    const standardToken: StandardToken = {
      accessToken: token,
      expiresAt: (payload.exp || 0) * 1000, // Convert to milliseconds
      userId: user.id,
      email: user.email,
      roles: user.roles,
      organizationIds: user.organizationIds,
      tenantId: user.tenantId
    };

    logger.info('JWT validated successfully and converted to StandardToken', {
      correlationId,
      userId: user.id,
      email: user.email,
      roles: user.roles?.length || 0,
      organizationIds: user.organizationIds?.length || 0,
      hasStandardToken: true
    });

    const securityEvent = request ? createAuthSecurityEvent(
      'jwt_authentication_success',
      request,
      user.id,
      user.organizationId,
      { tokenType: 'jwt', correlationId }
    ) : undefined;

    return {
      success: true,
      user,
      standardToken,
      authMethod: 'jwt',
      securityEvent,
      correlationId
    };
  } catch (error) {
    logger.error('Token validation failed', { error: error instanceof Error ? error.message : String(error) });
    return {
      success: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Authenticate request and extract user context
 * PRODUCTION-READY: Supports StandardToken and JWT tokens (API key auth removed)
 */
export async function authenticateRequest(request: HttpRequest): Promise<AuthResult> {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();

  // Extract token from Authorization header
  const token = extractToken(request);
  if (token) {
    const result = await validateToken(token, request);
    return {
      ...result,
      correlationId: result.correlationId || correlationId
    };
  }

  // Check for StandardToken in request body (for auth sync endpoint)
  try {
    const contentType = request.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      const body = await request.text();
      if (body) {
        const parsedBody = JSON.parse(body);
        if (parsedBody.standardToken) {
          logger.info('Found StandardToken in request body', { correlationId });
          return await validateStandardToken(parsedBody.standardToken, request);
        }
      }
    }
  } catch (error) {
    // Not JSON or no StandardToken, continue
    logger.debug('No StandardToken found in request body', { correlationId });
  }

  // No authentication provided
  const securityEvent = createAuthSecurityEvent(
    'authentication_missing',
    request,
    undefined,
    undefined,
    { reason: 'no_credentials', correlationId }
  );

  return {
    success: false,
    error: 'No authentication credentials provided',
    securityEvent,
    correlationId
  };
}

/**
 * Check if user has required role
 */
export function hasRole(user: UserContext, requiredRole: string): boolean {
  return user.roles?.includes(requiredRole) || false;
}

/**
 * Check if user has required permission
 */
export function hasPermission(user: UserContext, requiredPermission: string): boolean {
  return user.permissions?.includes(requiredPermission) || false;
}

/**
 * Create authentication middleware
 */
export function requireAuth(handler: (request: HttpRequest, context: any, user: UserContext) => Promise<any>) {
  return async (request: HttpRequest, context: any) => {
    const authResult = await authenticateRequest(request);

    if (!authResult.success || !authResult.user) {
      return {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        },
        jsonBody: {
          error: 'Unauthorized',
          message: authResult.error || 'Authentication required'
        }
      };
    }

    return await handler(request, context, authResult.user);
  };
}

/**
 * Legacy alias for authenticateRequest - for backward compatibility
 */
export const authenticateUser = authenticateRequest;
