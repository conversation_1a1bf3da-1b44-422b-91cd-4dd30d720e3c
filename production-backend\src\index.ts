/**
 * Main entry point for the Azure Functions application
 * Production-ready consolidated format with enhanced service initialization
 * and comprehensive error handling for enterprise-grade deployment
 */

// Load environment variables first
import './env';

// Import the app instance
import { app } from '@azure/functions';

// Configure the app for Azure Functions v4 with enhanced settings
app.setup({
  enableHttpStream: true,
});

console.log('🚀 Starting HEPZ Enterprise Document Processing Platform...');
console.log('📋 Loading unified functions and services...');

// =============================================================================
// UNIFIED FUNCTION IMPORTS - Production Ready Core Services
// =============================================================================
// These unified functions replace all legacy scattered functions
// Each provides comprehensive functionality with full Azure integrations

// Core Business Logic Functions
import './functions/unified-document-management';        // Document processing, storage, intelligence
import './functions/unified-workflow-management';        // Workflow automation, approval chains
import './functions/unified-user-management';            // User auth, profiles, permissions
import './functions/unified-organization-management';    // Multi-tenant organization handling
import './functions/unified-project-management';         // Project lifecycle, collaboration

// Communication & Collaboration
import './functions/unified-notification-management';    // Multi-channel notifications
import './functions/unified-communication-collaboration'; // Real-time collaboration, SignalR
import './functions/unified-mobile-notification';        // Mobile push notifications

// Analytics & Reporting
import './functions/unified-analytics-dashboard';        // Real-time dashboards, metrics
import './functions/unified-analytics-reporting';        // Advanced reporting, exports

// AI & Machine Learning
import './functions/unified-ai-ml-services';            // AI document analysis, ML models
import './functions/unified-ai-services-enhancement';    // Enhanced AI capabilities
import './functions/unified-document-enhancement';       // Document intelligence, RAG

// Infrastructure & Security
import './functions/unified-security-compliance';        // Security, audit, compliance
import './functions/unified-storage-data-management';    // Data management, encryption
import './functions/unified-infrastructure-utilities';   // System utilities, health checks
import './functions/unified-system-management';          // System configuration, feature flags

// External Integrations
import './functions/unified-external-services';          // Cloud storage, payment webhooks
import './functions/unified-integration-management';     // API integrations, webhooks

// =============================================================================
// EVENT-DRIVEN FUNCTIONS - Production Ready Handlers
// =============================================================================
// These handlers manage Azure service integrations and should NOT be consolidated

// Azure Service Handlers (Keep Separate)
import './functions/event-grid-handlers';               // Event Grid webhook processing
import './functions/service-bus-handlers';             // Service Bus message processing
import './functions/queue-handlers';                   // Storage queue processing
import './functions/timer-functions';                  // Scheduled background tasks
import './functions/blob-triggers';                    // Blob storage event handling
import './functions/event-grid-custom-trigger';        // Custom Event Grid triggers
import './functions/event-grid-storage-trigger';       // Storage Event Grid triggers

// Enterprise Monitoring
import './functions/monitoring-dashboard';              // System monitoring, health checks

// =============================================================================
// ENHANCED AZURE SERVICES INITIALIZATION
// =============================================================================
// Production-ready service initialization with proper dependency management,
// error handling, and graceful degradation

import { redis } from './shared/services/redis';
import { signalRService } from './shared/services/signalr';
import { ServiceBusEnhancedService } from './shared/services/service-bus';
import { eventGridIntegration } from './shared/services/event-grid-integration';
import { eventHubService } from './shared/services/event-hub';

// Service initialization state tracking
interface ServiceStatus {
  name: string;
  initialized: boolean;
  error?: string;
  initTime?: number;
}

const serviceStatuses: ServiceStatus[] = [
  { name: 'Redis', initialized: false },
  { name: 'Service Bus', initialized: false },
  { name: 'SignalR', initialized: false },
  { name: 'Event Grid', initialized: false },
  { name: 'Event Hub', initialized: false }
];

// Get service instances
const signalREnhanced = signalRService.getInstance();
const serviceBusEnhanced = ServiceBusEnhancedService.getInstance();

/**
 * Validate required environment variables for Azure Identity setup
 */
function validateEnvironment(): boolean {
  // For Azure Identity, we need resource endpoints/names, not connection strings
  const requiredAzureResources = [
    'COSMOS_DB_ENDPOINT',
    'AZURE_SERVICE_BUS_NAMESPACE', // This should be set to 'hepzbackend'
    'AZURE_REDIS_HOST',
    'EVENT_GRID_TOPIC_ENDPOINT'
  ];

  // Services that still require connection strings (Azure Blob Storage)
  const requiredConnectionStrings = [
    'AZURE_STORAGE_CONNECTION_STRING'
  ];

  const missingResources = requiredAzureResources.filter(varName => !process.env[varName]);
  const missingConnectionStrings = requiredConnectionStrings.filter(varName => !process.env[varName]);

  if (missingResources.length > 0) {
    console.error('❌ Missing required Azure resource configurations:', missingResources);
    console.error('💡 These should be resource endpoints/names for Azure Identity authentication');
    return false;
  }

  if (missingConnectionStrings.length > 0) {
    console.error('❌ Missing required connection strings:', missingConnectionStrings);
    console.error('💡 These services still require connection strings');
    return false;
  }

  console.log('✅ Environment validation passed for Azure Identity setup');
  return true;
}

/**
 * Initialize individual service with error handling and timing
 */
async function initializeService(
  serviceName: string,
  initFunction: () => Promise<void>,
  statusIndex: number
): Promise<boolean> {
  const startTime = Date.now();

  try {
    await initFunction();
    const initTime = Date.now() - startTime;

    serviceStatuses[statusIndex] = {
      name: serviceName,
      initialized: true,
      initTime
    };

    console.log(`✅ ${serviceName} initialized successfully (${initTime}ms)`);
    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    serviceStatuses[statusIndex] = {
      name: serviceName,
      initialized: false,
      error: errorMessage
    };

    console.error(`❌ Failed to initialize ${serviceName}:`, errorMessage);
    return false;
  }
}

/**
 * Initialize all Azure services with proper dependency order and error handling
 */
async function initializeServices(): Promise<void> {
  const totalStartTime = Date.now();

  try {
    console.log('🚀 Starting enhanced Azure services initialization...');
    console.log('🔍 Validating environment configuration...');

    // Step 1: Validate environment
    if (!validateEnvironment()) {
      throw new Error('Environment validation failed');
    }

    // Step 2: Initialize Redis first (required by other services)
    console.log('📡 Initializing Redis Enhanced Service...');
    await initializeService(
      'Redis Enhanced Service',
      () => redis.initialize(),
      0
    );

    // Step 3: Initialize Service Bus (independent)
    console.log('🚌 Initializing Service Bus Enhanced Service...');
    await initializeService(
      'Service Bus Enhanced Service',
      () => serviceBusEnhanced.initialize(),
      1
    );

    // Step 4: Initialize SignalR (depends on Redis for scaling)
    console.log('📡 Initializing SignalR Enhanced Service...');
    await initializeService(
      'SignalR Enhanced Service',
      () => signalREnhanced.initialize(),
      2
    );

    // Step 5: Event Grid Integration Service (auto-initialized in constructor)
    console.log('⚡ Event Grid Integration Service...');
    await initializeService(
      'Event Grid Integration Service',
      async () => {
        // Event Grid service is auto-initialized in constructor
        // Just perform a health check to verify it's working
        const isHealthy = await eventGridIntegration.healthCheck();
        if (!isHealthy) {
          throw new Error('Event Grid health check failed');
        }
      },
      3
    );

    // Step 6: Event Hub Service (with Azure Identity)
    console.log('🌐 Initializing Event Hub Service with Azure Identity...');
    await initializeService(
      'Event Hub Service',
      () => eventHubService.initialize(),
      4
    );

    // Summary
    const totalTime = Date.now() - totalStartTime;
    const successCount = serviceStatuses.filter(s => s.initialized).length;
    const totalCount = serviceStatuses.length;

    console.log('\n🎉 Service initialization completed!');
    console.log(`📊 Status: ${successCount}/${totalCount} services initialized (${totalTime}ms total)`);

    // Log service status details
    serviceStatuses.forEach(status => {
      if (status.initialized) {
        console.log(`   ✅ ${status.name}: Ready (${status.initTime}ms)`);
      } else {
        console.log(`   ❌ ${status.name}: Failed - ${status.error}`);
      }
    });

    // Log enhanced features
    if (successCount > 0) {
      console.log('\n� Enhanced features available:');
      if (serviceStatuses[0].initialized) {
        console.log('   🔴 Redis: Distributed locking, pub/sub, session management, clustering');
      }
      if (serviceStatuses[1].initialized) {
        console.log('   🚌 Service Bus: Dead letter handling, circuit breaker, batch processing');
      }
      if (serviceStatuses[2].initialized) {
        console.log('   📡 SignalR: Connection management, group management, cross-instance scaling');
      }
      if (serviceStatuses[3].initialized) {
        console.log('   ⚡ Event Grid: Advanced filtering, batching, schema validation');
      }
      if (serviceStatuses[4].initialized) {
        console.log('   🌐 Event Hub: Real-time analytics streaming, Azure Identity authentication');
      }
    }

    // Warning for partial failures
    if (successCount < totalCount) {
      console.warn(`⚠️  Warning: ${totalCount - successCount} service(s) failed to initialize`);
      console.warn('   The application will continue with reduced functionality');
    }

  } catch (error) {
    console.error('💥 Critical error during service initialization:', error);
    console.error('   The application may have reduced functionality');
  }
}

// =============================================================================
// APPLICATION STARTUP
// =============================================================================

// Start service initialization immediately
console.log('🔧 Initializing Azure services...');
initializeServices().then(() => {
  console.log('✨ HEPZ Enterprise Document Processing Platform is ready!');
  console.log('🌐 All unified functions are registered and available');
  console.log('🔗 Azure services are initialized and connected');
  console.log('📡 Event-driven handlers are active and listening');
  console.log('🚀 Production-ready backend is now serving requests');
}).catch(error => {
  console.error('💥 Failed to fully initialize the platform:', error);
  console.error('⚠️  Some functionality may be limited');
});

// =============================================================================
// EXPORT FOR AZURE FUNCTIONS RUNTIME
// =============================================================================

/**
 * Export the configured Azure Functions app instance
 * This is the main entry point for the Azure Functions runtime
 *
 * The app includes:
 * - 17 unified production-ready functions covering all business logic
 * - 7 event-driven handlers for Azure service integrations
 * - 1 monitoring dashboard for system health
 * - Enhanced Azure services (Redis, Service Bus, SignalR, Event Grid)
 * - Comprehensive error handling and graceful degradation
 * - Production-grade logging and monitoring
 */
export default app;

// =============================================================================
// FUNCTION SUMMARY
// =============================================================================
/*
UNIFIED FUNCTIONS (17):
├── unified-document-management        → Document processing, storage, intelligence
├── unified-workflow-management        → Workflow automation, approval chains
├── unified-user-management           → User auth, profiles, permissions
├── unified-organization-management   → Multi-tenant organization handling
├── unified-project-management        → Project lifecycle, collaboration
├── unified-notification-management   → Multi-channel notifications
├── unified-communication-collaboration → Real-time collaboration, SignalR
├── unified-mobile-notification       → Mobile push notifications
├── unified-analytics-dashboard       → Real-time dashboards, metrics
├── unified-analytics-reporting       → Advanced reporting, exports
├── unified-ai-ml-services           → AI document analysis, ML models
├── unified-ai-services-enhancement  → Enhanced AI capabilities
├── unified-document-enhancement     → Document intelligence, RAG
├── unified-security-compliance      → Security, audit, compliance
├── unified-storage-data-management  → Data management, encryption
├── unified-infrastructure-utilities → System utilities, health checks
├── unified-system-management        → System configuration, feature flags
├── unified-external-services        → Cloud storage, payment webhooks
└── unified-integration-management   → API integrations, webhooks

EVENT-DRIVEN HANDLERS (7):
├── event-grid-handlers              → Event Grid webhook processing
├── service-bus-handlers             → Service Bus message processing
├── queue-handlers                   → Storage queue processing
├── timer-functions                  → Scheduled background tasks
├── blob-triggers                    → Blob storage event handling
├── event-grid-custom-trigger        → Custom Event Grid triggers
└── event-grid-storage-trigger       → Storage Event Grid triggers

MONITORING (1):
└── monitoring-dashboard             → System monitoring, health checks

AZURE SERVICES:
├── Redis Enhanced Service           → Distributed locking, pub/sub, clustering
├── Service Bus Enhanced Service     → Dead letter handling, circuit breaker
├── SignalR Enhanced Service         → Connection management, scaling
└── Event Grid Integration Service   → Advanced filtering, batching
*/
