/**
 * Single Project Hook
 * Manages individual project operations and state
 */

import { useCallback } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import {
  useSelectedProject,
  useProjectLoading,
  useProjectError,
  useSelectProject
} from '@/stores/project-store'
import { backendApiClient } from '@/services/backend-api-client'
import type { Project, ProjectVisibility } from '@/types/backend'

export interface UseProjectOptions {
  projectId: string
  enabled?: boolean
}

export interface UseProjectResult {
  // State
  project: Project | undefined
  isLoading: boolean
  error: Error | null
  
  // Operations
  updateProject: (updates: Partial<Project>) => Promise<void>
  deleteProject: () => Promise<void>
  refetch: () => Promise<void>
}

export function useProject({ projectId, enabled = true }: UseProjectOptions): UseProjectResult {
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Get data from Zustand store
  const selectedProject = useSelectedProject()
  const isLoading = useProjectLoading()
  const error = useProjectError()
  const selectProject = useSelectProject()

  // Get project data (either selected project if it matches, or undefined)
  const project = selectedProject?.id === projectId ? selectedProject : undefined

  const refetch = useCallback(async () => {
    if (!enabled || !projectId) return
    try {
      await selectProject(projectId)
    } catch (error: any) {
      toast({
        title: 'Error fetching project',
        description: error.message || 'Failed to fetch project',
        variant: 'destructive',
      })
    }
  }, [selectProject, projectId, enabled, toast])

  // Update project mutation
  const updateMutation = useMutation({
    mutationFn: (updates: Partial<Project>) => 
      backendApiClient.updateProject(projectId, updates),
    onSuccess: (updatedProject) => {
      // Update cache
      queryClient.setQueryData(['project', projectId], updatedProject)
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      
      toast({
        title: 'Project updated',
        description: 'Project has been updated successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Update failed',
        description: error.message || 'Failed to update project',
      })
    }
  })

  // Delete project mutation
  const deleteMutation = useMutation({
    mutationFn: () => backendApiClient.deleteProject(projectId),
    onSuccess: () => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: ['project', projectId] })
      queryClient.invalidateQueries({ queryKey: ['projects'] })
      
      toast({
        title: 'Project deleted',
        description: 'Project has been deleted successfully.',
      })
    },
    onError: (error: Error) => {
      toast({
        variant: 'destructive',
        title: 'Delete failed',
        description: error.message || 'Failed to delete project',
      })
    }
  })

  return {
    // State
    project,
    isLoading: isLoading || updateMutation.isPending || deleteMutation.isPending,
    error: (error ? new Error(error) : null) || (updateMutation.error as Error) || (deleteMutation.error as Error),
    
    // Operations
    updateProject: async (updates: Partial<Project>) => {
      await updateMutation.mutateAsync(updates)
    },
    deleteProject: deleteMutation.mutateAsync,
    refetch: async () => {
      await refetch()
    }
  }
}
