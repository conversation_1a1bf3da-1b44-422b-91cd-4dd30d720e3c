"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Check, ChevronsUpDown, PlusCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useOrganizations } from "@/hooks/organizations/useOrganizations";
import { Skeleton } from "@/components/ui/skeleton";

export function OrganizationSwitcher() {
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const { organizations, loading: isLoading, currentOrganization, setCurrentOrganization } = useOrganizations();

  if (isLoading) {
    return <Skeleton className="h-9 w-[180px]" />;
  }

  if (!organizations || organizations.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="w-[180px] justify-start text-left font-normal"
        onClick={() => router.push("/organizations/create")}
      >
        <PlusCircle className="mr-2 h-4 w-4" />
        <span>Create Organization</span>
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select organization"
          className="w-[180px] justify-between"
        >
          {currentOrganization?.name || "Select organization"}
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandInput placeholder="Search organization..." />
            <CommandEmpty>No organization found.</CommandEmpty>
            <CommandGroup heading="Organizations">
              {organizations.map((org: any) => (
                <CommandItem
                  key={org.id}
                  onSelect={() => {
                    setCurrentOrganization(org);
                    setOpen(false);
                  }}
                  className="text-sm"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      currentOrganization?.id === org.id
                        ? "opacity-100"
                        : "opacity-0"
                    )}
                  />
                  {org.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          <CommandSeparator />
          <CommandList>
            <CommandGroup>
              <CommandItem
                onSelect={() => {
                  router.push("/organizations/create");
                  setOpen(false);
                }}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Create Organization
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
