/**
 * System Monitoring Dashboard
 * Comprehensive system health and performance monitoring
 */

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  HardDrive,
  MemoryStick,
  RefreshCw,
  Server,
  Wifi,
  XCircle,
  Zap
} from 'lucide-react'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts'

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  redis: { status: string; responseTime: number; memory: number }
  database: { status: string; responseTime: number; connections: number }
  serviceBus: { status: string; activeConnections: number; queueDepth: number }
  storage: { status: string; responseTime: number; usage: number }
  performance: {
    averageResponseTime: number
    requestsPerMinute: number
    errorRate: number
    memoryUsage: { used: number; total: number; percentage: number }
  }
  circuitBreakers: Array<{
    name: string
    state: 'closed' | 'open' | 'half-open'
    failureCount: number
    lastFailure?: string
  }>
  cacheStats: {
    hitRate: number
    missRate: number
    totalRequests: number
    evictions: number
  }
}

interface PerformanceMetrics {
  timestamp: string
  responseTime: number
  requestCount: number
  errorCount: number
  memoryUsage: number
}

interface SystemAlert {
  id: string
  type: 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: string
  resolved: boolean
}

export function SystemMonitoringDashboard() {
  const { toast } = useToast()
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics[]>([])
  const [alerts, setAlerts] = useState<SystemAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  const fetchSystemHealth = async () => {
    try {
      const response = await backendApiClient.request<SystemHealth>('/monitoring/health')
      setSystemHealth(response)
    } catch (error: any) {
      console.error('Failed to fetch system health:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch system health data',
        variant: 'destructive'
      })
    }
  }

  const fetchPerformanceMetrics = async () => {
    try {
      const response = await backendApiClient.request<PerformanceMetrics[]>('/monitoring/performance')
      setPerformanceMetrics(response)
    } catch (error: any) {
      console.error('Failed to fetch performance metrics:', error)
    }
  }

  const fetchAlerts = async () => {
    try {
      const response = await backendApiClient.request<SystemAlert[]>('/monitoring/alerts')
      setAlerts(response)
    } catch (error: any) {
      console.error('Failed to fetch alerts:', error)
    }
  }

  const refreshData = async () => {
    setRefreshing(true)
    await Promise.all([
      fetchSystemHealth(),
      fetchPerformanceMetrics(),
      fetchAlerts()
    ])
    setRefreshing(false)
  }

  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await refreshData()
      setLoading(false)
    }

    loadData()

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000)
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
        return 'text-green-600'
      case 'degraded':
      case 'warning':
        return 'text-yellow-600'
      case 'unhealthy':
      case 'error':
      case 'disconnected':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'degraded':
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'unhealthy':
      case 'error':
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">System Monitoring</h1>
        <Button
          onClick={refreshData}
          disabled={refreshing}
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overall Status</CardTitle>
            {systemHealth && getStatusIcon(systemHealth.status)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">
              {systemHealth?.status || 'Unknown'}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemHealth?.performance.averageResponseTime.toFixed(0)}ms
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemHealth?.performance.errorRate.toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <MemoryStick className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {systemHealth?.performance.memoryUsage.percentage.toFixed(0)}%
            </div>
            <Progress 
              value={systemHealth?.performance.memoryUsage.percentage || 0} 
              className="mt-2"
            />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="cache">Cache</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Redis Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Redis
                  {systemHealth && getStatusIcon(systemHealth.redis.status)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Response Time:</span>
                  <span>{systemHealth?.redis.responseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Memory Usage:</span>
                  <span>{systemHealth?.redis.memory}MB</span>
                </div>
              </CardContent>
            </Card>

            {/* Database Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Server className="h-5 w-5" />
                  Database
                  {systemHealth && getStatusIcon(systemHealth.database.status)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Response Time:</span>
                  <span>{systemHealth?.database.responseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Connections:</span>
                  <span>{systemHealth?.database.connections}</span>
                </div>
              </CardContent>
            </Card>

            {/* Service Bus Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wifi className="h-5 w-5" />
                  Service Bus
                  {systemHealth && getStatusIcon(systemHealth.serviceBus.status)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Active Connections:</span>
                  <span>{systemHealth?.serviceBus.activeConnections}</span>
                </div>
                <div className="flex justify-between">
                  <span>Queue Depth:</span>
                  <span>{systemHealth?.serviceBus.queueDepth}</span>
                </div>
              </CardContent>
            </Card>

            {/* Storage Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <HardDrive className="h-5 w-5" />
                  Storage
                  {systemHealth && getStatusIcon(systemHealth.storage.status)}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Response Time:</span>
                  <span>{systemHealth?.storage.responseTime}ms</span>
                </div>
                <div className="flex justify-between">
                  <span>Usage:</span>
                  <span>{systemHealth?.storage.usage}%</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Circuit Breakers */}
          {systemHealth?.circuitBreakers && systemHealth.circuitBreakers.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Circuit Breakers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {systemHealth.circuitBreakers.map((cb, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <span className="font-medium">{cb.name}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant={cb.state === 'closed' ? 'default' : 'destructive'}>
                          {cb.state}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          Failures: {cb.failureCount}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>System performance over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={performanceMetrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="timestamp" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="responseTime" stroke="#8884d8" name="Response Time (ms)" />
                  <Line type="monotone" dataKey="requestCount" stroke="#82ca9d" name="Request Count" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>Recent system alerts and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">No alerts at this time</p>
                ) : (
                  alerts.map((alert) => (
                    <Alert key={alert.id} variant={alert.type === 'error' ? 'destructive' : 'default'}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>{alert.title}</AlertTitle>
                      <AlertDescription>
                        {alert.message}
                        <div className="text-xs text-muted-foreground mt-1">
                          {new Date(alert.timestamp).toLocaleString()}
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cache" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cache Statistics</CardTitle>
              <CardDescription>Cache performance and statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {systemHealth?.cacheStats.hitRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Hit Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {systemHealth?.cacheStats.missRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Miss Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {systemHealth?.cacheStats.totalRequests.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">Total Requests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {systemHealth?.cacheStats.evictions.toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">Evictions</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
