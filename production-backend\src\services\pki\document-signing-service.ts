/**
 * Comprehensive Document Signing Service
 * Supports both PKI digital signatures and visual signature placement
 */

import { PKIProviderRegistry } from './pki-provider-interface'
import { KeyVaultService } from './key-vault-service'
import { logger } from '../../shared/utils/logger'
import * as PDFLib from 'pdf-lib'
import * as fs from 'fs'
import * as path from 'path'

export interface DocumentSigningRequest {
  documentId: string
  documentBuffer: Buffer
  documentFormat: 'pdf' | 'docx' | 'xlsx' | 'pptx'
  signatureType: 'visual' | 'pki' | 'both'
  
  // PKI Signature Options
  pkiOptions?: {
    providerId?: string
    certificateId?: string
    signatureFormat: 'PKCS7' | 'CAdES' | 'PAdES' | 'XAdES'
    timestampingRequired: boolean
    includeSigningCertificate: boolean
    includeCertificateChain: boolean
    complianceLevel: 'basic' | 'advanced' | 'qualified'
  }
  
  // Visual Signature Options
  visualOptions?: {
    signatureImage: Buffer
    position: SignaturePosition
    appearance: SignatureAppearance
    reason?: string
    location?: string
    contactInfo?: string
  }
  
  // User and Organization Context
  userId: string
  organizationId: string
  projectId?: string
}

export interface SignaturePosition {
  page: number
  x: number
  y: number
  width: number
  height: number
  coordinateSystem: 'bottom-left' | 'top-left' // PDF vs. screen coordinates
}

export interface SignatureAppearance {
  showSignatureImage: boolean
  showSignerName: boolean
  showSigningTime: boolean
  showReason: boolean
  showLocation: boolean
  showCertificateInfo: boolean
  backgroundColor?: string
  borderColor?: string
  borderWidth?: number
  fontSize?: number
  fontColor?: string
}

export interface DocumentSigningResult {
  signedDocumentBuffer: Buffer
  signatureId: string
  signatures: SignatureInfo[]
  metadata: {
    originalDocumentId: string
    signedAt: Date
    signedBy: string
    organizationId: string
    projectId?: string
    documentHash: string
    signatureCount: number
    complianceLevel: 'basic' | 'advanced' | 'qualified'
    auditTrail: AuditEvent[]
  }
}

export interface SignatureInfo {
  id: string
  type: 'visual' | 'pki' | 'combined'
  position?: SignaturePosition
  signerName: string
  signingTime: Date
  reason?: string
  location?: string
  
  // PKI-specific information
  certificateInfo?: {
    serialNumber: string
    issuer: string
    subject: string
    notBefore: Date
    notAfter: Date
    fingerprint: string
    providerId: string
    complianceLevel: 'basic' | 'advanced' | 'qualified'
  }
  
  // Visual signature information
  visualInfo?: {
    imageHash: string
    boundingBox: SignaturePosition
    appearance: SignatureAppearance
  }
  
  // Verification information
  verification: {
    isValid: boolean
    validationTime: Date
    validationErrors: string[]
    certificateChainValid?: boolean
    revocationStatus?: 'good' | 'revoked' | 'unknown'
  }
}

export interface AuditEvent {
  timestamp: Date
  event: 'document_uploaded' | 'signature_requested' | 'signature_applied' | 
         'certificate_validated' | 'document_signed' | 'verification_performed'
  actor: string
  details: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export class DocumentSigningService {
  private pkiRegistry: PKIProviderRegistry
  private keyVaultService: KeyVaultService

  constructor(pkiRegistry: PKIProviderRegistry) {
    this.pkiRegistry = pkiRegistry
    this.keyVaultService = new KeyVaultService()
  }

  async initialize(): Promise<void> {
    await this.keyVaultService.initialize()
    logger.info('Document signing service initialized')
  }

  async signDocument(request: DocumentSigningRequest): Promise<DocumentSigningResult> {
    try {
      logger.info('Starting document signing process', {
        documentId: request.documentId,
        signatureType: request.signatureType,
        userId: request.userId
      })

      // Validate request
      this.validateSigningRequest(request)

      // Calculate document hash for audit trail
      const documentHash = this.calculateDocumentHash(request.documentBuffer)

      // Initialize audit trail
      const auditTrail: AuditEvent[] = [{
        timestamp: new Date(),
        event: 'signature_requested',
        actor: request.userId,
        details: {
          documentId: request.documentId,
          signatureType: request.signatureType,
          documentFormat: request.documentFormat
        }
      }]

      let signedDocument = request.documentBuffer
      const signatures: SignatureInfo[] = []

      // Apply signatures based on type
      switch (request.signatureType) {
        case 'visual':
          const visualResult = await this.applyVisualSignature(signedDocument, request)
          signedDocument = visualResult.signedDocument
          signatures.push(visualResult.signatureInfo)
          break

        case 'pki':
          const pkiResult = await this.applyPKISignature(signedDocument, request)
          signedDocument = pkiResult.signedDocument
          signatures.push(pkiResult.signatureInfo)
          break

        case 'both':
          // Apply visual signature first
          const visualBothResult = await this.applyVisualSignature(signedDocument, request)
          signedDocument = visualBothResult.signedDocument
          signatures.push(visualBothResult.signatureInfo)

          // Then apply PKI signature
          const pkiBothResult = await this.applyPKISignature(signedDocument, request)
          signedDocument = pkiBothResult.signedDocument
          signatures.push(pkiBothResult.signatureInfo)
          break
      }

      // Add completion audit event
      auditTrail.push({
        timestamp: new Date(),
        event: 'document_signed',
        actor: request.userId,
        details: {
          documentId: request.documentId,
          signatureCount: signatures.length,
          signatureIds: signatures.map(s => s.id)
        }
      })

      const result: DocumentSigningResult = {
        signedDocumentBuffer: signedDocument,
        signatureId: this.generateSignatureId(),
        signatures,
        metadata: {
          originalDocumentId: request.documentId,
          signedAt: new Date(),
          signedBy: request.userId,
          organizationId: request.organizationId,
          projectId: request.projectId,
          documentHash,
          signatureCount: signatures.length,
          complianceLevel: request.pkiOptions?.complianceLevel || 'basic',
          auditTrail
        }
      }

      logger.info('Document signing completed successfully', {
        documentId: request.documentId,
        signatureId: result.signatureId,
        signatureCount: signatures.length
      })

      return result
    } catch (error) {
      logger.error('Document signing failed', { error, request })
      throw new Error('Document signing failed')
    }
  }

  async verifyDocumentSignatures(
    documentBuffer: Buffer,
    documentId: string
  ): Promise<SignatureInfo[]> {
    try {
      logger.info('Starting signature verification', { documentId })

      const signatures: SignatureInfo[] = []

      // Extract and verify signatures based on document format
      if (this.isPDF(documentBuffer)) {
        const pdfSignatures = await this.extractPDFSignatures(documentBuffer)
        for (const sig of pdfSignatures) {
          const verification = await this.verifySignature(sig)
          signatures.push({
            ...sig,
            verification
          })
        }
      }

      logger.info('Signature verification completed', {
        documentId,
        signatureCount: signatures.length,
        validSignatures: signatures.filter(s => s.verification.isValid).length
      })

      return signatures
    } catch (error) {
      logger.error('Signature verification failed', { error, documentId })
      throw new Error('Signature verification failed')
    }
  }

  private async applyVisualSignature(
    documentBuffer: Buffer,
    request: DocumentSigningRequest
  ): Promise<{ signedDocument: Buffer; signatureInfo: SignatureInfo }> {
    if (!request.visualOptions) {
      throw new Error('Visual signature options required')
    }

    if (request.documentFormat === 'pdf') {
      return await this.applyVisualSignatureToPDF(documentBuffer, request)
    } else {
      throw new Error(`Visual signatures not yet supported for ${request.documentFormat}`)
    }
  }

  private async applyVisualSignatureToPDF(
    pdfBuffer: Buffer,
    request: DocumentSigningRequest
  ): Promise<{ signedDocument: Buffer; signatureInfo: SignatureInfo }> {
    const { visualOptions } = request
    if (!visualOptions) throw new Error('Visual options required')

    try {
      // Load PDF document
      const pdfDoc = await PDFLib.PDFDocument.load(pdfBuffer)
      const pages = pdfDoc.getPages()
      
      if (visualOptions.position.page > pages.length) {
        throw new Error('Invalid page number for signature placement')
      }

      const page = pages[visualOptions.position.page - 1]
      
      // Embed signature image
      const signatureImage = await pdfDoc.embedPng(visualOptions.signatureImage)
      
      // Calculate position (convert from screen coordinates if needed)
      const { x, y, width, height } = this.convertCoordinates(
        visualOptions.position,
        page.getWidth(),
        page.getHeight()
      )

      // Draw signature image
      page.drawImage(signatureImage, {
        x,
        y,
        width,
        height
      })

      // Add signature appearance elements
      if (visualOptions.appearance.showSignerName) {
        page.drawText(`Signed by: ${request.userId}`, {
          x,
          y: y - 15,
          size: visualOptions.appearance.fontSize || 10,
          color: PDFLib.rgb(0, 0, 0)
        })
      }

      if (visualOptions.appearance.showSigningTime) {
        page.drawText(`Date: ${new Date().toLocaleString()}`, {
          x,
          y: y - 30,
          size: visualOptions.appearance.fontSize || 10,
          color: PDFLib.rgb(0, 0, 0)
        })
      }

      if (visualOptions.reason && visualOptions.appearance.showReason) {
        page.drawText(`Reason: ${visualOptions.reason}`, {
          x,
          y: y - 45,
          size: visualOptions.appearance.fontSize || 10,
          color: PDFLib.rgb(0, 0, 0)
        })
      }

      // Save modified PDF
      const signedPdfBuffer = Buffer.from(await pdfDoc.save())

      const signatureInfo: SignatureInfo = {
        id: this.generateSignatureId(),
        type: 'visual',
        position: visualOptions.position,
        signerName: request.userId,
        signingTime: new Date(),
        reason: visualOptions.reason,
        location: visualOptions.location,
        visualInfo: {
          imageHash: this.calculateImageHash(visualOptions.signatureImage),
          boundingBox: visualOptions.position,
          appearance: visualOptions.appearance
        },
        verification: {
          isValid: true,
          validationTime: new Date(),
          validationErrors: []
        }
      }

      return {
        signedDocument: signedPdfBuffer,
        signatureInfo
      }
    } catch (error) {
      logger.error('Failed to apply visual signature to PDF', { error })
      throw new Error('Visual signature application failed')
    }
  }

  private async applyPKISignature(
    documentBuffer: Buffer,
    request: DocumentSigningRequest
  ): Promise<{ signedDocument: Buffer; signatureInfo: SignatureInfo }> {
    if (!request.pkiOptions) {
      throw new Error('PKI signature options required')
    }

    try {
      // Get PKI provider
      const provider = this.pkiRegistry.getProvider(request.pkiOptions.providerId)
      
      // Get or create certificate
      let certificateId = request.pkiOptions.certificateId
      if (!certificateId) {
        // Create new certificate for user
        const certificate = await provider.requestCertificate({
          commonName: `${request.userId}@${request.organizationId}`,
          organizationName: request.organizationId,
          emailAddress: `${request.userId}@${request.organizationId}`,
          keyUsage: ['digitalSignature', 'nonRepudiation'],
          extendedKeyUsage: ['*******.*******.4'], // Document signing
          validityPeriod: 24,
          certificateType: 'document-signing' as any,
          customAttributes: {
            requestedBy: request.userId,
            organizationId: request.organizationId,
            projectId: request.projectId || ''
          }
        })
        certificateId = certificate.id
      }

      // Sign document
      const signingResult = await provider.signData({
        data: documentBuffer,
        dataType: 'document',
        certificateId,
        timestampingRequired: request.pkiOptions.timestampingRequired,
        signatureFormat: request.pkiOptions.signatureFormat,
        includeSigningCertificate: request.pkiOptions.includeSigningCertificate,
        includeCertificateChain: request.pkiOptions.includeCertificateChain
      })

      // Get certificate info for signature metadata
      const certificate = await provider.getCertificate(certificateId)

      const signatureInfo: SignatureInfo = {
        id: this.generateSignatureId(),
        type: 'pki',
        signerName: certificate.commonName,
        signingTime: new Date(),
        certificateInfo: {
          serialNumber: certificate.serialNumber,
          issuer: certificate.issuer,
          subject: certificate.subject,
          notBefore: certificate.notBefore,
          notAfter: certificate.notAfter,
          fingerprint: certificate.fingerprint,
          providerId: certificate.metadata.providerId,
          complianceLevel: certificate.metadata.complianceLevel
        },
        verification: {
          isValid: true,
          validationTime: new Date(),
          validationErrors: [],
          certificateChainValid: true,
          revocationStatus: 'good'
        }
      }

      // For PDF documents, embed the PKI signature
      let signedDocument = documentBuffer
      if (request.documentFormat === 'pdf') {
        signedDocument = await this.embedPKISignatureInPDF(
          documentBuffer,
          signingResult,
          signatureInfo
        )
      }

      return {
        signedDocument,
        signatureInfo
      }
    } catch (error) {
      logger.error('Failed to apply PKI signature', { error })
      throw new Error('PKI signature application failed')
    }
  }

  private async embedPKISignatureInPDF(
    pdfBuffer: Buffer,
    signingResult: any,
    signatureInfo: SignatureInfo
  ): Promise<Buffer> {
    try {
      // Import PDF manipulation library
      const PDFDocument = require('pdf-lib').PDFDocument
      const pdfDoc = await PDFDocument.load(pdfBuffer)

      // Create signature dictionary
      const signatureDict = {
        Type: 'Sig',
        Filter: 'Adobe.PPKLite',
        SubFilter: 'adbe.pkcs7.detached',
        ByteRange: [0, 0, 0, 0], // Will be updated during signing
        Contents: Buffer.from(signingResult.signature, 'hex'),
        Reason: signatureInfo.reason || 'Document signing',
        Location: signatureInfo.location || 'Digital',
        ContactInfo: (signatureInfo as any).contactInfo || '',
        M: new Date().toISOString(),
        Name: signatureInfo.signerName || 'Digital Signer'
      }

      // Add signature field to the PDF
      const pages = pdfDoc.getPages()
      const firstPage = pages[0]

      // Create signature annotation
      const signatureField = pdfDoc.getForm().createSignature('Signature1')
      signatureField.addToPage(firstPage, {
        x: signatureInfo.position?.x || 50,
        y: signatureInfo.position?.y || 50,
        width: signatureInfo.position?.width || 200,
        height: signatureInfo.position?.height || 50
      })

      // Embed the signature data
      const pdfBytes = await pdfDoc.save({
        useObjectStreams: false,
        addDefaultPage: false
      })

      logger.info('PKI signature successfully embedded in PDF', {
        signatureId: signatureInfo.id,
        documentSize: pdfBytes.length
      })

      return Buffer.from(pdfBytes)
    } catch (error) {
      logger.error('Failed to embed PKI signature in PDF', {
        error: error instanceof Error ? error.message : 'Unknown error',
        signatureId: signatureInfo.id
      })

      // Fallback: return original buffer with metadata annotation
      logger.warn('Using fallback signature embedding method')
      return this.addSignatureMetadataToPDF(pdfBuffer, signatureInfo)
    }
  }

  private async addSignatureMetadataToPDF(pdfBuffer: Buffer, signatureInfo: SignatureInfo): Promise<Buffer> {
    try {
      const PDFDocument = require('pdf-lib').PDFDocument
      const pdfDoc = await PDFDocument.load(pdfBuffer)

      // Add signature metadata to document info
      pdfDoc.setTitle(pdfDoc.getTitle() + ' [DIGITALLY SIGNED]')
      pdfDoc.setSubject(`Signed on ${new Date().toISOString()}`)
      pdfDoc.setKeywords([
        'digitally-signed',
        `signature-id:${signatureInfo.id}`,
        `signer:${signatureInfo.signerName || 'Unknown'}`
      ].join(', '))

      const pdfBytes = await pdfDoc.save()
      return Buffer.from(pdfBytes)
    } catch (error) {
      logger.error('Failed to add signature metadata to PDF', { error })
      return pdfBuffer // Return original if all else fails
    }
  }

  private validateSigningRequest(request: DocumentSigningRequest): void {
    if (!request.documentBuffer || request.documentBuffer.length === 0) {
      throw new Error('Document buffer is required')
    }

    if (request.signatureType === 'visual' && !request.visualOptions) {
      throw new Error('Visual signature options required for visual signatures')
    }

    if (request.signatureType === 'pki' && !request.pkiOptions) {
      throw new Error('PKI signature options required for PKI signatures')
    }

    if (request.signatureType === 'both' && (!request.visualOptions || !request.pkiOptions)) {
      throw new Error('Both visual and PKI options required for combined signatures')
    }
  }

  private calculateDocumentHash(buffer: Buffer): string {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(buffer).digest('hex')
  }

  private calculateImageHash(buffer: Buffer): string {
    const crypto = require('crypto')
    return crypto.createHash('sha256').update(buffer).digest('hex')
  }

  private generateSignatureId(): string {
    return `sig_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private isPDF(buffer: Buffer): boolean {
    return buffer.subarray(0, 4).toString() === '%PDF'
  }

  private convertCoordinates(
    position: SignaturePosition,
    pageWidth: number,
    pageHeight: number
  ): { x: number; y: number; width: number; height: number } {
    if (position.coordinateSystem === 'top-left') {
      // Convert from top-left to bottom-left (PDF coordinate system)
      return {
        x: position.x,
        y: pageHeight - position.y - position.height,
        width: position.width,
        height: position.height
      }
    }
    return position
  }

  private async extractPDFSignatures(pdfBuffer: Buffer): Promise<SignatureInfo[]> {
    // Implementation would extract existing signatures from PDF
    // This is a placeholder
    return []
  }

  private async verifySignature(signature: SignatureInfo): Promise<{
    isValid: boolean
    validationTime: Date
    validationErrors: string[]
    certificateChainValid?: boolean
    revocationStatus?: 'good' | 'revoked' | 'unknown'
  }> {
    // Implementation would verify the signature
    return {
      isValid: true,
      validationTime: new Date(),
      validationErrors: []
    }
  }
}
