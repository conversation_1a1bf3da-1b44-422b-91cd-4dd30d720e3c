'use client';

import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useProject } from '@/hooks/projects/useProject';
import { useWorkflow } from '@/hooks/workflows/useWorkflows';
import { ArrowLeft, Edit, Play } from 'lucide-react';
import { WorkflowDiagram } from '@/components/workflows/workflow-diagram';
import { Badge } from '@/components/ui/badge';
import { formatDate } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WorkflowHistory } from '@/components/workflows/workflow-history';

export default function WorkflowDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();

  // Ensure params exists and get IDs
  if (!params || !params.projectId || !params.workflowId) {
    return <div>Loading...</div>;
  }

  const projectId = Array.isArray(params.projectId) ? params.projectId[0] : params.projectId;
  const workflowId = Array.isArray(params.workflowId) ? params.workflowId[0] : params.workflowId;

  const { project: _project } = useProject({ projectId });
  const { data: workflow, isLoading, error } = useWorkflow(workflowId);

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load workflow details',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading workflow details...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <Button
            variant="ghost"
            size="sm"
            className="mb-2"
            onClick={() => router.push(`/projects/${projectId}/workflows`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Workflows
          </Button>
          <h1 className="text-3xl font-bold">{workflow?.name || 'Workflow Details'}</h1>
          <p className="text-muted-foreground">Project: {_project?.name}</p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/projects/${projectId}/workflows/${workflowId}/edit`)}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Workflow
          </Button>
          <Button
            onClick={() => {
              // Run workflow
              toast({
                title: 'Starting workflow',
                description: 'Initiating workflow execution...',
              });
              // This would typically involve an API call
            }}
          >
            <Play className="mr-2 h-4 w-4" />
            Run Workflow
          </Button>
        </div>
      </div>

      <Tabs defaultValue="diagram">
        <TabsList>
          <TabsTrigger value="diagram">Workflow Diagram</TabsTrigger>
          <TabsTrigger value="history">Execution History</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
        </TabsList>

        <TabsContent value="diagram" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Diagram</CardTitle>
            </CardHeader>
            <CardContent>
              <WorkflowDiagram steps={(workflow?.steps || []) as any} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Execution History</CardTitle>
            </CardHeader>
            <CardContent>
              <WorkflowHistory events={[]} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="details" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Workflow Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">Description</h3>
                <p className="text-sm text-muted-foreground">{workflow?.description || 'No description provided'}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Status</h3>
                <Badge variant={(workflow as any)?.isActive ? 'default' : 'secondary'} className="mt-1">
                  {(workflow as any)?.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>

              <div>
                <h3 className="text-sm font-medium">Created</h3>
                <p className="text-sm text-muted-foreground">
                  {workflow?.createdAt ? formatDate(new Date(workflow.createdAt)) : 'Unknown'}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Last Updated</h3>
                <p className="text-sm text-muted-foreground">
                  {workflow?.updatedAt ? formatDate(new Date(workflow.updatedAt)) : 'Unknown'}
                </p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Created By</h3>
                <p className="text-sm text-muted-foreground">{(workflow?.createdBy as any)?.name || workflow?.createdBy || 'Unknown'}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium">Trigger Type</h3>
                <p className="text-sm text-muted-foreground">{(workflow as any)?.triggerType || 'Manual'}</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
