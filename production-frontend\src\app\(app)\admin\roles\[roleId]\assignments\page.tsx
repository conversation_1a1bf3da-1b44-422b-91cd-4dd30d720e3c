"use client";

import { useState } from "react";
import { useParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { SystemRole } from "@/types/role";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/components/ui/use-toast";
import { AdminOnly } from "@/components/permission-guard";
import { EmptyState } from "@/components/empty-state";
import {
  ArrowLeft,
  Calendar,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  Trash,
  User,
  Users
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { RoleAssignmentWithRole, RoleScope } from "@/types/role";
import { User as UserType } from "@/types/user";
import { useRoleAssignments } from "@/hooks/admin/useRoleAssignments";



export default function RoleAssignmentsPage() {
  const params = useParams();
  const { toast } = useToast();

  // Ensure params is not null
  if (!params) {
    return <div>Loading...</div>;
  }

  const roleId = params.roleId as string;

  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("active");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);

  // Use the role assignments hook
  const {
    role,
    assignments = [],
    users = [],
    isLoading,
    selectedAssignmentId,
    setSelectedAssignmentId,
    assignRole,
    isAssigningRole,
    revokeRole,
    isRevokingRole,
    getUserById
  } = useRoleAssignments({ roleId });

  // Filter assignments based on search query and active tab
  const filteredAssignments = assignments.filter((assignment: any) => {
    const user = users.find((u: any) => u.id === assignment.userId);

    const matchesSearch =
      user?.displayName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user?.email.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTab =
      (activeTab === "active" && assignment.isActive) ||
      (activeTab === "inactive" && !assignment.isActive);

    return matchesSearch && matchesTab;
  });

  // Handle role assignment
  const handleAssignRole = async (userId: string) => {
    try {
      // Call the assignRole function from the hook
      assignRole({
        userId,
        roleId
      });

      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("Error assigning role:", error);
      toast({
        title: "Error assigning role",
        description: "An error occurred while assigning the role.",
        variant: "destructive"
      });
    }
  };

  // Handle role assignment removal
  const handleRemoveAssignment = async () => {
    if (!selectedAssignmentId) return;

    try {
      const assignment = assignments.find((a: any) => a.id === selectedAssignmentId);
      if (!assignment) return;

      // Call the revokeRole function from the hook
      revokeRole(selectedAssignmentId);

      setIsRemoveDialogOpen(false);
    } catch (error) {
      console.error("Error removing assignment:", error);
      toast({
        title: "Error removing assignment",
        description: "An error occurred while removing the role assignment.",
        variant: "destructive"
      });
    }
  };

  // Get user initials
  const getUserInitials = (user?: UserType) => {
    if (!user) return "";
    return `${user.firstName?.charAt(0) || ''}${user.lastName?.charAt(0) || ''}`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-12 w-full" />
        <div className="grid grid-cols-1 gap-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (!role) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/admin/roles">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">Role not found</h1>
        </div>
        <p>The role you are looking for does not exist or you do not have permission to view it.</p>
        <Button asChild>
          <Link href="/admin/roles">Back to Roles</Link>
        </Button>
      </div>
    );
  }

  return (
    <AdminOnly>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" asChild>
              <Link href="/admin/roles">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <h1 className="text-2xl font-bold tracking-tight">Role Assignments</h1>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Assign Role
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Assign Role</DialogTitle>
                <DialogDescription>
                  Assign the role "{role.name}" to a user.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Select User</h3>
                  <div className="grid gap-2">
                    {users
                      .filter((user: any) => !assignments.some((a: any) => a.userId === user.id && a.isActive))
                      .map((user: any) => (
                        <Card key={user.id} className="cursor-pointer hover:bg-accent/50" onClick={() => handleAssignRole(user.id)}>
                          <CardContent className="p-3 flex items-center space-x-3">
                            <Avatar>
                              <AvatarImage src={user.avatarUrl} alt={user.displayName} />
                              <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="text-sm font-medium">{user.displayName}</h4>
                              <p className="text-xs text-muted-foreground">{user.email}</p>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{role.name}</CardTitle>
            <CardDescription>{role.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {role.scope} Role
                  {role.isSystemRole && " • System Role"}
                  {role.isDefault && " • Default"}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Created {formatDistanceToNow(new Date(role.createdAt))} ago
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  {filteredAssignments.length} active assignments
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Tabs
            defaultValue="active"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value)}
            className="w-full md:w-auto"
          >
            <TabsList className="grid grid-cols-2 w-full md:w-auto">
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="inactive">Inactive</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {filteredAssignments.length === 0 ? (
          <EmptyState
            icon={<Users className="h-10 w-10 text-muted-foreground" />}
            title={searchQuery ? "No assignments found" : "No assignments"}
            description={
              searchQuery
                ? `No assignments match "${searchQuery}"`
                : "This role has not been assigned to any users yet."
            }
            action={
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Assign Role
              </Button>
            }
          />
        ) : (
          <div className="space-y-4">
            {filteredAssignments.map((assignment: any) => {
              const user = getUserById(assignment.userId);

              return (
                <Card key={assignment.id}>
                  <CardContent className="p-4 flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar>
                        <AvatarImage src={user?.avatarUrl} alt={user?.displayName} />
                        <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="text-sm font-medium">{user?.displayName}</h3>
                        <p className="text-xs text-muted-foreground">{user?.email}</p>
                        <div className="flex items-center mt-1 space-x-2">
                          <Badge variant={assignment.isActive ? "default" : "secondary"}>
                            {assignment.isActive ? "Active" : "Inactive"}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Assigned {formatDistanceToNow(new Date(assignment.assignedAt))} ago
                          </span>
                        </div>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/users/${assignment.userId}`}>
                            <User className="mr-2 h-4 w-4" />
                            View User
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive"
                          onClick={() => {
                            setSelectedAssignmentId(assignment.id);
                            setIsRemoveDialogOpen(true);
                          }}
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Remove Assignment
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Remove Assignment Dialog */}
        <Dialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Remove Role Assignment</DialogTitle>
              <DialogDescription>
                Are you sure you want to remove this role assignment? The user will lose all permissions granted by this role.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setIsRemoveDialogOpen(false);
                  setSelectedAssignmentId(null);
                }}
                disabled={isRevokingRole}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleRemoveAssignment}
                disabled={isRevokingRole}
              >
                {isRevokingRole ? "Removing..." : "Remove"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnly>
  );
}
