"use client";

import { useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { useCreateTemplate } from "@/hooks";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { TemplateForm } from "@/components/templates";
import { EmptyState } from "@/components/empty-state";
import { FileStack } from "lucide-react";
import { useOrganizations } from "@/hooks/organizations";
import { useProjects } from "@/hooks/projects";
import { TemplateStatus, TemplateType } from "@/services/template-service";

function CreateTemplateContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { currentOrganization } = useOrganizations();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get project ID from query params
  const projectId = searchParams ? searchParams.get("projectId") : null;

  // Fetch projects if needed
  const { projects } = useProjects({
    organizationId: currentOrganization?.id
  });

  // Find project if projectId is provided
  const project = projectId && projects
    ? projects.find((p: any) => p.id === projectId)
    : undefined;

  // Create template mutation
  const { mutate: createTemplate } = useCreateTemplate();

  // Handle form submission
  const handleSubmit = async (values: any) => {
    setIsSubmitting(true);

    // Add required categoryId if missing
    const templateData = {
      ...values,
      categoryId: values.categoryId || 'default',
      organizationId: currentOrganization?.id || ''
    };

    try {
      const data = await createTemplate(templateData);
      router.push(`/templates/${data.id}`);
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  // Organization check
  if (!currentOrganization) {
    return (
      <EmptyState
        icon={<FileStack className="h-10 w-10 text-muted-foreground" />}
        title="No organization selected"
        description="Select an organization from the dropdown to create templates"
        action={
          <Button asChild>
            <Link href="/organizations">
              View Organizations
            </Link>
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" asChild>
          <Link href="/templates">
            <ArrowLeft className="h-4 w-4" />
          </Link>
        </Button>
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Create Template</h1>
          <p className="text-muted-foreground">
            Create a new template for your organization
            {project ? ` in project "${project.name}"` : ""}
          </p>
        </div>
      </div>

      <TemplateForm
        organizationId={currentOrganization.id}
        projectId={projectId || undefined}
        onSubmit={handleSubmit}
        isSubmitting={isSubmitting}
        template={{
          id: "",
          name: "",
          description: "",
          type: TemplateType.DOCUMENT,
          organizationId: currentOrganization.id,
          projectId: projectId || undefined,
          content: {
            sections: [],
            styles: {},
            layout: "default"
          },
          fields: [],
          settings: {
            allowPublicAccess: false,
            requireApproval: false,
            enableVersioning: true,
            autoSave: true
          },
          metadata: {},
          tenantId: currentOrganization.id,
          status: TemplateStatus.DRAFT,
          tags: [],
          version: 1,
          createdAt: new Date().toISOString(),
          createdBy: "",
          updatedAt: new Date().toISOString(),
          updatedBy: "",
          isDefault: false
        }}
      />
    </div>
  );
}

export default function CreateTemplatePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CreateTemplateContent />
    </Suspense>
  );
}
