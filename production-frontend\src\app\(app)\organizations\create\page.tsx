"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Building2, Check } from "lucide-react";
import { useOrganizations } from "@/hooks/organizations";
import { OrganizationTier } from "@/types/organization";
import { organizationService } from "@/services";

// Form validation schema
const createOrganizationSchema = z.object({
  name: z.string().min(2, {
    message: "Organization name must be at least 2 characters",
  }).max(50, {
    message: "Organization name must be at most 50 characters",
  }),
  description: z.string().max(500, {
    message: "Description must be at most 500 characters",
  }).optional(),
});

type CreateOrganizationFormValues = z.infer<typeof createOrganizationSchema>;

// Member invitation schema
const inviteMemberSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.enum(["ADMIN", "MEMBER", "VIEWER"], {
    required_error: "Please select a role",
  }),
});

type InviteMemberFormValues = z.infer<typeof inviteMemberSchema>;

export default function CreateOrganizationPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { createOrganization, loading: isCreating, refresh: refetch } = useOrganizations();
  const [invitedMembers, setInvitedMembers] = useState<InviteMemberFormValues[]>([]);

  // Initialize organization form
  const form = useForm<CreateOrganizationFormValues>({
    resolver: zodResolver(createOrganizationSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  // Initialize member invitation form
  const memberForm = useForm<InviteMemberFormValues>({
    resolver: zodResolver(inviteMemberSchema),
    defaultValues: {
      email: "",
      role: "MEMBER",
    },
  });

  // Add member to invitation list
  const addMember = () => {
    const memberData = memberForm.getValues();

    // Validate member form
    const validation = inviteMemberSchema.safeParse(memberData);
    if (!validation.success) {
      toast({
        title: "Invalid member data",
        description: "Please check the email and role fields.",
        variant: "destructive",
      });
      return;
    }

    console.log('Adding member to invitation list:', memberData);

    // Check if email already exists
    if (invitedMembers.some(member => member.email === memberData.email)) {
      toast({
        title: "Member already invited",
        description: "This email address has already been added to the invitation list.",
        variant: "destructive",
      });
      return;
    }

    // Check plan limits (Free plan: max 5 members)
    if (invitedMembers.length >= 5) {
      toast({
        title: "Member limit reached",
        description: "Free plan allows up to 5 team members. Upgrade your plan to add more members.",
        variant: "destructive",
      });
      return;
    }

    setInvitedMembers(prev => [...prev, memberData]);
    memberForm.reset();
    toast({
      title: "Member added",
      description: `${memberData.email} has been added to the invitation list.`,
    });
  };

  // Remove member from invitation list
  const removeMember = (email: string) => {
    setInvitedMembers(prev => prev.filter(member => member.email !== email));
    toast({
      title: "Member removed",
      description: "Member has been removed from the invitation list.",
    });
  };

  // Form submission handler
  const onSubmit = async (data: CreateOrganizationFormValues) => {
    try {
      console.log('Submitting organization creation form:', data);

      // Create organization with FREE tier by default
      const organizationData = {
        ...data,
        tier: OrganizationTier.FREE
      };

      // Create organization and get the result
      const createdOrganization = await organizationService.createOrganization(organizationData);

      toast({
        title: "Organization created",
        description: `${data.name} has been created successfully.`,
      });

      // Send member invitations after organization is created
      if (invitedMembers.length > 0 && createdOrganization?.id) {
        console.log('Sending invitations to members:', invitedMembers);

        try {
          const invitationPromises = invitedMembers.map(async member => {
            try {
              // First, try to find existing user by email
              const userResponse = await fetch('/users/lookup', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: member.email })
              });

              let userId = member.email; // Fallback to email if user not found

              if (userResponse.ok) {
                const userData = await userResponse.json();
                userId = userData.id || userData.userId || member.email;
              }

              return organizationService.addMember(
                createdOrganization.id,
                {
                  userId,
                  role: member.role
                }
              );
            } catch (error) {
              // If user lookup fails, proceed with email-based invitation
              return organizationService.addMember(
                createdOrganization.id,
                {
                  userId: member.email, // Use email as userId for invitation
                  role: member.role
                }
              );
            }
          });

          const invitationResults = await Promise.allSettled(invitationPromises);
          const successfulInvitations = invitationResults.filter(result => result.status === 'fulfilled').length;
          const failedInvitations = invitationResults.filter(result => result.status === 'rejected').length;

          if (successfulInvitations > 0) {
            toast({
              title: "Invitations sent",
              description: `${successfulInvitations} invitation(s) sent successfully${failedInvitations > 0 ? `, ${failedInvitations} failed` : ''}.`,
            });
          }

          if (failedInvitations > 0) {
            console.error('Some invitations failed:', invitationResults.filter(r => r.status === 'rejected'));
          }
        } catch (invitationError) {
          console.error('Failed to send invitations:', invitationError);
          toast({
            title: "Organization created",
            description: "Organization created successfully, but invitations failed to send.",
            variant: "default",
          });
        }
      }

      // Refresh organizations in the store
      await refetch();

      router.push("/organizations");
    } catch (error: any) {
      console.error('Failed to create organization:', error);
      toast({
        title: "Failed to create organization",
        description: error.message || "An error occurred while creating the organization.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-2"
          asChild
        >
          <Link href="/organizations">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">Create Organization</h1>
        <p className="text-muted-foreground">
          Create a new organization to manage projects and team members
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Organization Details</CardTitle>
              <CardDescription>
                Basic information about your organization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Acme Inc." {...field} />
                    </FormControl>
                    <FormDescription>
                      This is the name that will be displayed to your team members.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="A brief description of your organization"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description to help team members understand the purpose of this organization.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Team Members</CardTitle>
              <CardDescription>
                Invite team members to your organization (optional)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Member invitation section */}
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={memberForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={memberForm.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="ADMIN">Admin</SelectItem>
                            <SelectItem value="MEMBER">Member</SelectItem>
                            <SelectItem value="VIEWER">Viewer</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addMember}
                  disabled={invitedMembers.length >= 5}
                >
                  Add Member {invitedMembers.length >= 5 && "(Limit Reached)"}
                </Button>
              </div>

              {/* Invited members list */}
              {invitedMembers.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Invited Members ({invitedMembers.length})</h4>
                  <div className="space-y-2">
                    {invitedMembers.map((member, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-primary">
                              {member.email.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="text-sm font-medium">{member.email}</p>
                            <p className="text-xs text-muted-foreground">{member.role}</p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeMember(member.email)}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-1">Free Plan Features</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Up to 3 projects</li>
                  <li>• Basic document processing</li>
                  <li>• Up to 5 team members ({invitedMembers.length + 1}/5 used)</li>
                  <li>• Community support</li>
                </ul>
                <p className="text-xs text-blue-600 mt-2">
                  You can upgrade your plan anytime from the billing settings.
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button type="submit" disabled={isCreating}>
                {isCreating ? "Creating..." : "Create Organization"}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  );
}
