"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"

const tagVariants = cva(
  "inline-flex items-center rounded-full text-xs font-medium ring-1 ring-inset",
  {
    variants: {
      variant: {
        default: "bg-primary/10 text-primary ring-primary/20",
        secondary: "bg-secondary/10 text-secondary ring-secondary/20",
        destructive: "bg-destructive/10 text-destructive ring-destructive/20",
        outline: "bg-background text-foreground ring-border",
        ghost: "ring-0 bg-transparent text-foreground",
      },
      size: {
        default: "px-2 py-1",
        sm: "px-1.5 py-0.5 text-xs",
        lg: "px-3 py-1.5 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface TagProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof tagVariants> {
  onRemove?: () => void
  icon?: React.ReactNode
  color?: string
}

const Tag = React.forwardRef<HTMLSpanElement, TagProps>(
  ({ className, variant, size, onRemove, icon, color, children, style, ...props }, ref) => {
    // Custom color styles
    const customStyles = color ? {
      backgroundColor: `${color}20`, // 20% opacity
      color: color,
      ringColor: `${color}40`, // 40% opacity
      ...style
    } : style;
    
    return (
      <span
        ref={ref}
        className={cn(tagVariants({ variant, size, className }))}
        style={customStyles}
        {...props}
      >
        {icon && <span className="mr-1">{icon}</span>}
        {children}
        {onRemove && (
          <button
            type="button"
            className="ml-1 -mr-0.5 h-3.5 w-3.5 rounded-full hover:bg-muted/20"
            onClick={(e) => {
              e.stopPropagation()
              onRemove()
            }}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Remove</span>
          </button>
        )}
      </span>
    )
  }
)
Tag.displayName = "Tag"

export interface TagGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  wrap?: boolean
}

const TagGroup = React.forwardRef<HTMLDivElement, TagGroupProps>(
  ({ className, wrap = true, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "flex items-center gap-1",
          wrap && "flex-wrap",
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
TagGroup.displayName = "TagGroup"

export { Tag, TagGroup, tagVariants }
