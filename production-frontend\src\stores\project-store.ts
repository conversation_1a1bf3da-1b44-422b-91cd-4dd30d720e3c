/**
 * Project Store
 * Manages project state, members, tasks, and milestones
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { projectService } from '../services/project-service'
import { storage } from '../lib/utils'
import type {
  ProjectStore,
  ProjectMemberData,
  ID
} from '../types'

interface ProjectStoreState extends ProjectStore {
  _hydrated: boolean
}

export const useProjectStore = create<ProjectStoreState>()(
  persist(
    (set, get) => ({
      // Initial state
      projects: [],
      selectedProject: null,
      members: {},
      loading: false,
      error: null,
      lastUpdated: undefined,
      _hydrated: false,

      // Actions
      fetchProjects: async () => {
        set({ loading: true, error: null })

        try {
          const response = await projectService.getProjects()
          
          set({
            projects: response.data,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to fetch projects',
          })
          throw error
        }
      },

      createProject: async (data: any) => {
        set({ loading: true, error: null })

        try {
          const project = await projectService.createProject(data as any)
          
          set(state => ({
            projects: [project, ...state.projects],
            selectedProject: project,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))

          return project
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to create project',
          })
          throw error
        }
      },

      updateProject: async (projectId: ID, data: any) => {
        set({ loading: true, error: null })

        try {
          const updatedProject = await projectService.updateProject(projectId, data as any)
          
          set(state => ({
            projects: state.projects.map(project => 
              project.id === projectId ? updatedProject : project
            ),
            selectedProject: state.selectedProject?.id === projectId 
              ? updatedProject 
              : state.selectedProject,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to update project',
          })
          throw error
        }
      },

      deleteProject: async (projectId: ID) => {
        set({ loading: true, error: null })

        try {
          await projectService.deleteProject(projectId)
          
          set(state => ({
            projects: state.projects.filter(project => project.id !== projectId),
            selectedProject: state.selectedProject?.id === projectId 
              ? null 
              : state.selectedProject,
            members: Object.fromEntries(
              Object.entries(state.members).filter(([key]) => key !== projectId)
            ),
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to delete project',
          })
          throw error
        }
      },

      selectProject: async (projectId: ID) => {
        set({ loading: true, error: null })

        try {
          const project = await projectService.getProject(projectId)
          
          set({
            selectedProject: project,
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          })

          // Fetch members if not already loaded
          const { members } = get()
          if (!members[projectId]) {
            get().fetchMembers?.(projectId)
          }
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to load project',
          })
          throw error
        }
      },

      addMember: async (projectId: ID, member: ProjectMemberData) => {
        set({ loading: true, error: null })

        try {
          const newMember = await projectService.addMember(projectId, member)
          
          set(state => ({
            members: {
              ...state.members,
              [projectId]: [...(state.members[projectId] || []), newMember],
            },
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to add member',
          })
          throw error
        }
      },

      removeMember: async (projectId: ID, userId: ID) => {
        set({ loading: true, error: null })

        try {
          await projectService.removeMember(projectId, userId)
          
          set(state => ({
            members: {
              ...state.members,
              [projectId]: (state.members[projectId] || []).filter(
                member => member.userId !== userId
              ),
            },
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to remove member',
          })
          throw error
        }
      },

      updateMemberRole: async (projectId: ID, userId: ID, role: string) => {
        set({ loading: true, error: null })

        try {
          const updatedMember = await projectService.updateMemberRole(projectId, userId, role)
          
          set(state => ({
            members: {
              ...state.members,
              [projectId]: (state.members[projectId] || []).map(member =>
                member.userId === userId ? updatedMember : member
              ),
            },
            loading: false,
            error: null,
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'Failed to update member role',
          })
          throw error
        }
      },

      // Helper method to fetch members
      fetchMembers: async (projectId: ID) => {
        try {
          const members = await projectService.getProjectMembers(projectId)
          
          set(state => ({
            members: {
              ...state.members,
              [projectId]: members as any,
            },
            lastUpdated: new Date().toISOString(),
          }))
        } catch (error: any) {
          console.warn('Failed to fetch project members:', error)
        }
      },

      // Computed selectors
      getActiveProjects: () => {
        const { projects } = get()
        return projects.filter(project => project.status === 'active')
      },

      getProjectsByOrganization: (organizationId: ID) => {
        const { projects } = get()
        return projects.filter(project => project.organizationId === organizationId)
      },
    }),
    {
      name: 'project-store',
      storage: createJSONStorage(() => ({
        getItem: (name) => storage.get(name, null),
        setItem: (name, value) => storage.set(name, value),
        removeItem: (name) => storage.remove(name),
      })),
      partialize: (state) => ({
        projects: state.projects.slice(0, 50), // Limit persisted projects
        selectedProject: state.selectedProject,
        members: Object.fromEntries(
          Object.entries(state.members).slice(0, 10) // Limit persisted member data
        ),
        lastUpdated: state.lastUpdated,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state._hydrated = true
        }
      },
    }
  )
)

// Selector hooks
export const useProjects = () => useProjectStore((state) => state.projects)
export const useSelectedProject = () => useProjectStore((state) => state.selectedProject)
export const useProjectMembers = (projectId: ID) => 
  useProjectStore((state) => state.members[projectId] || [])
export const useProjectLoading = () => useProjectStore((state) => state.loading)
export const useProjectError = () => useProjectStore((state) => state.error)

// Action hooks
export const useFetchProjects = () => useProjectStore((state) => state.fetchProjects)
export const useCreateProject = () => useProjectStore((state) => state.createProject)
export const useUpdateProject = () => useProjectStore((state) => state.updateProject)
export const useDeleteProject = () => useProjectStore((state) => state.deleteProject)
export const useSelectProject = () => useProjectStore((state) => state.selectProject)
export const useAddMember = () => useProjectStore((state) => state.addMember)
export const useRemoveMember = () => useProjectStore((state) => state.removeMember)
export const useUpdateMemberRole = () => useProjectStore((state) => state.updateMemberRole)

// Computed hooks
export const useActiveProjects = () =>
  useProjectStore((state) => state.getActiveProjects())

export const useProjectsByOrganization = (organizationId: ID) =>
  useProjectStore((state) => state.getProjectsByOrganization(organizationId))

export const useProjectStats = () => {
  const projects = useProjects()
  
  return {
    total: projects.length,
    byStatus: projects.reduce((acc, project) => {
      acc[project.status] = (acc[project.status] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    byPriority: projects.reduce((acc, project) => {
      const priority = project.priority || 'unknown'
      acc[priority] = (acc[priority] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    averageProgress: projects.length > 0
      ? projects.reduce((acc, project) => acc + (project.progress || 0), 0) / projects.length
      : 0,
  }
}

// Project member utilities
export const useProjectMemberUtils = (projectId: ID) => {
  const members = useProjectMembers(projectId)
  const addMember = useAddMember()
  const removeMember = useRemoveMember()
  const updateMemberRole = useUpdateMemberRole()

  return {
    members,
    memberCount: members.length,
    membersByRole: members.reduce((acc, member) => {
      acc[member.role] = (acc[member.role] || 0) + 1
      return acc
    }, {} as Record<string, number>),
    
    isMember: (userId: ID) => members.some(member => member.userId === userId),
    getMemberRole: (userId: ID) => members.find(member => member.userId === userId)?.role,
    
    inviteMember: (memberData: ProjectMemberData) => addMember(projectId, memberData),
    kickMember: (userId: ID) => removeMember(projectId, userId),
    changeRole: (userId: ID, role: string) => updateMemberRole(projectId, userId, role),
  }
}
