/**
 * Common Hooks
 * Reusable React hooks for common functionality
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { backendApiClient } from '@/services/backend-api-client'

// ============================================================================
// ASYNC STATE HOOKS
// ============================================================================

export interface AsyncState<T> {
  data: T | null
  loading: boolean
  error: string | null
  lastUpdated?: Date
}

/**
 * Hook for managing async state
 */
export function useAsyncState<T>(initialData: T | null = null): [
  AsyncState<T>,
  {
    setData: (data: T) => void
    setLoading: (loading: boolean) => void
    setError: (error: string | null) => void
    reset: () => void
  }
] {
  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    lastUpdated: undefined,
  })

  const setData = useCallback((data: T) => {
    setState(prev => ({
      ...prev,
      data,
      loading: false,
      error: null,
      lastUpdated: new Date(),
    }))
  }, [])

  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }))
  }, [])

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
      loading: false,
    }))
  }, [])

  const reset = useCallback(() => {
    setState({
      data: initialData,
      loading: false,
      error: null,
      lastUpdated: undefined,
    })
  }, [initialData])

  return [state, { setData, setLoading, setError, reset }]
}

// ============================================================================
// DEBOUNCE & THROTTLE HOOKS
// ============================================================================

/**
 * Hook for debouncing values
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Hook for debouncing callbacks
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback)
  const timeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  return useCallback(
    ((...args: any[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args)
      }, delay)
    }) as T,
    [delay]
  )
}

/**
 * Hook for throttling callbacks
 */
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback)
  const lastCallRef = useRef<number>(0)

  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  return useCallback(
    ((...args: any[]) => {
      const now = Date.now()
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now
        callbackRef.current(...args)
      }
    }) as T,
    [delay]
  )
}

// ============================================================================
// LOCAL STORAGE HOOKS
// ============================================================================

/**
 * Hook for managing localStorage with type safety
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value
        setStoredValue(valueToStore)
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      } catch (error) {
        console.error(`Error setting localStorage key "${key}":`, error)
      }
    },
    [key, storedValue]
  )

  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key)
      setStoredValue(initialValue)
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error)
    }
  }, [key, initialValue])

  return [storedValue, setValue, removeValue]
}

// ============================================================================
// PAGINATION HOOKS
// ============================================================================

export interface PaginationState {
  page: number
  pageSize: number
  totalCount: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

/**
 * Hook for managing pagination state
 */
export function usePagination(initialPageSize = 10) {
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    pageSize: initialPageSize,
    totalCount: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  })

  const setPage = useCallback((page: number) => {
    setPagination(prev => ({
      ...prev,
      page: Math.max(1, Math.min(page, prev.totalPages)),
    }))
  }, [])

  const setPageSize = useCallback((pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      pageSize,
      page: 1, // Reset to first page when changing page size
    }))
  }, [])

  const setTotalCount = useCallback((totalCount: number) => {
    setPagination(prev => {
      const totalPages = Math.ceil(totalCount / prev.pageSize)
      const currentPage = Math.min(prev.page, totalPages || 1)
      return {
        ...prev,
        totalCount,
        totalPages,
        page: currentPage,
        hasNext: currentPage < totalPages,
        hasPrev: currentPage > 1,
      }
    })
  }, [])

  const nextPage = useCallback(() => {
    setPage(pagination.page + 1)
  }, [pagination.page, setPage])

  const prevPage = useCallback(() => {
    setPage(pagination.page - 1)
  }, [pagination.page, setPage])

  const firstPage = useCallback(() => {
    setPage(1)
  }, [setPage])

  const lastPage = useCallback(() => {
    setPage(pagination.totalPages)
  }, [pagination.totalPages, setPage])

  return {
    pagination,
    setPage,
    setPageSize,
    setTotalCount,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
  }
}

// ============================================================================
// FORM HOOKS
// ============================================================================

/**
 * Hook for managing form state with validation
 */
export function useFormState<T extends Record<string, any>>(
  initialValues: T,
  validationSchema?: (values: T) => Record<string, string>
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }))
    if (errors[field as string]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }, [errors])

  const setFieldTouched = useCallback((field: keyof T, isTouched = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }))
  }, [])

  const validate = useCallback(() => {
    if (!validationSchema) return true

    const newErrors = validationSchema(values)
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [values, validationSchema])

  const reset = useCallback(() => {
    setValues(initialValues)
    setErrors({})
    setTouched({})
    setIsSubmitting(false)
  }, [initialValues])

  const handleSubmit = useCallback(
    (onSubmit: (values: T) => Promise<void> | void) => {
      return async (e?: React.FormEvent) => {
        e?.preventDefault()
        setIsSubmitting(true)

        try {
          if (validate()) {
            await onSubmit(values)
          }
        } catch (error) {
          console.error('Form submission error:', error)
        } finally {
          setIsSubmitting(false)
        }
      }
    },
    [values, validate]
  )

  return {
    values,
    errors,
    touched,
    isSubmitting,
    setValue,
    setFieldTouched,
    validate,
    reset,
    handleSubmit,
  }
}

// ============================================================================
// WINDOW SIZE HOOK
// ============================================================================

/**
 * Hook for tracking window size
 */
export function useWindowSize() {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  })

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return windowSize
}

// ============================================================================
// PREVIOUS VALUE HOOK
// ============================================================================

/**
 * Hook for tracking previous value
 */
export function usePrevious<T>(value: T): T | undefined {
  const ref = useRef<T>()
  useEffect(() => {
    ref.current = value
  })
  return ref.current
}
