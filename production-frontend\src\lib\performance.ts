/**
 * Performance Monitoring and Optimization Service
 * Tracks Core Web Vitals, API performance, and user interactions
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  url?: string
  userAgent?: string
  connectionType?: string
}

interface APIPerformanceMetric {
  endpoint: string
  method: string
  duration: number
  status: number
  timestamp: number
  size?: number
}

interface UserInteractionMetric {
  type: 'click' | 'scroll' | 'input' | 'navigation'
  element?: string
  duration?: number
  timestamp: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private apiMetrics: APIPerformanceMetric[] = []
  private userMetrics: UserInteractionMetric[] = []
  private observer: PerformanceObserver | null = null
  private isEnabled = true

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeObserver()
      this.trackCoreWebVitals()
      this.trackUserInteractions()
      this.trackNavigationTiming()
    }
  }

  private initializeObserver(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry)
        }
      })

      try {
        this.observer.observe({ entryTypes: ['navigation', 'resource', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] })
      } catch (error) {
        console.warn('Performance observer failed to initialize:', error)
      }
    }
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    const metric: PerformanceMetric = {
      name: entry.name,
      value: entry.duration || (entry as any).value || 0,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      connectionType: (navigator as any).connection?.effectiveType,
    }

    this.metrics.push(metric)

    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100)
    }
  }

  private trackCoreWebVitals(): void {
    // Track Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        
        this.recordMetric('LCP', lastEntry.startTime)
      })

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (error) {
        console.warn('LCP observer failed:', error)
      }
    }

    // Track First Input Delay (FID)
    if ('PerformanceObserver' in window) {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fidEntry = entry as any
          this.recordMetric('FID', fidEntry.processingStart - fidEntry.startTime)
        }
      })

      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (error) {
        console.warn('FID observer failed:', error)
      }
    }

    // Track Cumulative Layout Shift (CLS)
    if ('PerformanceObserver' in window) {
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const layoutShiftEntry = entry as any
          if (!layoutShiftEntry.hadRecentInput) {
            clsValue += layoutShiftEntry.value
          }
        }
        this.recordMetric('CLS', clsValue)
      })

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (error) {
        console.warn('CLS observer failed:', error)
      }
    }
  }

  private trackUserInteractions(): void {
    if (typeof window === 'undefined') return

    // Track clicks
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      this.recordUserInteraction('click', target.tagName.toLowerCase())
    })

    // Track scroll performance
    let scrollStart = 0
    document.addEventListener('scroll', () => {
      if (scrollStart === 0) {
        scrollStart = performance.now()
      }
    }, { passive: true })

    document.addEventListener('scrollend', () => {
      if (scrollStart > 0) {
        const duration = performance.now() - scrollStart
        this.recordUserInteraction('scroll', undefined, duration)
        scrollStart = 0
      }
    })

    // Track input responsiveness
    document.addEventListener('input', (event) => {
      const start = performance.now()
      requestAnimationFrame(() => {
        const duration = performance.now() - start
        this.recordUserInteraction('input', (event.target as HTMLElement).tagName.toLowerCase(), duration)
      })
    })
  }

  private trackNavigationTiming(): void {
    if (typeof window === 'undefined' || !('performance' in window)) return

    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation) {
          this.recordMetric('TTFB', navigation.responseStart - navigation.requestStart)
          this.recordMetric('DOM_LOAD', navigation.domContentLoadedEventEnd - navigation.fetchStart)
          this.recordMetric('FULL_LOAD', navigation.loadEventEnd - navigation.fetchStart)
          this.recordMetric('DNS_LOOKUP', navigation.domainLookupEnd - navigation.domainLookupStart)
          this.recordMetric('TCP_CONNECT', navigation.connectEnd - navigation.connectStart)
        }
      }, 0)
    })
  }

  recordMetric(name: string, value: number): void {
    if (!this.isEnabled) return

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      connectionType: typeof navigator !== 'undefined' ? (navigator as any).connection?.effectiveType : undefined,
    }

    this.metrics.push(metric)
  }

  recordAPIMetric(endpoint: string, method: string, duration: number, status: number, size?: number): void {
    if (!this.isEnabled) return

    const metric: APIPerformanceMetric = {
      endpoint,
      method,
      duration,
      status,
      timestamp: Date.now(),
      size,
    }

    this.apiMetrics.push(metric)

    // Keep only last 50 API metrics
    if (this.apiMetrics.length > 50) {
      this.apiMetrics = this.apiMetrics.slice(-50)
    }
  }

  private recordUserInteraction(type: UserInteractionMetric['type'], element?: string, duration?: number): void {
    if (!this.isEnabled) return

    const metric: UserInteractionMetric = {
      type,
      element,
      duration,
      timestamp: Date.now(),
    }

    this.userMetrics.push(metric)

    // Keep only last 50 user metrics
    if (this.userMetrics.length > 50) {
      this.userMetrics = this.userMetrics.slice(-50)
    }
  }

  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  getAPIMetrics(): APIPerformanceMetric[] {
    return [...this.apiMetrics]
  }

  getUserMetrics(): UserInteractionMetric[] {
    return [...this.userMetrics]
  }

  getPerformanceScore(): number {
    const lcp = this.metrics.find(m => m.name === 'LCP')?.value || 0
    const fid = this.metrics.find(m => m.name === 'FID')?.value || 0
    const cls = this.metrics.find(m => m.name === 'CLS')?.value || 0

    // Simple scoring algorithm (0-100)
    let score = 100

    // LCP scoring (good: <2.5s, needs improvement: 2.5-4s, poor: >4s)
    if (lcp > 4000) score -= 30
    else if (lcp > 2500) score -= 15

    // FID scoring (good: <100ms, needs improvement: 100-300ms, poor: >300ms)
    if (fid > 300) score -= 30
    else if (fid > 100) score -= 15

    // CLS scoring (good: <0.1, needs improvement: 0.1-0.25, poor: >0.25)
    if (cls > 0.25) score -= 30
    else if (cls > 0.1) score -= 15

    return Math.max(0, score)
  }

  enable(): void {
    this.isEnabled = true
  }

  disable(): void {
    this.isEnabled = false
  }

  clear(): void {
    this.metrics = []
    this.apiMetrics = []
    this.userMetrics = []
  }

  exportMetrics(): string {
    return JSON.stringify({
      performance: this.metrics,
      api: this.apiMetrics,
      user: this.userMetrics,
      score: this.getPerformanceScore(),
      timestamp: Date.now(),
    }, null, 2)
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Performance measurement decorator
export function measurePerformance<T extends (...args: any[]) => any>(
  target: any,
  propertyName: string,
  descriptor: TypedPropertyDescriptor<T>
): TypedPropertyDescriptor<T> | void {
  const method = descriptor.value!

  descriptor.value = ((...args: any[]) => {
    const start = performance.now()
    const result = method.apply(target, args)

    if (result instanceof Promise) {
      return result.finally(() => {
        const duration = performance.now() - start
        performanceMonitor.recordMetric(`${target.constructor.name}.${propertyName}`, duration)
      })
    } else {
      const duration = performance.now() - start
      performanceMonitor.recordMetric(`${target.constructor.name}.${propertyName}`, duration)
      return result
    }
  }) as any

  return descriptor
}

// Utility functions
export function measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
  const start = performance.now()
  return fn().finally(() => {
    const duration = performance.now() - start
    performanceMonitor.recordMetric(name, duration)
  })
}

export function measureSync<T>(name: string, fn: () => T): T {
  const start = performance.now()
  const result = fn()
  const duration = performance.now() - start
  performanceMonitor.recordMetric(name, duration)
  return result
}
