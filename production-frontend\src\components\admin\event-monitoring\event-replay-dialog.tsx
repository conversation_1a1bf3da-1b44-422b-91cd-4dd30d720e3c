/**
 * Event Replay Dialog
 * Allows administrators to replay events for a specific aggregate
 */
import React, { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { adminService } from '@/services';
import { EventType } from '@/services/event-grid-service';

// Aggregate types
const AGGREGATE_TYPES = [
  { value: 'document', label: 'Document' },
  { value: 'project', label: 'Project' },
  { value: 'organization', label: 'Organization' },
  { value: 'user', label: 'User' },
  { value: 'comment', label: 'Comment' },
  { value: 'workflow', label: 'Workflow' },
];

// Event types grouped by aggregate type
const EVENT_TYPES_BY_AGGREGATE = {
  document: [
    EventType.DOCUMENT_UPLOADED,
    EventType.DOCUMENT_PROCESSED,
    EventType.DOCUMENT_UPDATED,
    EventType.DOCUMENT_DELETED,
    EventType.DOCUMENT_SHARED,
    EventType.DOCUMENT_COMMENTED,
  ],
  project: [
    EventType.PROJECT_CREATED,
    EventType.PROJECT_UPDATED,
    EventType.PROJECT_DELETED,
    EventType.PROJECT_MEMBER_ADDED,
    EventType.PROJECT_MEMBER_REMOVED,
    EventType.PROJECT_MEMBER_ROLE_UPDATED,
  ],
  organization: [
    EventType.ORGANIZATION_CREATED,
    EventType.ORGANIZATION_UPDATED,
    EventType.ORGANIZATION_DELETED,
    EventType.ORGANIZATION_MEMBER_ADDED,
    EventType.ORGANIZATION_MEMBER_REMOVED,
    EventType.ORGANIZATION_MEMBER_ROLE_UPDATED,
  ],
  user: [
    EventType.USER_REGISTERED,
    EventType.USER_LOGGED_IN,
    EventType.USER_LOGGED_OUT,
    EventType.USER_PROFILE_UPDATED,
  ],
  comment: [
    EventType.COMMENT_CREATED,
    EventType.COMMENT_UPDATED,
    EventType.COMMENT_DELETED,
    EventType.COMMENT_RESOLVED,
    EventType.COMMENT_REOPENED,
  ],
  workflow: [
    EventType.WORKFLOW_CREATED,
    EventType.WORKFLOW_UPDATED,
    EventType.WORKFLOW_DELETED,
    EventType.WORKFLOW_EXECUTED,
    EventType.WORKFLOW_COMPLETED,
    EventType.WORKFLOW_FAILED,
  ],
};

// Form schema
const formSchema = z.object({
  aggregateType: z.string().min(1, 'Aggregate type is required'),
  aggregateId: z.string().min(1, 'Aggregate ID is required'),
  fromVersion: z.string().optional(),
  toVersion: z.string().optional(),
  fromTimestamp: z.string().optional(),
  toTimestamp: z.string().optional(),
  eventTypes: z.array(z.string()).optional(),
  dryRun: z.boolean().default(true),
});

// Form values type
type FormValues = z.infer<typeof formSchema>;

// Event replay dialog props
interface EventReplayDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (replayId: string) => void;
}

// Event replay dialog component
export function EventReplayDialog({
  isOpen,
  onClose,
  onSuccess
}: EventReplayDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableEventTypes, setAvailableEventTypes] = useState<EventType[]>([]);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      aggregateType: '',
      aggregateId: '',
      fromVersion: '',
      toVersion: '',
      fromTimestamp: '',
      toTimestamp: '',
      eventTypes: [],
      dryRun: true,
    },
  });

  // Watch aggregate type to update available event types
  const aggregateType = form.watch('aggregateType');

  // Update available event types when aggregate type changes
  React.useEffect(() => {
    if (aggregateType) {
      setAvailableEventTypes(EVENT_TYPES_BY_AGGREGATE[aggregateType as keyof typeof EVENT_TYPES_BY_AGGREGATE] || []);
      form.setValue('eventTypes', []);
    } else {
      setAvailableEventTypes([]);
    }
  }, [aggregateType, form]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Convert string values to appropriate types
      const fromVersion = values.fromVersion ? parseInt(values.fromVersion) : undefined;
      const toVersion = values.toVersion ? parseInt(values.toVersion) : undefined;

      // Validate version range
      if (fromVersion !== undefined && toVersion !== undefined && fromVersion > toVersion) {
        form.setError('toVersion', {
          type: 'manual',
          message: 'To version must be greater than or equal to from version',
        });
        return;
      }

      const result = await adminService.replayEvents(
        values.aggregateType,
        values.aggregateId,
        {
          fromVersion,
          toVersion,
          fromTimestamp: values.fromTimestamp || undefined,
          toTimestamp: values.toTimestamp || undefined,
          eventTypes: values.eventTypes as EventType[] || undefined,
          dryRun: values.dryRun,
        }
      );

      toast({
        title: values.dryRun ? 'Dry run completed' : 'Events replayed',
        description: `${result.eventsReplayed} events ${values.dryRun ? 'would be' : 'were'} replayed.`,
        variant: 'default',
      });

      if (onSuccess) {
        if (result.replayId) {
          onSuccess(result.replayId);
        }
      }

      onClose();
    } catch (error) {
      toast({
        title: 'Error replaying events',
        description: 'Failed to replay events. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Replay Events</DialogTitle>
          <DialogDescription>
            Replay events for a specific aggregate to recover from failures or rebuild state.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <Alert variant="warning">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>
                Replaying events can cause side effects. Use with caution and start with a dry run.
              </AlertDescription>
            </Alert>

            <FormField
              control={form.control}
              name="aggregateType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Aggregate Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select aggregate type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {AGGREGATE_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    The type of entity to replay events for
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="aggregateId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Aggregate ID</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter aggregate ID"
                      {...field}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    The unique identifier of the entity
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fromVersion"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>From Version</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Optional"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="toVersion"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>To Version</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Optional"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="fromTimestamp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>From Date</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="toTimestamp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>To Date</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {availableEventTypes.length > 0 && (
              <FormField
                control={form.control}
                name="eventTypes"
                render={() => (
                  <FormItem>
                    <div className="mb-2">
                      <FormLabel>Event Types</FormLabel>
                      <FormDescription>
                        Select specific event types to replay (optional)
                      </FormDescription>
                    </div>
                    <div className="grid grid-cols-1 gap-2 max-h-40 overflow-y-auto">
                      {availableEventTypes.map((type) => (
                        <FormField
                          key={type}
                          control={form.control}
                          name="eventTypes"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={type}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(type)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value || [], type])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== type
                                            )
                                          );
                                    }}
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormLabel className="text-sm font-normal">
                                  {type.replace(/_/g, ' ')}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="dryRun"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Dry Run</FormLabel>
                    <FormDescription>
                      Simulate the replay without actually publishing events
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                {form.watch('dryRun') ? 'Simulate Replay' : 'Replay Events'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
