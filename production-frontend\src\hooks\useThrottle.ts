import { useCallback, useRef } from 'react'

/**
 * Throttle Hook
 * Limits the execution of a function to at most once per specified delay
 */

/**
 * Throttle a callback function
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const timeoutRef = useRef<NodeJS.Timeout>()
  const lastCallTimeRef = useRef<number>(0)

  const throttledCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now()
      
      if (now - lastCallTimeRef.current >= delay) {
        lastCallTimeRef.current = now
        return callback(...args)
      }
      
      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      
      // Set timeout for remaining time
      const remainingTime = delay - (now - lastCallTimeRef.current)
      timeoutRef.current = setTimeout(() => {
        lastCallTimeRef.current = Date.now()
        callback(...args)
      }, remainingTime)
    },
    [callback, delay]
  ) as T

  return throttledCallback
}
