import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@/components/ui/charts';
import { Skeleton } from '@/components/ui/skeleton';

interface StorageAnalyticsData {
  totalStorage: number; // in bytes
  usedStorage: number; // in bytes
  storageByType: { type: string; size: number }[];
  storageByProject: { project: string; size: number }[];
  storageGrowth: { date: string; size: number }[];
}

interface StoragePanelProps {
  data?: StorageAnalyticsData;
  isLoading?: boolean;
}

export function StoragePanel({ data, isLoading = false }: StoragePanelProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Skeleton className="h-6 w-48" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-72" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[250px] w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Storage Analytics</CardTitle>
          <CardDescription>No storage data available</CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center h-[250px]">
          <p className="text-muted-foreground">No storage data recorded yet</p>
        </CardContent>
      </Card>
    );
  }

  const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };

  const storagePercentage = (data.usedStorage / data.totalStorage) * 100;

  const storageByTypeData = {
    labels: data.storageByType.map(item => item.type),
    datasets: [
      {
        label: 'Storage Used',
        data: data.storageByType.map(item => item.size),
        backgroundColor: [
          'hsl(var(--primary))',
          'hsl(var(--secondary))',
          'hsl(var(--accent))',
          'hsl(var(--muted))',
          'hsl(var(--destructive))',
        ],
        borderWidth: 1,
      },
    ],
  };

  const storageByProjectData = {
    labels: data.storageByProject.map(item => item.project),
    datasets: [
      {
        label: 'Storage Used (MB)',
        data: data.storageByProject.map(item => item.size / (1024 * 1024)), // Convert to MB
        backgroundColor: 'hsl(var(--primary))',
      },
    ],
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Storage Analytics</CardTitle>
        <CardDescription>
          Storage usage and distribution
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Storage Used</span>
              <span>
                {formatBytes(data.usedStorage)} of {formatBytes(data.totalStorage)}
              </span>
            </div>
            <Progress value={storagePercentage} className="h-2" />
            <p className="text-xs text-muted-foreground text-right">
              {storagePercentage.toFixed(1)}% used
            </p>
          </div>

          <Tabs defaultValue="type">
            <TabsList className="mb-4">
              <TabsTrigger value="type">By File Type</TabsTrigger>
              <TabsTrigger value="project">By Project</TabsTrigger>
            </TabsList>
            <TabsContent value="type" className="h-[200px]">
              <PieChart data={storageByTypeData} />
            </TabsContent>
            <TabsContent value="project" className="h-[200px]">
              <BarChart data={storageByProjectData} />
            </TabsContent>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
