/**
 * Templates Hook
 * Manages template operations and state
 */

import { useState, useCallback, useEffect } from 'react'
import { templateService } from '@/services/template-service'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'
import type { Template } from '@/types/backend'
import { TemplateType, TemplateStatus } from '@/types/backend'

export interface UseTemplatesOptions {
  organizationId?: ID
  projectId?: ID
  category?: string
  isPublic?: boolean
  autoLoad?: boolean
}

export interface UseTemplatesResult {
  templates: Template[]
  data: Template[]
  loading: boolean
  isLoading: boolean
  error: string | null

  loadTemplates: () => Promise<void>
  createTemplate: (data: Partial<Template>) => Promise<Template>
  updateTemplate: (templateId: ID, updates: Partial<Template>) => Promise<void>
  deleteTemplate: (templateId: ID) => Promise<void>
  cloneTemplate: (templateId: ID, name: string) => Promise<Template>

  refresh: () => Promise<void>
}

export function useTemplates(options: UseTemplatesOptions = {}): UseTemplatesResult {
  const { organizationId, projectId, category, isPublic, autoLoad = true } = options
  const { toast } = useToast()
  
  const [templates, setTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadTemplates = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await templateService.getTemplates({
        organizationId,
        projectId,
        category,
        isPublic
      })
      setTemplates(response.data || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load templates'
      setError(errorMessage)

      toast({
        title: 'Loading failed',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [organizationId, projectId, category, isPublic, toast])

  const createTemplate = useCallback(async (data: Partial<Template>): Promise<Template> => {
    try {
      const template = await templateService.createTemplate({
        name: data.name || '',
        description: data.description,
        type: data.type || TemplateType.DOCUMENT,
        status: data.status || TemplateStatus.DRAFT,
        organizationId: data.organizationId || organizationId || '',
        projectId: data.projectId || projectId,
        content: data.content,
        fields: data.fields,
        tags: data.tags,
        categoryId: (data as any).categoryId || 'default'
      })
      await loadTemplates()

      toast({
        title: 'Template created',
        description: `Template "${data.name}" has been created successfully.`,
      })

      return template
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create template'

      toast({
        title: 'Creation failed',
        description: errorMessage,
        variant: 'destructive',
      })

      throw err
    }
  }, [loadTemplates, organizationId, projectId, toast])

  const updateTemplate = useCallback(async (templateId: ID, updates: Partial<Template>) => {
    try {
      await templateService.updateTemplate(templateId, updates)
      await loadTemplates()

      toast({
        title: 'Template updated',
        description: 'Template has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update template'

      toast({
        title: 'Update failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTemplates, toast])

  const deleteTemplate = useCallback(async (templateId: ID) => {
    try {
      await templateService.deleteTemplate(templateId)
      await loadTemplates()

      toast({
        title: 'Template deleted',
        description: 'Template has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete template'

      toast({
        title: 'Deletion failed',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }, [loadTemplates, toast])

  const cloneTemplate = useCallback(async (templateId: ID, name: string): Promise<Template> => {
    try {
      const template = await templateService.cloneTemplate(templateId, name)
      await loadTemplates()

      toast({
        title: 'Template cloned',
        description: `Template has been cloned as "${name}".`,
      })

      return template
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to clone template'

      toast({
        title: 'Clone failed',
        description: errorMessage,
        variant: 'destructive',
      })

      throw err
    }
  }, [loadTemplates, toast])

  useEffect(() => {
    if (autoLoad) {
      loadTemplates()
    }
  }, [autoLoad, loadTemplates])

  return {
    templates,
    data: templates,
    loading,
    isLoading: loading,
    error,
    loadTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    cloneTemplate,
    refresh: loadTemplates,
  }
}

// Individual template hook
export function useTemplate(templateId: ID) {
  const [template, setTemplate] = useState<Template | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const loadTemplate = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const template = await templateService.getTemplate(templateId)
      setTemplate(template)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load template'
      setError(errorMessage)

      toast({
        title: 'Loading failed',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }, [templateId, toast])

  useEffect(() => {
    if (templateId) {
      loadTemplate()
    }
  }, [templateId, loadTemplate])

  return {
    template,
    data: template,
    loading,
    isLoading: loading,
    error,
    isError: !!error,
    refresh: loadTemplate,
    refetch: loadTemplate,
  }
}

export default useTemplates
