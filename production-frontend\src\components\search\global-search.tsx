'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Search, X, Clock, TrendingUp, Filter, Loader2 } from 'lucide-react'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { searchService } from '@/services/search-service'
import { aiService } from '@/services/ai-service'
import { debounce } from '@/lib/utils'
import type { SearchResult, SearchQuery } from '@/services/search-service'

interface GlobalSearchProps {
  isOpen: boolean
  onClose: () => void
  onResultClick?: (result: SearchResult) => void
}

export function GlobalSearch({ isOpen, onClose, onResultClick }: GlobalSearchProps) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [popularQueries, setPopularQueries] = useState<Array<{ query: string; count: number }>>([])
  const [showFilters, setShowFilters] = useState(false)
  const [selectedFilters, setSelectedFilters] = useState<{
    types: string[]
    tags: string[]
    dateRange?: { from: string; to: string }
  }>({
    types: [],
    tags: []
  })

  const inputRef = useRef<HTMLInputElement>(null)

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([])
        return
      }

      setIsLoading(true)
      try {
        const searchParams: SearchQuery = {
          query: searchQuery,
          type: selectedFilters.types.length > 0 ? selectedFilters.types as any : undefined,
          tags: selectedFilters.tags.length > 0 ? selectedFilters.tags : undefined,
          dateRange: selectedFilters.dateRange,
          limit: 10
        }

        const response = await searchService.search(searchParams)
        setResults(response.results)

        // Get AI-powered suggestions
        const aiSuggestions = await searchService.getSuggestions(searchQuery)
        setSuggestions(aiSuggestions)

      } catch (error) {
        console.error('Search error:', error)
        setResults([])
      } finally {
        setIsLoading(false)
      }
    }, 300),
    [selectedFilters]
  )

  // Load initial data
  useEffect(() => {
    if (isOpen) {
      setSearchHistory(searchService.getSearchHistory())
      setPopularQueries(searchService.getPopularQueries(5))
      
      // Focus input when dialog opens
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  // Trigger search when query changes
  useEffect(() => {
    debouncedSearch(query)
  }, [query, debouncedSearch])

  const handleResultClick = (result: SearchResult) => {
    // Record click analytics
    searchService.recordResultClick?.(result.id, query)
    
    // Navigate to result
    if (onResultClick) {
      onResultClick(result)
    } else {
      window.location.href = result.url
    }
    
    onClose()
  }

  const handleHistoryClick = (historyQuery: string) => {
    setQuery(historyQuery)
  }

  const handlePopularClick = (popularQuery: string) => {
    setQuery(popularQuery)
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setSuggestions([])
  }

  const toggleFilter = (type: 'types' | 'tags', value: string) => {
    setSelectedFilters(prev => ({
      ...prev,
      [type]: prev[type].includes(value)
        ? prev[type].filter(item => item !== value)
        : [...prev[type], value]
    }))
  }

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'document':
        return '📄'
      case 'project':
        return '📁'
      case 'workflow':
        return '⚡'
      case 'template':
        return '📋'
      case 'organization':
        return '🏢'
      case 'user':
        return '👤'
      default:
        return '📄'
    }
  }

  const highlightText = (text: string, highlights: string[]) => {
    if (!highlights || highlights.length === 0) return text
    
    let highlightedText = text
    highlights.forEach(highlight => {
      const regex = new RegExp(`(${highlight})`, 'gi')
      highlightedText = highlightedText.replace(regex, '<mark>$1</mark>')
    })
    
    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Global Search
          </DialogTitle>
        </DialogHeader>

        <Command className="rounded-none border-0">
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <CommandInput
              ref={inputRef}
              placeholder="Search documents, projects, workflows..."
              value={query}
              onValueChange={setQuery}
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
            />
            {query && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="ml-2"
            >
              <Filter className="h-4 w-4" />
            </Button>
          </div>

          {showFilters && (
            <div className="p-4 border-b bg-muted/50">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium mb-2 block">Content Types</label>
                  <div className="flex flex-wrap gap-2">
                    {['document', 'project', 'workflow', 'template'].map(type => (
                      <Badge
                        key={type}
                        variant={selectedFilters.types.includes(type) ? 'default' : 'outline'}
                        className="cursor-pointer"
                        onClick={() => toggleFilter('types', type)}
                      >
                        {type}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          <CommandList className="max-h-[400px] overflow-y-auto">
            {isLoading && (
              <div className="flex items-center justify-center py-6">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Searching...</span>
              </div>
            )}

            {!query && !isLoading && (
              <>
                {searchHistory.length > 0 && (
                  <CommandGroup heading="Recent Searches">
                    {searchHistory.slice(0, 5).map((item, index) => (
                      <CommandItem
                        key={index}
                        onSelect={() => handleHistoryClick(item)}
                        className="cursor-pointer"
                      >
                        <Clock className="mr-2 h-4 w-4" />
                        {item}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}

                {popularQueries.length > 0 && (
                  <CommandGroup heading="Popular Searches">
                    {popularQueries.map((item, index) => (
                      <CommandItem
                        key={index}
                        onSelect={() => handlePopularClick(item.query)}
                        className="cursor-pointer"
                      >
                        <TrendingUp className="mr-2 h-4 w-4" />
                        <span>{item.query}</span>
                        <Badge variant="secondary" className="ml-auto">
                          {item.count}
                        </Badge>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
              </>
            )}

            {query && !isLoading && results.length === 0 && (
              <CommandEmpty>
                <div className="text-center py-6">
                  <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No results found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search terms or filters
                  </p>
                </div>
              </CommandEmpty>
            )}

            {results.length > 0 && (
              <CommandGroup heading={`Results (${results.length})`}>
                {results.map((result) => (
                  <CommandItem
                    key={result.id}
                    onSelect={() => handleResultClick(result)}
                    className="cursor-pointer p-4"
                  >
                    <div className="flex items-start gap-3 w-full">
                      <span className="text-lg">{getResultIcon(result.type)}</span>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium truncate">
                            {highlightText(result.title, result.highlights?.title || [])}
                          </h4>
                          <Badge variant="outline" className="text-xs">
                            {result.type}
                          </Badge>
                        </div>
                        {result.description && (
                          <p className="text-sm text-muted-foreground line-clamp-2">
                            {highlightText(result.description, result.highlights?.content || [])}
                          </p>
                        )}
                        <div className="flex items-center gap-2 mt-2">
                          {result.metadata.author && (
                            <span className="text-xs text-muted-foreground">
                              by {result.metadata.author.name}
                            </span>
                          )}
                          <span className="text-xs text-muted-foreground">
                            {new Date(result.metadata.updatedAt).toLocaleDateString()}
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            {Math.round(result.score * 100)}% match
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}

            {suggestions.length > 0 && query && (
              <>
                <Separator />
                <CommandGroup heading="Suggestions">
                  {suggestions.map((suggestion, index) => (
                    <CommandItem
                      key={index}
                      onSelect={() => setQuery(suggestion)}
                      className="cursor-pointer"
                    >
                      <Search className="mr-2 h-4 w-4" />
                      {suggestion}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>
  )
}
