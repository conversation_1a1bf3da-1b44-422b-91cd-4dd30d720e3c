import { useState, useMemo, useCallback } from 'react'

/**
 * Filters Hook
 * Manages filtering state and provides filtering utilities
 */

export type FilterOperator = 
  | 'equals' 
  | 'not_equals' 
  | 'contains' 
  | 'not_contains'
  | 'starts_with'
  | 'ends_with'
  | 'greater_than'
  | 'less_than'
  | 'greater_than_or_equal'
  | 'less_than_or_equal'
  | 'in'
  | 'not_in'
  | 'is_null'
  | 'is_not_null'

export interface Filter<T> {
  id: string
  field: keyof T
  operator: FilterOperator
  value: any
  enabled?: boolean
}

export interface UseFiltersOptions<T> {
  initialFilters?: Filter<T>[]
}

export interface UseFiltersResult<T> {
  filters: Filter<T>[]
  filteredItems: T[]
  activeFilters: Filter<T>[]
  addFilter: (filter: Omit<Filter<T>, 'id'>) => void
  updateFilter: (id: string, updates: Partial<Filter<T>>) => void
  removeFilter: (id: string) => void
  toggleFilter: (id: string) => void
  clearFilters: () => void
  getFilter: (id: string) => Filter<T> | undefined
  hasActiveFilters: boolean
}

export function useFilters<T extends Record<string, any>>(
  items: T[],
  options: UseFiltersOptions<T> = {}
): UseFiltersResult<T> {
  const { initialFilters = [] } = options

  const [filters, setFilters] = useState<Filter<T>[]>(
    initialFilters.map((filter, index) => ({
      ...filter,
      id: filter.id || `filter-${index}`,
    }))
  )

  const activeFilters = useMemo(() => {
    return filters.filter(filter => filter.enabled !== false)
  }, [filters])

  const filteredItems = useMemo(() => {
    if (activeFilters.length === 0) {
      return items
    }

    return items.filter(item => {
      return activeFilters.every(filter => {
        const fieldValue = item[filter.field]
        const filterValue = filter.value

        switch (filter.operator) {
          case 'equals':
            return fieldValue === filterValue

          case 'not_equals':
            return fieldValue !== filterValue

          case 'contains':
            return String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase())

          case 'not_contains':
            return !String(fieldValue).toLowerCase().includes(String(filterValue).toLowerCase())

          case 'starts_with':
            return String(fieldValue).toLowerCase().startsWith(String(filterValue).toLowerCase())

          case 'ends_with':
            return String(fieldValue).toLowerCase().endsWith(String(filterValue).toLowerCase())

          case 'greater_than':
            return Number(fieldValue) > Number(filterValue)

          case 'less_than':
            return Number(fieldValue) < Number(filterValue)

          case 'greater_than_or_equal':
            return Number(fieldValue) >= Number(filterValue)

          case 'less_than_or_equal':
            return Number(fieldValue) <= Number(filterValue)

          case 'in':
            return Array.isArray(filterValue) && filterValue.includes(fieldValue)

          case 'not_in':
            return Array.isArray(filterValue) && !filterValue.includes(fieldValue)

          case 'is_null':
            return fieldValue === null || fieldValue === undefined

          case 'is_not_null':
            return fieldValue !== null && fieldValue !== undefined

          default:
            return true
        }
      })
    })
  }, [items, activeFilters])

  const addFilter = useCallback((filterData: Omit<Filter<T>, 'id'>) => {
    const newFilter: Filter<T> = {
      ...filterData,
      id: `filter-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      enabled: filterData.enabled !== false,
    }
    setFilters(prev => [...prev, newFilter])
  }, [])

  const updateFilter = useCallback((id: string, updates: Partial<Filter<T>>) => {
    setFilters(prev =>
      prev.map(filter =>
        filter.id === id ? { ...filter, ...updates } : filter
      )
    )
  }, [])

  const removeFilter = useCallback((id: string) => {
    setFilters(prev => prev.filter(filter => filter.id !== id))
  }, [])

  const toggleFilter = useCallback((id: string) => {
    setFilters(prev =>
      prev.map(filter =>
        filter.id === id ? { ...filter, enabled: !filter.enabled } : filter
      )
    )
  }, [])

  const clearFilters = useCallback(() => {
    setFilters([])
  }, [])

  const getFilter = useCallback((id: string) => {
    return filters.find(filter => filter.id === id)
  }, [filters])

  const hasActiveFilters = activeFilters.length > 0

  return {
    filters,
    filteredItems,
    activeFilters,
    addFilter,
    updateFilter,
    removeFilter,
    toggleFilter,
    clearFilters,
    getFilter,
    hasActiveFilters,
  }
}

/**
 * Simple text filter hook
 */
export function useTextFilter<T extends Record<string, any>>(
  items: T[],
  searchFields: (keyof T)[],
  searchTerm: string
) {
  return useMemo(() => {
    if (!searchTerm.trim()) {
      return items
    }

    const lowerSearchTerm = searchTerm.toLowerCase()

    return items.filter(item =>
      searchFields.some(field => {
        const fieldValue = item[field]
        return fieldValue && String(fieldValue).toLowerCase().includes(lowerSearchTerm)
      })
    )
  }, [items, searchFields, searchTerm])
}

/**
 * Date range filter hook
 */
export function useDateRangeFilter<T extends Record<string, any>>(
  items: T[],
  dateField: keyof T,
  startDate?: Date,
  endDate?: Date
) {
  return useMemo(() => {
    if (!startDate && !endDate) {
      return items
    }

    return items.filter(item => {
      const itemDate = new Date(item[dateField])
      
      if (startDate && itemDate < startDate) {
        return false
      }
      
      if (endDate && itemDate > endDate) {
        return false
      }
      
      return true
    })
  }, [items, dateField, startDate, endDate])
}
