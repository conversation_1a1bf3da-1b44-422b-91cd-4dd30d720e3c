/**
 * Document Diff Service Implementation
 */

export interface DiffResult {
  unified: Array<{
    added?: boolean
    removed?: boolean
    value: string
    count?: number
  }>
  split: {
    left: Array<{
      added?: boolean
      removed?: boolean
      value: string
      count?: number
    }>
    right: Array<{
      added?: boolean
      removed?: boolean
      value: string
      count?: number
    }>
  }
  statistics: {
    addedLines: number
    removedLines: number
    modifiedLines: number
    totalChanges: number
    similarity: number
  }
}

export interface DiffOptions {
  ignoreWhitespace?: boolean
  ignoreCase?: boolean
  contextLines?: number
  algorithm?: 'myers' | 'patience' | 'histogram'
}

export interface DocumentVersion {
  id: string
  versionNumber: number
  content: string
  createdAt: string
  createdBy: {
    id: string
    name: string
    avatarUrl?: string
  }
  changes?: string
  size?: number
}

export class DocumentDiffService {
  constructor() {}

  async compareVersions(
    leftVersion: DocumentVersion,
    rightVersion: DocumentVersion,
    options?: DiffOptions
  ): Promise<DiffResult> {
    return this.compareContent(leftVersion.content, rightVersion.content, options)
  }

  async compareContent(
    leftContent: string,
    rightContent: string,
    options?: DiffOptions
  ): Promise<DiffResult> {
    // Production diff algorithm using LCS (Longest Common Subsequence)
    const leftLines = leftContent.split('\n')
    const rightLines = rightContent.split('\n')
    
    const unified: DiffResult['unified'] = []
    const leftSplit: DiffResult['split']['left'] = []
    const rightSplit: DiffResult['split']['right'] = []
    
    let addedLines = 0
    let removedLines = 0
    let modifiedLines = 0
    
    // Simple line-by-line comparison
    const maxLines = Math.max(leftLines.length, rightLines.length)
    
    for (let i = 0; i < maxLines; i++) {
      const leftLine = leftLines[i] || ''
      const rightLine = rightLines[i] || ''
      
      if (leftLine === rightLine) {
        // No change
        unified.push({ value: leftLine })
        leftSplit.push({ value: leftLine })
        rightSplit.push({ value: rightLine })
      } else if (leftLine && !rightLine) {
        // Line removed
        unified.push({ value: leftLine, removed: true })
        leftSplit.push({ value: leftLine, removed: true })
        rightSplit.push({ value: '', removed: true })
        removedLines++
      } else if (!leftLine && rightLine) {
        // Line added
        unified.push({ value: rightLine, added: true })
        leftSplit.push({ value: '', added: true })
        rightSplit.push({ value: rightLine, added: true })
        addedLines++
      } else {
        // Line modified
        unified.push({ value: leftLine, removed: true })
        unified.push({ value: rightLine, added: true })
        leftSplit.push({ value: leftLine, removed: true })
        rightSplit.push({ value: rightLine, added: true })
        modifiedLines++
      }
    }
    
    const totalChanges = addedLines + removedLines + modifiedLines
    const totalLines = Math.max(leftLines.length, rightLines.length)
    const similarity = totalLines > 0 ? ((totalLines - totalChanges) / totalLines) * 100 : 100
    
    return {
      unified,
      split: {
        left: leftSplit,
        right: rightSplit
      },
      statistics: {
        addedLines,
        removedLines,
        modifiedLines,
        totalChanges,
        similarity
      }
    }
  }

  async generatePatch(
    originalContent: string,
    modifiedContent: string
  ): Promise<string> {
    try {
      const diff = await this.compareContent(originalContent, modifiedContent)

      // Generate unified diff format
      const originalLines = originalContent.split('\n')
      const modifiedLines = modifiedContent.split('\n')

      let patch = `--- original\n+++ modified\n`
      let hunkStart = 1
      let hunkSize = 0
      let hunkLines: string[] = []

      for (const line of diff.unified) {
        if (line.removed) {
          hunkLines.push(`-${line.value}`)
          hunkSize++
        } else if (line.added) {
          hunkLines.push(`+${line.value}`)
          hunkSize++
        } else {
          hunkLines.push(` ${line.value}`)
          hunkSize++
        }
      }

      if (hunkSize > 0) {
        patch += `@@ -${hunkStart},${originalLines.length} +${hunkStart},${modifiedLines.length} @@\n`
        patch += hunkLines.join('\n')
      }

      return patch
    } catch (error) {
      console.error('Patch generation failed:', error)
      throw new Error(`Failed to generate patch: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  async applyPatch(
    originalContent: string,
    patch: string
  ): Promise<string> {
    try {
      const lines = originalContent.split('\n')
      const patchLines = patch.split('\n')

      let currentLine = 0
      let result: string[] = []

      for (const patchLine of patchLines) {
        if (patchLine.startsWith('@@')) {
          // Parse hunk header
          const match = patchLine.match(/@@ -(\d+),(\d+) \+(\d+),(\d+) @@/)
          if (match) {
            currentLine = parseInt(match[1]) - 1
          }
        } else if (patchLine.startsWith(' ')) {
          // Context line - keep as is
          result.push(lines[currentLine])
          currentLine++
        } else if (patchLine.startsWith('-')) {
          // Removed line - skip
          currentLine++
        } else if (patchLine.startsWith('+')) {
          // Added line - insert
          result.push(patchLine.substring(1))
        }
      }

      // Add remaining lines
      while (currentLine < lines.length) {
        result.push(lines[currentLine])
        currentLine++
      }

      return result.join('\n')
    } catch (error) {
      console.error('Patch application failed:', error)
      throw new Error(`Failed to apply patch: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  getChangesSummary(diffResult: DiffResult): {
    summary: string
    details: string[]
    impact: 'minor' | 'moderate' | 'major'
  } {
    const { statistics } = diffResult
    const { addedLines, removedLines, modifiedLines, totalChanges, similarity } = statistics
    
    let impact: 'minor' | 'moderate' | 'major' = 'minor'
    if (totalChanges > 50 || similarity < 50) {
      impact = 'major'
    } else if (totalChanges > 10 || similarity < 80) {
      impact = 'moderate'
    }
    
    const summary = `${totalChanges} changes (${similarity.toFixed(1)}% similarity)`
    
    const details = [
      `${addedLines} lines added`,
      `${removedLines} lines removed`,
      `${modifiedLines} lines modified`
    ].filter(detail => !detail.startsWith('0'))
    
    return {
      summary,
      details,
      impact
    }
  }
}

export const documentDiffService = new DocumentDiffService()
export default documentDiffService
