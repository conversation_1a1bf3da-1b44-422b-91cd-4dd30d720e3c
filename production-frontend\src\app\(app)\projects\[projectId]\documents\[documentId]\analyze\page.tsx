'use client';

import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { DocumentAnalyzer } from '@/components/documents/document-analyzer';
import { useDocuments } from '@/hooks/documents/useDocuments';
import { useProjects } from '@/hooks/projects/useProjects';

export default function DocumentAnalyzePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const projectId = params?.projectId as string;
  const documentId = params?.documentId as string;

  const { projects } = useProjects({ organizationId: undefined });
  const { documents: documentsResponse } = useDocuments({ projectId });

  const project = projects?.find((p: any) => p.id === projectId);
  const document = documentsResponse?.find((d: any) => d.id === documentId);
  const isLoading = false;
  const error = null;

  useEffect(() => {
    if (error) {
      toast({
        title: 'Error',
        description: 'Failed to load document for analysis',
        variant: 'destructive',
      });
    }
  }, [error, toast]);

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading document for analysis...</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Analyze: {document?.name || 'Document'}</h1>
          <p className="text-muted-foreground">Project: {project?.name}</p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push(`/projects/${projectId}/documents/${documentId}`)}
        >
          Back to Document
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Document Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          {document ? (
            <DocumentAnalyzer document={document} />
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Document not found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
