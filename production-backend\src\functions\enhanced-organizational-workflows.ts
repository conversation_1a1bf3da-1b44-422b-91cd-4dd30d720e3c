/**
 * Enhanced Organizational Workflows
 * Comprehensive paperwork processing system for enterprise organizations
 * Includes approval routing, compliance tracking, digital signatures, and automated processing
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { signalREnhanced } from '../shared/services/signalr';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { enhancedDocumentIntelligence } from '../shared/services/enhanced-document-intelligence';
import { aiServices } from '../shared/services/ai-services';

// Enhanced workflow interfaces for organizational paperwork
export interface OrganizationalWorkflow {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  department: Department;
  complianceFramework: ComplianceFramework[];
  documentTypes: DocumentType[];
  approvalHierarchy: ApprovalLevel[];
  automationRules: AutomationRule[];
  retentionPolicy: RetentionPolicy;
  securityClassification: SecurityClassification;
  status: WorkflowStatus;
  version: string;
  isTemplate: boolean;
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  lastExecuted?: string;
  statistics: WorkflowStatistics;
  settings: OrganizationalWorkflowSettings;
}

export interface ApprovalLevel {
  id: string;
  level: number;
  name: string;
  description: string;
  approverRoles: string[];
  approverUsers: string[];
  requiredApprovals: number;
  escalationTimeHours: number;
  escalationTo: string[];
  conditions: ApprovalCondition[];
  parallelApproval: boolean;
  skipConditions?: SkipCondition[];
}

export interface AutomationRule {
  id: string;
  name: string;
  trigger: AutomationTrigger;
  conditions: RuleCondition[];
  actions: AutomationAction[];
  priority: number;
  enabled: boolean;
  aiAssisted: boolean;
}

export interface DocumentClassification {
  id: string;
  documentId: string;
  classifiedType: DocumentType;
  confidence: number;
  extractedData: Record<string, any>;
  complianceFlags: ComplianceFlag[];
  suggestedWorkflow: string;
  processingRecommendations: ProcessingRecommendation[];
  classifiedAt: string;
  classifiedBy: 'ai' | 'user';
}

export interface ComplianceTracking {
  id: string;
  workflowExecutionId: string;
  framework: ComplianceFramework;
  requirements: ComplianceRequirement[];
  status: ComplianceStatus;
  violations: ComplianceViolation[];
  auditTrail: AuditEntry[];
  lastAssessment: string;
  nextAssessment: string;
  riskLevel: RiskLevel;
}

// Enums
export enum WorkflowCategory {
  HR = 'hr',
  FINANCE = 'finance',
  LEGAL = 'legal',
  PROCUREMENT = 'procurement',
  OPERATIONS = 'operations',
  COMPLIANCE = 'compliance',
  IT = 'it',
  GENERAL = 'general'
}

export enum Department {
  HUMAN_RESOURCES = 'human_resources',
  FINANCE = 'finance',
  LEGAL = 'legal',
  PROCUREMENT = 'procurement',
  OPERATIONS = 'operations',
  IT = 'it',
  EXECUTIVE = 'executive',
  AUDIT = 'audit',
  RISK_MANAGEMENT = 'risk_management'
}

export enum DocumentType {
  INVOICE = 'invoice',
  PURCHASE_ORDER = 'purchase_order',
  CONTRACT = 'contract',
  EMPLOYEE_ONBOARDING = 'employee_onboarding',
  EXPENSE_REPORT = 'expense_report',
  POLICY_DOCUMENT = 'policy_document',
  COMPLIANCE_REPORT = 'compliance_report',
  FINANCIAL_STATEMENT = 'financial_statement',
  LEGAL_DOCUMENT = 'legal_document',
  VENDOR_AGREEMENT = 'vendor_agreement',
  TIMESHEET = 'timesheet',
  LEAVE_REQUEST = 'leave_request',
  PERFORMANCE_REVIEW = 'performance_review',
  BUDGET_REQUEST = 'budget_request',
  AUDIT_REPORT = 'audit_report'
}

export enum ComplianceFramework {
  SOX = 'sox',
  GDPR = 'gdpr',
  HIPAA = 'hipaa',
  PCI_DSS = 'pci_dss',
  ISO_27001 = 'iso_27001',
  SOC2 = 'soc2',
  CCPA = 'ccpa',
  FERPA = 'ferpa',
  FISMA = 'fisma',
  NIST = 'nist'
}

export enum SecurityClassification {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  RESTRICTED = 'restricted',
  TOP_SECRET = 'top_secret'
}

export enum WorkflowStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  ARCHIVED = 'archived',
  DEPRECATED = 'deprecated'
}

export enum ComplianceStatus {
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant',
  PENDING_REVIEW = 'pending_review',
  REQUIRES_ATTENTION = 'requires_attention'
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Supporting interfaces
export interface ApprovalCondition {
  field: string;
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

export interface SkipCondition {
  field: string;
  operator: string;
  value: any;
  reason: string;
}

export interface AutomationTrigger {
  type: 'document_uploaded' | 'approval_completed' | 'deadline_approaching' | 'compliance_violation' | 'custom';
  conditions: RuleCondition[];
}

export interface RuleCondition {
  field: string;
  operator: string;
  value: any;
  type: 'document' | 'user' | 'time' | 'system';
}

export interface AutomationAction {
  type: 'send_notification' | 'assign_task' | 'update_field' | 'trigger_workflow' | 'generate_report' | 'ai_analysis';
  parameters: Record<string, any>;
  delay?: number;
}

export interface RetentionPolicy {
  retentionPeriodYears: number;
  archiveAfterYears: number;
  deleteAfterYears: number;
  legalHoldExempt: boolean;
  complianceRequirements: string[];
}

export interface OrganizationalWorkflowSettings {
  allowParallelExecution: boolean;
  requireDigitalSignature: boolean;
  enableAuditTrail: boolean;
  autoClassifyDocuments: boolean;
  enableAIAssistance: boolean;
  notificationSettings: NotificationSettings;
  escalationSettings: EscalationSettings;
  complianceSettings: ComplianceSettings;
}

export interface NotificationSettings {
  emailEnabled: boolean;
  smsEnabled: boolean;
  inAppEnabled: boolean;
  slackEnabled: boolean;
  teamsEnabled: boolean;
  customWebhooks: string[];
}

export interface EscalationSettings {
  enableAutoEscalation: boolean;
  escalationTimeHours: number;
  maxEscalationLevels: number;
  escalationChain: string[];
}

export interface ComplianceSettings {
  enableComplianceTracking: boolean;
  requiredFrameworks: ComplianceFramework[];
  autoGenerateReports: boolean;
  alertOnViolations: boolean;
}

export interface WorkflowStatistics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageCompletionTimeHours: number;
  averageApprovalTimeHours: number;
  complianceScore: number;
  documentsProcessed: number;
  currentActiveExecutions: number;
}

export interface ComplianceFlag {
  framework: ComplianceFramework;
  requirement: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  remediation: string;
}

export interface ProcessingRecommendation {
  type: 'workflow' | 'approval' | 'classification' | 'compliance';
  recommendation: string;
  confidence: number;
  reasoning: string;
}

export interface ComplianceRequirement {
  id: string;
  framework: ComplianceFramework;
  requirement: string;
  description: string;
  mandatory: boolean;
  evidence: string[];
  status: 'met' | 'not_met' | 'partial' | 'not_applicable';
}

export interface ComplianceViolation {
  id: string;
  requirement: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: string;
  resolvedAt?: string;
  remediation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
}

export interface AuditEntry {
  id: string;
  timestamp: string;
  userId: string;
  action: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  result: 'success' | 'failure' | 'warning';
}

class EnhancedOrganizationalWorkflows {
  private readonly serviceBus = ServiceBusEnhancedService.getInstance();
  private readonly CACHE_TTL = 3600; // 1 hour

  /**
   * Create organizational workflow with enhanced features
   */
  async createOrganizationalWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    if (request.method === 'OPTIONS') {
      const preflightResponse = handlePreflight(request);
      if (preflightResponse) {
        return preflightResponse;
      }
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        name: Joi.string().min(1).max(255).required(),
        description: Joi.string().max(1000).required(),
        category: Joi.string().valid(...Object.values(WorkflowCategory)).required(),
        department: Joi.string().valid(...Object.values(Department)).required(),
        documentTypes: Joi.array().items(Joi.string().valid(...Object.values(DocumentType))).min(1).required(),
        complianceFramework: Joi.array().items(Joi.string().valid(...Object.values(ComplianceFramework))).default([]),
        approvalHierarchy: Joi.array().items(Joi.object({
          level: Joi.number().min(1).required(),
          name: Joi.string().required(),
          approverRoles: Joi.array().items(Joi.string()).default([]),
          approverUsers: Joi.array().items(Joi.string().uuid()).default([]),
          requiredApprovals: Joi.number().min(1).default(1),
          escalationTimeHours: Joi.number().min(1).default(24),
          parallelApproval: Joi.boolean().default(false)
        })).min(1).required(),
        automationRules: Joi.array().items(Joi.object()).default([]),
        retentionPolicy: Joi.object({
          retentionPeriodYears: Joi.number().min(1).default(7),
          archiveAfterYears: Joi.number().min(1).default(3),
          deleteAfterYears: Joi.number().min(1).default(10),
          legalHoldExempt: Joi.boolean().default(false)
        }).default({}),
        securityClassification: Joi.string().valid(...Object.values(SecurityClassification)).default(SecurityClassification.INTERNAL),
        settings: Joi.object().default({})
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkWorkflowPermission(
        authResult.user?.organizationId || '',
        authResult.user?.id || '',
        'create_organizational_workflow'
      );

      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Insufficient permissions to create organizational workflows' }
        }, request);
      }

      const workflowId = uuidv4();
      const now = new Date().toISOString();

      // Process approval hierarchy
      const approvalHierarchy = value.approvalHierarchy.map((level: any, index: number) => ({
        id: uuidv4(),
        level: index + 1,
        name: level.name,
        description: level.description || '',
        approverRoles: level.approverRoles || [],
        approverUsers: level.approverUsers || [],
        requiredApprovals: level.requiredApprovals || 1,
        escalationTimeHours: level.escalationTimeHours || 24,
        escalationTo: level.escalationTo || [],
        conditions: level.conditions || [],
        parallelApproval: level.parallelApproval || false,
        skipConditions: level.skipConditions || []
      }));

      const workflow: OrganizationalWorkflow = {
        id: workflowId,
        name: value.name,
        description: value.description,
        category: value.category,
        department: value.department,
        complianceFramework: value.complianceFramework,
        documentTypes: value.documentTypes,
        approvalHierarchy,
        automationRules: value.automationRules,
        retentionPolicy: {
          retentionPeriodYears: value.retentionPolicy.retentionPeriodYears || 7,
          archiveAfterYears: value.retentionPolicy.archiveAfterYears || 3,
          deleteAfterYears: value.retentionPolicy.deleteAfterYears || 10,
          legalHoldExempt: value.retentionPolicy.legalHoldExempt || false,
          complianceRequirements: value.complianceFramework
        },
        securityClassification: value.securityClassification,
        status: WorkflowStatus.DRAFT,
        version: '1.0.0',
        isTemplate: false,
        organizationId: authResult.user?.organizationId || '',
        createdBy: authResult.user.id,
        createdAt: now,
        updatedAt: now,
        statistics: {
          totalExecutions: 0,
          successfulExecutions: 0,
          failedExecutions: 0,
          averageCompletionTimeHours: 0,
          averageApprovalTimeHours: 0,
          complianceScore: 100,
          documentsProcessed: 0,
          currentActiveExecutions: 0
        },
        settings: this.getDefaultOrganizationalSettings(value.settings)
      };

      // Store workflow
      await db.createItem('organizational-workflows', workflow);

      // Cache workflow
      await redis.setex(`org-workflow:${workflowId}`, this.CACHE_TTL, JSON.stringify(workflow));

      // Log audit event
      await this.logAuditEvent(authResult.user.id, 'organizational_workflow_created', {
        workflowId,
        workflowName: workflow.name,
        category: workflow.category,
        department: workflow.department,
        organizationId: workflow.organizationId
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'OrganizationalWorkflow.Created',
        subject: `workflows/organizational/${workflowId}`,
        data: {
          workflowId,
          name: workflow.name,
          category: workflow.category,
          department: workflow.department,
          organizationId: workflow.organizationId,
          createdBy: authResult.user.id
        }
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: workflow
      }, request);

    } catch (error) {
      logger.error('Error creating organizational workflow', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Private helper methods
  private async checkWorkflowPermission(organizationId: string, userId: string, permission: string): Promise<boolean> {
    try {
      // Check user permissions in organization
      const userRole = await db.queryItems('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.userId = @userId',
        [organizationId, userId]
      );

      if (userRole.length === 0) {
        return false;
      }

      const member = userRole[0] as any;
      const allowedRoles = ['admin', 'workflow_manager', 'department_head'];

      return allowedRoles.includes(member.role) || member.permissions?.includes(permission);
    } catch (error) {
      logger.error('Error checking workflow permission', { organizationId, userId, permission, error });
      return false;
    }
  }

  private getDefaultOrganizationalSettings(customSettings: any = {}): OrganizationalWorkflowSettings {
    return {
      allowParallelExecution: customSettings.allowParallelExecution ?? false,
      requireDigitalSignature: customSettings.requireDigitalSignature ?? true,
      enableAuditTrail: customSettings.enableAuditTrail ?? true,
      autoClassifyDocuments: customSettings.autoClassifyDocuments ?? true,
      enableAIAssistance: customSettings.enableAIAssistance ?? true,
      notificationSettings: {
        emailEnabled: true,
        smsEnabled: false,
        inAppEnabled: true,
        slackEnabled: false,
        teamsEnabled: false,
        customWebhooks: []
      },
      escalationSettings: {
        enableAutoEscalation: true,
        escalationTimeHours: 24,
        maxEscalationLevels: 3,
        escalationChain: []
      },
      complianceSettings: {
        enableComplianceTracking: true,
        requiredFrameworks: customSettings.complianceFramework || [],
        autoGenerateReports: true,
        alertOnViolations: true
      }
    };
  }

  private async logAuditEvent(userId: string, action: string, details: Record<string, any>): Promise<void> {
    try {
      const auditEntry: AuditEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        userId,
        action,
        details,
        ipAddress: 'unknown', // Would be extracted from request in real implementation
        userAgent: 'unknown', // Would be extracted from request in real implementation
        result: 'success'
      };

      await db.createItem('audit-logs', auditEntry);
    } catch (error) {
      logger.error('Failed to log audit event', { userId, action, error });
    }
  }

  /**
   * Classify document and suggest workflow
   */
  async classifyDocumentAndSuggestWorkflow(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const documentId = request.params?.documentId;
      if (!documentId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Document ID is required' }
        }, request);
      }

      // Get document
      const document = await db.readItem('documents', documentId, documentId);
      if (!document) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Document not found' }
        }, request);
      }

      // Perform AI-powered document classification
      const classification = await this.performDocumentClassification(document, authResult.user);

      // Find matching workflows
      const suggestedWorkflows = await this.findMatchingWorkflows(
        classification.classifiedType,
        authResult.user?.organizationId || '',
        classification.extractedData
      );

      // Generate compliance recommendations
      const complianceRecommendations = await this.generateComplianceRecommendations(
        classification,
        suggestedWorkflows
      );

      const result = {
        classification,
        suggestedWorkflows,
        complianceRecommendations,
        autoProcessingAvailable: suggestedWorkflows.length > 0
      };

      return addCorsHeaders({
        status: 200,
        jsonBody: result
      }, request);

    } catch (error) {
      logger.error('Error classifying document', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Execute organizational workflow with enhanced features
   */
  async executeOrganizationalWorkflow(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        workflowId: Joi.string().uuid().required(),
        documentIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
        priority: Joi.string().valid('low', 'normal', 'high', 'urgent').default('normal'),
        dueDate: Joi.string().isoDate().optional(),
        variables: Joi.object().default({}),
        skipApprovalLevels: Joi.array().items(Joi.number()).default([]),
        emergencyExecution: Joi.boolean().default(false),
        complianceOverride: Joi.boolean().default(false)
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Get workflow
      const workflow = await this.getOrganizationalWorkflow(value.workflowId);
      if (!workflow) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Workflow not found' }
        }, request);
      }

      // Check execution permissions
      const hasPermission = await this.checkExecutionPermission(
        workflow,
        authResult.user,
        value.emergencyExecution
      );

      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Insufficient permissions to execute this workflow' }
        }, request);
      }

      // Validate documents
      const documents = await this.validateDocumentsForWorkflow(value.documentIds, workflow);
      if (documents.invalidDocuments.length > 0) {
        return addCorsHeaders({
          status: 400,
          jsonBody: {
            error: 'Invalid documents for workflow',
            invalidDocuments: documents.invalidDocuments
          }
        }, request);
      }

      // Create workflow execution
      const executionId = await this.createWorkflowExecution(workflow, value, authResult.user);

      // Start execution process
      await this.startWorkflowExecution(executionId, workflow, value, authResult.user);

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          executionId,
          status: 'started',
          message: 'Organizational workflow execution initiated successfully'
        }
      }, request);

    } catch (error) {
      logger.error('Error executing organizational workflow', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Get compliance dashboard data
   */
  async getComplianceDashboard(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const url = new URL(request.url);
      const timeRange = url.searchParams.get('timeRange') || '30d';
      const framework = url.searchParams.get('framework');

      // Get compliance data
      const [
        complianceOverview,
        recentViolations,
        upcomingDeadlines,
        riskAssessment
      ] = await Promise.all([
        this.getComplianceOverview(authResult.user?.organizationId || '', timeRange, framework || undefined),
        this.getRecentComplianceViolations(authResult.user?.organizationId || '', 10),
        this.getUpcomingComplianceDeadlines(authResult.user?.organizationId || ''),
        this.getRiskAssessment(authResult.user?.organizationId || '')
      ]);

      const dashboard = {
        overview: complianceOverview,
        recentViolations,
        upcomingDeadlines,
        riskAssessment,
        generatedAt: new Date().toISOString()
      };

      return addCorsHeaders({
        status: 200,
        jsonBody: dashboard
      }, request);

    } catch (error) {
      logger.error('Error getting compliance dashboard', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Additional private helper methods
  private async performDocumentClassification(document: any, _user: any): Promise<DocumentClassification> {
    try {
      // Use AI to classify document
      const classificationPrompt = `Analyze this document and classify it for organizational workflow processing.

Document metadata:
- Filename: ${document.fileName}
- Size: ${document.size} bytes
- Content type: ${document.contentType}

Based on the document content and metadata, determine:
1. Document type (invoice, contract, employee_onboarding, etc.)
2. Key data fields and values
3. Compliance requirements
4. Suggested workflow category

Return as JSON with: classifiedType, confidence, extractedData, complianceFlags, suggestedWorkflow`;

      const aiResponse = await aiServices.reason(classificationPrompt, [], {
        temperature: 0.3,
        maxTokens: 1500
      });

      // Parse AI response
      const classificationData = this.parseClassificationResponse(aiResponse.content);

      const classification: DocumentClassification = {
        id: uuidv4(),
        documentId: document.id,
        classifiedType: classificationData.classifiedType || DocumentType.POLICY_DOCUMENT,
        confidence: classificationData.confidence || 0.7,
        extractedData: classificationData.extractedData || {},
        complianceFlags: classificationData.complianceFlags || [],
        suggestedWorkflow: classificationData.suggestedWorkflow || '',
        processingRecommendations: classificationData.processingRecommendations || [],
        classifiedAt: new Date().toISOString(),
        classifiedBy: 'ai'
      };

      // Store classification
      await db.createItem('document-classifications', classification);

      return classification;
    } catch (error) {
      logger.error('Error performing document classification', { documentId: document.id, error });

      // Return fallback classification
      return {
        id: uuidv4(),
        documentId: document.id,
        classifiedType: DocumentType.POLICY_DOCUMENT,
        confidence: 0.5,
        extractedData: {},
        complianceFlags: [],
        suggestedWorkflow: '',
        processingRecommendations: [],
        classifiedAt: new Date().toISOString(),
        classifiedBy: 'ai'
      };
    }
  }

  private parseClassificationResponse(aiContent: string): any {
    try {
      // Extract JSON from AI response
      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return {};
    } catch (error) {
      logger.error('Error parsing classification response', { error });
      return {};
    }
  }

  private async findMatchingWorkflows(
    documentType: DocumentType,
    organizationId: string,
    _extractedData: Record<string, any>
  ): Promise<OrganizationalWorkflow[]> {
    try {
      const workflows = await db.queryItems('organizational-workflows',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.status = @status AND ARRAY_CONTAINS(c.documentTypes, @documentType)',
        [organizationId, WorkflowStatus.ACTIVE, documentType]
      );

      return workflows as OrganizationalWorkflow[];
    } catch (error) {
      logger.error('Error finding matching workflows', { documentType, organizationId, error });
      return [];
    }
  }

  private async generateComplianceRecommendations(
    classification: DocumentClassification,
    workflows: OrganizationalWorkflow[]
  ): Promise<ProcessingRecommendation[]> {
    const recommendations: ProcessingRecommendation[] = [];

    // Add compliance-based recommendations
    for (const flag of classification.complianceFlags) {
      recommendations.push({
        type: 'compliance',
        recommendation: `Ensure ${flag.framework} compliance: ${flag.description}`,
        confidence: 0.9,
        reasoning: `Document contains ${flag.framework} regulated content`
      });
    }

    // Add workflow recommendations
    if (workflows.length > 0) {
      const bestWorkflow = workflows[0]; // Simplified selection
      recommendations.push({
        type: 'workflow',
        recommendation: `Use ${bestWorkflow.name} workflow for processing`,
        confidence: 0.8,
        reasoning: `Document type matches workflow requirements`
      });
    }

    return recommendations;
  }

  private async getOrganizationalWorkflow(workflowId: string): Promise<OrganizationalWorkflow | null> {
    try {
      // Check cache first
      const cached = await redis.get(`org-workflow:${workflowId}`);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get from database
      const workflow = await db.readItem('organizational-workflows', workflowId, workflowId);
      if (workflow) {
        await redis.setex(`org-workflow:${workflowId}`, this.CACHE_TTL, JSON.stringify(workflow));
      }

      return workflow as OrganizationalWorkflow;
    } catch (error) {
      logger.error('Error getting organizational workflow', { workflowId, error });
      return null;
    }
  }

  private async checkExecutionPermission(
    workflow: OrganizationalWorkflow,
    user: any,
    emergencyExecution: boolean
  ): Promise<boolean> {
    try {
      // Check if user can execute workflows in this department
      const userRole = await db.queryItems('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.userId = @userId',
        [workflow.organizationId, user.id]
      );

      if (userRole.length === 0) {
        return false;
      }

      const member = userRole[0];

      const memberData = member as any;

      // Emergency execution requires special permissions
      if (emergencyExecution) {
        return memberData.role === 'admin' || memberData.permissions?.includes('emergency_workflow_execution');
      }

      // Regular execution permissions
      const allowedRoles = ['admin', 'workflow_manager', 'department_head'];
      const departmentPermission = memberData.departments?.includes(workflow.department);

      return allowedRoles.includes(memberData.role) || departmentPermission;
    } catch (error) {
      logger.error('Error checking execution permission', { workflowId: workflow.id, userId: user.id, error });
      return false;
    }
  }

  private async validateDocumentsForWorkflow(
    documentIds: string[],
    workflow: OrganizationalWorkflow
  ): Promise<{ validDocuments: any[]; invalidDocuments: string[] }> {
    const validDocuments: any[] = [];
    const invalidDocuments: string[] = [];

    for (const documentId of documentIds) {
      try {
        const document = await db.readItem('documents', documentId, documentId);
        if (!document) {
          invalidDocuments.push(documentId);
          continue;
        }

        // Check if document type is supported by workflow
        const classification = await db.queryItems('document-classifications',
          'SELECT * FROM c WHERE c.documentId = @documentId ORDER BY c.classifiedAt DESC OFFSET 0 LIMIT 1',
          [documentId]
        );

        if (classification.length > 0) {
          const docType = (classification[0] as any).classifiedType;
          if (!workflow.documentTypes.includes(docType)) {
            invalidDocuments.push(documentId);
            continue;
          }
        }

        validDocuments.push(document);
      } catch (error) {
        logger.error('Error validating document', { documentId, error });
        invalidDocuments.push(documentId);
      }
    }

    return { validDocuments, invalidDocuments };
  }

  private async createWorkflowExecution(
    workflow: OrganizationalWorkflow,
    executionData: any,
    user: any
  ): Promise<string> {
    const executionId = uuidv4();
    const now = new Date().toISOString();

    const execution = {
      id: executionId,
      workflowId: workflow.id,
      workflowName: workflow.name,
      documentIds: executionData.documentIds,
      status: 'pending',
      priority: executionData.priority,
      dueDate: executionData.dueDate,
      variables: executionData.variables,
      currentApprovalLevel: 1,
      approvalHistory: [],
      complianceTracking: {
        frameworks: workflow.complianceFramework,
        status: ComplianceStatus.PENDING_REVIEW,
        violations: [],
        lastAssessment: now
      },
      auditTrail: [{
        id: uuidv4(),
        timestamp: now,
        userId: user.id,
        action: 'workflow_execution_created',
        details: {
          workflowId: workflow.id,
          documentCount: executionData.documentIds.length,
          priority: executionData.priority
        },
        ipAddress: 'unknown',
        userAgent: 'unknown',
        result: 'success'
      }],
      organizationId: workflow.organizationId,
      department: workflow.department,
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('workflow-executions', execution);
    return executionId;
  }

  private async startWorkflowExecution(
    executionId: string,
    workflow: OrganizationalWorkflow,
    executionData: any,
    user: any
  ): Promise<void> {
    try {
      // Queue workflow execution
      await this.serviceBus.sendToQueue('workflow-executions', {
        body: {
          executionId,
          workflowId: workflow.id,
          action: 'start_execution',
          documentIds: executionData.documentIds,
          priority: executionData.priority,
          initiatedBy: user.id,
          organizationId: workflow.organizationId
        },
        correlationId: executionId,
        messageId: `workflow-exec-${executionId}-${Date.now()}`
      });

      // Send notifications to first approval level
      await this.notifyApprovers(workflow.approvalHierarchy[0], executionId, workflow);

    } catch (error) {
      logger.error('Error starting workflow execution', { executionId, workflowId: workflow.id, error });
      throw error;
    }
  }

  private async notifyApprovers(approvalLevel: ApprovalLevel, executionId: string, workflow: OrganizationalWorkflow): Promise<void> {
    try {
      // Get approvers
      const approvers = await this.getApproversForLevel(approvalLevel, workflow.organizationId);

      // Send notifications
      for (const approver of approvers) {
        await signalREnhanced.sendToUser(approver.userId, {
          target: 'workflow_approval_required',
          arguments: [{
            executionId,
            workflowName: workflow.name,
            approvalLevel: approvalLevel.level,
            dueDate: new Date(Date.now() + approvalLevel.escalationTimeHours * 60 * 60 * 1000).toISOString()
          }]
        });
      }

    } catch (error) {
      logger.error('Error notifying approvers', { executionId, approvalLevel: approvalLevel.id, error });
    }
  }

  private async getApproversForLevel(approvalLevel: ApprovalLevel, organizationId: string): Promise<any[]> {
    try {
      const approvers: any[] = [];

      // Get users by role
      if (approvalLevel.approverRoles.length > 0) {
        const roleApprovers = await db.queryItems('organization-members',
          'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.role IN (@roles)',
          [organizationId, approvalLevel.approverRoles]
        );
        approvers.push(...roleApprovers);
      }

      // Get specific users
      if (approvalLevel.approverUsers.length > 0) {
        for (const userId of approvalLevel.approverUsers) {
          const user = await db.readItem('users', userId, userId);
          if (user) {
            approvers.push({ userId, ...user });
          }
        }
      }

      return approvers;
    } catch (error) {
      logger.error('Error getting approvers for level', { approvalLevel: approvalLevel.id, error });
      return [];
    }
  }

  private async getComplianceOverview(_organizationId: string, _timeRange: string, _framework?: string): Promise<any> {
    // Implementation for compliance overview
    return {
      totalWorkflows: 0,
      compliantWorkflows: 0,
      violationsCount: 0,
      complianceScore: 95
    };
  }

  private async getRecentComplianceViolations(_organizationId: string, _limit: number): Promise<ComplianceViolation[]> {
    // Implementation for recent violations
    return [];
  }

  private async getUpcomingComplianceDeadlines(_organizationId: string): Promise<any[]> {
    // Implementation for upcoming deadlines
    return [];
  }

  private async getRiskAssessment(_organizationId: string): Promise<any> {
    // Implementation for risk assessment
    return {
      overallRisk: RiskLevel.LOW,
      riskFactors: [],
      recommendations: []
    };
  }
}

// Create instance
const organizationalWorkflows = new EnhancedOrganizationalWorkflows();

// Register HTTP functions
app.http('organizational-workflow-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/organizational',
  handler: (request, context) => organizationalWorkflows.createOrganizationalWorkflow(request, context)
});

app.http('document-classify-suggest-workflow', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/classify-workflow',
  handler: (request, context) => organizationalWorkflows.classifyDocumentAndSuggestWorkflow(request, context)
});

app.http('organizational-workflow-execute', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'workflows/organizational/execute',
  handler: (request, context) => organizationalWorkflows.executeOrganizationalWorkflow(request, context)
});

app.http('compliance-dashboard', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'compliance/dashboard',
  handler: (request, context) => organizationalWorkflows.getComplianceDashboard(request, context)
});
