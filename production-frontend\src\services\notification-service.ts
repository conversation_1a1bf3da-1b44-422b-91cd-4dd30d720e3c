/**
 * Notification Service
 * Handles in-app notifications, push notifications, and email notifications
 */

import { backendApiClient } from './backend-api-client'
import type { ID } from '../types'

export interface Notification {
  id: ID
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  data?: Record<string, any>
  read: boolean
  userId: ID
  createdAt: string
  updatedAt: string
  expiresAt?: string
  actionUrl?: string
  actionText?: string
  category?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
}

export interface NotificationPreferences {
  userId: ID
  email: {
    enabled: boolean
    frequency: 'immediate' | 'daily' | 'weekly' | 'never'
    types: string[]
  }
  push: {
    enabled: boolean
    types: string[]
  }
  inApp: {
    enabled: boolean
    types: string[]
  }
  quiet: {
    enabled: boolean
    startTime: string
    endTime: string
  }
}

export interface CreateNotificationRequest {
  type: Notification['type']
  title: string
  message: string
  userId?: ID
  userIds?: ID[]
  data?: Record<string, any>
  actionUrl?: string
  actionText?: string
  category?: string
  priority?: Notification['priority']
  expiresAt?: string
  sendEmail?: boolean
  sendPush?: boolean
}

class NotificationService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
  }

  /**
   * Get notifications for current user
   */
  async getNotifications(options?: {
    unreadOnly?: boolean
    category?: string
    limit?: number
    offset?: number
  }): Promise<{
    notifications: Notification[]
    total: number
    unreadCount: number
  }> {
    const params = new URLSearchParams()
    if (options?.unreadOnly) params.append('unreadOnly', 'true')
    if (options?.category) params.append('category', options.category)
    if (options?.limit) params.append('limit', options.limit.toString())
    if (options?.offset) params.append('offset', options.offset.toString())

    const response = await backendApiClient.request(`/notifications?${params}`)
    return response as any
  }

  /**
   * Get single notification
   */
  async getNotification(notificationId: ID): Promise<Notification> {
    const response = await backendApiClient.request(`/notifications/${notificationId}`)
    return response as Notification
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: ID): Promise<void> {
    await backendApiClient.request(`/notifications/${notificationId}/read`, { method: 'PUT' })
  }

  /**
   * Mark notification as unread
   */
  async markAsUnread(notificationId: ID): Promise<void> {
    await backendApiClient.request(`/notifications/${notificationId}/unread`, { method: 'PUT' })
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    await backendApiClient.request('/notifications/read-all', { method: 'PUT' })
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: ID): Promise<void> {
    await backendApiClient.request(`/notifications/${notificationId}`, { method: 'DELETE' })
  }

  /**
   * Delete all notifications
   */
  async deleteAllNotifications(): Promise<void> {
    await backendApiClient.request('/notifications', { method: 'DELETE' })
  }

  /**
   * Bulk operations
   */
  async bulkMarkAsRead(notificationIds: ID[]): Promise<void> {
    await backendApiClient.request('/notifications/bulk/read', {
      method: 'PUT',
      body: JSON.stringify({ notificationIds })
    })
  }

  async bulkDelete(notificationIds: ID[]): Promise<void> {
    await backendApiClient.request('/notifications/bulk', {
      method: 'DELETE',
      body: JSON.stringify({ notificationIds })
    })
  }

  /**
   * Create notification (admin only)
   */
  async createNotification(request: CreateNotificationRequest): Promise<Notification> {
    const response = await backendApiClient.request('/notifications', {
      method: 'POST',
      body: JSON.stringify(request)
    })
    return response as Notification
  }

  /**
   * Send notification to specific users (admin only)
   */
  async sendToUsers(userIds: ID[], notification: Omit<CreateNotificationRequest, 'userId' | 'userIds'>): Promise<void> {
    await backendApiClient.request('/notifications/send', {
      method: 'POST',
      body: JSON.stringify({
        ...notification,
        userIds
      })
    })
  }

  /**
   * Send broadcast notification (admin only)
   */
  async broadcast(notification: Omit<CreateNotificationRequest, 'userId' | 'userIds'>): Promise<void> {
    await backendApiClient.request('/notifications/broadcast', {
      method: 'POST',
      body: JSON.stringify(notification)
    })
  }

  /**
   * Get notification preferences
   */
  async getPreferences(): Promise<NotificationPreferences> {
    const response = await backendApiClient.request('/notifications/preferences')
    return response as NotificationPreferences
  }

  /**
   * Update notification preferences
   */
  async updatePreferences(preferences: Partial<NotificationPreferences>): Promise<NotificationPreferences> {
    const response = await backendApiClient.request('/notifications/preferences', {
      method: 'PUT',
      body: JSON.stringify(preferences)
    })
    return response as NotificationPreferences
  }

  /**
   * Get notification categories
   */
  async getCategories(): Promise<Array<{
    id: string
    name: string
    description: string
    defaultEnabled: boolean
  }>> {
    return await backendApiClient.request('/notifications/categories')
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(subscription: PushSubscription): Promise<void> {
    await backendApiClient.request('/notifications/push/subscribe', {
      method: 'POST',
      body: JSON.stringify({
        endpoint: subscription.endpoint,
        keys: {
          p256dh: subscription.getKey('p256dh'),
          auth: subscription.getKey('auth')
        }
      })
    })
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPush(): Promise<void> {
    await backendApiClient.request('/notifications/push/unsubscribe', {
      method: 'POST'
    })
  }

  /**
   * Test notification delivery
   */
  async testNotification(type: 'email' | 'push' | 'inApp'): Promise<void> {
    await backendApiClient.request('/notifications/test', {
      method: 'POST',
      body: JSON.stringify({ type })
    })
  }

  /**
   * Get notification statistics
   */
  async getStatistics(): Promise<{
    total: number
    unread: number
    byType: Record<string, number>
    byCategory: Record<string, number>
    recentActivity: Array<{
      date: string
      count: number
    }>
  }> {
    return await backendApiClient.request('/notifications/statistics')
  }

  /**
   * Real-time notifications setup using WebSocket
   */
  setupRealTimeNotifications(callback: (notification: Notification) => void): () => void {
    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout | null = null;

    const connect = () => {
      try {
        const wsUrl = `${this.baseUrl.replace('http', 'ws')}/notifications/ws`;
        ws = new WebSocket(wsUrl);

        ws.onopen = () => {
          console.log('Real-time notifications connected');

          // Clear any reconnection timeout
          if (reconnectTimeout) {
            clearTimeout(reconnectTimeout);
            reconnectTimeout = null;
          }
        };

        ws.onmessage = (event) => {
          try {
            const notification = JSON.parse(event.data);
            callback(notification);
          } catch (error) {
            console.error('Error parsing notification:', error);
          }
        };

        ws.onclose = () => {
          console.log('Real-time notifications disconnected');

          // Attempt to reconnect after 5 seconds
          reconnectTimeout = setTimeout(() => {
            console.log('Attempting to reconnect to notifications...');
            connect();
          }, 5000);
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
        };

      } catch (error) {
        console.error('Failed to establish WebSocket connection:', error);

        // Fallback to polling if WebSocket fails
        const interval = setInterval(async () => {
          try {
            const { notifications } = await this.getNotifications({ unreadOnly: true, limit: 1 });
            if (notifications.length > 0) {
              callback(notifications[0]);
            }
          } catch (error) {
            console.error('Error fetching notifications via polling:', error);
          }
        }, 30000); // Poll every 30 seconds

        return () => clearInterval(interval);
      }
    };

    // Initial connection
    connect();

    // Return cleanup function
    return () => {
      if (ws) {
        ws.close();
      }
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
    };
  }

  /**
   * Export notifications
   */
  async exportNotifications(format: 'json' | 'csv' = 'json'): Promise<Blob> {
    try {
      const response = await fetch(`${this.baseUrl}/notifications/export?format=${format}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`)
      }

      return await response.blob()
    } catch (error) {
      console.error('Notification export failed:', error)
      throw new Error(`Failed to export notifications: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Archive old notifications
   */
  async archiveOldNotifications(olderThanDays: number = 30): Promise<{
    archived: number
    deleted: number
  }> {
    const response = await backendApiClient.request('/notifications/archive', {
      method: 'POST',
      body: JSON.stringify({ olderThanDays })
    })
    return response as any
  }

  /**
   * Get notification templates (admin only)
   */
  async getTemplates(): Promise<Array<{
    id: string
    name: string
    subject: string
    body: string
    type: string
    variables: string[]
  }>> {
    return await backendApiClient.request('/notifications/templates')
  }

  /**
   * Create notification template (admin only)
   */
  async createTemplate(template: {
    name: string
    subject: string
    body: string
    type: string
    variables?: string[]
  }): Promise<void> {
    await backendApiClient.request('/notifications/templates', {
      method: 'POST',
      body: JSON.stringify(template)
    })
  }

  /**
   * Update notification template (admin only)
   */
  async updateTemplate(templateId: string, updates: {
    name?: string
    subject?: string
    body?: string
    variables?: string[]
  }): Promise<void> {
    await backendApiClient.request(`/notifications/templates/${templateId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    })
  }

  /**
   * Delete notification template (admin only)
   */
  async deleteTemplate(templateId: string): Promise<void> {
    await backendApiClient.request(`/notifications/templates/${templateId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Send notification using template (admin only)
   */
  async sendFromTemplate(templateId: string, data: {
    userIds?: ID[]
    variables?: Record<string, any>
    broadcast?: boolean
  }): Promise<void> {
    await backendApiClient.request(`/notifications/templates/${templateId}/send`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
export default notificationService
