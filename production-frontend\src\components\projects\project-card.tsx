"use client";

import React, { memo, useMemo } from "react";
import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Users,
  Calendar,
  Tag,
  FolderKanban,
  GitBranch,
  Activity
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Project } from "@/types/project";
import { cn } from "@/lib/utils";

interface ProjectCardProps {
  project: Project;
  className?: string;
  showActions?: boolean;
}

export const ProjectCard = memo(function ProjectCard({
  project,
  className,
  showActions = true
}: ProjectCardProps) {
  // Memoized computed values
  const { visibilityVariant, visibilityLabel } = useMemo(() => ({
    visibilityVariant: (project.visibility === 'PUBLIC' ? "default" :
      project.visibility === 'ORGANIZATION' ? "secondary" : "outline") as "default" | "secondary" | "outline",
    visibilityLabel: project.visibility === 'PUBLIC' ? "Public" :
      project.visibility === 'ORGANIZATION' ? "Organization" : "Private"
  }), [project.visibility]);

  const projectStats = useMemo(() => ({
    documentCount: project.documentIds?.length || project.documentCount || 0,
    memberCount: project.memberCount || 0,
    hasWorkflows: project.workflowIds && project.workflowIds.length > 0,
    hasAutoProcessing: project.settings?.autoProcessing,
    createdAgo: formatDistanceToNow(new Date(project.createdAt))
  }), [project.documentIds, project.documentCount, project.memberCount, project.workflowIds, project.settings?.autoProcessing, project.createdAt]);

  const displayTags = useMemo(() => {
    if (!project.tags || project.tags.length === 0) return null;
    return {
      visible: project.tags.slice(0, 3),
      remaining: project.tags.length > 3 ? project.tags.length - 3 : 0
    };
  }, [project.tags]);

  return (
    <Card className={cn("w-full hover:shadow-md transition-shadow", className)}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-lg flex items-center gap-2">
            <FolderKanban className="h-5 w-5 text-primary" />
            {project.name}
          </CardTitle>
          <Badge variant={visibilityVariant}>{visibilityLabel}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        {project.description && (
          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{project.description}</p>
        )}

        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 text-sm">
            <FileText size={14} className="text-muted-foreground" />
            <span>{projectStats.documentCount} Documents</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Users size={14} className="text-muted-foreground" />
            <span>{projectStats.memberCount} Members</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <GitBranch size={14} className="text-muted-foreground" />
            <span>{projectStats.hasWorkflows ? 'Workflows Enabled' : 'No Workflows'}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Activity size={14} className="text-muted-foreground" />
            <span>{projectStats.hasAutoProcessing ? 'Auto-Processing' : 'Manual Processing'}</span>
          </div>
        </div>

        {/* Tags section */}
        {displayTags && (
          <div className="flex items-center gap-2 text-sm mb-4">
            <Tag size={14} className="text-muted-foreground flex-shrink-0" />
            <div className="flex flex-wrap gap-1">
              {displayTags.visible.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">{tag}</Badge>
              ))}
              {displayTags.remaining > 0 && (
                <Badge variant="outline" className="text-xs">+{displayTags.remaining}</Badge>
              )}
            </div>
          </div>
        )}

        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Calendar size={12} />
          <span>Created {projectStats.createdAgo} ago</span>
        </div>
      </CardContent>

      {showActions && (
        <CardFooter className="flex gap-2">
          <Button asChild variant="default" className="flex-1">
            <Link href={`/projects/${project.id}`}>
              View Details
            </Link>
          </Button>
          <Button asChild variant="outline" className="flex-1">
            <Link href={`/documents/upload?projectId=${project.id}&organizationId=${project.organizationId}`}>
              Upload Document
            </Link>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
});
