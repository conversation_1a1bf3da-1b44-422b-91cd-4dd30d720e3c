/**
 * Azure Event Hub Service with Azure Identity Authentication
 * Provides secure, managed identity-based access to Event Hubs
 * Supports both analytics and integration event streaming
 */

import { EventHubProducerClient, EventDataBatch } from '@azure/event-hubs';
import { DefaultAzureCredential, ChainedTokenCredential, ManagedIdentityCredential, AzureCliCredential, EnvironmentCredential } from '@azure/identity';
import { logger } from '../utils/logger';

export interface EventHubConfig {
  namespaceName: string;
  analyticsHubName: string;
  integrationHubName: string;
  enabled: boolean;
}

export interface EventData {
  eventType: string;
  subject: string;
  data: any;
  organizationId?: string;
  userId?: string;
  correlationId?: string;
  timestamp?: string;
}

export interface EventHubMetrics {
  totalEvents: number;
  successfulEvents: number;
  failedEvents: number;
  averageProcessingTime: number;
  lastEventTime?: string;
}

/**
 * Event Hub Service with Azure Identity
 */
export class EventHubService {
  private analyticsClient: EventHubProducerClient | null = null;
  private integrationClient: EventHubProducerClient | null = null;
  private credential: DefaultAzureCredential | null = null;
  private config: EventHubConfig;
  private isInitialized = false;
  private metrics: EventHubMetrics = {
    totalEvents: 0,
    successfulEvents: 0,
    failedEvents: 0,
    averageProcessingTime: 0
  };

  constructor() {
    this.config = {
      namespaceName: process.env.EVENT_HUB_NAMESPACE_NAME || '',
      analyticsHubName: process.env.EVENT_HUB_ANALYTICS_NAME || 'analytics-events',
      integrationHubName: process.env.EVENT_HUB_INTEGRATION_NAME || 'integration-events',
      enabled: process.env.EVENT_HUB_ENABLED === 'true'
    };
  }

  /**
   * Initialize Event Hub service with Azure Identity
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      if (!this.config.enabled) {
        logger.info('Event Hub service is disabled');
        return;
      }

      if (!this.config.namespaceName) {
        logger.warn('Event Hub namespace name not configured - service will be disabled');
        return;
      }

      // Create credential chain for flexible authentication
      this.credential = new ChainedTokenCredential(
        new ManagedIdentityCredential(), // For Azure-hosted services
        new AzureCliCredential(),        // For local development
        new EnvironmentCredential()      // For service principal auth
      );

      // Initialize Analytics Event Hub client
      try {
        const analyticsEndpoint = `https://${this.config.namespaceName}.servicebus.windows.net`;
        this.analyticsClient = new EventHubProducerClient(
          analyticsEndpoint,
          this.config.analyticsHubName,
          this.credential
        );
        logger.info('Analytics Event Hub client initialized', {
          namespace: this.config.namespaceName,
          hubName: this.config.analyticsHubName
        });
      } catch (error) {
        logger.error('Failed to initialize Analytics Event Hub client', {
          error: error instanceof Error ? error.message : String(error)
        });
      }

      // Initialize Integration Event Hub client
      try {
        const integrationEndpoint = `https://${this.config.namespaceName}.servicebus.windows.net`;
        this.integrationClient = new EventHubProducerClient(
          integrationEndpoint,
          this.config.integrationHubName,
          this.credential
        );
        logger.info('Integration Event Hub client initialized', {
          namespace: this.config.namespaceName,
          hubName: this.config.integrationHubName
        });
      } catch (error) {
        logger.error('Failed to initialize Integration Event Hub client', {
          error: error instanceof Error ? error.message : String(error)
        });
      }

      this.isInitialized = true;
      logger.info('Event Hub service initialized successfully with Azure Identity');

    } catch (error) {
      logger.error('Failed to initialize Event Hub service', {
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Send analytics event
   */
  public async sendAnalyticsEvent(eventData: EventData): Promise<boolean> {
    return this.sendEvent(this.analyticsClient, eventData, 'analytics');
  }

  /**
   * Send integration event
   */
  public async sendIntegrationEvent(eventData: EventData): Promise<boolean> {
    return this.sendEvent(this.integrationClient, eventData, 'integration');
  }

  /**
   * Send event to specified Event Hub
   */
  private async sendEvent(
    client: EventHubProducerClient | null,
    eventData: EventData,
    hubType: 'analytics' | 'integration'
  ): Promise<boolean> {
    const startTime = Date.now();

    try {
      if (!client) {
        logger.debug(`${hubType} Event Hub client not available - skipping event`, {
          eventType: eventData.eventType
        });
        return false;
      }

      // Ensure event has required fields
      const enrichedEvent = {
        ...eventData,
        timestamp: eventData.timestamp || new Date().toISOString(),
        correlationId: eventData.correlationId || this.generateCorrelationId()
      };

      // Create batch and send
      const batch: EventDataBatch = await client.createBatch();
      const eventAdded = batch.tryAdd({
        body: enrichedEvent,
        properties: {
          eventType: enrichedEvent.eventType,
          organizationId: enrichedEvent.organizationId,
          timestamp: enrichedEvent.timestamp,
          hubType
        }
      });

      if (!eventAdded) {
        logger.warn('Event too large for batch', { eventType: eventData.eventType });
        this.updateMetrics(false, startTime);
        return false;
      }

      await client.sendBatch(batch);

      this.updateMetrics(true, startTime);
      logger.debug(`Event sent to ${hubType} Event Hub`, {
        eventType: eventData.eventType,
        correlationId: enrichedEvent.correlationId
      });

      return true;

    } catch (error) {
      this.updateMetrics(false, startTime);
      logger.error(`Failed to send event to ${hubType} Event Hub`, {
        error: error instanceof Error ? error.message : String(error),
        eventType: eventData.eventType
      });
      return false;
    }
  }

  /**
   * Send multiple events in batch
   */
  public async sendBatchEvents(
    events: EventData[],
    hubType: 'analytics' | 'integration'
  ): Promise<{ successful: number; failed: number }> {
    const client = hubType === 'analytics' ? this.analyticsClient : this.integrationClient;
    
    if (!client) {
      logger.warn(`${hubType} Event Hub client not available for batch send`);
      return { successful: 0, failed: events.length };
    }

    let successful = 0;
    let failed = 0;

    try {
      const batch = await client.createBatch();

      for (const eventData of events) {
        const enrichedEvent = {
          ...eventData,
          timestamp: eventData.timestamp || new Date().toISOString(),
          correlationId: eventData.correlationId || this.generateCorrelationId()
        };

        const eventAdded = batch.tryAdd({
          body: enrichedEvent,
          properties: {
            eventType: enrichedEvent.eventType,
            organizationId: enrichedEvent.organizationId,
            timestamp: enrichedEvent.timestamp,
            hubType
          }
        });

        if (eventAdded) {
          successful++;
        } else {
          failed++;
          logger.warn('Event too large for batch', { eventType: eventData.eventType });
        }
      }

      if (successful > 0) {
        await client.sendBatch(batch);
        logger.info(`Batch sent to ${hubType} Event Hub`, { successful, failed });
      }

    } catch (error) {
      logger.error(`Failed to send batch to ${hubType} Event Hub`, {
        error: error instanceof Error ? error.message : String(error),
        eventCount: events.length
      });
      failed = events.length;
      successful = 0;
    }

    return { successful, failed };
  }

  /**
   * Get service metrics
   */
  public getMetrics(): EventHubMetrics {
    return { ...this.metrics };
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.config.enabled) {
        return true; // Service is disabled, consider healthy
      }

      // Simple connectivity test - try to create a batch
      if (this.analyticsClient) {
        await this.analyticsClient.createBatch();
      }

      if (this.integrationClient) {
        await this.integrationClient.createBatch();
      }

      return true;
    } catch (error) {
      logger.error('Event Hub health check failed', {
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  /**
   * Close connections
   */
  public async close(): Promise<void> {
    try {
      if (this.analyticsClient) {
        await this.analyticsClient.close();
        this.analyticsClient = null;
      }

      if (this.integrationClient) {
        await this.integrationClient.close();
        this.integrationClient = null;
      }

      this.isInitialized = false;
      logger.info('Event Hub service closed');
    } catch (error) {
      logger.error('Error closing Event Hub service', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Update metrics
   */
  private updateMetrics(success: boolean, startTime: number): void {
    this.metrics.totalEvents++;
    
    if (success) {
      this.metrics.successfulEvents++;
    } else {
      this.metrics.failedEvents++;
    }

    const processingTime = Date.now() - startTime;
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime * (this.metrics.totalEvents - 1) + processingTime) / 
      this.metrics.totalEvents;

    this.metrics.lastEventTime = new Date().toISOString();
  }

  /**
   * Generate correlation ID
   */
  private generateCorrelationId(): string {
    return `evt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
  }
}

// Export singleton instance
export const eventHubService = new EventHubService();
