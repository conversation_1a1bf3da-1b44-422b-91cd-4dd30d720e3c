/**
 * useOrganizationalWorkflows Hook
 * React hook for managing organizational paperwork processing workflows
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../useAuth';
import { useToast } from '../use-toast';
import { organizationalWorkflowService } from '../../services/organizational-workflow-service';
import {
  type OrganizationalWorkflow,
  type WorkflowExecution,
  type DocumentClassification,
  type ComplianceDashboard,
  WorkflowCategory,
  Department,
  DocumentType,
  WorkflowStatus,
  ExecutionStatus,
  Priority,
  ComplianceFramework,
  SecurityClassification,
  type ApprovalLevel,
  type AutomationRule,
  type RetentionPolicy,
  type OrganizationalWorkflowSettings
} from '../../services/organizational-workflow-service';

interface UseOrganizationalWorkflowsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableRealTimeUpdates?: boolean;
}

interface WorkflowFilters {
  category?: WorkflowCategory;
  department?: Department;
  status?: WorkflowStatus;
  documentType?: DocumentType;
  search?: string;
}

interface ExecutionFilters {
  workflowId?: string;
  status?: ExecutionStatus;
  department?: Department;
  priority?: Priority;
  dateRange?: {
    start: string;
    end: string;
  };
}

export function useOrganizationalWorkflows(options: UseOrganizationalWorkflowsOptions = {}) {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // State management
  const [workflows, setWorkflows] = useState<OrganizationalWorkflow[]>([]);
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [complianceDashboard, setComplianceDashboard] = useState<ComplianceDashboard | null>(null);
  const [selectedWorkflow, setSelectedWorkflow] = useState<OrganizationalWorkflow | null>(null);
  const [selectedExecution, setSelectedExecution] = useState<WorkflowExecution | null>(null);
  
  // Loading states
  const [loadingWorkflows, setLoadingWorkflows] = useState(false);
  const [loadingExecutions, setLoadingExecutions] = useState(false);
  const [loadingCompliance, setLoadingCompliance] = useState(false);
  const [loadingClassification, setLoadingClassification] = useState(false);
  const [loadingExecution, setLoadingExecution] = useState(false);
  
  // Filter states
  const [workflowFilters, setWorkflowFilters] = useState<WorkflowFilters>({});
  const [executionFilters, setExecutionFilters] = useState<ExecutionFilters>({});
  
  // Pagination states
  const [workflowPagination, setWorkflowPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false
  });
  
  const [executionPagination, setExecutionPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false
  });
  
  // Error state
  const [error, setError] = useState<string | null>(null);

  /**
   * Load organizational workflows
   */
  const loadWorkflows = useCallback(async (filters: WorkflowFilters = {}, reset = false) => {
    if (!user) return;

    try {
      setLoadingWorkflows(true);
      setError(null);

      const workflows = await organizationalWorkflowService.getOrganizationalWorkflows(filters);
      
      if (reset) {
        setWorkflows(workflows);
      } else {
        setWorkflows(prev => [...prev, ...workflows]);
      }

      setWorkflowPagination(prev => ({
        ...prev,
        total: workflows.length,
        hasMore: workflows.length === prev.limit
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load workflows';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
    } finally {
      setLoadingWorkflows(false);
    }
  }, [user, toast]);

  /**
   * Load workflow executions
   */
  const loadExecutions = useCallback(async (filters: ExecutionFilters = {}, reset = false) => {
    if (!user) return;

    try {
      setLoadingExecutions(true);
      setError(null);

      const offset = reset ? 0 : executions.length;
      const result = await organizationalWorkflowService.getWorkflowExecutions({
        ...filters,
        limit: executionPagination.limit,
        offset
      });
      
      if (reset) {
        setExecutions(result.executions);
      } else {
        setExecutions(prev => [...prev, ...result.executions]);
      }

      setExecutionPagination(prev => ({
        ...prev,
        total: result.total,
        hasMore: result.hasMore
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load executions';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
    } finally {
      setLoadingExecutions(false);
    }
  }, [user, toast, executions.length, executionPagination.limit]);

  /**
   * Load compliance dashboard
   */
  const loadComplianceDashboard = useCallback(async (timeRange = '30d', framework?: ComplianceFramework) => {
    if (!user) return;

    try {
      setLoadingCompliance(true);
      setError(null);

      const dashboard = await organizationalWorkflowService.getComplianceDashboard({
        timeRange,
        framework
      });
      
      setComplianceDashboard(dashboard);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load compliance dashboard';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
    } finally {
      setLoadingCompliance(false);
    }
  }, [user, toast]);

  /**
   * Create organizational workflow
   */
  const createWorkflow = useCallback(async (workflowData: {
    name: string;
    description: string;
    category: WorkflowCategory;
    department: Department;
    documentTypes: DocumentType[];
    complianceFramework?: ComplianceFramework[];
    approvalHierarchy: Partial<ApprovalLevel>[];
    automationRules?: AutomationRule[];
    retentionPolicy?: Partial<RetentionPolicy>;
    securityClassification?: SecurityClassification;
    settings?: Partial<OrganizationalWorkflowSettings>;
  }) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setError(null);

      const workflow = await organizationalWorkflowService.createOrganizationalWorkflow(workflowData);
      
      // Add to workflows list
      setWorkflows(prev => [workflow, ...prev]);

      toast({
        title: 'Success',
        description: 'Organizational workflow created successfully'
      });

      return workflow;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create workflow';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    }
  }, [user, toast]);

  /**
   * Execute organizational workflow
   */
  const executeWorkflow = useCallback(async (executionData: {
    workflowId: string;
    documentIds: string[];
    priority?: Priority;
    dueDate?: string;
    variables?: Record<string, any>;
    skipApprovalLevels?: number[];
    emergencyExecution?: boolean;
    complianceOverride?: boolean;
  }) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoadingExecution(true);
      setError(null);

      const result = await organizationalWorkflowService.executeOrganizationalWorkflow(executionData);
      
      // Refresh executions
      await loadExecutions(executionFilters, true);

      toast({
        title: 'Success',
        description: result.message
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to execute workflow';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    } finally {
      setLoadingExecution(false);
    }
  }, [user, toast, loadExecutions, executionFilters]);

  /**
   * Classify document and suggest workflow
   */
  const classifyDocument = useCallback(async (documentId: string) => {
    if (!user) throw new Error('User not authenticated');

    try {
      setLoadingClassification(true);
      setError(null);

      const result = await organizationalWorkflowService.classifyDocumentAndSuggestWorkflow(documentId);
      
      toast({
        title: 'Success',
        description: 'Document classified successfully'
      });

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to classify document';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive'
      });
      throw error;
    } finally {
      setLoadingClassification(false);
    }
  }, [user, toast]);

  /**
   * Filter workflows
   */
  const filterWorkflows = useCallback((filters: WorkflowFilters) => {
    setWorkflowFilters(filters);
    setWorkflowPagination(prev => ({ ...prev, page: 1 }));
    loadWorkflows(filters, true);
  }, [loadWorkflows]);

  /**
   * Filter executions
   */
  const filterExecutions = useCallback((filters: ExecutionFilters) => {
    setExecutionFilters(filters);
    setExecutionPagination(prev => ({ ...prev, page: 1 }));
    loadExecutions(filters, true);
  }, [loadExecutions]);

  /**
   * Load more workflows
   */
  const loadMoreWorkflows = useCallback(() => {
    if (!loadingWorkflows && workflowPagination.hasMore) {
      setWorkflowPagination(prev => ({ ...prev, page: prev.page + 1 }));
      loadWorkflows(workflowFilters, false);
    }
  }, [loadingWorkflows, workflowPagination.hasMore, loadWorkflows, workflowFilters]);

  /**
   * Load more executions
   */
  const loadMoreExecutions = useCallback(() => {
    if (!loadingExecutions && executionPagination.hasMore) {
      setExecutionPagination(prev => ({ ...prev, page: prev.page + 1 }));
      loadExecutions(executionFilters, false);
    }
  }, [loadingExecutions, executionPagination.hasMore, loadExecutions, executionFilters]);

  /**
   * Refresh all data
   */
  const refreshAll = useCallback(async () => {
    await Promise.all([
      loadWorkflows(workflowFilters, true),
      loadExecutions(executionFilters, true),
      loadComplianceDashboard()
    ]);
  }, [loadWorkflows, loadExecutions, loadComplianceDashboard, workflowFilters, executionFilters]);

  /**
   * Clear cache
   */
  const clearCache = useCallback(() => {
    organizationalWorkflowService.clearCache();
  }, []);

  // Computed values
  const workflowsByCategory = useMemo(() => {
    return workflows.reduce((acc, workflow) => {
      const category = workflow.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(workflow);
      return acc;
    }, {} as Record<WorkflowCategory, OrganizationalWorkflow[]>);
  }, [workflows]);

  const workflowsByDepartment = useMemo(() => {
    return workflows.reduce((acc, workflow) => {
      const department = workflow.department;
      if (!acc[department]) {
        acc[department] = [];
      }
      acc[department].push(workflow);
      return acc;
    }, {} as Record<Department, OrganizationalWorkflow[]>);
  }, [workflows]);

  const executionsByStatus = useMemo(() => {
    return executions.reduce((acc, execution) => {
      const status = execution.status;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(execution);
      return acc;
    }, {} as Record<ExecutionStatus, WorkflowExecution[]>);
  }, [executions]);

  const activeExecutions = useMemo(() => {
    return executions.filter(execution => 
      execution.status === ExecutionStatus.RUNNING || 
      execution.status === ExecutionStatus.PENDING
    );
  }, [executions]);

  const completedExecutions = useMemo(() => {
    return executions.filter(execution => execution.status === ExecutionStatus.COMPLETED);
  }, [executions]);

  const failedExecutions = useMemo(() => {
    return executions.filter(execution => execution.status === ExecutionStatus.FAILED);
  }, [executions]);

  // Initial load
  useEffect(() => {
    if (user) {
      loadWorkflows(workflowFilters, true);
      loadExecutions(executionFilters, true);
      loadComplianceDashboard();
    }
  }, [user]); // Only depend on user to avoid infinite loops

  // Auto-refresh
  useEffect(() => {
    if (options.autoRefresh && user) {
      const interval = setInterval(() => {
        refreshAll();
      }, options.refreshInterval || 30000); // Default 30 seconds

      return () => clearInterval(interval);
    }
  }, [options.autoRefresh, options.refreshInterval, user, refreshAll]);

  return {
    // Data
    workflows,
    executions,
    complianceDashboard,
    selectedWorkflow,
    selectedExecution,
    
    // Computed data
    workflowsByCategory,
    workflowsByDepartment,
    executionsByStatus,
    activeExecutions,
    completedExecutions,
    failedExecutions,
    
    // Loading states
    loadingWorkflows,
    loadingExecutions,
    loadingCompliance,
    loadingClassification,
    loadingExecution,
    
    // Filters
    workflowFilters,
    executionFilters,
    
    // Pagination
    workflowPagination,
    executionPagination,
    
    // Error state
    error,
    
    // Actions
    createWorkflow,
    executeWorkflow,
    classifyDocument,
    filterWorkflows,
    filterExecutions,
    loadMoreWorkflows,
    loadMoreExecutions,
    refreshAll,
    clearCache,
    
    // Selection
    setSelectedWorkflow,
    setSelectedExecution,
    
    // Data loading
    loadWorkflows,
    loadExecutions,
    loadComplianceDashboard
  };
}
