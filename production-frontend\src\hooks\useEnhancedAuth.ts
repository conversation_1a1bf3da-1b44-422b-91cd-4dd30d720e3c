/**
 * Enhanced Authentication Hook
 * Production-ready MSAL Azure AD B2C authentication
 */

import { useCallback, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { msalAuthService, B2CPolicyType, AuthMethod } from '@/lib/auth/msal-service'
import { useAuthStore } from '@/stores/auth-store'
import { useNotificationStore } from '@/stores/notification-store'
import type { StandardToken, UserContext } from '@/types/backend'

// Re-export types from MSAL service
export { AuthMethod, B2CPolicyType } from '@/lib/auth/msal-service'

// Authentication state interface
export interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: UserContext | null
  token: StandardToken | null
  error: string | null
}

// Authentication actions interface
export interface AuthActions {
  signIn: (method?: AuthMethod) => Promise<void>
  signUp: (method?: AuthMethod) => Promise<void>
  signOut: (method?: AuthMethod) => Promise<void>
  resetPassword: (method?: AuthMethod) => Promise<void>
  editProfile: (method?: AuthMethod) => Promise<void>
  refreshToken: () => Promise<void>
  clearError: () => void
}

// Enhanced authentication hook return type
export interface UseEnhancedAuthReturn extends AuthState, AuthActions {
  // Utility methods
  hasRole: (role: string) => boolean
  hasPermission: (permission: string) => boolean
  isInOrganization: (organizationId: string) => boolean
  getAccessToken: () => Promise<string | null>
}

/**
 * Enhanced Authentication Hook
 * Production-ready MSAL Azure AD B2C authentication
 */
export function useEnhancedAuth(): UseEnhancedAuthReturn {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)

  // Zustand stores
  const {
    user,
    token,
    isAuthenticated,
    updateProfile: storeUpdateProfile,
    setToken: storeSetToken,
    clearAuth: storeClearAuth,
  } = useAuthStore()

  const { addNotification } = useNotificationStore()

  /**
   * Initialize MSAL and check authentication status
   */
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true)

        // Initialize MSAL service
        await msalAuthService.initialize()

        // Handle any pending redirect promise
        const redirectResult = await msalAuthService.handleRedirectPromise()
        if (redirectResult) {
          console.log('[Auth Hook] Redirect authentication completed')
        }

        // Check if user is already authenticated
        if (msalAuthService.isAuthenticated()) {
          const currentUser = await msalAuthService.getCurrentUser()
          const accessToken = await msalAuthService.getAccessToken()

          if (currentUser && accessToken) {
            // Update store with current user data
            await storeUpdateProfile(currentUser)

            // Create token object
            const tokenData: StandardToken = {
              accessToken,
              refreshToken: '', // B2C doesn't provide refresh tokens in browser
              userId: currentUser.id,
              email: currentUser.email,
              roles: currentUser.roles,
              organizationIds: currentUser.organizationIds,
              tenantId: currentUser.tenantId,
              expiresAt: Date.now() + 3600000, // 1 hour default
              scope: 'openid profile email',
              tokenType: 'Bearer'
            }

            storeSetToken(tokenData)
          }
        } else {
          // Clear auth store if not authenticated
          storeClearAuth()
        }

        setIsInitialized(true)
      } catch (error) {
        console.error('[Auth Hook] Initialization failed:', error)
        setError(error instanceof Error ? error.message : 'Authentication initialization failed')
        setIsInitialized(true)
      } finally {
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [storeUpdateProfile, storeSetToken, storeClearAuth])

  /**
   * Sign in with Azure AD B2C
   */
  const signIn = useCallback(async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      setError('Authentication service not initialized')
      return
    }

    setError(null)
    setIsLoading(true)

    try {
      const tokenData = await msalAuthService.signIn(B2CPolicyType.SIGN_UP_SIGN_IN, method)

      // Update auth store with token
      storeSetToken(tokenData)

      // Get user profile
      const userProfile = await msalAuthService.getCurrentUser()
      if (userProfile) {
        await storeUpdateProfile(userProfile)
      }

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Welcome!',
        message: `Successfully signed in as ${tokenData.email}`
      })

      // Redirect to dashboard
      router.push('/dashboard')

    } catch (error: any) {
      console.error('[Auth Hook] Sign in failed:', error)
      setError(error.message || 'Failed to sign in. Please try again.')

      // Show error notification
      addNotification({
        type: 'error',
        title: 'Sign In Failed',
        message: error.message || 'Failed to sign in. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }, [isInitialized, storeSetToken, storeUpdateProfile, addNotification, router])

  /**
   * Sign up new user
   */
  const signUp = useCallback(async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      setError('Authentication service not initialized')
      return
    }

    setError(null)
    setIsLoading(true)

    try {
      const tokenData = await msalAuthService.signUp(method)

      // Update auth store with token
      storeSetToken(tokenData)

      // Get user profile
      const userProfile = await msalAuthService.getCurrentUser()
      if (userProfile) {
        await storeUpdateProfile(userProfile)
      }

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Account Created!',
        message: `Welcome ${tokenData.email}! Your account has been created successfully.`
      })

      // Redirect to dashboard
      router.push('/dashboard')

    } catch (error: any) {
      console.error('[Auth Hook] Sign up failed:', error)
      setError(error.message || 'Failed to create account. Please try again.')

      // Show error notification
      addNotification({
        type: 'error',
        title: 'Sign Up Failed',
        message: error.message || 'Failed to create account. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }, [isInitialized, storeSetToken, storeUpdateProfile, addNotification, router])

  /**
   * Sign out user
   */
  const signOut = useCallback(async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      setError('Authentication service not initialized')
      return
    }

    setError(null)
    setIsLoading(true)

    try {
      await msalAuthService.signOut(method)

      // Clear auth store
      storeClearAuth()

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Signed Out',
        message: 'You have been successfully signed out.'
      })

      // Redirect to login
      router.push('/auth/enhanced-login')

    } catch (error: any) {
      console.error('[Auth Hook] Sign out failed:', error)
      setError(error.message || 'Failed to sign out. Please try again.')

      // Show error notification
      addNotification({
        type: 'error',
        title: 'Sign Out Failed',
        message: error.message || 'Failed to sign out. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }, [isInitialized, storeClearAuth, addNotification, router])

  /**
   * Reset password
   */
  const resetPassword = useCallback(async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      setError('Authentication service not initialized')
      return
    }

    setError(null)
    setIsLoading(true)

    try {
      await msalAuthService.resetPassword(method)

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Password Reset',
        message: 'Password reset process has been initiated. Please follow the instructions.'
      })

    } catch (error: any) {
      console.error('[Auth Hook] Password reset failed:', error)
      setError(error.message || 'Failed to initiate password reset. Please try again.')

      // Show error notification
      addNotification({
        type: 'error',
        title: 'Password Reset Failed',
        message: error.message || 'Failed to initiate password reset. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }, [isInitialized, addNotification])

  /**
   * Edit profile
   */
  const editProfile = useCallback(async (method: AuthMethod = AuthMethod.POPUP) => {
    if (!isInitialized) {
      setError('Authentication service not initialized')
      return
    }

    setError(null)
    setIsLoading(true)

    try {
      const result = await msalAuthService.editProfile(method)

      // If we got a new token, update the store
      if (result && 'accessToken' in result) {
        storeSetToken(result)
      }

      // Refresh user profile
      const updatedProfile = await msalAuthService.getCurrentUser()
      if (updatedProfile) {
        await storeUpdateProfile(updatedProfile)
      }

      // Show success notification
      addNotification({
        type: 'success',
        title: 'Profile Updated',
        message: 'Your profile has been updated successfully.'
      })

    } catch (error: any) {
      console.error('[Auth Hook] Profile edit failed:', error)
      setError(error.message || 'Failed to update profile. Please try again.')

      // Show error notification
      addNotification({
        type: 'error',
        title: 'Profile Update Failed',
        message: error.message || 'Failed to update profile. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }, [isInitialized, storeSetToken, storeUpdateProfile, addNotification])

  /**
   * Refresh access token (B2C compatible)
   * Note: Azure AD B2C doesn't provide refresh tokens in browser environments
   * This method attempts to silently acquire a new token from MSAL
   */
  const refreshToken = useCallback(async () => {
    if (!isInitialized) {
      console.warn('[Auth Hook] Cannot refresh token: MSAL not initialized')
      return
    }

    try {
      console.log('[Auth Hook] Attempting silent token refresh for B2C')
      const accessToken = await msalAuthService.getAccessToken()

      if (accessToken && user) {
        const tokenData: StandardToken = {
          accessToken,
          refreshToken: '', // B2C doesn't provide refresh tokens in browser
          userId: user.id,
          email: user.email,
          roles: user.roles,
          organizationIds: user.organizationIds,
          tenantId: user.tenantId,
          expiresAt: Date.now() + 3600000, // 1 hour default
          scope: 'openid profile email',
          tokenType: 'Bearer'
        }

        storeSetToken(tokenData)
        console.log('[Auth Hook] Token refreshed successfully')
      } else {
        console.warn('[Auth Hook] No access token or user available for refresh')
      }
    } catch (error) {
      console.error('[Auth Hook] Token refresh failed:', error)
      // For B2C, silent refresh failures are common and expected
      // Don't show notifications for silent failures
      throw error // Re-throw so calling code can handle appropriately
    }
  }, [isInitialized, user, storeSetToken])

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  /**
   * Get access token
   */
  const getAccessToken = useCallback(async (): Promise<string | null> => {
    if (!isInitialized) return null
    return await msalAuthService.getAccessToken()
  }, [isInitialized])

  /**
   * Check if user has specific role
   */
  const hasRole = useCallback((role: string): boolean => {
    return user?.roles?.includes(role) || false
  }, [user])

  /**
   * Check if user has specific permission
   */
  const hasPermission = useCallback((permission: string): boolean => {
    return user?.permissions?.includes(permission) || false
  }, [user])

  /**
   * Check if user is in specific organization
   */
  const isInOrganization = useCallback((organizationId: string): boolean => {
    return user?.organizationIds?.includes(organizationId) || false
  }, [user])

  return {
    // State
    isAuthenticated: isAuthenticated && isInitialized,
    isLoading: isLoading || !isInitialized,
    user,
    token,
    error,

    // Actions
    signIn,
    signUp,
    signOut,
    resetPassword,
    editProfile,
    refreshToken,
    clearError,

    // Utilities
    hasRole,
    hasPermission,
    isInOrganization,
    getAccessToken,
  }
}

// Export for backward compatibility
export default useEnhancedAuth
