"use client";

import { ReactNode } from "react";
import { useAuth } from "@/hooks/useAuth";
import { PermissionAction, PermissionUtils } from "@/lib/permissions";
import { SystemRole } from "@/types/role";

interface PermissionGuardProps {
  /**
   * Permission required to render children
   */
  permission?: PermissionAction;

  /**
   * Multiple permissions required (all must be present)
   */
  permissions?: PermissionAction[];

  /**
   * Multiple permissions where any one is sufficient
   */
  anyPermission?: PermissionAction[];

  /**
   * Content to render if user has permission
   */
  children: ReactNode;

  /**
   * Content to render if user doesn't have permission
   */
  fallback?: ReactNode;

  /**
   * Whether to hide the component completely if user doesn't have permission
   */
  hideIfNoPermission?: boolean;

  /**
   * Organization ID for organization-specific permissions
   */
  organizationId?: string;

  /**
   * Project ID for project-specific permissions
   */
  projectId?: string;
}

/**
 * Component that conditionally renders content based on user permissions
 */
export function PermissionGuard({
  permission,
  permissions,
  anyPermission,
  children,
  fallback,
  hideIfNoPermission = false
}: PermissionGuardProps) {
  const { user } = useAuth();

  // Get user permissions as strings
  const userPermissions = user?.permissions?.map((p: any) =>
    typeof p === 'string' ? p : `${p.resource}:${p.action}`
  ) || [];

  // Convert permission objects to strings
  const permissionToString = (perm: any): string =>
    typeof perm === 'string' ? perm : `${perm.resource}:${perm.action}`;

  // Check if user has the required permission(s)
  const hasRequiredPermission = permission
    ? PermissionUtils.hasPermission(userPermissions, permissionToString(permission))
    : permissions
    ? PermissionUtils.hasAllPermissions(userPermissions, permissions.map(permissionToString))
    : anyPermission
    ? PermissionUtils.hasAnyPermission(userPermissions, anyPermission.map(permissionToString))
    : true;

  // If user doesn't have permission and we should hide the component
  if (!hasRequiredPermission && hideIfNoPermission) {
    return null;
  }

  // Render children if user has permission, otherwise render fallback
  return hasRequiredPermission ? <>{children}</> : fallback ? <>{fallback}</> : null;
}

/**
 * Component that renders content only for system administrators
 */
export function AdminOnly({
  children,
  fallback,
  hideIfNoPermission = false
}: Omit<PermissionGuardProps, 'permission' | 'permissions' | 'anyPermission'>) {
  const { user } = useAuth();

  const isSystemAdmin = user?.systemRoles?.includes(SystemRole.SYSTEM_ADMIN);

  if (!isSystemAdmin && hideIfNoPermission) {
    return null;
  }

  return isSystemAdmin ? <>{children}</> : fallback ? <>{fallback}</> : null;
}

/**
 * Component that renders content only for organization administrators
 */
export function OrganizationAdminOnly({
  children,
  fallback,
  hideIfNoPermission = false,
  organizationId
}: Omit<PermissionGuardProps, 'permission' | 'permissions' | 'anyPermission'> & { organizationId: string }) {
  const { user } = useAuth();

  const isOrgAdmin = user?.systemRoles?.includes(SystemRole.ORGANIZATION_ADMIN) &&
                     user?.organizationIds?.includes(organizationId);

  if (!isOrgAdmin && hideIfNoPermission) {
    return null;
  }

  return isOrgAdmin ? <>{children}</> : fallback ? <>{fallback}</> : null;
}

/**
 * Component that renders content only for project administrators
 */
export function ProjectAdminOnly({
  children,
  fallback,
  hideIfNoPermission = false,
  projectId: _projectId // Renamed to indicate it's not used
}: Omit<PermissionGuardProps, 'permission' | 'permissions' | 'anyPermission'> & { projectId: string }) {
  const { user } = useAuth();

  // In a real app, you would check if the user is a project admin for this specific project
  // This is a simplified version - we would use projectId to check specific project permissions
  const isProjectAdmin = user?.systemRoles?.includes(SystemRole.PROJECT_ADMIN);

  if (!isProjectAdmin && hideIfNoPermission) {
    return null;
  }

  return isProjectAdmin ? <>{children}</> : fallback ? <>{fallback}</> : null;
}
