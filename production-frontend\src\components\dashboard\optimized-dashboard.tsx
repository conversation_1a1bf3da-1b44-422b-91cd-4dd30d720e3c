/**
 * Optimized Dashboard - Lazy Loading Implementation Example
 * Demonstrates how to implement lazy loading throughout the application
 * Only loads services and components when explicitly needed by user interaction
 */

"use client"

import React, { useState, useEffect, Suspense } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { useSmartServicePreloading, useServiceHealth } from '@/hooks/useLazyServices'
import { LazyFeatureLoader, preloadFeature } from '@/components/layout/lazy-feature-loader'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Brain, 
  Users, 
  FileText, 
  BarChart3, 
  Workflow, 
  Search,
  Settings,
  Zap
} from 'lucide-react'

interface DashboardFeature {
  id: string
  name: string
  description: string
  icon: React.ReactNode
  requiresPermission: string[]
  category: 'productivity' | 'collaboration' | 'analytics' | 'admin'
}

const DASHBOARD_FEATURES: DashboardFeature[] = [
  {
    id: 'ai',
    name: 'AI Assistant',
    description: 'Get AI-powered insights and assistance',
    icon: <Brain className="h-5 w-5" />,
    requiresPermission: ['user', 'admin', 'editor'],
    category: 'productivity'
  },
  {
    id: 'collaboration',
    name: 'Collaboration',
    description: 'Work together in real-time',
    icon: <Users className="h-5 w-5" />,
    requiresPermission: ['user', 'admin', 'editor'],
    category: 'collaboration'
  },
  {
    id: 'documentProcessing',
    name: 'Document Processing',
    description: 'Process and analyze documents',
    icon: <FileText className="h-5 w-5" />,
    requiresPermission: ['user', 'admin', 'editor'],
    category: 'productivity'
  },
  {
    id: 'analytics',
    name: 'Analytics',
    description: 'View detailed analytics and reports',
    icon: <BarChart3 className="h-5 w-5" />,
    requiresPermission: ['admin', 'analyst'],
    category: 'analytics'
  },
  {
    id: 'workflow',
    name: 'Workflow Automation',
    description: 'Create and manage automated workflows',
    icon: <Workflow className="h-5 w-5" />,
    requiresPermission: ['admin', 'editor'],
    category: 'productivity'
  },
  {
    id: 'search',
    name: 'Advanced Search',
    description: 'Search across all your content',
    icon: <Search className="h-5 w-5" />,
    requiresPermission: ['user', 'admin', 'editor'],
    category: 'productivity'
  }
]

export function OptimizedDashboard() {
  const { user } = useAuth()
  const [activeFeatures, setActiveFeatures] = useState<Set<string>>(new Set())
  const [preloadedFeatures, setPreloadedFeatures] = useState<Set<string>>(new Set())
  const serviceHealth = useServiceHealth()
  
  // Initialize smart preloading
  useSmartServicePreloading()

  // Get user roles for permission checking
  const userRoles = user?.roles?.map(role => 
    typeof role === 'string' ? role : role.name || role.id
  ) || []

  // Filter features based on user permissions
  const availableFeatures = DASHBOARD_FEATURES.filter(feature =>
    feature.requiresPermission.some(permission => userRoles.includes(permission))
  )

  // Group features by category
  const featuresByCategory = availableFeatures.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = []
    }
    acc[feature.category].push(feature)
    return acc
  }, {} as Record<string, DashboardFeature[]>)

  // Handle feature activation
  const activateFeature = (featureId: string) => {
    setActiveFeatures(prev => new Set(prev).add(featureId))
  }

  // Handle feature deactivation
  const deactivateFeature = (featureId: string) => {
    setActiveFeatures(prev => {
      const newSet = new Set(prev)
      newSet.delete(featureId)
      return newSet
    })
  }

  // Preload feature on hover (smart preloading)
  const handleFeatureHover = (featureId: string) => {
    if (!preloadedFeatures.has(featureId)) {
      preloadFeature(featureId)
      setPreloadedFeatures(prev => new Set(prev).add(featureId))
    }
  }

  // Quick stats (lightweight, always loaded)
  const quickStats = {
    activeServices: Object.keys(serviceHealth).length,
    healthyServices: Object.values(serviceHealth).filter(Boolean).length,
    activeFeatures: activeFeatures.size
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.name || 'User'}
          </p>
        </div>
        
        {/* Service Health Indicator */}
        <div className="flex items-center space-x-2">
          <Badge variant={quickStats.healthyServices === quickStats.activeServices ? 'default' : 'destructive'}>
            <Zap className="h-3 w-3 mr-1" />
            {quickStats.healthyServices}/{quickStats.activeServices} Services
          </Badge>
        </div>
      </div>

      {/* Quick Stats - Always loaded, lightweight */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quickStats.activeServices}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quickStats.activeFeatures}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.round((quickStats.healthyServices / Math.max(quickStats.activeServices, 1)) * 100)}%
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feature Categories */}
      <Tabs defaultValue="productivity" className="space-y-4">
        <TabsList>
          <TabsTrigger value="productivity">Productivity</TabsTrigger>
          <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="admin">Admin</TabsTrigger>
        </TabsList>

        {Object.entries(featuresByCategory).map(([category, features]) => (
          <TabsContent key={category} value={category} className="space-y-4">
            {/* Feature Cards - Only UI, no heavy services loaded */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {features.map(feature => (
                <Card 
                  key={feature.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onMouseEnter={() => handleFeatureHover(feature.id)}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {feature.icon}
                        <CardTitle className="text-lg">{feature.name}</CardTitle>
                      </div>
                      {activeFeatures.has(feature.id) && (
                        <Badge variant="secondary">Active</Badge>
                      )}
                    </div>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex space-x-2">
                      {!activeFeatures.has(feature.id) ? (
                        <Button 
                          onClick={() => activateFeature(feature.id)}
                          size="sm"
                        >
                          Activate
                        </Button>
                      ) : (
                        <Button 
                          onClick={() => deactivateFeature(feature.id)}
                          variant="outline"
                          size="sm"
                        >
                          Deactivate
                        </Button>
                      )}
                      {preloadedFeatures.has(feature.id) && (
                        <Badge variant="outline" className="text-xs">
                          Preloaded
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Active Features - Only loaded when activated */}
            {features.filter(f => activeFeatures.has(f.id)).map(feature => (
              <Card key={`active-${feature.id}`}>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {feature.icon}
                    <span>{feature.name}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Lazy load the actual feature component */}
                  <LazyFeatureLoader 
                    feature={feature.id as any}
                    enabled={true}
                    fallback={
                      <div className="text-center py-8 text-muted-foreground">
                        {feature.name} is not available
                      </div>
                    }
                  />
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        ))}
      </Tabs>

      {/* Performance Info (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Performance Info</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Active Services:</strong> {Object.keys(serviceHealth).join(', ') || 'None'}
              </div>
              <div>
                <strong>Preloaded Features:</strong> {Array.from(preloadedFeatures).join(', ') || 'None'}
              </div>
              <div>
                <strong>Active Features:</strong> {Array.from(activeFeatures).join(', ') || 'None'}
              </div>
              <div>
                <strong>User Roles:</strong> {userRoles.join(', ') || 'None'}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
