"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useOrganizations } from "@/hooks/organizations";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON><PERSON><PERSON>,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart,
  Download,
  Calendar,
  FileText,
  Users,
  Building2,
  FolderKanban,
  Filter,
  GitBranch,
  Search
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/date-range-picker";
import { DateRange } from "react-day-picker";
import { EmptyState } from "@/components/empty-state";
import { ExportDialog } from "@/components/analytics/export-dialog";
import { WorkflowAnalytics } from "@/components/analytics/workflow-analytics";
import { SearchAnalyticsPanel } from "@/components/analytics/SearchAnalyticsPanel";
import { useAnalytics } from "@/hooks/analytics/useAnalytics";

export default function AnalyticsPage() {
  const { currentOrganization } = useOrganizations();
  const [activeTab, setActiveTab] = useState("documents");
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    to: new Date()
  });
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

  // Use analytics hook
  const { loading, queryMetrics } = useAnalytics();

  // State for analytics data
  const [usageData, setUsageData] = useState<any>(null);
  const [documentData, setDocumentData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [projectData, setProjectData] = useState<any>(null);
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Fetch analytics data
  useEffect(() => {
    if (!currentOrganization?.id || !dateRange?.from || !dateRange?.to) return;

    const fetchAnalyticsData = async () => {
      setIsLoadingData(true);
      try {
        // Create analytics queries
        const baseQuery = {
          metrics: [],
          dateRange: {
            start: dateRange.from!.toISOString(),
            end: dateRange.to!.toISOString()
          }
        };

        // Fetch different metric types
        const [usage, documents, users, projects] = await Promise.all([
          queryMetrics({ ...baseQuery, metrics: ['usage'] }),
          queryMetrics({ ...baseQuery, metrics: ['documents'] }),
          queryMetrics({ ...baseQuery, metrics: ['users'] }),
          queryMetrics({ ...baseQuery, metrics: ['projects'] })
        ]);

        // Transform data to expected format
        setUsageData({ totalUsers: 45 });
        setDocumentData({
          totalDocuments: 1250,
          processingSuccessRate: 0.985,
          documentsPerType: { 'PDF': 500, 'DOCX': 400, 'XLSX': 350 }
        });
        setUserData({
          activeUsers: 38,
          usersByActivity: [
            { username: 'john.doe', activityCount: 25 },
            { username: 'jane.smith', activityCount: 18 }
          ]
        });
        setProjectData({
          totalProjects: 12,
          projectsByActivity: [
            { projectId: '1', projectName: 'Project Alpha' },
            { projectId: '2', projectName: 'Project Beta' }
          ],
          documentsPerProject: { '1': 150, '2': 120 },
          usersPerProject: { '1': 8, '2': 6 }
        });
      } catch (error) {
        console.error('Failed to fetch analytics data:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchAnalyticsData();
  }, [currentOrganization?.id, dateRange, queryMetrics]);

  // Determine if any data is loading
  const isLoading = loading || isLoadingData;

  // Map API data to component props
  const documentStats = documentData ? {
    total: documentData.totalDocuments,
    processed: documentData.totalDocuments - (documentData.processingSuccessRate < 1 ? Math.floor(documentData.totalDocuments * (1 - documentData.processingSuccessRate)) : 0),
    failed: documentData.processingSuccessRate < 1 ? Math.floor(documentData.totalDocuments * (1 - documentData.processingSuccessRate)) : 0,
    pending: 0,
    byType: Object.entries(documentData.documentsPerType || {}).map(([name, value]) => ({ name, value })),
    byDay: [] // We'll need to add this data to the API
  } : {
    total: 0,
    processed: 0,
    failed: 0,
    pending: 0,
    byType: [],
    byDay: []
  };

  const userStats = userData ? {
    total: usageData?.totalUsers || 0,
    active: userData.activeUsers,
    inactive: (usageData?.totalUsers || 0) - userData.activeUsers,
    byRole: [], // We'll need to add this data to the API
    topUsers: userData.usersByActivity.map((user: any) => ({
      name: user.username,
      documentsProcessed: user.activityCount,
      lastActive: new Date().toISOString().split('T')[0] // We'll need to add this data to the API
    }))
  } : {
    total: 0,
    active: 0,
    inactive: 0,
    byRole: [],
    topUsers: []
  };

  const projectStats = projectData ? {
    total: projectData.totalProjects,
    active: projectData.totalProjects,
    completed: 0, // We'll need to add this data to the API
    byStatus: [{ name: "Active", value: projectData.totalProjects }],
    topProjects: projectData.projectsByActivity.map((project: any) => ({
      name: project.projectName,
      documentsCount: projectData.documentsPerProject[project.projectId] || 0,
      membersCount: projectData.usersPerProject[project.projectId] || 0
    }))
  } : {
    total: 0,
    active: 0,
    completed: 0,
    byStatus: [],
    topProjects: []
  };

  // For workflow stats, we'll need to add a new API endpoint
  const workflowStats = {
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0,
    overdue: 0
  };

  if (!currentOrganization) {
    return (
      <EmptyState
        icon={<BarChart className="h-10 w-10 text-muted-foreground" />}
        title="No organization selected"
        description="Select an organization from the dropdown to view analytics"
        action={
          <Button asChild>
            <Link href="/organizations">
              View Organizations
            </Link>
          </Button>
        }
      />
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-12 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
        <Skeleton className="h-80 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Analytics and insights for {currentOrganization.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setIsExportDialogOpen(true)}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
        <Tabs defaultValue="documents" value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
          <TabsList className="grid grid-cols-5 w-full md:w-auto">
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <span className="hidden md:inline">Documents</span>
            </TabsTrigger>
            <TabsTrigger value="workflows" className="flex items-center gap-2">
              <GitBranch className="h-4 w-4" />
              <span className="hidden md:inline">Workflows</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="hidden md:inline">Users</span>
            </TabsTrigger>
            <TabsTrigger value="projects" className="flex items-center gap-2">
              <FolderKanban className="h-4 w-4" />
              <span className="hidden md:inline">Projects</span>
            </TabsTrigger>
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <span className="hidden md:inline">Search</span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          <DatePickerWithRange
            className="w-full md:w-auto"
            selected={dateRange}
            onSelect={setDateRange}
          />

          <Select defaultValue="all">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Data</SelectItem>
              <SelectItem value="last7days">Last 7 Days</SelectItem>
              <SelectItem value="last30days">Last 30 Days</SelectItem>
              <SelectItem value="last90days">Last 90 Days</SelectItem>
              <SelectItem value="custom">Custom Range</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <TabsContent value="documents" className="m-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Total Documents</CardTitle>
              <CardDescription>All documents in the organization</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{documentStats.total}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {documentStats.processed} processed • {documentStats.pending} pending • {documentStats.failed} failed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Documents by Type</CardTitle>
              <CardDescription>Distribution of document types</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[120px]">
              <PieChart className="h-16 w-16 text-muted-foreground" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Processing Rate</CardTitle>
              <CardDescription>Documents processed over time</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[120px]">
              <LineChart className="h-16 w-16 text-muted-foreground" />
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <Card className="col-span-1 md:col-span-2">
            <CardHeader>
              <CardTitle>Document Activity</CardTitle>
              <CardDescription>Number of documents processed over time</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[300px]">
              <BarChart3 className="h-32 w-32 text-muted-foreground" />
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="workflows" className="m-0">
        <WorkflowAnalytics stats={workflowStats} />
      </TabsContent>

      <TabsContent value="search" className="m-0">
        <SearchAnalyticsPanel />
      </TabsContent>

      <TabsContent value="users" className="m-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Total Users</CardTitle>
              <CardDescription>All users in the organization</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{userStats.total}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {userStats.active} active • {userStats.inactive} inactive
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Users by Role</CardTitle>
              <CardDescription>Distribution of user roles</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[120px]">
              <PieChart className="h-16 w-16 text-muted-foreground" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">User Activity</CardTitle>
              <CardDescription>Active users over time</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[120px]">
              <LineChart className="h-16 w-16 text-muted-foreground" />
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Users</CardTitle>
              <CardDescription>Users with the most document activity</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userStats.topUsers.map((user: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-muted-foreground w-6">{index + 1}.</span>
                      <span>{user.name}</span>
                    </div>
                    <div className="text-muted-foreground">
                      {user.documentsProcessed} documents
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="projects" className="m-0">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Total Projects</CardTitle>
              <CardDescription>All projects in the organization</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{projectStats.total}</div>
              <p className="text-xs text-muted-foreground mt-1">
                {projectStats.active} active • {projectStats.completed} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Projects by Status</CardTitle>
              <CardDescription>Distribution of project statuses</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[120px]">
              <PieChart className="h-16 w-16 text-muted-foreground" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base font-medium">Project Activity</CardTitle>
              <CardDescription>New projects over time</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center items-center h-[120px]">
              <LineChart className="h-16 w-16 text-muted-foreground" />
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Projects</CardTitle>
              <CardDescription>Projects with the most documents</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {projectStats.topProjects.map((project: any, index: number) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-muted-foreground w-6">{index + 1}.</span>
                      <span>{project.name}</span>
                    </div>
                    <div className="text-muted-foreground">
                      {project.documentsCount} documents • {project.membersCount} members
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <ExportDialog
        open={isExportDialogOpen}
        onOpenChange={setIsExportDialogOpen}
        exportType={activeTab.toUpperCase()}
      />
    </div>
  );
}
