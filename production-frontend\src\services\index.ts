/**
 * Services - Central Export
 */

// Core API client
export { backendApiClient } from './backend-api-client'

// Feature services
export { documentService } from './document-service'
export { projectService } from './project-service'
export { templateService } from './template-service'
export { workflowService } from './workflow-service'
export { organizationService } from './organization-service'

// Additional services
export { notificationService } from './notification-service'
export { exportService } from './export-service'
export { documentSearchService } from './document-search-service'
export { templateFieldsService } from './template-fields-service'
export { templateRenderingService } from './template-rendering-service'
export { workflowAutomationService } from './workflow-automation-service'
export { EventType, type EventData } from './event-grid-service'

// Production admin service with real backend integration
export const adminService = {
  // Event management with real backend endpoints
  getEventDetails: async (eventId: string) => {
    try {
      const response = await fetch(`/management/events/${eventId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to get event details: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to get event details:', error)
      return { id: eventId, data: {}, error: 'Failed to retrieve event details' }
    }
  },
  getEventMonitoring: async () => {
    try {
      const response = await fetch('/management/events/monitoring', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to get event monitoring: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to get event monitoring:', error)
      return { events: [], error: 'Failed to retrieve event monitoring data' }
    }
  },
  replayEvent: async (eventId: string) => {
    try {
      const response = await fetch(`/management/events/${eventId}/replay`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to replay event: ${response.statusText}`)
      }

      return await response.json()
    } catch (error) {
      console.error('Failed to replay event:', error)
      return { success: false, error: 'Failed to replay event' }
    }
  },
  getDeadLetterQueueItemDetails: async (itemId: string) => {
    console.warn('Admin service not yet implemented - using placeholder')
    return { id: itemId, data: {}, message: 'Admin service not yet implemented' }
  },
  getEvents: async (params: any) => {
    console.warn('Admin service not yet implemented - using placeholder')
    return {
      events: [],
      metrics: { total: 0, success: 0, failed: 0, processing: 0 },
      message: 'Admin service not yet implemented'
    }
  },
  getDeadLetterQueue: async (params: any) => {
    console.warn('Admin service not yet implemented - using placeholder')
    return {
      items: [],
      metrics: { total: 0, byType: {} },
      message: 'Admin service not yet implemented'
    }
  },
  retryDeadLetterItem: async (id: string) => {
    console.warn('Admin service not yet implemented - using placeholder')
    return { success: false, message: 'Admin service not yet implemented' }
  },
  retryAllDeadLetterItems: async (params: any) => {
    console.warn('Admin service not yet implemented - using placeholder')
    return { success: false, message: 'Admin service not yet implemented' }
  },
  replayEvents: async (aggregateType: string, aggregateId: string, options: any) => {
    console.warn('Admin service not yet implemented - using placeholder')
    return {
      success: false,
      message: 'Admin service not yet implemented',
      replayId: null,
      eventsReplayed: 0
    }
  }
}

// Re-export types
export type {
  ApiRequestConfig,
  ApiResponse,
  PaginatedApiResponse,
  ApiError,
  ApiErrorResponse,
  ValidationErrorResponse,
  FileUploadRequest,
  FileUploadResponse,
  MultipartUploadRequest,
  MultipartUploadResponse,
  BatchRequest,
  BatchResponse,
  WebSocketConfig,
  WebSocketMessage,
  WebSocketEvent,
} from '../types/api'
