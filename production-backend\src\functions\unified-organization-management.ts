/**
 * Unified Organization Management Function
 * Consolidates all organization CRUD operations, settings, members, teams, and billing
 * Replaces: organization-create.ts, organization-list.ts, organization-manage.ts,
 *          organization-settings.ts, organization-members-invite.ts, organization-teams-create.ts,
 *          organization-billing.ts
 * 
 * Production-ready implementation with comprehensive error handling,
 * caching, Azure best practices, and Service Bus integration
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { signalRService } from '../shared/services/signalr';

// Unified organization types and enums
enum OrganizationTier {
  FREE = 'FREE',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE'
}

enum UserRole {
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
  VIEWER = 'VIEWER'
}

enum TeamType {
  DEPARTMENT = 'DEPARTMENT',
  PROJECT = 'PROJECT',
  FUNCTIONAL = 'FUNCTIONAL',
  CROSS_FUNCTIONAL = 'CROSS_FUNCTIONAL'
}

enum TeamVisibility {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION'
}

enum TeamRole {
  LEAD = 'LEAD',
  MEMBER = 'MEMBER',
  CONTRIBUTOR = 'CONTRIBUTOR'
}

enum InvitationStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  EXPIRED = 'EXPIRED'
}

// Comprehensive interfaces
interface Organization {
  id: string;
  name: string;
  description: string;
  tier: OrganizationTier;
  status: 'active' | 'suspended' | 'deleted';
  createdBy: string;
  createdAt: string;
  updatedBy: string;
  updatedAt: string;
  projectIds: string[];
  memberIds: string[];
  teamIds: string[];
  settings: OrganizationSettings;
  billing: BillingInfo;
  statistics: OrganizationStatistics;
  tenantId: string;
}

interface OrganizationSettings {
  general: {
    allowPublicProjects: boolean;
    requireApprovalForNewMembers: boolean;
    enableGuestAccess: boolean;
    defaultProjectVisibility: 'private' | 'organization' | 'public';
    defaultDocumentRetentionDays: number;
    enableAuditLog: boolean;
    allowedDocumentTypes: string[];
    maxFileSize: number;
    maxProjects: number;
  };
  security: {
    enforcePasswordPolicy: boolean;
    passwordPolicy: PasswordPolicy;
    requireTwoFactor: boolean;
    sessionTimeoutMinutes: number;
    allowedIpRanges: string[];
    enableDeviceTracking: boolean;
    requireApprovalForSensitiveActions: boolean;
  };
  features: {
    aiAnalysis: boolean;
    advancedWorkflows: boolean;
    bulkProcessing: boolean;
    apiAccess: boolean;
    customBranding: boolean;
    ssoIntegration: boolean;
  };
  integrations: {
    enabledIntegrations: string[];
    webhookEndpoints: string[];
    apiKeys: string[];
  };
  notifications: {
    emailNotifications: boolean;
    slackIntegration: boolean;
    teamsIntegration: boolean;
    customWebhooks: boolean;
  };
}

interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge: number;
}

interface BillingInfo {
  subscriptionId?: string;
  customerId?: string;
  paymentMethodId?: string;
  billingEmail: string;
  billingAddress?: Address;
  currentPlan: string;
  planStartDate: string;
  planEndDate?: string;
  autoRenew: boolean;
  usage: UsageMetrics;
  invoices: string[];
}

interface Address {
  street: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface UsageMetrics {
  documentsProcessed: number;
  storageUsed: number;
  apiCalls: number;
  activeUsers: number;
  lastUpdated: string;
}

interface OrganizationStatistics {
  memberCount: number;
  projectCount: number;
  documentCount: number;
  teamCount: number;
  storageUsed: number;
  lastActivity: string;
}

interface OrganizationMember {
  id: string;
  userId: string;
  organizationId: string;
  role: UserRole;
  permissions: string[];
  joinedAt: string;
  invitedBy: string;
  status: 'active' | 'inactive' | 'suspended';
  lastActivity?: string;
  teamIds: string[];
  tenantId: string;
}

interface Team {
  id: string;
  name: string;
  description: string;
  type: TeamType;
  visibility: TeamVisibility;
  organizationId: string;
  parentTeamId?: string;
  memberIds: string[];
  leadIds: string[];
  projectIds: string[];
  settings: TeamSettings;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
}

interface TeamSettings {
  allowSelfJoin: boolean;
  requireApprovalToJoin: boolean;
  maxMembers?: number;
  enableDiscussions: boolean;
  enableFileSharing: boolean;
  enableTaskManagement: boolean;
  defaultProjectRole: string;
}

interface Invitation {
  id: string;
  organizationId: string;
  email: string;
  role: UserRole;
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  status: InvitationStatus;
  message?: string;
  acceptedAt?: string;
  declinedAt?: string;
  token: string;
  tenantId: string;
}

// Validation schemas
const createOrganizationSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  tier: Joi.string().valid(...Object.values(OrganizationTier)).default(OrganizationTier.FREE),
  billingEmail: Joi.string().email().optional()
});

const updateOrganizationSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  description: Joi.string().max(500).optional(),
  tier: Joi.string().valid(...Object.values(OrganizationTier)).optional(),
  settings: Joi.object().optional()
});

const listOrganizationsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  search: Joi.string().max(100).optional(),
  tier: Joi.string().valid(...Object.values(OrganizationTier)).optional(),
  status: Joi.string().valid('active', 'suspended', 'deleted').optional()
});

const inviteMemberSchema = Joi.object({
  email: Joi.string().email().required(),
  role: Joi.string().valid(...Object.values(UserRole)).default(UserRole.MEMBER),
  message: Joi.string().max(500).optional(),
  teamIds: Joi.array().items(Joi.string().uuid()).optional()
});

const createTeamSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(TeamType)).required(),
  visibility: Joi.string().valid(...Object.values(TeamVisibility)).default(TeamVisibility.ORGANIZATION),
  parentTeamId: Joi.string().uuid().optional(),
  initialMembers: Joi.array().items(Joi.object({
    userId: Joi.string().uuid().required(),
    role: Joi.string().valid(...Object.values(TeamRole)).default(TeamRole.MEMBER)
  })).optional(),
  settings: Joi.object({
    allowSelfJoin: Joi.boolean().default(false),
    requireApprovalToJoin: Joi.boolean().default(true),
    maxMembers: Joi.number().integer().min(1).optional(),
    enableDiscussions: Joi.boolean().default(true),
    enableFileSharing: Joi.boolean().default(true),
    enableTaskManagement: Joi.boolean().default(false),
    defaultProjectRole: Joi.string().default('member')
  }).optional()
});

const updateSettingsSchema = Joi.object({
  general: Joi.object().optional(),
  security: Joi.object().optional(),
  features: Joi.object().optional(),
  integrations: Joi.object().optional(),
  notifications: Joi.object().optional()
});

/**
 * Unified Organization Management Class
 * Handles all organization operations with comprehensive error handling and caching
 */
class UnifiedOrganizationManager {
  private serviceBusService: ServiceBusEnhancedService;

  constructor() {
    // Initialize Service Bus service
    this.serviceBusService = ServiceBusEnhancedService.getInstance();
  }

  /**
   * Create organization
   */
  async createOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const startTime = Date.now();

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = createOrganizationSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const { name, description, tier, billingEmail } = value;

      // Check tier limits
      if (tier === OrganizationTier.FREE) {
        const existingOrgs = await this.getUserOrganizations(user.id, OrganizationTier.FREE);
        if (existingOrgs.length >= 1) {
          return addCorsHeaders({
            status: 403,
            jsonBody: {
              error: 'Free tier limit reached',
              message: 'You can only create one free organization. Please upgrade to create more.'
            }
          }, request);
        }
      }

      // Create organization
      const organizationId = uuidv4();
      const organization = await this.createOrganizationRecord({
        id: organizationId,
        name,
        description: description || '',
        tier,
        billingEmail: billingEmail || user.email,
        createdBy: user.id,
        tenantId: user.tenantId || user.id
      });

      // Create membership for creator
      await this.createMembership(organizationId, user.id, UserRole.ADMIN, user.id);

      // Update user's organizations
      await this.updateUserOrganizations(user.id, organizationId);

      // Cache organization
      await this.cacheOrganization(organization);

      // Log activity
      await this.logOrganizationActivity(user.id, 'organization_created', {
        organizationId,
        organizationName: name,
        tier,
        memberCount: 1
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Organization.Created',
        subject: `organizations/${organizationId}/created`,
        data: {
          organizationId,
          organizationName: name,
          tier,
          createdBy: user.id,
          memberCount: 1,
          correlationId
        }
      });

      // Send welcome notification
      await this.sendWelcomeNotification(user.id, organization);

      logger.info('Organization created successfully', {
        correlationId,
        organizationId,
        name,
        tier,
        userId: user.id,
        processingTime: Date.now() - startTime
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: {
          success: true,
          organization: this.sanitizeOrganization(organization),
          processingTime: Date.now() - startTime
        }
      }, request);

    } catch (error) {
      logger.error('Organization creation failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * List organizations for user
   */
  async listOrganizations(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Parse query parameters
      const url = new URL(request.url);
      const queryParams: any = Object.fromEntries(url.searchParams.entries());

      // Convert string values to appropriate types
      if (queryParams.page) queryParams.page = parseInt(queryParams.page);
      if (queryParams.limit) queryParams.limit = parseInt(queryParams.limit);

      const { error, value } = listOrganizationsSchema.validate(queryParams);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const { page, limit, search, tier, status } = value;

      // Check cache first
      const cacheKey = `user:${user.id}:organizations:${JSON.stringify(value)}`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return addCorsHeaders({
          status: 200,
          jsonBody: JSON.parse(cached)
        }, request);
      }

      // Get user's organization memberships
      const memberships = await db.queryItems<OrganizationMember>('organization-members',
        'SELECT * FROM c WHERE c.userId = @userId AND c.status = "active"',
        [{ name: '@userId', value: user.id }]
      );

      const organizationIds = memberships.map(m => m.organizationId);

      if (organizationIds.length === 0) {
        const result = {
          organizations: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false
          }
        };

        await redis.setex(cacheKey, 300, JSON.stringify(result));
        return addCorsHeaders({
          status: 200,
          jsonBody: result
        }, request);
      }

      // Build query for organizations
      let queryText = `SELECT * FROM c WHERE c.id IN (${organizationIds.map((_, i) => `@orgId${i}`).join(', ')})`;
      const parameters: any[] = organizationIds.map((id, i) => ({ name: `@orgId${i}`, value: id }));

      // Add filters
      if (search) {
        queryText += ' AND (CONTAINS(c.name, @search) OR CONTAINS(c.description, @search))';
        parameters.push({ name: '@search', value: search });
      }

      if (tier) {
        queryText += ' AND c.tier = @tier';
        parameters.push({ name: '@tier', value: tier });
      }

      if (status) {
        queryText += ' AND c.status = @status';
        parameters.push({ name: '@status', value: status });
      }

      // Add ordering and pagination
      queryText += ' ORDER BY c.updatedAt DESC';
      queryText += ` OFFSET ${(page - 1) * limit} LIMIT ${limit}`;

      // Execute query
      const organizations = await db.queryItems<Organization>('organizations', queryText, parameters);

      // Get total count
      const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)').split(' ORDER BY')[0];
      const totalCountResult = await db.queryItems<number>('organizations', countQuery, parameters);
      const totalCount = totalCountResult[0] || 0;

      // Enrich organizations with user role and statistics
      const enrichedOrganizations = await Promise.all(
        organizations.map(async (org) => {
          const membership = memberships.find(m => m.organizationId === org.id);
          const stats = await this.getOrganizationStatistics(org.id);

          return {
            ...this.sanitizeOrganization(org),
            userRole: membership?.role,
            statistics: stats
          };
        })
      );

      const result = {
        organizations: enrichedOrganizations,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      };

      // Cache result for 5 minutes
      await redis.setex(cacheKey, 300, JSON.stringify(result));

      logger.info('Organizations listed successfully', {
        correlationId,
        userId: user.id,
        page,
        limit,
        totalCount
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: result
      }, request);

    } catch (error) {
      logger.error('Organization listing failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Update organization
   */
  async updateOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    const correlationId = context.invocationId;
    const organizationId = request.url.split('/')[4]; // Extract from URL path

    try {
      // Authenticate user
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const user = authResult.user;

      // Validate request
      const body = await request.json();
      const { error, value } = updateOrganizationSchema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      const updateData = value;

      // Get organization
      const organization = await db.readItem('organizations', organizationId, user.tenantId || 'default');
      if (!organization) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Organization not found' }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkOrganizationPermission(organizationId, user.id, 'admin');
      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'You do not have permission to update this organization' }
        }, request);
      }

      // Update organization
      const updatedOrganization = {
        ...organization,
        ...updateData,
        updatedBy: user.id,
        updatedAt: new Date().toISOString()
      };

      // Handle tier changes
      if (updateData.tier && updateData.tier !== organization.tier) {
        updatedOrganization.settings = {
          ...updatedOrganization.settings,
          ...this.getTierSettings(updateData.tier)
        };
      }

      await db.updateItem('organizations', updatedOrganization);

      // Invalidate cache
      await this.invalidateOrganizationCache(organizationId);

      // Log activity
      await this.logOrganizationActivity(user.id, 'organization_updated', {
        organizationId,
        changes: updateData,
        organizationName: updatedOrganization.name
      });

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'Organization.Updated',
        subject: `organizations/${organizationId}/updated`,
        data: {
          organizationId,
          changes: updateData,
          updatedBy: user.id,
          correlationId
        }
      });

      logger.info('Organization updated successfully', {
        correlationId,
        organizationId,
        userId: user.id,
        changes: Object.keys(updateData)
      });

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          organization: this.sanitizeOrganization(updatedOrganization)
        }
      }, request);

    } catch (error) {
      logger.error('Organization update failed', {
        error: error instanceof Error ? error.message : String(error),
        correlationId,
        organizationId
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Helper Methods
   */
  private async getUserOrganizations(userId: string, tier?: OrganizationTier): Promise<Organization[]> {
    try {
      let queryText = 'SELECT o.* FROM organizations o JOIN organization_members m ON o.id = m.organizationId WHERE m.userId = @userId AND m.status = "active"';
      const parameters: any[] = [{ name: '@userId', value: userId }];

      if (tier) {
        queryText += ' AND o.tier = @tier';
        parameters.push({ name: '@tier', value: tier });
      }

      return await db.queryItems<Organization>('organizations', queryText, parameters);
    } catch (error) {
      logger.error('Error getting user organizations', {
        userId,
        tier,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }

  private async createOrganizationRecord(data: any): Promise<Organization> {
    const now = new Date().toISOString();

    const organization: Organization = {
      id: data.id,
      name: data.name,
      description: data.description,
      tier: data.tier,
      status: 'active',
      createdBy: data.createdBy,
      createdAt: now,
      updatedBy: data.createdBy,
      updatedAt: now,
      projectIds: [],
      memberIds: [data.createdBy],
      teamIds: [],
      settings: this.getDefaultSettings(data.tier),
      billing: {
        billingEmail: data.billingEmail,
        currentPlan: data.tier.toLowerCase(),
        planStartDate: now,
        autoRenew: true,
        usage: {
          documentsProcessed: 0,
          storageUsed: 0,
          apiCalls: 0,
          activeUsers: 1,
          lastUpdated: now
        },
        invoices: []
      },
      statistics: {
        memberCount: 1,
        projectCount: 0,
        documentCount: 0,
        teamCount: 0,
        storageUsed: 0,
        lastActivity: now
      },
      tenantId: data.tenantId
    };

    await db.createItem('organizations', organization);
    return organization;
  }

  private getDefaultSettings(tier: OrganizationTier): OrganizationSettings {
    return {
      general: {
        allowPublicProjects: false,
        requireApprovalForNewMembers: true,
        enableGuestAccess: false,
        defaultProjectVisibility: 'private',
        defaultDocumentRetentionDays: 365,
        enableAuditLog: tier !== OrganizationTier.FREE,
        allowedDocumentTypes: ['pdf', 'docx', 'xlsx', 'pptx', 'jpg', 'png'],
        maxFileSize: tier === OrganizationTier.FREE ? 20 * 1024 * 1024 : 100 * 1024 * 1024,
        maxProjects: tier === OrganizationTier.FREE ? 3 : tier === OrganizationTier.PROFESSIONAL ? 10 : 100
      },
      security: {
        enforcePasswordPolicy: tier !== OrganizationTier.FREE,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
          maxAge: 90
        },
        requireTwoFactor: false,
        sessionTimeoutMinutes: 480,
        allowedIpRanges: [],
        enableDeviceTracking: tier === OrganizationTier.ENTERPRISE,
        requireApprovalForSensitiveActions: tier === OrganizationTier.ENTERPRISE
      },
      features: {
        aiAnalysis: tier !== OrganizationTier.FREE,
        advancedWorkflows: tier === OrganizationTier.ENTERPRISE,
        bulkProcessing: tier !== OrganizationTier.FREE,
        apiAccess: tier === OrganizationTier.ENTERPRISE,
        customBranding: tier === OrganizationTier.ENTERPRISE,
        ssoIntegration: tier === OrganizationTier.ENTERPRISE
      },
      integrations: {
        enabledIntegrations: [],
        webhookEndpoints: [],
        apiKeys: []
      },
      notifications: {
        emailNotifications: true,
        slackIntegration: false,
        teamsIntegration: false,
        customWebhooks: false
      }
    };
  }

  private getTierSettings(tier: OrganizationTier): Partial<OrganizationSettings> {
    return {
      general: {
        maxFileSize: tier === OrganizationTier.FREE ? 20 * 1024 * 1024 : 100 * 1024 * 1024,
        maxProjects: tier === OrganizationTier.FREE ? 3 : tier === OrganizationTier.PROFESSIONAL ? 10 : 100,
        enableAuditLog: tier !== OrganizationTier.FREE,
        allowPublicProjects: false,
        requireApprovalForNewMembers: true,
        enableGuestAccess: false,
        defaultProjectVisibility: 'private',
        defaultDocumentRetentionDays: 365,
        allowedDocumentTypes: ['pdf', 'docx', 'xlsx', 'pptx', 'jpg', 'png']
      },
      features: {
        aiAnalysis: tier !== OrganizationTier.FREE,
        advancedWorkflows: tier === OrganizationTier.ENTERPRISE,
        bulkProcessing: tier !== OrganizationTier.FREE,
        apiAccess: tier === OrganizationTier.ENTERPRISE,
        customBranding: tier === OrganizationTier.ENTERPRISE,
        ssoIntegration: tier === OrganizationTier.ENTERPRISE
      },
      security: {
        enforcePasswordPolicy: tier !== OrganizationTier.FREE,
        enableDeviceTracking: tier === OrganizationTier.ENTERPRISE,
        requireApprovalForSensitiveActions: tier === OrganizationTier.ENTERPRISE,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
          maxAge: 90
        },
        requireTwoFactor: false,
        sessionTimeoutMinutes: 480,
        allowedIpRanges: []
      }
    };
  }

  private async createMembership(organizationId: string, userId: string, role: UserRole, invitedBy: string): Promise<void> {
    const membership: OrganizationMember = {
      id: uuidv4(),
      userId,
      organizationId,
      role,
      permissions: this.getRolePermissions(role),
      joinedAt: new Date().toISOString(),
      invitedBy,
      status: 'active',
      teamIds: [],
      tenantId: userId
    };

    await db.createItem('organization-members', membership);
  }

  private getRolePermissions(role: UserRole): string[] {
    switch (role) {
      case UserRole.ADMIN:
        return ['read', 'write', 'delete', 'manage_members', 'manage_settings', 'manage_billing'];
      case UserRole.MEMBER:
        return ['read', 'write'];
      case UserRole.VIEWER:
        return ['read'];
      default:
        return ['read'];
    }
  }

  private async updateUserOrganizations(userId: string, organizationId: string): Promise<void> {
    try {
      const user = await db.readItem('users', userId, userId);
      if (user) {
        const updatedUser = {
          ...user,
          id: userId,
          organizationIds: [...(user.organizationIds || []), organizationId],
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('users', updatedUser);
      }
    } catch (error) {
      logger.error('Error updating user organizations', {
        userId,
        organizationId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async cacheOrganization(organization: Organization): Promise<void> {
    try {
      await redis.setex(`org:${organization.id}:details`, 1800, JSON.stringify(organization));
    } catch (error) {
      logger.error('Error caching organization', {
        organizationId: organization.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async invalidateOrganizationCache(organizationId: string): Promise<void> {
    try {
      const keys = [
        `org:${organizationId}:details`,
        `org:${organizationId}:members`,
        `org:${organizationId}:teams`,
        `org:${organizationId}:stats`
      ];

      for (const key of keys) {
        await redis.del(key);
      }
    } catch (error) {
      logger.error('Error invalidating organization cache', {
        organizationId,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async getOrganizationStatistics(organizationId: string): Promise<OrganizationStatistics> {
    try {
      // Check cache first
      const cacheKey = `org:${organizationId}:stats`;
      const cached = await redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Calculate statistics
      const [memberCount, projectCount, documentCount, teamCount] = await Promise.all([
        this.getMemberCount(organizationId),
        this.getProjectCount(organizationId),
        this.getDocumentCount(organizationId),
        this.getTeamCount(organizationId)
      ]);

      const stats: OrganizationStatistics = {
        memberCount,
        projectCount,
        documentCount,
        teamCount,
        storageUsed: 0, // Would calculate from actual storage usage
        lastActivity: new Date().toISOString()
      };

      // Cache for 10 minutes
      await redis.setex(cacheKey, 600, JSON.stringify(stats));
      return stats;
    } catch (error) {
      logger.error('Error getting organization statistics', {
        organizationId,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        memberCount: 0,
        projectCount: 0,
        documentCount: 0,
        teamCount: 0,
        storageUsed: 0,
        lastActivity: new Date().toISOString()
      };
    }
  }

  private async getMemberCount(organizationId: string): Promise<number> {
    const result = await db.queryItems<number>('organization-members',
      'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = "active"',
      [{ name: '@orgId', value: organizationId }]
    );
    return result[0] || 0;
  }

  private async getProjectCount(organizationId: string): Promise<number> {
    const result = await db.queryItems<number>('projects',
      'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId',
      [{ name: '@orgId', value: organizationId }]
    );
    return result[0] || 0;
  }

  private async getDocumentCount(organizationId: string): Promise<number> {
    const result = await db.queryItems<number>('documents',
      'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId',
      [{ name: '@orgId', value: organizationId }]
    );
    return result[0] || 0;
  }

  private async getTeamCount(organizationId: string): Promise<number> {
    const result = await db.queryItems<number>('teams',
      'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId',
      [{ name: '@orgId', value: organizationId }]
    );
    return result[0] || 0;
  }

  private async checkOrganizationPermission(organizationId: string, userId: string, requiredPermission: string): Promise<boolean> {
    try {
      const memberships = await db.queryItems<OrganizationMember>('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = "active"',
        [
          { name: '@orgId', value: organizationId },
          { name: '@userId', value: userId }
        ]
      );

      if (memberships.length === 0) {
        return false;
      }

      const membership = memberships[0];

      // Admin has all permissions
      if (membership.role === UserRole.ADMIN) {
        return true;
      }

      // Check specific permission
      return membership.permissions.includes(requiredPermission);
    } catch (error) {
      logger.error('Error checking organization permission', {
        organizationId,
        userId,
        requiredPermission,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async logOrganizationActivity(userId: string, activity: string, details: any): Promise<void> {
    try {
      await db.createItem('activities', {
        id: uuidv4(),
        type: activity,
        userId,
        organizationId: details.organizationId,
        timestamp: new Date().toISOString(),
        details,
        tenantId: userId
      });
    } catch (error) {
      logger.error('Error logging organization activity', {
        userId,
        activity,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async sendWelcomeNotification(userId: string, organization: Organization): Promise<void> {
    try {
      // Send notification via Service Bus
      const notificationMessage = {
        type: 'welcome_notification',
        userId,
        organizationId: organization.id,
        organizationName: organization.name,
        templateId: 'organization_welcome',
        data: {
          userName: '', // Will be populated by notification service
          organizationName: organization.name,
          organizationId: organization.id,
          welcomeMessage: `Welcome to ${organization.name}! You have been successfully added to the organization.`,
          nextSteps: [
            'Complete your profile setup',
            'Explore the dashboard',
            'Connect with your team members',
            'Review organization settings'
          ]
        },
        priority: 'normal',
        channels: ['email', 'in_app'],
        scheduledAt: new Date().toISOString()
      };

      // Send to notification queue
      await this.serviceBusService.sendToQueue('notifications', {
        body: notificationMessage,
        correlationId: `welcome-${userId}-${organization.id}`,
        messageId: `welcome-notification-${Date.now()}`,
        timeToLive: 24 * 60 * 60 * 1000 // 24 hours
      });

      logger.info('Welcome notification sent successfully', {
        userId,
        organizationId: organization.id,
        organizationName: organization.name
      });
    } catch (error) {
      logger.error('Error sending welcome notification', {
        userId,
        organizationId: organization.id,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private sanitizeOrganization(organization: any): any {
    // Remove sensitive fields before returning
    const sanitized = { ...organization };
    delete sanitized._rid;
    delete sanitized._self;
    delete sanitized._etag;
    delete sanitized._attachments;
    delete sanitized._ts;

    // Remove sensitive billing information
    if (sanitized.billing) {
      delete sanitized.billing.paymentMethodId;
      delete sanitized.billing.customerId;
    }

    return sanitized;
  }
}

// Create instance of the manager
const organizationManager = new UnifiedOrganizationManager();

/**
 * Additional Organization Management Functions
 */

/**
 * Invite member to organization
 */
async function inviteMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const organizationId = request.url.split('/')[4]; // Extract from URL path

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    // Validate request
    const body = await request.json();
    const { error, value } = inviteMemberSchema.validate(body);
    if (error) {
      return addCorsHeaders({
        status: 400,
        jsonBody: { error: error.details[0].message }
      }, request);
    }

    const { email, role, message } = value;

    // Check permissions
    const hasPermission = await organizationManager['checkOrganizationPermission'](organizationId, user.id, 'manage_members');
    if (!hasPermission) {
      return addCorsHeaders({
        status: 403,
        jsonBody: { error: 'You do not have permission to invite members' }
      }, request);
    }

    // Check if user is already a member
    const existingMember = await db.queryItems<OrganizationMember>('organization-members',
      'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @email',
      [
        { name: '@orgId', value: organizationId },
        { name: '@email', value: email }
      ]
    );

    if (existingMember.length > 0) {
      return addCorsHeaders({
        status: 409,
        jsonBody: { error: 'User is already a member of this organization' }
      }, request);
    }

    // Create invitation
    const invitationId = uuidv4();
    const invitation: Invitation = {
      id: invitationId,
      organizationId,
      email,
      role,
      invitedBy: user.id,
      invitedAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      status: InvitationStatus.PENDING,
      message,
      token: uuidv4(),
      tenantId: user.tenantId || user.id
    };

    await db.createItem('organization-invitations', invitation);

    // Log activity
    await organizationManager['logOrganizationActivity'](user.id, 'member_invited', {
      organizationId,
      invitationId,
      email,
      role
    });

    // Publish event
    await eventGridIntegration.publishEvent({
      eventType: 'Organization.MemberInvited',
      subject: `organizations/${organizationId}/members/invited`,
      data: {
        organizationId,
        invitationId,
        email,
        role,
        invitedBy: user.id,
        correlationId
      }
    });

    logger.info('Member invitation created successfully', {
      correlationId,
      organizationId,
      invitationId,
      email,
      role,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      jsonBody: {
        success: true,
        invitation: {
          id: invitationId,
          email,
          role,
          status: InvitationStatus.PENDING,
          expiresAt: invitation.expiresAt
        }
      }
    }, request);

  } catch (error) {
    logger.error('Member invitation failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId,
      organizationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

/**
 * Get organization details
 */
async function getOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  const organizationId = request.url.split('/')[4]; // Extract from URL path

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const user = authResult.user;

    // Check cache first
    const cacheKey = `org:${organizationId}:details:${user.id}`;
    const cached = await redis.get(cacheKey);
    if (cached) {
      return addCorsHeaders({
        status: 200,
        jsonBody: JSON.parse(cached)
      }, request);
    }

    // Get organization
    const organization = await db.readItem('organizations', organizationId, user.tenantId || 'default');
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        jsonBody: { error: 'Organization not found' }
      }, request);
    }

    // Check permissions
    const hasAccess = await organizationManager['checkOrganizationPermission'](organizationId, user.id, 'read');
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        jsonBody: { error: 'Access denied' }
      }, request);
    }

    // Get user's role and statistics
    const [membership, stats] = await Promise.all([
      db.queryItems<OrganizationMember>('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId',
        [
          { name: '@orgId', value: organizationId },
          { name: '@userId', value: user.id }
        ]
      ),
      organizationManager['getOrganizationStatistics'](organizationId)
    ]);

    const result = {
      success: true,
      organization: {
        ...organizationManager['sanitizeOrganization'](organization),
        userRole: membership[0]?.role,
        statistics: stats
      }
    };

    // Cache for 5 minutes
    await redis.setex(cacheKey, 300, JSON.stringify(result));

    logger.info('Organization retrieved successfully', {
      correlationId,
      organizationId,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      jsonBody: result
    }, request);

  } catch (error) {
    logger.error('Organization retrieval failed', {
      error: error instanceof Error ? error.message : String(error),
      correlationId,
      organizationId
    });

    return addCorsHeaders({
      status: 500,
      jsonBody: { error: 'Internal server error' }
    }, request);
  }
}

// Register HTTP functions
app.http('organization-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations',
  handler: (request, context) => organizationManager.createOrganization(request, context)
});

app.http('organization-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/list',
  handler: (request, context) => organizationManager.listOrganizations(request, context)
});

app.http('organization-get', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}',
  handler: getOrganization
});

app.http('organization-update', {
  methods: ['PUT', 'PATCH', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/update',
  handler: (request, context) => organizationManager.updateOrganization(request, context)
});

app.http('organization-invite-member', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/{organizationId}/members/invite',
  handler: inviteMember
});

// Organization member activities endpoint
app.http('organization-member-activities', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'organizations/members/{memberId}/activities',
  handler: async (request, context) => {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) return preflightResponse;

    try {
      const user = await authenticateRequest(request);
      if (!user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const memberId = (context as any).bindingData?.memberId;
      if (!memberId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Member ID is required' }
        }, request);
      }

      const url = new URL(request.url);
      const page = parseInt(url.searchParams.get('page') || '1');
      const pageSize = parseInt(url.searchParams.get('pageSize') || '20');
      const activityType = url.searchParams.get('activityType');

      // Check if user has permission to view member activities
      const organizationId = (user as any).organizationId;

      // Build query for member activities
      let sqlQuery = 'SELECT * FROM c WHERE c.userId = @memberId AND c.organizationId = @orgId';
      let parameters = [
        { name: '@memberId', value: memberId },
        { name: '@orgId', value: organizationId }
      ];

      if (activityType) {
        sqlQuery += ' AND c.type = @activityType';
        parameters.push({ name: '@activityType', value: activityType });
      }

      sqlQuery += ' ORDER BY c.timestamp DESC';

      // Get member activities
      const activities = await db.queryItems('user-activities', sqlQuery, parameters);

      // Apply pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedActivities = activities.slice(startIndex, endIndex);

      // Format activities for response
      const formattedActivities = paginatedActivities.map((activity: any) => ({
        id: activity.id,
        type: activity.type,
        category: activity.category,
        description: activity.description,
        timestamp: activity.timestamp,
        metadata: activity.metadata,
        organizationId: activity.organizationId,
        projectId: activity.projectId,
        documentId: activity.documentId
      }));

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          success: true,
          activities: formattedActivities,
          pagination: {
            page,
            pageSize,
            total: activities.length,
            totalPages: Math.ceil(activities.length / pageSize)
          }
        }
      }, request);

    } catch (error) {
      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }
});
