import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { usePopularSearchQueries } from '@/hooks/search';
import { SearchSuggestion, PopularSearchQuery } from '@/services/search-service';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator
} from '@/components/ui/command';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { 
  Search as SearchIcon, 
  History, 
  TrendingUp, 
  Sparkles, 
  X,
  ArrowRight
} from 'lucide-react';

interface SearchSuggestionsProps {
  onSearch: (query: string) => void;
  onClose?: () => void;
  autoFocus?: boolean;
  placeholder?: string;
  showPopular?: boolean;
  showRecent?: boolean;
}

export function SearchSuggestions({
  onSearch,
  onClose,
  autoFocus = true,
  placeholder = 'Search for anything...',
  showPopular = true,
  showRecent = true
}: SearchSuggestionsProps) {
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const [isOpen, setIsOpen] = useState(true);
  
  // Search suggestions with real API integration
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);

  // Debounced search suggestions
  useEffect(() => {
    if (!query.trim()) {
      setSuggestions([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      setIsLoadingSuggestions(true);
      try {
        const response = await fetch('/search/suggestions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: query.trim(),
            limit: 5
          })
        });

        if (response.ok) {
          const data = await response.json();
          setSuggestions(data.suggestions || []);
        } else {
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Failed to fetch search suggestions:', error);
        setSuggestions([]);
      } finally {
        setIsLoadingSuggestions(false);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [query]);

  // Popular queries
  const {
    popularQueries,
    isLoading: isLoadingPopularQueries
  } = usePopularSearchQueries();

  // Recent searches with localStorage persistence
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isLoadingRecentSearches, setIsLoadingRecentSearches] = useState(false);

  // Load recent searches from localStorage on mount
  useEffect(() => {
    setIsLoadingRecentSearches(true);
    try {
      const stored = localStorage.getItem('recentSearches');
      if (stored) {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          setRecentSearches(parsed.slice(0, 10)); // Limit to 10 recent searches
        }
      }
    } catch (error) {
      console.error('Failed to load recent searches:', error);
    } finally {
      setIsLoadingRecentSearches(false);
    }
  }, []);

  // Save search to recent searches
  const saveToRecentSearches = (searchQuery: string) => {
    try {
      const trimmed = searchQuery.trim();
      if (!trimmed) return;

      setRecentSearches(prev => {
        const filtered = prev.filter(search => search !== trimmed);
        const updated = [trimmed, ...filtered].slice(0, 10);
        localStorage.setItem('recentSearches', JSON.stringify(updated));
        return updated;
      });
    } catch (error) {
      console.error('Failed to save recent search:', error);
    }
  };

  const clearRecentSearches = () => {
    try {
      localStorage.removeItem('recentSearches');
      setRecentSearches([]);
    } catch (error) {
      console.error('Failed to clear recent searches:', error);
    }
  };
  
  // Focus input on mount
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);
  
  // Handle search
  const handleSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return;
    saveToRecentSearches(searchQuery);
    onSearch(searchQuery);
    setIsOpen(false);
  };
  
  // Handle suggestion click
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    handleSearch(suggestion.text);
  };
  
  // Handle popular query click
  const handlePopularQueryClick = (popularQuery: PopularSearchQuery) => {
    handleSearch(popularQuery.query);
  };
  
  // Handle recent search click
  const handleRecentSearchClick = (recentSearch: string) => {
    handleSearch(recentSearch);
  };
  
  // Handle clear recent searches
  const handleClearRecentSearches = (e: React.MouseEvent) => {
    e.stopPropagation();
    clearRecentSearches();
  };
  
  // Handle close
  const handleClose = () => {
    setIsOpen(false);
    if (onClose) onClose();
  };
  
  // Handle key down
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(query);
    } else if (e.key === 'Escape') {
      handleClose();
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <Command className="rounded-lg border shadow-md">
      <div className="flex items-center border-b px-3">
        <SearchIcon className="mr-2 h-4 w-4 shrink-0 opacity-50" />
        <CommandInput
          ref={inputRef}
          placeholder={placeholder}
          value={query}
          onValueChange={setQuery}
          onKeyDown={handleKeyDown}
          className="flex-1"
        />
        {query && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => setQuery('')}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      <CommandList>
        <CommandEmpty>
          {query ? (
            <div className="py-6 text-center text-sm">
              <p>No results found for &quot;{query}&quot;</p>
              <Button 
                variant="link" 
                className="mt-2"
                onClick={() => handleSearch(query)}
              >
                Search for &quot;{query}&quot;
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div className="py-6 text-center text-sm">
              <p>Type to search</p>
            </div>
          )}
        </CommandEmpty>
        
        {/* Suggestions */}
        {query && suggestions.length > 0 && (
          <CommandGroup heading="Suggestions">
            {isLoadingSuggestions ? (
              Array.from({ length: 3 }).map((_, i) => (
                <CommandItem key={i} disabled>
                  <Skeleton className="h-4 w-full" />
                </CommandItem>
              ))
            ) : (
              suggestions.map((suggestion) => (
                <CommandItem
                  key={suggestion.id}
                  onSelect={() => handleSuggestionClick(suggestion)}
                  className="flex items-center"
                >
                  <Sparkles className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span dangerouslySetInnerHTML={{ 
                    __html: suggestion.highlights?.title?.[0] || suggestion.text 
                  }} />
                </CommandItem>
              ))
            )}
          </CommandGroup>
        )}
        
        {/* Recent Searches */}
        {showRecent && recentSearches.length > 0 && (
          <CommandGroup heading={
            <div className="flex items-center justify-between">
              <span>Recent Searches</span>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 text-xs"
                onClick={handleClearRecentSearches}
              >
                Clear
              </Button>
            </div>
          }>
            {isLoadingRecentSearches ? (
              Array.from({ length: 3 }).map((_, i) => (
                <CommandItem key={i} disabled>
                  <Skeleton className="h-4 w-full" />
                </CommandItem>
              ))
            ) : (
              recentSearches.map((search, index) => (
                <CommandItem
                  key={index}
                  onSelect={() => handleRecentSearchClick(search)}
                >
                  <History className="mr-2 h-4 w-4 text-muted-foreground" />
                  {search}
                </CommandItem>
              ))
            )}
          </CommandGroup>
        )}
        
        {/* Popular Searches */}
        {showPopular && popularQueries.length > 0 && (
          <CommandGroup heading="Popular Searches">
            {isLoadingPopularQueries ? (
              Array.from({ length: 3 }).map((_, i) => (
                <CommandItem key={i} disabled>
                  <Skeleton className="h-4 w-full" />
                </CommandItem>
              ))
            ) : (
              popularQueries.map((popularQuery, index) => (
                <CommandItem
                  key={index}
                  onSelect={() => handlePopularQueryClick({
                    ...popularQuery,
                    lastUsed: new Date().toISOString()
                  })}
                >
                  <TrendingUp className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>{popularQuery.query}</span>
                  <Badge variant="secondary" className="ml-auto">
                    {popularQuery.count}
                  </Badge>
                </CommandItem>
              ))
            )}
          </CommandGroup>
        )}
        
        <CommandSeparator />
        
        <CommandGroup>
          <CommandItem
            onSelect={() => router.push('/search')}
            className="justify-center text-center"
          >
            <span>Advanced Search</span>
            <ArrowRight className="ml-2 h-4 w-4" />
          </CommandItem>
        </CommandGroup>
      </CommandList>
    </Command>
  );
}

export default SearchSuggestions;
