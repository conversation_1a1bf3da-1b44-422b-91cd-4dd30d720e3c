"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2, Star, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/components/ui/use-toast";
import { backendApiClient } from "@/services/backend-api-client";

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  isPopular?: boolean;
  variantId: string;
  storeId: string;
}

interface SubscriptionPlanSelectorProps {
  onPlanSelected?: (planId: string) => void;
  showFreeOption?: boolean;
  preselectedPlan?: string;
  className?: string;
}

export function SubscriptionPlanSelector({
  onPlanSelected,
  showFreeOption = true,
  preselectedPlan,
  className = ""
}: SubscriptionPlanSelectorProps) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(preselectedPlan || null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [configError, setConfigError] = useState<string | null>(null);
  const { login } = useAuth();
  const { toast } = useToast();

  // Fetch available subscription plans
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setIsLoading(true);
        const response = await backendApiClient.request<SubscriptionPlan[]>('/subscriptions/plans');
        setPlans(response || []);
      } catch (error: any) {
        console.error('Failed to fetch subscription plans:', error);

        // Check if this is a configuration error (503 status)
        if (error?.response?.status === 503 || error?.message?.includes('not configured')) {
          setConfigError('Subscription service is not configured. Only free plan is available.');
          console.warn('LemonSqueezy not configured, showing free plan only');
        } else {
          toast({
            title: "Failed to load plans",
            description: "Please refresh the page to try again.",
            variant: "destructive",
          });
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlans();
  }, [toast]);

  const handlePlanSelection = (planId: string) => {
    setSelectedPlan(planId);
    onPlanSelected?.(planId);
  };

  const handleContinueWithPlan = async () => {
    if (!selectedPlan) {
      toast({
        title: "Please select a plan",
        description: "Choose a subscription plan to continue.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      if (selectedPlan === 'free') {
        // Continue with free plan (no subscription needed)
        console.log('Selected free plan');
        window.location.href = "/dashboard";
      } else {
        // Continue with paid plan
        console.log('Selected plan:', selectedPlan);
        window.location.href = "/onboarding";
      }
    } catch (error) {
      console.error('Failed to continue with plan:', error);
      toast({
        title: "Failed to continue",
        description: "Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const formatPrice = (price: number, currency: string, interval: string) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    });
    return `${formatter.format(price / 100)}/${interval}`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading subscription plans...</span>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Choose Your Plan</h2>
        <p className="text-muted-foreground">
          Select a subscription plan that fits your needs. You can change or cancel anytime.
        </p>
      </div>

      {/* Configuration Warning */}
      {configError && (
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription className="text-orange-800">
            {configError}
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Free Plan Option */}
        {showFreeOption && (
          <Card
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedPlan === 'free' ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => handlePlanSelection('free')}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Free Plan
                {selectedPlan === 'free' && (
                  <Check className="h-5 w-5 text-primary" />
                )}
              </CardTitle>
              <CardDescription>Perfect for getting started</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-4">$0</div>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  5 documents per month
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Basic document processing
                </li>
                <li className="flex items-center">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Email support
                </li>
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Paid Plans */}
        {plans.map((plan) => (
          <Card
            key={plan.variantId}
            className={`cursor-pointer transition-all hover:shadow-md relative ${
              selectedPlan === plan.variantId ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => handlePlanSelection(plan.variantId)}
          >
            {plan.isPopular && (
              <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <Star className="h-3 w-3 mr-1" />
                Most Popular
              </Badge>
            )}

            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {plan.name}
                {selectedPlan === plan.variantId && (
                  <Check className="h-5 w-5 text-primary" />
                )}
              </CardTitle>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>

            <CardContent>
              <div className="text-3xl font-bold mb-4">
                {formatPrice(plan.price, plan.currency, plan.interval)}
              </div>

              <ul className="space-y-2 text-sm">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Continue Button */}
      <div className="flex justify-center pt-6">
        <Button
          onClick={handleContinueWithPlan}
          disabled={!selectedPlan || isProcessing}
          size="lg"
          className="min-w-[200px]"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            `Continue with ${selectedPlan === 'free' ? 'Free Plan' : 'Selected Plan'}`
          )}
        </Button>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <p>
          By continuing, you agree to our{" "}
          <a href="/terms" className="underline hover:text-primary">
            Terms of Service
          </a>{" "}
          and{" "}
          <a href="/privacy" className="underline hover:text-primary">
            Privacy Policy
          </a>
        </p>
      </div>
    </div>
  );
}
