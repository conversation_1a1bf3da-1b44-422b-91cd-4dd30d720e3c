/**
 * Lazy Component Registry - Comprehensive Lazy Loading for Heavy Components
 * Covers all major components that should be loaded only when needed
 * Prevents unnecessary bundle size and improves initial page load performance
 */

"use client"

import React, { Suspense, lazy } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2 } from 'lucide-react'

// ============================================================================
// DOCUMENT COMPONENTS - Heavy PDF/Editor components
// ============================================================================

export const LazyAdvancedDocumentViewer = lazy(() => 
  import('@/components/documents/advanced-document-viewer').then(module => ({ 
    default: module.AdvancedDocumentViewer 
  }))
)

export const LazyEditorJSRichTextEditor = lazy(() => 
  import('@/components/documents/editorjs-rich-text-editor').then(module => ({ 
    default: module.EditorJSRichTextEditor 
  }))
)

export const LazyProductionRichTextEditor = lazy(() => 
  import('@/components/documents/production-rich-text-editor').then(module => ({ 
    default: module.ProductionRichTextEditor 
  }))
)

export const LazyDocumentVisualization = lazy(() => 
  import('@/components/documents/document-visualization').then(module => ({ 
    default: module.DocumentVisualization 
  }))
)

export const LazyInteractivePDFEditor = lazy(() => 
  import('@/components/documents/interactive-pdf-editor').then(module => ({ 
    default: module.InteractivePDFEditor 
  }))
)

export const LazyDocumentSigningModern = lazy(() => 
  import('@/components/documents/document-signing-modern').then(module => ({ 
    default: module.DocumentSigningModern 
  }))
)

// ============================================================================
// AI COMPONENTS - Heavy AI processing components
// ============================================================================

export const LazyAIDocumentAssistant = lazy(() => 
  import('@/components/documents/ai-document-assistant').then(module => ({ 
    default: module.AIDocumentAssistant 
  }))
)

export const LazyAIModelConfigEditor = lazy(() => 
  import('@/components/ai/AIModelConfigEditor').then(module => ({ 
    default: module.AIModelConfigEditor 
  }))
)

export const LazyAIModelDatasetSelector = lazy(() => 
  import('@/components/ai/AIModelDatasetSelector').then(module => ({ 
    default: module.AIModelDatasetSelector 
  }))
)

export const LazyAIModelForm = lazy(() => 
  import('@/components/ai/AIModelForm').then(module => ({ 
    default: module.AIModelForm 
  }))
)

// ============================================================================
// ANALYTICS COMPONENTS - Heavy chart/visualization components
// ============================================================================

export const LazyComprehensiveAnalyticsDashboard = lazy(() => 
  import('@/components/analytics/comprehensive-analytics-dashboard').then(module => ({ 
    default: module.ComprehensiveAnalyticsDashboard 
  }))
)

export const LazySearchAnalyticsPanel = lazy(() => 
  import('@/components/analytics/SearchAnalyticsPanel').then(module => ({ 
    default: module.SearchAnalyticsPanel 
  }))
)

export const LazyWorkflowAnalytics = lazy(() => 
  import('@/components/analytics/workflow-analytics').then(module => ({ 
    default: module.WorkflowAnalytics 
  }))
)

// ============================================================================
// WORKFLOW COMPONENTS - Complex workflow builders
// ============================================================================

export const LazyWorkflowBuilder = lazy(() => 
  import('@/components/workflows/workflow-builder').then(module => ({ 
    default: module.WorkflowBuilder 
  }))
)

export const LazyWorkflowDesigner = lazy(() => 
  import('@/components/workflows/workflow-designer').then(module => ({ 
    default: module.WorkflowDesigner 
  }))
)

export const LazyWorkflowAutomationCenter = lazy(() => 
  import('@/components/workflow/WorkflowAutomationCenter').then(module => ({ 
    default: module.WorkflowAutomationCenter 
  }))
)

export const LazyOrganizationalWorkflowDashboard = lazy(() => 
  import('@/components/workflows/organizational-workflow-dashboard').then(module => ({ 
    default: module.OrganizationalWorkflowDashboard 
  }))
)

// ============================================================================
// ADMIN COMPONENTS - Heavy monitoring/management components
// ============================================================================

export const LazyEventGridMonitor = lazy(() =>
  import('@/components/admin/EventGridMonitor')
)

export const LazySystemMonitoringDashboard = lazy(() => 
  import('@/components/admin/system-monitoring-dashboard').then(module => ({ 
    default: module.SystemMonitoringDashboard 
  }))
)

export const LazyNotificationAnalyticsDashboard = lazy(() =>
  import('@/components/admin/notification-analytics-dashboard')
)

export const LazyMobileDeviceManagement = lazy(() => 
  import('@/components/admin/mobile-device-management').then(module => ({ 
    default: module.MobileDeviceManagement 
  }))
)

// ============================================================================
// BULK OPERATIONS - Heavy processing components
// ============================================================================

export const LazyBulkOperationsDashboard = lazy(() => 
  import('@/components/bulk-operations/BulkOperationsDashboard').then(module => ({ 
    default: module.BulkOperationsDashboard 
  }))
)

export const LazyDocumentProcessingCenter = lazy(() => 
  import('@/components/document-processing/DocumentProcessingCenter').then(module => ({ 
    default: module.DocumentProcessingCenter 
  }))
)

// ============================================================================
// SECURITY COMPONENTS - Heavy security/audit components
// ============================================================================

export const LazySecurityDashboard = lazy(() => 
  import('@/components/security/security-dashboard').then(module => ({ 
    default: module.SecurityDashboard 
  }))
)

export const LazyAuditLogViewer = lazy(() => 
  import('@/components/security/audit-log-viewer').then(module => ({ 
    default: module.AuditLogViewer 
  }))
)

// ============================================================================
// PKI/SIGNATURE COMPONENTS - Heavy cryptographic components
// ============================================================================

export const LazyEnhancedDocumentSigning = lazy(() => 
  import('@/components/pki/enhanced-document-signing').then(module => ({ 
    default: module.EnhancedDocumentSigning 
  }))
)

export const LazyPKIConfigurationPanel = lazy(() => 
  import('@/components/pki/pki-configuration-panel').then(module => ({ 
    default: module.PKIConfigurationPanel 
  }))
)

export const LazyDocuSignCenter = lazy(() => 
  import('@/components/docusign/DocuSignCenter').then(module => ({ 
    default: module.DocuSignCenter 
  }))
)

// ============================================================================
// TEMPLATE COMPONENTS - Heavy editor components
// ============================================================================

export const LazyEditorJSTemplateEditor = lazy(() => 
  import('@/components/templates/editorjs-template-editor').then(module => ({ 
    default: module.EditorJSTemplateEditor 
  }))
)

export const LazyTemplateEditor = lazy(() => 
  import('@/components/templates/template-editor').then(module => ({ 
    default: module.TemplateEditor 
  }))
)

// ============================================================================
// LOADING COMPONENTS
// ============================================================================

interface LazyWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  error?: React.ReactNode
}

export function LazyWrapper({ children, fallback, error }: LazyWrapperProps) {
  const defaultFallback = (
    <div className="flex items-center justify-center p-8">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto" />
        <p className="text-sm text-muted-foreground">Loading component...</p>
      </div>
    </div>
  )

  const defaultError = (
    <Alert variant="destructive">
      <AlertDescription>
        Failed to load component. Please refresh the page.
      </AlertDescription>
    </Alert>
  )

  return (
    <Suspense fallback={fallback || defaultFallback}>
      <ErrorBoundary fallback={error || defaultError}>
        {children}
      </ErrorBoundary>
    </Suspense>
  )
}

// Simple error boundary
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError() {
    return { hasError: true }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// ============================================================================
// COMPONENT SKELETONS - Specific loading states
// ============================================================================

export function DocumentViewerSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-20" />
        </div>
      </div>
      <Skeleton className="h-96 w-full" />
      <div className="flex items-center justify-center space-x-4">
        <Skeleton className="h-8 w-8" />
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-8 w-8" />
      </div>
    </div>
  )
}

export function AnalyticsDashboardSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="space-y-3">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-4 w-16" />
        </div>
      ))}
    </div>
  )
}

export function WorkflowBuilderSkeleton() {
  return (
    <div className="flex h-96">
      <div className="flex-1 p-4 space-y-4">
        <Skeleton className="h-8 w-48" />
        <div className="grid grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
      <div className="w-80 border-l p-4 space-y-4">
        <Skeleton className="h-6 w-32" />
        <div className="space-y-2">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-10 w-full" />
          ))}
        </div>
      </div>
    </div>
  )
}
