/**
 * Organizations Hook
 * Manages organization data and operations
 */

import { useState, useCallback, useEffect } from 'react'
import { useApi, useMutation } from './useApi'
import { useAuth } from './useAuthStore'
import type { ID } from '../types'

export interface Organization {
  id: ID
  name: string
  displayName: string
  description?: string
  domain?: string
  logo?: string
  settings: {
    allowPublicSignup: boolean
    requireEmailVerification: boolean
    defaultRole: string
    maxUsers?: number
    features: string[]
  }
  subscription?: {
    plan: string
    status: 'active' | 'inactive' | 'cancelled' | 'past_due'
    expiresAt?: string
  }
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
  createdBy: ID
}

export interface OrganizationMember {
  id: ID
  userId: ID
  organizationId: ID
  role: string
  status: 'active' | 'inactive' | 'pending' | 'suspended'
  joinedAt: string
  invitedBy?: ID
  user: {
    id: ID
    email: string
    firstName: string
    lastName: string
    displayName: string
    avatar?: string
  }
}

export interface CreateOrganizationRequest {
  name: string
  displayName: string
  description?: string
  domain?: string
  settings?: Partial<Organization['settings']>
  metadata?: Record<string, any>
}

export interface UpdateOrganizationRequest {
  name?: string
  displayName?: string
  description?: string
  domain?: string
  logo?: string
  settings?: Partial<Organization['settings']>
  metadata?: Record<string, any>
}

export interface InviteMemberRequest {
  email: string
  role: string
  message?: string
}

export interface UseOrganizationsResult {
  organizations: Organization[]
  currentOrganization: Organization | null
  members: OrganizationMember[]
  loading: boolean
  isLoading: boolean // Alias for loading
  error: string | null
  
  // Organization operations
  createOrganization: (request: CreateOrganizationRequest) => Promise<Organization>
  updateOrganization: (id: ID, request: UpdateOrganizationRequest) => Promise<Organization>
  deleteOrganization: (id: ID) => Promise<void>
  switchOrganization: (id: ID) => Promise<void>
  
  // Member operations
  inviteMember: (organizationId: ID, request: InviteMemberRequest) => Promise<void>
  removeMember: (organizationId: ID, userId: ID) => Promise<void>
  updateMemberRole: (organizationId: ID, userId: ID, role: string) => Promise<void>
  
  // Utility functions
  refresh: () => Promise<void>
  getOrganizationById: (id: ID) => Organization | undefined
  getMembersByOrganization: (organizationId: ID) => OrganizationMember[]
  isUserMember: (organizationId: ID, userId: ID) => boolean
}

export function useOrganizations(): UseOrganizationsResult {
  const { user } = useAuth()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null)
  const [members, setMembers] = useState<OrganizationMember[]>([])
  
  // Fetch organizations
  const { data: orgsData, isLoading: orgsLoading, error: orgsError, refetch: refetchOrgs } = useApi<Organization[]>(
    '/organizations',
    { immediate: !!user }
  )
  
  // Fetch members for current organization
  const { data: membersData, isLoading: membersLoading, refetch: refetchMembers } = useApi<OrganizationMember[]>(
    currentOrganization ? `/organizations/${currentOrganization.id}/members` : '',
    { immediate: !!currentOrganization }
  )
  
  // Update local state
  useEffect(() => {
    if (orgsData) {
      setOrganizations(orgsData)
      // Set current organization if not set
      if (!currentOrganization && orgsData.length > 0) {
        setCurrentOrganization(orgsData[0])
      }
    }
  }, [orgsData, currentOrganization])
  
  useEffect(() => {
    if (membersData) {
      setMembers(membersData)
    }
  }, [membersData])
  
  // Create organization mutation
  const createMutation = useMutation({
    mutationFn: (request: CreateOrganizationRequest) => fetch('/organizations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => res.json()),
    onSuccess: (newOrg: Organization) => {
      setOrganizations(prev => [...prev, newOrg])
    }
  })
  
  // Update organization mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, request }: { id: ID; request: UpdateOrganizationRequest }) => fetch(`/organizations/${id}`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => res.json()),
    onSuccess: (updatedOrg: Organization) => {
      setOrganizations(prev =>
        prev.map(org => org.id === updatedOrg.id ? updatedOrg : org)
      )
      if (currentOrganization?.id === updatedOrg.id) {
        setCurrentOrganization(updatedOrg)
      }
    }
  })
  
  // Delete organization mutation
  const deleteMutation = useMutation({
    mutationFn: (id: ID) => fetch(`/organizations/${id}`, {
      method: 'DELETE',
    }).then(res => {
      if (!res.ok) throw new Error('Failed to delete organization')
    }),
    onSuccess: (_, id: ID) => {
      setOrganizations(prev => prev.filter(org => org.id !== id))
      if (currentOrganization?.id === id) {
        setCurrentOrganization(organizations.find(org => org.id !== id) || null)
      }
    }
  })
  
  // Invite member mutation
  const inviteMutation = useMutation({
    mutationFn: ({ organizationId, request }: { organizationId: ID; request: InviteMemberRequest }) => fetch(`/organizations/${organizationId}/members/invite`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    }).then(res => {
      if (!res.ok) throw new Error('Failed to invite member')
    }),
    onSuccess: () => {
      refetchMembers()
    }
  })
  
  // Remove member mutation
  const removeMemberMutation = useMutation<void, Error, { organizationId: ID; userId: ID }>({
    mutationFn: ({ organizationId, userId }) => fetch(`/organizations/${organizationId}/members/${userId}`, {
      method: 'DELETE',
    }).then(res => {
      if (!res.ok) throw new Error('Failed to remove member')
    }),
    onSuccess: (_, { userId }) => {
      setMembers(prev => prev.filter(member => member.userId !== userId))
    }
  })
  
  // Update member role mutation
  const updateMemberRoleMutation = useMutation<void, Error, { organizationId: ID; userId: ID; role: string }>({
    mutationFn: ({ organizationId, userId, role }) => fetch(`/api/organizations/${organizationId}/members/${userId}/role`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ role }),
    }).then(res => {
      if (!res.ok) throw new Error('Failed to update member role')
    }),
    onSuccess: (_, { userId, role }) => {
      setMembers(prev =>
        prev.map(member =>
          member.userId === userId ? { ...member, role } : member
        )
      )
    }
  })
  
  // Action functions
  const createOrganization = useCallback(async (request: CreateOrganizationRequest) => {
    return createMutation.mutateAsync(request)
  }, [createMutation])
  
  const updateOrganization = useCallback(async (id: ID, request: UpdateOrganizationRequest) => {
    return updateMutation.mutateAsync({ id, request })
  }, [updateMutation])
  
  const deleteOrganization = useCallback(async (id: ID) => {
    return deleteMutation.mutateAsync(id)
  }, [deleteMutation])
  
  const switchOrganization = useCallback(async (id: ID) => {
    const org = organizations.find(o => o.id === id)
    if (org) {
      setCurrentOrganization(org)
    }
  }, [organizations])
  
  const inviteMember = useCallback(async (organizationId: ID, request: InviteMemberRequest) => {
    return inviteMutation.mutateAsync({ organizationId, request })
  }, [inviteMutation])
  
  const removeMember = useCallback(async (organizationId: ID, userId: ID) => {
    return removeMemberMutation.mutateAsync({ organizationId, userId })
  }, [removeMemberMutation])
  
  const updateMemberRole = useCallback(async (organizationId: ID, userId: ID, role: string) => {
    return updateMemberRoleMutation.mutateAsync({ organizationId, userId, role })
  }, [updateMemberRoleMutation])
  
  const refresh = useCallback(async () => {
    await Promise.all([refetchOrgs(), refetchMembers()])
  }, [refetchOrgs, refetchMembers])
  
  // Utility functions
  const getOrganizationById = useCallback((id: ID) => {
    return organizations.find(org => org.id === id)
  }, [organizations])
  
  const getMembersByOrganization = useCallback((organizationId: ID) => {
    return members.filter(member => member.organizationId === organizationId)
  }, [members])
  
  const isUserMember = useCallback((organizationId: ID, userId: ID) => {
    return members.some(member => 
      member.organizationId === organizationId && 
      member.userId === userId &&
      member.status === 'active'
    )
  }, [members])
  
  const loading = orgsLoading || membersLoading || createMutation.isPending || updateMutation.isPending || deleteMutation.isPending
  const error = orgsError || (createMutation.error as any)?.message || (updateMutation.error as any)?.message || (deleteMutation.error as any)?.message || null
  
  return {
    organizations,
    currentOrganization,
    members,
    loading,
    isLoading: loading, // Alias for loading
    error: error instanceof Error ? error.message : error,
    
    createOrganization,
    updateOrganization,
    deleteOrganization,
    switchOrganization,
    
    inviteMember,
    removeMember,
    updateMemberRole,
    
    refresh,
    getOrganizationById,
    getMembersByOrganization,
    isUserMember,
  }
}
