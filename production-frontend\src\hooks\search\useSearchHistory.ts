/**
 * Search History Hook
 * Manages search history and recent searches
 */

import { useState, useCallback, useEffect } from 'react'
import type { SearchOptions } from './useSearch'

export interface SearchHistoryItem {
  id: string
  query: string
  filters?: any
  timestamp: string
  resultCount: number
}

export interface UseSearchHistoryResult {
  history: SearchHistoryItem[]
  recentSearches: string[]
  
  addToHistory: (options: SearchOptions, resultCount: number) => void
  removeFromHistory: (id: string) => void
  clearHistory: () => void
  getPopularSearches: () => string[]
}

const STORAGE_KEY = 'search-history'
const MAX_HISTORY_ITEMS = 50
const MAX_RECENT_SEARCHES = 10

export function useSearchHistory(): UseSearchHistoryResult {
  const [history, setHistory] = useState<SearchHistoryItem[]>([])

  // Load history from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsed = JSON.parse(stored)
        setHistory(parsed)
      }
    } catch (error) {
      console.error('Failed to load search history:', error)
    }
  }, [])

  // Save history to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(history))
    } catch (error) {
      console.error('Failed to save search history:', error)
    }
  }, [history])

  const addToHistory = useCallback((options: SearchOptions, resultCount: number) => {
    const item: SearchHistoryItem = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      query: options.query,
      filters: options.filters,
      timestamp: new Date().toISOString(),
      resultCount
    }

    setHistory(prev => {
      // Remove duplicate queries
      const filtered = prev.filter(h => h.query !== options.query)
      
      // Add new item at the beginning
      const updated = [item, ...filtered]
      
      // Limit to max items
      return updated.slice(0, MAX_HISTORY_ITEMS)
    })
  }, [])

  const removeFromHistory = useCallback((id: string) => {
    setHistory(prev => prev.filter(item => item.id !== id))
  }, [])

  const clearHistory = useCallback(() => {
    setHistory([])
  }, [])

  const getPopularSearches = useCallback((): string[] => {
    // Count query frequency
    const queryCount = history.reduce((acc, item) => {
      acc[item.query] = (acc[item.query] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Sort by frequency and return top queries
    return Object.entries(queryCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([query]) => query)
  }, [history])

  const recentSearches = history
    .slice(0, MAX_RECENT_SEARCHES)
    .map(item => item.query)

  return {
    history,
    recentSearches,
    addToHistory,
    removeFromHistory,
    clearHistory,
    getPopularSearches,
  }
}

export default useSearchHistory
