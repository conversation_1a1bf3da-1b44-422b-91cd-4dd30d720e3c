import { useState, useCallback } from 'react'

/**
 * Validation Hook
 * Provides validation utilities and common validation rules
 */

export type ValidationRule<T = any> = (value: T) => string | null

export type ValidationSchema<T> = {
  [K in keyof T]?: ValidationRule<T[K]>[]
}

export interface UseValidationResult<T> {
  errors: Record<keyof T, string>
  isValid: boolean
  validate: (values: T) => boolean
  validateField: (name: keyof T, value: any) => string | null
  clearErrors: () => void
  clearFieldError: (name: keyof T) => void
  setFieldError: (name: keyof T, error: string) => void
}

export function useValidation<T extends Record<string, any>>(
  schema: ValidationSchema<T>
): UseValidationResult<T> {
  const [errors, setErrors] = useState<Record<keyof T, string>>({} as Record<keyof T, string>)

  const validateField = useCallback((name: keyof T, value: any): string | null => {
    const rules = schema[name]
    if (!rules) return null

    for (const rule of rules) {
      const error = rule(value)
      if (error) return error
    }
    return null
  }, [schema])

  const validate = useCallback((values: T): boolean => {
    const newErrors: Record<keyof T, string> = {} as Record<keyof T, string>
    let hasErrors = false

    for (const [name, value] of Object.entries(values)) {
      const error = validateField(name as keyof T, value)
      if (error) {
        newErrors[name as keyof T] = error
        hasErrors = true
      }
    }

    setErrors(newErrors)
    return !hasErrors
  }, [validateField])

  const clearErrors = useCallback(() => {
    setErrors({} as Record<keyof T, string>)
  }, [])

  const clearFieldError = useCallback((name: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[name]
      return newErrors
    })
  }, [])

  const setFieldError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }))
  }, [])

  const isValid = Object.keys(errors).length === 0

  return {
    errors,
    isValid,
    validate,
    validateField,
    clearErrors,
    clearFieldError,
    setFieldError,
  }
}

// Common validation rules
export const validationRules = {
  required: (message = 'This field is required'): ValidationRule => 
    (value) => {
      if (value === null || value === undefined || value === '') {
        return message
      }
      return null
    },

  minLength: (min: number, message?: string): ValidationRule<string> =>
    (value) => {
      if (typeof value === 'string' && value.length < min) {
        return message || `Must be at least ${min} characters`
      }
      return null
    },

  maxLength: (max: number, message?: string): ValidationRule<string> =>
    (value) => {
      if (typeof value === 'string' && value.length > max) {
        return message || `Must be no more than ${max} characters`
      }
      return null
    },

  email: (message = 'Invalid email address'): ValidationRule<string> =>
    (value) => {
      if (typeof value === 'string' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          return message
        }
      }
      return null
    },

  url: (message = 'Invalid URL'): ValidationRule<string> =>
    (value) => {
      if (typeof value === 'string' && value) {
        try {
          new URL(value)
        } catch {
          return message
        }
      }
      return null
    },

  pattern: (regex: RegExp, message = 'Invalid format'): ValidationRule<string> =>
    (value) => {
      if (typeof value === 'string' && value && !regex.test(value)) {
        return message
      }
      return null
    },

  min: (min: number, message?: string): ValidationRule<number> =>
    (value) => {
      if (typeof value === 'number' && value < min) {
        return message || `Must be at least ${min}`
      }
      return null
    },

  max: (max: number, message?: string): ValidationRule<number> =>
    (value) => {
      if (typeof value === 'number' && value > max) {
        return message || `Must be no more than ${max}`
      }
      return null
    },

  oneOf: (options: any[], message?: string): ValidationRule =>
    (value) => {
      if (!options.includes(value)) {
        return message || `Must be one of: ${options.join(', ')}`
      }
      return null
    },

  custom: (validator: (value: any) => boolean, message: string): ValidationRule =>
    (value) => {
      if (!validator(value)) {
        return message
      }
      return null
    },
}
