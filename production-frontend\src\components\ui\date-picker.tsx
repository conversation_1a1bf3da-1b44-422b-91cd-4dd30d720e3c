"use client"

import * as React from "react"
import { format } from "date-fns"
import { CalendarIcon, X } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface DatePickerProps {
  date?: Date
  onSelect: (date?: Date) => void
  disabled?: boolean
  placeholder?: string
  label?: string
  className?: string
  clearable?: boolean
  formatString?: string
  fromYear?: number
  toYear?: number
  fromDate?: Date
  toDate?: Date
  showTimePicker?: boolean
}

export function DatePicker({
  date,
  onSelect,
  disabled = false,
  placeholder = "Pick a date",
  label,
  className,
  clearable = true,
  formatString = "PPP",
  fromYear,
  toYear,
  fromDate,
  toDate,
  showTimePicker = false
}: DatePickerProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [time, setTime] = React.useState<string>(
    date ? `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}` : "00:00"
  )

  // Update time when date changes
  React.useEffect(() => {
    if (date) {
      setTime(`${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`)
    }
  }, [date])

  // Handle time change
  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTime(e.target.value)

    if (date) {
      const [hours, minutes] = e.target.value.split(':').map(Number)
      const newDate = new Date(date)
      newDate.setHours(hours || 0)
      newDate.setMinutes(minutes || 0)
      onSelect(newDate)
    }
  }

  // Handle date selection
  const handleSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      // Preserve time if we already have a date
      if (date) {
        selectedDate.setHours(date.getHours())
        selectedDate.setMinutes(date.getMinutes())
      } else if (showTimePicker) {
        // Set time from input if we're showing time picker
        const [hours, minutes] = time.split(':').map(Number)
        selectedDate.setHours(hours || 0)
        selectedDate.setMinutes(minutes || 0)
      }
    }

    onSelect(selectedDate)

    if (!showTimePicker) {
      setIsOpen(false)
    }
  }

  // Handle clear button click
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect(undefined)
    setTime("00:00")
  }

  return (
    <div className={cn("grid gap-2", className)}>
      {label && <label className="text-sm font-medium">{label}</label>}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <div className="relative">
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
              disabled={disabled}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, formatString) : <span>{placeholder}</span>}
            </Button>
          </PopoverTrigger>

          {clearable && date && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-0"
              onClick={handleClear}
              type="button"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleSelect}
            disabled={disabled}
            initialFocus
            fromYear={fromYear}
            toYear={toYear}
            fromDate={fromDate}
            toDate={toDate}
          />

          {showTimePicker && (
            <div className="p-3 border-t border-border">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Time:</label>
                <Input
                  type="time"
                  value={time}
                  onChange={handleTimeChange}
                  className="w-24"
                />
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  )
}

