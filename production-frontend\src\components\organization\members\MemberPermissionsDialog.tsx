'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { OrganizationMember } from '@/types/organization';

interface MemberPermissionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  member: OrganizationMember;
  onSave: (permissions: string[]) => void;
  isSubmitting: boolean;
}

// Define available permissions
const availablePermissions = [
  {
    id: 'documents.view',
    label: 'View Documents',
    description: 'Can view documents in the organization',
  },
  {
    id: 'documents.create',
    label: 'Create Documents',
    description: 'Can create new documents in the organization',
  },
  {
    id: 'documents.edit',
    label: 'Edit Documents',
    description: 'Can edit existing documents in the organization',
  },
  {
    id: 'documents.delete',
    label: 'Delete Documents',
    description: 'Can delete documents from the organization',
  },
  {
    id: 'projects.view',
    label: 'View Projects',
    description: 'Can view projects in the organization',
  },
  {
    id: 'projects.create',
    label: 'Create Projects',
    description: 'Can create new projects in the organization',
  },
  {
    id: 'projects.edit',
    label: 'Edit Projects',
    description: 'Can edit existing projects in the organization',
  },
  {
    id: 'projects.delete',
    label: 'Delete Projects',
    description: 'Can delete projects from the organization',
  },
  {
    id: 'members.view',
    label: 'View Members',
    description: 'Can view organization members',
  },
  {
    id: 'members.invite',
    label: 'Invite Members',
    description: 'Can invite new members to the organization',
  },
  {
    id: 'members.remove',
    label: 'Remove Members',
    description: 'Can remove members from the organization',
  },
  {
    id: 'settings.view',
    label: 'View Settings',
    description: 'Can view organization settings',
  },
  {
    id: 'settings.edit',
    label: 'Edit Settings',
    description: 'Can edit organization settings',
  },
];

// Group permissions by category
const permissionCategories = [
  {
    name: 'Documents',
    permissions: availablePermissions.filter((p) => p.id.startsWith('documents.')),
  },
  {
    name: 'Projects',
    permissions: availablePermissions.filter((p) => p.id.startsWith('projects.')),
  },
  {
    name: 'Members',
    permissions: availablePermissions.filter((p) => p.id.startsWith('members.')),
  },
  {
    name: 'Settings',
    permissions: availablePermissions.filter((p) => p.id.startsWith('settings.')),
  },
];

export function MemberPermissionsDialog({
  open,
  onOpenChange,
  member,
  onSave,
  isSubmitting,
}: MemberPermissionsDialogProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>(
    member.permissions || []
  );

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions((prev) => [...prev, permissionId]);
    } else {
      setSelectedPermissions((prev) => prev.filter((id) => id !== permissionId));
    }
  };

  const handleSave = () => {
    onSave(selectedPermissions);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Member Permissions</DialogTitle>
          <DialogDescription>
            Configure permissions for {member.name || member.email}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {permissionCategories.map((category) => (
            <div key={category.name} className="space-y-4">
              <h3 className="text-sm font-medium">{category.name}</h3>
              <div className="space-y-2">
                {category.permissions.map((permission) => (
                  <div key={permission.id} className="flex items-start space-x-2">
                    <Checkbox
                      id={permission.id}
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={(checked) =>
                        handlePermissionChange(permission.id, checked === true)
                      }
                    />
                    <div className="grid gap-1.5">
                      <Label
                        htmlFor={permission.id}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {permission.label}
                      </Label>
                      <p className="text-xs text-muted-foreground">
                        {permission.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isSubmitting}>
            {isSubmitting ? 'Saving...' : 'Save Permissions'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
