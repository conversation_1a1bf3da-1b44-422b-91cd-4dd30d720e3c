import { useState, useCallback } from 'react'

/**
 * Toggle Hook
 * Manages boolean state with toggle functionality
 */

export interface UseToggleResult {
  value: boolean
  toggle: () => void
  setTrue: () => void
  setFalse: () => void
  setValue: (value: boolean) => void
}

export function useToggle(initialValue: boolean = false): UseToggleResult {
  const [value, setValue] = useState<boolean>(initialValue)

  const toggle = useCallback(() => {
    setValue(prev => !prev)
  }, [])

  const setTrue = useCallback(() => {
    setValue(true)
  }, [])

  const setFalse = useCallback(() => {
    setValue(false)
  }, [])

  const setValueCallback = useCallback((newValue: boolean) => {
    setValue(newValue)
  }, [])

  return {
    value,
    toggle,
    setTrue,
    setFalse,
    setValue: setValueCallback,
  }
}
