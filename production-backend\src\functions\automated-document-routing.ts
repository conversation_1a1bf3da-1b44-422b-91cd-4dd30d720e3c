/**
 * Automated Document Routing System
 * AI-powered document classification, routing, and processing automation
 */

import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { enhancedDocumentIntelligence } from '../shared/services/enhanced-document-intelligence';
import { aiServices } from '../shared/services/ai-services';
import { signalREnhanced } from '../shared/services/signalr';
import { eventGridIntegration } from '../shared/services/event-grid-integration';

// Document routing interfaces
export interface DocumentRoutingRule {
  id: string;
  name: string;
  description: string;
  priority: number;
  enabled: boolean;
  conditions: RoutingCondition[];
  actions: RoutingAction[];
  department: string;
  documentTypes: string[];
  organizationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  statistics: RoutingStatistics;
}

export interface RoutingCondition {
  id: string;
  type: ConditionType;
  field: string;
  operator: ConditionOperator;
  value: any;
  logicalOperator?: LogicalOperator;
  aiAssisted: boolean;
  confidence?: number;
}

export interface RoutingAction {
  id: string;
  type: ActionType;
  parameters: Record<string, any>;
  order: number;
  enabled: boolean;
  conditions?: RoutingCondition[];
}

export interface DocumentProcessingPipeline {
  id: string;
  name: string;
  description: string;
  stages: ProcessingStage[];
  documentTypes: string[];
  organizationId: string;
  isActive: boolean;
  settings: PipelineSettings;
  statistics: PipelineStatistics;
}

export interface ProcessingStage {
  id: string;
  name: string;
  type: StageType;
  order: number;
  configuration: StageConfiguration;
  dependencies: string[];
  timeoutMinutes: number;
  retryPolicy: RetryPolicy;
  enabled: boolean;
}

export interface AutomatedWorkflowExecution {
  id: string;
  documentId: string;
  pipelineId: string;
  routingRuleId?: string;
  currentStage: number;
  status: ExecutionStatus;
  startedAt: string;
  completedAt?: string;
  results: StageResult[];
  errors: ExecutionError[];
  organizationId: string;
  triggeredBy: string;
}

// Enums
export enum ConditionType {
  DOCUMENT_TYPE = 'document_type',
  FILE_SIZE = 'file_size',
  CONTENT_CONTAINS = 'content_contains',
  METADATA_FIELD = 'metadata_field',
  AI_CLASSIFICATION = 'ai_classification',
  SENDER_EMAIL = 'sender_email',
  DEPARTMENT = 'department',
  SECURITY_LEVEL = 'security_level',
  COMPLIANCE_FLAG = 'compliance_flag'
}

export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  REGEX_MATCH = 'regex_match',
  IN_LIST = 'in_list',
  NOT_IN_LIST = 'not_in_list'
}

export enum LogicalOperator {
  AND = 'and',
  OR = 'or',
  NOT = 'not'
}

export enum ActionType {
  ASSIGN_WORKFLOW = 'assign_workflow',
  ROUTE_TO_DEPARTMENT = 'route_to_department',
  ASSIGN_APPROVER = 'assign_approver',
  SET_PRIORITY = 'set_priority',
  ADD_TAGS = 'add_tags',
  SEND_NOTIFICATION = 'send_notification',
  TRIGGER_ANALYSIS = 'trigger_analysis',
  APPLY_TEMPLATE = 'apply_template',
  SET_RETENTION_POLICY = 'set_retention_policy',
  REQUIRE_SIGNATURE = 'require_signature',
  ESCALATE = 'escalate',
  ARCHIVE = 'archive'
}

export enum StageType {
  DOCUMENT_ANALYSIS = 'document_analysis',
  CONTENT_EXTRACTION = 'content_extraction',
  CLASSIFICATION = 'classification',
  VALIDATION = 'validation',
  ROUTING = 'routing',
  APPROVAL = 'approval',
  SIGNATURE = 'signature',
  NOTIFICATION = 'notification',
  ARCHIVAL = 'archival',
  CUSTOM = 'custom'
}

export enum ExecutionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused'
}

// Supporting interfaces
export interface RoutingStatistics {
  totalDocumentsProcessed: number;
  successfulRoutes: number;
  failedRoutes: number;
  averageProcessingTimeMs: number;
  lastExecuted?: string;
}

export interface StageConfiguration {
  aiModel?: string;
  analysisTypes?: string[];
  extractionFields?: string[];
  validationRules?: ValidationRule[];
  approvers?: string[];
  notificationTemplates?: string[];
  customParameters?: Record<string, any>;
}

export interface RetryPolicy {
  maxRetries: number;
  retryDelayMs: number;
  exponentialBackoff: boolean;
  retryConditions: string[];
}

export interface PipelineSettings {
  parallelProcessing: boolean;
  maxConcurrentDocuments: number;
  timeoutMinutes: number;
  enableAuditTrail: boolean;
  notificationSettings: NotificationSettings;
  errorHandling: ErrorHandlingSettings;
}

export interface PipelineStatistics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTimeMinutes: number;
  documentsProcessed: number;
  currentActiveExecutions: number;
}

export interface StageResult {
  stageId: string;
  stageName: string;
  status: ExecutionStatus;
  startedAt: string;
  completedAt?: string;
  output: Record<string, any>;
  metrics: Record<string, number>;
  errors: string[];
}

export interface ExecutionError {
  stageId: string;
  errorType: string;
  message: string;
  details: Record<string, any>;
  timestamp: string;
  retryable: boolean;
}

export interface ValidationRule {
  field: string;
  type: 'required' | 'format' | 'range' | 'custom';
  parameters: Record<string, any>;
  errorMessage: string;
}

export interface NotificationSettings {
  onStart: boolean;
  onComplete: boolean;
  onError: boolean;
  recipients: string[];
  channels: string[];
}

export interface ErrorHandlingSettings {
  continueOnError: boolean;
  maxErrors: number;
  escalateOnFailure: boolean;
  escalationRecipients: string[];
}

class AutomatedDocumentRouting {
  private readonly serviceBus = ServiceBusEnhancedService.getInstance();
  private readonly CACHE_TTL = 3600; // 1 hour

  /**
   * Create document routing rule
   */
  async createRoutingRule(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const body = await request.json() as any;
      const schema = Joi.object({
        name: Joi.string().min(1).max(255).required(),
        description: Joi.string().max(1000).required(),
        priority: Joi.number().min(1).max(100).default(50),
        department: Joi.string().required(),
        documentTypes: Joi.array().items(Joi.string()).min(1).required(),
        conditions: Joi.array().items(Joi.object({
          type: Joi.string().valid(...Object.values(ConditionType)).required(),
          field: Joi.string().required(),
          operator: Joi.string().valid(...Object.values(ConditionOperator)).required(),
          value: Joi.any().required(),
          logicalOperator: Joi.string().valid(...Object.values(LogicalOperator)).optional(),
          aiAssisted: Joi.boolean().default(false)
        })).min(1).required(),
        actions: Joi.array().items(Joi.object({
          type: Joi.string().valid(...Object.values(ActionType)).required(),
          parameters: Joi.object().required(),
          order: Joi.number().min(1).required(),
          enabled: Joi.boolean().default(true)
        })).min(1).required()
      });

      const { error, value } = schema.validate(body);
      if (error) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: error.details[0].message }
        }, request);
      }

      // Check permissions
      const hasPermission = await this.checkRoutingPermission(
        authResult.user?.organizationId || '',
        authResult.user?.id || '',
        'create_routing_rule'
      );

      if (!hasPermission) {
        return addCorsHeaders({
          status: 403,
          jsonBody: { error: 'Insufficient permissions to create routing rules' }
        }, request);
      }

      const ruleId = uuidv4();
      const now = new Date().toISOString();

      // Process conditions and actions
      const conditions = value.conditions.map((condition: any) => ({
        id: uuidv4(),
        type: condition.type,
        field: condition.field,
        operator: condition.operator,
        value: condition.value,
        logicalOperator: condition.logicalOperator,
        aiAssisted: condition.aiAssisted,
        confidence: condition.aiAssisted ? 0.8 : 1.0
      }));

      const actions = value.actions.map((action: any) => ({
        id: uuidv4(),
        type: action.type,
        parameters: action.parameters,
        order: action.order,
        enabled: action.enabled,
        conditions: action.conditions || []
      }));

      const routingRule: DocumentRoutingRule = {
        id: ruleId,
        name: value.name,
        description: value.description,
        priority: value.priority,
        enabled: true,
        conditions,
        actions,
        department: value.department,
        documentTypes: value.documentTypes,
        organizationId: authResult.user?.organizationId || '',
        createdBy: authResult.user?.id || '',
        createdAt: now,
        updatedAt: now,
        statistics: {
          totalDocumentsProcessed: 0,
          successfulRoutes: 0,
          failedRoutes: 0,
          averageProcessingTimeMs: 0
        }
      };

      // Store routing rule
      await db.createItem('document-routing-rules', routingRule);

      // Cache routing rule
      await redis.setex(`routing-rule:${ruleId}`, this.CACHE_TTL, JSON.stringify(routingRule));

      // Publish event
      await eventGridIntegration.publishEvent({
        eventType: 'DocumentRouting.RuleCreated',
        subject: `routing/rules/${ruleId}`,
        data: {
          ruleId,
          name: routingRule.name,
          department: routingRule.department,
          organizationId: routingRule.organizationId,
          createdBy: authResult.user.id
        }
      });

      return addCorsHeaders({
        status: 201,
        jsonBody: routingRule
      }, request);

    } catch (error) {
      logger.error('Error creating routing rule', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  /**
   * Process document through automated routing
   */
  async processDocumentRouting(request: HttpRequest, _context: InvocationContext): Promise<HttpResponseInit> {
    const preflightResponse = handlePreflight(request);
    if (preflightResponse) {
      return preflightResponse;
    }

    try {
      const authResult = await authenticateRequest(request);
      if (!authResult.success || !authResult.user) {
        return addCorsHeaders({
          status: 401,
          jsonBody: { error: 'Unauthorized' }
        }, request);
      }

      const documentId = request.params?.documentId;
      if (!documentId) {
        return addCorsHeaders({
          status: 400,
          jsonBody: { error: 'Document ID is required' }
        }, request);
      }

      // Get document
      const document = await db.readItem('documents', documentId, documentId);
      if (!document) {
        return addCorsHeaders({
          status: 404,
          jsonBody: { error: 'Document not found' }
        }, request);
      }

      // Perform AI-powered document analysis
      const analysisResult = await this.analyzeDocumentForRouting(document);

      // Find matching routing rules
      const matchingRules = await this.findMatchingRoutingRules(
        document,
        analysisResult,
        authResult.user?.organizationId || ''
      );

      // Execute routing actions
      const routingResults = await this.executeRoutingActions(
        document,
        matchingRules,
        analysisResult,
        authResult.user
      );

      // Create automated workflow execution if needed
      const workflowExecution = await this.createAutomatedExecution(
        document,
        matchingRules,
        authResult.user
      );

      return addCorsHeaders({
        status: 200,
        jsonBody: {
          documentId,
          analysisResult,
          matchingRules: matchingRules.map(r => ({ id: r.id, name: r.name, priority: r.priority })),
          routingResults,
          workflowExecutionId: workflowExecution?.id,
          message: 'Document routing processed successfully'
        }
      }, request);

    } catch (error) {
      logger.error('Error processing document routing', {
        error: error instanceof Error ? error.message : String(error)
      });

      return addCorsHeaders({
        status: 500,
        jsonBody: { error: 'Internal server error' }
      }, request);
    }
  }

  // Private helper methods
  private async checkRoutingPermission(organizationId: string, userId: string, permission: string): Promise<boolean> {
    try {
      const userRole = await db.queryItems('organization-members',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.userId = @userId',
        [organizationId, userId]
      );

      if (userRole.length === 0) {
        return false;
      }

      const member = userRole[0] as any;
      const allowedRoles = ['admin', 'workflow_manager', 'automation_manager'];

      return allowedRoles.includes(member.role) || member.permissions?.includes(permission);
    } catch (error) {
      logger.error('Error checking routing permission', { organizationId, userId, permission, error });
      return false;
    }
  }

  private async analyzeDocumentForRouting(document: any): Promise<any> {
    try {
      // Use AI to analyze document for routing decisions
      const analysisPrompt = `Analyze this document for automated routing and processing.

Document metadata:
- Filename: ${document.fileName}
- Size: ${document.size} bytes
- Content type: ${document.contentType}
- Upload date: ${document.createdAt}

Determine:
1. Document type and category
2. Urgency level (low, normal, high, urgent)
3. Department that should handle this document
4. Required approvals and signatures
5. Compliance requirements
6. Processing recommendations

Return as JSON with: documentType, urgency, department, approvals, compliance, recommendations`;

      const aiResponse = await aiServices.reason(analysisPrompt, [], {
        temperature: 0.3,
        maxTokens: 1500
      });

      // Parse AI response
      return this.parseRoutingAnalysis(aiResponse.content);
    } catch (error) {
      logger.error('Error analyzing document for routing', { documentId: document.id, error });
      return {
        documentType: 'unknown',
        urgency: 'normal',
        department: 'general',
        approvals: [],
        compliance: [],
        recommendations: []
      };
    }
  }

  private parseRoutingAnalysis(aiContent: string): any {
    try {
      const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      return {};
    } catch (error) {
      logger.error('Error parsing routing analysis', { error });
      return {};
    }
  }

  private async findMatchingRoutingRules(
    document: any,
    analysisResult: any,
    organizationId: string
  ): Promise<DocumentRoutingRule[]> {
    try {
      // Get all active routing rules for the organization
      const allRules = await db.queryItems('document-routing-rules',
        'SELECT * FROM c WHERE c.organizationId = @organizationId AND c.enabled = true ORDER BY c.priority DESC',
        [organizationId]
      );

      const matchingRules: DocumentRoutingRule[] = [];

      for (const rule of allRules as DocumentRoutingRule[]) {
        if (await this.evaluateRoutingConditions(rule.conditions, document, analysisResult)) {
          matchingRules.push(rule);
        }
      }

      return matchingRules;
    } catch (error) {
      logger.error('Error finding matching routing rules', { documentId: document.id, organizationId, error });
      return [];
    }
  }

  private async evaluateRoutingConditions(
    conditions: RoutingCondition[],
    document: any,
    analysisResult: any
  ): Promise<boolean> {
    try {
      // Evaluate each condition
      const results: boolean[] = [];

      for (const condition of conditions) {
        const result = await this.evaluateCondition(condition, document, analysisResult);
        results.push(result);
      }

      // Apply logical operators (simplified - assumes AND for now)
      return results.every(result => result);
    } catch (error) {
      logger.error('Error evaluating routing conditions', { error });
      return false;
    }
  }

  private async evaluateCondition(
    condition: RoutingCondition,
    document: any,
    analysisResult: any
  ): Promise<boolean> {
    try {
      let fieldValue: any;

      // Get field value based on condition type
      switch (condition.type) {
        case ConditionType.DOCUMENT_TYPE:
          fieldValue = analysisResult.documentType || 'unknown';
          break;
        case ConditionType.FILE_SIZE:
          fieldValue = document.size;
          break;
        case ConditionType.CONTENT_CONTAINS:
          fieldValue = document.extractedText || '';
          break;
        case ConditionType.AI_CLASSIFICATION:
          fieldValue = analysisResult.documentType;
          break;
        case ConditionType.DEPARTMENT:
          fieldValue = analysisResult.department;
          break;
        default:
          fieldValue = document[condition.field];
      }

      // Evaluate condition based on operator
      switch (condition.operator) {
        case ConditionOperator.EQUALS:
          return fieldValue === condition.value;
        case ConditionOperator.NOT_EQUALS:
          return fieldValue !== condition.value;
        case ConditionOperator.CONTAINS:
          return String(fieldValue).toLowerCase().includes(String(condition.value).toLowerCase());
        case ConditionOperator.NOT_CONTAINS:
          return !String(fieldValue).toLowerCase().includes(String(condition.value).toLowerCase());
        case ConditionOperator.GREATER_THAN:
          return Number(fieldValue) > Number(condition.value);
        case ConditionOperator.LESS_THAN:
          return Number(fieldValue) < Number(condition.value);
        case ConditionOperator.IN_LIST:
          return Array.isArray(condition.value) && condition.value.includes(fieldValue);
        case ConditionOperator.REGEX_MATCH:
          const regex = new RegExp(condition.value);
          return regex.test(String(fieldValue));
        default:
          return false;
      }
    } catch (error) {
      logger.error('Error evaluating condition', { condition: condition.id, error });
      return false;
    }
  }

  private async executeRoutingActions(
    document: any,
    matchingRules: DocumentRoutingRule[],
    analysisResult: any,
    user: any
  ): Promise<any[]> {
    const results: any[] = [];

    for (const rule of matchingRules) {
      for (const action of rule.actions.filter(a => a.enabled)) {
        try {
          const result = await this.executeAction(action, document, analysisResult, user);
          results.push({
            ruleId: rule.id,
            actionId: action.id,
            actionType: action.type,
            result,
            success: true
          });
        } catch (error) {
          logger.error('Error executing routing action', { 
            ruleId: rule.id, 
            actionId: action.id, 
            error 
          });
          results.push({
            ruleId: rule.id,
            actionId: action.id,
            actionType: action.type,
            error: error instanceof Error ? error.message : String(error),
            success: false
          });
        }
      }
    }

    return results;
  }

  private async executeAction(
    action: RoutingAction,
    document: any,
    _analysisResult: any,
    user: any
  ): Promise<any> {
    switch (action.type) {
      case ActionType.ASSIGN_WORKFLOW:
        return await this.assignWorkflow(document.id, action.parameters.workflowId, user);
      
      case ActionType.ROUTE_TO_DEPARTMENT:
        return await this.routeToDepartment(document.id, action.parameters.department, user);
      
      case ActionType.SET_PRIORITY:
        return await this.setPriority(document.id, action.parameters.priority);
      
      case ActionType.ADD_TAGS:
        return await this.addTags(document.id, action.parameters.tags);
      
      case ActionType.SEND_NOTIFICATION:
        return await this.sendNotification(document, action.parameters, user);
      
      case ActionType.TRIGGER_ANALYSIS:
        return await this.triggerAnalysis(document.id, action.parameters.analysisType);
      
      default:
        logger.warn('Unknown action type', { actionType: action.type });
        return { message: 'Action type not implemented' };
    }
  }

  private async assignWorkflow(documentId: string, workflowId: string, user: any): Promise<any> {
    try {
      // Get the workflow definition
      const workflow = await db.readItem('workflows', workflowId, user.organizationId);
      if (!workflow) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      // Create workflow instance
      const workflowInstance = {
        id: uuidv4(),
        workflowId,
        documentId,
        status: 'active',
        currentStep: 0,
        assignedBy: user.id,
        assignedAt: new Date().toISOString(),
        organizationId: user.organizationId,
        variables: {},
        history: [{
          step: 'assignment',
          timestamp: new Date().toISOString(),
          actor: user.id,
          action: 'workflow_assigned',
          details: { workflowId, workflowName: workflow.name }
        }]
      };

      await db.createItem('workflow-instances', workflowInstance);

      // Update document with workflow assignment
      await db.updateItem('documents', {
        id: documentId,
        workflowId,
        workflowInstanceId: workflowInstance.id,
        status: 'in_workflow',
        updatedAt: new Date().toISOString()
      });

      logger.info('Workflow assigned successfully', { documentId, workflowId, instanceId: workflowInstance.id });
      return {
        message: 'Workflow assigned successfully',
        workflowId,
        instanceId: workflowInstance.id,
        workflowName: workflow.name
      };
    } catch (error) {
      logger.error('Failed to assign workflow', { error, documentId, workflowId });
      throw error;
    }
  }

  private async routeToDepartment(documentId: string, department: string, user: any): Promise<any> {
    try {
      // Update document with department assignment
      await db.updateItem('documents', {
        id: documentId,
        assignedDepartment: department,
        routedAt: new Date().toISOString(),
        routedBy: user.id,
        status: 'routed',
        updatedAt: new Date().toISOString()
      });

      // Create routing record
      const routingRecord = {
        id: uuidv4(),
        documentId,
        fromDepartment: user.department || 'system',
        toDepartment: department,
        routedBy: user.id,
        routedAt: new Date().toISOString(),
        reason: 'automated_routing',
        organizationId: user.organizationId
      };

      await db.createItem('document-routing-history', routingRecord);

      // Send notification to department
      await this.notifyDepartment(department, documentId, user.organizationId);

      logger.info('Document routed to department', { documentId, department });
      return {
        message: 'Document routed successfully',
        department,
        routingId: routingRecord.id
      };
    } catch (error) {
      logger.error('Failed to route document to department', { error, documentId, department });
      throw error;
    }
  }

  private async setPriority(documentId: string, priority: string): Promise<any> {
    try {
      // Validate priority value
      const validPriorities = ['low', 'normal', 'high', 'urgent'];
      if (!validPriorities.includes(priority.toLowerCase())) {
        throw new Error(`Invalid priority: ${priority}. Must be one of: ${validPriorities.join(', ')}`);
      }

      // Update document priority
      await db.updateItem('documents', {
        id: documentId,
        priority: priority.toLowerCase(),
        priorityUpdatedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      logger.info('Document priority updated', { documentId, priority });
      return {
        message: 'Priority set successfully',
        priority: priority.toLowerCase(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to set document priority', { error, documentId, priority });
      throw error;
    }
  }

  private async addTags(documentId: string, tags: string[]): Promise<any> {
    try {
      // Get current document to merge tags
      const document = await db.readItem('documents', documentId, documentId);
      if (!document) {
        throw new Error(`Document not found: ${documentId}`);
      }

      // Merge new tags with existing ones
      const existingTags = document.tags || [];
      const newTags = [...new Set([...existingTags, ...tags])]; // Remove duplicates

      // Update document with new tags
      await db.updateItem('documents', {
        id: documentId,
        tags: newTags,
        tagsUpdatedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });

      logger.info('Tags added to document', { documentId, addedTags: tags, totalTags: newTags.length });
      return {
        message: 'Tags added successfully',
        addedTags: tags,
        totalTags: newTags,
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to add tags to document', { error, documentId, tags });
      throw error;
    }
  }

  private async sendNotification(document: any, parameters: any, user: any): Promise<any> {
    try {
      const notification = {
        id: uuidv4(),
        type: 'document_routing',
        title: 'Document Routed',
        message: `Document "${document.name}" has been automatically routed`,
        recipients: parameters.recipients || [],
        documentId: document.id,
        organizationId: user.organizationId,
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        status: 'pending',
        metadata: {
          routingReason: parameters.reason || 'automated_routing',
          documentType: document.type,
          priority: parameters.priority || 'normal'
        }
      };

      await db.createItem('notifications', notification);

      // Send via notification service if available
      try {
        const response = await fetch('/api/notifications/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(notification)
        });

        if (response.ok) {
          logger.info('Notification sent successfully', { notificationId: notification.id });
        } else {
          logger.warn('Failed to send notification via service', {
            notificationId: notification.id,
            status: response.status
          });
        }
      } catch (serviceError) {
        logger.warn('Notification service unavailable, notification stored for later processing', {
          notificationId: notification.id,
          error: serviceError
        });
      }

      return {
        message: 'Notification sent successfully',
        notificationId: notification.id,
        recipients: parameters.recipients
      };
    } catch (error) {
      logger.error('Failed to send notification', { error, documentId: document.id });
      throw error;
    }
  }

  private async triggerAnalysis(documentId: string, analysisType: string): Promise<any> {
    try {
      const analysisRequest = {
        id: uuidv4(),
        documentId,
        analysisType,
        status: 'pending',
        requestedAt: new Date().toISOString(),
        priority: 'normal'
      };

      await db.createItem('analysis-requests', analysisRequest);

      // Trigger analysis via service bus or direct API call
      try {
        const response = await fetch('/api/document-intelligence/analyze', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            documentId,
            analysisType,
            requestId: analysisRequest.id
          })
        });

        if (response.ok) {
          logger.info('Analysis triggered successfully', {
            documentId,
            analysisType,
            requestId: analysisRequest.id
          });
        } else {
          logger.warn('Failed to trigger analysis via API', {
            documentId,
            analysisType,
            status: response.status
          });
        }
      } catch (serviceError) {
        logger.warn('Analysis service unavailable, request queued for later processing', {
          documentId,
          analysisType,
          requestId: analysisRequest.id,
          error: serviceError
        });
      }

      return {
        message: 'Analysis triggered successfully',
        analysisType,
        requestId: analysisRequest.id
      };
    } catch (error) {
      logger.error('Failed to trigger analysis', { error, documentId, analysisType });
      throw error;
    }
  }

  private async notifyDepartment(department: string, documentId: string, organizationId: string): Promise<void> {
    try {
      // Get department users
      const departmentUsers = await db.queryItems(
        'users',
        'SELECT * FROM c WHERE c.department = @department AND c.organizationId = @organizationId',
        [department, organizationId]
      );

      if (departmentUsers.length === 0) {
        logger.warn('No users found in department for notification', { department, organizationId });
        return;
      }

      // Create notification for each department user
      const notifications = departmentUsers.map((user: any) => ({
        id: uuidv4(),
        type: 'document_routed',
        title: 'New Document Routed to Your Department',
        message: `A document has been automatically routed to the ${department} department`,
        userId: user.id,
        documentId,
        organizationId,
        createdAt: new Date().toISOString(),
        status: 'unread',
        metadata: {
          department,
          routingType: 'automated'
        }
      }));

      // Create all notifications
      await Promise.all(
        notifications.map(notification =>
          db.createItem('notifications', notification)
        )
      );

      logger.info('Department notification sent', {
        department,
        documentId,
        recipientCount: notifications.length
      });
    } catch (error) {
      logger.error('Failed to notify department', { error, department, documentId });
      // Don't throw - this is a non-critical operation
    }
  }

  private async createAutomatedExecution(
    document: any,
    matchingRules: DocumentRoutingRule[],
    user: any
  ): Promise<AutomatedWorkflowExecution | null> {
    if (matchingRules.length === 0) {
      return null;
    }

    const executionId = uuidv4();
    const execution: AutomatedWorkflowExecution = {
      id: executionId,
      documentId: document.id,
      pipelineId: 'auto-routing-pipeline',
      routingRuleId: matchingRules[0].id,
      currentStage: 1,
      status: ExecutionStatus.RUNNING,
      startedAt: new Date().toISOString(),
      results: [],
      errors: [],
      organizationId: document.organizationId,
      triggeredBy: user.id
    };

    await db.createItem('automated-workflow-executions', execution);
    return execution;
  }
}

// Create instance
const documentRouting = new AutomatedDocumentRouting();

// Register HTTP functions
app.http('routing-rule-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'routing/rules',
  handler: (request, context) => documentRouting.createRoutingRule(request, context)
});

app.http('document-routing-process', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{documentId}/route',
  handler: (request, context) => documentRouting.processDocumentRouting(request, context)
});
