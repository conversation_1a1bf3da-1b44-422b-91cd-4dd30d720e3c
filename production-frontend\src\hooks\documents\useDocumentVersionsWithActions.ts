/**
 * Document Versions with Actions Hook
 * React hook for document version management with actions
 */

import { useState, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useDocumentStore } from '@/stores/document-store'
import { useToast } from '@/hooks/use-toast'
import { backendApiClient } from '@/services/backend-api-client'
import type { DocumentVersion } from '@/types/document'

export interface DocumentVersionWithActions extends DocumentVersion {
  actions: {
    restore: () => Promise<void>
    download: () => Promise<void>
    delete: () => Promise<void>
    compare: (otherVersionId: string) => Promise<void>
  }
}

/**
 * Hook to get document versions with action methods
 */
export function useDocumentVersionsWithActions(documentId: string) {
  const [selectedVersions, setSelectedVersions] = useState<string[]>([])
  const [isComparing, setIsComparing] = useState(false)
  const queryClient = useQueryClient()
  const { toast } = useToast()

  // Get document versions
  const versionsQuery = useQuery({
    queryKey: ['document-versions', documentId],
    queryFn: async () => {
      return await backendApiClient.request<DocumentVersion[]>(`/documents/${documentId}/versions`)
    },
    enabled: !!documentId,
  })

  // Restore version mutation
  const restoreVersionMutation = useMutation({
    mutationFn: async (versionId: string) => {
      return await backendApiClient.request(`/documents/${documentId}/versions/${versionId}/restore`, {
        method: 'POST'
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-versions', documentId] })
      queryClient.invalidateQueries({ queryKey: ['document', documentId] })
      toast({
        title: 'Version restored',
        description: 'The document version has been restored successfully.',
      })
    },
    onError: () => {
      toast({
        title: 'Error restoring version',
        description: 'There was a problem restoring the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })

  // Delete version mutation
  const deleteVersionMutation = useMutation({
    mutationFn: async (versionId: string) => {
      await backendApiClient.request(`/documents/${documentId}/versions/${versionId}`, {
        method: 'DELETE'
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-versions', documentId] })
      toast({
        title: 'Version deleted',
        description: 'The document version has been deleted successfully.',
      })
    },
    onError: () => {
      toast({
        title: 'Error deleting version',
        description: 'There was a problem deleting the document version. Please try again.',
        variant: 'destructive',
      })
    },
  })

  // Download version
  const downloadVersion = useCallback(async (versionId: string) => {
    try {
      const response = await backendApiClient.request(`/documents/${documentId}/versions/${versionId}/download`, {
        method: 'GET',
        // responseType: 'blob' // Not supported by our API client
      })
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response]))
      const link = document.createElement('a')
      link.href = url
      link.download = `document-version-${versionId}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      toast({
        title: 'Download started',
        description: 'The document version download has started.',
      })
    } catch (error) {
      toast({
        title: 'Download failed',
        description: 'There was a problem downloading the document version. Please try again.',
        variant: 'destructive',
      })
    }
  }, [documentId, toast])

  // Compare versions
  const compareVersions = useCallback(async (version1Id: string, version2Id: string) => {
    try {
      setIsComparing(true)
      const comparison = await backendApiClient.request(`/documents/${documentId}/versions/compare`, {
        method: 'POST',
        body: JSON.stringify({
          version1Id,
          version2Id
        })
      })
      
      // Handle comparison result (could open in new window, show modal, etc.)
      console.log('Version comparison:', comparison)
      
      toast({
        title: 'Comparison complete',
        description: 'The document versions have been compared successfully.',
      })
    } catch (error) {
      toast({
        title: 'Comparison failed',
        description: 'There was a problem comparing the document versions. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsComparing(false)
    }
  }, [documentId, toast])

  // Create versions with actions
  const versionsWithActions: DocumentVersionWithActions[] = (versionsQuery.data || []).map(version => ({
    ...version,
    actions: {
      restore: () => restoreVersionMutation.mutateAsync(version.id),
      download: () => downloadVersion(version.id),
      delete: () => deleteVersionMutation.mutateAsync(version.id),
      compare: (otherVersionId: string) => compareVersions(version.id, otherVersionId),
    }
  }))

  // Selection handlers
  const toggleVersionSelection = useCallback((versionId: string) => {
    setSelectedVersions(prev => 
      prev.includes(versionId) 
        ? prev.filter(id => id !== versionId)
        : [...prev, versionId]
    )
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedVersions([])
  }, [])

  const selectAll = useCallback(() => {
    setSelectedVersions(versionsWithActions.map(v => v.id))
  }, [versionsWithActions])

  // Bulk actions
  const deleteSelectedVersions = useCallback(async () => {
    try {
      await Promise.all(
        selectedVersions.map(versionId => 
          deleteVersionMutation.mutateAsync(versionId)
        )
      )
      setSelectedVersions([])
    } catch (error) {
      toast({
        title: 'Bulk delete failed',
        description: 'Some versions could not be deleted. Please try again.',
        variant: 'destructive',
      })
    }
  }, [selectedVersions, deleteVersionMutation, toast])

  // Additional state for component compatibility
  const [selectedVersion, setSelectedVersion] = useState<DocumentVersionWithActions | null>(null)
  const [isRestoreDialogOpen, setIsRestoreDialogOpen] = useState(false)

  // Helper functions for component compatibility
  const formatFileSize = useCallback((bytes?: number) => {
    if (!bytes) return '0 B'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }, [])

  const handleViewVersion = useCallback((version: DocumentVersionWithActions) => {
    // Open version in new tab or modal
    if (version.url) {
      window.open(version.url.toString(), '_blank')
    }
  }, [])

  const handleDownloadVersion = useCallback((version: DocumentVersionWithActions) => {
    downloadVersion(version.id)
  }, [downloadVersion])

  const handleRestoreVersion = useCallback((version: DocumentVersionWithActions) => {
    setSelectedVersion(version)
    setIsRestoreDialogOpen(true)
  }, [])

  const confirmRestore = useCallback(async () => {
    if (selectedVersion) {
      await restoreVersionMutation.mutateAsync(selectedVersion.id)
      setIsRestoreDialogOpen(false)
      setSelectedVersion(null)
    }
  }, [selectedVersion, restoreVersionMutation])

  return {
    versions: versionsWithActions,
    isLoading: versionsQuery.isLoading,
    error: versionsQuery.error,
    isRestoring: restoreVersionMutation.isPending,
    isDeleting: deleteVersionMutation.isPending,
    isComparing,

    // Selection state
    selectedVersions,
    selectedVersion,
    isRestoreDialogOpen,
    setIsRestoreDialogOpen,
    toggleVersionSelection,
    clearSelection,
    selectAll,

    // Helper functions
    formatFileSize,
    handleViewVersion,
    handleDownloadVersion,
    handleRestoreVersion,
    confirmRestore,

    // Actions
    restoreVersion: restoreVersionMutation.mutate,
    deleteVersion: deleteVersionMutation.mutate,
    downloadVersion,
    compareVersions,
    deleteSelectedVersions,

    // Async actions
    restoreVersionAsync: restoreVersionMutation.mutateAsync,
    deleteVersionAsync: deleteVersionMutation.mutateAsync,
  }
}
