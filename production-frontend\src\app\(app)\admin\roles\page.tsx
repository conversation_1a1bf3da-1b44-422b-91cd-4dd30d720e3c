"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Plus,
  Search,
  MoreHorizontal,
  Pencil,
  Trash,
  Shield,
  Users,
  Building,
  FolderKanban
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { EmptyState } from "@/components/empty-state";
import { AdminOnly } from "@/components/permission-guard";
import { Role, RoleScope } from "@/types/role";
import { formatDistanceToNow } from "date-fns";
import { useRoles } from "@/hooks/admin/useRoles";



export default function RolesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<RoleScope | "ALL">("ALL");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedRoleId, setSelectedRoleId] = useState<string | null>(null);

  // Use the roles hook to fetch and manage roles
  const {
    roles = [],
    isLoadingRoles: isLoading,
    deleteRole,
    isDeletingRole
  } = useRoles({
    scope: activeTab === "ALL" ? undefined : activeTab
  });

  // Filter roles based on search query
  const filteredRoles = roles.filter((role) => {
    const matchesSearch =
      role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (role.description && role.description.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesSearch;
  });

  // Handle role deletion
  const handleDeleteRole = () => {
    if (selectedRoleId) {
      deleteRole(selectedRoleId);
      setIsDeleteDialogOpen(false);
    }
  };

  // Get icon for role scope
  const getRoleScopeIcon = (scope: RoleScope) => {
    switch (scope) {
      case RoleScope.SYSTEM:
        return <Shield className="h-4 w-4" />;
      case RoleScope.ORGANIZATION:
        return <Building className="h-4 w-4" />;
      case RoleScope.PROJECT:
        return <FolderKanban className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  return (
    <AdminOnly>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Roles & Permissions</h1>
            <p className="text-muted-foreground">
              Manage roles and permissions across the system
            </p>
          </div>
          <Button asChild>
            <Link href="/admin/roles/create">
              <Plus className="mr-2 h-4 w-4" />
              Create Role
            </Link>
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search roles..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Tabs
            defaultValue="ALL"
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as RoleScope | "ALL")}
            className="w-full md:w-auto"
          >
            <TabsList className="grid grid-cols-4 w-full md:w-auto">
              <TabsTrigger value="ALL">All</TabsTrigger>
              <TabsTrigger value={RoleScope.SYSTEM}>System</TabsTrigger>
              <TabsTrigger value={RoleScope.ORGANIZATION}>Organization</TabsTrigger>
              <TabsTrigger value={RoleScope.PROJECT}>Project</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Skeleton key={i} className="h-48 w-full" />
            ))}
          </div>
        ) : filteredRoles.length === 0 ? (
          <EmptyState
            icon={<Shield className="h-10 w-10 text-muted-foreground" />}
            title={searchQuery ? "No roles found" : "No roles"}
            description={
              searchQuery
                ? `No roles match "${searchQuery}"`
                : "You haven't created any roles yet."
            }
            action={
              <Button asChild>
                <Link href="/admin/roles/create">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Role
                </Link>
              </Button>
            }
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRoles.map((role) => (
              <Card key={role.id} className="overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{role.name}</CardTitle>
                    <Badge variant={role.isSystemRole ? "secondary" : "default"}>
                      {role.scope}
                    </Badge>
                  </div>
                  <CardDescription>{role.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex items-center gap-2 text-sm mb-3">
                    {getRoleScopeIcon(role.scope)}
                    <span>
                      {role.isSystemRole ? "System Role" : "Custom Role"}
                      {role.isDefault && " • Default"}
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    <p>
                      {role.permissions.length === 1 && role.permissions[0]?.name === "*"
                        ? "All permissions"
                        : `${role.permissions.length} permissions`}
                    </p>
                    <p>
                      Created {formatDistanceToNow(new Date(role.createdAt))} ago
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button asChild variant="outline">
                    <Link href={`/admin/roles/${role.id}`}>
                      View
                    </Link>
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/roles/${role.id}/edit`}>
                          <Pencil className="mr-2 h-4 w-4" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/roles/${role.id}/assignments`}>
                          <Users className="mr-2 h-4 w-4" />
                          Assignments
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {!role.isSystemRole && (
                        <DropdownMenuItem
                          className="text-destructive focus:text-destructive"
                          onClick={() => {
                            setSelectedRoleId(role.id);
                            setIsDeleteDialogOpen(true);
                          }}
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Role</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this role? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteRole}
                disabled={isDeletingRole}
              >
                {isDeletingRole ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnly>
  );
}
