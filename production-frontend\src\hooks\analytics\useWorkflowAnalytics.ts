/**
 * Workflow Analytics Hook
 * Manages workflow analytics data and operations
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'

export interface WorkflowAnalyticsData {
  id: ID
  workflowId: ID
  workflowName: string
  executionCount: number
  successCount: number
  failureCount: number
  averageExecutionTime: number
  totalExecutionTime: number
  lastExecuted: string
  status: 'active' | 'inactive' | 'error'
  performance: {
    successRate: number
    averageStepTime: number
    bottleneckSteps: string[]
    errorRate: number
  }
  usage: {
    dailyExecutions: Array<{ date: string; count: number }>
    weeklyExecutions: Array<{ week: string; count: number }>
    monthlyExecutions: Array<{ month: string; count: number }>
  }
  errors: Array<{
    stepId: string
    stepName: string
    errorType: string
    errorMessage: string
    count: number
    lastOccurred: string
  }>
}

export interface WorkflowExecutionMetrics {
  totalExecutions: number
  successfulExecutions: number
  failedExecutions: number
  averageExecutionTime: number
  executionTrends: Array<{
    date: string
    executions: number
    successes: number
    failures: number
    averageTime: number
  }>
  stepPerformance: Array<{
    stepId: string
    stepName: string
    averageTime: number
    successRate: number
    errorCount: number
  }>
  resourceUsage: {
    cpuUsage: number
    memoryUsage: number
    networkUsage: number
    storageUsage: number
  }
}

export interface UseWorkflowAnalyticsResult {
  // State
  analytics: WorkflowAnalyticsData[]
  executionMetrics: WorkflowExecutionMetrics | null
  loading: boolean
  error: string | null
  
  // Operations
  loadWorkflowAnalytics: (dateRange?: { start: string; end: string }) => Promise<void>
  loadExecutionMetrics: (workflowId: ID, dateRange?: { start: string; end: string }) => Promise<void>
  getWorkflowPerformance: (workflowId: ID) => Promise<any>
  getWorkflowErrors: (workflowId: ID) => Promise<any>
  
  // Utilities
  getTopPerformingWorkflows: (limit?: number) => WorkflowAnalyticsData[]
  getWorstPerformingWorkflows: (limit?: number) => WorkflowAnalyticsData[]
  getWorkflowsByStatus: (status: string) => WorkflowAnalyticsData[]
  
  // Refresh
  refresh: () => Promise<void>
}

export function useWorkflowAnalytics(): UseWorkflowAnalyticsResult {
  const { toast } = useToast()
  
  const [analytics, setAnalytics] = useState<WorkflowAnalyticsData[]>([])
  const [executionMetrics, setExecutionMetrics] = useState<WorkflowExecutionMetrics | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load workflow analytics
  const loadWorkflowAnalytics = useCallback(async (dateRange?: { start: string; end: string }) => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (dateRange?.start) params.append('start', dateRange.start)
      if (dateRange?.end) params.append('end', dateRange.end)

      const response = await backendApiClient.request(`/analytics/workflows?${params}`)
      setAnalytics(response || [])
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow analytics'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Load execution metrics for specific workflow
  const loadExecutionMetrics = useCallback(async (workflowId: ID, dateRange?: { start: string; end: string }) => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (dateRange?.start) params.append('start', dateRange.start)
      if (dateRange?.end) params.append('end', dateRange.end)

      const response = await backendApiClient.request(`/analytics/workflows/${workflowId}/executions?${params}`)
      setExecutionMetrics(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load execution metrics'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Get workflow performance data
  const getWorkflowPerformance = useCallback(async (workflowId: ID) => {
    try {
      const response = await backendApiClient.request(`/analytics/workflows/${workflowId}/performance`)
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow performance'
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Get workflow errors
  const getWorkflowErrors = useCallback(async (workflowId: ID) => {
    try {
      const response = await backendApiClient.request(`/analytics/workflows/${workflowId}/errors`)
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow errors'
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Get top performing workflows
  const getTopPerformingWorkflows = useCallback((limit: number = 5): WorkflowAnalyticsData[] => {
    return [...analytics]
      .sort((a, b) => b.performance.successRate - a.performance.successRate)
      .slice(0, limit)
  }, [analytics])

  // Get worst performing workflows
  const getWorstPerformingWorkflows = useCallback((limit: number = 5): WorkflowAnalyticsData[] => {
    return [...analytics]
      .sort((a, b) => a.performance.successRate - b.performance.successRate)
      .slice(0, limit)
  }, [analytics])

  // Get workflows by status
  const getWorkflowsByStatus = useCallback((status: string): WorkflowAnalyticsData[] => {
    return analytics.filter(workflow => workflow.status === status)
  }, [analytics])

  // Refresh all data
  const refresh = useCallback(async () => {
    const defaultDateRange = {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    }

    await loadWorkflowAnalytics(defaultDateRange)
  }, [loadWorkflowAnalytics])

  // Load initial data
  useEffect(() => {
    refresh()
  }, [refresh])

  return {
    // State
    analytics,
    executionMetrics,
    loading,
    error,
    
    // Operations
    loadWorkflowAnalytics,
    loadExecutionMetrics,
    getWorkflowPerformance,
    getWorkflowErrors,
    
    // Utilities
    getTopPerformingWorkflows,
    getWorstPerformingWorkflows,
    getWorkflowsByStatus,
    
    // Refresh
    refresh,
  }
}

// Individual workflow analytics hook
export function useWorkflowAnalytic(workflowId: ID) {
  const [analytic, setAnalytic] = useState<WorkflowAnalyticsData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  const loadAnalytic = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request(`/analytics/workflows/${workflowId}`)
      setAnalytic(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load workflow analytic'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [workflowId, toast])

  useEffect(() => {
    if (workflowId) {
      loadAnalytic()
    }
  }, [workflowId, loadAnalytic])

  return {
    analytic,
    loading,
    error,
    refresh: loadAnalytic,
  }
}

// Workflow execution tracking hook
export function useWorkflowExecutionTracking() {
  const { toast } = useToast()

  const trackExecution = useCallback(async (workflowId: ID, executionData: any) => {
    try {
      await backendApiClient.request(`/analytics/workflows/${workflowId}/executions`, {
        method: 'POST',
        body: JSON.stringify(executionData)
      })
    } catch (err: any) {
      console.error('Failed to track workflow execution:', err)
    }
  }, [])

  const trackStepExecution = useCallback(async (workflowId: ID, stepId: ID, stepData: any) => {
    try {
      await backendApiClient.request(`/analytics/workflows/${workflowId}/steps/${stepId}/executions`, {
        method: 'POST',
        body: JSON.stringify(stepData)
      })
    } catch (err: any) {
      console.error('Failed to track step execution:', err)
    }
  }, [])

  const trackError = useCallback(async (workflowId: ID, errorData: any) => {
    try {
      await backendApiClient.request(`/analytics/workflows/${workflowId}/errors`, {
        method: 'POST',
        body: JSON.stringify(errorData)
      })
    } catch (err: any) {
      console.error('Failed to track workflow error:', err)
    }
  }, [])

  return {
    trackExecution,
    trackStepExecution,
    trackError,
  }
}

export default useWorkflowAnalytics
