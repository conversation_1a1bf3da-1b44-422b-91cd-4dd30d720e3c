"use client";

import { useState } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from "@dnd-kit/sortable";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  GitBranch,
  Save,
  CheckCircle2,
  UserCheck,
  FileText,
  AlertCircle
} from "lucide-react";
import { WorkflowStep, WorkflowStepType, WorkflowStepStatus } from "@/types/workflow";
import { SortableWorkflowStep } from "./sortable-workflow-step";
import { cn } from "@/lib/utils";

interface WorkflowDesignerProps {
  initialSteps?: WorkflowStep[];
  onSave?: (steps: WorkflowStep[]) => void;
  className?: string;
}

export function WorkflowDesigner({
  initialSteps = [],
  onSave,
  className
}: WorkflowDesignerProps) {
  const [steps, setSteps] = useState<WorkflowStep[]>(initialSteps);
  const [workflowName, setWorkflowName] = useState<string>("");
  const [workflowDescription, setWorkflowDescription] = useState<string>("");

  // Set up DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Generate a unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // Add a new step
  const addStep = (type: string) => {
    // Create a partial step with required fields
    const partialStep: Partial<WorkflowStep> = {
      id: generateId(),
      name: `New ${type} Step`,
      description: "",
      type: type as WorkflowStepType,
      status: WorkflowStepStatus.PENDING,
      order: steps.length + 1,
      assigneeId: "",
      // workflowId will be set when the workflow is created
      // assigneeType: "user", // Default assignee type - not in WorkflowStep interface
    };

    // Add non-standard properties that might be expected by the UI
    const stepWithExtras = {
      ...partialStep,
      createdAt: new Date().toISOString(),
    };

    // Cast to WorkflowStep since we're providing the minimum required fields
    setSteps([...steps, stepWithExtras as unknown as WorkflowStep]);
  };

  // Remove a step
  const removeStep = (id: string) => {
    const newSteps = steps.filter(step => step.id !== id);
    // Reorder the remaining steps
    const reorderedSteps = newSteps.map((step, index) => ({
      ...step,
      order: index + 1
    }));
    setSteps(reorderedSteps);
  };

  // Update a step
  const updateStep = (updatedStep: WorkflowStep) => {
    const newSteps = steps.map(step =>
      step.id === updatedStep.id ? updatedStep : step
    );
    setSteps(newSteps);
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setSteps((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        const reorderedItems = arrayMove(items, oldIndex, newIndex);

        // Update order property
        return reorderedItems.map((item, index) => ({
          ...item,
          order: index + 1
        }));
      });
    }
  };

  // Save workflow
  const saveWorkflow = () => {
    if (onSave) {
      onSave(steps);
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Workflow Designer
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="workflow-name">Workflow Name</Label>
              <Input
                id="workflow-name"
                placeholder="Enter workflow name"
                value={workflowName}
                onChange={(e) => setWorkflowName(e.target.value)}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="workflow-description">Description</Label>
              <Textarea
                id="workflow-description"
                placeholder="Enter workflow description"
                value={workflowDescription}
                onChange={(e) => setWorkflowDescription(e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-wrap gap-2">
        <Button variant="outline" onClick={() => addStep("APPROVAL")}>
          <UserCheck className="mr-1 h-4 w-4" />
          Add Approval Step
        </Button>
        <Button variant="outline" onClick={() => addStep("TASK")}>
          <CheckCircle2 className="mr-1 h-4 w-4" />
          Add Task Step
        </Button>
        <Button variant="outline" onClick={() => addStep("DOCUMENT")}>
          <FileText className="mr-1 h-4 w-4" />
          Add Document Step
        </Button>
      </div>

      <div className="space-y-3">
        <h3 className="text-lg font-medium">Workflow Steps</h3>

        {steps.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg bg-muted/50">
            <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-muted-foreground text-center">
              No steps added yet. Use the buttons above to add steps to your workflow.
            </p>
          </div>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={steps.map(step => step.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-3">
                {steps.map((step) => (
                  <SortableWorkflowStep
                    key={step.id}
                    step={step}
                    onUpdate={updateStep}
                    onRemove={removeStep}
                  />
                ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </div>

      <div className="flex justify-end">
        <Button onClick={saveWorkflow} disabled={steps.length === 0 || !workflowName}>
          <Save className="mr-1 h-4 w-4" />
          Save Workflow
        </Button>
      </div>
    </div>
  );
}
