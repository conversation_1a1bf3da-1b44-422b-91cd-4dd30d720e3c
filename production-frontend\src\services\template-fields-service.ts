/**
 * Template Fields Service
 * Handles template field management and validation
 */

import { backendApiClient } from './backend-api-client'
import type { ID } from '../types'

export type FieldType =
  | 'text'
  | 'number'
  | 'date'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'textarea'
  | 'file'
  | 'richtext'
  | 'email'
  | 'signature'
  | 'table'

export interface FieldOption {
  label: string
  value: any
  description?: string
  disabled?: boolean
}

export interface FieldValidation {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: string
  customValidator?: string
  errorMessage?: string
}

export interface TemplateField {
  id: ID
  name: string
  label: string
  type: FieldType
  description?: string
  placeholder?: string
  defaultValue?: any
  validation?: FieldValidation
  options?: FieldOption[]
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  style?: {
    fontSize?: number
    fontFamily?: string
    fontWeight?: string
    color?: string
    backgroundColor?: string
    borderColor?: string
    borderWidth?: number
    borderRadius?: number
  }
  conditional?: {
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'not_contains'
    value: any
  }
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface TemplateSection {
  id: ID
  name: string
  title: string
  description?: string
  fields: TemplateField[]
  order: number
  collapsible?: boolean
  collapsed?: boolean
  conditional?: {
    field: string
    operator: 'equals' | 'not_equals' | 'contains' | 'not_contains'
    value: any
  }
  style?: {
    backgroundColor?: string
    borderColor?: string
    padding?: number
    margin?: number
  }
  createdAt: string
  updatedAt: string
}

export interface CreateFieldRequest {
  templateId: ID
  sectionId?: ID
  name: string
  label: string
  type: FieldType
  description?: string
  placeholder?: string
  defaultValue?: any
  validation?: FieldValidation
  options?: FieldOption[]
  position: {
    x: number
    y: number
    width: number
    height: number
  }
  style?: any
  conditional?: any
  metadata?: Record<string, any>
}

export interface UpdateFieldRequest {
  name?: string
  label?: string
  type?: FieldType
  description?: string
  placeholder?: string
  defaultValue?: any
  validation?: FieldValidation
  options?: FieldOption[]
  position?: {
    x: number
    y: number
    width: number
    height: number
  }
  style?: any
  conditional?: any
  metadata?: Record<string, any>
}

export interface CreateSectionRequest {
  templateId: ID
  name: string
  title: string
  description?: string
  order: number
  collapsible?: boolean
  collapsed?: boolean
  conditional?: any
  style?: any
}

export interface UpdateSectionRequest {
  name?: string
  title?: string
  description?: string
  order?: number
  collapsible?: boolean
  collapsed?: boolean
  conditional?: any
  style?: any
}

class TemplateFieldsService {
  /**
   * Get all fields for a template
   */
  async getTemplateFields(templateId: ID): Promise<TemplateField[]> {
    return await backendApiClient.request(`/templates/${templateId}/fields`)
  }

  /**
   * Get all sections for a template
   */
  async getTemplateSections(templateId: ID): Promise<TemplateSection[]> {
    return await backendApiClient.request(`/templates/${templateId}/sections`)
  }

  /**
   * Create a new field
   */
  async createField(request: CreateFieldRequest): Promise<TemplateField> {
    return await backendApiClient.request('/template-fields', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Update a field
   */
  async updateField(fieldId: ID, request: UpdateFieldRequest): Promise<TemplateField> {
    return await backendApiClient.request(`/template-fields/${fieldId}`, {
      method: 'PUT',
      body: JSON.stringify(request)
    })
  }

  /**
   * Delete a field
   */
  async deleteField(fieldId: ID): Promise<void> {
    await backendApiClient.request(`/template-fields/${fieldId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Create a new section
   */
  async createSection(request: CreateSectionRequest): Promise<TemplateSection> {
    return await backendApiClient.request('/template-sections', {
      method: 'POST',
      body: JSON.stringify(request)
    })
  }

  /**
   * Update a section
   */
  async updateSection(sectionId: ID, request: UpdateSectionRequest): Promise<TemplateSection> {
    return await backendApiClient.request(`/template-sections/${sectionId}`, {
      method: 'PUT',
      body: JSON.stringify(request)
    })
  }

  /**
   * Delete a section
   */
  async deleteSection(sectionId: ID): Promise<void> {
    await backendApiClient.request(`/template-sections/${sectionId}`, {
      method: 'DELETE'
    })
  }

  /**
   * Reorder fields within a section
   */
  async reorderFields(sectionId: ID, fieldIds: ID[]): Promise<void> {
    await backendApiClient.request(`/template-sections/${sectionId}/reorder-fields`, {
      method: 'PUT',
      body: JSON.stringify({ fieldIds })
    })
  }

  /**
   * Reorder sections within a template
   */
  async reorderSections(templateId: ID, sectionIds: ID[]): Promise<void> {
    await backendApiClient.request(`/templates/${templateId}/reorder-sections`, {
      method: 'PUT',
      body: JSON.stringify({ sectionIds })
    })
  }

  /**
   * Validate field configuration
   */
  async validateField(field: Partial<TemplateField>): Promise<{
    valid: boolean
    errors: string[]
    warnings: string[]
  }> {
    return await backendApiClient.request('/template-fields/validate', {
      method: 'POST',
      body: JSON.stringify(field)
    })
  }

  /**
   * Get field types and their configurations
   */
  async getFieldTypes(): Promise<{
    type: FieldType
    label: string
    description: string
    defaultValidation: FieldValidation
    supportedOptions: boolean
    supportedStyles: string[]
  }[]> {
    return await backendApiClient.request('/template-fields/types')
  }

  /**
   * Duplicate a field
   */
  async duplicateField(fieldId: ID): Promise<TemplateField> {
    return await backendApiClient.request(`/template-fields/${fieldId}/duplicate`, {
      method: 'POST'
    })
  }

  /**
   * Move field to different section
   */
  async moveField(fieldId: ID, targetSectionId: ID): Promise<TemplateField> {
    return await backendApiClient.request(`/template-fields/${fieldId}/move`, {
      method: 'PUT',
      body: JSON.stringify({ targetSectionId })
    })
  }
}

export const templateFieldsService = new TemplateFieldsService()
export default templateFieldsService
