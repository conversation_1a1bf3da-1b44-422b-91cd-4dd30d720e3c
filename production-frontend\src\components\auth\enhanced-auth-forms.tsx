'use client'

/**
 * Enhanced Authentication Forms
 * Comprehensive Azure AD B2C authentication components with all policies support
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  UserPlus,
  LogIn,
  KeyRound,
  Settings,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react'
import { useEnhancedAuth, AuthMethod } from '@/hooks/useEnhancedAuth'

interface AuthFormProps {
  onSuccess?: () => void
  onError?: (error: string) => void
  defaultMethod?: AuthMethod
}

/**
 * Enhanced Sign In Form
 */
export function EnhancedSignInForm({ onSuccess, onError, defaultMethod = AuthMethod.POPUP }: AuthFormProps) {
  const { signIn, resetPassword, isLoading, error, clearError } = useEnhancedAuth()
  const [authMethod, setAuthMethod] = useState<AuthMethod>(defaultMethod)
  const [showPasswordReset, setShowPasswordReset] = useState(false)

  const handleSignIn = async () => {
    try {
      clearError()
      await signIn(authMethod)
      onSuccess?.()
    } catch (err: any) {
      onError?.(err.message)
    }
  }

  const handlePasswordReset = async () => {
    try {
      clearError()
      await resetPassword(authMethod)
      setShowPasswordReset(false)
    } catch (err: any) {
      onError?.(err.message)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          <LogIn className="h-6 w-6 mx-auto mb-2" />
          Welcome Back
        </CardTitle>
        <CardDescription className="text-center">
          Sign in to your account to continue
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Authentication Method Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Authentication Method</Label>
          <div className="flex gap-2">
            <Button
              variant={authMethod === AuthMethod.POPUP ? "default" : "outline"}
              size="sm"
              onClick={() => setAuthMethod(AuthMethod.POPUP)}
              className="flex-1"
            >
              Popup
            </Button>
            <Button
              variant={authMethod === AuthMethod.REDIRECT ? "default" : "outline"}
              size="sm"
              onClick={() => setAuthMethod(AuthMethod.REDIRECT)}
              className="flex-1"
            >
              Redirect
            </Button>
          </div>
        </div>

        {!showPasswordReset ? (
          <>
            {/* Main Sign In Button */}
            <Button
              onClick={handleSignIn}
              className="w-full"
              disabled={isLoading}
              size="lg"
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
              ) : (
                <Building2 className="h-5 w-5 mr-2" />
              )}
              {isLoading ? "Signing in..." : "Sign in with Microsoft"}
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Need help?
                </span>
              </div>
            </div>

            {/* Password Reset Link */}
            <Button
              variant="link"
              onClick={() => setShowPasswordReset(true)}
              className="w-full text-sm"
              disabled={isLoading}
            >
              <KeyRound className="h-4 w-4 mr-2" />
              Forgot your password?
            </Button>
          </>
        ) : (
          <>
            {/* Password Reset Section */}
            <div className="space-y-4">
              <div className="text-center">
                <KeyRound className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <h3 className="text-lg font-semibold">Reset Password</h3>
                <p className="text-sm text-muted-foreground">
                  Click the button below to start the password reset process
                </p>
              </div>

              <Button
                onClick={handlePasswordReset}
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                ) : (
                  <KeyRound className="h-5 w-5 mr-2" />
                )}
                {isLoading ? "Initiating reset..." : "Reset Password"}
              </Button>

              <Button
                variant="outline"
                onClick={() => setShowPasswordReset(false)}
                className="w-full"
                disabled={isLoading}
              >
                Back to Sign In
              </Button>
            </div>
          </>
        )}

        {/* Method Info */}
        <div className="text-xs text-center text-muted-foreground">
          <Badge variant="outline" className="text-xs">
            {authMethod === AuthMethod.POPUP ? "Popup" : "Redirect"} Method
          </Badge>
          <p className="mt-1">
            {authMethod === AuthMethod.POPUP 
              ? "Opens authentication in a popup window"
              : "Redirects to Microsoft authentication page"
            }
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Enhanced Sign Up Form
 */
export function EnhancedSignUpForm({ onSuccess, onError, defaultMethod = AuthMethod.POPUP }: AuthFormProps) {
  const { signUp, isLoading, error, clearError } = useEnhancedAuth()
  const [authMethod, setAuthMethod] = useState<AuthMethod>(defaultMethod)

  const handleSignUp = async () => {
    try {
      clearError()
      await signUp(authMethod)
      onSuccess?.()
    } catch (err: any) {
      onError?.(err.message)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">
          <UserPlus className="h-6 w-6 mx-auto mb-2" />
          Create Account
        </CardTitle>
        <CardDescription className="text-center">
          Sign up for a new account to get started
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Authentication Method Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Authentication Method</Label>
          <div className="flex gap-2">
            <Button
              variant={authMethod === AuthMethod.POPUP ? "default" : "outline"}
              size="sm"
              onClick={() => setAuthMethod(AuthMethod.POPUP)}
              className="flex-1"
            >
              Popup
            </Button>
            <Button
              variant={authMethod === AuthMethod.REDIRECT ? "default" : "outline"}
              size="sm"
              onClick={() => setAuthMethod(AuthMethod.REDIRECT)}
              className="flex-1"
            >
              Redirect
            </Button>
          </div>
        </div>

        {/* Main Sign Up Button */}
        <Button
          onClick={handleSignUp}
          className="w-full"
          disabled={isLoading}
          size="lg"
        >
          {isLoading ? (
            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
          ) : (
            <Building2 className="h-5 w-5 mr-2" />
          )}
          {isLoading ? "Creating account..." : "Sign up with Microsoft"}
        </Button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Secure Registration
            </span>
          </div>
        </div>

        {/* Terms and Privacy */}
        <div className="text-xs text-center text-muted-foreground">
          By signing up, you agree to our{' '}
          <a href="/terms" className="underline hover:text-primary">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="/privacy" className="underline hover:text-primary">
            Privacy Policy
          </a>
        </div>

        {/* Method Info */}
        <div className="text-xs text-center text-muted-foreground">
          <Badge variant="outline" className="text-xs">
            {authMethod === AuthMethod.POPUP ? "Popup" : "Redirect"} Method
          </Badge>
          <p className="mt-1">
            {authMethod === AuthMethod.POPUP 
              ? "Opens registration in a popup window"
              : "Redirects to Microsoft registration page"
            }
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Profile Edit Form
 */
export function ProfileEditForm({ onSuccess, onError, defaultMethod = AuthMethod.POPUP }: AuthFormProps) {
  const { editProfile, isLoading, error, clearError, user } = useEnhancedAuth()
  const [authMethod, setAuthMethod] = useState<AuthMethod>(defaultMethod)

  const handleEditProfile = async () => {
    try {
      clearError()
      await editProfile(authMethod)
      onSuccess?.()
    } catch (err: any) {
      onError?.(err.message)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-xl font-bold text-center">
          <Settings className="h-5 w-5 mx-auto mb-2" />
          Edit Profile
        </CardTitle>
        <CardDescription className="text-center">
          Update your profile information
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current User Info */}
        {user && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="text-sm">
              <p><strong>Name:</strong> {user.displayName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Not set'}</p>
              <p><strong>Email:</strong> {user.email}</p>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Authentication Method Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Authentication Method</Label>
          <div className="flex gap-2">
            <Button
              variant={authMethod === AuthMethod.POPUP ? "default" : "outline"}
              size="sm"
              onClick={() => setAuthMethod(AuthMethod.POPUP)}
              className="flex-1"
            >
              Popup
            </Button>
            <Button
              variant={authMethod === AuthMethod.REDIRECT ? "default" : "outline"}
              size="sm"
              onClick={() => setAuthMethod(AuthMethod.REDIRECT)}
              className="flex-1"
            >
              Redirect
            </Button>
          </div>
        </div>

        {/* Edit Profile Button */}
        <Button
          onClick={handleEditProfile}
          className="w-full"
          disabled={isLoading}
          size="lg"
        >
          {isLoading ? (
            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
          ) : (
            <Settings className="h-5 w-5 mr-2" />
          )}
          {isLoading ? "Opening editor..." : "Edit Profile"}
        </Button>

        {/* Method Info */}
        <div className="text-xs text-center text-muted-foreground">
          <Badge variant="outline" className="text-xs">
            {authMethod === AuthMethod.POPUP ? "Popup" : "Redirect"} Method
          </Badge>
          <p className="mt-1">
            {authMethod === AuthMethod.POPUP 
              ? "Opens profile editor in a popup window"
              : "Redirects to Microsoft profile editor"
            }
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Authentication Status Display
 */
export function AuthStatusDisplay() {
  const { isAuthenticated, user, isLoading } = useEnhancedAuth()

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-lg">Authentication Status</CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center gap-2">
          {isAuthenticated ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <AlertCircle className="h-5 w-5 text-red-500" />
          )}
          <span className="font-medium">
            {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
          </span>
        </div>

        {isLoading && (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">Processing...</span>
          </div>
        )}

        {user && (
          <div className="space-y-2">
            <div className="text-sm">
              <p><strong>Name:</strong> {user.displayName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Not set'}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>User ID:</strong> {user.id}</p>
            </div>
            
            {user.roles && user.roles.length > 0 && (
              <div>
                <p className="text-sm font-medium">Roles:</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {user.roles.map((role) => (
                    <Badge key={role} variant="secondary" className="text-xs">
                      {role}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          <p><strong>Status:</strong> {isAuthenticated ? 'Active Session' : 'No Session'}</p>
        </div>
      </CardContent>
    </Card>
  )
}
