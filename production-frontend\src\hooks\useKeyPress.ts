import { useState, useEffect } from 'react'

/**
 * Key Press Hook
 * Detects when specific keys are pressed
 */

export function useKeyPress(
  targetKey: string | string[],
  options: {
    event?: 'keydown' | 'keyup'
    target?: HTMLElement | Document | Window
    enabled?: boolean
  } = {}
): boolean {
  const { event = 'keydown', target = document, enabled = true } = options
  const [keyPressed, setKeyPressed] = useState(false)

  useEffect(() => {
    if (!enabled) return

    const downHandler = (event: Event) => {
      const keyEvent = event as KeyboardEvent
      const keys = Array.isArray(targetKey) ? targetKey : [targetKey]
      if (keys.includes(keyEvent.key)) {
        setKeyPressed(true)
      }
    }

    const upHandler = (event: Event) => {
      const keyEvent = event as KeyboardEvent
      const keys = Array.isArray(targetKey) ? targetKey : [targetKey]
      if (keys.includes(keyEvent.key)) {
        setKeyPressed(false)
      }
    }

    if (event === 'keydown') {
      target.addEventListener('keydown', downHandler)
      target.addEventListener('keyup', upHandler)
    } else {
      target.addEventListener('keyup', downHandler)
    }

    return () => {
      if (event === 'keydown') {
        target.removeEventListener('keydown', downHandler)
        target.removeEventListener('keyup', upHandler)
      } else {
        target.removeEventListener('keyup', downHandler)
      }
    }
  }, [targetKey, event, target, enabled])

  return keyPressed
}

/**
 * Multiple key press hook
 */
export function useKeyCombo(
  keys: string[],
  callback: () => void,
  options: {
    target?: HTMLElement | Document | Window
    enabled?: boolean
  } = {}
): void {
  const { target = document, enabled = true } = options

  useEffect(() => {
    if (!enabled) return

    const pressedKeys = new Set<string>()

    const downHandler = (event: Event) => {
      const keyEvent = event as KeyboardEvent
      pressedKeys.add(keyEvent.key)

      // Check if all keys in combo are pressed
      if (keys.every(key => pressedKeys.has(key))) {
        callback()
      }
    }

    const upHandler = (event: Event) => {
      const keyEvent = event as KeyboardEvent
      pressedKeys.delete(keyEvent.key)
    }

    target.addEventListener('keydown', downHandler)
    target.addEventListener('keyup', upHandler)

    return () => {
      target.removeEventListener('keydown', downHandler)
      target.removeEventListener('keyup', upHandler)
    }
  }, [keys, callback, target, enabled])
}
