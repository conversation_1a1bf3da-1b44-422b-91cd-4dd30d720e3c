'use client';

import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useTemplates } from '@/hooks';
import { TemplateCard } from '@/components/templates/template-card';
import { EmptyState } from '@/components/empty-state';
import { FileStack, Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useState } from 'react';

interface ProjectTemplatesProps {
  projectId: string;
}

export function ProjectTemplates({ projectId }: ProjectTemplatesProps) {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');

  const {
    data: templates = [],
    isLoading,
    error
  } = useTemplates({ projectId });

  // Filter templates by search query
  const templateArray = Array.isArray(templates) ? templates : [];
  const filteredTemplates = searchQuery
    ? templateArray.filter((template: any) =>
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags?.some((tag: string) => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : templateArray;

  if (error) {
    return (
      <EmptyState
        icon={<FileStack className="h-10 w-10 text-muted-foreground" />}
        title="Error loading templates"
        description="There was a problem loading the templates for this project."
        action={
          <Button onClick={() => router.refresh()}>
            Try Again
          </Button>
        }
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search templates..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          onClick={() => router.push(`/projects/${projectId}/templates/create`)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Create
        </Button>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="h-40 bg-muted rounded-md animate-pulse" />
          ))}
        </div>
      ) : filteredTemplates.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredTemplates.map((template: any) => (
            <div
              key={template.id}
              onClick={() => router.push(`/projects/${projectId}/templates/${template.id}`)}
              className="cursor-pointer"
            >
              <TemplateCard template={template} />
            </div>
          ))}
        </div>
      ) : (
        <EmptyState
          icon={<FileStack className="h-10 w-10 text-muted-foreground" />}
          title={searchQuery ? "No matching templates" : "No templates yet"}
          description={
            searchQuery
              ? "Try a different search term or clear the search"
              : "Create your first template to get started"
          }
          action={
            <Button
              onClick={() => router.push(`/projects/${projectId}/templates/create`)}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Template
            </Button>
          }
        />
      )}
    </div>
  );
}
