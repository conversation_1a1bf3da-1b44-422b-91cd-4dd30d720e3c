"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useCreateWorkflow } from "@/hooks/workflows/useWorkflows";
import { useProjects } from "@/hooks/projects";
import { useOrganizations } from "@/hooks/organizations";
import { WorkflowDesigner } from "@/components/workflows";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, GitBranch } from "lucide-react";
import { WorkflowStep } from "@/types/workflow";

export default function CreateWorkflowPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams?.get("projectId") || null;
  const organizationId = searchParams?.get("organizationId") || null;

  const [selectedProjectId, setSelectedProjectId] = useState<string>(projectId || "");
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string>(organizationId || "");
  const [workflowName] = useState<string>("");
  const [workflowDescription] = useState<string>("");

  // Fetch organizations
  const {
    organizations: organizationsData,
    isLoading: isLoadingOrgs
  } = useOrganizations();

  // Fetch projects
  const {
    projects: projectsData,
    isLoading: isLoadingProjects
  } = useProjects({
    organizationId: selectedOrganizationId || undefined
  });

  // Create workflow mutation
  const { mutate: createWorkflow } = useCreateWorkflow();

  // Extract data from query results
  const organizations = organizationsData || [];
  const projects = projectsData || [];

  // Handle organization change
  const handleOrganizationChange = (value: string) => {
    setSelectedOrganizationId(value);
    setSelectedProjectId(""); // Reset project when organization changes
  };

  // Handle project change
  const handleProjectChange = (value: string) => {
    setSelectedProjectId(value);
  };

  // Handle workflow save
  const handleSaveWorkflow = (steps: WorkflowStep[]) => {
    if (!selectedProjectId || !selectedOrganizationId) {
      return;
    }

    createWorkflow({
      name: workflowName || "New Workflow",
      description: workflowDescription,
      projectId: selectedProjectId,
      organizationId: selectedOrganizationId,
      documentId: '', // Add required documentId field
      steps: steps.map(step => ({
        name: step.name,
        description: step.description,
        // Convert the type to string to avoid type mismatch
        type: step.type as unknown as string,
        order: step.order,
        assigneeId: step.assigneeId,
        dueDate: step.dueDate,
        isRequired: true
      }) as any)
    }, {
      onSuccess: (data: any) => {
        router.push(`/workflows/${data.id}`);
      }
    });
  };

  // Handle go back
  const handleGoBack = () => {
    router.back();
  };

  return (
    <div className="container py-6 space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <Button variant="ghost" size="sm" onClick={handleGoBack} className="mb-2">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">Create Workflow</h1>
          <p className="text-muted-foreground">
            Design a new workflow for document approvals and reviews
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Workflow Settings
          </CardTitle>
          <CardDescription>
            Select the organization and project for this workflow
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Organization</label>
              {isLoadingOrgs ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select value={selectedOrganizationId} onValueChange={handleOrganizationChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select organization" />
                  </SelectTrigger>
                  <SelectContent>
                    {organizations.map((org: any) => (
                      <SelectItem key={org.id} value={org.id}>
                        {org.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Project</label>
              {isLoadingProjects || !selectedOrganizationId ? (
                <Skeleton className="h-10 w-full" />
              ) : (
                <Select value={selectedProjectId} onValueChange={handleProjectChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map((project: any) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {selectedProjectId && selectedOrganizationId && (
        <WorkflowDesigner
          onSave={handleSaveWorkflow}
        />
      )}
    </div>
  );
}
