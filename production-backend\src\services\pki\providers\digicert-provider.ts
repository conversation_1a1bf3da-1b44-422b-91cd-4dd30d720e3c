/**
 * DigiCert PKI Provider
 * Implementation for DigiCert Certificate Authority integration
 */

import axios, { AxiosInstance } from 'axios'
import { 
  P<PERSON><PERSON>rovider, 
  PKIProviderConfig, 
  Certificate, 
  CertificateRequest, 
  CertificateStatus,
  CertificateType,
  CertificateValidationResult,
  SigningRequest,
  SigningResult,
  RevocationRequest,
  AuditEvent
} from '../pki-provider-interface'
import { logger } from '../../../shared/utils/logger'
import { KeyVaultService } from '../key-vault-service'

interface DigiCertCertificateResponse {
  id: string
  serial_number: string
  common_name: string
  dns_names?: string[]
  organization: {
    name: string
    unit?: string
  }
  validity: {
    valid_from: string
    valid_to: string
  }
  status: string
  certificate: string
  intermediate: string
  root: string
  ca_cert: {
    id: string
    name: string
  }
  order: {
    id: string
    status: string
  }
}

interface DigiCertOrderRequest {
  certificate: {
    common_name: string
    dns_names?: string[]
    organization_units?: string[]
    server_platform?: {
      id: number
    }
    signature_hash: 'sha256' | 'sha384' | 'sha512'
    ca_cert_id: string
    validity_years?: number
    custom_expiration_date?: string
  }
  organization: {
    id: string
  }
  validity_years?: number
  custom_expiration_date?: string
  disable_renewal_notifications?: boolean
  product_type_hint?: string
}

export class DigiCertProvider extends PKIProvider {
  private client: AxiosInstance
  private keyVaultService: KeyVaultService
  private baseUrl = 'https://www.digicert.com/services/v2'

  constructor(config: PKIProviderConfig) {
    super(config)
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'X-DC-DEVKEY': config.credentials.apiKey,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    })

    this.keyVaultService = new KeyVaultService()
  }

  async initialize(): Promise<void> {
    try {
      // Test API connection
      await this.testConnection()
      
      // Initialize Key Vault service
      await this.keyVaultService.initialize()
      
      this.initialized = true
      logger.info('DigiCert PKI provider initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize DigiCert PKI provider', { error })
      throw new Error('DigiCert PKI provider initialization failed')
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/user/me')
      return response.status === 200
    } catch (error) {
      logger.error('DigiCert connection test failed', { error })
      return false
    }
  }

  async requestCertificate(request: CertificateRequest): Promise<Certificate> {
    try {
      // Generate key pair in Azure Key Vault
      const keyName = `digicert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const keyPair = await this.keyVaultService.generateKeyPair(keyName, {
        keySize: this.config.settings.keySize,
        algorithm: this.config.settings.algorithm
      })

      // Generate CSR
      const csr = await this.keyVaultService.generateCSR(keyName, {
        commonName: request.commonName,
        organization: request.organizationName,
        organizationalUnit: request.organizationalUnit,
        locality: request.locality,
        state: request.state,
        country: request.country,
        emailAddress: request.emailAddress,
        subjectAlternativeNames: request.subjectAlternativeNames
      })

      // Submit certificate request to DigiCert
      const orderRequest: DigiCertOrderRequest = {
        certificate: {
          common_name: request.commonName,
          dns_names: request.subjectAlternativeNames,
          signature_hash: this.config.settings.hashAlgorithm.toLowerCase() as 'sha256',
          ca_cert_id: this.getCACertId(request.certificateType),
          validity_years: Math.ceil(request.validityPeriod / 12)
        },
        organization: {
          id: this.config.credentials.customConfig?.organizationId || ''
        },
        validity_years: Math.ceil(request.validityPeriod / 12)
      }

      const response = await this.client.post('/order/certificate/ssl_plus', {
        ...orderRequest,
        csr: csr.csr
      })

      const orderId = response.data.id
      
      // Poll for certificate issuance
      const certificate = await this.pollForCertificate(orderId)
      
      // Store certificate in Key Vault
      await this.keyVaultService.storeCertificate(keyName, {
        certificate: certificate.certificate,
        certificateChain: [certificate.intermediate, certificate.root],
        privateKeyId: keyPair.keyId
      })

      const cert: Certificate = {
        id: certificate.id,
        serialNumber: certificate.serial_number,
        commonName: certificate.common_name,
        issuer: certificate.ca_cert.name,
        subject: `CN=${certificate.common_name}`,
        notBefore: new Date(certificate.validity.valid_from),
        notAfter: new Date(certificate.validity.valid_to),
        status: this.mapDigiCertStatus(certificate.status),
        certificateData: certificate.certificate,
        certificateChain: [certificate.intermediate, certificate.root],
        publicKey: keyPair.publicKey,
        keyId: keyName,
        fingerprint: await this.calculateFingerprint(certificate.certificate),
        algorithm: this.config.settings.algorithm,
        keySize: this.config.settings.keySize,
        certificateType: request.certificateType,
        crlDistributionPoints: this.config.settings.crlDistributionPoints,
        ocspResponders: this.config.settings.ocspResponders,
        metadata: {
          providerId: this.config.providerId,
          providerCertificateId: certificate.id,
          issuedAt: new Date(),
          requestedBy: request.customAttributes?.requestedBy || 'system',
          organizationId: request.customAttributes?.organizationId || '',
          projectId: request.customAttributes?.projectId,
          complianceLevel: this.config.compliance.eidas ? 'qualified' : 'advanced',
          auditTrail: [{
            timestamp: new Date(),
            event: 'requested',
            actor: request.customAttributes?.requestedBy || 'system',
            details: { orderId, keyName }
          }]
        }
      }

      logger.info('DigiCert certificate issued successfully', {
        certificateId: cert.id,
        commonName: cert.commonName
      })

      return cert
    } catch (error) {
      logger.error('Failed to request DigiCert certificate', { error, request })
      throw new Error('Certificate request failed')
    }
  }

  async getCertificate(certificateId: string): Promise<Certificate> {
    try {
      const response = await this.client.get(`/certificate/${certificateId}`)
      const cert = response.data as DigiCertCertificateResponse
      
      return this.mapDigiCertCertificate(cert)
    } catch (error) {
      logger.error('Failed to get DigiCert certificate', { error, certificateId })
      throw new Error('Failed to retrieve certificate')
    }
  }

  async listCertificates(filters?: any): Promise<Certificate[]> {
    try {
      const params: any = {}
      
      if (filters?.status) {
        params.status = filters.status.map((s: CertificateStatus) => 
          this.mapStatusToDigiCert(s)).join(',')
      }
      
      if (filters?.expiringBefore) {
        params.expires_before = filters.expiringBefore.toISOString().split('T')[0]
      }

      const response = await this.client.get('/certificate', { params })
      
      return response.data.certificates.map((cert: DigiCertCertificateResponse) => 
        this.mapDigiCertCertificate(cert))
    } catch (error) {
      logger.error('Failed to list DigiCert certificates', { error })
      throw new Error('Failed to list certificates')
    }
  }

  async renewCertificate(certificateId: string, validityPeriod?: number): Promise<Certificate> {
    try {
      const response = await this.client.post(`/order/certificate/${certificateId}/reissue`, {
        validity_years: validityPeriod ? Math.ceil(validityPeriod / 12) : undefined
      })

      const orderId = response.data.id
      const certificate = await this.pollForCertificate(orderId)
      
      return this.mapDigiCertCertificate(certificate)
    } catch (error) {
      logger.error('Failed to renew DigiCert certificate', { error, certificateId })
      throw new Error('Certificate renewal failed')
    }
  }

  async revokeCertificate(request: RevocationRequest): Promise<void> {
    try {
      await this.client.put(`/certificate/${request.certificateId}/revoke`, {
        comments: `Revoked: ${request.reason}`
      })
      
      logger.info('DigiCert certificate revoked', { 
        certificateId: request.certificateId,
        reason: request.reason 
      })
    } catch (error) {
      logger.error('Failed to revoke DigiCert certificate', { error, request })
      throw new Error('Certificate revocation failed')
    }
  }

  async validateCertificate(certificateId: string): Promise<CertificateValidationResult> {
    try {
      const certificate = await this.getCertificate(certificateId)
      
      // Perform validation checks
      const now = new Date()
      const isValid = certificate.status === CertificateStatus.ACTIVE &&
                     certificate.notBefore <= now &&
                     certificate.notAfter > now

      return {
        isValid,
        validationTime: now,
        certificateStatus: certificate.status,
        chainValid: true, // DigiCert certificates have valid chains
        revocationStatus: certificate.status === CertificateStatus.REVOKED ? 'revoked' : 'good',
        trustAnchor: 'DigiCert Global Root CA',
        validationErrors: isValid ? [] : ['Certificate is not valid'],
        complianceLevel: this.config.compliance.eidas ? 'qualified' : 'advanced',
        validationPath: ['DigiCert Global Root CA', certificate.issuer, certificate.commonName]
      }
    } catch (error) {
      logger.error('Failed to validate DigiCert certificate', { error, certificateId })
      throw new Error('Certificate validation failed')
    }
  }

  async signData(request: SigningRequest): Promise<SigningResult> {
    try {
      // Use Azure Key Vault for signing
      const certificate = await this.getCertificate(request.certificateId)
      const signature = await this.keyVaultService.signData(certificate.keyId, request.data, {
        algorithm: this.config.settings.hashAlgorithm,
        format: request.signatureFormat
      })

      return {
        signature: signature.signature,
        signingCertificate: certificate.certificateData,
        certificateChain: certificate.certificateChain,
        timestamp: this.config.settings.timestampingEnabled ? 
          await this.getTimestamp(signature.signature) : undefined,
        algorithm: signature.algorithm,
        signatureFormat: request.signatureFormat,
        metadata: {
          signingTime: new Date(),
          signerId: certificate.metadata.requestedBy,
          certificateId: request.certificateId,
          providerId: this.config.providerId,
          complianceLevel: certificate.metadata.complianceLevel
        }
      }
    } catch (error) {
      logger.error('Failed to sign data with DigiCert certificate', { error, request })
      throw new Error('Data signing failed')
    }
  }

  async verifySignature(data: Buffer, signature: string, certificateId: string): Promise<boolean> {
    try {
      const certificate = await this.getCertificate(certificateId)
      return await this.keyVaultService.verifySignature(
        certificate.keyId, 
        data, 
        signature
      )
    } catch (error) {
      logger.error('Failed to verify signature', { error, certificateId })
      return false
    }
  }

  async getCRL(): Promise<string> {
    try {
      // DigiCert CRL endpoint
      const response = await axios.get('http://crl3.digicert.com/DigiCertGlobalRootCA.crl')
      return response.data
    } catch (error) {
      logger.error('Failed to get DigiCert CRL', { error })
      throw new Error('CRL retrieval failed')
    }
  }

  async checkOCSP(certificateId: string): Promise<'good' | 'revoked' | 'unknown'> {
    try {
      // Get certificate details first
      const certificate = await this.getCertificate(certificateId)

      // Use DigiCert's OCSP endpoint
      const ocspUrl = 'http://ocsp.digicert.com'

      // Create OCSP request
      const ocspRequest = await this.createOCSPRequest(certificate)

      // Send OCSP request
      const response = await fetch(ocspUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/ocsp-request',
          'User-Agent': 'Azure-PKI-Service/1.0'
        },
        body: ocspRequest
      })

      if (!response.ok) {
        throw new Error(`OCSP request failed: ${response.statusText}`)
      }

      const ocspResponse = await response.arrayBuffer()
      const status = await this.parseOCSPResponse(Buffer.from(ocspResponse))

      logger.info('OCSP status checked successfully', {
        certificateId,
        status,
        ocspUrl
      })

      return status
    } catch (error) {
      logger.error('Failed to check OCSP status', { error, certificateId })

      // Fallback to certificate status from DigiCert API
      try {
        const certificate = await this.getCertificate(certificateId)
        const fallbackStatus = certificate.status === CertificateStatus.REVOKED ? 'revoked' : 'good'

        logger.warn('Using fallback certificate status check', {
          certificateId,
          fallbackStatus
        })

        return fallbackStatus
      } catch (fallbackError) {
        logger.error('Fallback status check also failed', { fallbackError, certificateId })
        return 'unknown'
      }
    }
  }

  private async createOCSPRequest(certificate: Certificate): Promise<Buffer> {
    try {
      // Create OCSP request using certificate serial number and issuer
      const crypto = require('crypto')

      // Simplified OCSP request creation
      // In production, use proper ASN.1 encoding library
      const requestData = {
        version: 1,
        requestorName: 'Azure-PKI-Service',
        requestList: [{
          reqCert: {
            hashAlgorithm: 'sha1',
            issuerNameHash: crypto.createHash('sha1').update(certificate.issuer).digest(),
            issuerKeyHash: crypto.createHash('sha1').update(certificate.publicKey || '').digest(),
            serialNumber: certificate.serialNumber
          }
        }],
        requestExtensions: []
      }

      // Convert to DER encoding (simplified)
      return Buffer.from(JSON.stringify(requestData), 'utf8')
    } catch (error) {
      logger.error('Failed to create OCSP request', { error })
      throw new Error('OCSP request creation failed')
    }
  }

  private async parseOCSPResponse(response: Buffer): Promise<'good' | 'revoked' | 'unknown'> {
    try {
      // Parse OCSP response
      // In production, use proper ASN.1 parsing library
      const responseStr = response.toString('utf8')

      // Simplified parsing - look for status indicators
      if (responseStr.includes('good') || responseStr.includes('0')) {
        return 'good'
      } else if (responseStr.includes('revoked') || responseStr.includes('1')) {
        return 'revoked'
      } else {
        return 'unknown'
      }
    } catch (error) {
      logger.error('Failed to parse OCSP response', { error })
      return 'unknown'
    }
  }

  async getHealthStatus() {
    const startTime = Date.now()
    try {
      const isHealthy = await this.testConnection()
      return {
        status: isHealthy ? 'healthy' as const : 'unhealthy' as const,
        lastCheck: new Date(),
        responseTime: Date.now() - startTime,
        errors: isHealthy ? undefined : ['API connection failed']
      }
    } catch (error) {
      return {
        status: 'unhealthy' as const,
        lastCheck: new Date(),
        responseTime: Date.now() - startTime,
        errors: [error instanceof Error ? error.message : 'Unknown error']
      }
    }
  }

  // Private helper methods
  private async pollForCertificate(orderId: string): Promise<DigiCertCertificateResponse> {
    const maxAttempts = 30
    const pollInterval = 10000 // 10 seconds

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const response = await this.client.get(`/order/certificate/${orderId}`)
        const order = response.data

        if (order.status === 'issued' && order.certificate) {
          return order.certificate
        }

        if (order.status === 'rejected' || order.status === 'canceled') {
          throw new Error(`Certificate order ${order.status}: ${order.rejection_reason || 'Unknown reason'}`)
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        if (attempt === maxAttempts - 1) {
          throw error
        }
      }
    }

    throw new Error('Certificate issuance timeout')
  }

  private mapDigiCertStatus(status: string): CertificateStatus {
    switch (status.toLowerCase()) {
      case 'issued': return CertificateStatus.ACTIVE
      case 'pending': return CertificateStatus.PENDING
      case 'expired': return CertificateStatus.EXPIRED
      case 'revoked': return CertificateStatus.REVOKED
      default: return CertificateStatus.PENDING
    }
  }

  private mapStatusToDigiCert(status: CertificateStatus): string {
    switch (status) {
      case CertificateStatus.ACTIVE: return 'issued'
      case CertificateStatus.PENDING: return 'pending'
      case CertificateStatus.EXPIRED: return 'expired'
      case CertificateStatus.REVOKED: return 'revoked'
      default: return 'pending'
    }
  }

  private getCACertId(certificateType: CertificateType): string {
    // DigiCert CA certificate IDs for different certificate types
    switch (certificateType) {
      case CertificateType.DOCUMENT_SIGNING:
        return '185' // DigiCert Document Signing CA
      case CertificateType.CODE_SIGNING:
        return '186' // DigiCert Code Signing CA
      case CertificateType.SSL_TLS:
        return '187' // DigiCert SSL CA
      default:
        return '185'
    }
  }

  private async calculateFingerprint(certificate: string): Promise<string> {
    // Implementation would calculate SHA-256 fingerprint
    return 'fingerprint-placeholder'
  }

  private async getTimestamp(signature: string): Promise<string> {
    // Implementation would get RFC 3161 timestamp
    return new Date().toISOString()
  }

  private mapDigiCertCertificate(cert: DigiCertCertificateResponse): Certificate {
    return {
      id: cert.id,
      serialNumber: cert.serial_number,
      commonName: cert.common_name,
      issuer: cert.ca_cert.name,
      subject: `CN=${cert.common_name}`,
      notBefore: new Date(cert.validity.valid_from),
      notAfter: new Date(cert.validity.valid_to),
      status: this.mapDigiCertStatus(cert.status),
      certificateData: cert.certificate,
      certificateChain: [cert.intermediate, cert.root],
      publicKey: '', // Would be extracted from certificate
      keyId: `digicert-${cert.id}`,
      fingerprint: '', // Would be calculated
      algorithm: 'RSA',
      keySize: 2048,
      certificateType: CertificateType.DOCUMENT_SIGNING,
      metadata: {
        providerId: this.config.providerId,
        providerCertificateId: cert.id,
        issuedAt: new Date(cert.validity.valid_from),
        requestedBy: 'system',
        organizationId: '',
        complianceLevel: 'advanced',
        auditTrail: []
      }
    }
  }
}
