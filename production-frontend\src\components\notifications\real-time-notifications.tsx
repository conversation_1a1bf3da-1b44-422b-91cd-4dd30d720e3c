"use client";

import { useState, useEffect } from "react";
import { useNotifications } from "@/hooks/infrastructure";
import { useToast } from "@/components/ui/use-toast";
import { Bell, Info, AlertTriangle, AlertCircle, CheckCircle } from "lucide-react";

interface Notification {
  id: string;
  type: "info" | "success" | "warning" | "error";
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  link?: string;
}

export function RealTimeNotifications() {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Handle new notifications
  const handleNotification = (notification: Notification) => {
    // Add the notification to the state
    setNotifications((prev) => [notification, ...prev]);

    // Show a toast notification
    toast({
      title: notification.title,
      description: notification.message,
      variant: notification.type === "error" ? "destructive" : "default",
    });
  };

  // Subscribe to notifications
  const { notifications: realtimeNotifications } = useNotifications();

  // Handle new notifications
  useEffect(() => {
    if (realtimeNotifications.length > 0) {
      const latestNotification = realtimeNotifications[realtimeNotifications.length - 1];
      if (!latestNotification.read) {
        handleNotification(latestNotification);
      }
    }
  }, [realtimeNotifications]);

  // Get the icon for the notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "info":
        return <Info className="h-4 w-4 text-blue-500" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  // Format the timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div className="space-y-2">
      {notifications.length === 0 ? (
        <div className="text-center py-4 text-muted-foreground">
          No new notifications
        </div>
      ) : (
        notifications.map((notification) => (
          <div
            key={notification.id}
            className={`flex items-start gap-3 p-3 rounded-md ${
              notification.read ? "bg-background" : "bg-muted"
            }`}
          >
            <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>
            <div className="flex-1 space-y-1">
              <div className="flex justify-between items-start">
                <h4 className="text-sm font-medium">{notification.title}</h4>
                <span className="text-xs text-muted-foreground">
                  {formatTimestamp(notification.timestamp)}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">{notification.message}</p>
              {notification.link && (
                <a
                  href={notification.link}
                  className="text-xs text-primary hover:underline"
                >
                  View details
                </a>
              )}
            </div>
          </div>
        ))
      )}
    </div>
  );
}
