import { useEffect, useRef, useState, useCallback } from 'react'

/**
 * Intersection Observer Hook
 * Provides intersection observer functionality with multiple targets support
 */

export interface UseIntersectionObserverOptions {
  threshold?: number | number[]
  rootMargin?: string
  root?: Element | null
  triggerOnce?: boolean
  enabled?: boolean
}

export interface UseIntersectionObserverResult {
  ref: React.RefObject<HTMLElement>
  isIntersecting: boolean
  entry: IntersectionObserverEntry | null
}

export function useIntersectionObserver(
  options: UseIntersectionObserverOptions = {}
): UseIntersectionObserverResult {
  const {
    threshold = 0,
    rootMargin = '0px',
    root = null,
    triggerOnce = false,
    enabled = true
  } = options

  const [isIntersecting, setIsIntersecting] = useState(false)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!enabled || !ref.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setEntry(entry)
        setIsIntersecting(entry.isIntersecting)
        
        if (triggerOnce && entry.isIntersecting) {
          observer.disconnect()
        }
      },
      {
        threshold,
        rootMargin,
        root,
      }
    )

    observer.observe(ref.current)

    return () => {
      observer.disconnect()
    }
  }, [threshold, rootMargin, root, triggerOnce, enabled])

  return {
    ref,
    isIntersecting,
    entry,
  }
}

/**
 * Multiple elements intersection observer hook
 */
export interface UseMultipleIntersectionObserverOptions extends UseIntersectionObserverOptions {
  onIntersect?: (entries: IntersectionObserverEntry[]) => void
}

export function useMultipleIntersectionObserver(
  options: UseMultipleIntersectionObserverOptions = {}
) {
  const {
    threshold = 0,
    rootMargin = '0px',
    root = null,
    onIntersect,
    enabled = true
  } = options

  const [entries, setEntries] = useState<IntersectionObserverEntry[]>([])
  const observerRef = useRef<IntersectionObserver | null>(null)
  const elementsRef = useRef<Set<Element>>(new Set())

  useEffect(() => {
    if (!enabled) return

    observerRef.current = new IntersectionObserver(
      (entries) => {
        setEntries(entries)
        onIntersect?.(entries)
      },
      {
        threshold,
        rootMargin,
        root,
      }
    )

    // Observe existing elements
    elementsRef.current.forEach(element => {
      observerRef.current?.observe(element)
    })

    return () => {
      observerRef.current?.disconnect()
    }
  }, [threshold, rootMargin, root, onIntersect, enabled])

  const observe = useCallback((element: Element) => {
    if (!element || !observerRef.current) return

    elementsRef.current.add(element)
    observerRef.current.observe(element)
  }, [])

  const unobserve = useCallback((element: Element) => {
    if (!element || !observerRef.current) return

    elementsRef.current.delete(element)
    observerRef.current.unobserve(element)
  }, [])

  const disconnect = useCallback(() => {
    observerRef.current?.disconnect()
    elementsRef.current.clear()
    setEntries([])
  }, [])

  return {
    entries,
    observe,
    unobserve,
    disconnect,
  }
}

/**
 * Visibility tracking hook
 */
export interface UseVisibilityTrackingOptions extends UseIntersectionObserverOptions {
  onVisible?: () => void
  onHidden?: () => void
}

export function useVisibilityTracking(
  options: UseVisibilityTrackingOptions = {}
) {
  const { onVisible, onHidden, ...observerOptions } = options
  const [isVisible, setIsVisible] = useState(false)
  const [visibilityHistory, setVisibilityHistory] = useState<{
    timestamp: number
    visible: boolean
  }[]>([])

  const { ref, isIntersecting, entry } = useIntersectionObserver(observerOptions)

  useEffect(() => {
    if (isIntersecting !== isVisible) {
      setIsVisible(isIntersecting)
      
      setVisibilityHistory(prev => [
        ...prev,
        { timestamp: Date.now(), visible: isIntersecting }
      ].slice(-10)) // Keep last 10 visibility changes

      if (isIntersecting) {
        onVisible?.()
      } else {
        onHidden?.()
      }
    }
  }, [isIntersecting, isVisible, onVisible, onHidden])

  const getTotalVisibleTime = useCallback(() => {
    let totalTime = 0
    let lastVisibleStart = 0

    visibilityHistory.forEach(({ timestamp, visible }) => {
      if (visible) {
        lastVisibleStart = timestamp
      } else if (lastVisibleStart > 0) {
        totalTime += timestamp - lastVisibleStart
        lastVisibleStart = 0
      }
    })

    // Add current visible time if still visible
    if (isVisible && lastVisibleStart > 0) {
      totalTime += Date.now() - lastVisibleStart
    }

    return totalTime
  }, [visibilityHistory, isVisible])

  return {
    ref,
    isVisible,
    entry,
    visibilityHistory,
    getTotalVisibleTime,
  }
}

/**
 * Scroll progress hook using intersection observer
 */
export function useScrollProgress() {
  const [progress, setProgress] = useState(0)
  const ref = useRef<HTMLElement>(null)

  useEffect(() => {
    if (!ref.current) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        const { intersectionRatio, boundingClientRect, rootBounds } = entry
        
        if (!rootBounds) return

        const elementTop = boundingClientRect.top
        const elementHeight = boundingClientRect.height
        const viewportHeight = rootBounds.height

        let progress = 0

        if (elementTop <= 0) {
          // Element is above viewport
          const visibleHeight = Math.min(elementHeight + elementTop, viewportHeight)
          progress = Math.max(0, visibleHeight / elementHeight)
        } else if (elementTop < viewportHeight) {
          // Element is entering viewport
          const visibleHeight = Math.min(viewportHeight - elementTop, elementHeight)
          progress = visibleHeight / elementHeight
        }

        setProgress(Math.min(1, Math.max(0, progress)))
      },
      {
        threshold: Array.from({ length: 101 }, (_, i) => i / 100), // 0 to 1 in 0.01 increments
      }
    )

    observer.observe(ref.current)

    return () => {
      observer.disconnect()
    }
  }, [])

  return {
    ref,
    progress,
  }
}
