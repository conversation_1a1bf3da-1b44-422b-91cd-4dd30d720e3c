/**
 * Analytics Hook
 * Manages analytics data and operations
 */

import { useState, useCallback, useEffect } from 'react'
import { backendApiClient } from '@/services/backend-api-client'
import { useToast } from '@/hooks/use-toast'
import type { ID } from '@/types'

export interface AnalyticsMetric {
  id: string
  name: string
  value: number
  previousValue?: number
  change?: number
  changePercentage?: number
  trend: 'up' | 'down' | 'stable'
  unit?: string
  format?: 'number' | 'percentage' | 'currency' | 'duration'
}

export interface AnalyticsChart {
  id: string
  title: string
  type: 'line' | 'bar' | 'pie' | 'area' | 'scatter'
  data: Array<{
    label: string
    value: number
    date?: string
    category?: string
  }>
  options?: {
    showLegend?: boolean
    showGrid?: boolean
    colors?: string[]
    height?: number
  }
}

export interface AnalyticsDashboard {
  id: ID
  name: string
  description?: string
  metrics: AnalyticsMetric[]
  charts: AnalyticsChart[]
  filters: Record<string, any>
  dateRange: {
    start: string
    end: string
  }
  refreshInterval?: number
  isPublic: boolean
  createdAt: string
  updatedAt: string
}

export interface AnalyticsFilter {
  field: string
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains'
  value: any
  label?: string
}

export interface AnalyticsQuery {
  metrics: string[]
  dimensions?: string[]
  filters?: AnalyticsFilter[]
  dateRange: {
    start: string
    end: string
  }
  granularity?: 'hour' | 'day' | 'week' | 'month'
  limit?: number
  orderBy?: string
  orderDirection?: 'asc' | 'desc'
}

export interface UseAnalyticsResult {
  // State
  dashboards: AnalyticsDashboard[]
  currentDashboard: AnalyticsDashboard | null
  loading: boolean
  error: string | null
  
  // Dashboard operations
  loadDashboard: (dashboardId: ID) => Promise<void>
  createDashboard: (dashboard: Omit<AnalyticsDashboard, 'id' | 'createdAt' | 'updatedAt'>) => Promise<AnalyticsDashboard>
  updateDashboard: (dashboardId: ID, updates: Partial<AnalyticsDashboard>) => Promise<void>
  deleteDashboard: (dashboardId: ID) => Promise<void>
  duplicateDashboard: (dashboardId: ID, name: string) => Promise<AnalyticsDashboard>
  
  // Data querying
  queryMetrics: (query: AnalyticsQuery) => Promise<AnalyticsMetric[]>
  queryChartData: (query: AnalyticsQuery) => Promise<AnalyticsChart>
  
  // Real-time data
  subscribeToMetrics: (metricIds: string[], callback: (metrics: AnalyticsMetric[]) => void) => () => void
  
  // Export
  exportDashboard: (dashboardId: ID, format: 'pdf' | 'png' | 'csv' | 'json') => Promise<void>
  
  // Utilities
  getAvailableMetrics: () => Promise<Array<{ id: string; name: string; description: string; category: string }>>
  getAvailableDimensions: () => Promise<Array<{ id: string; name: string; description: string; type: string }>>
  validateQuery: (query: AnalyticsQuery) => { valid: boolean; errors: string[] }
  
  // Refresh
  refresh: () => Promise<void>
}

export function useAnalytics(): UseAnalyticsResult {
  const { toast } = useToast()
  
  const [dashboards, setDashboards] = useState<AnalyticsDashboard[]>([])
  const [currentDashboard, setCurrentDashboard] = useState<AnalyticsDashboard | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load dashboards
  const loadDashboards = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request<AnalyticsDashboard[]>('/analytics/dashboards')
      setDashboards(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load dashboards'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Load specific dashboard
  const loadDashboard = useCallback(async (dashboardId: ID) => {
    setLoading(true)
    setError(null)

    try {
      const response = await backendApiClient.request<AnalyticsDashboard>(`/analytics/dashboards/${dashboardId}`)
      setCurrentDashboard(response)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load dashboard'
      setError(errorMessage)
      
      toast({
        type: 'error',
        title: 'Loading failed',
        description: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }, [toast])

  // Create dashboard
  const createDashboard = useCallback(async (dashboard: Omit<AnalyticsDashboard, 'id' | 'createdAt' | 'updatedAt'>): Promise<AnalyticsDashboard> => {
    try {
      const response = await backendApiClient.request<AnalyticsDashboard>('/analytics/dashboards', {
        method: 'POST',
        body: JSON.stringify(dashboard)
      })
      await loadDashboards()
      
      toast({
        type: 'success',
        title: 'Dashboard created',
        description: 'Analytics dashboard has been created successfully.',
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to create dashboard'
      
      toast({
        type: 'error',
        title: 'Creation failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadDashboards, toast])

  // Update dashboard
  const updateDashboard = useCallback(async (dashboardId: ID, updates: Partial<AnalyticsDashboard>) => {
    try {
      await backendApiClient.request(`/analytics/dashboards/${dashboardId}`, {
        method: 'PUT',
        body: JSON.stringify(updates)
      })
      await loadDashboards()
      
      if (currentDashboard?.id === dashboardId) {
        setCurrentDashboard(prev => prev ? { ...prev, ...updates } : null)
      }
      
      toast({
        type: 'success',
        title: 'Dashboard updated',
        description: 'Analytics dashboard has been updated successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to update dashboard'
      
      toast({
        type: 'error',
        title: 'Update failed',
        description: errorMessage,
      })
    }
  }, [loadDashboards, currentDashboard, toast])

  // Delete dashboard
  const deleteDashboard = useCallback(async (dashboardId: ID) => {
    try {
      await backendApiClient.request(`/analytics/dashboards/${dashboardId}`, {
        method: 'DELETE'
      })
      await loadDashboards()
      
      if (currentDashboard?.id === dashboardId) {
        setCurrentDashboard(null)
      }
      
      toast({
        type: 'success',
        title: 'Dashboard deleted',
        description: 'Analytics dashboard has been deleted successfully.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to delete dashboard'
      
      toast({
        type: 'error',
        title: 'Deletion failed',
        description: errorMessage,
      })
    }
  }, [loadDashboards, currentDashboard, toast])

  // Duplicate dashboard
  const duplicateDashboard = useCallback(async (dashboardId: ID, name: string): Promise<AnalyticsDashboard> => {
    try {
      const response = await backendApiClient.request<AnalyticsDashboard>(`/analytics/dashboards/${dashboardId}/duplicate`, {
        method: 'POST',
        body: JSON.stringify({ name })
      })
      await loadDashboards()
      
      toast({
        type: 'success',
        title: 'Dashboard duplicated',
        description: 'Analytics dashboard has been duplicated successfully.',
      })
      
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to duplicate dashboard'
      
      toast({
        type: 'error',
        title: 'Duplication failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [loadDashboards, toast])

  // Query metrics
  const queryMetrics = useCallback(async (query: AnalyticsQuery): Promise<AnalyticsMetric[]> => {
    try {
      const response = await backendApiClient.request<AnalyticsMetric[]>('/analytics/query/metrics', {
        method: 'POST',
        body: JSON.stringify(query)
      })
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to query metrics'
      
      toast({
        type: 'error',
        title: 'Query failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Query chart data
  const queryChartData = useCallback(async (query: AnalyticsQuery): Promise<AnalyticsChart> => {
    try {
      const response = await backendApiClient.request<AnalyticsChart>('/analytics/query/chart', {
        method: 'POST',
        body: JSON.stringify(query)
      })
      return response
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to query chart data'
      
      toast({
        type: 'error',
        title: 'Query failed',
        description: errorMessage,
      })
      
      throw err
    }
  }, [toast])

  // Subscribe to real-time metrics
  const subscribeToMetrics = useCallback((metricIds: string[], callback: (metrics: AnalyticsMetric[]) => void) => {
    // In a real implementation, this would use WebSocket or Server-Sent Events
    const interval = setInterval(async () => {
      try {
        const response = await backendApiClient.request<AnalyticsMetric[]>('/analytics/realtime/metrics', {
          method: 'POST',
          body: JSON.stringify({ metricIds })
        })
        callback(response)
      } catch (err) {
        console.error('Failed to fetch real-time metrics:', err)
      }
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  // Export dashboard
  const exportDashboard = useCallback(async (dashboardId: ID, format: 'pdf' | 'png' | 'csv' | 'json') => {
    try {
      const response = await backendApiClient.request(`/analytics/dashboards/${dashboardId}/export?format=${format}`)
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `dashboard-${dashboardId}.${format}`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      toast({
        type: 'success',
        title: 'Export started',
        description: 'Dashboard export is being downloaded.',
      })
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to export dashboard'
      
      toast({
        type: 'error',
        title: 'Export failed',
        description: errorMessage,
      })
    }
  }, [toast])

  // Get available metrics
  const getAvailableMetrics = useCallback(async () => {
    const response = await backendApiClient.request('/analytics/metrics/available')
    return response
  }, [])

  // Get available dimensions
  const getAvailableDimensions = useCallback(async () => {
    const response = await backendApiClient.request('/analytics/dimensions/available')
    return response
  }, [])

  // Validate query
  const validateQuery = useCallback((query: AnalyticsQuery): { valid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (!query.metrics || query.metrics.length === 0) {
      errors.push('At least one metric is required')
    }
    
    if (!query.dateRange || !query.dateRange.start || !query.dateRange.end) {
      errors.push('Date range is required')
    }
    
    if (query.dateRange && new Date(query.dateRange.start) > new Date(query.dateRange.end)) {
      errors.push('Start date must be before end date')
    }
    
    if (query.limit && (query.limit < 1 || query.limit > 10000)) {
      errors.push('Limit must be between 1 and 10000')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }, [])

  // Load dashboards on mount
  useEffect(() => {
    loadDashboards()
  }, [loadDashboards])

  return {
    // State
    dashboards,
    currentDashboard,
    loading,
    error,
    
    // Dashboard operations
    loadDashboard,
    createDashboard,
    updateDashboard,
    deleteDashboard,
    duplicateDashboard,
    
    // Data querying
    queryMetrics,
    queryChartData,
    
    // Real-time data
    subscribeToMetrics,
    
    // Export
    exportDashboard,
    
    // Utilities
    getAvailableMetrics,
    getAvailableDimensions,
    validateQuery,
    
    // Refresh
    refresh: loadDashboards,
  }
}

// Individual metric hooks for specific analytics
export function useDocumentMetrics() {
  const { queryMetrics } = useAnalytics()
  return queryMetrics
}

export function useUserActivityMetrics() {
  const { queryMetrics } = useAnalytics()
  return queryMetrics
}

export function useProjectMetrics() {
  const { queryMetrics } = useAnalytics()
  return queryMetrics
}

export function useUsageMetrics() {
  const { queryMetrics } = useAnalytics()
  return queryMetrics
}

export default useAnalytics
